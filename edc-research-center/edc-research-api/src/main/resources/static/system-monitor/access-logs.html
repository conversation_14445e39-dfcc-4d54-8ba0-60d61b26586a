<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问日志监控</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .page-header {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            margin-bottom: 20px;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border: none;
        }
        .table th {
            font-weight: 600;
            color: #2c3e50;
            border-top: none;
        }
        .search-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        .status-200 { background-color: #e8f5e9; color: #2e7d32; }
        .status-300 { background-color: #e3f2fd; color: #1565c0; }
        .status-400 { background-color: #fff8e1; color: #f57f17; }
        .status-500 { background-color: #ffebee; color: #c62828; }
        .method-badge {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }
        .method-get { background-color: #e8f5e9; color: #2e7d32; }
        .method-post { background-color: #e3f2fd; color: #1565c0; }
        .method-put { background-color: #fff8e1; color: #f57f17; }
        .method-delete { background-color: #ffebee; color: #c62828; }
        .token-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .token-info {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .logout-btn {
            width: 100%;
            padding: 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>


    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="h3 mb-0">
                        <i class="fa fa-list-alt text-primary"></i> 访问日志监控
                    </h1>
                    <p class="text-muted mb-0">实时监控系统访问日志和用户行为</p>
                </div>
                <div class="col-auto d-flex align-items-center">
                    <!-- Token状态显示 -->
                    <div id="tokenStatus" class="mr-3">
                        <div class="d-flex align-items-center bg-light rounded px-3 py-2">
                            <div class="mr-2">
                                <span id="token-status" class="badge badge-success">Token有效</span>
                            </div>
                            <div class="mr-2">
                                <small class="text-muted">剩余: <span id="token-remaining-time">--</span></small>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="logout()">
                                <i class="fa fa-sign-out"></i> 退出
                            </button>
                        </div>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" onclick="navigateToPage('index.html')">
                            <i class="fa fa-home"></i> 首页
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('online-users.html')">
                            <i class="fa fa-users"></i> 在线用户
                        </button>
                        <button class="btn btn-outline-primary" onclick="navigateToPage('exception-logs.html')">
                            <i class="fa fa-exclamation-triangle"></i> 异常日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>用户ID</label>
                            <input type="text" class="form-control" id="userId" placeholder="输入用户ID">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>请求路径</label>
                            <input type="text" class="form-control" id="requestUrl" placeholder="输入请求路径">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>请求方法</label>
                            <select class="form-control" id="requestMethod">
                                <option value="">全部</option>
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>状态码</label>
                            <select class="form-control" id="statusCode">
                                <option value="">全部</option>
                                <option value="200">200</option>
                                <option value="400">400</option>
                                <option value="401">401</option>
                                <option value="403">403</option>
                                <option value="404">404</option>
                                <option value="500">500</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                                <button type="button" class="btn btn-outline-secondary ml-2" onclick="resetSearch()">
                                    <i class="fa fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">访问日志列表</h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                            <i class="fa fa-refresh"></i> 刷新
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="exportData()">
                            <i class="fa fa-download"></i> 导出
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="alertContainer"></div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="thead-light">
                            <tr>
                                <th>访问时间</th>
                                <th>用户ID</th>
                                <th>IP地址</th>
                                <th>请求方法</th>
                                <th>请求路径</th>
                                <th>状态码</th>
                                <th>响应时间</th>
                                <th>用户代理</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">加载中...</span>
                                    </div>
                                    <div class="mt-2">正在加载数据...</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <span class="text-muted">每页显示</span>
                    <select class="form-control form-control-sm mx-2" style="width: auto;" id="pageSize" onchange="changePageSize()">
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span class="text-muted">条记录</span>
                </div>
            </div>
            <div class="col-md-6">
                <nav>
                    <ul class="pagination justify-content-end mb-0" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script src="common.js"></script>
    <script>
        let currentPage = 1;
        let currentPageSize = 20;
        let totalPages = 0;
        let totalRecords = 0;

        $(document).ready(function() {
            // 检查登录状态
            if (!checkLoginStatus()) {
                return;
            }

            // 初始化Token状态显示
            updateTokenStatus();
            // 每秒更新Token状态
            setInterval(updateTokenStatus, 1000);

            // 立即加载数据
            setTimeout(() => {
                loadAccessLogs();
            }, 100); // 延迟100ms确保页面完全加载

            // 搜索表单提交
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                currentPage = 1;
                loadAccessLogs();
            });
        });

        // 加载访问日志数据
        async function loadAccessLogs() {
            try {
                showLoading();
                
                const params = {
                    pageNum: currentPage,
                    pageSize: currentPageSize,
                    userId: $('#userId').val().trim(),
                    requestUrl: $('#requestUrl').val().trim(),
                    requestMethod: $('#requestMethod').val(),
                    statusCode: $('#statusCode').val()
                };

                const url = `${getApiBaseUrl()}/monitor/access-logs/list`;
                const response = await makeApiRequest(url, {
                    method: 'GET',
                    data: params
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    displayAccessLogs(result.data);
                    showAlert('alertContainer', '数据加载成功', 'success');
                } else {
                    showAlert('alertContainer', result.message || '加载数据失败', 'danger');
                }
            } catch (error) {
                console.error('加载访问日志失败:', error);

                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '网络请求失败: ' + error.message, 'danger');
            }
        }

        // 显示访问日志数据
        function displayAccessLogs(data) {
            const tbody = $('#dataTableBody');
            tbody.empty();

            if (!data || !data.list || data.list.length === 0) {
                tbody.html(`
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fa fa-inbox text-muted" style="font-size: 48px;"></i>
                            <div class="mt-2 text-muted">暂无数据</div>
                        </td>
                    </tr>
                `);
                return;
            }

            totalPages = data.totalPage || 0;
            totalRecords = data.total || 0;

            data.list.forEach(log => {
                const row = `
                    <tr>
                        <td>${formatDateTime(log.accessTime)}</td>
                        <td>${log.userId || '-'}</td>
                        <td>${log.requestIp || '-'}</td>
                        <td><span class="method-badge method-${log.requestMethod?.toLowerCase()}">${log.requestMethod || '-'}</span></td>
                        <td title="${log.requestUrl || '-'}">${truncateText(log.requestUrl || '-', 50)}</td>
                        <td><span class="status-badge status-${Math.floor((log.responseStatus || 0) / 100) * 100}">${log.responseStatus || '-'}</span></td>
                        <td>${log.responseTime ? log.responseTime + 'ms' : '-'}</td>
                        <td title="${log.userAgent || '-'}">${truncateText(log.userAgent || '-', 30)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(${log.id})">
                                <i class="fa fa-eye"></i> 详情
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });

            updatePagination();
        }

        // 截断文本
        function truncateText(text, maxLength) {
            if (!text || text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // 显示加载状态
        function showLoading() {
            $('#dataTableBody').html(`
                <tr>
                    <td colspan="9" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载数据...</div>
                    </td>
                </tr>
            `);
        }

        // 更新分页
        function updatePagination() {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页
            const prevDisabled = currentPage <= 1 ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${prevDisabled}">
                    <a class="page-link" href="javascript:void(0)" onclick="changePage(${currentPage - 1})">上一页</a>
                </li>
            `);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const active = i === currentPage ? 'active' : '';
                pagination.append(`
                    <li class="page-item ${active}">
                        <a class="page-link" href="javascript:void(0)" onclick="changePage(${i})">${i}</a>
                    </li>
                `);
            }

            // 下一页
            const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
            pagination.append(`
                <li class="page-item ${nextDisabled}">
                    <a class="page-link" href="javascript:void(0)" onclick="changePage(${currentPage + 1})">下一页</a>
                </li>
            `);
        }

        // 切换页码
        function changePage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            currentPage = page;
            loadAccessLogs();
        }

        // 改变页面大小
        function changePageSize() {
            currentPageSize = parseInt($('#pageSize').val());
            currentPage = 1;
            loadAccessLogs();
        }

        // 重置搜索
        function resetSearch() {
            $('#searchForm')[0].reset();
            currentPage = 1;
            loadAccessLogs();
        }

        // 刷新数据
        function refreshData() {
            loadAccessLogs();
        }

        // 查看日志详情
        async function viewLogDetail(logId) {
            try {
                const response = await makeApiRequest(`${getApiBaseUrl()}/monitor/access-logs/detail/${logId}`);
                const result = await response.json();

                if (result.code === 200) {
                    showLogDetailModal(result.data);
                } else {
                    showAlert('alertContainer', result.message || '获取日志详情失败', 'danger');
                }
            } catch (error) {
                // 检查是否是token过期错误
                if (error.message.includes('访问令牌无效') || error.message.includes('已过期')) {
                    console.log('Token已过期，即将跳转到登录页面');
                    setTimeout(() => {
                        logout();
                    }, 1000);
                    return;
                }

                showAlert('alertContainer', '获取日志详情失败: ' + error.message, 'danger');
            }
        }

        // 显示日志详情模态框
        function showLogDetailModal(log) {
            const modalHtml = `
                <div class="modal fade" id="logDetailModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">访问日志详情</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>访问时间:</strong> ${formatDateTime(log.accessTime)}</p>
                                        <p><strong>用户ID:</strong> ${log.userId || '-'}</p>
                                        <p><strong>用户名:</strong> ${log.userName || '-'}</p>
                                        <p><strong>请求IP:</strong> ${log.requestIp || '-'}</p>
                                        <p><strong>位置:</strong> ${log.location || '-'}</p>
                                        <p><strong>请求方法:</strong> <span class="method-badge method-${(log.requestMethod || '').toLowerCase()}">${log.requestMethod || '-'}</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>响应状态:</strong> <span class="status-badge status-${Math.floor((log.responseStatus || 0) / 100) * 100}">${log.responseStatus || '-'}</span></p>
                                        <p><strong>响应时间:</strong> ${log.responseTime ? log.responseTime + 'ms' : '-'}</p>
                                        <p><strong>浏览器:</strong> ${log.browser || '-'}</p>
                                        <p><strong>操作系统:</strong> ${log.os || '-'}</p>
                                        <p><strong>设备类型:</strong> ${log.deviceType || '-'}</p>
                                        <p><strong>会话ID:</strong> ${log.sessionId || '-'}</p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <p><strong>请求URL:</strong></p>
                                        <div class="bg-light p-2 rounded"><code>${log.requestUrl || '-'}</code></div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <p><strong>User Agent:</strong></p>
                                        <div class="bg-light p-2 rounded"><small>${log.userAgent || '-'}</small></div>
                                    </div>
                                </div>
                                ${log.requestParams ? `
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <p><strong>请求参数:</strong></p>
                                        <div class="bg-light p-2 rounded"><pre>${log.requestParams}</pre></div>
                                    </div>
                                </div>
                                ` : ''}
                                ${log.requestHeaders ? `
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <p><strong>请求头:</strong></p>
                                        <div class="bg-light p-2 rounded"><pre>${log.requestHeaders}</pre></div>
                                    </div>
                                </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            $('#logDetailModal').remove();
            // 添加新的模态框
            $('body').append(modalHtml);
            // 显示模态框
            $('#logDetailModal').modal('show');
        }

        // 更新Token状态显示
        function updateTokenStatus() {
            const statusElement = $('#token-status');
            const timeElement = $('#token-remaining-time');
            const tokenStatusDiv = $('#tokenStatus');

            if (statusElement.length) {
                const isExpired = TokenManager.isTokenExpired();
                statusElement.text(isExpired ? '已过期' : 'Token有效');
                statusElement.removeClass('badge-success badge-danger').addClass(isExpired ? 'badge-danger' : 'badge-success');
            }

            if (timeElement.length) {
                timeElement.text(TokenManager.formatRemainingTime());
            }

            // 显示token状态区域
            if (tokenStatusDiv.length) {
                tokenStatusDiv.show();
            }
        }

        // 导出数据
        function exportData() {
            // TODO: 实现数据导出功能
            alert('数据导出功能待实现');
        }
    </script>
</body>
</html>
