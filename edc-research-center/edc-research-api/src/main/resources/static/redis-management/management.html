<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redis数据管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }

        .header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
        }

        .header .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header .token-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .header .token-status.valid {
            background: #d4edda;
            color: #155724;
        }

        .header .token-status.invalid {
            background: #f8d7da;
            color: #721c24;
        }

        .logout-btn {
            padding: 8px 16px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        .tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
        }

        .tab-btn {
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            padding: 20px;
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }

        .form-row .form-group {
            flex: 1;
        }

        .btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5a6fd8;
        }

        .btn.danger {
            background: #dc3545;
        }

        .btn.danger:hover {
            background: #c82333;
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .result-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
            padding: 20px;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .result-header h3 {
            color: #333;
            margin: 0;
        }

        .result-stats {
            font-size: 14px;
            color: #666;
        }

        .key-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .key-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .key-item:last-child {
            border-bottom: none;
        }

        .key-item:hover {
            background: #f8f9fa;
        }

        .key-info {
            flex: 1;
        }

        .key-name {
            font-family: monospace;
            font-weight: bold;
            color: #333;
        }

        .key-meta {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .key-actions {
            display: flex;
            gap: 5px;
        }

        .key-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .view-btn {
            background: #17a2b8;
            color: white;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
        }

        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }

        .loading .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            line-height: 1.4;
        }

        .confirm-dialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .confirm-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 400px;
            width: 90%;
        }

        .confirm-content h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .confirm-content p {
            margin-bottom: 20px;
            color: #666;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .checkbox-group {
            margin: 10px 0;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }

        .selected-count {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin-left: 10px;
        }

        .key-checkbox {
            width: auto !important;
            margin-right: 10px;
        }



        .pagination-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .pagination-controls button {
            padding: 8px 16px;
            font-size: 14px;
        }

        .pagination-controls button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(3px);
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #666;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .modal-close:hover {
            background: #e0e0e0;
            color: #333;
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .data-info {
            margin-bottom: 20px;
        }

        .info-row {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }

        .info-row label {
            font-weight: 600;
            color: #555;
            min-width: 80px;
            margin-right: 15px;
        }

        .key-value {
            font-family: 'Courier New', monospace;
            background: #f1f3f4;
            padding: 4px 8px;
            border-radius: 4px;
            color: #1a73e8;
            font-weight: 500;
        }

        .data-type {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .ttl-value {
            color: #f57c00;
            font-weight: 500;
        }

        .data-content {
            border-top: 1px solid #e0e0e0;
            padding-top: 20px;
        }

        .data-content label {
            font-weight: 600;
            color: #555;
            display: block;
            margin-bottom: 10px;
        }

        .formatted-data {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            color: #333;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            background: #f8f9fa;
        }

        .modal-footer .btn {
            width: auto;
            padding: 10px 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Redis数据管理</h1>
        <div class="user-info">
            <span class="token-status" id="tokenStatus">Token有效</span>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>
    </div>

    <div class="container">
        <div class="tabs">
            <div class="tab-header">
                <button class="tab-btn active" onclick="switchTab('query')">数据查询</button>
                <button class="tab-btn" onclick="switchTab('delete')">数据删除</button>
                <button class="tab-btn" onclick="switchTab('authcode')">AuthCode查询</button>
            </div>

            <!-- 查询标签页 -->
            <div class="tab-content active" id="query-tab">
                <div class="alert" id="query-alert"></div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="queryType">查询类型</label>
                        <select id="queryType" onchange="updateQueryForm()">
                            <option value="key">指定Key</option>
                            <option value="pattern">模糊匹配</option>
                            <option value="prefix">前缀匹配</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="queryInput">查询条件</label>
                        <input type="text" id="queryInput" placeholder="请输入Redis键名">
                    </div>
                    <div class="form-group" id="limitGroup" style="display: none;">
                        <label for="queryLimit">限制数量</label>
                        <input type="number" id="queryLimit" value="100" min="1" max="1000">
                    </div>
                    <div class="form-group">
                        <label for="queryPageSize">每页显示</label>
                        <select id="queryPageSize" onchange="updateQueryPageSize()">
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                            <option value="100">100条</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn" onclick="executeQuery()">查询</button>
                    </div>
                </div>

                <div class="loading" id="query-loading">
                    <div class="spinner"></div>
                    <p>正在查询...</p>
                </div>

                <div class="result-container" id="query-result" style="display: none;">
                    <div class="result-header">
                        <h3>查询结果</h3>
                        <div class="result-stats" id="query-stats"></div>
                    </div>
                    <div id="query-content"></div>
                    <!-- 分页控件 -->
                    <div id="query-pagination" style="display: none; margin-top: 20px; text-align: center;">
                        <button class="btn" onclick="queryPrevPage()" id="query-prev-btn">上一页</button>
                        <span id="query-page-info" style="margin: 0 15px;">第1页 / 共1页</span>
                        <button class="btn" onclick="queryNextPage()" id="query-next-btn">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 删除标签页 -->
            <div class="tab-content" id="delete-tab">
                <div class="alert" id="delete-alert"></div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="deleteType">删除类型</label>
                        <select id="deleteType" onchange="updateDeleteForm()">
                            <option value="key">指定Key</option>
                            <option value="batch">批量删除</option>
                            <option value="pattern">模糊删除</option>
                            <option value="prefix">前缀删除</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="deleteInput">删除条件</label>
                        <input type="text" id="deleteInput" placeholder="请输入要删除的条件">
                    </div>
                    <div class="form-group">
                        <button class="btn danger" onclick="executeDelete()">删除</button>
                    </div>
                </div>

                <!-- 批量删除选择区域 -->
                <div id="batch-select-area" style="display: none; margin-top: 20px;">
                    <div class="checkbox-group">
                        <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                        <label for="select-all">全选</label>
                        <span class="selected-count" id="selected-count">已选择: 0</span>
                        <button class="btn danger" onclick="deleteBatchSelected()" style="margin-left: 10px;">删除选中</button>
                    </div>
                </div>

                <div class="loading" id="delete-loading">
                    <div class="spinner"></div>
                    <p>正在删除...</p>
                </div>

                <div class="result-container" id="delete-result" style="display: none;">
                    <div class="result-header">
                        <h3>删除结果</h3>
                        <div class="result-stats" id="delete-stats"></div>
                    </div>
                    <div id="delete-content"></div>
                </div>
            </div>

            <!-- AuthCode查询标签页 -->
            <div class="tab-content" id="authcode-tab">
                <div class="alert" id="authcode-alert"></div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="authCodeInput">AuthCode</label>
                        <input type="text" id="authCodeInput" placeholder="请输入AuthCode">
                    </div>
                    <div class="form-group">
                        <label for="authCodePageSize">每页显示</label>
                        <select id="authCodePageSize" onchange="updateAuthCodePageSize()">
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                            <option value="100">100条</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn" onclick="generateAuthCode()">生成AuthCode</button>
                    </div>
                    <div class="form-group">
                        <button class="btn" onclick="queryByAuthCode()">查询所有数据</button>
                    </div>
                </div>

                <!-- AuthCode批量删除选择区域 -->
                <div id="authcode-batch-select-area" style="display: none; margin-top: 20px;">
                    <div class="checkbox-group">
                        <input type="checkbox" id="authcode-select-all" onchange="toggleAuthCodeSelectAll()">
                        <label for="authcode-select-all">全选</label>
                        <span class="selected-count" id="authcode-selected-count">已选择: 0</span>
                        <button class="btn danger" onclick="deleteAuthCodeBatchSelected()" style="margin-left: 10px;">删除选中</button>
                    </div>
                </div>

                <div class="loading" id="authcode-loading">
                    <div class="spinner"></div>
                    <p>正在查询...</p>
                </div>

                <div class="result-container" id="authcode-result" style="display: none;">
                    <div class="result-header">
                        <h3>AuthCode查询结果</h3>
                        <div class="result-stats" id="authcode-stats"></div>
                    </div>
                    <div id="authcode-content"></div>
                    <!-- 分页控件 -->
                    <div id="authcode-pagination" style="display: none; margin-top: 20px; text-align: center;">
                        <button class="btn" onclick="authcodePrevPage()" id="authcode-prev-btn">上一页</button>
                        <span id="authcode-page-info" style="margin: 0 15px;">第1页 / 共1页</span>
                        <button class="btn" onclick="authcodeNextPage()" id="authcode-next-btn">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div class="confirm-dialog" id="confirmDialog">
        <div class="confirm-content">
            <h3>确认删除</h3>
            <p id="confirmMessage">确定要删除这些数据吗？此操作不可撤销。</p>
            <div id="confirmDetails"></div>
            <div class="confirm-actions">
                <button class="btn" onclick="closeConfirmDialog()">取消</button>
                <button class="btn danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 数据查看弹窗 -->
    <div class="modal-overlay" id="dataViewModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">数据详情</h3>
                <button class="modal-close" onclick="closeDataViewModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="data-info">
                    <div class="info-row">
                        <label>键名:</label>
                        <span id="modalKeyName" class="key-value"></span>
                    </div>
                    <div class="info-row">
                        <label>数据类型:</label>
                        <span id="modalDataType" class="data-type"></span>
                    </div>
                    <div class="info-row">
                        <label>TTL:</label>
                        <span id="modalTTL" class="ttl-value"></span>
                    </div>
                </div>
                <div class="data-content">
                    <label>数据值:</label>
                    <div id="modalDataValue" class="formatted-data"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="copyDataToClipboard()">复制数据</button>
                <button class="btn danger" onclick="deleteCurrentKey()">删除此键</button>
                <button class="btn" onclick="closeDataViewModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        let accessToken = '';
        let selectedKeys = new Set();
        let pendingDeleteData = null;

        // 分页相关变量
        let currentQueryPage = 1;
        let currentQuerySize = 20;
        let currentQueryType = '';
        let currentQueryCondition = '';
        let currentQueryTotal = 0;
        let currentQueryTotalPages = 0;

        // AuthCode查询分页变量
        let currentAuthCodePage = 1;
        let currentAuthCodeSize = 20;
        let currentAuthCode = '';
        let currentAuthCodeTotal = 0;
        let currentAuthCodeTotalPages = 0;

        // 当前查看的键信息
        let currentViewingKey = null;

        // 动态获取API基础路径
        function getApiBaseUrl() {
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}/api`;
        }

        // 动态获取页面基础路径
        function getPageBaseUrl() {
            const protocol = window.location.protocol;
            const host = window.location.host;
            return `${protocol}//${host}`;
        }

        // 页面初始化
        window.addEventListener('load', () => {
            console.log('管理页面开始初始化...');
            checkAuth();
            // 延迟一下再验证token，避免页面刚加载时的时序问题
            setTimeout(() => {
                updateTokenStatus();
            }, 500);
            setInterval(updateTokenStatus, 30000); // 每30秒检查一次token状态
        });

        // 检查认证状态
        function checkAuth() {
            accessToken = localStorage.getItem('redis-management-token');
            const expire = localStorage.getItem('redis-management-token-expire');

            console.log('管理页面认证检查:');
            console.log('- accessToken:', accessToken ? '存在' : '不存在');
            console.log('- expire:', expire);
            console.log('- 当前时间:', Date.now());
            console.log('- 过期时间:', expire ? parseInt(expire) : '无');
            console.log('- 是否过期:', expire ? (Date.now() >= parseInt(expire)) : '无过期时间');

            if (!accessToken || !expire || Date.now() >= parseInt(expire)) {
                console.log('Token验证失败，跳转到登录页');
                // Token无效，跳转到登录页
                window.location.href = '/api/redis-management/login.html';
                return;
            }

            console.log('Token本地验证通过');
        }

        // 更新Token状态显示
        async function updateTokenStatus() {
            const statusEl = document.getElementById('tokenStatus');

            console.log('开始验证Token状态...');
            console.log('- 使用的accessToken:', accessToken);
            console.log('- API地址:', `${getApiBaseUrl()}/redis/management/auth/validate-token`);

            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/auth/validate-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    }
                });

                console.log('Token验证API响应状态:', response.status);
                
                const result = await response.json();
                console.log('管理页面Token验证API响应:', result);

                // 修复：使用code字段判断成功状态
                if (result.code === 200 && result.data && result.data.valid) {
                    statusEl.textContent = `Token有效 (${Math.floor(result.data.remainingTime / 60)}分钟)`;
                    statusEl.className = 'token-status valid';
                } else {
                    console.log('Token验证失败，详细信息:');
                    console.log('- result.code:', result.code);
                    console.log('- result.data:', result.data);
                    console.log('- result.message:', result.message);
                    statusEl.textContent = 'Token无效，3秒后跳转到登录页面';
                    statusEl.className = 'token-status invalid';

                    // 显示用户友好的提示
                    alert('Token验证失败，即将跳转到登录页面重新认证');

                    setTimeout(() => {
                        console.log('Token验证失败，跳转到登录页面');
                        window.location.href = '/api/redis-management/login.html';
                    }, 3000);
                }
            } catch (error) {
                console.error('Token验证网络错误:', error);
                statusEl.textContent = '网络错误，3秒后跳转到登录页面';
                statusEl.className = 'token-status invalid';

                // 显示用户友好的提示
                alert('网络连接错误，无法验证Token，即将跳转到登录页面');

                // 网络错误也应该跳转到登录页面
                setTimeout(() => {
                    console.log('由于网络错误跳转到登录页面');
                    window.location.href = '/api/redis-management/login.html';
                }, 3000);
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('redis-management-token');
            localStorage.removeItem('redis-management-token-expire');
            window.location.href = '/api/redis-management/login.html';
        }

        // 切换标签页
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新标签内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName + '-tab').classList.add('active');
        }

        // 更新查询表单
        function updateQueryForm() {
            const queryType = document.getElementById('queryType').value;
            const limitGroup = document.getElementById('limitGroup');
            const queryInput = document.getElementById('queryInput');
            
            if (queryType === 'key') {
                limitGroup.style.display = 'none';
                queryInput.placeholder = '请输入Redis键名';
            } else if (queryType === 'pattern') {
                limitGroup.style.display = 'block';
                queryInput.placeholder = '请输入匹配模式，如：user:*';
            } else if (queryType === 'prefix') {
                limitGroup.style.display = 'block';
                queryInput.placeholder = '请输入前缀，如：user:';
            }
        }

        // 更新删除表单
        function updateDeleteForm() {
            const deleteType = document.getElementById('deleteType').value;
            const deleteInput = document.getElementById('deleteInput');

            if (deleteType === 'key') {
                deleteInput.placeholder = '请输入要删除的Redis键名';
            } else if (deleteType === 'batch') {
                deleteInput.placeholder = '请输入要删除的键名，用逗号分隔';
            } else if (deleteType === 'pattern') {
                deleteInput.placeholder = '请输入匹配模式，如：user:*';
            } else if (deleteType === 'prefix') {
                deleteInput.placeholder = '请输入前缀，如：user:';
            }
        }

        // 更新查询分页大小
        function updateQueryPageSize() {
            const newSize = parseInt(document.getElementById('queryPageSize').value);
            if (newSize !== currentQuerySize) {
                currentQuerySize = newSize;
                currentQueryPage = 1; // 重置到第一页
                if (currentQueryType && currentQueryCondition) {
                    executePageQuery();
                }
            }
        }

        // 更新AuthCode查询分页大小
        function updateAuthCodePageSize() {
            const newSize = parseInt(document.getElementById('authCodePageSize').value);
            if (newSize !== currentAuthCodeSize) {
                currentAuthCodeSize = newSize;
                currentAuthCodePage = 1; // 重置到第一页
                if (currentAuthCode) {
                    executeAuthCodeQuery();
                }
            }
        }

        // 显示提示信息
        function showAlert(containerId, message, type = 'error') {
            const alert = document.getElementById(containerId);
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // 执行查询
        async function executeQuery() {
            const queryType = document.getElementById('queryType').value;
            const queryInput = document.getElementById('queryInput').value.trim();
            const queryLimit = document.getElementById('queryLimit').value;

            if (!queryInput) {
                showAlert('query-alert', '请输入查询条件');
                return;
            }

            // 保存当前查询条件用于分页
            currentQueryType = queryType;
            currentQueryCondition = queryInput;
            currentQueryPage = 1;

            const loading = document.getElementById('query-loading');
            const result = document.getElementById('query-result');

            loading.style.display = 'block';
            result.style.display = 'none';

            try {
                let url, body;

                if (queryType === 'key') {
                    url = `${getApiBaseUrl()}/redis/management/query/key`;
                    body = { key: queryInput };
                } else {
                    // 对于pattern和prefix查询，优先使用分页接口
                    if (queryType === 'pattern' || queryType === 'prefix') {
                        url = `${getApiBaseUrl()}/redis/management/query/pagination`;
                        body = {
                            queryType: queryType,
                            queryCondition: queryInput,
                            page: currentQueryPage,
                            size: currentQuerySize
                        };
                    } else if (queryType === 'pattern') {
                        url = `${getApiBaseUrl()}/redis/management/query/pattern`;
                        body = { pattern: queryInput, limit: parseInt(queryLimit) };
                    } else if (queryType === 'prefix') {
                        url = `${getApiBaseUrl()}/redis/management/query/prefix`;
                        body = { prefix: queryInput, limit: parseInt(queryLimit) };
                    }
                }

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify(body)
                });

                const apiResult = await response.json();
                console.log('查询API响应:', apiResult);

                loading.style.display = 'none';

                // 修复：使用code字段判断成功状态
                if (apiResult.code === 200) {
                    displayQueryResult(apiResult.data, queryType);
                    showAlert('query-alert', '查询完成', 'success');
                } else {
                    showAlert('query-alert', apiResult.message || '查询失败');
                }
            } catch (error) {
                loading.style.display = 'none';
                showAlert('query-alert', '网络请求失败：' + error.message);
            }
        }

        // 显示查询结果
        function displayQueryResult(data, queryType) {
            const result = document.getElementById('query-result');
            const stats = document.getElementById('query-stats');
            const content = document.getElementById('query-content');
            const pagination = document.getElementById('query-pagination');

            if (queryType === 'key') {
                // 单个key查询结果
                stats.textContent = data.exists ? '找到1个键' : '键不存在';

                if (data.exists) {
                    content.innerHTML = `
                        <div class="key-item">
                            <div class="key-info">
                                <div class="key-name">${data.key}</div>
                                <div class="key-meta">类型: ${data.type} | TTL: ${data.ttl}秒</div>
                            </div>
                        </div>
                        <div style="margin-top: 15px;">
                            <strong>数据值:</strong>
                            <div class="json-viewer">${formatValue(data.value)}</div>
                        </div>
                    `;
                } else {
                    content.innerHTML = '<p>指定的键不存在</p>';
                }
                pagination.style.display = 'none';
            } else {
                // 检查是否是分页数据
                if (data.total !== undefined) {
                    // 分页查询结果
                    currentQueryTotal = data.total;
                    currentQueryTotalPages = data.totalPages;

                    stats.textContent = `找到${data.total}个键，当前显示第${data.page}页`;

                    if (data.keys && data.keys.length > 0) {
                        content.innerHTML = `
                            <div class="key-list">
                                ${data.keys.map(key => `
                                    <div class="key-item">
                                        <div class="key-info">
                                            <input type="checkbox" class="key-checkbox" value="${key}" onchange="updateSelectedCount()">
                                            <div class="key-name">${key}</div>
                                        </div>
                                        <div class="key-actions">
                                            <button class="view-btn" onclick="viewKey('${key}')">查看</button>
                                            <button class="delete-btn" onclick="deleteKey('${key}')">删除</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `;

                        // 显示分页控件
                        if (data.totalPages > 1) {
                            pagination.style.display = 'block';
                            updateQueryPagination();
                        } else {
                            pagination.style.display = 'none';
                        }

                        // 显示批量选择区域
                        document.getElementById('batch-select-area').style.display = 'block';
                    } else {
                        content.innerHTML = '<p>没有找到匹配的键</p>';
                        pagination.style.display = 'none';
                        document.getElementById('batch-select-area').style.display = 'none';
                    }
                } else {
                    // 传统查询结果
                    const keys = data.keys || [];
                    stats.textContent = `找到${keys.length}个键${data.truncated ? ' (已截断)' : ''}`;

                    if (keys.length > 0) {
                        content.innerHTML = `
                            <div class="key-list">
                                ${keys.map(key => `
                                    <div class="key-item">
                                        <div class="key-info">
                                            <input type="checkbox" class="key-checkbox" value="${key}" onchange="updateSelectedCount()">
                                            <div class="key-name">${key}</div>
                                        </div>
                                        <div class="key-actions">
                                            <button class="view-btn" onclick="viewKey('${key}')">查看</button>
                                            <button class="delete-btn" onclick="deleteKey('${key}')">删除</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `;
                        document.getElementById('batch-select-area').style.display = 'block';
                    } else {
                        content.innerHTML = '<p>没有找到匹配的键</p>';
                        document.getElementById('batch-select-area').style.display = 'none';
                    }
                    pagination.style.display = 'none';
                }
            }

            result.style.display = 'block';
        }

        // 更新查询分页控件
        function updateQueryPagination() {
            const pageInfo = document.getElementById('query-page-info');
            const prevBtn = document.getElementById('query-prev-btn');
            const nextBtn = document.getElementById('query-next-btn');

            pageInfo.textContent = `第${currentQueryPage}页 / 共${currentQueryTotalPages}页`;
            prevBtn.disabled = currentQueryPage <= 1;
            nextBtn.disabled = currentQueryPage >= currentQueryTotalPages;
        }

        // 查询上一页
        async function queryPrevPage() {
            if (currentQueryPage > 1) {
                currentQueryPage--;
                await executePageQuery();
            }
        }

        // 查询下一页
        async function queryNextPage() {
            if (currentQueryPage < currentQueryTotalPages) {
                currentQueryPage++;
                await executePageQuery();
            }
        }

        // 执行分页查询
        async function executePageQuery() {
            const loading = document.getElementById('query-loading');
            const result = document.getElementById('query-result');

            loading.style.display = 'block';
            result.style.display = 'none';

            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/query/pagination`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify({
                        queryType: currentQueryType,
                        queryCondition: currentQueryCondition,
                        page: currentQueryPage,
                        size: currentQuerySize
                    })
                });

                const apiResult = await response.json();
                console.log('分页查询API响应:', apiResult);

                loading.style.display = 'none';

                if (apiResult.code === 200) {
                    displayQueryResult(apiResult.data, 'pagination');
                    showAlert('query-alert', '查询完成', 'success');
                } else {
                    showAlert('query-alert', apiResult.message || '查询失败');
                }
            } catch (error) {
                loading.style.display = 'none';
                showAlert('query-alert', '网络请求失败：' + error.message);
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.key-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.key-checkbox:checked');
            const count = checkboxes.length;
            document.getElementById('selected-count').textContent = `已选择: ${count}`;

            // 更新全选状态
            const allCheckboxes = document.querySelectorAll('.key-checkbox');
            const selectAll = document.getElementById('select-all');
            selectAll.checked = count > 0 && count === allCheckboxes.length;
        }

        // 删除选中的keys
        function deleteBatchSelected() {
            const checkboxes = document.querySelectorAll('.key-checkbox:checked');
            const selectedKeys = Array.from(checkboxes).map(cb => cb.value);

            if (selectedKeys.length === 0) {
                showAlert('query-alert', '请先选择要删除的键', 'warning');
                return;
            }

            showConfirmDialog(
                `确定要删除${selectedKeys.length}个选中的键吗？`,
                selectedKeys,
                () => executeBatchDelete(selectedKeys)
            );
        }

        // 执行批量删除
        async function executeBatchDelete(keys) {
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/delete/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify({ keys })
                });

                const result = await response.json();
                console.log('批量删除API响应:', result);

                if (result.code === 200) {
                    showAlert('query-alert', `批量删除完成，成功删除${result.data.deletedCount}个键`, 'success');
                    // 刷新当前列表
                    refreshCurrentList();
                    // 清空选择状态
                    clearSelection();
                } else {
                    showAlert('query-alert', result.message || '批量删除失败');
                }
            } catch (error) {
                showAlert('query-alert', '批量删除失败：' + error.message);
            }
        }

        // 清空选择状态
        function clearSelection() {
            const checkboxes = document.querySelectorAll('.key-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('select-all').checked = false;
            updateSelectedCount();
        }

        // 查看单个key的详细信息
        async function viewKey(key) {
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/query/key`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify({ key })
                });

                const result = await response.json();
                console.log('键详情查询API响应:', result);

                // 修复：使用code字段判断成功状态
                if (result.code === 200 && result.data && result.data.exists) {
                    const data = result.data;
                    showDataViewModal(data);
                } else {
                    showAlert('query-alert', '键不存在或查询失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                showAlert('query-alert', '查询失败：' + error.message);
            }
        }

        // 显示数据查看弹窗
        function showDataViewModal(data) {
            currentViewingKey = data.key;

            // 设置弹窗内容
            document.getElementById('modalKeyName').textContent = data.key;
            document.getElementById('modalDataType').textContent = data.type || 'unknown';

            // 格式化TTL显示
            const ttl = data.ttl;
            let ttlText = '';
            if (ttl === -1) {
                ttlText = '永不过期';
            } else if (ttl === -2) {
                ttlText = '键不存在';
            } else if (ttl > 0) {
                if (ttl > 86400) {
                    ttlText = `${Math.floor(ttl / 86400)}天 ${Math.floor((ttl % 86400) / 3600)}小时`;
                } else if (ttl > 3600) {
                    ttlText = `${Math.floor(ttl / 3600)}小时 ${Math.floor((ttl % 3600) / 60)}分钟`;
                } else if (ttl > 60) {
                    ttlText = `${Math.floor(ttl / 60)}分钟 ${ttl % 60}秒`;
                } else {
                    ttlText = `${ttl}秒`;
                }
            } else {
                ttlText = '已过期';
            }
            document.getElementById('modalTTL').textContent = ttlText;

            // 格式化数据值显示
            const formattedValue = formatDataValue(data.value);
            document.getElementById('modalDataValue').innerHTML = formattedValue;

            // 显示弹窗
            document.getElementById('dataViewModal').style.display = 'flex';
        }

        // 格式化数据值显示
        function formatDataValue(value) {
            if (value === null || value === undefined) {
                return '<span style="color: #999; font-style: italic;">null</span>';
            }

            if (typeof value === 'string') {
                // 尝试解析JSON
                try {
                    const parsed = JSON.parse(value);
                    return JSON.stringify(parsed, null, 2);
                } catch (e) {
                    // 不是JSON，直接返回字符串
                    return value;
                }
            }

            if (typeof value === 'object') {
                return JSON.stringify(value, null, 2);
            }

            return String(value);
        }

        // 关闭数据查看弹窗
        function closeDataViewModal() {
            document.getElementById('dataViewModal').style.display = 'none';
            currentViewingKey = null;
        }

        // 复制数据到剪贴板
        async function copyDataToClipboard() {
            const dataValue = document.getElementById('modalDataValue').textContent;
            try {
                await navigator.clipboard.writeText(dataValue);
                showAlert('query-alert', '数据已复制到剪贴板', 'success');
            } catch (error) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = dataValue;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('query-alert', '数据已复制到剪贴板', 'success');
            }
        }

        // 删除当前查看的键
        function deleteCurrentKey() {
            if (currentViewingKey) {
                closeDataViewModal();
                deleteKey(currentViewingKey);
            }
        }

        // 删除单个key
        function deleteKey(key) {
            showConfirmDialog('确定要删除这个键吗？', [key], () => {
                executeSingleDelete(key);
            });
        }

        // 执行单个key删除
        async function executeSingleDelete(key) {
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/delete/key`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify({ key })
                });

                const result = await response.json();
                console.log('删除键API响应:', result);

                // 修复：使用code字段判断成功状态
                if (result.code === 200) {
                    showAlert('query-alert', '删除成功', 'success');
                    // 刷新当前列表
                    refreshCurrentList();
                } else {
                    showAlert('query-alert', result.message || '删除失败');
                }
            } catch (error) {
                showAlert('query-alert', '删除失败：' + error.message);
            }
        }

        // 刷新当前列表
        function refreshCurrentList() {
            // 检查当前活动的标签页
            const activeTab = document.querySelector('.tab-btn.active').textContent;

            if (activeTab === 'AuthCode查询' && currentAuthCode) {
                // 刷新AuthCode查询结果
                executeAuthCodeQuery();
            } else if (activeTab === '数据查询' && currentQueryType && currentQueryCondition) {
                // 刷新数据查询结果
                if (currentQueryType === 'pattern' || currentQueryType === 'prefix') {
                    executePageQuery();
                } else {
                    executeQuery();
                }
            }
        }

        // 执行删除操作
        async function executeDelete() {
            const deleteType = document.getElementById('deleteType').value;
            const deleteInput = document.getElementById('deleteInput').value.trim();
            
            if (!deleteInput) {
                showAlert('delete-alert', '请输入删除条件');
                return;
            }
            
            // 先查询要删除的数据进行确认
            let keysToDelete = [];
            
            try {
                if (deleteType === 'key') {
                    keysToDelete = [deleteInput];
                } else if (deleteType === 'batch') {
                    keysToDelete = deleteInput.split(',').map(k => k.trim()).filter(k => k);
                } else if (deleteType === 'pattern') {
                    // 先查询匹配的keys
                    const response = await fetch(`${getApiBaseUrl()}/redis/management/query/pattern`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Access-Token': accessToken
                        },
                        body: JSON.stringify({ pattern: deleteInput, limit: 1000 })
                    });
                    
                    const result = await response.json();
                    console.log('模式查询API响应:', result);
                    // 修复：使用code字段判断成功状态
                    if (result.code === 200) {
                        keysToDelete = result.data.keys || [];
                    }
                } else if (deleteType === 'prefix') {
                    // 先查询匹配的keys
                    const response = await fetch(`${getApiBaseUrl()}/redis/management/query/prefix`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Access-Token': accessToken
                        },
                        body: JSON.stringify({ prefix: deleteInput, limit: 1000 })
                    });
                    
                    const result = await response.json();
                    console.log('前缀查询API响应:', result);
                    // 修复：使用code字段判断成功状态
                    if (result.code === 200) {
                        keysToDelete = result.data.keys || [];
                    }
                }
                
                if (keysToDelete.length === 0) {
                    showAlert('delete-alert', '没有找到要删除的键', 'warning');
                    return;
                }
                
                // 显示确认对话框
                showConfirmDialog(
                    `确定要删除${keysToDelete.length}个键吗？`,
                    keysToDelete,
                    () => executeConfirmedDelete(deleteType, deleteInput, keysToDelete)
                );
                
            } catch (error) {
                showAlert('delete-alert', '查询要删除的键失败：' + error.message);
            }
        }

        // 显示确认对话框
        function showConfirmDialog(message, keys, callback) {
            const dialog = document.getElementById('confirmDialog');
            const messageEl = document.getElementById('confirmMessage');
            const detailsEl = document.getElementById('confirmDetails');
            
            messageEl.textContent = message;
            
            if (keys.length <= 10) {
                detailsEl.innerHTML = `
                    <div style="margin: 10px 0;">
                        <strong>将要删除的键:</strong>
                        <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 5px;">
                            ${keys.map(key => `<div style="font-family: monospace; font-size: 12px;">${key}</div>`).join('')}
                        </div>
                    </div>
                `;
            } else {
                detailsEl.innerHTML = `
                    <div style="margin: 10px 0;">
                        <strong>将要删除${keys.length}个键</strong>
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            键数量较多，请确认删除条件正确
                        </div>
                    </div>
                `;
            }
            
            pendingDeleteData = callback;
            dialog.style.display = 'flex';
        }

        // 关闭确认对话框
        function closeConfirmDialog() {
            document.getElementById('confirmDialog').style.display = 'none';
            pendingDeleteData = null;
        }

        // 确认删除
        function confirmDelete() {
            if (pendingDeleteData) {
                pendingDeleteData();
                closeConfirmDialog();
            }
        }

        // 执行确认后的删除
        async function executeConfirmedDelete(deleteType, deleteInput, keysToDelete) {
            const loading = document.getElementById('delete-loading');
            const result = document.getElementById('delete-result');
            
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                let url, body;
                
                if (deleteType === 'key') {
                    url = `${getApiBaseUrl()}/redis/management/delete/key`;
                    body = { key: deleteInput };
                } else if (deleteType === 'batch') {
                    url = `${getApiBaseUrl()}/redis/management/delete/batch`;
                    body = { keys: keysToDelete };
                } else if (deleteType === 'pattern') {
                    url = `${getApiBaseUrl()}/redis/management/delete/pattern`;
                    body = { pattern: deleteInput };
                } else if (deleteType === 'prefix') {
                    url = `${getApiBaseUrl()}/redis/management/delete/prefix`;
                    body = { prefix: deleteInput };
                }
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify(body)
                });
                
                const apiResult = await response.json();
                console.log('批量删除API响应:', apiResult);

                loading.style.display = 'none';

                // 修复：使用code字段判断成功状态
                if (apiResult.code === 200) {
                    displayDeleteResult(apiResult.data, deleteType);
                    showAlert('delete-alert', '删除完成', 'success');
                } else {
                    showAlert('delete-alert', apiResult.message || '删除失败');
                }
            } catch (error) {
                loading.style.display = 'none';
                showAlert('delete-alert', '网络请求失败：' + error.message);
            }
        }

        // 显示删除结果
        function displayDeleteResult(data, deleteType) {
            const result = document.getElementById('delete-result');
            const stats = document.getElementById('delete-stats');
            const content = document.getElementById('delete-content');

            if (deleteType === 'key') {
                stats.textContent = data.success ? '删除成功' : '删除失败';
                content.innerHTML = `
                    <div class="key-item">
                        <div class="key-info">
                            <div class="key-name">${data.key}</div>
                            <div class="key-meta">
                                删除前存在: ${data.existed ? '是' : '否'} |
                                删除结果: ${data.success ? '成功' : '失败'}
                            </div>
                        </div>
                    </div>
                `;
            } else if (deleteType === 'batch') {
                stats.textContent = `删除了${data.deletedCount}个键`;
                content.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong>删除统计:</strong>
                        成功${data.successKeys.length}个，失败${data.failedKeys.length}个
                    </div>
                    ${data.successKeys.length > 0 ? `
                        <div style="margin-bottom: 15px;">
                            <strong>成功删除的键:</strong>
                            <div class="json-viewer">${data.successKeys.join('\n')}</div>
                        </div>
                    ` : ''}
                    ${data.failedKeys.length > 0 ? `
                        <div>
                            <strong>删除失败的键:</strong>
                            <div class="json-viewer">${data.failedKeys.join('\n')}</div>
                        </div>
                    ` : ''}
                `;
            } else {
                stats.textContent = `删除了${data.deletedCount}个键`;
                content.innerHTML = `
                    <div style="margin-bottom: 15px;">
                        <strong>匹配的键数量:</strong> ${data.matchedKeys.length}
                    </div>
                    <div style="margin-bottom: 15px;">
                        <strong>删除数量:</strong> ${data.deletedCount}
                    </div>
                    ${data.matchedKeys.length > 0 ? `
                        <div>
                            <strong>删除的键:</strong>
                            <div class="json-viewer">${data.matchedKeys.join('\n')}</div>
                        </div>
                    ` : ''}
                `;
            }

            result.style.display = 'block';
        }

        // 生成AuthCode
        async function generateAuthCode() {
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/auth/generate-auth-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    }
                });

                const result = await response.json();
                console.log('生成AuthCode API响应:', result);

                if (result.code === 200 && result.data) {
                    document.getElementById('authCodeInput').value = result.data.authCode;
                    showAlert('authcode-alert', `AuthCode生成成功！有效期：${result.data.expiresIn}秒`, 'success');
                } else {
                    showAlert('authcode-alert', result.message || 'AuthCode生成失败');
                }
            } catch (error) {
                showAlert('authcode-alert', '生成AuthCode失败：' + error.message);
            }
        }

        // 通过AuthCode查询所有数据
        async function queryByAuthCode() {
            const authCode = document.getElementById('authCodeInput').value.trim();

            if (!authCode) {
                showAlert('authcode-alert', '请输入AuthCode');
                return;
            }

            currentAuthCode = authCode;
            currentAuthCodePage = 1;
            await executeAuthCodeQuery();
        }

        // 执行AuthCode查询
        async function executeAuthCodeQuery() {
            const loading = document.getElementById('authcode-loading');
            const result = document.getElementById('authcode-result');

            loading.style.display = 'block';
            result.style.display = 'none';

            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/query/auth-code`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        authCode: currentAuthCode,
                        page: currentAuthCodePage,
                        size: currentAuthCodeSize
                    })
                });

                const apiResult = await response.json();
                console.log('AuthCode查询API响应:', apiResult);

                loading.style.display = 'none';

                if (apiResult.code === 200) {
                    displayAuthCodeQueryResult(apiResult.data);
                    showAlert('authcode-alert', '查询完成', 'success');
                } else {
                    showAlert('authcode-alert', apiResult.message || '查询失败');
                }
            } catch (error) {
                loading.style.display = 'none';
                showAlert('authcode-alert', '网络请求失败：' + error.message);
            }
        }

        // 显示AuthCode查询结果
        function displayAuthCodeQueryResult(data) {
            const result = document.getElementById('authcode-result');
            const stats = document.getElementById('authcode-stats');
            const content = document.getElementById('authcode-content');
            const pagination = document.getElementById('authcode-pagination');

            currentAuthCodeTotal = data.total;
            currentAuthCodeTotalPages = data.totalPages;

            stats.textContent = `找到${data.total}个键，当前显示第${data.page}页`;

            if (data.keys && data.keys.length > 0) {
                content.innerHTML = `
                    <div class="key-list">
                        ${data.keys.map(keyInfo => `
                            <div class="key-item">
                                <div class="key-info">
                                    <input type="checkbox" class="authcode-key-checkbox" value="${keyInfo.key}" onchange="updateAuthCodeSelectedCount()">
                                    <div class="key-name">${keyInfo.key}</div>
                                    <div class="key-meta">类型: ${keyInfo.type} | TTL: ${formatTTLDisplay(keyInfo.ttl)}</div>
                                </div>
                                <div class="key-actions">
                                    <button class="view-btn" onclick="viewKeyFromAuthCode('${keyInfo.key}', ${JSON.stringify(keyInfo).replace(/"/g, '&quot;')})">查看</button>
                                    <button class="delete-btn" onclick="deleteKey('${keyInfo.key}')">删除</button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;

                // 显示AuthCode批量选择区域
                document.getElementById('authcode-batch-select-area').style.display = 'block';

                // 显示分页控件
                if (data.totalPages > 1) {
                    pagination.style.display = 'block';
                    updateAuthCodePagination();
                } else {
                    pagination.style.display = 'none';
                }
            } else {
                content.innerHTML = '<p>没有找到数据</p>';
                pagination.style.display = 'none';
                document.getElementById('authcode-batch-select-area').style.display = 'none';
            }

            result.style.display = 'block';
        }

        // 格式化显示值
        function formatValue(value) {
            if (value === null || value === undefined) {
                return '<span style="color: #999;">null</span>';
            }

            if (typeof value === 'object') {
                try {
                    return JSON.stringify(value, null, 2);
                } catch (e) {
                    return String(value);
                }
            }

            return String(value);
        }

        // 更新AuthCode分页控件
        function updateAuthCodePagination() {
            const pageInfo = document.getElementById('authcode-page-info');
            const prevBtn = document.getElementById('authcode-prev-btn');
            const nextBtn = document.getElementById('authcode-next-btn');

            pageInfo.textContent = `第${currentAuthCodePage}页 / 共${currentAuthCodeTotalPages}页`;
            prevBtn.disabled = currentAuthCodePage <= 1;
            nextBtn.disabled = currentAuthCodePage >= currentAuthCodeTotalPages;
        }

        // AuthCode查询上一页
        async function authcodePrevPage() {
            if (currentAuthCodePage > 1) {
                currentAuthCodePage--;
                await executeAuthCodeQuery();
            }
        }

        // AuthCode查询下一页
        async function authcodeNextPage() {
            if (currentAuthCodePage < currentAuthCodeTotalPages) {
                currentAuthCodePage++;
                await executeAuthCodeQuery();
            }
        }

        // 格式化TTL显示
        function formatTTLDisplay(ttl) {
            if (ttl === -1) {
                return '永不过期';
            } else if (ttl === -2) {
                return '键不存在';
            } else if (ttl > 0) {
                if (ttl > 86400) {
                    return `${Math.floor(ttl / 86400)}天`;
                } else if (ttl > 3600) {
                    return `${Math.floor(ttl / 3600)}小时`;
                } else if (ttl > 60) {
                    return `${Math.floor(ttl / 60)}分钟`;
                } else {
                    return `${ttl}秒`;
                }
            } else {
                return '已过期';
            }
        }

        // 从AuthCode查询结果查看键详情
        function viewKeyFromAuthCode(key, keyInfo) {
            try {
                // 解析keyInfo字符串
                const data = {
                    key: key,
                    type: keyInfo.type,
                    ttl: keyInfo.ttl,
                    value: keyInfo.value,
                    exists: true
                };
                showDataViewModal(data);
            } catch (error) {
                console.error('解析键信息失败:', error);
                // 降级到普通查看方式
                viewKey(key);
            }
        }

        // 点击弹窗外部关闭弹窗
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('dataViewModal');
            if (event.target === modal) {
                closeDataViewModal();
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeDataViewModal();
            }
        });

        // AuthCode查询全选/取消全选
        function toggleAuthCodeSelectAll() {
            const selectAll = document.getElementById('authcode-select-all');
            const checkboxes = document.querySelectorAll('.authcode-key-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateAuthCodeSelectedCount();
        }

        // 更新AuthCode查询选中数量
        function updateAuthCodeSelectedCount() {
            const checkboxes = document.querySelectorAll('.authcode-key-checkbox:checked');
            const count = checkboxes.length;
            document.getElementById('authcode-selected-count').textContent = `已选择: ${count}`;

            // 更新全选状态
            const allCheckboxes = document.querySelectorAll('.authcode-key-checkbox');
            const selectAll = document.getElementById('authcode-select-all');
            selectAll.checked = count > 0 && count === allCheckboxes.length;
        }

        // 删除AuthCode查询选中的keys
        function deleteAuthCodeBatchSelected() {
            const checkboxes = document.querySelectorAll('.authcode-key-checkbox:checked');
            const selectedKeys = Array.from(checkboxes).map(cb => cb.value);

            if (selectedKeys.length === 0) {
                showAlert('authcode-alert', '请先选择要删除的键', 'warning');
                return;
            }

            showConfirmDialog(
                `确定要删除${selectedKeys.length}个选中的键吗？`,
                selectedKeys,
                () => executeAuthCodeBatchDelete(selectedKeys)
            );
        }

        // 执行AuthCode查询的批量删除
        async function executeAuthCodeBatchDelete(keys) {
            try {
                const response = await fetch(`${getApiBaseUrl()}/redis/management/delete/batch`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Access-Token': accessToken
                    },
                    body: JSON.stringify({ keys })
                });

                const result = await response.json();
                console.log('AuthCode批量删除API响应:', result);

                if (result.code === 200) {
                    showAlert('authcode-alert', `批量删除完成，成功删除${result.data.deletedCount}个键`, 'success');
                    // 刷新AuthCode查询结果
                    executeAuthCodeQuery();
                    // 清空选择状态
                    clearAuthCodeSelection();
                } else {
                    showAlert('authcode-alert', result.message || '批量删除失败');
                }
            } catch (error) {
                showAlert('authcode-alert', '批量删除失败：' + error.message);
            }
        }

        // 清空AuthCode查询选择状态
        function clearAuthCodeSelection() {
            const checkboxes = document.querySelectorAll('.authcode-key-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('authcode-select-all').checked = false;
            updateAuthCodeSelectedCount();
        }
    </script>
</body>
</html>
