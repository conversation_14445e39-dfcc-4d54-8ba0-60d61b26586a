package com.haoys.user.project.controller;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.expand.ProjectUserExpand;
import com.haoys.user.expand.ProjectVisitStatExpand;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.service.ProjectTesteeStatisticsService;
import com.haoys.user.service.ProjectVisitConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.ArrayList;
import java.util.List;


@Api(tags = "项目录入统计管理")
@RestController
@Slf4j
@Validated
@RequestMapping("/project-stat")
public class ProjectStatManageController {
    
    
    @Autowired
    private ProjectTesteeStatisticsService projectTesteeStatisticsService;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    
    
    @ApiOperation("项目配置-录入统计(研究者)")
    @GetMapping("/list")
    public CommonResult<ProjectVisitStatExpand> getProjectUserList(@RequestParam(value = "enterpriseId", required = false) String enterpriseId,
                                                                   @RequestParam(value = "realName", required = false) String realName,
                                                                   @RequestParam(value = "username", required = false) String username,
                                                                   @RequestParam(value = "status", required = false) Integer status,
                                                                   @RequestParam(value = "activeStatus", required = false) Boolean activeStatus,
                                                                   @RequestParam(value = "lockStatus", required = false) Boolean lockStatus,
                                                                   @RequestParam(value = "projectId") String projectId,
                                                                   @RequestParam(value = "group", required = false) String group,
                                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        if(StringUtils.isEmpty(projectId)){
            return CommonResult.failed("项目id不能为空");
        }
        ProjectVisitStatExpand projectVisitStatExpand = new ProjectVisitStatExpand();
        List<ProjectVisitStatExpand.DynamicColumnHeadRowConfig> visitHeadRowList = new ArrayList<>();
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitConfigListByProjectId(projectId);
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectVisitStatExpand.DynamicColumnHeadRowConfig dynamicColumnHeadRowConfig= new ProjectVisitStatExpand.DynamicColumnHeadRowConfig();
            ProjectVisitStatExpand.FixedColumnHeadRowConfig fixedColumnHeadRowConfig = new ProjectVisitStatExpand.FixedColumnHeadRowConfig();
            
            ProjectVisitStatExpand.OneLevelHeadRowConfig oneLevelHeadRowConfig = new ProjectVisitStatExpand.OneLevelHeadRowConfig();
            oneLevelHeadRowConfig.setVisitId(projectVisitConfig.getId().toString());
            oneLevelHeadRowConfig.setVisitName(projectVisitConfig.getVisitName());
            List<ProjectVisitStatExpand.ChildLevelHeadRowConfig> childLevelHeadRowConfigList = new ArrayList<>();
            ProjectVisitStatExpand.ChildLevelHeadRowConfig childLevelHeadRowConfigSubmit = new ProjectVisitStatExpand.ChildLevelHeadRowConfig();
            childLevelHeadRowConfigSubmit.setName("提交");
            ProjectVisitStatExpand.ChildLevelHeadRowConfig childLevelHeadRowConfigCheck = new ProjectVisitStatExpand.ChildLevelHeadRowConfig();
            childLevelHeadRowConfigCheck.setName("审核");
            
            childLevelHeadRowConfigList.add(childLevelHeadRowConfigSubmit);
            childLevelHeadRowConfigList.add(childLevelHeadRowConfigCheck);
            
            oneLevelHeadRowConfig.setChildLevelHeadRowConfigList(childLevelHeadRowConfigList);
            dynamicColumnHeadRowConfig.setFixedColumnHeadRowConfig(fixedColumnHeadRowConfig);
            dynamicColumnHeadRowConfig.setOneLevelHeadRowConfig(oneLevelHeadRowConfig);
            visitHeadRowList.add(dynamicColumnHeadRowConfig);
        }
        
        projectVisitStatExpand.setVisitHeadRowList(visitHeadRowList);
        log.info("Fetching project user list for enterpriseId: {}, pageNum: {}, pageSize: {}", enterpriseId, pageNum, pageSize);
        CommonPage<ProjectUserExpand> projectUserExpandData = projectTesteeStatisticsService.getProjectUserExpandListForPage(projectVisitConfigList, projectId, enterpriseId, group, realName, username, status, activeStatus, lockStatus, pageNum, pageSize);
        projectVisitStatExpand.setProjectVisitDataList(projectUserExpandData);
        return CommonResult.success(projectVisitStatExpand);
    }
    
    
    @ApiOperation("项目配置-录入统计(参与者)")
    @GetMapping("/testee-list")
    public CommonResult<ProjectVisitStatExpand> getProjectTesteeListForBoRui(@RequestParam(value = "projectId") String projectId,
                                                                             @RequestParam(value = "testeeCode", required = false) String testeeCode,
                                                                             @RequestParam(value = "realName", required = false) String realName,
                                                                             @RequestParam(value = "pageNum") Integer pageNum,
                                                                             @RequestParam(value = "pageSize") Integer pageSize) {
        ProjectVisitStatExpand projectVisitStatExpand = new ProjectVisitStatExpand();
        List<ProjectVisitStatExpand.DynamicColumnHeadRowConfig> visitHeadRowList = new ArrayList<>();
        List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitConfigListByProjectId(projectId);
        for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
            ProjectVisitStatExpand.DynamicColumnHeadRowConfig dynamicColumnHeadRowConfig= new ProjectVisitStatExpand.DynamicColumnHeadRowConfig();
            ProjectVisitStatExpand.FixedColumnHeadRowConfig fixedColumnHeadRowConfig = new ProjectVisitStatExpand.FixedColumnHeadRowConfig();
            
            ProjectVisitStatExpand.OneLevelHeadRowConfig oneLevelHeadRowConfig = new ProjectVisitStatExpand.OneLevelHeadRowConfig();
            oneLevelHeadRowConfig.setVisitId(projectVisitConfig.getId().toString());
            oneLevelHeadRowConfig.setVisitName(projectVisitConfig.getVisitName());
            List<ProjectVisitStatExpand.ChildLevelHeadRowConfig> childLevelHeadRowConfigList = new ArrayList<>();
            ProjectVisitStatExpand.ChildLevelHeadRowConfig childLevelHeadRowConfigSubmit = new ProjectVisitStatExpand.ChildLevelHeadRowConfig();
            childLevelHeadRowConfigSubmit.setName("提交");
            ProjectVisitStatExpand.ChildLevelHeadRowConfig childLevelHeadRowConfigCheck = new ProjectVisitStatExpand.ChildLevelHeadRowConfig();
            childLevelHeadRowConfigCheck.setName("审核");
            
            childLevelHeadRowConfigList.add(childLevelHeadRowConfigSubmit);
            childLevelHeadRowConfigList.add(childLevelHeadRowConfigCheck);
            
            oneLevelHeadRowConfig.setChildLevelHeadRowConfigList(childLevelHeadRowConfigList);
            dynamicColumnHeadRowConfig.setFixedColumnHeadRowConfig(fixedColumnHeadRowConfig);
            dynamicColumnHeadRowConfig.setOneLevelHeadRowConfig(oneLevelHeadRowConfig);
            visitHeadRowList.add(dynamicColumnHeadRowConfig);
        }
        
        projectVisitStatExpand.setVisitHeadRowList(visitHeadRowList);
        CommonPage<ProjectUserExpand> projectUserExpandData = projectTesteeStatisticsService.getProjectTesteeListForBoRui(projectVisitConfigList, projectId, testeeCode, realName, pageNum, pageSize);
        projectVisitStatExpand.setProjectVisitDataList(projectUserExpandData);
        return CommonResult.success(projectVisitStatExpand);
    }
    
}
