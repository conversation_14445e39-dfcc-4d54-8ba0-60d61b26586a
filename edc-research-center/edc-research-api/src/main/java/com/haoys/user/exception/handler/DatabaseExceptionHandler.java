package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;

/**
 * 数据库异常处理器
 *
 * <p>专门处理数据库操作相关的异常</p>
 * <p>包括SQL异常、数据访问异常、约束违反等数据库相关错误</p>
 * <p>提供数据库友好的错误提示，同时保护敏感的数据库信息</p>
 * <p>保留原始GlobalExceptionHandler的审计日志功能</p>
 *
 * <h3>处理的异常类型：</h3>
 * <ul>
 *   <li><b>数据访问异常</b>：DataAccessException</li>
 *   <li><b>SQL异常</b>：SQLException</li>
 *   <li><b>数据完整性违反</b>：DataIntegrityViolationException</li>
 *   <li><b>重复键异常</b>：DuplicateKeyException</li>
 *   <li><b>空结果异常</b>：EmptyResultDataAccessException</li>
 *   <li><b>约束违反异常</b>：SQLIntegrityConstraintViolationException</li>
 * </ul>
 *
 * <h3>安全特性：</h3>
 * <ul>
 *   <li>敏感数据库信息自动过滤，防止信息泄露</li>
 *   <li>SQL语句和表结构信息不返回给客户端</li>
 *   <li>统一的数据库错误响应格式</li>
 *   <li>详细的数据库操作审计日志</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(400) // 中等优先级
public class DatabaseExceptionHandler extends BaseExceptionHandler {

    // ================================ 数据访问异常处理 ================================

    /**
     * 处理数据访问异常
     *
     * <p>处理Spring Data Access层的通用异常</p>
     * <p>这是数据库异常的基类，包含各种数据库操作异常</p>
     *
     * @param exception 数据访问异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(DataAccessException.class)
    public CommonResult<String> handleDataAccessException(DataAccessException exception, 
                                                         HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            String.format("数据访问异常: 异常类型=%s", exception.getClass().getSimpleName()));
        
        // 数据访问异常通常包含敏感信息，不返回详细信息给客户端
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "数据操作失败，请稍后重试");
    }

    /**
     * 处理SQL异常
     *
     * <p>处理底层SQL执行异常</p>
     * <p>这类异常通常包含敏感的数据库信息，需要过滤</p>
     *
     * @param exception SQL异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(SQLException.class)
    public CommonResult<String> handleSQLException(SQLException exception, HttpServletRequest request) {
        String sqlState = exception.getSQLState();
        int errorCode = exception.getErrorCode();
        
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            String.format("SQL异常: SQLState=%s, ErrorCode=%d", sqlState, errorCode));
        
        // 根据SQL状态码提供用户友好的错误信息
        String userMessage = "数据操作失败";
        
        if (sqlState != null) {
            if (sqlState.startsWith("23")) {
                // 完整性约束违反
                userMessage = "数据完整性约束违反，请检查数据";
            } else if (sqlState.startsWith("42")) {
                // 语法错误或访问规则违反
                userMessage = "数据操作请求格式错误";
            } else if (sqlState.startsWith("08")) {
                // 连接异常
                userMessage = "数据库连接异常，请稍后重试";
            } else if (sqlState.startsWith("40")) {
                // 事务回滚
                userMessage = "数据操作冲突，请重试";
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, userMessage);
    }

    /**
     * 处理数据完整性违反异常
     *
     * <p>处理数据完整性约束违反的异常</p>
     * <p>例如：外键约束、检查约束等违反</p>
     *
     * @param exception 数据完整性违反异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public CommonResult<String> handleDataIntegrityViolation(DataIntegrityViolationException exception, 
                                                            HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", "数据完整性约束违反");
        
        String message = "数据完整性约束违反";
        String exceptionMessage = exception.getMessage();
        
        // 根据具体错误提供更友好的提示
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("foreign key constraint")) {
                message = "关联数据不存在，请检查相关数据";
            } else if (exceptionMessage.contains("check constraint")) {
                message = "数据不符合业务规则，请检查输入";
            } else if (exceptionMessage.contains("not null constraint")) {
                message = "必填字段不能为空";
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, message);
    }

    /**
     * 处理重复键异常
     *
     * <p>处理唯一约束违反的异常</p>
     * <p>例如：主键重复、唯一索引冲突等</p>
     *
     * @param exception 重复键异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public CommonResult<String> handleDuplicateKeyException(DuplicateKeyException exception, 
                                                           HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", "重复键异常");
        
        String message = "数据已存在，请检查唯一性字段";
        String exceptionMessage = exception.getMessage();
        
        // 尝试从异常消息中提取更具体的信息
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("PRIMARY")) {
                message = "主键重复，该记录已存在";
            } else if (exceptionMessage.contains("UNIQUE")) {
                message = "数据重复，请检查唯一性约束字段";
            } else if (exceptionMessage.contains("Duplicate entry")) {
                message = "数据重复，请检查输入的唯一性";
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, message);
    }

    /**
     * 处理空结果异常
     *
     * <p>处理期望单个结果但查询为空的异常</p>
     * <p>通常在使用queryForObject等方法时触发</p>
     *
     * @param exception 空结果异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(EmptyResultDataAccessException.class)
    public CommonResult<String> handleEmptyResultDataAccess(EmptyResultDataAccessException exception, 
                                                           HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "INFO", "查询结果为空");
        
        return createFailedResponseWithTraceId(request, ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND, 
                                             "请求的数据不存在");
    }

    /**
     * 处理SQL完整性约束违反异常
     *
     * <p>处理底层SQL完整性约束违反异常</p>
     * <p>这是SQLException的子类，专门处理约束违反</p>
     *
     * @param exception SQL完整性约束违反异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(SQLIntegrityConstraintViolationException.class)
    public CommonResult<String> handleSQLIntegrityConstraintViolation(SQLIntegrityConstraintViolationException exception, 
                                                                     HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "WARN", "SQL完整性约束违反");
        
        String message = "数据完整性约束违反";
        String exceptionMessage = exception.getMessage();
        
        // 根据具体的约束违反类型提供友好提示
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("foreign key")) {
                message = "关联数据约束违反，请检查相关数据是否存在";
            } else if (exceptionMessage.contains("unique")) {
                message = "数据重复，请检查唯一性约束";
            } else if (exceptionMessage.contains("primary key")) {
                message = "主键重复，该记录已存在";
            } else if (exceptionMessage.contains("check")) {
                message = "数据不符合检查约束，请检查输入值";
            } else if (exceptionMessage.contains("not null")) {
                message = "必填字段不能为空";
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, message);
    }

    // ================================ 事务相关异常处理 ================================

    /**
     * 处理事务异常
     *
     * <p>处理事务管理相关的异常</p>
     *
     * @param exception 事务异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(org.springframework.transaction.TransactionException.class)
    public CommonResult<String> handleTransactionException(org.springframework.transaction.TransactionException exception, 
                                                          HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", 
            String.format("事务异常: 异常类型=%s", exception.getClass().getSimpleName()));
        
        String message = "事务处理失败";
        
        // 根据具体的事务异常类型提供更详细的信息
        if (exception instanceof org.springframework.transaction.CannotCreateTransactionException) {
            message = "无法创建事务，请检查数据库连接";
        } else if (exception instanceof org.springframework.transaction.TransactionTimedOutException) {
            message = "事务超时，请稍后重试";
        } else if (exception instanceof org.springframework.transaction.UnexpectedRollbackException) {
            message = "事务意外回滚，请重试操作";
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, message);
    }

    /**
     * 处理连接池异常
     *
     * <p>处理数据库连接池相关的异常</p>
     *
     * @param exception 连接池异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(org.springframework.jdbc.CannotGetJdbcConnectionException.class)
    public CommonResult<String> handleCannotGetJdbcConnection(org.springframework.jdbc.CannotGetJdbcConnectionException exception, 
                                                             HttpServletRequest request) {
        String traceId = logExceptionWithTraceId(request, exception, "ERROR", "无法获取数据库连接");
        
        // 连接池异常需要告警
        if (exceptionAlertService != null) {
            try {
                String alertMessage = String.format("数据库连接异常: 请求路径=%s, TraceId=%s", 
                                                   request.getRequestURI(), traceId);
                exceptionAlertService.sendSmsNotification(new String[]{"admin"}, alertMessage);
            } catch (Exception alertException) {
                log.error("发送数据库连接异常告警失败", alertException);
            }
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_RESULT_FAIL, 
                                             "数据库连接异常，请稍后重试");
    }
}
