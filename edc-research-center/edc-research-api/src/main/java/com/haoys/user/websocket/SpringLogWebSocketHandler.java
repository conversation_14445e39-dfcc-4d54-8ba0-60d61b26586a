package com.haoys.user.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Spring WebSocket日志处理器
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-29
 */
@Slf4j
@Component
public class SpringLogWebSocketHandler extends TextWebSocketHandler {

    // 存储所有连接的会话
    private static final CopyOnWriteArraySet<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
    
    // 存储客户端ID与会话的映射
    private static final ConcurrentHashMap<String, WebSocketSession> clientSessions = new ConcurrentHashMap<>();
    
    // JSON序列化工具
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 定时任务执行器
    private static ScheduledExecutorService executorService;
    
    // 是否已启动推送任务
    private static volatile boolean isPushStarted = false;

    // WebSocket日志配置
    @Value("${websocket.log.enabled:false}")
    private boolean logEnabled;

    @Value("${websocket.log.online-output-enabled:true}")
    private boolean onlineOutputEnabled;

    @Value("${websocket.log.print-enabled:true}")
    private boolean printEnabled;

    // 日志智能提取配置
    @Value("${websocket.log.smart-extract-enabled:false}")
    private boolean smartExtractEnabled;

    @Value("${websocket.log.show-full-content:true}")
    private boolean showFullContent;

    @Value("${websocket.log.extract-content-only:false}")
    private boolean extractContentOnly;

    // 日志文件路径配置
    @Value("${logging.file.name}")
    private String logFilePath;

    @Value("${logging.pattern.rolling-file-name}")
    private String rollingLogPath;

    // 记录上次读取的文件大小和最后修改时间，用于检测文件变化
    private static long lastFileSize = 0;
    private static long lastModifiedTime = 0;
    private static String currentLogFile = null;
    private static String lastLogLine = null; // 记录最后一行日志，避免重复推送

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String clientId = extractClientId(session);
        sessions.add(session);
        clientSessions.put(clientId, session);
        
        if (logEnabled) {
            log.info("🔗 Spring WebSocket连接建立成功，客户端ID: {}, 会话ID: {}, 当前连接数: {}",
                    clientId, session.getId(), sessions.size());
        }
        
        // 发送连接成功消息
        sendWelcomeMessage(session, clientId);
        
        // 重置日志文件状态，确保能读取到最新内容
        resetLogFileStatus();

        // 启动推送任务（只启动一次）
        startPushTask();
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String clientId = extractClientId(session);
        sessions.remove(session);
        clientSessions.remove(clientId);
        
        log.info("🔌 Spring WebSocket连接关闭，客户端ID: {}, 会话ID: {}, 剩余连接数: {}", 
                clientId, session.getId(), sessions.size());
        
        // 如果没有活跃连接，停止推送任务
        if (sessions.isEmpty()) {
            stopPushTask();
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String clientId = extractClientId(session);
        String payload = message.getPayload();
        
        if (logEnabled) {
            log.info("📨 收到Spring WebSocket消息，客户端ID: {}, 消息: {}", clientId, payload);
        }
        
        try {
            // 解析消息
            if (payload.contains("ping")) {
                // 回复pong
                sendMessage(session, createMessage("pong", "服务器响应", "pong"));
            } else {
                // 回显消息
                sendMessage(session, createMessage("echo", "消息回显", "收到: " + payload));
            }
        } catch (Exception e) {
            log.error("处理消息失败", e);
            sendMessage(session, createMessage("error", "处理失败", e.getMessage()));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String clientId = extractClientId(session);
        log.error("❌ Spring WebSocket传输错误，客户端ID: {}, 会话ID: {}", clientId, session.getId(), exception);
        
        // 发送错误消息
        sendMessage(session, createMessage("error", "传输错误", exception.getMessage()));
    }

    /**
     * 提取客户端ID
     */
    private String extractClientId(WebSocketSession session) {
        String uri = session.getUri().toString();
        String[] parts = uri.split("/");
        if (parts.length > 0) {
            String lastPart = parts[parts.length - 1];
            if (lastPart.contains("?")) {
                lastPart = lastPart.split("\\?")[0];
            }
            return lastPart.isEmpty() ? "unknown-" + session.getId() : lastPart;
        }
        return "unknown-" + session.getId();
    }

    /**
     * 发送欢迎消息
     */
    private void sendWelcomeMessage(WebSocketSession session, String clientId) {
        String welcomeMsg = createMessage("welcome", "连接成功", 
                String.format("欢迎 %s！Spring WebSocket连接已建立，开始接收实时日志", clientId));
        sendMessage(session, welcomeMsg);
        
        // 发送系统状态
        String statusMsg = createMessage("status", "系统状态", 
                String.format("当前连接数: %d, 服务器时间: %s", 
                        sessions.size(), LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)));
        sendMessage(session, statusMsg);
    }

    /**
     * 启动推送任务
     */
    private synchronized void startPushTask() {
        if (!isPushStarted) {
            isPushStarted = true;
            
            if (executorService == null || executorService.isShutdown()) {
                executorService = Executors.newScheduledThreadPool(2, r -> {
                    Thread thread = new Thread(r, "spring-websocket-log-push");
                    thread.setDaemon(true);
                    return thread;
                });
            }
            
            // 启动实时日志推送任务（更频繁的检查）
            executorService.scheduleWithFixedDelay(this::pushLogMessages, 1, 1, TimeUnit.SECONDS);
            
            // 启动心跳任务
            executorService.scheduleWithFixedDelay(this::sendHeartbeat, 30, 30, TimeUnit.SECONDS);
            
            log.info("🚀 Spring WebSocket推送任务已启动");
        }
    }

    /**
     * 停止推送任务
     */
    private synchronized void stopPushTask() {
        if (isPushStarted && sessions.isEmpty()) {
            isPushStarted = false;
            
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                log.info("🛑 Spring WebSocket推送任务已停止");
            }
        }
    }

    /**
     * 推送日志消息（使用最新的日志文件logFilePath内容）
     */
    private void pushLogMessages() {
        if (sessions.isEmpty()) {
            return;
        }
        try {
            // 优先读取配置的主日志文件内容
            String realLogMessage = readLatestLogFromFile();
            if (realLogMessage != null && !realLogMessage.trim().isEmpty()) {
                log.debug("📤 准备推送日志内容: {}", realLogMessage.length() > 100 ?
                        realLogMessage.substring(0, 100) + "..." : realLogMessage);

                // 解析并推送真实日志
                String[] logLines = realLogMessage.split("\n");
                int pushedCount = 0;
                for (String logLine : logLines) {
                    if (logLine.trim().isEmpty()) continue;
                    String logMessage = parseAndCreateLogMessage(logLine.trim());
                    broadcastMessage(logMessage);
                    pushedCount++;
                }

                if (pushedCount > 0) {
                    log.debug("✅ 成功推送 {} 条日志消息到 {} 个WebSocket连接", pushedCount, sessions.size());
                }
            } else {
                log.debug("🔍 未检测到新的日志内容");
            }
        } catch (Exception e) {
            log.error("推送日志消息失败", e);
        }
    }

    /**
     * 读取日志文件的最新内容（使用配置的logFilePath）
     * 改进版本：更可靠的文件变化检测和内容读取
     */
    private String readLatestLogFromFile() {
        try {
            // 获取最新的日志文件（优先使用配置的logFilePath）
            File latestLogFile = getLatestLogFile();
            if (latestLogFile == null) {
                log.debug("未找到可读取的日志文件");
                return null;
            }

            // 检查文件是否有变化（同时检查大小和修改时间）
            long currentFileSize = latestLogFile.length();
            long currentModified = latestLogFile.lastModified();
            String currentFilePath = latestLogFile.getAbsolutePath();

            // 如果是同一个文件且大小和修改时间都没有变化，则没有新内容
            if (currentFilePath.equals(currentLogFile) &&
                currentFileSize == lastFileSize &&
                currentModified == lastModifiedTime) {
                return null;
            }

            // 读取文件最后几行
            String latestContent = readLastLinesFromFile(latestLogFile, 5);
            if (latestContent != null && !latestContent.trim().isEmpty()) {
                // 检查是否是重复的日志行
                if (!latestContent.equals(lastLogLine)) {
                    // 更新记录的文件信息
                    lastFileSize = currentFileSize;
                    lastModifiedTime = currentModified;
                    currentLogFile = currentFilePath;
                    lastLogLine = latestContent;

                    log.debug("📖 从文件读取到新日志: {} (大小: {} bytes, 修改时间: {})",
                            latestLogFile.getName(), currentFileSize, new Date(currentModified));
                    return latestContent;
                }
            }

        } catch (Exception e) {
            log.debug("读取日志文件失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 获取最新的日志文件（通过API接口获取真实的文件列表）
     */
    private File getLatestLogFile() {
        try {
            // 1. 首先尝试通过API接口获取文件列表
            List<LogFileInfo> logFiles = getLogFilesFromAPI();
            if (logFiles != null && !logFiles.isEmpty()) {
                // 优先使用主日志文件（不带数字编号的.log文件）
                for (LogFileInfo fileInfo : logFiles) {
                    if (fileInfo.getName().endsWith(".log")) {
                        File mainLogFile = new File(fileInfo.getPath(), fileInfo.getName());
                        if (mainLogFile.exists() && mainLogFile.canRead()) {
                            //log.debug("使用API获取的主日志文件: {}", mainLogFile.getAbsolutePath());
                            return mainLogFile;
                        }
                    }
                }

                // 如果没有主日志文件，使用最新的滚动日志文件
                LogFileInfo latestFile = logFiles.get(0); // API返回的列表已经按时间倒序排列
                File logFile = new File(latestFile.getPath(), latestFile.getName());
                if (logFile.exists() && logFile.canRead()) {
                    //log.debug("使用API获取的最新滚动日志文件: {}", logFile.getAbsolutePath());
                    return logFile;
                }
            }
        } catch (Exception e) {
            log.debug("通过API获取日志文件失败，回退到配置路径: {}", e.getMessage());
        }

        // 2. 回退到配置的主日志文件路径
        if (logFilePath != null && !logFilePath.isEmpty()) {
            File mainLogFile = new File(logFilePath);
            if (mainLogFile.exists() && mainLogFile.isFile() && mainLogFile.canRead()) {
                log.debug("使用配置的主日志文件: {}", logFilePath);
                return mainLogFile;
            }
        }

        log.debug("未找到任何可用的日志文件");
        return null;
    }

    /**
     * 直接获取日志文件列表（重构后使用项目内部方法）
     */
    private List<LogFileInfo> getLogFilesFromAPI() {
        try {
            List<LogFileInfo> logFiles = new ArrayList<>();
            String[] logPaths = getConfiguredLogPaths();
            for (String logPath : logPaths) {
                File logsDir = new File(logPath);
                if (logsDir.exists() && logsDir.isDirectory()) {
                    File[] files = logsDir.listFiles((dir, name) ->
                        name.endsWith(".log") || name.endsWith(".out") || name.endsWith(".gz"));

                    if (files != null) {
                        // 按最后修改时间倒序排列
                        Arrays.sort(files, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));

                        for (File file : files) {
                            LogFileInfo fileInfo = new LogFileInfo();
                            fileInfo.setName(file.getName());
                            fileInfo.setPath(logPath);
                            fileInfo.setSize(file.length());
                            fileInfo.setLastModified(new Date(file.lastModified()).toString());
                            logFiles.add(fileInfo);
                        }
                    }
                }
            }

            //log.debug("直接获取到 {} 个日志文件", logFiles.size());
            return logFiles;
        } catch (Exception e) {
            log.debug("获取日志文件列表失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取配置的日志路径数组（与LogManagementController保持一致）
     */
    private String[] getConfiguredLogPaths() {
        List<String> paths = new ArrayList<>();

        // 优先使用配置的日志路径
        if (logFilePath != null && !logFilePath.isEmpty()) {
            File logFile = new File(logFilePath);
            String logDir = logFile.getParent();
            if (logDir != null) {
                paths.add(logDir);
            }
        }

        // 添加滚动日志路径
        if (rollingLogPath != null && !rollingLogPath.isEmpty()) {
            File rollingFile = new File(rollingLogPath);
            String rollingDir = rollingFile.getParent();
            if (rollingDir != null && !paths.contains(rollingDir)) {
                paths.add(rollingDir);
            }
        }

        // 回退到默认路径
        String[] defaultPaths = {new File(rollingLogPath).getParent()};
        for (String defaultPath : defaultPaths) {
            if (!paths.contains(defaultPath)) {
                paths.add(defaultPath);
            }
        }
        return paths.toArray(new String[0]);
    }



    /**
     * 重置日志文件状态
     */
    private static void resetLogFileStatus() {
        lastFileSize = 0;
        lastModifiedTime = 0;
        currentLogFile = null;
        lastLogLine = null;
        log.debug("🔄 重置日志文件状态");
    }

    /**
     * 读取文件的最后几行（改进版本）
     */
    private String readLastLinesFromFile(File file, int lines) {
        try {
            List<String> allLines = Files.readAllLines(file.toPath(), StandardCharsets.UTF_8);
            if (allLines.isEmpty()) {
                return null;
            }

            // 获取最后几行，而不是只获取最后一行
            int startIndex = Math.max(0, allLines.size() - lines);
            List<String> lastLines = allLines.subList(startIndex, allLines.size());

            // 过滤出有效的日志行
            StringBuilder result = new StringBuilder();
            for (String line : lastLines) {
                if (isValidLogLine(line)) {
                    if (result.length() > 0) {
                        result.append("\n");
                    }
                    result.append(line);
                }
            }

            String resultStr = result.toString();
            if (!resultStr.trim().isEmpty()) {
                return resultStr;
            }

        } catch (Exception e) {
            log.debug("读取日志文件失败: {}", file.getPath(), e);
        }

        return null;
    }

    /**
     * 检查是否是日志行的开始（通常以时间戳开头）
     */
    private boolean isLogLineStart(String line) {
        if (line == null || line.length() < 10) {
            return false;
        }

        // 检查是否以时间戳格式开头 (例如: 2025-06-29 12:12:44.762)
        return line.matches("^\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}\\.\\d{3}.*") ||
               line.matches("^\\d{2}:\\d{2}:\\d{2}\\.\\d{3}.*");
    }

    /**
     * 检查是否是有效的日志行
     */
    private boolean isValidLogLine(String line) {
        if (line == null || line.trim().isEmpty() || line.length() < 20) {
            return false;
        }

        // 检查是否包含日志级别
        return line.contains("INFO") || line.contains("WARN") ||
               line.contains("ERROR") || line.contains("DEBUG") ||
               line.contains("TRACE");
    }

    /**
     * 解析真实日志并创建消息（支持配置化的智能提取）
     */
    private String parseAndCreateLogMessage(String logLine) {
        try {
            // 改进的日志解析逻辑
            String level = "INFO";
            String content = logLine.trim();

            // 更精确的日志级别检测
            if (content.contains(" ERROR ") || content.contains("[ERROR]")) {
                level = "ERROR";
            } else if (content.contains(" WARN ") || content.contains("[WARN]")) {
                level = "WARN";
            } else if (content.contains(" DEBUG ") || content.contains("[DEBUG]")) {
                level = "DEBUG";
            } else if (content.contains(" INFO ") || content.contains("[INFO]")) {
                level = "INFO";
            }

            // 根据配置决定是否进行智能提取
            String finalContent;
            if (smartExtractEnabled && !showFullContent) {
                // 启用智能提取且不显示全部内容
                String extractedContent = extractLogContent(content);
                finalContent = (extractedContent != null && !extractedContent.trim().isEmpty())
                        ? extractedContent : content;

                if (logEnabled) {
                    log.debug("🧠 智能提取日志内容: 原始[{}] -> 提取[{}]",
                            content.length() > 50 ? content.substring(0, 50) + "..." : content,
                            finalContent.length() > 50 ? finalContent.substring(0, 50) + "..." : finalContent);
                }
            } else {
                // 显示全部日志内容（默认行为）
                finalContent = content;
                if (logEnabled) {
                    log.debug("📄 显示完整日志内容: {}",
                            content.length() > 100 ? content.substring(0, 100) + "..." : content);
                }
            }

            return createMessage(level, "实时日志", finalContent);

        } catch (Exception e) {
            log.debug("解析日志行失败: {}", logLine, e);
            return createMessage("INFO", "实时日志", logLine);
        }
    }

    /**
     * 智能提取日志内容（支持配置化控制）
     */
    private String extractLogContent(String logLine) {
        // 如果配置为显示全部内容，直接返回原始日志
        if (showFullContent) {
            return logLine;
        }

        // 如果未启用智能提取，返回原始内容
        if (!smartExtractEnabled) {
            return logLine;
        }

        try {
            // 智能提取逻辑：尝试多种日志格式的解析
            String extractedContent = performSmartExtraction(logLine);

            // 如果只提取内容且提取成功，返回提取的内容
            if (extractContentOnly && extractedContent != null && !extractedContent.trim().isEmpty()) {
                return extractedContent;
            }

            // 否则返回提取的内容，如果提取失败则返回原始内容
            return (extractedContent != null && !extractedContent.trim().isEmpty()) ? extractedContent : logLine;

        } catch (Exception e) {
            if (logEnabled) {
                log.debug("智能提取日志内容失败，返回原始内容: {}", e.getMessage());
            }
            return logLine;
        }
    }

    /**
     * 执行智能提取的具体逻辑
     */
    private String performSmartExtraction(String logLine) {
        try {
            // 格式1: 时间戳 级别 [线程] 类名 : 消息
            int colonIndex = logLine.lastIndexOf(" : ");
            if (colonIndex > 0 && colonIndex < logLine.length() - 3) {
                String extracted = logLine.substring(colonIndex + 3);
                if (logEnabled) {
                    log.debug("🎯 使用冒号分隔符提取: {}", extracted.length() > 50 ? extracted.substring(0, 50) + "..." : extracted);
                }
                return extracted;
            }

            // 格式2: 时间戳 级别 [线程] 类名 - 消息
            int dashIndex = logLine.lastIndexOf(" - ");
            if (dashIndex > 0 && dashIndex < logLine.length() - 3) {
                String extracted = logLine.substring(dashIndex + 3);
                if (logEnabled) {
                    log.debug("🎯 使用破折号分隔符提取: {}", extracted.length() > 50 ? extracted.substring(0, 50) + "..." : extracted);
                }
                return extracted;
            }

            // 格式3: 查找类名后的内容
            String[] parts = logLine.split("\\s+");
            if (parts.length >= 4) {
                // 跳过时间戳、级别、线程、类名，获取消息部分
                StringBuilder message = new StringBuilder();
                boolean foundClassName = false;
                for (int i = 0; i < parts.length; i++) {
                    if (foundClassName) {
                        if (message.length() > 0) message.append(" ");
                        message.append(parts[i]);
                    } else if (parts[i].contains(".") && parts[i].length() > 10) {
                        // 可能是类名
                        foundClassName = true;
                    }
                }
                if (message.length() > 0) {
                    String extracted = message.toString();
                    if (logEnabled) {
                        log.debug("🎯 使用类名分析提取: {}", extracted.length() > 50 ? extracted.substring(0, 50) + "..." : extracted);
                    }
                    return extracted;
                }
            }

            // 如果都无法解析，返回null表示提取失败
            if (logEnabled) {
                log.debug("🎯 智能提取失败，无法识别日志格式");
            }
            return null;

        } catch (Exception e) {
            if (logEnabled) {
                log.debug("🎯 智能提取过程中发生异常: {}", e.getMessage());
            }
            return null;
        }
    }


    /**
     * 发送心跳
     */
    private void sendHeartbeat() {
        if (!sessions.isEmpty()) {
            String heartbeat = createMessage("heartbeat", "心跳", 
                    "服务器时间: " + LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            broadcastMessage(heartbeat);
            log.debug("💓 发送心跳消息，连接数: {}", sessions.size());
        }
    }

    /**
     * 创建消息
     */
    private String createMessage(String type, String title, String content) {
        try {
            LogMessage message = new LogMessage();
            message.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            message.setLevel(type.toUpperCase());
            message.setBody(String.format("[%s] %s", title, content));
            message.setSequence(System.currentTimeMillis());
            
            return objectMapper.writeValueAsString(message);
        } catch (Exception e) {
            log.error("创建消息失败", e);
            return String.format("{\"error\":\"创建消息失败: %s\"}", e.getMessage());
        }
    }

    /**
     * 发送消息给指定会话
     */
    private void sendMessage(WebSocketSession session, String message) {
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (IOException e) {
                log.error("发送消息失败", e);
            }
        }
    }

    /**
     * 广播消息给所有连接（改进版本）
     */
    private void broadcastMessage(String message) {
        // 清理已关闭的连接
        sessions.removeIf(session -> !session.isOpen());

        if (sessions.isEmpty()) {
            log.debug("⚠️ 没有活跃的WebSocket连接，跳过消息广播");
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (WebSocketSession session : sessions) {
            try {
                if (session.isOpen()) {
                    session.sendMessage(new TextMessage(message));
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.debug("发送消息到会话 {} 失败: {}", session.getId(), e.getMessage());
                failCount++;
            }
        }

        if (logEnabled && (successCount > 0 || failCount > 0)) {
            log.debug("📡 消息广播完成 - 成功: {}, 失败: {}, 总连接数: {}",
                    successCount, failCount, sessions.size());
        }
    }

    /**
     * 获取连接状态
     */
    public static String getConnectionStatus() {
        return String.format("活跃连接数: %d, 推送任务状态: %s", sessions.size(), isPushStarted ? "运行中" : "已停止");
    }

    /**
     * 日志消息类
     */
    public static class LogMessage {
        private String timestamp;
        private String level;
        private String body;
        private Long sequence;

        // Getters and Setters
        public String getTimestamp() { return timestamp; }
        public void setTimestamp(String timestamp) { this.timestamp = timestamp; }

        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }

        public String getBody() { return body; }
        public void setBody(String body) { this.body = body; }

        public Long getSequence() { return sequence; }
        public void setSequence(Long sequence) { this.sequence = sequence; }
    }

    /**
     * 日志文件信息类
     */
    public static class LogFileInfo {
        private String name;
        private String path;
        private long size;
        private String lastModified;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }

        public long getSize() { return size; }
        public void setSize(long size) { this.size = size; }

        public String getLastModified() { return lastModified; }
        public void setLastModified(String lastModified) { this.lastModified = lastModified; }
    }
}
