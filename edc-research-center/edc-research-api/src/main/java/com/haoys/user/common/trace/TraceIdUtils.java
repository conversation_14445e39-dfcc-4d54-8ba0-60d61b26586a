package com.haoys.user.common.trace;

import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * 链路追踪工具类
 * 
 * <p>用于在分布式系统中进行链路追踪，特别适用于集群环境</p>
 * <p>支持多种TraceId来源：请求头、MDC、自动生成</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
public class TraceIdUtils {

    /**
     * TraceId在MDC中的键名
     */
    public static final String TRACE_ID_KEY = "traceId";

    /**
     * 常见的TraceId请求头名称
     */
    private static final String[] TRACE_HEADERS = {
        "X-Trace-Id",           // 自定义追踪ID
        "X-Request-Id",         // 请求ID
        "X-Correlation-Id",     // 关联ID
        "traceid",              // 小写追踪ID
        "trace-id",             // 短横线格式
        "X-B3-TraceId",         // Zipkin B3格式
        "uber-trace-id"         // Jaeger格式
    };

    /**
     * TraceId最大长度
     */
    private static final int MAX_TRACE_ID_LENGTH = 128;

    /**
     * 服务标识前缀
     */
    private static final String SERVICE_PREFIX = "EDC";

    /**
     * 获取或生成TraceId
     *
     * <p>优先级：请求头 > MDC > 生成新ID</p>
     *
     * @param httpServletRequest HTTP请求对象
     * @return TraceId
     */
    public static String getOrGenerateTraceId(HttpServletRequest httpServletRequest) {
        String traceId = extractFromHeaders(httpServletRequest);
        if (isValidTraceId(traceId)) {
            setToMDC(traceId);
            return traceId;
        }
        traceId = getFromMDC();
        if (isValidTraceId(traceId)) {
            return traceId;
        }
        traceId = generateNewTraceId();
        setToMDC(traceId);
        return traceId;
    }

    /**
     * 从请求头中提取TraceId
     *
     * @param httpServletRequest HTTP请求对象
     * @return TraceId，如果没有则返回null
     */
    public static String extractFromHeaders(HttpServletRequest httpServletRequest) {
        if (httpServletRequest == null) {
            return null;
        }
        for (String headerName : TRACE_HEADERS) {
            String traceIdValue = httpServletRequest.getHeader(headerName);
            if (isValidTraceId(traceIdValue)) {
                return sanitizeTraceId(traceIdValue);
            }
        }
        return null;
    }

    /**
     * 从MDC中获取TraceId
     *
     * @return TraceId，如果没有则返回null
     */
    public static String getFromMDC() {
        try {
            String traceId = MDC.get(TRACE_ID_KEY);
            return isValidTraceId(traceId) ? traceId : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 设置TraceId到MDC
     *
     * @param traceIdValue TraceId值
     */
    public static void setToMDC(String traceIdValue) {
        if (isValidTraceId(traceIdValue)) {
            try {
                MDC.put(TRACE_ID_KEY, traceIdValue);
            } catch (Exception exception) {
                // 忽略MDC设置失败
            }
        }
    }

    /**
     * 从MDC中移除TraceId
     */
    public static void removeFromMDC() {
        try {
            MDC.remove(TRACE_ID_KEY);
        } catch (Exception e) {
            // 忽略MDC移除失败
        }
    }

    /**
     * 清理MDC中的所有内容
     */
    public static void clearMDC() {
        try {
            MDC.clear();
        } catch (Exception e) {
            // 忽略MDC清理失败
        }
    }

    /**
     * 设置TraceId到响应头
     *
     * @param response HTTP响应对象
     * @param traceId TraceId
     */
    public static void setToResponse(HttpServletResponse response, String traceId) {
        if (response != null && isValidTraceId(traceId)) {
            try {
                response.setHeader("X-Trace-Id", traceId);
                response.setHeader("X-Request-Id", traceId);
            } catch (Exception e) {
                // 忽略响应头设置失败
            }
        }
    }

    /**
     * 生成新的TraceId
     *
     * @return 新生成的TraceId
     */
    public static String generateNewTraceId() {
        long timestamp = System.currentTimeMillis();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return String.format("%s-%d-%s", SERVICE_PREFIX, timestamp, uuid.substring(0, 8));
    }

    /**
     * 生成带请求信息的TraceId
     *
     * @param request HTTP请求对象
     * @return 新生成的TraceId
     */
    public static String generateTraceIdWithRequest(HttpServletRequest request) {
        long timestamp = System.currentTimeMillis();
        int hashCode = request != null ? Math.abs(request.hashCode()) : 0;
        int random = (int) (Math.random() * 1000);
        
        return String.format("%s-%d-%d-%03d", SERVICE_PREFIX, timestamp, hashCode, random);
    }

    /**
     * 验证TraceId是否有效
     *
     * @param traceId TraceId
     * @return true表示有效，false表示无效
     */
    public static boolean isValidTraceId(String traceId) {
        return StringUtils.hasText(traceId) && 
               traceId.length() <= MAX_TRACE_ID_LENGTH &&
               traceId.matches("[a-zA-Z0-9\\-_]+");
    }

    /**
     * 清理和格式化TraceId
     *
     * @param traceId 原始TraceId
     * @return 清理后的TraceId
     */
    public static String sanitizeTraceId(String traceId) {
        if (!StringUtils.hasText(traceId)) {
            return null;
        }

        // 去除首尾空格
        traceId = traceId.trim();

        // 限制长度
        if (traceId.length() > MAX_TRACE_ID_LENGTH) {
            traceId = traceId.substring(0, MAX_TRACE_ID_LENGTH);
        }

        // 只保留字母、数字、短横线、下划线
        traceId = traceId.replaceAll("[^a-zA-Z0-9\\-_]", "");

        return traceId.isEmpty() ? null : traceId;
    }

    /**
     * 创建子TraceId
     * 
     * <p>用于在服务内部创建子操作的追踪ID</p>
     *
     * @param parentTraceId 父TraceId
     * @param suffix 后缀标识
     * @return 子TraceId
     */
    public static String createChildTraceId(String parentTraceId, String suffix) {
        if (!isValidTraceId(parentTraceId)) {
            return generateNewTraceId();
        }

        if (!StringUtils.hasText(suffix)) {
            suffix = String.valueOf(System.currentTimeMillis() % 10000);
        }

        String childTraceId = parentTraceId + "-" + suffix;
        return sanitizeTraceId(childTraceId);
    }

    /**
     * 获取当前线程的TraceId
     *
     * @return 当前线程的TraceId，如果没有则返回null
     */
    public static String getCurrentTraceId() {
        return getFromMDC();
    }

    /**
     * 检查当前线程是否有TraceId
     *
     * @return true表示有TraceId，false表示没有
     */
    public static boolean hasCurrentTraceId() {
        return isValidTraceId(getCurrentTraceId());
    }

    /**
     * 在指定的TraceId上下文中执行操作
     *
     * @param traceId TraceId
     * @param runnable 要执行的操作
     */
    public static void runWithTraceId(String traceId, Runnable runnable) {
        String originalTraceId = getCurrentTraceId();
        try {
            setToMDC(traceId);
            runnable.run();
        } finally {
            if (originalTraceId != null) {
                setToMDC(originalTraceId);
            } else {
                removeFromMDC();
            }
        }
    }

    /**
     * 在指定的TraceId上下文中执行操作并返回结果
     *
     * @param traceId TraceId
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T callWithTraceId(String traceId, java.util.function.Supplier<T> supplier) {
        String originalTraceId = getCurrentTraceId();
        try {
            setToMDC(traceId);
            return supplier.get();
        } finally {
            if (originalTraceId != null) {
                setToMDC(originalTraceId);
            } else {
                removeFromMDC();
            }
        }
    }

    /**
     * 格式化TraceId用于日志输出
     *
     * @param traceId TraceId
     * @return 格式化后的字符串
     */
    public static String formatForLog(String traceId) {
        return isValidTraceId(traceId) ? "[" + traceId + "]" : "[NO-TRACE]";
    }

    /**
     * 从TraceId中提取时间戳（如果可能）
     *
     * @param traceId TraceId
     * @return 时间戳，如果无法提取则返回-1
     */
    public static long extractTimestamp(String traceId) {
        if (!isValidTraceId(traceId)) {
            return -1;
        }

        try {
            String[] parts = traceId.split("-");
            if (parts.length >= 2) {
                return Long.parseLong(parts[1]);
            }
        } catch (NumberFormatException e) {
            // 忽略解析失败
        }

        return -1;
    }
}
