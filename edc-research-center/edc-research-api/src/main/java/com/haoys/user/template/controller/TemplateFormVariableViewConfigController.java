package com.haoys.user.template.controller;


import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.domain.param.crf.TemplateFormGroupFieldOptionParam;
import com.haoys.user.domain.param.crf.TemplateVariableViewConfigParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormFieldOptionVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupFieldOptionVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.model.TemplateVariableViewBase;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormOptionsService;
import com.haoys.user.service.TemplateFormVariableViewConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@Api(tags = "表单字段选项联动控制管理")
@RequestMapping("/template-form-variable-view")
public class TemplateFormVariableViewConfigController extends BaseController {

    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private TemplateFormOptionsService templateFormOptionsService;
    @Autowired
    private TemplateFormVariableViewConfigService templateFormVariableViewConfigService;



    @ApiOperation(value = "查询表单联动控制变量列表")
    @GetMapping(value = "/getTemplateCurrentFormVariableList")
    public CommonResult<List<TemplateFormVariableViewVo>> getTemplateCurrentFormVariableList(@RequestParam(value = "projectId") String projectId,
                                                                                             @RequestParam(value = "formId") String formId,
                                                                                             @RequestParam(value = "optionId", required = false) String optionId) {
        List<TemplateFormVariableViewVo> dataList = templateConfigService.getTemplateCurrentFormVariableList(projectId, formId, optionId);
        return CommonResult.success(dataList);
    }

    @NoRepeatSubmit
    @Log(title = "表单配置-创建表单-保存选项联动规则", businessType = BusinessType.UPDATE, projectRecordLog = true)
    @ApiOperation("表单配置-创建表单-保存选项联动规则")
    @PostMapping(value = "/saveTemplateFormVariableViewConfig")
    public CommonResult<Object> saveTemplateFormVariableViewConfig(@Validated @RequestBody TemplateVariableViewConfigParam templateVariableViewConfigParam) {
        templateVariableViewConfigParam.setCreateUserId(getUserId());
        CustomResult data = templateFormVariableViewConfigService.saveTemplateFormVariableViewConfig(templateVariableViewConfigParam);
        return CommonResult.success(data);
    }

    @ApiOperation(value = "根据optionId查询表单联动控制变量规则列表")
    @GetMapping(value = "/getTemplateFormVariableListByOptionId")
    public CommonResult<List<TemplateFormVariableViewConfigVo>> getTemplateFormVariableListByOptionId(@RequestParam(value = "projectId") String projectId,
                                                                                                      @RequestParam(value = "formId") String formId,
                                                                                                      @RequestParam(value = "formDetailId") String formDetailId,
                                                                                                      @RequestParam(value = "formTableId", required = false) String formTableId,
                                                                                                      @RequestParam(value = "optionValueId",required = false) String optionValueId) {
        List<TemplateFormVariableViewConfigVo> dataList = new  ArrayList<>();
        String[] optionValueIdArray = optionValueId.split(",");
        if(optionValueIdArray.length > 1){
            dataList = templateFormVariableViewConfigService.getTemplateFormVariableListByOptionIds(projectId, formId, optionValueId);
        }else {
            TemplateVariableViewBase templateVariableViewBase = templateFormVariableViewConfigService.getTemplateVariableBaseInfo(projectId, formId, formDetailId, formTableId, optionValueId);
            if(templateVariableViewBase != null){
                dataList = templateFormVariableViewConfigService.getTemplateFormVariableListByOptionId(projectId, formId, templateVariableViewBase.getId().toString());
            }
        }
        return CommonResult.success(dataList);
    }


    @ApiOperation("表单字段下拉框分页列表")
    @RequestMapping(value = "/getDictionaryListForPage", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupName", value = "组名称", dataType = "string"),
    })
    public CommonResult<CommonPage<TemplateFormGroupFieldOptionVo>> getDictionaryListForPage(@RequestParam(value = "groupName")String groupName,
                                                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        CommonPage<TemplateFormGroupFieldOptionVo> dictionaryList = templateFormOptionsService.getTemplateFormGroupFieldOptionListForPage(groupName, pageNum, pageSize);
        return CommonResult.success(dictionaryList);
    }


    @NoRepeatSubmit
    @ApiOperation(value = "新增表单字段下拉框")
    @Log(title = "新增表单字段下拉框", businessType = BusinessType.INSERT)
    @RequestMapping(value = "/saveFormGroupFieldOption", method = RequestMethod.POST)
    public CommonResult<Object> saveFormGroupFieldOption(@Validated @RequestBody TemplateFormGroupFieldOptionParam templateFormGroupFieldOptionParam) {
        templateFormGroupFieldOptionParam.setCreateUserId(getUserId());
        CustomResult data = templateFormOptionsService.saveTemplateFormGroupFieldOption(templateFormGroupFieldOptionParam);
        return CommonResult.success(data.getData());
    }

    @NoRepeatSubmit
    @ApiOperation(value = "删除表单字段下拉框,parentId和id二选一")
    @Log(title = "删除表单字段下拉框,parentId和id二选一", businessType = BusinessType.DELETE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父级id,设置此参数将删除父级下边的字段选项", dataType = "string"),
            @ApiImplicitParam(name = "id", value = "父级id,设置此参数仅删除当前字段选项", dataType = "string"),
    })
    @RequestMapping(value = "/removeFormGroupFieldOption", method = RequestMethod.POST)
    public CommonResult<Object> removeFormGroupFieldOption(String parentId, String id) {
        CustomResult data = templateFormOptionsService.deleteFormGroupFieldOption(parentId, id, getUserId());
        return CommonResult.success(data.getData());
    }


    @ApiOperation(value = "根据父级id查询表单字段下拉框-添加数据时使用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父级id,", dataType = "string"),
    })
    @RequestMapping(value = "/getFormGroupFieldOptionByParentId", method = RequestMethod.GET)
    public CommonResult<List<TemplateFormGroupFieldOptionVo>> getFormGroupFieldOptionByParentId(String parentId) {
        List<TemplateFormGroupFieldOptionVo> data = templateFormOptionsService.getFormGroupFieldOptionByParentId(parentId);
        return CommonResult.success(data);
    }

    @ApiOperation(value = "根据父级id查询表单字段选项集合-CRF设置变量时使用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "父级id,", dataType = "string"),
    })
    @RequestMapping(value = "/getFormFieldOptionByParentId", method = RequestMethod.GET)
    public CommonResult<TemplateFormFieldOptionVo> getFormFieldOptionByParentId(String parentId) {
        TemplateFormFieldOptionVo data = templateFormOptionsService.getFormFieldOptionByParentId(parentId);
        return CommonResult.success(data);
    }
}
