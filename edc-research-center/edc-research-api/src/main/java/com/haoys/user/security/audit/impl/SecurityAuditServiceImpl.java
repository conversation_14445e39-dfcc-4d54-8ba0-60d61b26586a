package com.haoys.user.security.audit.impl;

import com.haoys.user.security.audit.SecurityAuditService;
import com.haoys.user.security.audit.model.SecurityAuditEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 安全审计服务实现类
 * 
 * <p>实现安全事件记录、风险评估、统计分析等功能</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@Service
public class SecurityAuditServiceImpl implements SecurityAuditService {

    private static final Logger log = LoggerFactory.getLogger(SecurityAuditServiceImpl.class);

    /**
     * IP地址事件计数器（IP -> 事件数量）
     */
    private final ConcurrentHashMap<String, AtomicLong> ipEventCounters = new ConcurrentHashMap<>();

    /**
     * 用户事件计数器（用户名 -> 事件数量）
     */
    private final ConcurrentHashMap<String, AtomicLong> userEventCounters = new ConcurrentHashMap<>();

    /**
     * IP地址最后事件时间（IP -> 最后事件时间）
     */
    private final ConcurrentHashMap<String, LocalDateTime> ipLastEventTime = new ConcurrentHashMap<>();

    /**
     * 失败登录计数器（IP -> 失败次数）
     */
    private final ConcurrentHashMap<String, AtomicLong> failedLoginCounters = new ConcurrentHashMap<>();

    @Override
    public void recordEvent(SecurityAuditEvent auditEvent) {
        try {
            // 补充服务器信息
            if (auditEvent.getServerInfo() == null) {
                auditEvent.setServerInfo(getServerInfo());
            }

            // 记录到日志
            log.info("安全审计事件: {}", auditEvent);

            // 更新统计信息
            updateStatistics(auditEvent);

            // 这里可以扩展：保存到数据库、发送到消息队列等
            // saveToDatabase(auditEvent);
            // sendToMessageQueue(auditEvent);

        } catch (Exception exception) {
            log.error("记录安全审计事件失败: eventId={}", auditEvent.getEventId(), exception);
        }
    }

    @Override
    public void recordEvent(String eventType, String userName, String clientIp, String requestPath, String description) {
        try {
            SecurityAuditEvent auditEvent = new SecurityAuditEvent();
            auditEvent.setEventType(SecurityAuditEvent.SecurityEventType.valueOf(eventType));
            auditEvent.setUserName(userName);
            auditEvent.setClientIp(clientIp);
            auditEvent.setRequestPath(requestPath);
            auditEvent.setDescription(description);
            auditEvent.setSuccess(true); // 默认成功，具体根据业务调整

            recordEvent(auditEvent);
        } catch (Exception exception) {
            log.error("记录安全事件失败: eventType={}, userName={}, clientIp={}", eventType, userName, clientIp, exception);
        }
    }

    @Override
    public void recordLoginEvent(String userName, String clientIp, String userAgent, boolean success, String failureReason) {
        SecurityAuditEvent auditEvent = new SecurityAuditEvent(SecurityAuditEvent.SecurityEventType.LOGIN, userName, clientIp, "/login");
        auditEvent.setUserAgent(userAgent);
        auditEvent.setSuccess(success);
        auditEvent.setFailureReason(failureReason);
        auditEvent.setDescription(success ? "用户登录成功" : "用户登录失败: " + failureReason);

        recordEvent(auditEvent);

        // 更新失败登录计数
        if (!success) {
            failedLoginCounters.computeIfAbsent(clientIp, k -> new AtomicLong(0)).incrementAndGet();
        }
    }

    @Override
    public void recordAccessEvent(String userName, String clientIp, String requestPath, String action, boolean success, String failureReason) {
        SecurityAuditEvent.SecurityEventType eventType = success ? SecurityAuditEvent.SecurityEventType.DATA_ACCESS : SecurityAuditEvent.SecurityEventType.ACCESS_DENIED;
        SecurityAuditEvent auditEvent = new SecurityAuditEvent(eventType, userName, clientIp, requestPath);
        auditEvent.setSuccess(success);
        auditEvent.setFailureReason(failureReason);
        auditEvent.setDescription(String.format("用户%s访问%s: %s", action, requestPath, success ? "成功" : "失败"));

        recordEvent(auditEvent);
    }

    @Override
    public void recordDataOperationEvent(String userName, String clientIp, String operation, String resourceType, String resourceId, boolean success) {
        SecurityAuditEvent auditEvent = new SecurityAuditEvent(SecurityAuditEvent.SecurityEventType.DATA_ACCESS, userName, clientIp, "/" + resourceType + "/" + resourceId);
        auditEvent.setSuccess(success);
        auditEvent.setDescription(String.format("数据操作: %s %s %s", operation, resourceType, resourceId));
        auditEvent.setEventDetails(String.format("{\"operation\":\"%s\",\"resourceType\":\"%s\",\"resourceId\":\"%s\"}", operation, resourceType, resourceId));

        recordEvent(auditEvent);
    }

    @Override
    public void recordExceptionEvent(String userName, String clientIp, String requestPath, String exceptionType, String exceptionMessage) {
        SecurityAuditEvent auditEvent = new SecurityAuditEvent(SecurityAuditEvent.SecurityEventType.SECURITY_EXCEPTION, userName, clientIp, requestPath);
        auditEvent.setSuccess(false);
        auditEvent.setFailureReason(exceptionType + ": " + exceptionMessage);
        auditEvent.setDescription("安全异常: " + exceptionType);
        auditEvent.setEventDetails(String.format("{\"exceptionType\":\"%s\",\"exceptionMessage\":\"%s\"}", exceptionType, exceptionMessage));

        recordEvent(auditEvent);
    }

    @Override
    public SecurityRiskAssessment assessSecurityRisk(String clientIp, int timeWindowMinutes) {
        try {
            // 获取IP的事件统计
            long totalEvents = ipEventCounters.getOrDefault(clientIp, new AtomicLong(0)).get();
            long failedLogins = failedLoginCounters.getOrDefault(clientIp, new AtomicLong(0)).get();

            // 计算风险评分
            int riskScore = calculateRiskScore(totalEvents, failedLogins, timeWindowMinutes);

            // 确定风险级别
            String riskLevel = determineRiskLevel(riskScore);

            // 生成描述
            String description = generateRiskDescription(totalEvents, failedLogins, timeWindowMinutes);

            // 判断是否应该阻止
            boolean shouldBlock = riskScore >= 80;

            return new SecurityRiskAssessment(riskLevel, riskScore, description, shouldBlock);

        } catch (Exception exception) {
            log.error("评估安全风险失败: clientIp={}", clientIp, exception);
            return new SecurityRiskAssessment("UNKNOWN", 0, "风险评估失败", false);
        }
    }

    @Override
    public SecurityEventStatistics getUserSecurityStatistics(String userName, int timeWindowHours) {
        try {
            long totalEvents = userEventCounters.getOrDefault(userName, new AtomicLong(0)).get();
            // 这里简化实现，实际应该从数据库查询指定时间窗口的数据
            return new SecurityEventStatistics(totalEvents, 0, 0, 0, "LOGIN");
        } catch (Exception exception) {
            log.error("获取用户安全统计失败: userName={}", userName, exception);
            return new SecurityEventStatistics(0, 0, 0, 0, "UNKNOWN");
        }
    }

    @Override
    public SecurityEventStatistics getIpSecurityStatistics(String clientIp, int timeWindowHours) {
        try {
            long totalEvents = ipEventCounters.getOrDefault(clientIp, new AtomicLong(0)).get();
            long failedLogins = failedLoginCounters.getOrDefault(clientIp, new AtomicLong(0)).get();
            // 这里简化实现，实际应该从数据库查询指定时间窗口的数据
            return new SecurityEventStatistics(totalEvents, failedLogins, 0, 0, "AUTHENTICATION_FAILED");
        } catch (Exception exception) {
            log.error("获取IP安全统计失败: clientIp={}", clientIp, exception);
            return new SecurityEventStatistics(0, 0, 0, 0, "UNKNOWN");
        }
    }

    /**
     * 更新统计信息
     */
    private void updateStatistics(SecurityAuditEvent auditEvent) {
        String clientIp = auditEvent.getClientIp();
        String userName = auditEvent.getUserName();

        // 更新IP事件计数
        if (clientIp != null) {
            ipEventCounters.computeIfAbsent(clientIp, k -> new AtomicLong(0)).incrementAndGet();
            ipLastEventTime.put(clientIp, auditEvent.getEventTime());
        }

        // 更新用户事件计数
        if (userName != null) {
            userEventCounters.computeIfAbsent(userName, k -> new AtomicLong(0)).incrementAndGet();
        }
    }

    /**
     * 计算风险评分
     */
    private int calculateRiskScore(long totalEvents, long failedLogins, int timeWindowMinutes) {
        int score = 0;

        // 基于总事件数的评分
        if (totalEvents > 100) score += 20;
        else if (totalEvents > 50) score += 10;
        else if (totalEvents > 20) score += 5;

        // 基于失败登录次数的评分
        if (failedLogins > 10) score += 50;
        else if (failedLogins > 5) score += 30;
        else if (failedLogins > 2) score += 15;

        // 基于时间窗口的评分（事件频率）
        if (timeWindowMinutes > 0) {
            double eventRate = (double) totalEvents / timeWindowMinutes;
            if (eventRate > 5) score += 30;
            else if (eventRate > 2) score += 15;
            else if (eventRate > 1) score += 5;
        }

        return Math.min(score, 100); // 最大100分
    }

    /**
     * 确定风险级别
     */
    private String determineRiskLevel(int riskScore) {
        if (riskScore >= 80) return "CRITICAL";
        else if (riskScore >= 60) return "HIGH";
        else if (riskScore >= 40) return "MEDIUM";
        else if (riskScore >= 20) return "LOW";
        else return "MINIMAL";
    }

    /**
     * 生成风险描述
     */
    private String generateRiskDescription(long totalEvents, long failedLogins, int timeWindowMinutes) {
        StringBuilder description = new StringBuilder();
        description.append(String.format("在%d分钟内发生%d个安全事件", timeWindowMinutes, totalEvents));
        if (failedLogins > 0) {
            description.append(String.format("，其中%d次登录失败", failedLogins));
        }
        return description.toString();
    }

    /**
     * 获取服务器信息
     */
    private String getServerInfo() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName() + " (" + java.net.InetAddress.getLocalHost().getHostAddress() + ")";
        } catch (Exception exception) {
            return "Unknown Server";
        }
    }
}
