<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="generator.properties"/>
    <context id="MySqlContext" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 为模型生成序列化方法-->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>
        <!-- 为生成的Java模型创建一个toString方法 -->
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
        <!--生成mapper.xml时覆盖原文件-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <commentGenerator type="com.haoys.user.Comment">
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.connectionURL}"
                        userId="${jdbc.userId}"
                        password="${jdbc.password}">
            <!--解决mysql驱动升级到8.0后不生成指定数据库代码的问题-->
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.haoys.user.model"
                            targetProject="/Users/<USER>/source_code/edc-research-master/edc-research-center/edc-research-service/src/main/java"/>

        <sqlMapGenerator targetPackage="com.haoys.user.mapper"
                         targetProject="/Users/<USER>/source_code/edc-research-master/edc-research-center/edc-research-service/src/main/resources"/>

        <javaClientGenerator targetPackage="com.haoys.user.mapper" type="XMLMAPPER"
                             targetProject="/Users/<USER>/source_code/edc-research-master/edc-research-center/edc-research-service/src/main/java"/>

        <!-- ========== 代码生成配置说明 ========== -->
        <!-- 1. 取消注释需要生成代码的表配置 -->
        <!-- 2. domainObjectName为生成的实体类名，遵循驼峰命名规范 -->
        <!-- 3. columnOverride用于处理特殊字段类型，如TEXT字段转VARCHAR避免生成BLOB -->
        <!-- 4. 生成全部表可设置tableName为% -->

        <!-- ========== 系统基础表 ========== -->
        <!-- 系统租户相关 -->
        <!--<table tableName='system_tenant' domainObjectName='SystemTenant'>
            <columnOverride column="tenant_introduce" property="tenantIntroduce" jdbcType="VARCHAR"></columnOverride>
        </table>-->
        <!--<table tableName='system_tenant_user' domainObjectName='SystemTenantUser'/>-->

        <!-- 系统菜单权限相关 -->
        <!--<table tableName='platform_system_menu' domainObjectName='PlatformSystemMenu'/>-->

        <!-- 系统文件管理相关 -->
        <!--<table tableName='system_file_info' domainObjectName='SystemFileInfo'/>-->

        <!-- 系统聊天记录相关 -->
        <!--<table tableName='system_chat_record' domainObjectName='SystemChatRecord'>
            <columnOverride column="content" property="content" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!-- 疾病数据库相关 -->
        <!--<table tableName='disease_database_user' domainObjectName='DiseaseDatabaseUser'/>-->
        <!--<table tableName='disease_database_auth' domainObjectName='DiseaseDatabaseAuth'/>-->

        <!-- 患者自定义搜索相关 -->
        <!--<table tableName='patient_custom_search' domainObjectName='PatientCustomSearch' >
            <columnOverride column="search" jdbcType="VARCHAR" property="search"></columnOverride>
        </table>-->

        <!-- 项目公告相关 -->
        <!--<table tableName='project_announcement' domainObjectName='ProjectAnnouncement'>
            <columnOverride column="content" property="content" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!-- ========== 项目管理相关表 ========== -->
        <!-- 项目基础信息 -->
        <!--<table tableName='project' domainObjectName='Project'/>-->
        <!--<table tableName='template' domainObjectName='Template'/>-->

        <!-- ========== 项目CRF表单相关表 ========== -->
        <!-- 项目字典配置 -->
        <!--<table tableName='project_dictionary' domainObjectName='ProjectDictionary'/>-->

        <!-- 表单模板配置 -->
        <!--<table tableName='template_group_lable' domainObjectName='TemplateGroupLable'/>-->
        <!--<table tableName='template_form_config' domainObjectName='TemplateFormConfig'/>-->
        <!--<table tableName='template_form_detail' domainObjectName='TemplateFormDetail'>
            <columnOverride column="expand" property="expand" jdbcType="VARCHAR"></columnOverride>
        </table>-->
        <!--<table tableName='template_form_detail_image' domainObjectName='TemplateFormDetailImage'/>-->
        <!--<table tableName='template_form_options' domainObjectName='TemplateFormOptions'/>-->
        <!--<table tableName='template_form_detail_options' domainObjectName='TemplateFormDetailOptions'/>-->
        <!--<table tableName='template_form_table' domainObjectName='TemplateFormTable'/>-->

        <!-- 表单分组和变量配置 -->
        <!--<table tableName='template_form_group_variable' domainObjectName='TemplateFormGroupVariable'/>-->
        <!--<table tableName='template_form_group_table' domainObjectName='TemplateFormGroupTable'/>-->

        <!-- 表单逻辑和规则配置 -->
        <!--<table tableName='template_form_logic' domainObjectName='TemplateFormLogic'/>-->
        <!--<table tableName='template_form_logic_data' domainObjectName='TemplateFormLogicData'/>-->
        <!--<table tableName='template_form_dvp_rule' domainObjectName='TemplateFormDvpRule'/>-->

        <!-- 流程表单配置 -->
        <!--<table tableName='flow_form_set' domainObjectName='FlowFormSet'/>-->
        <!--<table tableName='flow_form_set_expand' domainObjectName='FlowFormSetExpand'/>-->
        <!--<table tableName='flow_plan_form' domainObjectName='FlowPlanFormInfo'/>-->

        <!-- ========== 受试者管理相关表 ========== -->
        <!-- 受试者变量映射和同步 -->
        <!--<table tableName='project_testee_variable_mapping' domainObjectName='ProjectTesteeVariableMapping'/>-->
        <!--<table tableName='project_testee_variable_sync' domainObjectName='ProjectTesteeVariableSync'/>-->

        <!-- 受试者表单签名和SDV -->
        <!--<table tableName='project_form_sign_sdv' domainObjectName='ProjectFormSignSdv'/>-->

        <!-- 受试者检查和流程管理 -->
        <!--<table tableName='project_testee_check' domainObjectName='ProjectTesteeCheck'/>-->
        <!--<table tableName='project_testee_process' domainObjectName='ProjectTesteeProcess'/>-->

        <!-- 受试者访视记录 -->
        <!--<table tableName='project_visit_testee_record' domainObjectName='ProjectVisitTesteeRecord'/>-->

        <!-- 受试者质疑和回复 -->
        <!--<table tableName='project_testee_challenge' domainObjectName='ProjectTesteeChallenge'/>-->
        <!--<table tableName='project_testee_challenge_reply' domainObjectName='ProjectTesteeChallengeReply'/>-->

        <!-- ========== 模板变量视图相关表 ========== -->
        <!--<table tableName='template_variable_view_base' domainObjectName='TemplateVariableViewBase'/>-->
        <!--<table tableName='template_variable_view_config' domainObjectName='TemplateVariableViewConfig'/>-->

        <!-- ========== 受试者和访视配置相关表 ========== -->
        <!-- 受试者基础信息 -->
        <!--<table tableName='project_testee_info' domainObjectName='ProjectTesteeInfo'/>-->

        <!-- 访视用户配置 -->
        <!--<table tableName='project_visit_user' domainObjectName='ProjectVisitUser'/>-->

        <!-- 受试者和访视配置 -->
        <!--<table tableName='project_testee_config' domainObjectName='ProjectTesteeConfig'/>-->
        <!--<table tableName='project_visit_config' domainObjectName='ProjectVisitConfig'/>-->

        <!-- ========== 受试者数据提交记录相关表 ========== -->
        <!-- 受试者结果和表格数据 -->
        <!--<table tableName='project_testee_result' domainObjectName='ProjectTesteeResult'/>-->
        <!--<table tableName='project_testee_table' domainObjectName='ProjectTesteeTable'/>-->

        <!-- 受试者文件和OCR识别 -->
        <!--<table tableName='project_testee_file' domainObjectName='ProjectTesteeFile'/>-->
        <!--<table tableName='project_testee_ocr' domainObjectName='ProjectTesteeOcr'>
            <columnOverride column="words_result" property="wordsResult" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="form_words_result" property="formWordsResult" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="general_accurate_result" property="generalAccurateResult" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!-- ========== 系统日志记录相关表 ========== -->
        <!-- 系统异常日志 -->
        <!--<table tableName='system_exception_log' domainObjectName='SystemExceptionLog'>
            <columnOverride column="exception_message" property="exceptionMessage" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="stack_trace_message" property="stackTraceMessage" jdbcType="VARCHAR"></columnOverride>
            <columnOverride column="description" property="description" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!-- 系统埋点日志 -->
        <!--<table tableName='system_point_log' domainObjectName='SystemPointLog'>
            <columnOverride column="content" property="content" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!-- ========== 数据导出和搜索相关表 ========== -->
        <!-- 受试者数据导出 -->
        <!--<table tableName='project_testee_export' domainObjectName='ProjectTesteeExport'/>-->

        <!-- 项目搜索收藏 -->
        <!--<table tableName='project_search_collect' domainObjectName='ProjectSearchCollect'>
            <columnOverride column="condition_group" property="conditionGroup" jdbcType="VARCHAR"></columnOverride>
        </table>-->



        <!-- ========== 项目用户权限管理相关表 ========== -->
        <!-- 项目用户基础信息 -->
        <!--<table tableName='project_user_info' domainObjectName='ProjectUserInfo'/>-->
        <!--<table tableName='project_apply_user' domainObjectName='ProjectApplyUser'/>-->

        <!-- 项目组织架构 -->
        <!--<table tableName='project_user_org' domainObjectName='ProjectUserOrg'/>-->
        <!--<table tableName='project_org_info' domainObjectName='ProjectOrgInfo'/>-->

        <!-- 项目角色权限管理 -->
        <!--<table tableName='project_role' domainObjectName='ProjectRole'/>-->
        <!--<table tableName='project_role_menu' domainObjectName='ProjectRoleMenu'/>-->
        <!--<table tableName='project_menu' domainObjectName='ProjectMenu'/>-->
        <!--<table tableName='project_user_role' domainObjectName='ProjectUserRole'/>-->

        <!-- 项目组织角色权限管理 -->
        <!--<table tableName='project_org_role' domainObjectName='ProjectOrgRole'/>-->
        <!--<table tableName='project_org_menu' domainObjectName='ProjectOrgMenu'/>-->
        <!--<table tableName='project_org_user_role' domainObjectName='ProjectOrgUserRole'/>-->
        <!--<table tableName='project_org_role_menu' domainObjectName='ProjectOrgRoleMenu'/>-->

        <!-- ========== 患者随访系统相关表 ========== -->
        <!-- 患者随访计划和任务 -->
        <!--<table tableName='project_patient_plan' domainObjectName='ProjectPatientPlan'/>-->
        <!--<table tableName='project_patient_plan_detail' domainObjectName='ProjectPatientPlanDetail'/>-->
        <!--<table tableName='project_patient_task' domainObjectName='ProjectPatientTask'/>-->
        <!--<table tableName='project_patient_task_variable' domainObjectName='ProjectPatientTaskVariable'/>-->

        <!-- 患者随访结果和日历 -->
        <!--<table tableName='project_patient_result' domainObjectName='ProjectPatientResult'/>-->
        <!--<table tableName='project_patient_calendar' domainObjectName='ProjectPatientCalendar'/>-->

        <!-- ========== 系统和项目配置相关表 ========== -->
        <!-- 项目配置基础表 -->
        <!--<table tableName='project_config_base' domainObjectName='ProjectConfigBase'/>-->
        <!--<table tableName='project_config_value' domainObjectName='ProjectConfigValue'/>-->

        <!-- 当前启用的配置表 - 项目配置模块管理 -->
        <table tableName='project_config_module' domainObjectName='ProjectConfigModule'/>

        <!-- ========== 系统消息和授权相关表 ========== -->
        <!-- 系统发送记录 -->
        <!--<table tableName='send_message_record' domainObjectName='SendMessageRecord'>
            <columnOverride column="return_data" property="returnData" jdbcType="VARCHAR"></columnOverride>
        </table>-->

        <!-- 系统授权和研究者信息 -->
        <!--<table tableName='system_authorization_info' domainObjectName='SystemAuthorizationInfo'/>-->
        <!--<table tableName='project_researchers_info' domainObjectName='ProjectResearchersInfo'/>-->



        <!--药方分析记录-->
        <!--<table tableName='project_prescription' domainObjectName='ProjectPrescription'/>
        <table tableName='project_pharmacopeia' domainObjectName='ProjectPharmacopeia'/>
        <table tableName='project_analysis_code' domainObjectName='ProjectAnalysisCode'/>
        <table tableName='project_analysis_record' domainObjectName='ProjectAnalysisRecord'/>-->


<!--        <table tableName='template_lab_config' domainObjectName='TemplateLabConfig'/>-->
<!--        <table tableName='template_lab_org_info' domainObjectName='TemplateLabOrgInfo'/>-->

<!--        <table tableName='rcts_drug_distribute_manage' domainObjectName='RctsDrugDistributeManage'/>-->
<!--        <table tableName='rcts_drug_register_manage' domainObjectName='RctsDrugRegisterManage'/>-->
<!--        <table tableName='rcts_randomized_blind_record' domainObjectName='RctsRandomizedBlindRecord'/>-->
<!--        <table tableName='rcts_randomized_config' domainObjectName='RctsRandomizedConfig'/>-->
<!--        <table tableName='rcts_randomized_params_config' domainObjectName='RctsRandomizedParamsConfig'>-->
<!--            <columnOverride column="randomized_group_config" property="RandomizedGroupConfig" jdbcType="VARCHAR"></columnOverride>-->
<!--            <columnOverride column="randomized_layer_config" property="RandomizedLayerConfig" jdbcType="VARCHAR"></columnOverride>-->
<!--        </table>-->
<!--        <table tableName='rcts_randomized_result' domainObjectName='RctsRandomizedResult'/>-->
<!--        <table tableName='rcts_testee_info' domainObjectName='RctsTesteeInfo'/>-->




        <!--数据分析平台-->
        <!--        <table tableName='analysis_platform_user_record' domainObjectName='AnalysisPlatformUserRecord'/>-->

        <!--
        <table tableName='project_prescription_mix' domainObjectName='ProjectPrescriptionMix'/>
        <table tableName='project_property_taste' domainObjectName='ProjectPropertyTaste'/>
        <table tableName='project_taste_stat' domainObjectName='ProjectTasteStat'/>
        <table tableName='project_refinement' domainObjectName='ProjectRefinement'/>
        -->

    </context>
</generatorConfiguration>
