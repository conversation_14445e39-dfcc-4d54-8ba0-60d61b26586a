<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormDetailMapper">
    <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="template_id" jdbcType="BIGINT" property="templateId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="form_id" jdbcType="BIGINT" property="formId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="lang_value" jdbcType="VARCHAR" property="langValue"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="hidden" jdbcType="BIT" property="hidden"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="require_type" jdbcType="VARCHAR" property="requireType"/>
        <result column="required" jdbcType="BIT" property="required"/>
        <result column="show_title" jdbcType="BIT" property="showTitle"/>
        <result column="show_content" jdbcType="BIT" property="showContent"/>
        <result column="placeholder" jdbcType="VARCHAR" property="placeholder"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="rules" jdbcType="VARCHAR" property="rules"/>
        <result column="combobox" jdbcType="VARCHAR" property="combobox"/>
        <result column="dic_resource" jdbcType="VARCHAR" property="dicResource"/>
        <result column="ref_dic_id" jdbcType="VARCHAR" property="refDicId"/>
        <result column="default_dic_value" jdbcType="VARCHAR" property="defaultDicValue"/>
        <result column="custom_table" jdbcType="BIT" property="customTable"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="score_value" jdbcType="DECIMAL" property="scoreValue"/>
        <result column="unit_value" jdbcType="VARCHAR" property="unitValue"/>
        <result column="style_data" jdbcType="VARCHAR" property="styleData"/>
        <result column="panel_size" jdbcType="VARCHAR" property="panelSize"/>
        <result column="custom_testee" jdbcType="BIT" property="customTestee"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="group_info" jdbcType="VARCHAR" property="groupInfo"/>
        <result column="copy_variable_id" jdbcType="BIGINT" property="copyVariableId"/>
        <result column="base_variable_id" jdbcType="BIGINT" property="baseVariableId"/>
        <result column="enable_view_config" jdbcType="BIT" property="enableViewConfig"/>
        <result column="lab_config_scope" jdbcType="VARCHAR" property="labConfigScope"/>
        <result column="ext_data_1" jdbcType="VARCHAR" property="extData1"/>
        <result column="ext_data_2" jdbcType="VARCHAR" property="extData2"/>
        <result column="ext_data_3" jdbcType="VARCHAR" property="extData3"/>
        <result column="ext_data_4" jdbcType="VARCHAR" property="extData4"/>
        <result column="ext_data_5" jdbcType="VARCHAR" property="extData5"/>
        <result column="expand" jdbcType="VARCHAR" property="expand"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="version_info" jdbcType="VARCHAR" property="versionInfo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="platform_id" jdbcType="VARCHAR" property="platformId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, template_id, project_id, form_id, type, label, field_name, lang_value, title,
        content, hidden, model, require_type, required, show_title, show_content, placeholder,
        icon, rules, combobox, dic_resource, ref_dic_id, default_dic_value, custom_table,
        default_value, score_value, unit_value, style_data, panel_size, custom_testee, group_id,
        group_info, copy_variable_id, base_variable_id, enable_view_config, lab_config_scope,
        ext_data_1, ext_data_2, ext_data_3, ext_data_4, ext_data_5, expand, sort, status,
        version_info, create_time, update_time, create_user, update_user, tenant_id, platform_id
    </sql>
    <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormDetailExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from template_form_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from template_form_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from template_form_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormDetailExample">
        delete from template_form_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.haoys.user.model.TemplateFormDetail">
        insert into template_form_detail (id, template_id, project_id,
        form_id, type, label,
        field_name, lang_value, title,
        content, hidden, model,
        require_type, required, show_title,
        show_content, placeholder, icon,
        rules, combobox, dic_resource,
        ref_dic_id, default_dic_value, custom_table,
        default_value, score_value, unit_value,
        style_data, panel_size, custom_testee,
        group_id, group_info, copy_variable_id,
        base_variable_id, enable_view_config, lab_config_scope,
        ext_data_1, ext_data_2, ext_data_3,
        ext_data_4, ext_data_5, expand,
        sort, status, version_info,
        create_time, update_time, create_user,
        update_user, tenant_id, platform_id
        )
        values (#{id,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT},
        #{formId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR},
        #{fieldName,jdbcType=VARCHAR}, #{langValue,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
        #{content,jdbcType=VARCHAR}, #{hidden,jdbcType=BIT}, #{model,jdbcType=VARCHAR},
        #{requireType,jdbcType=VARCHAR}, #{required,jdbcType=BIT}, #{showTitle,jdbcType=BIT},
        #{showContent,jdbcType=BIT}, #{placeholder,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR},
        #{rules,jdbcType=VARCHAR}, #{combobox,jdbcType=VARCHAR}, #{dicResource,jdbcType=VARCHAR},
        #{refDicId,jdbcType=VARCHAR}, #{defaultDicValue,jdbcType=VARCHAR}, #{customTable,jdbcType=BIT},
        #{defaultValue,jdbcType=VARCHAR}, #{scoreValue,jdbcType=DECIMAL}, #{unitValue,jdbcType=VARCHAR},
        #{styleData,jdbcType=VARCHAR}, #{panelSize,jdbcType=VARCHAR}, #{customTestee,jdbcType=BIT},
        #{groupId,jdbcType=BIGINT}, #{groupInfo,jdbcType=VARCHAR}, #{copyVariableId,jdbcType=BIGINT},
        #{baseVariableId,jdbcType=BIGINT}, #{enableViewConfig,jdbcType=BIT}, #{labConfigScope,jdbcType=VARCHAR},
        #{extData1,jdbcType=VARCHAR}, #{extData2,jdbcType=VARCHAR}, #{extData3,jdbcType=VARCHAR},
        #{extData4,jdbcType=VARCHAR}, #{extData5,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR},
        #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{versionInfo,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR},
        #{updateUser,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormDetail">
        insert into template_form_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="templateId != null">
                template_id,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="formId != null">
                form_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="label != null">
                label,
            </if>
            <if test="fieldName != null">
                field_name,
            </if>
            <if test="langValue != null">
                lang_value,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="hidden != null">
                hidden,
            </if>
            <if test="model != null">
                model,
            </if>
            <if test="requireType != null">
                require_type,
            </if>
            <if test="required != null">
                required,
            </if>
            <if test="showTitle != null">
                show_title,
            </if>
            <if test="showContent != null">
                show_content,
            </if>
            <if test="placeholder != null">
                placeholder,
            </if>
            <if test="icon != null">
                icon,
            </if>
            <if test="rules != null">
                rules,
            </if>
            <if test="combobox != null">
                combobox,
            </if>
            <if test="dicResource != null">
                dic_resource,
            </if>
            <if test="refDicId != null">
                ref_dic_id,
            </if>
            <if test="defaultDicValue != null">
                default_dic_value,
            </if>
            <if test="customTable != null">
                custom_table,
            </if>
            <if test="defaultValue != null">
                default_value,
            </if>
            <if test="scoreValue != null">
                score_value,
            </if>
            <if test="unitValue != null">
                unit_value,
            </if>
            <if test="styleData != null">
                style_data,
            </if>
            <if test="panelSize != null">
                panel_size,
            </if>
            <if test="customTestee != null">
                custom_testee,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="groupInfo != null">
                group_info,
            </if>
            <if test="copyVariableId != null">
                copy_variable_id,
            </if>
            <if test="baseVariableId != null">
                base_variable_id,
            </if>
            <if test="enableViewConfig != null">
                enable_view_config,
            </if>
            <if test="labConfigScope != null">
                lab_config_scope,
            </if>
            <if test="extData1 != null">
                ext_data_1,
            </if>
            <if test="extData2 != null">
                ext_data_2,
            </if>
            <if test="extData3 != null">
                ext_data_3,
            </if>
            <if test="extData4 != null">
                ext_data_4,
            </if>
            <if test="extData5 != null">
                ext_data_5,
            </if>
            <if test="expand != null">
                expand,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="versionInfo != null">
                version_info,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="platformId != null">
                platform_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="templateId != null">
                #{templateId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                #{projectId,jdbcType=BIGINT},
            </if>
            <if test="formId != null">
                #{formId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="label != null">
                #{label,jdbcType=VARCHAR},
            </if>
            <if test="fieldName != null">
                #{fieldName,jdbcType=VARCHAR},
            </if>
            <if test="langValue != null">
                #{langValue,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="hidden != null">
                #{hidden,jdbcType=BIT},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="requireType != null">
                #{requireType,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                #{required,jdbcType=BIT},
            </if>
            <if test="showTitle != null">
                #{showTitle,jdbcType=BIT},
            </if>
            <if test="showContent != null">
                #{showContent,jdbcType=BIT},
            </if>
            <if test="placeholder != null">
                #{placeholder,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                #{icon,jdbcType=VARCHAR},
            </if>
            <if test="rules != null">
                #{rules,jdbcType=VARCHAR},
            </if>
            <if test="combobox != null">
                #{combobox,jdbcType=VARCHAR},
            </if>
            <if test="dicResource != null">
                #{dicResource,jdbcType=VARCHAR},
            </if>
            <if test="refDicId != null">
                #{refDicId,jdbcType=VARCHAR},
            </if>
            <if test="defaultDicValue != null">
                #{defaultDicValue,jdbcType=VARCHAR},
            </if>
            <if test="customTable != null">
                #{customTable,jdbcType=BIT},
            </if>
            <if test="defaultValue != null">
                #{defaultValue,jdbcType=VARCHAR},
            </if>
            <if test="scoreValue != null">
                #{scoreValue,jdbcType=DECIMAL},
            </if>
            <if test="unitValue != null">
                #{unitValue,jdbcType=VARCHAR},
            </if>
            <if test="styleData != null">
                #{styleData,jdbcType=VARCHAR},
            </if>
            <if test="panelSize != null">
                #{panelSize,jdbcType=VARCHAR},
            </if>
            <if test="customTestee != null">
                #{customTestee,jdbcType=BIT},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=BIGINT},
            </if>
            <if test="groupInfo != null">
                #{groupInfo,jdbcType=VARCHAR},
            </if>
            <if test="copyVariableId != null">
                #{copyVariableId,jdbcType=BIGINT},
            </if>
            <if test="baseVariableId != null">
                #{baseVariableId,jdbcType=BIGINT},
            </if>
            <if test="enableViewConfig != null">
                #{enableViewConfig,jdbcType=BIT},
            </if>
            <if test="labConfigScope != null">
                #{labConfigScope,jdbcType=VARCHAR},
            </if>
            <if test="extData1 != null">
                #{extData1,jdbcType=VARCHAR},
            </if>
            <if test="extData2 != null">
                #{extData2,jdbcType=VARCHAR},
            </if>
            <if test="extData3 != null">
                #{extData3,jdbcType=VARCHAR},
            </if>
            <if test="extData4 != null">
                #{extData4,jdbcType=VARCHAR},
            </if>
            <if test="extData5 != null">
                #{extData5,jdbcType=VARCHAR},
            </if>
            <if test="expand != null">
                #{expand,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="versionInfo != null">
                #{versionInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                #{platformId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormDetailExample"
            resultType="java.lang.Long">
        select count(*) from template_form_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update template_form_detail
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.templateId != null">
                template_id = #{record.templateId,jdbcType=BIGINT},
            </if>
            <if test="record.projectId != null">
                project_id = #{record.projectId,jdbcType=BIGINT},
            </if>
            <if test="record.formId != null">
                form_id = #{record.formId,jdbcType=BIGINT},
            </if>
            <if test="record.type != null">
                type = #{record.type,jdbcType=VARCHAR},
            </if>
            <if test="record.label != null">
                label = #{record.label,jdbcType=VARCHAR},
            </if>
            <if test="record.fieldName != null">
                field_name = #{record.fieldName,jdbcType=VARCHAR},
            </if>
            <if test="record.langValue != null">
                lang_value = #{record.langValue,jdbcType=VARCHAR},
            </if>
            <if test="record.title != null">
                title = #{record.title,jdbcType=VARCHAR},
            </if>
            <if test="record.content != null">
                content = #{record.content,jdbcType=VARCHAR},
            </if>
            <if test="record.hidden != null">
                hidden = #{record.hidden,jdbcType=BIT},
            </if>
            <if test="record.model != null">
                model = #{record.model,jdbcType=VARCHAR},
            </if>
            <if test="record.requireType != null">
                require_type = #{record.requireType,jdbcType=VARCHAR},
            </if>
            <if test="record.required != null">
                required = #{record.required,jdbcType=BIT},
            </if>
            <if test="record.showTitle != null">
                show_title = #{record.showTitle,jdbcType=BIT},
            </if>
            <if test="record.showContent != null">
                show_content = #{record.showContent,jdbcType=BIT},
            </if>
            <if test="record.placeholder != null">
                placeholder = #{record.placeholder,jdbcType=VARCHAR},
            </if>
            <if test="record.icon != null">
                icon = #{record.icon,jdbcType=VARCHAR},
            </if>
            <if test="record.rules != null">
                rules = #{record.rules,jdbcType=VARCHAR},
            </if>
            <if test="record.combobox != null">
                combobox = #{record.combobox,jdbcType=VARCHAR},
            </if>
            <if test="record.dicResource != null">
                dic_resource = #{record.dicResource,jdbcType=VARCHAR},
            </if>
            <if test="record.refDicId != null">
                ref_dic_id = #{record.refDicId,jdbcType=VARCHAR},
            </if>
            <if test="record.defaultDicValue != null">
                default_dic_value = #{record.defaultDicValue,jdbcType=VARCHAR},
            </if>
            <if test="record.customTable != null">
                custom_table = #{record.customTable,jdbcType=BIT},
            </if>
            <if test="record.defaultValue != null">
                default_value = #{record.defaultValue,jdbcType=VARCHAR},
            </if>
            <if test="record.scoreValue != null">
                score_value = #{record.scoreValue,jdbcType=DECIMAL},
            </if>
            <if test="record.unitValue != null">
                unit_value = #{record.unitValue,jdbcType=VARCHAR},
            </if>
            <if test="record.styleData != null">
                style_data = #{record.styleData,jdbcType=VARCHAR},
            </if>
            <if test="record.panelSize != null">
                panel_size = #{record.panelSize,jdbcType=VARCHAR},
            </if>
            <if test="record.customTestee != null">
                custom_testee = #{record.customTestee,jdbcType=BIT},
            </if>
            <if test="record.groupId != null">
                group_id = #{record.groupId,jdbcType=BIGINT},
            </if>
            <if test="record.groupInfo != null">
                group_info = #{record.groupInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.copyVariableId != null">
                copy_variable_id = #{record.copyVariableId,jdbcType=BIGINT},
            </if>
            <if test="record.baseVariableId != null">
                base_variable_id = #{record.baseVariableId,jdbcType=BIGINT},
            </if>
            <if test="record.enableViewConfig != null">
                enable_view_config = #{record.enableViewConfig,jdbcType=BIT},
            </if>
            <if test="record.labConfigScope != null">
                lab_config_scope = #{record.labConfigScope,jdbcType=VARCHAR},
            </if>
            <if test="record.extData1 != null">
                ext_data_1 = #{record.extData1,jdbcType=VARCHAR},
            </if>
            <if test="record.extData2 != null">
                ext_data_2 = #{record.extData2,jdbcType=VARCHAR},
            </if>
            <if test="record.extData3 != null">
                ext_data_3 = #{record.extData3,jdbcType=VARCHAR},
            </if>
            <if test="record.extData4 != null">
                ext_data_4 = #{record.extData4,jdbcType=VARCHAR},
            </if>
            <if test="record.extData5 != null">
                ext_data_5 = #{record.extData5,jdbcType=VARCHAR},
            </if>
            <if test="record.expand != null">
                expand = #{record.expand,jdbcType=VARCHAR},
            </if>
            <if test="record.sort != null">
                sort = #{record.sort,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.versionInfo != null">
                version_info = #{record.versionInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createUser != null">
                create_user = #{record.createUser,jdbcType=VARCHAR},
            </if>
            <if test="record.updateUser != null">
                update_user = #{record.updateUser,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantId != null">
                tenant_id = #{record.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="record.platformId != null">
                platform_id = #{record.platformId,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update template_form_detail
        set id = #{record.id,jdbcType=BIGINT},
        template_id = #{record.templateId,jdbcType=BIGINT},
        project_id = #{record.projectId,jdbcType=BIGINT},
        form_id = #{record.formId,jdbcType=BIGINT},
        type = #{record.type,jdbcType=VARCHAR},
        label = #{record.label,jdbcType=VARCHAR},
        field_name = #{record.fieldName,jdbcType=VARCHAR},
        lang_value = #{record.langValue,jdbcType=VARCHAR},
        title = #{record.title,jdbcType=VARCHAR},
        content = #{record.content,jdbcType=VARCHAR},
        hidden = #{record.hidden,jdbcType=BIT},
        model = #{record.model,jdbcType=VARCHAR},
        require_type = #{record.requireType,jdbcType=VARCHAR},
        required = #{record.required,jdbcType=BIT},
        show_title = #{record.showTitle,jdbcType=BIT},
        show_content = #{record.showContent,jdbcType=BIT},
        placeholder = #{record.placeholder,jdbcType=VARCHAR},
        icon = #{record.icon,jdbcType=VARCHAR},
        rules = #{record.rules,jdbcType=VARCHAR},
        combobox = #{record.combobox,jdbcType=VARCHAR},
        dic_resource = #{record.dicResource,jdbcType=VARCHAR},
        ref_dic_id = #{record.refDicId,jdbcType=VARCHAR},
        default_dic_value = #{record.defaultDicValue,jdbcType=VARCHAR},
        custom_table = #{record.customTable,jdbcType=BIT},
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
        score_value = #{record.scoreValue,jdbcType=DECIMAL},
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
        style_data = #{record.styleData,jdbcType=VARCHAR},
        panel_size = #{record.panelSize,jdbcType=VARCHAR},
        custom_testee = #{record.customTestee,jdbcType=BIT},
        group_id = #{record.groupId,jdbcType=BIGINT},
        group_info = #{record.groupInfo,jdbcType=VARCHAR},
        copy_variable_id = #{record.copyVariableId,jdbcType=BIGINT},
        base_variable_id = #{record.baseVariableId,jdbcType=BIGINT},
        enable_view_config = #{record.enableViewConfig,jdbcType=BIT},
        lab_config_scope = #{record.labConfigScope,jdbcType=VARCHAR},
        ext_data_1 = #{record.extData1,jdbcType=VARCHAR},
        ext_data_2 = #{record.extData2,jdbcType=VARCHAR},
        ext_data_3 = #{record.extData3,jdbcType=VARCHAR},
        ext_data_4 = #{record.extData4,jdbcType=VARCHAR},
        ext_data_5 = #{record.extData5,jdbcType=VARCHAR},
        expand = #{record.expand,jdbcType=VARCHAR},
        sort = #{record.sort,jdbcType=INTEGER},
        status = #{record.status,jdbcType=VARCHAR},
        version_info = #{record.versionInfo,jdbcType=VARCHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        create_user = #{record.createUser,jdbcType=VARCHAR},
        update_user = #{record.updateUser,jdbcType=VARCHAR},
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
        platform_id = #{record.platformId,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormDetail">
        update template_form_detail
        <set>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                project_id = #{projectId,jdbcType=BIGINT},
            </if>
            <if test="formId != null">
                form_id = #{formId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="label != null">
                label = #{label,jdbcType=VARCHAR},
            </if>
            <if test="fieldName != null">
                field_name = #{fieldName,jdbcType=VARCHAR},
            </if>
            <if test="langValue != null">
                lang_value = #{langValue,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="hidden != null">
                hidden = #{hidden,jdbcType=BIT},
            </if>
            <if test="model != null">
                model = #{model,jdbcType=VARCHAR},
            </if>
            <if test="requireType != null">
                require_type = #{requireType,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                required = #{required,jdbcType=BIT},
            </if>
            <if test="showTitle != null">
                show_title = #{showTitle,jdbcType=BIT},
            </if>
            <if test="showContent != null">
                show_content = #{showContent,jdbcType=BIT},
            </if>
            <if test="placeholder != null">
                placeholder = #{placeholder,jdbcType=VARCHAR},
            </if>
            <if test="icon != null">
                icon = #{icon,jdbcType=VARCHAR},
            </if>
            <if test="rules != null">
                rules = #{rules,jdbcType=VARCHAR},
            </if>
            <if test="combobox != null">
                combobox = #{combobox,jdbcType=VARCHAR},
            </if>
            <if test="dicResource != null">
                dic_resource = #{dicResource,jdbcType=VARCHAR},
            </if>
            <if test="refDicId != null">
                ref_dic_id = #{refDicId,jdbcType=VARCHAR},
            </if>
            <if test="defaultDicValue != null">
                default_dic_value = #{defaultDicValue,jdbcType=VARCHAR},
            </if>
            <if test="customTable != null">
                custom_table = #{customTable,jdbcType=BIT},
            </if>
            <if test="defaultValue != null">
                default_value = #{defaultValue,jdbcType=VARCHAR},
            </if>
            <if test="scoreValue != null">
                score_value = #{scoreValue,jdbcType=DECIMAL},
            </if>
            <if test="unitValue != null">
                unit_value = #{unitValue,jdbcType=VARCHAR},
            </if>
            <if test="styleData != null">
                style_data = #{styleData,jdbcType=VARCHAR},
            </if>
            <if test="panelSize != null">
                panel_size = #{panelSize,jdbcType=VARCHAR},
            </if>
            <if test="customTestee != null">
                custom_testee = #{customTestee,jdbcType=BIT},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=BIGINT},
            </if>
            <if test="groupInfo != null">
                group_info = #{groupInfo,jdbcType=VARCHAR},
            </if>
            <if test="copyVariableId != null">
                copy_variable_id = #{copyVariableId,jdbcType=BIGINT},
            </if>
            <if test="baseVariableId != null">
                base_variable_id = #{baseVariableId,jdbcType=BIGINT},
            </if>
            <if test="enableViewConfig != null">
                enable_view_config = #{enableViewConfig,jdbcType=BIT},
            </if>
            <if test="labConfigScope != null">
                lab_config_scope = #{labConfigScope,jdbcType=VARCHAR},
            </if>
            <if test="extData1 != null">
                ext_data_1 = #{extData1,jdbcType=VARCHAR},
            </if>
            <if test="extData2 != null">
                ext_data_2 = #{extData2,jdbcType=VARCHAR},
            </if>
            <if test="extData3 != null">
                ext_data_3 = #{extData3,jdbcType=VARCHAR},
            </if>
            <if test="extData4 != null">
                ext_data_4 = #{extData4,jdbcType=VARCHAR},
            </if>
            <if test="extData5 != null">
                ext_data_5 = #{extData5,jdbcType=VARCHAR},
            </if>
            <if test="expand != null">
                expand = #{expand,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="versionInfo != null">
                version_info = #{versionInfo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                platform_id = #{platformId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormDetail">
        update template_form_detail
        set template_id = #{templateId,jdbcType=BIGINT},
        project_id = #{projectId,jdbcType=BIGINT},
        form_id = #{formId,jdbcType=BIGINT},
        type = #{type,jdbcType=VARCHAR},
        label = #{label,jdbcType=VARCHAR},
        field_name = #{fieldName,jdbcType=VARCHAR},
        lang_value = #{langValue,jdbcType=VARCHAR},
        title = #{title,jdbcType=VARCHAR},
        content = #{content,jdbcType=VARCHAR},
        hidden = #{hidden,jdbcType=BIT},
        model = #{model,jdbcType=VARCHAR},
        require_type = #{requireType,jdbcType=VARCHAR},
        required = #{required,jdbcType=BIT},
        show_title = #{showTitle,jdbcType=BIT},
        show_content = #{showContent,jdbcType=BIT},
        placeholder = #{placeholder,jdbcType=VARCHAR},
        icon = #{icon,jdbcType=VARCHAR},
        rules = #{rules,jdbcType=VARCHAR},
        combobox = #{combobox,jdbcType=VARCHAR},
        dic_resource = #{dicResource,jdbcType=VARCHAR},
        ref_dic_id = #{refDicId,jdbcType=VARCHAR},
        default_dic_value = #{defaultDicValue,jdbcType=VARCHAR},
        custom_table = #{customTable,jdbcType=BIT},
        default_value = #{defaultValue,jdbcType=VARCHAR},
        score_value = #{scoreValue,jdbcType=DECIMAL},
        unit_value = #{unitValue,jdbcType=VARCHAR},
        style_data = #{styleData,jdbcType=VARCHAR},
        panel_size = #{panelSize,jdbcType=VARCHAR},
        custom_testee = #{customTestee,jdbcType=BIT},
        group_id = #{groupId,jdbcType=BIGINT},
        group_info = #{groupInfo,jdbcType=VARCHAR},
        copy_variable_id = #{copyVariableId,jdbcType=BIGINT},
        base_variable_id = #{baseVariableId,jdbcType=BIGINT},
        enable_view_config = #{enableViewConfig,jdbcType=BIT},
        lab_config_scope = #{labConfigScope,jdbcType=VARCHAR},
        ext_data_1 = #{extData1,jdbcType=VARCHAR},
        ext_data_2 = #{extData2,jdbcType=VARCHAR},
        ext_data_3 = #{extData3,jdbcType=VARCHAR},
        ext_data_4 = #{extData4,jdbcType=VARCHAR},
        ext_data_5 = #{extData5,jdbcType=VARCHAR},
        expand = #{expand,jdbcType=VARCHAR},
        sort = #{sort,jdbcType=INTEGER},
        status = #{status,jdbcType=VARCHAR},
        version_info = #{versionInfo,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        create_user = #{createUser,jdbcType=VARCHAR},
        update_user = #{updateUser,jdbcType=VARCHAR},
        tenant_id = #{tenantId,jdbcType=VARCHAR},
        platform_id = #{platformId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <!--根据projectId和VisitId查询表单明细组件列表-->
    <select id="getProjectFormTableLabelListByProjectIdAndVisitId"
            resultType="com.haoys.user.domain.vo.ecrf.TemplateFormVariableExportVo">
        SELECT
        (@rownum := @rownum + 1) AS rownum,
        t.projectId,t.visitId,t.formId,t.formName,t.formDetailId,t.label,t.fieldName,
        t.sort,t.tableCellSort,t.type,t.`options`,t.required, t.create_time createTime
        FROM
        (
        SELECT
        template_form_config.project_id projectId,
        template_form_config.id formId,
        template_form_config.form_name formName,
        template_form_detail.id formDetailId,
        template_form_detail.label,
        template_form_detail.field_name fieldName,
        template_form_detail.sort,
        0 tableCellSort,
        template_form_detail.type,
        template_form_detail.`options`,
        template_form_detail.required,
        template_form_detail.create_time
        FROM
        template_form_detail
        INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
        WHERE
        template_form_config.status = 0
        AND template_form_detail.status = 0
        AND template_form_config.project_id = #{projectId}
        <if test="visitId != null and visitId !=''">
            AND template_form_config.visit_id = #{visitId}
        </if>
        <if test="formId != null and formId !=''">
            AND template_form_config.id = #{formId}
        </if>
        <if test="formDetailId != null and formDetailId !=''">
            AND template_form_detail.id = #{formDetailId}
        </if>
        AND template_form_detail.type NOT IN ('uploadImg','table', 'dynamic_group')
        UNION ALL
        SELECT
        template_form_config.project_id projectId,
        template_form_config.id formId,
        template_form_config.form_name formName,
        template_form_detail.id formDetailId,
        CONCAT(template_form_detail.label,'-',template_form_table.label) label,
        /*CONCAT(project_testee_table.id,'-',template_form_table.`key`) `key`,*/
        template_form_table.field_name fieldName,
        template_form_detail.sort,
        template_form_table.sort tableCellSort,
        template_form_table.type,
        template_form_table.expand `options`,
        template_form_table.required,
        template_form_table.create_time
        FROM
        template_form_table
        INNER JOIN template_form_detail ON template_form_detail.id = template_form_table.form_detail_id
        INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
        /*LEFT JOIN project_testee_table ON project_testee_table.form_table_id = template_form_table.id and
        project_testee_table.status = '0'*/
        WHERE
        template_form_config.status = 0
        AND template_form_detail.status = 0
        AND template_form_table.`status` = 0
        AND template_form_config.project_id = #{projectId}
        <if test="formId != null and formId !=''">
            AND template_form_table.form_id = #{formId}
        </if>
        <if test="formTableIds != null and formTableIds !=''">
            AND template_form_table.id = #{formTableIds}
        </if>
        AND template_form_table.type != 'uploadImg'
        ) t,(SELECT @rownum := 0) r
        ORDER BY
        t.formId ASC,t.sort,t.tableCellSort+0 ASC,t.formDetailId ASC,
        t.create_time ASC
    </select>

    <select id="getTemplateFormVariableIdByName" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo">
        SELECT template_form_detail.id,
        template_form_detail.form_id formId,
        template_form_detail.label,
        template_form_detail.field_name fieldName,
        template_form_detail.sort
        FROM template_form_config
        INNER JOIN template_form_detail ON template_form_detail.form_id = template_form_config.id
        WHERE 1 = 1
        and template_form_detail.status = 0
        and template_form_config.project_id = #{projectId}
        and template_form_config.id = #{formId}
        and template_form_detail.label = #{variableName}
        ORDER BY template_form_detail.sort
        limit 1
    </select>


    <!--根据访视名称、表单名称、变量key查询表单详情-->
    <select id="getFormDetailByFormNameAndVariableName"
            resultType="com.haoys.user.domain.vo.ecrf.TemplateFormDetailExcelImportVo">
        SELECT
        template_form_config.project_id projectId,
        template_form_detail.form_id formId,
        template_form_detail.id formDetailid,
        template_form_detail.label,
        template_form_detail.field_name fieldName,
        template_form_detail.type,
        template_form_detail.`options`,
        template_form_detail.required,
        template_form_detail.unit_value unitValue,
        template_form_detail.sort,
        template_form_detail.expand
        FROM
        template_form_config
        INNER JOIN project_visit_config ON project_visit_config.id = template_form_config.visit_id
        AND project_visit_config.project_id = template_form_config.project_id
        INNER JOIN template_form_detail ON template_form_detail.form_id = template_form_config.id
        WHERE 1 = 1 and template_form_detail.status = 0 and template_form_config.template_id is NULL
        and template_form_config.project_id = #{projectId}
        and project_visit_config.visit_name = #{visitName}
        <if test="formName != null and formName !=''">
            and template_form_config.form_name = #{formName}
        </if>
        and template_form_detail.field_name = #{variableName}
        ORDER BY
        template_form_detail.sort limit 1
    </select>

    <!--根据访视表单名称和variable查询表格变量详情-->
    <select id="getFormTableInfoByFormNameAndVariableName"
            resultType="com.haoys.user.domain.vo.ecrf.TemplateFormTableExcelImportVo">
        SELECT
        template_form_config.project_id projectId,
        template_form_detail.form_id formId,
        template_form_detail.id formDetailid,
        template_form_table.id formTableId,
        template_form_table.label,
        template_form_table.field_name fieldName,
        template_form_table.type,
        template_form_table.expand,
        template_form_table.options,
        template_form_table.required,
        template_form_table.unit_value unitValue,
        template_form_table.sort,
        template_form_table.ext_data_1 extData1,
        template_form_table.ext_data_2 extData2,
        template_form_table.ext_data_3 extData3,
        template_form_table.ext_data_4 extData4
        FROM
        template_form_config
        INNER JOIN project_visit_config ON project_visit_config.id = template_form_config.visit_id
        AND project_visit_config.project_id = template_form_config.project_id
        INNER JOIN template_form_detail ON template_form_detail.form_id = template_form_config.id
        INNER JOIN template_form_table ON template_form_table.form_detail_id = template_form_detail.id
        WHERE 1 = 1 and template_form_detail.status = 0 and template_form_config.template_id IS NULL
        AND template_form_table.`status` = 0 AND template_form_table.template_id IS NULL
        AND template_form_config.project_id = #{projectId}
        AND project_visit_config.visit_name = #{visitName}
        <if test="formName != null and formName !=''">
            and template_form_config.form_name = #{formName}
        </if>
        AND template_form_table.field_name = #{variableName}
        ORDER BY
        template_form_detail.sort limit 1
    </select>

    <select id="getTemplateFormCopyVariableByGroupId" resultMap="BaseResultMap">
        select *
        from template_form_detail
        where group_id = #{id}
        and status = '0'
    </select>

    <!--查询联动控制表单变量列表-->
    <select id="getTemplateCurrentFormVariableList" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo">
        <!--SELECT
            template_form_detail_group.id groupId,template_form_detail_group.label groupName,
            template_form_detail.id formDetailId, template_form_detail.label variableName,
            template_form_table.id formTableId,template_form_table.label tableVariableName
        FROM template_form_detail
        LEFT JOIN template_form_detail template_form_detail_group ON template_form_detail_group.id = template_form_detail.group_id
        LEFT JOIN template_form_table ON template_form_table.form_detail_id = template_form_detail.id AND template_form_table.status = '0'
        WHERE 1 = 1
        AND template_form_detail.project_id = #{projectId} AND template_form_detail.form_id = #{formId}
        AND template_form_detail.custom_testee != TRUE AND template_form_detail.status = '0'-->

        SELECT
            ( @rownum := @rownum + 1 ) AS variableNumber,
            t.projectId,
            t.formId,
            t.formName,
            t.groupId,
            IFNULL(t.groupName,'') groupName,
            t.formDetailId,
            t.variableName,
            t.fieldName,
            t.formTableId,
            t.hidden,
            t.sort,
            t.tableColumnSort,
            t.type,
            t.create_time createTime
        FROM
        (
            SELECT
                template_form_config.project_id projectId,
                template_form_config.id formId,
                template_form_config.form_name formName,
                template_form_detail_group.id groupId,
                template_form_detail_group.label groupName,
                template_form_detail.id formDetailId,
                template_form_detail.label variableName,
                template_form_detail.field_name fieldName,
                template_form_detail.hidden,
                template_form_detail.sort,
                NULL formTableId,
                0 tableColumnSort,
                template_form_detail.type,
                template_form_detail.create_time
        FROM
            template_form_detail
        INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
        LEFT JOIN template_form_detail template_form_detail_group ON template_form_detail_group.id =
        template_form_detail.group_id
        WHERE 1 = 1
            <if test="projectId != null and projectId !=''">
                AND template_form_config.project_id = #{projectId}
            </if>
            <if test="projectId == null and projectId ==''">
                AND template_form_config.project_id is null
            </if>
            AND template_form_config.id = #{formId}
            AND template_form_config.STATUS = '0'
            AND template_form_detail.STATUS = '0'
            AND template_form_detail.custom_testee != TRUE UNION ALL
        SELECT
            template_form_config.project_id projectId,
            template_form_config.id formId,
            template_form_config.form_name formName,
            template_form_detail_group.id groupId,
            template_form_detail_group.label groupName,
            template_form_detail.id formDetailId,
            CONCAT( template_form_detail.label, '-', template_form_table.label ) variableName,
            template_form_table.field_name fieldName,
            template_form_table.hidden,
            template_form_detail.sort,
            template_form_table.id formTableId,
            template_form_table.sort tableColumnSort,
            template_form_table.type,
            template_form_table.create_time
        FROM
            template_form_table
        INNER JOIN template_form_detail ON template_form_detail.id = template_form_table.form_detail_id
        LEFT JOIN template_form_detail template_form_detail_group ON template_form_detail_group.id =
        template_form_detail.group_id
        INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
        WHERE 1 = 1
        <if test="projectId != null and projectId !=''">
            AND template_form_config.project_id = #{projectId}
        </if>
        <if test="projectId == null and projectId ==''">
            AND template_form_config.project_id is null
        </if>
            AND template_form_table.form_id = #{formId}
            AND template_form_config.status = '0'
            AND template_form_detail.status = '0'
            AND template_form_table.status = '0'
            AND template_form_detail.custom_testee != true
        ) t,( SELECT @rownum := 0 ) r
        ORDER BY
            t.formId ASC,
            t.sort,
            t.tableColumnSort + 0 ASC,
            t.formDetailId ASC,
            t.create_time ASC
    </select>

    <!--查询表单变量列表-->
    <select id="getTemplateVariableDetailConfig" resultType="com.haoys.user.domain.vo.ecrf.TemplateVariableVo">
        SELECT
        id variableId,
        type,
        label,
        field_name fieldName
        FROM
        template_form_detail
        WHERE
        STATUS = '0'
        AND field_name REGEXP '[0-9]'
        AND ( custom_testee = FALSE OR custom_testee IS NULL )
        UNION ALL
        SELECT
        id variableId,
        'tabColumn' AS type,
        label,
        field_name fieldName
        FROM
        template_form_table
        WHERE
        STATUS = '0'
        AND field_name REGEXP '[0-9]'
    </select>
    <select id="getFormVariableAndRuleList"
            resultType="com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo">
        SELECT
        ( @rownum := @rownum + 1 ) AS variableNumber,
        t.projectId,
        t.formId,
        t.formName,
        t.groupId,
        IFNULL(t.groupName,'') groupName,
        t.formDetailId,
        t.variableName,
        t.fieldName,
        t.formTableId,
        t.hidden,
        t.sort,
        t.tableColumnSort,
        t.type,
        t.computeRule,
        t.create_time createTime
        FROM
        (
        SELECT
        template_form_config.project_id projectId,
        template_form_config.id formId,
        template_form_config.form_name formName,
        template_form_detail_group.id groupId,
        template_form_detail_group.label groupName,
        template_form_detail.id formDetailId,
        template_form_detail.label variableName,
        template_form_detail.field_name fieldName,
        template_form_detail.hidden,
        template_form_detail.sort,
        NULL formTableId,
        0 tableColumnSort,
        template_form_detail.type,
        template_form_detail.create_time,
        template_form_variable_rule.rule_details as computeRule
        FROM
        template_form_detail
        INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
        LEFT JOIN template_form_detail template_form_detail_group ON template_form_detail_group.id =
        template_form_detail.group_id
        left JOIN template_form_variable_rule ON template_form_variable_rule.variable_id = template_form_detail.id and
        template_form_variable_rule.status=0

        WHERE 1 = 1
        AND template_form_config.project_id = #{projectId}
        AND template_form_config.id = #{formId}
        AND template_form_config.STATUS = '0'
        AND template_form_detail.STATUS = '0'
        AND template_form_detail.custom_testee !=

        TRUE UNION ALL

        SELECT
        template_form_config.project_id projectId,
        template_form_config.id formId,
        template_form_config.form_name formName,
        template_form_detail_group.id groupId,
        template_form_detail_group.label groupName,
        template_form_detail.id formDetailId,
        CONCAT( template_form_detail.label, '-', template_form_table.label ) variableName,
        template_form_table.field_name fieldName,
        template_form_table.hidden,
        template_form_detail.sort,
        template_form_table.id formTableId,
        template_form_table.sort tableColumnSort,
        template_form_table.type,
        template_form_table.create_time,
        template_form_variable_rule.rule_details as computeRule
        FROM
        template_form_table
        INNER JOIN template_form_detail ON template_form_detail.id = template_form_table.form_detail_id
        LEFT JOIN template_form_detail template_form_detail_group ON template_form_detail_group.id =
        template_form_detail.group_id
        INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
        left JOIN template_form_variable_rule ON template_form_variable_rule.variable_id = template_form_table.id and
        template_form_variable_rule.status=0
        WHERE 1 = 1
        AND template_form_config.project_id = #{projectId}
        AND template_form_table.form_id = #{formId}
        AND template_form_config.STATUS = '0'
        AND template_form_detail.STATUS = '0'
        AND template_form_table.STATUS = '0'
        AND template_form_detail.custom_testee != TRUE
        ) t,( SELECT @rownum := 0 ) r
        ORDER BY
        t.formId ASC,
        t.sort,
        t.tableColumnSort + 0 ASC,
        t.formDetailId ASC,
        t.create_time ASC
    </select>


</mapper>
