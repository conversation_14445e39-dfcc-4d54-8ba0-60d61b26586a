<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeTableMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeTable">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_expand_id" jdbcType="BIGINT" property="formExpandId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="resource_variable_id" jdbcType="BIGINT" property="resourceVariableId" />
    <result column="resource_table_id" jdbcType="BIGINT" property="resourceTableId" />
    <result column="row_no" jdbcType="BIGINT" property="rowNo" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_value" jdbcType="VARCHAR" property="fieldValue" />
    <result column="field_text" jdbcType="VARCHAR" property="fieldText" />
    <result column="unit_value" jdbcType="VARCHAR" property="unitValue" />
    <result column="unit_text" jdbcType="VARCHAR" property="unitText" />
    <result column="score_value" jdbcType="DECIMAL" property="scoreValue" />
    <result column="option_hidden" jdbcType="BIT" property="optionHidden" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="complate_status" jdbcType="VARCHAR" property="complateStatus" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, form_expand_id, form_detail_id, form_table_id,
    testee_id, group_id, resource_variable_id, resource_table_id, row_no, label, field_name,
    field_value, field_text, unit_value, unit_text, score_value, option_hidden, sort,
    complate_status, data_from, status, create_time, create_user, update_time, update_user,
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeTableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_testee_table
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_table
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeTableExample">
    delete from project_testee_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeTable">
    insert into project_testee_table (id, project_id, plan_id,
      visit_id, form_id, form_expand_id,
      form_detail_id, form_table_id, testee_id,
      group_id, resource_variable_id, resource_table_id,
      row_no, label, field_name,
      field_value, field_text, unit_value,
      unit_text, score_value, option_hidden,
      sort, complate_status, data_from,
      status, create_time, create_user,
      update_time, update_user, tenant_id,
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT},
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formExpandId,jdbcType=BIGINT},
      #{formDetailId,jdbcType=BIGINT}, #{formTableId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT},
      #{groupId,jdbcType=BIGINT}, #{resourceVariableId,jdbcType=BIGINT}, #{resourceTableId,jdbcType=BIGINT},
      #{rowNo,jdbcType=BIGINT}, #{label,jdbcType=VARCHAR}, #{fieldName,jdbcType=VARCHAR},
      #{fieldValue,jdbcType=VARCHAR}, #{fieldText,jdbcType=VARCHAR}, #{unitValue,jdbcType=VARCHAR},
      #{unitText,jdbcType=VARCHAR}, #{scoreValue,jdbcType=DECIMAL}, #{optionHidden,jdbcType=BIT},
      #{sort,jdbcType=INTEGER}, #{complateStatus,jdbcType=VARCHAR}, #{dataFrom,jdbcType=VARCHAR},
      #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR},
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeTable">
    insert into project_testee_table
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formExpandId != null">
        form_expand_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="resourceVariableId != null">
        resource_variable_id,
      </if>
      <if test="resourceTableId != null">
        resource_table_id,
      </if>
      <if test="rowNo != null">
        row_no,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="fieldValue != null">
        field_value,
      </if>
      <if test="fieldText != null">
        field_text,
      </if>
      <if test="unitValue != null">
        unit_value,
      </if>
      <if test="unitText != null">
        unit_text,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="optionHidden != null">
        option_hidden,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="complateStatus != null">
        complate_status,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formExpandId != null">
        #{formExpandId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="resourceVariableId != null">
        #{resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="resourceTableId != null">
        #{resourceTableId,jdbcType=BIGINT},
      </if>
      <if test="rowNo != null">
        #{rowNo,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldValue != null">
        #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null">
        #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="unitText != null">
        #{unitText,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="optionHidden != null">
        #{optionHidden,jdbcType=BIT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="complateStatus != null">
        #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeTableExample" resultType="java.lang.Long">
    select count(*) from project_testee_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_table
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formExpandId != null">
        form_expand_id = #{record.formExpandId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceVariableId != null">
        resource_variable_id = #{record.resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceTableId != null">
        resource_table_id = #{record.resourceTableId,jdbcType=BIGINT},
      </if>
      <if test="record.rowNo != null">
        row_no = #{record.rowNo,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldName != null">
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldValue != null">
        field_value = #{record.fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldText != null">
        field_text = #{record.fieldText,jdbcType=VARCHAR},
      </if>
      <if test="record.unitValue != null">
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
      </if>
      <if test="record.unitText != null">
        unit_text = #{record.unitText,jdbcType=VARCHAR},
      </if>
      <if test="record.scoreValue != null">
        score_value = #{record.scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="record.optionHidden != null">
        option_hidden = #{record.optionHidden,jdbcType=BIT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.complateStatus != null">
        complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_table
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_expand_id = #{record.formExpandId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      resource_variable_id = #{record.resourceVariableId,jdbcType=BIGINT},
      resource_table_id = #{record.resourceTableId,jdbcType=BIGINT},
      row_no = #{record.rowNo,jdbcType=BIGINT},
      label = #{record.label,jdbcType=VARCHAR},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      field_value = #{record.fieldValue,jdbcType=VARCHAR},
      field_text = #{record.fieldText,jdbcType=VARCHAR},
      unit_value = #{record.unitValue,jdbcType=VARCHAR},
      unit_text = #{record.unitText,jdbcType=VARCHAR},
      score_value = #{record.scoreValue,jdbcType=DECIMAL},
      option_hidden = #{record.optionHidden,jdbcType=BIT},
      sort = #{record.sort,jdbcType=INTEGER},
      complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeTable">
    update project_testee_table
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formExpandId != null">
        form_expand_id = #{formExpandId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="resourceVariableId != null">
        resource_variable_id = #{resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="resourceTableId != null">
        resource_table_id = #{resourceTableId,jdbcType=BIGINT},
      </if>
      <if test="rowNo != null">
        row_no = #{rowNo,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldValue != null">
        field_value = #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null">
        field_text = #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        unit_value = #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="unitText != null">
        unit_text = #{unitText,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="optionHidden != null">
        option_hidden = #{optionHidden,jdbcType=BIT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="complateStatus != null">
        complate_status = #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeTable">
    update project_testee_table
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_expand_id = #{formExpandId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      resource_variable_id = #{resourceVariableId,jdbcType=BIGINT},
      resource_table_id = #{resourceTableId,jdbcType=BIGINT},
      row_no = #{rowNo,jdbcType=BIGINT},
      label = #{label,jdbcType=VARCHAR},
      field_name = #{fieldName,jdbcType=VARCHAR},
      field_value = #{fieldValue,jdbcType=VARCHAR},
      field_text = #{fieldText,jdbcType=VARCHAR},
      unit_value = #{unitValue,jdbcType=VARCHAR},
      unit_text = #{unitText,jdbcType=VARCHAR},
      score_value = #{scoreValue,jdbcType=DECIMAL},
      option_hidden = #{optionHidden,jdbcType=BIT},
      sort = #{sort,jdbcType=INTEGER},
      complate_status = #{complateStatus,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!--批量插入表格记录-->
  <insert id="saveBatchProjectTesteeTableRowRecord">
    insert into project_testee_table(
            id, project_id, plan_id, visit_id, form_id, form_detail_id, form_table_id,
            testee_id, group_id, resource_variable_id, resource_table_id, row_no,
            label, field_name, field_value, field_text, unit_value, unit_text, complate_status,sort, data_from,
            status, create_time, create_user,tenant_id, platform_id) values
    <foreach item="item" index="index" collection="tableHeadRowInfoList" separator=",">
      (#{item.id},#{item.projectId},#{item.planId},#{item.visitId},#{item.formId},#{item.formDetailId},
      #{item.formTableId},#{item.testeeId},#{item.groupId},#{item.resourceVariableId},#{item.resourceTableId},#{item.rowNo},#{item.label},
      #{item.fieldName},#{item.fieldValue},#{item.fieldText},#{item.unitValue},#{item.unitText},#{item.complateStatus},#{item.sort},
      #{item.dataFrom}, #{item.status},#{item.createTime},#{item.createUser},#{item.tenantId},#{item.platformId})
    </foreach>
  </insert>

  <!--受试者CRF表格列表数据-->
  <select id="getProjectTesteeTableListForPage" resultType="com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo">
    SELECT
        project_testee_table.row_no rowNo,
        template_form_table.label,
        template_form_table.type,
        template_form_table.ref_dic_id refDicId,
        template_form_table.dic_resource dicResource,
        template_form_table.default_dic_value defaultDicValue,
        template_form_table.form_id formId,
        template_form_table.form_detail_id formDetailId,
        template_form_table.enable_view_config enableViewConfig,
        template_form_table.lab_config_scope labConfigScope,
        project_testee_table.id,
        project_testee_table.plan_id planId,
        project_testee_table.form_table_id formTableId,
        project_testee_table.field_name fieldName,
        project_testee_table.field_value fieldValue,
        project_testee_table.field_text fieldText,
        project_testee_table.unit_value unitValue,
        project_testee_table.unit_text unitText,
        project_testee_table.complate_status complateStatus,
        project_testee_table.create_time createTime,
        project_testee_table.update_time updateTime,
        project_testee_table.create_user createUser
    FROM
        template_form_table
        LEFT JOIN project_testee_table ON template_form_table.id = project_testee_table.form_table_id
    WHERE
        project_testee_table.project_id = #{projectId}
        AND project_testee_table.plan_id = #{planId}
        AND project_testee_table.visit_id = #{visitId}
        AND project_testee_table.form_id = #{formId}
        <if test="formExpandId != null and formExpandId != ''">
          AND template_form_table.form_expand_id = #{formExpandId}
        </if>
        <if test="formDetailId != null and formDetailId != ''">
          AND template_form_table.form_detail_id = #{formDetailId}
        </if>
        AND project_testee_table.testee_id = #{testeeId}
        AND project_testee_table.row_no = #{rowNumber}
        ORDER BY template_form_table.sort ASC, template_form_table.create_time ASC

  </select>

  <!--查询表格行编号记录信息-->
  <select id="getProjectTesteeTableRowNumber" resultType="java.lang.Long">
    SELECT
        project_testee_table.row_no rowNo
    FROM project_testee_table
    <if test="queryFormGroup != null and queryFormGroup == '1'.toString()">
      INNER JOIN template_form_group_table ON template_form_group_table.id = project_testee_table.form_table_id
      AND template_form_group_table.group_id = #{groupId}
      <if test="formDetailId != null and formDetailId != ''">
        AND template_form_group_table.form_detail_id = #{formDetailId}
      </if>
    </if>
    <if test="queryFormGroup != null and queryFormGroup == '0'.toString()">
      INNER JOIN template_form_table ON template_form_table.id = project_testee_table.form_table_id AND template_form_table.status = 0 AND template_form_table.form_detail_id = #{formDetailId}
    </if>
    WHERE 1=1
      AND project_testee_table.project_id = #{projectId}
      AND project_testee_table.plan_id = #{planId}
      AND project_testee_table.visit_id = #{visitId}
      AND project_testee_table.form_id = #{formId}
      <if test="formExpandId != null and formExpandId != ''">
        AND template_form_table.form_expand_id = #{formExpandId}
      </if>
      AND project_testee_table.testee_id = #{testeeId}
      AND project_testee_table.status = '0'
    <if test="taskDate != null and taskDate != ''">
      AND DATE_FORMAT(project_testee_table.create_time,'%Y-%m-%d')=#{taskDate}
    </if>
    GROUP BY project_testee_table.row_no
    <!--tableSort 表格排序方式 1-时间倒序 2-时间正序 3-sort倒序 4-sort正序-->
    ORDER BY
    <if test="tableSort == '1'.toString()">
      project_testee_table.create_time DESC
    </if>
    <if test="tableSort == '2'.toString()">
      project_testee_table.create_time ASC
    </if>
    <if test="tableSort == '3'.toString()">
      project_testee_table.sort DESC, project_testee_table.create_time DESC
    </if>
    <if test="tableSort == '4'.toString()">
      project_testee_table.sort ASC, project_testee_table.create_time ASC
    </if>
  </select>

  <!--根据编号查询普通表格行记录-->
  <select id="getProjectTesteeTableRowRecord" resultType="com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo">
    SELECT
      project_testee_table.id, project_testee_table.project_id projectId,project_testee_table.visit_id visitId,
      project_testee_table.form_id formId,project_testee_table.form_detail_id formDetailId,project_testee_table.form_table_id formTableId,
      project_testee_table.status,project_testee_table.row_no rowNumber,project_testee_table.complate_status complateStatus,
      project_testee_table.field_name fieldName, project_testee_table.field_value fieldValue, project_testee_table.unit_value unitValue,
      template_form_table.label, template_form_table.type,template_form_table.required,template_form_table.require_type,
      template_form_table.dic_resource dicResource, template_form_table.ref_dic_id refDicId
    FROM project_testee_table
    INNER JOIN template_form_table ON template_form_table.id = project_testee_table.form_table_id
    WHERE 1=1 AND project_testee_table.project_id = #{projectId}
    <if test="planId != null and planId != ''">
      AND project_testee_table.plan_id = #{planId}
    </if>
    AND project_testee_table.visit_id = #{visitId}
    AND project_testee_table.form_id = #{formId}
    <if test="formExpandId != null and formExpandId != ''">
      AND project_testee_table.form_expand_id = #{formExpandId}
    </if>
    <if test="formDetailId != null and formDetailId != ''">
      AND project_testee_table.form_detail_id = #{formDetailId}
    </if>
    <if test="formTableId != null and formTableId != ''">
      AND project_testee_table.form_table_id = #{formTableId}
    </if>
    <if test="rowNumber != null and rowNumber != ''">
      AND project_testee_table.row_no = #{rowNumber}
    </if>
    AND project_testee_table.testee_id = #{testeeId}
    AND template_form_table.status = #{status}
    ORDER BY template_form_table.sort asc,template_form_table.create_time asc
  </select>

  <!--查询受试者指定列数据-->
  <select id="getProjectTesteeTableRowFieldRecord" resultType="com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo">
    SELECT
	    project_testee_table.id, project_testee_table.project_id projectId,project_testee_table.visit_id visitId,
	    project_testee_table.form_id formId,project_testee_table.form_table_id formTableId,project_testee_table.status,
	    project_testee_table.form_table_id formTableId,
	    project_testee_table.row_no rowNumber,
        template_form_table.label,
        project_testee_table.field_name fieldName,
        project_testee_table.field_value fieldValue,
        project_testee_table.unit_value unitValue,
        project_testee_table.complate_status complateStatus
	FROM project_testee_table
	    INNER JOIN template_form_table ON template_form_table.id = project_testee_table.form_table_id
	WHERE 1=1
		AND project_testee_table.project_id = #{projectId}
        AND project_testee_table.form_table_id = #{tableId}
  </select>


  <select id="getProjectTesteeGroupTableRowRecord" resultType="com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo">
    SELECT
      project_testee_table.id,
      project_testee_table.project_id projectId,
      project_testee_table.visit_id visitId,
      project_testee_table.form_id formId,
      template_form_group_table.id formTableId,
      template_form_group_table.form_detail_id formDetailId,
      template_form_group_table.group_id groupId,
      template_form_group_table.resource_variable_id resourceVariableId,
      template_form_group_table.resource_table_id resourceTableId,
      project_testee_table.status,
      project_testee_table.row_no rowNumber,
      template_form_table.label,
      template_form_table.type,
      project_testee_table.field_name fieldName,
      project_testee_table.field_value fieldValue,
      project_testee_table.unit_value unitValue,
      project_testee_table.complate_status complateStatus
    FROM project_testee_table
      INNER JOIN template_form_group_table ON template_form_group_table.id = project_testee_table.form_table_id
	  INNER JOIN template_form_table ON template_form_table.id = template_form_group_table.resource_table_id
    WHERE 1=1
      AND project_testee_table.project_id = #{projectId}
      AND project_testee_table.plan_id = #{planId}
      AND project_testee_table.visit_id = #{visitId}
      AND project_testee_table.form_id = #{formId}
      <if test="formExpandId != null and formExpandId != ''">
        AND project_testee_table.form_expand_id = #{formExpandId}
      </if>
      AND project_testee_table.testee_id = #{testeeId}
      AND template_form_table.status = '0'
      AND project_testee_table.row_no = #{rowNumber}
    ORDER BY template_form_table.sort asc,template_form_table.create_time asc
  </select>

  <select id="getProjectTesteeGroupTableListForPage" resultType="com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo">
    SELECT
      project_testee_table.row_no rowNo, template_form_table.label, template_form_table.type,
      project_testee_table.id,project_testee_table.form_detail_id formDetailId, project_testee_table.resource_variable_id resourceVariableId,
      project_testee_table.form_table_id formTableId, project_testee_table.resource_table_id resourceTableId,
      project_testee_table.field_name fieldName, project_testee_table.field_value fieldValue,
      project_testee_table.field_text fieldText,
      template_form_table.lab_config_scope labConfigScope,project_testee_table.unit_text unitText,
      project_testee_table.unit_value unitValue, project_testee_table.complate_status complateStatus,
      project_testee_table.create_time createTime, project_testee_table.update_time updateTime
     FROM template_form_group_table
     INNER JOIN template_form_table ON template_form_table.id = template_form_group_table.resource_table_id
     INNER JOIN project_testee_table ON template_form_group_table.id = project_testee_table.form_table_id
     WHERE
      project_testee_table.project_id = #{projectId}
     AND project_testee_table.visit_id = #{visitId}
     AND project_testee_table.form_id = #{formId}
     AND template_form_group_table.form_detail_id = #{formDetailId}
     AND project_testee_table.testee_id = #{testeeId}
     AND project_testee_table.row_no = #{rowNumber}
     ORDER BY template_form_group_table.sort ASC, template_form_group_table.create_time ASC
  </select>

  <!--导出受试者表格提交数据-->
  <select id="getProjectTesteeTableRecordsWithVariableIds" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo">
    SELECT
      project_testee_info.id testeeId,
      project_testee_table.visit_id visitId,
      project_testee_table.form_id formId,
      project_visit_user.testee_code as code ,
      project_testee_info.real_name realName,
      project_visit_user.owner_org_id ownerOrgId,
      project_visit_user.owner_org_name ownerOrgName,
      project_visit_user.owner_doctor_id ownerDoctor,
      project_visit_user.create_time createTime,
      project_testee_table.form_detail_id formDetailId,
      project_testee_table.form_table_id formTableId,
      project_testee_table.row_no rowNumber,
      project_testee_table.label,
      project_testee_table.field_name fieldName,
      project_testee_table.`field_value` fieldValue,
      project_testee_table.sort tableSort,
      template_form_table.dic_resource as dicResource,
      template_form_table.type as type
    FROM
      project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
    INNER JOIN template_form_table ON template_form_table.id = project_testee_table.form_table_id and template_form_table.status = '0'
    WHERE 1 = 1 AND project_testee_table.project_id = #{projectId}
    <if test="visitId != null and visitId != ''">
      AND project_testee_table.visit_id = #{visitId}
    </if>
    <if test="formId != null and formId != ''">
      AND project_testee_table.form_id = #{formId}
    </if>
    <if test= "formDetailId != null and formDetailId != ''">
      AND project_testee_table.form_detail_id = #{formDetailId}
    </if>
    <if test= "formTableId != null and formTableId != ''">
      AND project_testee_table.form_table_id = #{formTableId}
    </if>
    <if test="testeeId != null and testeeId != ''">
      AND project_testee_info.id = #{testeeId}
    </if>
    <if test="orgId != null and orgId != ''">
      AND project_visit_user.owner_org_id = #{orgId}
    </if>
    AND project_testee_table.status = '0'
    ORDER BY CONVERT(project_testee_info.code USING gbk) ASC,
          project_testee_table.form_detail_id ASC,
          project_testee_table.row_no ASC,
          <!--project_testee_table.sort ASC,-->
          project_testee_table.create_time ASC
  </select>

  <!--查询受试者访视表单和表格提交数据-->
  <select id="getProjectTesteeFormAndTableResultByVariableIds" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableResultExportVo">
    SELECT * FROM (
      SELECT
    project_testee_info.id testeeId,
        project_testee_result.visit_id visitId,
        project_testee_result.form_detail_id formDetailId,
        project_testee_info.code,
        project_testee_info.real_name realName,
        project_testee_info.owner_org_id ownerOrgId,
        project_testee_info.owner_org_name ownerOrgName,
        project_testee_info.owner_doctor_id ownerDoctor,
        project_testee_info.create_time createTime,
        '1' AS rowNumber,
        project_testee_result.sort formDetailSort,
        '2' AS tableSort,
        project_testee_result.label,
        project_testee_result.field_name fieldName,
        IF(LENGTH(project_testee_result.field_value)=0,'',project_testee_result.field_value) fieldValue
      FROM
          project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
      WHERE 1 = 1
      AND project_testee_result.project_id = #{projectId}
      <if test="visitId != null and visitId != ''">
        AND project_testee_result.visit_id = #{visitId}
      </if>
      <if test="formId != null and formId != ''">
        AND project_testee_result.form_id = #{formId}
      </if>
      <if test= "formDetailId != null and formDetailId != ''">
        AND project_testee_result.form_detail_id = #{formDetailId}
      </if>
      <if test="testeeId != null and testeeId != ''">
        AND project_testee_info.id = #{testeeId}
      </if>
      <if test="orgId != null and orgId != ''">
        AND project_testee_info.owner_org_id = #{orgId}
      </if>
      AND project_testee_result.`status` = '0'
      AND project_testee_result.field_name NOT LIKE 'table%'
      AND project_testee_result.field_name NOT LIKE 'dynamic_group%'

    UNION ALL
      SELECT
    project_testee_info.id testeeId,
        project_testee_table.visit_id visitId,
        project_testee_table.form_detail_id formDetailId,
        project_testee_info.code,
        project_testee_info.real_name realName,
        project_testee_info.owner_org_id ownerOrgId,
        project_testee_info.owner_org_name ownerOrgName,
        project_testee_info.owner_doctor_id ownerDoctor,
        project_testee_info.create_time createTime,
        project_testee_table.row_no rowNumber,
        '' AS formDetailSort,
        project_testee_table.sort tableSort,
        project_testee_table.label,
        project_testee_table.field_name fieldName,
        project_testee_table.`field_value` fieldValue
/*        GROUP_CONCAT(IF(LENGTH(project_testee_table.field_value)=0,'-',project_testee_table.field_value)  ORDER BY project_testee_table.sort ASC SEPARATOR '\r\n') fieldValue
*/
    FROM
      project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
      WHERE 1 = 1 AND project_testee_table.project_id = #{projectId}
      <if test="visitId != null and visitId != ''">
        AND project_testee_table.visit_id = #{visitId}
      </if>
      <if test="formId != null and formId != ''">
        AND project_testee_table.form_id = #{formId}
      </if>
      <if test= "formDetailId != null and formDetailId != ''">
        AND project_testee_table.form_detail_id = #{formDetailId}
      </if>
      <if test= "formTableId != null and formTableId != ''">
        AND project_testee_table.form_table_id = #{formTableId}
      </if>
      <if test="testeeId != null and testeeId != ''">
        AND project_testee_info.id = #{testeeId}
      </if>
      <if test="orgId != null and orgId != ''">
        AND project_testee_info.owner_org_id = #{orgId}
      </if>
      AND project_testee_table.status = '0'
      /*GROUP BY project_testee_table.form_detail_id,project_testee_table.form_table_id*/
    ) t where t.testeeId IS NOT NULL
    ORDER BY CONVERT(t.code USING gbk) ASC, t.formDetailId DESC, t.rowNumber ASC, t.tableSort ASC, t.createTime ASC
  </select>

  <!--查询受试者当前访视下所有表格汇总-->
  <select id="getProjectTesteeFormAndTableCountByVariableIds" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableCountVo">
    SELECT t.project_id,t.visit_id,t.formDetailId,t.tableName,count(t.rowCount) rowCount FROM (
        SELECT
            project_testee_table.project_id,project_testee_table.visit_id,project_testee_table.testee_id,
            template_form_detail.id formDetailId,template_form_detail.label tableName,count(project_testee_table.row_no) rowCount
        FROM
            template_form_detail
            INNER JOIN template_form_table ON template_form_detail.id = template_form_table.form_detail_id
            LEFT JOIN project_testee_table ON project_testee_table.form_detail_id = template_form_table.form_detail_id
            AND project_testee_table.form_table_id = template_form_table.id
            WHERE project_testee_table.project_id = #{projectId}
            AND project_testee_table.visit_id = #{visitId}
            AND project_testee_table.testee_id = #{testeeId}
            AND project_testee_table.`status` = '0'
            GROUP BY project_testee_table.testee_id,project_testee_table.form_detail_id,project_testee_table.row_no
     ) t WHERE 1=1 GROUP BY t.rowCount

  </select>
    <select id="getProjectTesteeGroupVariableAndTable" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo">
      SELECT * FROM (
                      SELECT
                        project_testee_info.id testeeId,
                        project_testee_result.visit_id visitId,
                        project_testee_result.form_detail_id formDetailId,
                        project_testee_info.code,
                        project_testee_info.real_name realName,
                        project_testee_info.owner_org_id ownerOrgId,
                        project_testee_info.owner_org_name ownerOrgName,
                        project_testee_info.owner_doctor ownerDoctor,
                        project_testee_info.create_time createTime,
                        '1' AS rowNumber,
                        project_testee_result.sort formDetailSort,
                        '2' AS tableSort,
                        project_testee_result.label,
                        project_testee_result.field_name fieldName,
                        IF(LENGTH(project_testee_result.field_value)=0,'',project_testee_result.field_value) fieldValue,
                        '0' as  resourceTableId,
                        template_form_group_variable.resource_variable_id variableId,
                        template_form_group_variable.group_id as groupId
                      FROM
                        project_testee_info
                          INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
                          INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
                          INNER JOIN template_form_group_variable ON template_form_group_variable.id = project_testee_result.form_detail_id and template_form_group_variable.status=0
                      WHERE 1 = 1
                        AND project_testee_result.project_id = #{projectId}
                        AND project_testee_result.form_id=#{fromId}

                        <if test= "orgId != null and orgId != ''">
                          AND project_testee_info.owner_org_id=#{orgId}
                        </if>
                        AND project_testee_info.id=#{testeeId}
                        AND project_testee_result.`status` = '0'
                        AND project_testee_result.field_name NOT LIKE 'table%'
                        AND project_testee_result.field_name NOT LIKE 'dynamic_group%'
      UNION ALL
                      SELECT
                        project_testee_info.id testeeId,
                        project_testee_table.visit_id visitId,
                        project_testee_table.form_detail_id formDetailId,
                        project_testee_info.code,
                        project_testee_info.real_name realName,
                        project_testee_info.owner_org_id ownerOrgId,
                        project_testee_info.owner_org_name ownerOrgName,
                        project_testee_info.owner_doctor ownerDoctor,
                        project_testee_info.create_time createTime,
                        project_testee_table.row_no rowNumber,
                        '' AS formDetailSort,
                        project_testee_table.sort tableSort,
                        project_testee_table.label,
                        project_testee_table.field_name fieldName,
                        project_testee_table.`field_value` fieldValue,
                        template_form_group_table.resource_table_id resourceTableId,
                        template_form_group_table.resource_table_id variableId,
                        template_form_group_table.group_Id as groupId

                      FROM
                        project_testee_info
                          INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
                          INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
                          INNER JOIN template_form_group_table ON template_form_group_table.id = project_testee_table.form_table_id and template_form_group_table.status=0
                          INNER JOIN template_form_table ON template_form_table.id = template_form_group_table.resource_table_id
                      WHERE 1 = 1
                        AND project_testee_table.project_id =  #{projectId}
                        AND project_testee_table.form_id=#{fromId}
                        <if test= "orgId != null and orgId != ''">
                          AND project_testee_info.owner_org_id=#{orgId}
                        </if>
                        AND project_testee_info.id=#{testeeId}
                        AND project_testee_table.status = '0'
      ) t where t.testeeId IS NOT NULL
      ORDER BY CONVERT(t.code USING gbk) ASC,t.formDetailId DESC, t.rowNumber ASC, t.tableSort ASC, t.createTime ASC,t.groupId asc

    </select>
  <select id="getProjectTesteeGroupTableResult"
          resultType="com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo">
    SELECT
      project_testee_table.testee_id testeeId,
      project_testee_table.group_id groupId,
      project_testee_table.visit_id visitId,
      project_testee_table.form_id formId,
      project_testee_table.form_detail_id formDetailId,
      project_testee_table.resource_table_id formTableId,
      project_testee_table.row_no rowNumber,
      project_testee_table.label,
      project_testee_table.field_name fieldName,
      project_testee_table.`field_value` fieldValue,
      project_testee_table.sort tableSort,
      template_form_table.type type,
      template_form_table.dic_resource dicResource
    FROM
      project_testee_table
        INNER JOIN template_form_group_table on template_form_group_table.id=	project_testee_table.form_table_id
        INNER JOIN template_form_table ON template_form_table.id = template_form_group_table.resource_table_id
    WHERE
      1 = 1
      AND project_testee_table.project_id = #{projectId}
      AND project_testee_table.visit_id = #{visitId}
      AND project_testee_table.form_id = #{fromId}
      AND project_testee_table.resource_table_id = #{formTableId}
      AND project_testee_table.testee_id = #{testeeId}
  </select>

  <delete id="deleteByGroupId">
    update project_testee_table set status=#{status} ,update_user=#{userId},update_time=sysdate() where group_id=#{groupId}
  </delete>

</mapper>
