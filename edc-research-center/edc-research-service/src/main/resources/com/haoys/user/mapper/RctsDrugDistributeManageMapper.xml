<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.RctsDrugDistributeManageMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.RctsDrugDistributeManage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="drug_id" jdbcType="BIGINT" property="drugId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="testee_code" jdbcType="VARCHAR" property="testeeCode" />
    <result column="acronym" jdbcType="VARCHAR" property="acronym" />
    <result column="distribute_count" jdbcType="INTEGER" property="distributeCount" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="distribute_time" jdbcType="TIMESTAMP" property="distributeTime" />
    <result column="usage_start_time" jdbcType="TIMESTAMP" property="usageStartTime" />
    <result column="usage_end_time" jdbcType="TIMESTAMP" property="usageEndTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, drug_id, testee_id, testee_code, acronym, distribute_count, status, 
    expand, distribute_time, usage_start_time, usage_end_time, create_time, update_time, 
    create_user_id, update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.RctsDrugDistributeManageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rcts_drug_distribute_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rcts_drug_distribute_manage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rcts_drug_distribute_manage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.RctsDrugDistributeManageExample">
    delete from rcts_drug_distribute_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.RctsDrugDistributeManage">
    insert into rcts_drug_distribute_manage (id, project_id, drug_id, 
      testee_id, testee_code, acronym, 
      distribute_count, status, expand, 
      distribute_time, usage_start_time, usage_end_time, 
      create_time, update_time, create_user_id, 
      update_user_id, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{drugId,jdbcType=BIGINT}, 
      #{testeeId,jdbcType=BIGINT}, #{testeeCode,jdbcType=VARCHAR}, #{acronym,jdbcType=VARCHAR}, 
      #{distributeCount,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, 
      #{distributeTime,jdbcType=TIMESTAMP}, #{usageStartTime,jdbcType=TIMESTAMP}, #{usageEndTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.RctsDrugDistributeManage">
    insert into rcts_drug_distribute_manage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="drugId != null">
        drug_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="testeeCode != null">
        testee_code,
      </if>
      <if test="acronym != null">
        acronym,
      </if>
      <if test="distributeCount != null">
        distribute_count,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="distributeTime != null">
        distribute_time,
      </if>
      <if test="usageStartTime != null">
        usage_start_time,
      </if>
      <if test="usageEndTime != null">
        usage_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="drugId != null">
        #{drugId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeCode != null">
        #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="acronym != null">
        #{acronym,jdbcType=VARCHAR},
      </if>
      <if test="distributeCount != null">
        #{distributeCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="distributeTime != null">
        #{distributeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usageStartTime != null">
        #{usageStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usageEndTime != null">
        #{usageEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.RctsDrugDistributeManageExample" resultType="java.lang.Long">
    select count(*) from rcts_drug_distribute_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rcts_drug_distribute_manage
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.drugId != null">
        drug_id = #{record.drugId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeCode != null">
        testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.acronym != null">
        acronym = #{record.acronym,jdbcType=VARCHAR},
      </if>
      <if test="record.distributeCount != null">
        distribute_count = #{record.distributeCount,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.distributeTime != null">
        distribute_time = #{record.distributeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.usageStartTime != null">
        usage_start_time = #{record.usageStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.usageEndTime != null">
        usage_end_time = #{record.usageEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rcts_drug_distribute_manage
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      drug_id = #{record.drugId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      acronym = #{record.acronym,jdbcType=VARCHAR},
      distribute_count = #{record.distributeCount,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      distribute_time = #{record.distributeTime,jdbcType=TIMESTAMP},
      usage_start_time = #{record.usageStartTime,jdbcType=TIMESTAMP},
      usage_end_time = #{record.usageEndTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.RctsDrugDistributeManage">
    update rcts_drug_distribute_manage
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="drugId != null">
        drug_id = #{drugId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeCode != null">
        testee_code = #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="acronym != null">
        acronym = #{acronym,jdbcType=VARCHAR},
      </if>
      <if test="distributeCount != null">
        distribute_count = #{distributeCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="distributeTime != null">
        distribute_time = #{distributeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usageStartTime != null">
        usage_start_time = #{usageStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usageEndTime != null">
        usage_end_time = #{usageEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.RctsDrugDistributeManage">
    update rcts_drug_distribute_manage
    set project_id = #{projectId,jdbcType=BIGINT},
      drug_id = #{drugId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      testee_code = #{testeeCode,jdbcType=VARCHAR},
      acronym = #{acronym,jdbcType=VARCHAR},
      distribute_count = #{distributeCount,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      distribute_time = #{distributeTime,jdbcType=TIMESTAMP},
      usage_start_time = #{usageStartTime,jdbcType=TIMESTAMP},
      usage_end_time = #{usageEndTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getDrugDistributePage" resultType="com.haoys.user.domain.vo.rcts.DrugDistributeVo">
    SELECT
    dm.id, dm.project_id, dm.drug_id, dm.testee_id, dm.testee_code,
    dm.acronym, dm.distribute_count, dm.create_time distributeTime,
    dr.material_code, dr.product_name, dr.merchandise_name, dr.manufacturer, dr.country,
           dm.usage_start_time, dm.usage_end_time, dr.min_package_unit minPackageUnit,
           dr.material_specification materialSpecification, dr.storage_requirement storageRequirement
    FROM rcts_drug_distribute_manage dm LEFT JOIN rcts_drug_register_manage dr ON dm.drug_id = dr.id
    WHERE dm.status = '0'
    <if test="projectId != null">
      and project_id = #{projectId}
    </if>
    <if test="testeeCode != null">
      AND dm.testee_code = #{testeeCode}
    </if>
    <if test="acronym != null">
      and acronym like concat('%',#{acronym},'%')
    </if>
    <if test="queryMaterial != null">
      and concat(dr.material_code, dr.product_name, dr.merchandise_name) like concat('%',#{queryMaterial},'%')
    </if>

  </select>


</mapper>