<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormLogicDataMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormLogicData">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="logic_id" jdbcType="BIGINT" property="logicId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="target_visit_id" jdbcType="BIGINT" property="targetVisitId" />
    <result column="target_form_id" jdbcType="BIGINT" property="targetFormId" />
    <result column="target_detail_id" jdbcType="BIGINT" property="targetDetailId" />
    <result column="condition_value" jdbcType="VARCHAR" property="conditionValue" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="extands" jdbcType="VARCHAR" property="extands" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, logic_id, project_id, template_id, target_visit_id, target_form_id, target_detail_id, 
    condition_value, status, extands, platform_id, create_time, update_time, create_user_id, 
    update_user_id, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormLogicDataExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_logic_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_form_logic_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_form_logic_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormLogicDataExample">
    delete from template_form_logic_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormLogicData">
    insert into template_form_logic_data (id, logic_id, project_id, 
      template_id, target_visit_id, target_form_id, 
      target_detail_id, condition_value, status, 
      extands, platform_id, create_time, 
      update_time, create_user_id, update_user_id, 
      tenant_id)
    values (#{id,jdbcType=BIGINT}, #{logicId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{templateId,jdbcType=BIGINT}, #{targetVisitId,jdbcType=BIGINT}, #{targetFormId,jdbcType=BIGINT}, 
      #{targetDetailId,jdbcType=BIGINT}, #{conditionValue,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{extands,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormLogicData">
    insert into template_form_logic_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="logicId != null">
        logic_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="targetVisitId != null">
        target_visit_id,
      </if>
      <if test="targetFormId != null">
        target_form_id,
      </if>
      <if test="targetDetailId != null">
        target_detail_id,
      </if>
      <if test="conditionValue != null">
        condition_value,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extands != null">
        extands,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="logicId != null">
        #{logicId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="targetVisitId != null">
        #{targetVisitId,jdbcType=BIGINT},
      </if>
      <if test="targetFormId != null">
        #{targetFormId,jdbcType=BIGINT},
      </if>
      <if test="targetDetailId != null">
        #{targetDetailId,jdbcType=BIGINT},
      </if>
      <if test="conditionValue != null">
        #{conditionValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="extands != null">
        #{extands,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormLogicDataExample" resultType="java.lang.Long">
    select count(*) from template_form_logic_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_logic_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.logicId != null">
        logic_id = #{record.logicId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.targetVisitId != null">
        target_visit_id = #{record.targetVisitId,jdbcType=BIGINT},
      </if>
      <if test="record.targetFormId != null">
        target_form_id = #{record.targetFormId,jdbcType=BIGINT},
      </if>
      <if test="record.targetDetailId != null">
        target_detail_id = #{record.targetDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.conditionValue != null">
        condition_value = #{record.conditionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.extands != null">
        extands = #{record.extands,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_logic_data
    set id = #{record.id,jdbcType=BIGINT},
      logic_id = #{record.logicId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      target_visit_id = #{record.targetVisitId,jdbcType=BIGINT},
      target_form_id = #{record.targetFormId,jdbcType=BIGINT},
      target_detail_id = #{record.targetDetailId,jdbcType=BIGINT},
      condition_value = #{record.conditionValue,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      extands = #{record.extands,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormLogicData">
    update template_form_logic_data
    <set>
      <if test="logicId != null">
        logic_id = #{logicId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="targetVisitId != null">
        target_visit_id = #{targetVisitId,jdbcType=BIGINT},
      </if>
      <if test="targetFormId != null">
        target_form_id = #{targetFormId,jdbcType=BIGINT},
      </if>
      <if test="targetDetailId != null">
        target_detail_id = #{targetDetailId,jdbcType=BIGINT},
      </if>
      <if test="conditionValue != null">
        condition_value = #{conditionValue,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="extands != null">
        extands = #{extands,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormLogicData">
    update template_form_logic_data
    set logic_id = #{logicId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      template_id = #{templateId,jdbcType=BIGINT},
      target_visit_id = #{targetVisitId,jdbcType=BIGINT},
      target_form_id = #{targetFormId,jdbcType=BIGINT},
      target_detail_id = #{targetDetailId,jdbcType=BIGINT},
      condition_value = #{conditionValue,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      extands = #{extands,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>