<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTasteStatMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTasteStat">
    <result column="index" jdbcType="BIGINT" property="index" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.haoys.user.model.ProjectTasteStat">
    <result column="data_json" jdbcType="LONGVARCHAR" property="dataJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    `index`, batch_code
  </sql>
  <sql id="Blob_Column_List">
    data_json
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.haoys.user.model.ProjectTasteStatExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_taste_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTasteStatExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_taste_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTasteStatExample">
    delete from project_taste_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTasteStat">
    insert into project_taste_stat (index, batch_code, data_json
      )
    values (#{index,jdbcType=BIGINT}, #{batchCode,jdbcType=VARCHAR}, #{dataJson,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTasteStat">
    insert into project_taste_stat
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="index != null">
        index,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="dataJson != null">
        data_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="index != null">
        #{index,jdbcType=BIGINT},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="dataJson != null">
        #{dataJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTasteStatExample" resultType="java.lang.Long">
    select count(*) from project_taste_stat
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_taste_stat
    <set>
      <if test="record.index != null">
        index = #{record.index,jdbcType=BIGINT},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dataJson != null">
        data_json = #{record.dataJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update project_taste_stat
    set index = #{record.index,jdbcType=BIGINT},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      data_json = #{record.dataJson,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_taste_stat
    set index = #{record.index,jdbcType=BIGINT},
      batch_code = #{record.batchCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
</mapper>