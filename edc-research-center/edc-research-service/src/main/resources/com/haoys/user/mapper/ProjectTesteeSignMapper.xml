<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeSignMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeSign">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="contact_information" jdbcType="VARCHAR" property="contactInformation" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="testee_code" jdbcType="VARCHAR" property="testeeCode" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.haoys.user.model.ProjectTesteeSign">
    <result column="file_urls" jdbcType="LONGVARCHAR" property="fileUrls" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sign_type, contact_information, sign_time, testee_code, project_id, testee_id,
    status, create_time, update_time, create_user, update_user, tenant_id, platform_id
  </sql>
  <sql id="Blob_Column_List">
    file_urls
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.haoys.user.model.ProjectTesteeSignExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_testee_sign
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeSignExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_sign
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_testee_sign
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_sign
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeSignExample">
    delete from project_testee_sign
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeSign">
    insert into project_testee_sign (id, sign_type, contact_information,
      sign_time, testee_code, project_id,
      testee_id, status, create_time,
      update_time, create_user, update_user,
      tenant_id, platform_id, file_urls
      )
    values (#{id,jdbcType=BIGINT}, #{signType,jdbcType=INTEGER}, #{contactInformation,jdbcType=VARCHAR},
      #{signTime,jdbcType=TIMESTAMP}, #{testeeCode,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT},
      #{testeeId,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR},
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}, #{fileUrls,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeSign">
    insert into project_testee_sign
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="signType != null">
        sign_type,
      </if>
      <if test="contactInformation != null">
        contact_information,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="testeeCode != null">
        testee_code,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="fileUrls != null">
        file_urls,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="signType != null">
        #{signType,jdbcType=INTEGER},
      </if>
      <if test="contactInformation != null">
        #{contactInformation,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testeeCode != null">
        #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrls != null">
        #{fileUrls,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeSignExample" resultType="java.lang.Long">
    select count(*) from project_testee_sign
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

    <update id="updateByExampleSelective" parameterType="map">
    update project_testee_sign
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.signType != null">
        sign_type = #{record.signType,jdbcType=INTEGER},
      </if>
      <if test="record.contactInformation != null">
        contact_information = #{record.contactInformation,jdbcType=VARCHAR},
      </if>
      <if test="record.signTime != null">
        sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.testeeCode != null">
        testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrls != null">
        file_urls = #{record.fileUrls,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update project_testee_sign
    set id = #{record.id,jdbcType=BIGINT},
      sign_type = #{record.signType,jdbcType=INTEGER},
      contact_information = #{record.contactInformation,jdbcType=VARCHAR},
      sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      file_urls = #{record.fileUrls,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_sign
    set id = #{record.id,jdbcType=BIGINT},
      sign_type = #{record.signType,jdbcType=INTEGER},
      contact_information = #{record.contactInformation,jdbcType=VARCHAR},
      sign_time = #{record.signTime,jdbcType=TIMESTAMP},
      testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeSign">
    update project_testee_sign
    <set>
      <if test="signType != null">
        sign_type = #{signType,jdbcType=INTEGER},
      </if>
      <if test="contactInformation != null">
        contact_information = #{contactInformation,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testeeCode != null">
        testee_code = #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrls != null">
        file_urls = #{fileUrls,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.haoys.user.model.ProjectTesteeSign">
    update project_testee_sign
    set sign_type = #{signType,jdbcType=INTEGER},
      contact_information = #{contactInformation,jdbcType=VARCHAR},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      testee_code = #{testeeCode,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      file_urls = #{fileUrls,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeSign">
    update project_testee_sign
    set sign_type = #{signType,jdbcType=INTEGER},
      contact_information = #{contactInformation,jdbcType=VARCHAR},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      testee_code = #{testeeCode,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getMaxTesteeCode" resultType="java.lang.String">
    select max(testee_code) from project_testee_sign where project_id = #{projectId,jdbcType=BIGINT}
  </select>

  <select id="getProjectTesteeSign" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />,<include refid="Blob_Column_List" />
     from project_testee_sign where project_id = #{projectId,jdbcType=BIGINT}
    and testee_code = #{testeeCode,jdbcType=VARCHAR}
    and status = '0' order by create_time desc limit 1
  </select>

</mapper>
