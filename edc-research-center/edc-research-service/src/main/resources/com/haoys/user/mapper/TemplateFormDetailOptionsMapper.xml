<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormDetailOptionsMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormDetailOptions">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="option_value" jdbcType="VARCHAR" property="optionValue" />
    <result column="score_value" jdbcType="DECIMAL" property="scoreValue" />
    <result column="lang_value" jdbcType="VARCHAR" property="langValue" />
    <result column="unit_value" jdbcType="VARCHAR" property="unitValue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="dic_type" jdbcType="VARCHAR" property="dicType" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, parent_id, code, name, option_value, score_value, lang_value, unit_value, 
    description, dic_type, sort, status, create_user_id, create_time, update_user_id, 
    update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormDetailOptionsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_detail_options
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_form_detail_options
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from template_form_detail_options
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormDetailOptionsExample">
    delete from template_form_detail_options
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormDetailOptions">
    insert into template_form_detail_options (id, project_id, parent_id, 
      code, name, option_value, 
      score_value, lang_value, unit_value, 
      description, dic_type, sort, 
      status, create_user_id, create_time, 
      update_user_id, update_time, tenant_id, 
      platform_id)
    values (#{id,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT}, #{parentId,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{optionValue,jdbcType=VARCHAR}, 
      #{scoreValue,jdbcType=DECIMAL}, #{langValue,jdbcType=VARCHAR}, #{unitValue,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{dicType,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{status,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormDetailOptions">
    insert into template_form_detail_options
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="optionValue != null">
        option_value,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="langValue != null">
        lang_value,
      </if>
      <if test="unitValue != null">
        unit_value,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="dicType != null">
        dic_type,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="optionValue != null">
        #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="langValue != null">
        #{langValue,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="dicType != null">
        #{dicType,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormDetailOptionsExample" resultType="java.lang.Long">
    select count(*) from template_form_detail_options
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_detail_options
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.optionValue != null">
        option_value = #{record.optionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.scoreValue != null">
        score_value = #{record.scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="record.langValue != null">
        lang_value = #{record.langValue,jdbcType=VARCHAR},
      </if>
      <if test="record.unitValue != null">
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.dicType != null">
        dic_type = #{record.dicType,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_detail_options
    set id = #{record.id,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      option_value = #{record.optionValue,jdbcType=VARCHAR},
      score_value = #{record.scoreValue,jdbcType=DECIMAL},
      lang_value = #{record.langValue,jdbcType=VARCHAR},
      unit_value = #{record.unitValue,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      dic_type = #{record.dicType,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormDetailOptions">
    update template_form_detail_options
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="optionValue != null">
        option_value = #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="langValue != null">
        lang_value = #{langValue,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        unit_value = #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="dicType != null">
        dic_type = #{dicType,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormDetailOptions">
    update template_form_detail_options
    set project_id = #{projectId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      option_value = #{optionValue,jdbcType=VARCHAR},
      score_value = #{scoreValue,jdbcType=DECIMAL},
      lang_value = #{langValue,jdbcType=VARCHAR},
      unit_value = #{unitValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      dic_type = #{dicType,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>


  <!--表单字段下拉框分页列表-->
  <select id="getTemplateFormGroupFieldOptionListForPage" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormGroupFieldOptionVo">
    SELECT
    id, parent_id parentId, name, score_value scoreValue, unit_value unitValue, sort, status,
    create_user_id createUserId, create_time createTime,
    update_user_id updateUserId, update_time updateTime
    FROM template_form_detail_options WHERE parent_id = '0'
    <if test="parentId != null and parentId != ''">
      AND parent_id = #{parentId}
    </if>
    <if test="name != null and name != ''">
      AND name like concat('%', #{name}, '%')
    </if>
    AND status = 0
  </select>

  <update id="deleteFormGroupFieldByParentId">
    update template_form_detail_options set status = '1' where parent_id = #{parentId}
  </update>

  <select id="getFormGroupFieldOptionByParentId" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormGroupFieldOptionVo">

    SELECT
      id, parent_id parentId, name, score_value scoreValue, unit_value unitValue, sort, status,
      create_user_id createUserId, create_time createTime,
      update_user_id updateUserId, update_time updateTime
    FROM template_form_detail_options WHERE parent_id = #{parentId} and status = '0'

  </select>
</mapper>