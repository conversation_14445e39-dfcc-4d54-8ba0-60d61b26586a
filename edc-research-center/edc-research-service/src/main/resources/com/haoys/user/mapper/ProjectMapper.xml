<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.Project">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="project_type" jdbcType="VARCHAR" property="projectType" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="if_public" jdbcType="BIT" property="ifPublic" />
    <result column="if_publish_org" jdbcType="BIT" property="ifPublishOrg" />
    <result column="project_nature" jdbcType="VARCHAR" property="projectNature" />
    <result column="research_area" jdbcType="VARCHAR" property="researchArea" />
    <result column="group_unit" jdbcType="VARCHAR" property="groupUnit" />
    <result column="gu_researcher" jdbcType="VARCHAR" property="guResearcher" />
    <result column="gu_contact" jdbcType="VARCHAR" property="guContact" />
    <result column="nitiator" jdbcType="VARCHAR" property="nitiator" />
    <result column="sponsor" jdbcType="VARCHAR" property="sponsor" />
    <result column="nitiator_contact" jdbcType="VARCHAR" property="nitiatorContact" />
    <result column="involved_org_num" jdbcType="INTEGER" property="involvedOrgNum" />
    <result column="registe_number" jdbcType="VARCHAR" property="registeNumber" />
    <result column="plan_start_date" jdbcType="DATE" property="planStartDate" />
    <result column="plan_group_num" jdbcType="INTEGER" property="planGroupNum" />
    <result column="record_number" jdbcType="VARCHAR" property="recordNumber" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="database_id" jdbcType="VARCHAR" property="databaseId" />
    <result column="database_from" jdbcType="VARCHAR" property="databaseFrom" />
    <result column="research_method" jdbcType="VARCHAR" property="researchMethod" />
    <result column="create_method" jdbcType="VARCHAR" property="createMethod" />
    <result column="enable_prescription_analysis" jdbcType="BIT" property="enablePrescriptionAnalysis" />
    <result column="enable_randomized_config" jdbcType="BIT" property="enableRandomizedConfig" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="project_switch" jdbcType="INTEGER" property="projectSwitch" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, code, project_type, start_date, end_date, status, if_public, if_publish_org, 
    project_nature, research_area, group_unit, gu_researcher, gu_contact, nitiator, sponsor, 
    nitiator_contact, involved_org_num, registe_number, plan_start_date, plan_group_num, 
    record_number, template_id, database_id, database_from, research_method, create_method, 
    enable_prescription_analysis, enable_randomized_config, total_count, description, 
    project_switch, create_user, create_time, update_user, update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectExample">
    delete from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.Project">
    insert into project (id, name, code, 
      project_type, start_date, end_date, 
      status, if_public, if_publish_org, 
      project_nature, research_area, group_unit, 
      gu_researcher, gu_contact, nitiator, 
      sponsor, nitiator_contact, involved_org_num, 
      registe_number, plan_start_date, plan_group_num, 
      record_number, template_id, database_id, 
      database_from, research_method, create_method, 
      enable_prescription_analysis, enable_randomized_config, 
      total_count, description, project_switch, 
      create_user, create_time, update_user, 
      update_time, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{projectType,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=VARCHAR}, #{ifPublic,jdbcType=BIT}, #{ifPublishOrg,jdbcType=BIT}, 
      #{projectNature,jdbcType=VARCHAR}, #{researchArea,jdbcType=VARCHAR}, #{groupUnit,jdbcType=VARCHAR}, 
      #{guResearcher,jdbcType=VARCHAR}, #{guContact,jdbcType=VARCHAR}, #{nitiator,jdbcType=VARCHAR}, 
      #{sponsor,jdbcType=VARCHAR}, #{nitiatorContact,jdbcType=VARCHAR}, #{involvedOrgNum,jdbcType=INTEGER}, 
      #{registeNumber,jdbcType=VARCHAR}, #{planStartDate,jdbcType=DATE}, #{planGroupNum,jdbcType=INTEGER}, 
      #{recordNumber,jdbcType=VARCHAR}, #{templateId,jdbcType=BIGINT}, #{databaseId,jdbcType=VARCHAR}, 
      #{databaseFrom,jdbcType=VARCHAR}, #{researchMethod,jdbcType=VARCHAR}, #{createMethod,jdbcType=VARCHAR}, 
      #{enablePrescriptionAnalysis,jdbcType=BIT}, #{enableRandomizedConfig,jdbcType=BIT}, 
      #{totalCount,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{projectSwitch,jdbcType=INTEGER}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.Project">
    insert into project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="projectType != null">
        project_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ifPublic != null">
        if_public,
      </if>
      <if test="ifPublishOrg != null">
        if_publish_org,
      </if>
      <if test="projectNature != null">
        project_nature,
      </if>
      <if test="researchArea != null">
        research_area,
      </if>
      <if test="groupUnit != null">
        group_unit,
      </if>
      <if test="guResearcher != null">
        gu_researcher,
      </if>
      <if test="guContact != null">
        gu_contact,
      </if>
      <if test="nitiator != null">
        nitiator,
      </if>
      <if test="sponsor != null">
        sponsor,
      </if>
      <if test="nitiatorContact != null">
        nitiator_contact,
      </if>
      <if test="involvedOrgNum != null">
        involved_org_num,
      </if>
      <if test="registeNumber != null">
        registe_number,
      </if>
      <if test="planStartDate != null">
        plan_start_date,
      </if>
      <if test="planGroupNum != null">
        plan_group_num,
      </if>
      <if test="recordNumber != null">
        record_number,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="databaseId != null">
        database_id,
      </if>
      <if test="databaseFrom != null">
        database_from,
      </if>
      <if test="researchMethod != null">
        research_method,
      </if>
      <if test="createMethod != null">
        create_method,
      </if>
      <if test="enablePrescriptionAnalysis != null">
        enable_prescription_analysis,
      </if>
      <if test="enableRandomizedConfig != null">
        enable_randomized_config,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="projectSwitch != null">
        project_switch,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        #{projectType,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="ifPublic != null">
        #{ifPublic,jdbcType=BIT},
      </if>
      <if test="ifPublishOrg != null">
        #{ifPublishOrg,jdbcType=BIT},
      </if>
      <if test="projectNature != null">
        #{projectNature,jdbcType=VARCHAR},
      </if>
      <if test="researchArea != null">
        #{researchArea,jdbcType=VARCHAR},
      </if>
      <if test="groupUnit != null">
        #{groupUnit,jdbcType=VARCHAR},
      </if>
      <if test="guResearcher != null">
        #{guResearcher,jdbcType=VARCHAR},
      </if>
      <if test="guContact != null">
        #{guContact,jdbcType=VARCHAR},
      </if>
      <if test="nitiator != null">
        #{nitiator,jdbcType=VARCHAR},
      </if>
      <if test="sponsor != null">
        #{sponsor,jdbcType=VARCHAR},
      </if>
      <if test="nitiatorContact != null">
        #{nitiatorContact,jdbcType=VARCHAR},
      </if>
      <if test="involvedOrgNum != null">
        #{involvedOrgNum,jdbcType=INTEGER},
      </if>
      <if test="registeNumber != null">
        #{registeNumber,jdbcType=VARCHAR},
      </if>
      <if test="planStartDate != null">
        #{planStartDate,jdbcType=DATE},
      </if>
      <if test="planGroupNum != null">
        #{planGroupNum,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="databaseId != null">
        #{databaseId,jdbcType=VARCHAR},
      </if>
      <if test="databaseFrom != null">
        #{databaseFrom,jdbcType=VARCHAR},
      </if>
      <if test="researchMethod != null">
        #{researchMethod,jdbcType=VARCHAR},
      </if>
      <if test="createMethod != null">
        #{createMethod,jdbcType=VARCHAR},
      </if>
      <if test="enablePrescriptionAnalysis != null">
        #{enablePrescriptionAnalysis,jdbcType=BIT},
      </if>
      <if test="enableRandomizedConfig != null">
        #{enableRandomizedConfig,jdbcType=BIT},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="projectSwitch != null">
        #{projectSwitch,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectExample" resultType="java.lang.Long">
    select count(*) from project
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.projectType != null">
        project_type = #{record.projectType,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.ifPublic != null">
        if_public = #{record.ifPublic,jdbcType=BIT},
      </if>
      <if test="record.ifPublishOrg != null">
        if_publish_org = #{record.ifPublishOrg,jdbcType=BIT},
      </if>
      <if test="record.projectNature != null">
        project_nature = #{record.projectNature,jdbcType=VARCHAR},
      </if>
      <if test="record.researchArea != null">
        research_area = #{record.researchArea,jdbcType=VARCHAR},
      </if>
      <if test="record.groupUnit != null">
        group_unit = #{record.groupUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.guResearcher != null">
        gu_researcher = #{record.guResearcher,jdbcType=VARCHAR},
      </if>
      <if test="record.guContact != null">
        gu_contact = #{record.guContact,jdbcType=VARCHAR},
      </if>
      <if test="record.nitiator != null">
        nitiator = #{record.nitiator,jdbcType=VARCHAR},
      </if>
      <if test="record.sponsor != null">
        sponsor = #{record.sponsor,jdbcType=VARCHAR},
      </if>
      <if test="record.nitiatorContact != null">
        nitiator_contact = #{record.nitiatorContact,jdbcType=VARCHAR},
      </if>
      <if test="record.involvedOrgNum != null">
        involved_org_num = #{record.involvedOrgNum,jdbcType=INTEGER},
      </if>
      <if test="record.registeNumber != null">
        registe_number = #{record.registeNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.planStartDate != null">
        plan_start_date = #{record.planStartDate,jdbcType=DATE},
      </if>
      <if test="record.planGroupNum != null">
        plan_group_num = #{record.planGroupNum,jdbcType=INTEGER},
      </if>
      <if test="record.recordNumber != null">
        record_number = #{record.recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.databaseId != null">
        database_id = #{record.databaseId,jdbcType=VARCHAR},
      </if>
      <if test="record.databaseFrom != null">
        database_from = #{record.databaseFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.researchMethod != null">
        research_method = #{record.researchMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.createMethod != null">
        create_method = #{record.createMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.enablePrescriptionAnalysis != null">
        enable_prescription_analysis = #{record.enablePrescriptionAnalysis,jdbcType=BIT},
      </if>
      <if test="record.enableRandomizedConfig != null">
        enable_randomized_config = #{record.enableRandomizedConfig,jdbcType=BIT},
      </if>
      <if test="record.totalCount != null">
        total_count = #{record.totalCount,jdbcType=INTEGER},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.projectSwitch != null">
        project_switch = #{record.projectSwitch,jdbcType=INTEGER},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      project_type = #{record.projectType,jdbcType=VARCHAR},
      start_date = #{record.startDate,jdbcType=TIMESTAMP},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=VARCHAR},
      if_public = #{record.ifPublic,jdbcType=BIT},
      if_publish_org = #{record.ifPublishOrg,jdbcType=BIT},
      project_nature = #{record.projectNature,jdbcType=VARCHAR},
      research_area = #{record.researchArea,jdbcType=VARCHAR},
      group_unit = #{record.groupUnit,jdbcType=VARCHAR},
      gu_researcher = #{record.guResearcher,jdbcType=VARCHAR},
      gu_contact = #{record.guContact,jdbcType=VARCHAR},
      nitiator = #{record.nitiator,jdbcType=VARCHAR},
      sponsor = #{record.sponsor,jdbcType=VARCHAR},
      nitiator_contact = #{record.nitiatorContact,jdbcType=VARCHAR},
      involved_org_num = #{record.involvedOrgNum,jdbcType=INTEGER},
      registe_number = #{record.registeNumber,jdbcType=VARCHAR},
      plan_start_date = #{record.planStartDate,jdbcType=DATE},
      plan_group_num = #{record.planGroupNum,jdbcType=INTEGER},
      record_number = #{record.recordNumber,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=BIGINT},
      database_id = #{record.databaseId,jdbcType=VARCHAR},
      database_from = #{record.databaseFrom,jdbcType=VARCHAR},
      research_method = #{record.researchMethod,jdbcType=VARCHAR},
      create_method = #{record.createMethod,jdbcType=VARCHAR},
      enable_prescription_analysis = #{record.enablePrescriptionAnalysis,jdbcType=BIT},
      enable_randomized_config = #{record.enableRandomizedConfig,jdbcType=BIT},
      total_count = #{record.totalCount,jdbcType=INTEGER},
      description = #{record.description,jdbcType=VARCHAR},
      project_switch = #{record.projectSwitch,jdbcType=INTEGER},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.Project">
    update project
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        project_type = #{projectType,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="ifPublic != null">
        if_public = #{ifPublic,jdbcType=BIT},
      </if>
      <if test="ifPublishOrg != null">
        if_publish_org = #{ifPublishOrg,jdbcType=BIT},
      </if>
      <if test="projectNature != null">
        project_nature = #{projectNature,jdbcType=VARCHAR},
      </if>
      <if test="researchArea != null">
        research_area = #{researchArea,jdbcType=VARCHAR},
      </if>
      <if test="groupUnit != null">
        group_unit = #{groupUnit,jdbcType=VARCHAR},
      </if>
      <if test="guResearcher != null">
        gu_researcher = #{guResearcher,jdbcType=VARCHAR},
      </if>
      <if test="guContact != null">
        gu_contact = #{guContact,jdbcType=VARCHAR},
      </if>
      <if test="nitiator != null">
        nitiator = #{nitiator,jdbcType=VARCHAR},
      </if>
      <if test="sponsor != null">
        sponsor = #{sponsor,jdbcType=VARCHAR},
      </if>
      <if test="nitiatorContact != null">
        nitiator_contact = #{nitiatorContact,jdbcType=VARCHAR},
      </if>
      <if test="involvedOrgNum != null">
        involved_org_num = #{involvedOrgNum,jdbcType=INTEGER},
      </if>
      <if test="registeNumber != null">
        registe_number = #{registeNumber,jdbcType=VARCHAR},
      </if>
      <if test="planStartDate != null">
        plan_start_date = #{planStartDate,jdbcType=DATE},
      </if>
      <if test="planGroupNum != null">
        plan_group_num = #{planGroupNum,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        record_number = #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="databaseId != null">
        database_id = #{databaseId,jdbcType=VARCHAR},
      </if>
      <if test="databaseFrom != null">
        database_from = #{databaseFrom,jdbcType=VARCHAR},
      </if>
      <if test="researchMethod != null">
        research_method = #{researchMethod,jdbcType=VARCHAR},
      </if>
      <if test="createMethod != null">
        create_method = #{createMethod,jdbcType=VARCHAR},
      </if>
      <if test="enablePrescriptionAnalysis != null">
        enable_prescription_analysis = #{enablePrescriptionAnalysis,jdbcType=BIT},
      </if>
      <if test="enableRandomizedConfig != null">
        enable_randomized_config = #{enableRandomizedConfig,jdbcType=BIT},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="projectSwitch != null">
        project_switch = #{projectSwitch,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.Project">
    update project
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      project_type = #{projectType,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR},
      if_public = #{ifPublic,jdbcType=BIT},
      if_publish_org = #{ifPublishOrg,jdbcType=BIT},
      project_nature = #{projectNature,jdbcType=VARCHAR},
      research_area = #{researchArea,jdbcType=VARCHAR},
      group_unit = #{groupUnit,jdbcType=VARCHAR},
      gu_researcher = #{guResearcher,jdbcType=VARCHAR},
      gu_contact = #{guContact,jdbcType=VARCHAR},
      nitiator = #{nitiator,jdbcType=VARCHAR},
      sponsor = #{sponsor,jdbcType=VARCHAR},
      nitiator_contact = #{nitiatorContact,jdbcType=VARCHAR},
      involved_org_num = #{involvedOrgNum,jdbcType=INTEGER},
      registe_number = #{registeNumber,jdbcType=VARCHAR},
      plan_start_date = #{planStartDate,jdbcType=DATE},
      plan_group_num = #{planGroupNum,jdbcType=INTEGER},
      record_number = #{recordNumber,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=BIGINT},
      database_id = #{databaseId,jdbcType=VARCHAR},
      database_from = #{databaseFrom,jdbcType=VARCHAR},
      research_method = #{researchMethod,jdbcType=VARCHAR},
      create_method = #{createMethod,jdbcType=VARCHAR},
      enable_prescription_analysis = #{enablePrescriptionAnalysis,jdbcType=BIT},
      enable_randomized_config = #{enableRandomizedConfig,jdbcType=BIT},
      total_count = #{totalCount,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      project_switch = #{projectSwitch,jdbcType=INTEGER},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!--查询工作台项目广场列表-->
  <select id="getPublicProjectListForPage" resultType="com.haoys.user.domain.vo.project.ProjectVo">
    SELECT
      project.id,
      project.name,
      project.code,
      project.group_unit groupUnit,
      project.description,
      project.if_public ifPublic,
      project.nitiator,
      project.sponsor,
      project.`research_area` researchArea,
      project.`research_method`,
      project.enable_prescription_analysis enablePrescriptionAnalysis,
      project_apply_user.status,
      project_apply_user.check_status checkStatus,
      project_apply_user.user_id applyUserId,
      project_apply_user.org_id applyOrgId,
      project.create_user createUser,
      project.create_time createTime,
      project.project_nature projectNature,
      project.registe_number registeNumber,
      project.record_number recordNumber,
      system_org_infoG.org_name groupUnitName,
      system_org_infoN.org_name nitiatorName
    FROM
        project
    LEFT JOIN project_apply_user ON project_apply_user.project_id = project.id AND project_apply_user.user_id = #{userId}
    LEFT JOIN system_org_info system_org_infoG ON project.group_unit = system_org_infoG.org_id
    LEFT JOIN system_org_info system_org_infoN ON project.nitiator = system_org_infoN.org_id
    WHERE
    1 = 1 AND project.if_public = TRUE and project.status = '0'
    AND project.tenant_id = #{tenantId} AND project.platform_id = #{platformId}
    <if test="projectName != null">
      AND (project.NAME LIKE CONCAT('%',#{projectName},'%') or project.code LIKE CONCAT('%',#{projectName},'%'))
    </if>
    <if test="databaseId != null and databaseId != ''">
      AND project.database_id = #{databaseId}
    </if>
    ORDER BY project.create_time DESC
  </select>

  <!--  查询我的项目列表-->
  <select id="getOwnerProjectListForPage" resultType="com.haoys.user.domain.vo.project.ProjectVo">
    SELECT
      DISTINCT project.id,
      project.NAME,
      project.code,
      project.group_unit groupUnit,
      project.description,
      project.if_public ifPublic,
      project.nitiator,
      project.sponsor,
      project.`research_area` researchArea,
      project.`research_method`,
      project.enable_prescription_analysis enablePrescriptionAnalysis,
      /*project_apply_user.status,
      project_apply_user.check_status checkStatus,*/
      project.create_user createUser,
      project.project_nature projectNature,
      project.registe_number registeNumber,
      project.record_number recordNumber,
      project.create_time createTime,
      system_org_infoG.org_name groupUnitName,
      system_org_infoN.org_name nitiatorName
    FROM
        project
    INNER JOIN project_user_info ON project_user_info.project_id = project.id
     AND project_user_info.active_status = true
     AND (project_user_info.user_id = #{userId} OR project.create_user = #{userId})
    LEFT JOIN system_org_info system_org_infoG ON project.group_unit = system_org_infoG.org_id
    LEFT JOIN system_org_info system_org_infoN ON project.nitiator = system_org_infoN.org_id
    /*LEFT JOIN project_apply_user ON project_apply_user.project_id = project.id*/
    WHERE 1 = 1 AND project.status = '0' AND project.if_public = true
    AND project.tenant_id = #{tenantId} AND project.platform_id = #{platformId}
    <if test="projectName != null">
      AND (project.NAME LIKE CONCAT('%',#{projectName},'%') or project.code LIKE CONCAT('%',#{projectName},'%'))
    </if>
    <!-- 显示自己所在中心项目-->
    <!--<if test="orgId != null">
      AND project_apply_user.org_id = #{orgId}
    </if>-->
    <if test="databaseId != null and databaseId != ''">
      AND project.database_id = #{databaseId}
    </if>
    <if test="enableProjectRandomizedConfig == true">
      AND project.enable_randomized_config = #{enableProjectRandomizedConfig}
    </if>
    ORDER BY project.create_time DESC
  </select>

  <!--后台项目管理列表-->
  <select id="getProjectManageListForPage" resultType="com.haoys.user.domain.vo.project.ProjectVo">
    select
      p.id, p.name, p.code,p.research_method,
      p.group_unit groupUnit,p.start_date startDate, p.end_date endDate, p.status,
      p.if_public ifpublic,p.project_switch projectSwitch,
      p.if_publish_org,p.enable_prescription_analysis enablePrescriptionAnalysis,
      p.research_area researchArea,p.nitiator, p.sponsor, p.template_id, p.description,
      p.create_user, p.create_time, p.update_user,p.project_nature projectNature,
      p.registe_number registeNumber, p.record_number recordNumber,
      p.update_time, p.platform_id,soi.org_name groupUnitName,
      d.name researchAreaValue from project p
    left join dictionary d on p.research_area = d.id
    left join system_org_info soi on p.group_unit = soi.org_id

    where 1 = 1 and p.status = '0' AND p.tenant_id = #{tenantId} AND p.platform_id = #{platformId}
    <if test="userId != null and userId != ''">
      and p.create_user LIKE #{userId}
    </if>
    <if test="name != null">
      and (p.name LIKE CONCAT('%',#{name},'%') or p.code LIKE CONCAT('%',#{name},'%'))
    </if>
    <if test="ifPublic != null">
      and p.if_public = #{ifPublic}
    </if>
    <if test="researchArea != null">
      and p.research_area = #{researchArea}
    </if>
    order by p.create_time desc
  </select>

  <select id="getProjectBaseInfoByName" resultMap="BaseResultMap">
    select * from project where name = #{name} and status = '0' and tenant_id = #{systemTenantId} and platform_id = #{systemPlatformId}
  </select>

  <select id="getProjectBaseInfoByCode" resultMap="BaseResultMap">
    select * from project where code = #{code} and status = '0' and tenant_id = #{systemTenantId} and platform_id = #{systemPlatformId}
  </select>

  <select id="getOwnerProjectListForH5" resultType="com.haoys.user.domain.vo.project.ProjectVo">
    SELECT
      DISTINCT project.id,
      project.NAME,
      project.code,
      project.group_unit groupUnit,
      project.description,
      project.if_public ifPublic,
      project.nitiator,
      project.sponsor,
      project.`research_area` researchArea,
      project.enable_prescription_analysis enablePrescriptionAnalysis,
      project.create_user createUser,
      project.project_nature projectNature,
      project.registe_number registeNumber,
      project.record_number recordNumber,
      project.create_time createTime,
      project_visit_user.owner_org_id ownerOrgId
    FROM
      project
    INNER JOIN project_visit_user on project.id = project_visit_user.project_id
    INNER JOIN project_testee_info on project_testee_info.id = project_visit_user.testee_id
    WHERE project_testee_info.user_id = #{userId} and project_visit_user.status = '0'
    ORDER BY project.create_time DESC
  </select>

  <select id="getProjectList" resultType="com.haoys.user.domain.vo.project.ProjectVo">
    select * from project where status = '0'
    <if test="projectName != null and projectName != ''">
      and if_public = #{projectName}
    </if>
    <if test="ifPublic != null">
      and if_public = #{ifPublic}
    </if>
    <if test="researchArea != null and researchArea != ''">
      and research_area = #{researchArea}
    </if>
    and tenant_id = #{tenantId} and platform_id = #{platformId}
  </select>

  <select id="getEnableProjectBaseInfo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from project
    where status = '0'  and if_public = true and tenant_id = #{loginTenantId} and platform_id = #{loginPlatformId}
    order by create_time desc limit 1
  </select>
</mapper>