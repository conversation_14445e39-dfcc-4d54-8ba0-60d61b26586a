<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectPharmacopeiaMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectPharmacopeia">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="alias" jdbcType="VARCHAR" property="alias" />
    <result column="nature_flav" jdbcType="VARCHAR" property="natureFlav" />
    <result column="return_essence" jdbcType="VARCHAR" property="returnEssence" />
    <result column="efficacy" jdbcType="VARCHAR" property="efficacy" />
    <result column="indication" jdbcType="VARCHAR" property="indication" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="attention" jdbcType="VARCHAR" property="attention" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="medicinal_property" jdbcType="VARCHAR" property="medicinalProperty" />
    <result column="medicinal_taste" jdbcType="VARCHAR" property="medicinalTaste" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, alias, nature_flav, return_essence, efficacy, indication, content, attention, 
    status, medicinal_property, medicinal_taste, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectPharmacopeiaExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_pharmacopeia
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_pharmacopeia
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_pharmacopeia
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectPharmacopeiaExample">
    delete from project_pharmacopeia
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectPharmacopeia">
    insert into project_pharmacopeia (id, name, alias, 
      nature_flav, return_essence, efficacy, 
      indication, content, attention, 
      status, medicinal_property, medicinal_taste, 
      create_time)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{alias,jdbcType=VARCHAR}, 
      #{natureFlav,jdbcType=VARCHAR}, #{returnEssence,jdbcType=VARCHAR}, #{efficacy,jdbcType=VARCHAR}, 
      #{indication,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{attention,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{medicinalProperty,jdbcType=VARCHAR}, #{medicinalTaste,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectPharmacopeia">
    insert into project_pharmacopeia
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="alias != null">
        alias,
      </if>
      <if test="natureFlav != null">
        nature_flav,
      </if>
      <if test="returnEssence != null">
        return_essence,
      </if>
      <if test="efficacy != null">
        efficacy,
      </if>
      <if test="indication != null">
        indication,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="attention != null">
        attention,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="medicinalProperty != null">
        medicinal_property,
      </if>
      <if test="medicinalTaste != null">
        medicinal_taste,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null">
        #{alias,jdbcType=VARCHAR},
      </if>
      <if test="natureFlav != null">
        #{natureFlav,jdbcType=VARCHAR},
      </if>
      <if test="returnEssence != null">
        #{returnEssence,jdbcType=VARCHAR},
      </if>
      <if test="efficacy != null">
        #{efficacy,jdbcType=VARCHAR},
      </if>
      <if test="indication != null">
        #{indication,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="attention != null">
        #{attention,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="medicinalProperty != null">
        #{medicinalProperty,jdbcType=VARCHAR},
      </if>
      <if test="medicinalTaste != null">
        #{medicinalTaste,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectPharmacopeiaExample" resultType="java.lang.Long">
    select count(*) from project_pharmacopeia
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_pharmacopeia
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.alias != null">
        alias = #{record.alias,jdbcType=VARCHAR},
      </if>
      <if test="record.natureFlav != null">
        nature_flav = #{record.natureFlav,jdbcType=VARCHAR},
      </if>
      <if test="record.returnEssence != null">
        return_essence = #{record.returnEssence,jdbcType=VARCHAR},
      </if>
      <if test="record.efficacy != null">
        efficacy = #{record.efficacy,jdbcType=VARCHAR},
      </if>
      <if test="record.indication != null">
        indication = #{record.indication,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.attention != null">
        attention = #{record.attention,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.medicinalProperty != null">
        medicinal_property = #{record.medicinalProperty,jdbcType=VARCHAR},
      </if>
      <if test="record.medicinalTaste != null">
        medicinal_taste = #{record.medicinalTaste,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_pharmacopeia
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      alias = #{record.alias,jdbcType=VARCHAR},
      nature_flav = #{record.natureFlav,jdbcType=VARCHAR},
      return_essence = #{record.returnEssence,jdbcType=VARCHAR},
      efficacy = #{record.efficacy,jdbcType=VARCHAR},
      indication = #{record.indication,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      attention = #{record.attention,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      medicinal_property = #{record.medicinalProperty,jdbcType=VARCHAR},
      medicinal_taste = #{record.medicinalTaste,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectPharmacopeia">
    update project_pharmacopeia
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="alias != null">
        alias = #{alias,jdbcType=VARCHAR},
      </if>
      <if test="natureFlav != null">
        nature_flav = #{natureFlav,jdbcType=VARCHAR},
      </if>
      <if test="returnEssence != null">
        return_essence = #{returnEssence,jdbcType=VARCHAR},
      </if>
      <if test="efficacy != null">
        efficacy = #{efficacy,jdbcType=VARCHAR},
      </if>
      <if test="indication != null">
        indication = #{indication,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="attention != null">
        attention = #{attention,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="medicinalProperty != null">
        medicinal_property = #{medicinalProperty,jdbcType=VARCHAR},
      </if>
      <if test="medicinalTaste != null">
        medicinal_taste = #{medicinalTaste,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectPharmacopeia">
    update project_pharmacopeia
    set name = #{name,jdbcType=VARCHAR},
      alias = #{alias,jdbcType=VARCHAR},
      nature_flav = #{natureFlav,jdbcType=VARCHAR},
      return_essence = #{returnEssence,jdbcType=VARCHAR},
      efficacy = #{efficacy,jdbcType=VARCHAR},
      indication = #{indication,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      attention = #{attention,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      medicinal_property = #{medicinalProperty,jdbcType=VARCHAR},
      medicinal_taste = #{medicinalTaste,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>