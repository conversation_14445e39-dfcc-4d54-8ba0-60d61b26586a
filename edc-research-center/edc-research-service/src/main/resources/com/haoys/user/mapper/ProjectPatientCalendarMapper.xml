<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectPatientCalendarMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectPatientCalendar">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="config_info" jdbcType="VARCHAR" property="configInfo" />
    <result column="task_date" jdbcType="DATE" property="taskDate" />
    <result column="check_task_result" jdbcType="BIT" property="checkTaskResult" />
    <result column="complete_state" jdbcType="VARCHAR" property="completeState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, testee_id, config_info, task_date, check_task_result, complete_state, 
    create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectPatientCalendarExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_patient_calendar
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_patient_calendar
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_patient_calendar
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectPatientCalendarExample">
    delete from project_patient_calendar
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectPatientCalendar">
    insert into project_patient_calendar (id, project_id, plan_id, 
      testee_id, config_info, task_date, 
      check_task_result, complete_state, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{testeeId,jdbcType=BIGINT}, #{configInfo,jdbcType=VARCHAR}, #{taskDate,jdbcType=DATE}, 
      #{checkTaskResult,jdbcType=BIT}, #{completeState,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectPatientCalendar">
    insert into project_patient_calendar
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="configInfo != null">
        config_info,
      </if>
      <if test="taskDate != null">
        task_date,
      </if>
      <if test="checkTaskResult != null">
        check_task_result,
      </if>
      <if test="completeState != null">
        complete_state,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="configInfo != null">
        #{configInfo,jdbcType=VARCHAR},
      </if>
      <if test="taskDate != null">
        #{taskDate,jdbcType=DATE},
      </if>
      <if test="checkTaskResult != null">
        #{checkTaskResult,jdbcType=BIT},
      </if>
      <if test="completeState != null">
        #{completeState,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectPatientCalendarExample" resultType="java.lang.Long">
    select count(*) from project_patient_calendar
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_patient_calendar
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.configInfo != null">
        config_info = #{record.configInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDate != null">
        task_date = #{record.taskDate,jdbcType=DATE},
      </if>
      <if test="record.checkTaskResult != null">
        check_task_result = #{record.checkTaskResult,jdbcType=BIT},
      </if>
      <if test="record.completeState != null">
        complete_state = #{record.completeState,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_patient_calendar
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      config_info = #{record.configInfo,jdbcType=VARCHAR},
      task_date = #{record.taskDate,jdbcType=DATE},
      check_task_result = #{record.checkTaskResult,jdbcType=BIT},
      complete_state = #{record.completeState,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectPatientCalendar">
    update project_patient_calendar
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="configInfo != null">
        config_info = #{configInfo,jdbcType=VARCHAR},
      </if>
      <if test="taskDate != null">
        task_date = #{taskDate,jdbcType=DATE},
      </if>
      <if test="checkTaskResult != null">
        check_task_result = #{checkTaskResult,jdbcType=BIT},
      </if>
      <if test="completeState != null">
        complete_state = #{completeState,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectPatientCalendar">
    update project_patient_calendar
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      config_info = #{configInfo,jdbcType=VARCHAR},
      task_date = #{taskDate,jdbcType=DATE},
      check_task_result = #{checkTaskResult,jdbcType=BIT},
      complete_state = #{completeState,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>