<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeResultMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_expand_id" jdbcType="BIGINT" property="formExpandId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="resource_variable_id" jdbcType="BIGINT" property="resourceVariableId" />
    <result column="base_variable_id" jdbcType="BIGINT" property="baseVariableId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_value" jdbcType="VARCHAR" property="fieldValue" />
    <result column="field_text" jdbcType="VARCHAR" property="fieldText" />
    <result column="unit_value" jdbcType="VARCHAR" property="unitValue" />
    <result column="unit_text" jdbcType="VARCHAR" property="unitText" />
    <result column="score_value" jdbcType="DECIMAL" property="scoreValue" />
    <result column="option_hidden" jdbcType="BIT" property="optionHidden" />
    <result column="complate_status" jdbcType="VARCHAR" property="complateStatus" />
    <result column="testee_input" jdbcType="BIT" property="testeeInput" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, form_expand_id, form_detail_id, testee_id, 
    group_id, resource_variable_id, base_variable_id, label, field_name, field_value, 
    field_text, unit_value, unit_text, score_value, option_hidden, complate_status, testee_input, 
    data_from, status, sort, create_time, update_time, create_user, update_user, tenant_id, 
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_testee_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeResultExample">
    delete from project_testee_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeResult">
    insert into project_testee_result (id, project_id, plan_id, 
      visit_id, form_id, form_expand_id, 
      form_detail_id, testee_id, group_id, 
      resource_variable_id, base_variable_id, label, 
      field_name, field_value, field_text, 
      unit_value, unit_text, score_value, 
      option_hidden, complate_status, testee_input, 
      data_from, status, sort, 
      create_time, update_time, create_user, 
      update_user, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formExpandId,jdbcType=BIGINT}, 
      #{formDetailId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, 
      #{resourceVariableId,jdbcType=BIGINT}, #{baseVariableId,jdbcType=BIGINT}, #{label,jdbcType=VARCHAR}, 
      #{fieldName,jdbcType=VARCHAR}, #{fieldValue,jdbcType=VARCHAR}, #{fieldText,jdbcType=VARCHAR}, 
      #{unitValue,jdbcType=VARCHAR}, #{unitText,jdbcType=VARCHAR}, #{scoreValue,jdbcType=DECIMAL}, 
      #{optionHidden,jdbcType=BIT}, #{complateStatus,jdbcType=VARCHAR}, #{testeeInput,jdbcType=BIT}, 
      #{dataFrom,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, 
      #{updateUser,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeResult">
    insert into project_testee_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formExpandId != null">
        form_expand_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="resourceVariableId != null">
        resource_variable_id,
      </if>
      <if test="baseVariableId != null">
        base_variable_id,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="fieldValue != null">
        field_value,
      </if>
      <if test="fieldText != null">
        field_text,
      </if>
      <if test="unitValue != null">
        unit_value,
      </if>
      <if test="unitText != null">
        unit_text,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="optionHidden != null">
        option_hidden,
      </if>
      <if test="complateStatus != null">
        complate_status,
      </if>
      <if test="testeeInput != null">
        testee_input,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formExpandId != null">
        #{formExpandId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="resourceVariableId != null">
        #{resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="baseVariableId != null">
        #{baseVariableId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldValue != null">
        #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null">
        #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="unitText != null">
        #{unitText,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="optionHidden != null">
        #{optionHidden,jdbcType=BIT},
      </if>
      <if test="complateStatus != null">
        #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="testeeInput != null">
        #{testeeInput,jdbcType=BIT},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeResultExample" resultType="java.lang.Long">
    select count(*) from project_testee_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formExpandId != null">
        form_expand_id = #{record.formExpandId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceVariableId != null">
        resource_variable_id = #{record.resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.baseVariableId != null">
        base_variable_id = #{record.baseVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldName != null">
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldValue != null">
        field_value = #{record.fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldText != null">
        field_text = #{record.fieldText,jdbcType=VARCHAR},
      </if>
      <if test="record.unitValue != null">
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
      </if>
      <if test="record.unitText != null">
        unit_text = #{record.unitText,jdbcType=VARCHAR},
      </if>
      <if test="record.scoreValue != null">
        score_value = #{record.scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="record.optionHidden != null">
        option_hidden = #{record.optionHidden,jdbcType=BIT},
      </if>
      <if test="record.complateStatus != null">
        complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.testeeInput != null">
        testee_input = #{record.testeeInput,jdbcType=BIT},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_result
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_expand_id = #{record.formExpandId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      resource_variable_id = #{record.resourceVariableId,jdbcType=BIGINT},
      base_variable_id = #{record.baseVariableId,jdbcType=BIGINT},
      label = #{record.label,jdbcType=VARCHAR},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      field_value = #{record.fieldValue,jdbcType=VARCHAR},
      field_text = #{record.fieldText,jdbcType=VARCHAR},
      unit_value = #{record.unitValue,jdbcType=VARCHAR},
      unit_text = #{record.unitText,jdbcType=VARCHAR},
      score_value = #{record.scoreValue,jdbcType=DECIMAL},
      option_hidden = #{record.optionHidden,jdbcType=BIT},
      complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      testee_input = #{record.testeeInput,jdbcType=BIT},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeResult">
    update project_testee_result
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formExpandId != null">
        form_expand_id = #{formExpandId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="resourceVariableId != null">
        resource_variable_id = #{resourceVariableId,jdbcType=BIGINT},
      </if>
      <if test="baseVariableId != null">
        base_variable_id = #{baseVariableId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldValue != null">
        field_value = #{fieldValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null">
        field_text = #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        unit_value = #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="unitText != null">
        unit_text = #{unitText,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="optionHidden != null">
        option_hidden = #{optionHidden,jdbcType=BIT},
      </if>
      <if test="complateStatus != null">
        complate_status = #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="testeeInput != null">
        testee_input = #{testeeInput,jdbcType=BIT},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeResult">
    update project_testee_result
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_expand_id = #{formExpandId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      resource_variable_id = #{resourceVariableId,jdbcType=BIGINT},
      base_variable_id = #{baseVariableId,jdbcType=BIGINT},
      label = #{label,jdbcType=VARCHAR},
      field_name = #{fieldName,jdbcType=VARCHAR},
      field_value = #{fieldValue,jdbcType=VARCHAR},
      field_text = #{fieldText,jdbcType=VARCHAR},
      unit_value = #{unitValue,jdbcType=VARCHAR},
      unit_text = #{unitText,jdbcType=VARCHAR},
      score_value = #{scoreValue,jdbcType=DECIMAL},
      option_hidden = #{optionHidden,jdbcType=BIT},
      complate_status = #{complateStatus,jdbcType=VARCHAR},
      testee_input = #{testeeInput,jdbcType=BIT},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--按照访视分组查询受试者提交结果-->
  <select id="getTesteeFormResultList" resultType="java.util.Map">
    SELECT
      project_testee_info.id testeeId,
      project_visit_config.id visitId,
      project_visit_config.visit_name visitName,
      project_testee_info.code,
      project_testee_info.real_name realName,
      project_testee_result.label,
      project_testee_result.field_name fieldName,
      project_testee_result.field_value fieldValue
    FROM
      project_testee_info
    INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
    INNER JOIN template_form_config ON template_form_config.id = project_testee_result.form_id
    INNER JOIN template_form_detail ON template_form_detail.form_id = template_form_config.id
    INNER JOIN project_visit_config ON project_visit_config.project_id = project_testee_result.project_id
    AND project_visit_config.id = project_testee_result.visit_id
    WHERE project_testee_result.project_id = #{projectId}
    <if test="visitId != null">
      AND project_testee_result.visit_id = #{visitId}
    </if>
    <if test="testeeId != null">
      AND project_testee_result.testee_id = #{testeeId}
    </if>
    /*AND template_form_detail.`key` NOT LIKE 'table%'*/
    AND project_testee_result.status = 0
    AND template_form_detail.`key` NOT LIKE 'uploadImg%'
    ORDER BY template_form_detail.sort asc,template_form_detail.create_time asc
  </select>


  <!--查询项目下受试者列表及表单和表格录入数据-->
  <select id="getProjectTesteeFormAndTableResultList" resultType="com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo">
    SELECT t.* FROM (
      SELECT
        project_testee_info.id testeeId,
        project_testee_result.visit_id visitId,
        project_testee_info.code,
        project_testee_info.real_name realName,
        project_testee_info.create_time createTime,
        project_testee_result.label,
        project_testee_result.field_name fieldName,
        IF(LENGTH(project_testee_result.field_value)=0,'',project_testee_result.field_value) fieldValue
      FROM
        project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
      WHERE 1 = 1
            AND project_testee_result.project_id = #{projectId}
      <if test="planId != null and planId != ''">
        AND project_testee_result.plan_id = #{planId}
      </if>
      <if test= "formId != null and formId != ''">
        AND project_testee_result.form_id = #{formId}
      </if>
      <if test= "variableId != null and variableId != ''">
        AND project_testee_result.form_detail_id = #{variableId}
      </if>
      <if test="projectOrgId != null and projectOrgId != ''">
        AND project_visit_user.owner_org_id = #{projectOrgId}
      </if>
      <if test="testeeId != null and testeeId != ''">
       AND project_testee_info.id = #{testeeId}
      </if>
        AND project_testee_result.`status` = 0
        AND project_testee_result.field_name NOT LIKE 'table%'
        AND project_testee_result.field_name NOT LIKE 'dynamic_group%'

    UNION ALL
          SELECT
            project_testee_info.id testeeId,
            project_testee_table.visit_id visitId,
            project_testee_info.code,
            project_testee_info.real_name realName,
            project_testee_info.create_time createTime,
            project_testee_table.label,
            project_testee_table.field_name fieldName,
            /*CONCAT(project_testee_table.id,'-',project_testee_table.field_name) fieldName,*/
            project_testee_table.`field_value` fieldValue
            /*GROUP_CONCAT(IF(LENGTH(project_testee_table.field_value)=0,'-',project_testee_table.field_value)  ORDER BY project_testee_table.create_time DESC SEPARATOR '\r\n') fieldValue*/
          FROM
            project_testee_info
          INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
          INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
          WHERE 1 = 1 AND project_testee_table.project_id = #{projectId}
            <if test="planId != null and planId != ''">
              AND project_testee_table.plan_id = #{planId}
            </if>
            <if test="formId != null and formId != ''">
              AND project_testee_table.form_id = #{formId}
            </if>
            <if test= "variableId != null and variableId != ''">
              AND project_testee_table.form_table_id = #{variableId}
            </if>
            <if test="projectOrgId != null and projectOrgId != ''">
              AND project_visit_user.owner_org_id = #{projectOrgId}
            </if>
            <if test="testeeId != null and testeeId != ''">
              AND project_testee_info.id = #{testeeId}
            </if>
            AND project_testee_table.status = 0
        ) t where t.testeeId IS NOT NULL
    ORDER BY t.testeeId, t.createTime desc
  </select>

  <select id="getResearchTesteeResultList" resultType="java.util.Map">
    SELECT
      project_testee_result.testee_id testeeId,
      project_visit_config.id visitId,
      project_visit_config.visit_name visitName,
      project_testee_info.code,
      project_testee_info.real_name realName,
      project_testee_result.label,
      project_testee_result.field_name fieldName,
      project_testee_result.field_value fieldValue
    FROM
        template_form_config
    INNER JOIN template_form_detail ON template_form_detail.form_id = template_form_config.id
    LEFT JOIN project_testee_result ON project_testee_result.form_detail_id = template_form_detail.id
    INNER JOIN project_testee_info ON project_testee_info.id = project_testee_result.testee_id
    INNER JOIN project_visit_config ON project_visit_config.project_id = project_testee_result.project_id
    AND project_visit_config.id = project_testee_result.visit_id
    WHERE project_testee_result.status = 0 and project_testee_result.project_id = #{projectId}
    <if test="testeeId != null">
      AND project_testee_result.testee_id = #{testeeId}
    </if>
    AND template_form_detail.`key` NOT LIKE 'table%' AND template_form_detail.`key` NOT LIKE 'uploadImg%'
    ORDER BY template_form_detail.sort
  </select>

  <select id="getLastTesteeVisitFollowTime" resultType="com.haoys.user.model.ProjectTesteeResult">
    SELECT
      project_testee_result.testee_id testeeId,
      project_testee_result.label,
      project_testee_result.field_name fieldName,
      project_testee_result.field_value fieldValue
    FROM
        project_testee_result
    WHERE project_testee_result.status = 0
    AND project_testee_result.project_id = #{projectId}
    AND project_testee_result.visit_id = #{visitId}
    AND project_testee_result.testee_id = #{testeeId}
    AND project_testee_result.label REGEXP #{followVisitViewName}
    ORDER BY project_testee_result.create_time DESC limit 1
  </select>

  <!--查询受试者分组字段录入结果-->
  <select id="getProjectTesteeGroupResult" resultMap="BaseResultMap">
    select * from project_testee_result where 1 = 1
      AND project_testee_result.project_id = #{projectId}
      AND project_testee_result.visit_id = #{visitId}
      AND project_testee_result.form_id = #{formId}
      AND project_testee_result.form_detail_id = #{groupVariableId}
      AND project_testee_result.testee_id = #{testeeId}
      AND project_testee_result.group_id = #{groupId}
      AND project_testee_result.status = '0'
  </select>

  <!--受试者数据导出-普通表单提交记录-->
  <select id="getProjectTesteeFormResultByVariableIds" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeResultExportVo">
    SELECT
    project_testee_info.id testeeId,
      project_testee_result.visit_id visitId,
      project_testee_info.code,
      project_testee_info.real_name realName,
      project_testee_info.owner_org_id ownerOrgId,
      project_testee_info.owner_org_name ownerOrgName,
      project_testee_info.owner_doctor_id ownerDoctor,
      project_testee_info.create_time createTime,
      project_testee_result.label,
      project_testee_result.field_name fieldName,
      IF(LENGTH(project_testee_result.field_value)=0,'',project_testee_result.field_value) fieldValue
    FROM
        project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
    INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
    WHERE 1 = 1
    AND project_testee_result.project_id = #{projectId}
    <if test="visitId != null and visitId != ''">
      AND project_testee_result.visit_id = #{visitId}
    </if>
    <if test= "formId != null and formId != ''">
      AND project_testee_result.form_id = #{formId}
    </if>
    <if test= "formDetailId != null and formDetailId != ''">
      AND project_testee_result.form_detail_id = #{formDetailId}
    </if>
    <if test="testeeId != null and testeeId != ''">
      AND project_testee_info.id = #{testeeId}
    </if>
    <if test="orgId != null and orgId != ''">
      AND project_testee_info.owner_org_id = #{orgId}
    </if>
    AND project_testee_result.`status` = 0
    AND project_testee_result.field_name NOT LIKE 'table%'
    AND project_testee_result.field_name NOT LIKE 'dynamic_group%'
  </select>


  <resultMap id="testeeResultMap" type="com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo">
    <result column="testeeId" jdbcType="VARCHAR" property="testeeId"/>
    <result column="code" jdbcType="VARCHAR" property="code"/>
    <result column="realName" jdbcType="VARCHAR" property="realName"/>
    <result column="gender" jdbcType="VARCHAR" property="gender"/>
    <result column="ownerOrgId" jdbcType="VARCHAR" property="ownerOrgId"/>
    <result column="ownerOrgName" jdbcType="VARCHAR" property="ownerOrgName"/>
    <result column="ownerDoctor" jdbcType="VARCHAR" property="ownerDoctor"/>
    <result column="contant" jdbcType="VARCHAR" property="contant" />
    <result column="joinGroupTime" jdbcType="TIMESTAMP" property="joinGroupTime"/>
    <!--<collection property="variableList" javaType="java.util.List" resultMap="formVariableResultMap">
    </collection>-->
  </resultMap>

  <resultMap id="formVariableResultMap" type="com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo$FormVariableVo">
    <result column="label" jdbcType="VARCHAR" property="label"/>
    <result column="fieldName"  jdbcType="VARCHAR" property="fieldName"/>
    <result column="fieldValue" jdbcType="VARCHAR" property="fieldValue"/>
  </resultMap>

  <!--导出受试者查询列表-->
  <select id="getExportProjectTesteeListForPage" resultMap="testeeResultMap">
    SELECT t.testeeId, t.code, t.realName, t.ownerOrgId, t.ownerOrgName, t.ownerDoctor,
           t.contant, t.joinGroupTime, t.label, t.fieldName, t.fieldValue FROM (
      SELECT
        project_testee_info.id testeeId,
        project_testee_result.visit_id visitId,
        project_testee_result.form_id formId,
        project_testee_result.form_detail_id formDetailId,
        '' formTableId,
        project_testee_info.code,
        project_testee_info.real_name realName,
        project_testee_info.gender,
        project_testee_info.owner_org_id ownerOrgId,
        project_testee_info.owner_org_name ownerOrgName,
        project_testee_info.owner_doctor ownerDoctor,
        project_testee_info.contant,
        project_testee_info.create_time joinGroupTime,
        project_testee_result.label,
        project_testee_result.field_name fieldName,
        IF(LENGTH(project_testee_result.field_value)=0,'',project_testee_result.field_value) fieldValue
      FROM
          project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
      WHERE 1 = 1
      AND project_testee_result.project_id = #{projectId}
      <if test="conditionValue != null and conditionValue != ''">
        ${conditionValue}
      </if>
      AND project_testee_result.`status` = '0'
      AND project_testee_result.field_name NOT LIKE 'table%'
      AND project_testee_result.field_name NOT LIKE 'dynamic_group%'
    UNION ALL
      SELECT
        project_testee_info.id testeeId,
        project_testee_table.visit_id visitId,
        project_testee_table.form_id formId,
        project_testee_table.form_detail_id formDetailId,
        project_testee_table.form_table_id formTableId,
        project_testee_info.code,
        project_testee_info.real_name realName,
        project_testee_info.gender,
        project_testee_info.owner_org_id ownerOrgId,
        project_testee_info.owner_org_name ownerOrgName,
        project_testee_info.owner_doctor ownerDoctor,
        project_testee_info.contant,
        project_testee_info.create_time joinGroupTime,
        project_testee_table.label,
        project_testee_table.field_name fieldName,
        MAX(project_testee_table.`field_value`) fieldValue
    FROM
        project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
      WHERE 1 = 1 AND project_testee_table.project_id = #{projectId}
      <if test="conditionTableValue != null and conditionTableValue != ''">
        ${conditionTableValue}
      </if>
      AND project_testee_table.status = '0'
    ) t where 1 = 1
      <!--<if test="conditionTableValue != null and conditionTableValue != ''">
        ${conditionTableValue}
      </if>-->
       AND t.testeeId IS NOT NULL GROUP BY t.testeeId
    ORDER BY t.realName DESC, t.joinGroupTime ASC

  </select>

  <select id="getExportProjectTesteeVariableValue" resultMap="formVariableResultMap">
    SELECT t.formDetailId, t.label, t.fieldName, t.fieldValue FROM (
      SELECT
        project_testee_info.id testeeId,
        project_testee_info.real_name realName,
        project_testee_result.visit_id visitId,
        project_testee_result.form_id formId,
        project_testee_result.form_detail_id formDetailId,
        '' formTableId,
        '1' AS rowNumber,
        '1' AS tableSort,
        project_testee_result.id,
        project_testee_result.label,
        project_testee_result.field_name fieldName,
        IF(LENGTH(project_testee_result.field_value)=0,'',project_testee_result.field_value) fieldValue
      FROM
      project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
      WHERE 1 = 1
      AND project_testee_result.project_id = #{projectId}
      AND project_testee_result.testee_id = #{testeeId}
      <if test="conditionValue != null and conditionValue != ''">
        ${conditionValue}
      </if>
      AND project_testee_result.`status` = '0'
      AND project_testee_result.field_name NOT LIKE 'table%'
      AND project_testee_result.field_name NOT LIKE 'dynamic_group%'
    UNION ALL
    SELECT
      project_testee_info.id testeeId,
      project_testee_info.real_name realName,
      project_testee_table.visit_id visitId,
      project_testee_table.form_id formId,
      project_testee_table.form_detail_id formDetailId,
      project_testee_table.form_table_id formTableId,
      project_testee_table.row_no rowNumber,
      project_testee_table.sort tableSort,
      project_testee_table.id,
      project_testee_table.label,
      project_testee_table.field_name fieldName,
      MAX(project_testee_table.`field_value`) fieldValue
    FROM
        project_testee_info
      INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
      INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
      WHERE 1 = 1
      AND project_testee_table.project_id = #{projectId}
      AND project_testee_table.testee_id = #{testeeId}
      <if test="conditionTableValue != null and conditionTableValue != ''">
        ${conditionTableValue}
      </if>
      AND project_testee_table.status = '0'
    ) t where 1 = 1
        <!--<if test="conditionTableValue != null and conditionTableValue != ''">
          ${conditionTableValue}
        </if>-->
      AND t.testeeId IS NOT NULL
    ORDER BY t.realName DESC, t.formDetailId DESC, t.rowNumber ASC, t.tableSort ASC
  </select>

  <select id="getProjectTesteeVariableResultFromMysqlDataSource" resultType="com.haoys.user.domain.vo.ecrf.ProjectTesteeVariableResultVo">
    SELECT
        t.project_id,
        t.visit_id,
        t.form_id,
        t.group_id,
        t.table_id,
        t.variable_id,
        t.variable_name,
        t.variable_record_id,
        t.record_row_number,
        t.record_row_sort,
        t.variable_record_time,
        t.testee_id,
        t.testee_code,
        t.real_name,
        t.gender,
        t.birthday,
        t.owner_org_id,
        t.owner_org_name,
        t.owner_doctor_id,
        t.mobile,
        t.join_group_time,
        t.field_name,
        t.field_value
    FROM (
        SELECT
            project_testee_info.id testee_id,
            project_testee_result.project_id,
            project_testee_result.visit_id,
            project_testee_result.form_id,
            project_testee_result.group_id,
            NULL table_id,
            project_testee_result.create_time variable_record_time,
            NULL record_row_sort,
            NULL record_row_number,
            project_testee_result.form_detail_id variable_id,
            project_testee_result.id variable_record_id,
            project_testee_info.code testee_code,
            project_testee_info.real_name ,
            project_testee_info.gender,
            project_testee_info.birthday,
            project_testee_info.owner_org_id owner_org_id,
            project_testee_info.owner_org_name owner_org_name,
            project_testee_info.owner_doctor owner_doctor_id,
            project_testee_info.contant mobile,
            project_testee_info.create_time join_group_time,
            project_testee_result.label variable_name,
            project_testee_result.field_name field_name,
        IF (LENGTH( project_testee_result.field_value ) = 0, '', project_testee_result.field_value ) field_value
        FROM
            project_testee_info
            INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
            INNER JOIN project_testee_result ON project_testee_result.testee_id = project_testee_info.id
        WHERE project_testee_result.project_id = #{projectId} AND project_testee_result.testee_id = #{testeeId}
                AND project_testee_result.`status` = '0'
                AND project_testee_result.field_name NOT LIKE 'dynamic_group%'
    UNION ALL
        SELECT
            project_testee_info.id testee_id,
            project_testee_table.project_id,
            project_testee_table.visit_id,
            project_testee_table.form_id,
            project_testee_table.group_id,
            project_testee_table.form_detail_id table_id,
            project_testee_table.create_time variable_record_time,
            project_testee_table.sort record_row_sort,
            project_testee_table.row_no record_row_number,
            project_testee_table.form_table_id variable_id,
            project_testee_table.id variable_record_id,
            project_testee_info.code testee_code,
            project_testee_info.real_name,
            project_testee_info.gender,
            project_testee_info.birthday,
            project_testee_info.owner_org_id ,
            project_testee_info.owner_org_name ,
            project_testee_info.owner_doctor owner_doctor_id,
            project_testee_info.contant mobile,
            project_testee_info.create_time join_group_time,
            project_testee_table.label variable_name,
            project_testee_table.field_name field_name,
            project_testee_table.`field_value` field_value
        FROM
            project_testee_info
            INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id
            INNER JOIN project_testee_table ON project_testee_table.testee_id = project_testee_info.id
        WHERE project_testee_table.project_id = #{projectId} AND project_testee_table.testee_id = #{testeeId} AND project_testee_table.status = '0'
        ) t
    WHERE 1 = 1  AND t.testee_id IS NOT NULL ORDER BY t.real_name DESC, t.table_id DESC, t.record_row_number ASC, t.record_row_sort ASC
  </select>

  <!--患者表单数据综合查询-->
  <!--t.testee_id, t.group_id, t.variable_id, t.fieldLabel, GROUP_CONCAT(t.fieldValue) fieldValue-->
  <select id="getExportProjectTesteeListForPageVersion2" resultType="java.util.Map">
    SELECT t.*
        FROM project_testee_info INNER JOIN (
          SELECT
                project_id, testee_id,project_testee_result.group_id,project_testee_result.form_detail_id variable_id,
          <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="" separator="," close="">
            <if test="formAndTable.groupId == null or formAndTable.groupId == ''">
              IFNULL(MAX(CASE `form_detail_id` WHEN #{formAndTable.variableId} THEN field_value END),'-') AS #{formAndTable.fieldName}
            </if>
            <if test="formAndTable.groupId != null and formAndTable.groupId != ''">
              IFNULL(MAX(CASE `resource_variable_id` WHEN #{formAndTable.variableId} THEN field_value END),'-') AS #{formAndTable.fieldName}
            </if>
          </foreach>
          FROM project_testee_result WHERE project_id = #{projectId}
            AND project_testee_result.`form_detail_id` IN
          <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="(" separator="," close=")">
            <!--<if test="formAndTable.groupId == null or formAndTable.groupId == ''">-->
                #{formAndTable.variableId}
            <!--</if>-->
          </foreach>
            OR project_testee_result.`resource_variable_id` IN
          <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="(" separator="," close=")">
            <!--<if test="formAndTable.groupId != null and formAndTable.groupId != ''">-->
              #{formAndTable.variableId}
            <!--</if>-->
          </foreach>
          GROUP BY project_testee_result.testee_id
          UNION ALL
          SELECT
            project_id, testee_id,project_testee_table.group_id,project_testee_table.form_table_id variable_id,
          <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="" separator="," close="">
            <if test="formAndTable.groupId == null or formAndTable.groupId == ''">
              IFNULL(MAX(CASE `form_table_id` WHEN #{formAndTable.variableId} THEN field_value END),'-') AS #{formAndTable.fieldName}
            </if>
            <if test="formAndTable.groupId != null and formAndTable.groupId != ''">
              IFNULL(MAX(CASE `resource_table_id` WHEN #{formAndTable.variableId} THEN field_value END),'-') AS #{formAndTable.fieldName}
            </if>
          </foreach>
          FROM project_testee_table WHERE project_id = #{projectId}
          AND project_testee_table.`form_table_id` IN
          <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="(" separator="," close=")">
            <!--<if test="formAndTable.groupId == null or formAndTable.groupId == ''">-->
              #{formAndTable.variableId}
            <!--</if>-->
          </foreach>
          OR project_testee_table.`resource_table_id` IN
          <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="(" separator="," close=")">
            <!--<if test="formAndTable.groupId != null and formAndTable.groupId != ''">-->
              #{formAndTable.variableId}
            <!--</if>-->
          </foreach>
          GROUP BY project_testee_table.form_detail_id, project_testee_table.row_no
        ) t ON t.testee_id = project_testee_info.id
      WHERE 1 = 1
        <if test="orgIds != null and orgIds != ''">
          AND project_testee_info.owner_org_id IN (${orgIds})
        </if>
        <foreach collection="queryTesteeFormAndTableParamList" item="formAndTable" open="" separator=" " close="">
          ${formAndTable.operatorValue} t.${formAndTable.fieldName} ${formAndTable.relation} ${formAndTable.inputValue}
        </foreach>
        GROUP BY testee_id
        ORDER BY ${sortField} ${sortType}

  </select>

  <!--查询受试者普通表单变量提交记录-->
  <select id="getTesteeFormResultValueByFormDetailId" resultMap="BaseResultMap">
    SELECT * FROM project_testee_result WHERE project_id = #{projectId} AND plan_id = #{planId} AND visit_id = #{visitId}
    AND form_id = #{formId}
    <if test="formExpandId != null and formExpandId != ''">
      AND project_testee_result.form_expand_id =#{formExpandId}
    </if>
    AND form_detail_id = #{formDetailId} AND testee_id = #{testeeId} AND status = '0'
  </select>

  <delete id="deleteByGroupId">
    update project_testee_result set status=#{status} ,update_user=#{userId},update_time=sysdate() where group_id=#{groupId}
  </delete>

  <select id="getSystemDictionaryOptionReference" resultType="String">
    (select template_form_detail.form_id from template_form_detail inner join project_testee_result on project_testee_result.form_detail_id = template_form_detail.id
    where template_form_detail.dic_resource = '1' and template_form_detail.ref_dic_id = #{dictionaryId} and template_form_detail.status = '0' limit 1)
    UNION ALL
    (select template_form_table.form_id from template_form_table inner join project_testee_table on project_testee_table.form_table_id = template_form_table.id
    where template_form_table.dic_resource = '1' and template_form_table.ref_dic_id = #{dictionaryId} and template_form_table.status = '0' limit 1)
  </select>

  <select id="getProjectDictionaryOptionReference" resultType="String">
    select template_form_detail.form_id from template_form_detail inner join project_testee_result on project_testee_result.form_detail_id = template_form_detail.id
    where project_testee_result.project_id = #{projectId} and template_form_detail.dic_resource != '1' and template_form_detail.ref_dic_id = #{dictionaryId}
      and template_form_detail.status = '0' limit 1
    UNION ALL
    select form_id from template_form_table inner join project_testee_table on project_testee_table.form_table_id = template_form_table.id
    where project_testee_table.project_id = #{projectId} and template_form_table.dic_resource != '1' and template_form_table.ref_dic_id = #{dictionaryId}
      and template_form_table.status = '0' limit 1
  </select>

  <select id="getTesteeFormBaseInfoByFormVariableId" resultType="com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo">
    select
      project_testee_result.*, template_form_detail.type
    from
      project_testee_result
    inner join template_form_detail on template_form_detail.id = project_testee_result.form_detail_id
    where project_testee_result.project_id = #{projectId} and project_testee_result.form_id = #{formId}
    and project_testee_result.form_detail_id = #{formDetailId}
    and project_testee_result.testee_id = #{testeeId} and project_testee_result.testee_input = true
    and project_testee_result.status = '0'
  </select>

  <select id="getProjectTesteeFormVariableIdComplateCount" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeFormVariableComplateResultVo">
    SELECT
      SUM(IF(project_testee_result.complate_status = '1', 1, 0)) inputCount,
      SUM(IF(project_testee_result.complate_status = '3', 1, 0)) complateCount,
      COUNT(template_form_detail.id) variableCount
    FROM
      project_testee_result INNER JOIN template_form_detail ON template_form_detail.id = project_testee_result.form_detail_id
        AND project_testee_result.form_id = template_form_detail.form_id
    WHERE project_testee_result.project_id = #{projectId}
      AND project_testee_result.plan_id = #{planId}
      AND project_testee_result.visit_id = #{visitId}
      AND project_testee_result.form_id = #{formId}
      AND project_testee_result.testee_id = #{testeeId}
      AND (project_testee_result.testee_input IS NULL OR project_testee_result.testee_input = false) AND project_testee_result.status = '0'
      AND template_form_detail.type NOT IN('table','dynamic_group')
    GROUP BY project_testee_result.project_id,project_testee_result.plan_id,project_testee_result.visit_id,project_testee_result.form_id
  </select>

  <select id="getProjectTesteeTableVariableIdComplateCount" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeFormVariableComplateResultVo">
    SELECT
      SUM(IF(project_testee_table.complate_status = '1', 1, 0)) inputCount,
      SUM(IF(project_testee_table.complate_status = '3', 1, 0)) complateCount,
      COUNT(template_form_table.id) variableCount
    FROM
      project_testee_table INNER JOIN template_form_table ON template_form_table.id = project_testee_table.form_table_id
        AND template_form_table.form_detail_id = template_form_table.form_detail_id
    WHERE project_testee_table.project_id = #{projectId}
      AND project_testee_table.plan_id = #{planId}
      AND project_testee_table.visit_id = #{visitId}
      AND project_testee_table.form_id = #{formId}
      AND project_testee_table.testee_id = #{testeeId}
      AND project_testee_table.status = '0'
    GROUP BY project_testee_table.project_id, project_testee_table.plan_id, project_testee_table.visit_id, project_testee_table.form_id
  </select>
  <select id="getProjectTesteeGroupResultList" resultType="com.haoys.user.domain.vo.participant.ProjectTesteeResultVo">
    select
        project_testee_result.id,
        project_testee_result.visit_id as visitId,
        project_testee_result.form_id as formId,
        project_testee_result.form_detail_id as formDetailId,
        project_testee_result.resource_variable_id as resourceVariableId,
        project_testee_result.group_id as groupId,
        project_testee_result.field_value as fieldValue,
        template_form_detail.dic_resource as dicResource
        from project_testee_result
        join template_form_detail on template_form_detail.id=project_testee_result.form_detail_id
        where  project_testee_result.project_id = #{projectId}
        AND project_testee_result.visit_id = #{visitId}
        AND project_testee_result.form_id = #{formId}
        AND project_testee_result.resource_variable_id = #{formDetailId}
        AND project_testee_result.testee_id = #{testeeId}
        AND project_testee_result.status = '0'
  </select>


  <insert id="batchInsertTesteeRecord" parameterType="java.util.List">
    insert into testee_variable_result(project_id, plan_id, visit_id, form_id, form_detail_id,testee_id,field_name,field_value,create_time) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.projectId},#{item.planId},#{item.visitId},#{item.formId},#{item.formDetailId},
      #{item.testeeId}),#{item.fieldName}),#{item.fieldValue},#{item.createTime}}))
    </foreach>
  </insert>

</mapper>