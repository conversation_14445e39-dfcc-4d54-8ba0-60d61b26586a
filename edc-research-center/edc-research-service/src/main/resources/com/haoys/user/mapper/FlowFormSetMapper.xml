<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.FlowFormSetMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.FlowFormSet">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="xh" jdbcType="INTEGER" property="xh" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="mo_ro_auth" jdbcType="BIT" property="moRoAuth" />
    <result column="mo_rw_auth" jdbcType="BIT" property="moRwAuth" />
    <result column="pc_ro_auth" jdbcType="BIT" property="pcRoAuth" />
    <result column="pc_rw_auth" jdbcType="BIT" property="pcRwAuth" />
    <result column="lab_config_scope" jdbcType="VARCHAR" property="labConfigScope" />
    <result column="enable_randomized_config" jdbcType="BIT" property="enableRandomizedConfig" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, xh, project_id, plan_id, visit_id, form_id, mo_ro_auth, mo_rw_auth, pc_ro_auth,
    pc_rw_auth, lab_config_scope, enable_randomized_config, status, create_time, create_user,
    update_time, update_user, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.FlowFormSetExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from flow_form_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from flow_form_set
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from flow_form_set
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.FlowFormSetExample">
    delete from flow_form_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.FlowFormSet">
    insert into flow_form_set (id, xh, project_id,
      plan_id, visit_id, form_id,
      mo_ro_auth, mo_rw_auth, pc_ro_auth, pc_rw_auth,
      lab_config_scope, enable_randomized_config, status,
      create_time, create_user, update_time,
      update_user, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{xh,jdbcType=INTEGER}, #{projectId,jdbcType=BIGINT},
      #{planId,jdbcType=BIGINT}, #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT},
      #{moRoAuth,jdbcType=BIT}, #{moRwAuth,jdbcType=BIT}, #{pcRoAuth,jdbcType=BIT}, #{pcRwAuth,jdbcType=BIT},
      #{labConfigScope,jdbcType=VARCHAR}, #{enableRandomizedConfig,jdbcType=BIT}, #{status,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateUser,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.FlowFormSet">
    insert into flow_form_set
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="xh != null">
        xh,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="moRoAuth != null">
        mo_ro_auth,
      </if>
      <if test="moRwAuth != null">
        mo_rw_auth,
      </if>
      <if test="pcRoAuth != null">
        pc_ro_auth,
      </if>
      <if test="pcRwAuth != null">
        pc_rw_auth,
      </if>
      <if test="labConfigScope != null">
        lab_config_scope,
      </if>
      <if test="enableRandomizedConfig != null">
        enable_randomized_config,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="xh != null">
        #{xh,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="moRoAuth != null">
        #{moRoAuth,jdbcType=BIT},
      </if>
      <if test="moRwAuth != null">
        #{moRwAuth,jdbcType=BIT},
      </if>
      <if test="pcRoAuth != null">
        #{pcRoAuth,jdbcType=BIT},
      </if>
      <if test="pcRwAuth != null">
        #{pcRwAuth,jdbcType=BIT},
      </if>
      <if test="labConfigScope != null">
        #{labConfigScope,jdbcType=VARCHAR},
      </if>
      <if test="enableRandomizedConfig != null">
        #{enableRandomizedConfig,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.FlowFormSetExample" resultType="java.lang.Long">
    select count(*) from flow_form_set
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update flow_form_set
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.xh != null">
        xh = #{record.xh,jdbcType=INTEGER},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.moRoAuth != null">
        mo_ro_auth = #{record.moRoAuth,jdbcType=BIT},
      </if>
      <if test="record.moRwAuth != null">
        mo_rw_auth = #{record.moRwAuth,jdbcType=BIT},
      </if>
      <if test="record.pcRoAuth != null">
        pc_ro_auth = #{record.pcRoAuth,jdbcType=BIT},
      </if>
      <if test="record.pcRwAuth != null">
        pc_rw_auth = #{record.pcRwAuth,jdbcType=BIT},
      </if>
      <if test="record.labConfigScope != null">
        lab_config_scope = #{record.labConfigScope,jdbcType=VARCHAR},
      </if>
      <if test="record.enableRandomizedConfig != null">
        enable_randomized_config = #{record.enableRandomizedConfig,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update flow_form_set
    set id = #{record.id,jdbcType=BIGINT},
      xh = #{record.xh,jdbcType=INTEGER},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      mo_ro_auth = #{record.moRoAuth,jdbcType=BIT},
      mo_rw_auth = #{record.moRwAuth,jdbcType=BIT},
      pc_ro_auth = #{record.pcRoAuth,jdbcType=BIT},
      pc_rw_auth = #{record.pcRwAuth,jdbcType=BIT},
      lab_config_scope = #{record.labConfigScope,jdbcType=VARCHAR},
      enable_randomized_config = #{record.enableRandomizedConfig,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.FlowFormSet">
    update flow_form_set
    <set>
      <if test="xh != null">
        xh = #{xh,jdbcType=INTEGER},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="moRoAuth != null">
        mo_ro_auth = #{moRoAuth,jdbcType=BIT},
      </if>
      <if test="moRwAuth != null">
        mo_rw_auth = #{moRwAuth,jdbcType=BIT},
      </if>
      <if test="pcRoAuth != null">
        pc_ro_auth = #{pcRoAuth,jdbcType=BIT},
      </if>
      <if test="pcRwAuth != null">
        pc_rw_auth = #{pcRwAuth,jdbcType=BIT},
      </if>
      <if test="labConfigScope != null">
        lab_config_scope = #{labConfigScope,jdbcType=VARCHAR},
      </if>
      <if test="enableRandomizedConfig != null">
        enable_randomized_config = #{enableRandomizedConfig,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.FlowFormSet">
    update flow_form_set
    set xh = #{xh,jdbcType=INTEGER},
      project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      mo_ro_auth = #{moRoAuth,jdbcType=BIT},
      mo_rw_auth = #{moRwAuth,jdbcType=BIT},
      pc_ro_auth = #{pcRoAuth,jdbcType=BIT},
      pc_rw_auth = #{pcRwAuth,jdbcType=BIT},
      lab_config_scope = #{labConfigScope,jdbcType=VARCHAR},
      enable_randomized_config = #{enableRandomizedConfig,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="UpdatePer">
    update flow_form_set
    <set>
      <if test="moRoAuth != null">
        mo_ro_auth = #{moRoAuth,jdbcType=VARCHAR},
      </if>
      <if test="moRwAuth != null">
        mo_rw_auth = #{moRwAuth,jdbcType=VARCHAR},
      </if>
      <if test="pcRoAuth != null">
        pc_ro_auth = #{pcRoAuth,jdbcType=VARCHAR},
      </if>
      <if test="pcRwAuth != null">
        pc_rw_auth = #{pcRwAuth,jdbcType=VARCHAR},
      </if>
    </set>
    where project_id = #{projectId}
      and visit_id = #{visitId}
  </update>
  <insert id="batchSave">
  insert into flow_form_set (id, xh, project_id,
  plan_id, visit_id, form_id,
  mo_ro_auth, mo_rw_auth, pc_ro_auth,
  pc_rw_auth, status, create_time,
  create_user, update_time, update_user,
  tenant_id, platform_id)
  values
  <foreach item="item" index="index" collection="list" separator=",">
    (#{id,jdbcType=BIGINT}, #{xh,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT},
    #{planId,jdbcType=BIGINT}, #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT},
    #{moRoAuth,jdbcType=VARCHAR}, #{moRwAuth,jdbcType=VARCHAR}, #{pcRoAuth,jdbcType=VARCHAR},
    #{pcRwAuth,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
    #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
    #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </foreach>
  </insert>

  <select id="listByFlowId" resultType="com.haoys.user.domain.expand.FlowFormSetValueExpand">
    SELECT
      formset.id,
      formset.xh,
      formset.project_id as projectId,
      formset.plan_id as planId,
      formset.visit_id as visitId,
      formset.form_id as formId,
      form.form_name as formName,
      form.form_code as formCode,
      formset.mo_ro_auth as moRoAuth,
      formset.mo_rw_auth as moRwAuth,
      formset.pc_ro_auth as pcRoAuth,
      formset.pc_rw_auth as pcRwAuth
    FROM
      flow_form_set formset
      JOIN template_form_config form ON form.id = formset.form_Id and form.status=0
    WHERE
      formset.visit_id = #{flowId}
    ORDER BY
      formset.xh ASC
  </select>
  <select id="listByFlowIdAndFilterPer" resultType="com.haoys.user.domain.vo.flow.ProjectFlowFormVo">
    SELECT
      formset.form_id as formId,
      form.form_name as formName
    FROM
      flow_form_set formset
      JOIN template_form_config form ON form.id = formset.form_Id
    WHERE
      formset.visit_id = #{visitId}
    <if test="moRoAuth != null">
      and mo_ro_auth = #{moRoAuth}
    </if>
    <if test="moRwAuth != null">
      and mo_rw_auth = #{moRwAuth}
    </if>
    <if test="pcRoAuth != null">
      and pc_ro_auth = #{pcRoAuth}
    </if>
    <if test="pcRwAuth != null">
      and pc_rw_auth = #{pcRwAuth}
    </if>
    ORDER BY
      formset.xh ASC
  </select>

  <select id="getFormConfigListByProjectIdAndFormId" resultType="com.haoys.user.domain.expand.FlowFormSetValueExpand">
    select * from flow_form_set where project_id = #{projectId} and plan_id = #{planId} and visit_id = #{visitId} and form_id = #{formId} and status = '0'
  </select>
  <select id="getFlowFormSetValueExpandByFlowId"
          resultType="com.haoys.user.domain.expand.FlowFormSetValueExpand">
    SELECT
      formset.id,
      formset.xh,
      formset.project_id as projectId,
      formset.plan_id as planId,
      formset.visit_id as visitId,
      formset.form_id as formId,
      form.form_name as formName,
      form.form_code as formCode,
      formset.mo_ro_auth as moRoAuth,
      formset.mo_rw_auth as moRwAuth,
      formset.pc_ro_auth as pcRoAuth,
      formset.pc_rw_auth as pcRwAuth
    FROM
      flow_form_set formset
        JOIN template_form_config form ON form.id = formset.form_Id and form.status=0
    WHERE
      formset.visit_id = #{flowId}
    ORDER BY
      formset.xh ASC
  </select>

</mapper>
