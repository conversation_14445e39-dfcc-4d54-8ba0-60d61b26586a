<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeExportManageMapper">
    <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeExportManage">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="exprot_name" jdbcType="VARCHAR" property="exprotName"/>
        <result column="export_type" jdbcType="CHAR" property="exportType"/>
        <result column="size" jdbcType="VARCHAR" property="size"/>
        <result column="export_press" jdbcType="CHAR" property="exportPress"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="platform_id" jdbcType="VARCHAR" property="platformId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , exprot_name, export_type, size, export_press, create_time, create_user, update_time,
    update_user, tenant_id, platform_id
    </sql>
    <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeExportManageExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from project_testee_export_manage
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from project_testee_export_manage
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from project_testee_export_manage
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeExportManageExample">
        delete from project_testee_export_manage
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeExportManage">
        insert into project_testee_export_manage (id, exprot_name, export_type,
                                                  size, export_press, create_time,
                                                  create_user, update_time, update_user,
                                                  tenant_id, platform_id)
        values (#{id,jdbcType=INTEGER}, #{exprotName,jdbcType=VARCHAR}, #{exportType,jdbcType=CHAR},
                #{size,jdbcType=VARCHAR}, #{exportPress,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeExportManage">
        insert into project_testee_export_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="exprotName != null">
                exprot_name,
            </if>
            <if test="exportType != null">
                export_type,
            </if>
            <if test="size != null">
                size,
            </if>
            <if test="exportPress != null">
                export_press,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="platformId != null">
                platform_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="exprotName != null">
                #{exprotName,jdbcType=VARCHAR},
            </if>
            <if test="exportType != null">
                #{exportType,jdbcType=CHAR},
            </if>
            <if test="size != null">
                #{size,jdbcType=VARCHAR},
            </if>
            <if test="exportPress != null">
                #{exportPress,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                #{platformId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeExportManageExample"
            resultType="java.lang.Long">
        select count(*) from project_testee_export_manage
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update project_testee_export_manage
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.exprotName != null">
                exprot_name = #{record.exprotName,jdbcType=VARCHAR},
            </if>
            <if test="record.exportType != null">
                export_type = #{record.exportType,jdbcType=CHAR},
            </if>
            <if test="record.size != null">
                size = #{record.size,jdbcType=VARCHAR},
            </if>
            <if test="record.exportPress != null">
                export_press = #{record.exportPress,jdbcType=CHAR},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createUser != null">
                create_user = #{record.createUser,jdbcType=VARCHAR},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateUser != null">
                update_user = #{record.updateUser,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantId != null">
                tenant_id = #{record.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="record.platformId != null">
                platform_id = #{record.platformId,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update project_testee_export_manage
        set id = #{record.id,jdbcType=INTEGER},
        exprot_name = #{record.exprotName,jdbcType=VARCHAR},
        export_type = #{record.exportType,jdbcType=CHAR},
        size = #{record.size,jdbcType=VARCHAR},
        export_press = #{record.exportPress,jdbcType=CHAR},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        create_user = #{record.createUser,jdbcType=VARCHAR},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
        update_user = #{record.updateUser,jdbcType=VARCHAR},
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
        platform_id = #{record.platformId,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeExportManage">
        update project_testee_export_manage
        <set>
            <if test="exprotName != null">
                exprot_name = #{exprotName,jdbcType=VARCHAR},
            </if>
            <if test="exportType != null">
                export_type = #{exportType,jdbcType=CHAR},
            </if>
            <if test="size != null">
                size = #{size,jdbcType=VARCHAR},
            </if>
            <if test="exportPress != null">
                export_press = #{exportPress,jdbcType=CHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                platform_id = #{platformId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeExportManage">
        update project_testee_export_manage
        set exprot_name  = #{exprotName,jdbcType=VARCHAR},
            export_type  = #{exportType,jdbcType=CHAR},
            size         = #{size,jdbcType=VARCHAR},
            export_press = #{exportPress,jdbcType=CHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP},
            create_user  = #{createUser,jdbcType=VARCHAR},
            update_time  = #{updateTime,jdbcType=TIMESTAMP},
            update_user  = #{updateUser,jdbcType=VARCHAR},
            tenant_id    = #{tenantId,jdbcType=VARCHAR},
            platform_id  = #{platformId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>


    <select id="selectList" resultType="com.haoys.user.model.ProjectTesteeExportManage">
        select man.id,
               man.exprot_name as exprotName,
               man.export_type as exportType,
        (select max(create_time) FROM project_testee_export where export_file_type=man.id and project_id=#{projectId}
        and org_id=#{orgId}
        and operator=#{userId} ) as create_time
        from project_testee_export_manage man
        where 1=1
        <if test="exportName != null and exportName !=''">
            AND man.exprot_name like concat('%', #{exportName}, '%')
        </if>
        <if test="exportType != null and exportType !=0 ">
            AND  man.export_type = #{exportType}
        </if>
    </select>
    <select id="getDownList" resultType="com.haoys.user.model.ProjectTesteeExportManage">
        select ex.task_name as exprotName,
        ex.export_status as exportPress,
        ex.download_url as downloadUrl,
        ex.create_time as createTime,
        ex.export_file_type as exportType,
        ex.id as exId
        from project_testee_export ex
        where 1=1 and ex.id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and ex.status=0
    </select>

</mapper>
