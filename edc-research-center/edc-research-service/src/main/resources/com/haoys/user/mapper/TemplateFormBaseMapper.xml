<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormBaseMapper">

  <delete id="deleteByPrimaryKey"></delete>

  <insert id="insert"></insert>

  <insert id="insertSelective"></insert>

  <select id="selectByPrimaryKey" resultType="com.haoys.user.model.TemplateFormConfig"></select>

  <update id="updateByPrimaryKey"></update>

  <select id="getSystemQueryReturnRecord" resultType="java.util.Map">${queryConditionValue}</select>

  <select id="getSystemQueryReturnTableRecord" resultType="java.util.Map">${queryConditionValue}</select>

</mapper>