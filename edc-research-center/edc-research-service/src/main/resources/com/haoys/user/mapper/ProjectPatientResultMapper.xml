<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectPatientResultMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectPatientResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="task_date" jdbcType="DATE" property="taskDate" />
    <result column="task_rate" jdbcType="VARCHAR" property="taskRate" />
    <result column="push_rate" jdbcType="INTEGER" property="pushRate" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="complate_count" jdbcType="INTEGER" property="complateCount" />
    <result column="complate_time" jdbcType="TIMESTAMP" property="complateTime" />
    <result column="complate_status" jdbcType="VARCHAR" property="complateStatus" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, task_id, task_name, testee_id, task_date, task_rate, push_rate, 
    task_type, complate_count, complate_time, complate_status, expand, submit_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectPatientResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_patient_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_patient_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_patient_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectPatientResultExample">
    delete from project_patient_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectPatientResult">
    insert into project_patient_result (id, project_id, plan_id, 
      task_id, task_name, testee_id, 
      task_date, task_rate, push_rate, 
      task_type, complate_count, complate_time, 
      complate_status, expand, submit_time, 
      create_time)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{taskId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{testeeId,jdbcType=BIGINT}, 
      #{taskDate,jdbcType=DATE}, #{taskRate,jdbcType=VARCHAR}, #{pushRate,jdbcType=INTEGER}, 
      #{taskType,jdbcType=VARCHAR}, #{complateCount,jdbcType=INTEGER}, #{complateTime,jdbcType=TIMESTAMP}, 
      #{complateStatus,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectPatientResult">
    insert into project_patient_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="taskDate != null">
        task_date,
      </if>
      <if test="taskRate != null">
        task_rate,
      </if>
      <if test="pushRate != null">
        push_rate,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="complateCount != null">
        complate_count,
      </if>
      <if test="complateTime != null">
        complate_time,
      </if>
      <if test="complateStatus != null">
        complate_status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="taskDate != null">
        #{taskDate,jdbcType=DATE},
      </if>
      <if test="taskRate != null">
        #{taskRate,jdbcType=VARCHAR},
      </if>
      <if test="pushRate != null">
        #{pushRate,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="complateCount != null">
        #{complateCount,jdbcType=INTEGER},
      </if>
      <if test="complateTime != null">
        #{complateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="complateStatus != null">
        #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectPatientResultExample" resultType="java.lang.Long">
    select count(*) from project_patient_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_patient_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.taskDate != null">
        task_date = #{record.taskDate,jdbcType=DATE},
      </if>
      <if test="record.taskRate != null">
        task_rate = #{record.taskRate,jdbcType=VARCHAR},
      </if>
      <if test="record.pushRate != null">
        push_rate = #{record.pushRate,jdbcType=INTEGER},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.complateCount != null">
        complate_count = #{record.complateCount,jdbcType=INTEGER},
      </if>
      <if test="record.complateTime != null">
        complate_time = #{record.complateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.complateStatus != null">
        complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_patient_result
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      task_date = #{record.taskDate,jdbcType=DATE},
      task_rate = #{record.taskRate,jdbcType=VARCHAR},
      push_rate = #{record.pushRate,jdbcType=INTEGER},
      task_type = #{record.taskType,jdbcType=VARCHAR},
      complate_count = #{record.complateCount,jdbcType=INTEGER},
      complate_time = #{record.complateTime,jdbcType=TIMESTAMP},
      complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectPatientResult">
    update project_patient_result
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="taskDate != null">
        task_date = #{taskDate,jdbcType=DATE},
      </if>
      <if test="taskRate != null">
        task_rate = #{taskRate,jdbcType=VARCHAR},
      </if>
      <if test="pushRate != null">
        push_rate = #{pushRate,jdbcType=INTEGER},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="complateCount != null">
        complate_count = #{complateCount,jdbcType=INTEGER},
      </if>
      <if test="complateTime != null">
        complate_time = #{complateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="complateStatus != null">
        complate_status = #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectPatientResult">
    update project_patient_result
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      task_id = #{taskId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      testee_id = #{testeeId,jdbcType=BIGINT},
      task_date = #{taskDate,jdbcType=DATE},
      task_rate = #{taskRate,jdbcType=VARCHAR},
      push_rate = #{pushRate,jdbcType=INTEGER},
      task_type = #{taskType,jdbcType=VARCHAR},
      complate_count = #{complateCount,jdbcType=INTEGER},
      complate_time = #{complateTime,jdbcType=TIMESTAMP},
      complate_status = #{complateStatus,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>