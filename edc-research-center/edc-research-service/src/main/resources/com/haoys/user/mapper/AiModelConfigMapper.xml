<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.AiModelConfigMapper">

    <resultMap id="BaseResultMap" type="com.haoys.user.domain.entity.AiModelConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="api_key" jdbcType="VARCHAR" property="apiKey"/>
        <result column="api_url" jdbcType="VARCHAR" property="apiUrl"/>
        <result column="max_tokens" jdbcType="INTEGER" property="maxTokens"/>
        <result column="temperature" jdbcType="DECIMAL" property="temperature"/>
        <result column="top_p" jdbcType="DECIMAL" property="topP"/>
        <result column="frequency_penalty" jdbcType="DECIMAL" property="frequencyPenalty"/>
        <result column="presence_penalty" jdbcType="DECIMAL" property="presencePenalty"/>
        <result column="system_prompt" jdbcType="LONGVARCHAR" property="systemPrompt"/>
        <result column="token_price_input" jdbcType="DECIMAL" property="tokenPriceInput"/>
        <result column="token_price_output" jdbcType="DECIMAL" property="tokenPriceOutput"/>
        <result column="rate_limit_rpm" jdbcType="INTEGER" property="rateLimitRpm"/>
        <result column="rate_limit_tpm" jdbcType="INTEGER" property="rateLimitTpm"/>
        <result column="enabled" jdbcType="TINYINT" property="enabled"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, model_type, model_name, api_key, api_url, max_tokens, temperature, top_p,
        frequency_penalty, presence_penalty, system_prompt, token_price_input, token_price_output,
        rate_limit_rpm, rate_limit_tpm, enabled, priority, description, create_user, create_time,
        update_user, update_time
    </sql>

    <insert id="insert" parameterType="com.haoys.user.domain.entity.AiModelConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_model_config (
            model_type, model_name, api_key, api_url, max_tokens, temperature, top_p,
            frequency_penalty, presence_penalty, system_prompt, token_price_input, token_price_output,
            rate_limit_rpm, rate_limit_tpm, enabled, priority, description, create_user, create_time,
            update_user, update_time
        ) VALUES (
            #{modelType}, #{modelName}, #{apiKey}, #{apiUrl}, #{maxTokens}, #{temperature}, #{topP},
            #{frequencyPenalty}, #{presencePenalty}, #{systemPrompt}, #{tokenPriceInput}, #{tokenPriceOutput},
            #{rateLimitRpm}, #{rateLimitTpm}, #{enabled}, #{priority}, #{description}, #{createUser}, #{createTime},
            #{updateUser}, #{updateTime}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_model_config
        WHERE id = #{id}
    </select>

    <select id="selectByTypeAndName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_model_config
        WHERE model_type = #{modelType} AND model_name = #{modelName}
    </select>

    <select id="selectEnabledConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_model_config
        WHERE enabled = 1
        ORDER BY priority DESC, create_time ASC
    </select>

    <select id="selectByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_model_config
        WHERE model_type = #{modelType}
        <if test="enabled != null">
            AND enabled = #{enabled}
        </if>
        ORDER BY priority DESC, create_time ASC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_model_config
        ORDER BY priority DESC, create_time ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ai_model_config
    </select>

    <update id="updateById" parameterType="com.haoys.user.domain.entity.AiModelConfig">
        UPDATE ai_model_config
        SET model_type = #{modelType},
            model_name = #{modelName},
            api_key = #{apiKey},
            api_url = #{apiUrl},
            max_tokens = #{maxTokens},
            temperature = #{temperature},
            top_p = #{topP},
            frequency_penalty = #{frequencyPenalty},
            presence_penalty = #{presencePenalty},
            system_prompt = #{systemPrompt},
            token_price_input = #{tokenPriceInput},
            token_price_output = #{tokenPriceOutput},
            rate_limit_rpm = #{rateLimitRpm},
            rate_limit_tpm = #{rateLimitTpm},
            enabled = #{enabled},
            priority = #{priority},
            description = #{description},
            update_user = #{updateUser},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <update id="updateEnabled">
        UPDATE ai_model_config
        SET enabled = #{enabled}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_model_config
        WHERE id = #{id}
    </delete>

    <select id="selectBestModel" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_model_config
        WHERE model_type = #{modelType} AND enabled = 1
        ORDER BY priority DESC
        LIMIT 1
    </select>

</mapper>
