<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeFileMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeFile">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="original_name" jdbcType="VARCHAR" property="originalName" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_ext" jdbcType="VARCHAR" property="fileExt" />
    <result column="upload_path" jdbcType="VARCHAR" property="uploadPath" />
    <result column="table_number" jdbcType="BIGINT" property="tableNumber" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="batch_upload" jdbcType="BIT" property="batchUpload" />
    <result column="batch_open_ocr" jdbcType="BIT" property="batchOpenOcr" />
    <result column="upload_config" jdbcType="VARCHAR" property="uploadConfig" />
    <result column="image_type" jdbcType="VARCHAR" property="imageType" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="ext_data_1" jdbcType="VARCHAR" property="extData1" />
    <result column="ext_data_2" jdbcType="VARCHAR" property="extData2" />
    <result column="pre_page_no" jdbcType="BIGINT" property="prePageNo" />
    <result column="file_number" jdbcType="INTEGER" property="fileNumber" />
    <result column="if_montage" jdbcType="BIT" property="ifMontage" />
    <result column="target_file_id" jdbcType="BIGINT" property="targetFileId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="ocr_flag" jdbcType="BIT" property="ocrFlag" />
    <result column="task_date" jdbcType="DATE" property="taskDate" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, testee_id, group_id, group_name, original_name, 
    file_name, file_url, file_ext, upload_path, table_number, resource_id, resource_type, 
    batch_upload, batch_open_ocr, upload_config, image_type, expand, ext_data_1, ext_data_2, 
    pre_page_no, file_number, if_montage, target_file_id, sort, status, ocr_flag, task_date, 
    version, create_time, update_time, create_user, update_user, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeFileExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_testee_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeFileExample">
    delete from project_testee_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeFile">
    insert into project_testee_file (id, project_id, plan_id, 
      visit_id, form_id, testee_id, 
      group_id, group_name, original_name, 
      file_name, file_url, file_ext, 
      upload_path, table_number, resource_id, 
      resource_type, batch_upload, batch_open_ocr, 
      upload_config, image_type, expand, 
      ext_data_1, ext_data_2, pre_page_no, 
      file_number, if_montage, target_file_id, 
      sort, status, ocr_flag, 
      task_date, version, create_time, 
      update_time, create_user, update_user, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, 
      #{groupId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, #{originalName,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{fileExt,jdbcType=VARCHAR}, 
      #{uploadPath,jdbcType=VARCHAR}, #{tableNumber,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, 
      #{resourceType,jdbcType=VARCHAR}, #{batchUpload,jdbcType=BIT}, #{batchOpenOcr,jdbcType=BIT}, 
      #{uploadConfig,jdbcType=VARCHAR}, #{imageType,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, 
      #{extData1,jdbcType=VARCHAR}, #{extData2,jdbcType=VARCHAR}, #{prePageNo,jdbcType=BIGINT}, 
      #{fileNumber,jdbcType=INTEGER}, #{ifMontage,jdbcType=BIT}, #{targetFileId,jdbcType=BIGINT}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{ocrFlag,jdbcType=BIT}, 
      #{taskDate,jdbcType=DATE}, #{version,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeFile">
    insert into project_testee_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="originalName != null">
        original_name,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="fileExt != null">
        file_ext,
      </if>
      <if test="uploadPath != null">
        upload_path,
      </if>
      <if test="tableNumber != null">
        table_number,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="batchUpload != null">
        batch_upload,
      </if>
      <if test="batchOpenOcr != null">
        batch_open_ocr,
      </if>
      <if test="uploadConfig != null">
        upload_config,
      </if>
      <if test="imageType != null">
        image_type,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="extData1 != null">
        ext_data_1,
      </if>
      <if test="extData2 != null">
        ext_data_2,
      </if>
      <if test="prePageNo != null">
        pre_page_no,
      </if>
      <if test="fileNumber != null">
        file_number,
      </if>
      <if test="ifMontage != null">
        if_montage,
      </if>
      <if test="targetFileId != null">
        target_file_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ocrFlag != null">
        ocr_flag,
      </if>
      <if test="taskDate != null">
        task_date,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="originalName != null">
        #{originalName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileExt != null">
        #{fileExt,jdbcType=VARCHAR},
      </if>
      <if test="uploadPath != null">
        #{uploadPath,jdbcType=VARCHAR},
      </if>
      <if test="tableNumber != null">
        #{tableNumber,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="batchUpload != null">
        #{batchUpload,jdbcType=BIT},
      </if>
      <if test="batchOpenOcr != null">
        #{batchOpenOcr,jdbcType=BIT},
      </if>
      <if test="uploadConfig != null">
        #{uploadConfig,jdbcType=VARCHAR},
      </if>
      <if test="imageType != null">
        #{imageType,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="extData1 != null">
        #{extData1,jdbcType=VARCHAR},
      </if>
      <if test="extData2 != null">
        #{extData2,jdbcType=VARCHAR},
      </if>
      <if test="prePageNo != null">
        #{prePageNo,jdbcType=BIGINT},
      </if>
      <if test="fileNumber != null">
        #{fileNumber,jdbcType=INTEGER},
      </if>
      <if test="ifMontage != null">
        #{ifMontage,jdbcType=BIT},
      </if>
      <if test="targetFileId != null">
        #{targetFileId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="ocrFlag != null">
        #{ocrFlag,jdbcType=BIT},
      </if>
      <if test="taskDate != null">
        #{taskDate,jdbcType=DATE},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeFileExample" resultType="java.lang.Long">
    select count(*) from project_testee_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_file
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.originalName != null">
        original_name = #{record.originalName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.fileExt != null">
        file_ext = #{record.fileExt,jdbcType=VARCHAR},
      </if>
      <if test="record.uploadPath != null">
        upload_path = #{record.uploadPath,jdbcType=VARCHAR},
      </if>
      <if test="record.tableNumber != null">
        table_number = #{record.tableNumber,jdbcType=BIGINT},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.batchUpload != null">
        batch_upload = #{record.batchUpload,jdbcType=BIT},
      </if>
      <if test="record.batchOpenOcr != null">
        batch_open_ocr = #{record.batchOpenOcr,jdbcType=BIT},
      </if>
      <if test="record.uploadConfig != null">
        upload_config = #{record.uploadConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.imageType != null">
        image_type = #{record.imageType,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.extData1 != null">
        ext_data_1 = #{record.extData1,jdbcType=VARCHAR},
      </if>
      <if test="record.extData2 != null">
        ext_data_2 = #{record.extData2,jdbcType=VARCHAR},
      </if>
      <if test="record.prePageNo != null">
        pre_page_no = #{record.prePageNo,jdbcType=BIGINT},
      </if>
      <if test="record.fileNumber != null">
        file_number = #{record.fileNumber,jdbcType=INTEGER},
      </if>
      <if test="record.ifMontage != null">
        if_montage = #{record.ifMontage,jdbcType=BIT},
      </if>
      <if test="record.targetFileId != null">
        target_file_id = #{record.targetFileId,jdbcType=BIGINT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.ocrFlag != null">
        ocr_flag = #{record.ocrFlag,jdbcType=BIT},
      </if>
      <if test="record.taskDate != null">
        task_date = #{record.taskDate,jdbcType=DATE},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_file
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      original_name = #{record.originalName,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      file_ext = #{record.fileExt,jdbcType=VARCHAR},
      upload_path = #{record.uploadPath,jdbcType=VARCHAR},
      table_number = #{record.tableNumber,jdbcType=BIGINT},
      resource_id = #{record.resourceId,jdbcType=BIGINT},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      batch_upload = #{record.batchUpload,jdbcType=BIT},
      batch_open_ocr = #{record.batchOpenOcr,jdbcType=BIT},
      upload_config = #{record.uploadConfig,jdbcType=VARCHAR},
      image_type = #{record.imageType,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      ext_data_1 = #{record.extData1,jdbcType=VARCHAR},
      ext_data_2 = #{record.extData2,jdbcType=VARCHAR},
      pre_page_no = #{record.prePageNo,jdbcType=BIGINT},
      file_number = #{record.fileNumber,jdbcType=INTEGER},
      if_montage = #{record.ifMontage,jdbcType=BIT},
      target_file_id = #{record.targetFileId,jdbcType=BIGINT},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      ocr_flag = #{record.ocrFlag,jdbcType=BIT},
      task_date = #{record.taskDate,jdbcType=DATE},
      version = #{record.version,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeFile">
    update project_testee_file
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="originalName != null">
        original_name = #{originalName,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileExt != null">
        file_ext = #{fileExt,jdbcType=VARCHAR},
      </if>
      <if test="uploadPath != null">
        upload_path = #{uploadPath,jdbcType=VARCHAR},
      </if>
      <if test="tableNumber != null">
        table_number = #{tableNumber,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="batchUpload != null">
        batch_upload = #{batchUpload,jdbcType=BIT},
      </if>
      <if test="batchOpenOcr != null">
        batch_open_ocr = #{batchOpenOcr,jdbcType=BIT},
      </if>
      <if test="uploadConfig != null">
        upload_config = #{uploadConfig,jdbcType=VARCHAR},
      </if>
      <if test="imageType != null">
        image_type = #{imageType,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="extData1 != null">
        ext_data_1 = #{extData1,jdbcType=VARCHAR},
      </if>
      <if test="extData2 != null">
        ext_data_2 = #{extData2,jdbcType=VARCHAR},
      </if>
      <if test="prePageNo != null">
        pre_page_no = #{prePageNo,jdbcType=BIGINT},
      </if>
      <if test="fileNumber != null">
        file_number = #{fileNumber,jdbcType=INTEGER},
      </if>
      <if test="ifMontage != null">
        if_montage = #{ifMontage,jdbcType=BIT},
      </if>
      <if test="targetFileId != null">
        target_file_id = #{targetFileId,jdbcType=BIGINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="ocrFlag != null">
        ocr_flag = #{ocrFlag,jdbcType=BIT},
      </if>
      <if test="taskDate != null">
        task_date = #{taskDate,jdbcType=DATE},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeFile">
    update project_testee_file
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      original_name = #{originalName,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_ext = #{fileExt,jdbcType=VARCHAR},
      upload_path = #{uploadPath,jdbcType=VARCHAR},
      table_number = #{tableNumber,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      batch_upload = #{batchUpload,jdbcType=BIT},
      batch_open_ocr = #{batchOpenOcr,jdbcType=BIT},
      upload_config = #{uploadConfig,jdbcType=VARCHAR},
      image_type = #{imageType,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      ext_data_1 = #{extData1,jdbcType=VARCHAR},
      ext_data_2 = #{extData2,jdbcType=VARCHAR},
      pre_page_no = #{prePageNo,jdbcType=BIGINT},
      file_number = #{fileNumber,jdbcType=INTEGER},
      if_montage = #{ifMontage,jdbcType=BIT},
      target_file_id = #{targetFileId,jdbcType=BIGINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      ocr_flag = #{ocrFlag,jdbcType=BIT},
      task_date = #{taskDate,jdbcType=DATE},
      version = #{version,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--查询文件对应的缩略图列表-->
  <select id="getProjectTesteeThumbnailImageByFileId" resultMap="BaseResultMap">
    select * from project_testee_file where 1=1 and project_id = #{projectId} and target_file_id = #{fileId} order by file_number asc
  </select>

  <!--查询文件资源列表-->
  <select id="getProjectTesteeFormImageList" resultMap="BaseResultMap">
    SELECT
      id, project_id, visit_id, form_id, testee_id, group_id, group_name, original_name,
      file_name, file_url, file_ext, upload_path, table_number, resource_id, resource_type,
      batch_upload, batch_open_ocr, upload_config, image_type, expand, ext_data_1, ext_data_2,
      pre_page_no, file_number, if_montage, target_file_id, sort, status, ocr_flag, task_date,
      create_time, update_time, create_user, update_user,
    (CASE `image_type` WHEN 'montage' THEN 1 END) AS 'type1',
    (CASE `image_type` WHEN 'original' THEN 2 END) AS 'type2',
    (CASE `image_type` WHEN 'crop' THEN 3 END) AS 'type3'
    FROM
        project_testee_file
    WHERE project_id = #{projectId}
    <if test="planId != null and planId != ''">
      AND plan_id = #{planId}
    </if>
    <if test="visitId != null and visitId != ''">
      AND visit_id = #{visitId}
    </if>
    <if test="formId != null and formId != ''">
      AND form_id = #{formId}
    </if>
    <if test="resourceId != null and resourceId != ''">
      AND resource_id = #{resourceId}
    </if>
    <if test="rowNumber != null and rowNumber != ''">
      AND table_number = #{rowNumber}
    </if>
    <if test="tableId != null and tableId != ''">
      AND ext_data_1 = #{tableId}
    </if>
    <if test="ocrFlag != null and ocrFlag != ''">
      AND ocr_flag = #{ocrFlag}
    </if>
    <if test="batchUpload != null and batchUpload != ''">
      AND batch_upload = #{batchUpload}
    </if>
    <if test="batchOpenOcr != null and batchOpenOcr != ''">
      AND batch_open_ocr = #{batchOpenOcr}
    </if>
    <if test="taskDate != null and taskDate != ''">
      AND task_date = #{taskDate}
    </if>
    <if test="medicalType != null and medicalType != ''">
      AND resource_type = #{medicalType}
    </if>
    <if test="groupName != null and groupName != ''">
      AND group_name = #{groupName}
    </if>
    <if test="queryIgnoreMontage != null and queryIgnoreMontage != ''">
      AND if_montage = #{queryIgnoreMontage}
    </if>
    AND testee_id = #{testeeId} AND status = #{status}
    ORDER BY  type1 desc, type2 desc, type3 desc,form_id asc,resource_id asc, create_time asc

  </select>

  <select id="getProjectFileListForPage" resultType="com.haoys.user.domain.vo.project.ProjectTesteeFileVo">
    select * from project_testee_file where project_id = #{projectId}
    <if test="searchValue != null and searchValue != ''">
      AND (project_testee_file.file_name LIKE CONCAT('%',#{searchValue},'%') or project_testee_file.file_number = #{searchValue} )
    </if>
    and resource_type = #{resourceType} and project_testee_file.status = '0'
    order by ${sortField} ${sortType}
  </select>

  <select id="checkFileRecordRepeat" resultMap="BaseResultMap">
    select * from project_testee_file where
    project_id = #{projectId} and file_name = #{fileName}
    and file_number = #{fileNumber} and version = #{version}<!-- and create_user = #{createUserId}-->
    and resource_type = 'project_file' and status = '0' limit 1
  </select>


</mapper>