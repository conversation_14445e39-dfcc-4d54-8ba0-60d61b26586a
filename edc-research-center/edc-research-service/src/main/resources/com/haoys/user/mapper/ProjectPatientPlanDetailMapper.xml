<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectPatientPlanDetailMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectPatientPlanDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="if_visit_window" jdbcType="BIT" property="ifVisitWindow" />
    <result column="start_visit_value" jdbcType="INTEGER" property="startVisitValue" />
    <result column="end_visit_value" jdbcType="INTEGER" property="endVisitValue" />
    <result column="send_rate" jdbcType="VARCHAR" property="sendRate" />
    <result column="base_visit_id" jdbcType="BIGINT" property="baseVisitId" />
    <result column="base_form_id" jdbcType="BIGINT" property="baseFormId" />
    <result column="base_variable_id" jdbcType="BIGINT" property="baseVariableId" />
    <result column="base_visit_value" jdbcType="TIMESTAMP" property="baseVisitValue" />
    <result column="base_visit_interval" jdbcType="INTEGER" property="baseVisitInterval" />
    <result column="day_send_rate" jdbcType="INTEGER" property="daySendRate" />
    <result column="ext_data_01" jdbcType="VARCHAR" property="extData01" />
    <result column="ext_data_02" jdbcType="VARCHAR" property="extData02" />
    <result column="ext_data_03" jdbcType="VARCHAR" property="extData03" />
    <result column="ext_data_04" jdbcType="VARCHAR" property="extData04" />
    <result column="ext_data_05" jdbcType="VARCHAR" property="extData05" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, task_id, if_visit_window, start_visit_value, end_visit_value, 
    send_rate, base_visit_id, base_form_id, base_variable_id, base_visit_value, base_visit_interval, 
    day_send_rate, ext_data_01, ext_data_02, ext_data_03, ext_data_04, ext_data_05, status, 
    sort, create_user, create_time, update_user, update_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectPatientPlanDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_patient_plan_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_patient_plan_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_patient_plan_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectPatientPlanDetailExample">
    delete from project_patient_plan_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectPatientPlanDetail">
    insert into project_patient_plan_detail (id, project_id, plan_id, 
      task_id, if_visit_window, start_visit_value, 
      end_visit_value, send_rate, base_visit_id, 
      base_form_id, base_variable_id, base_visit_value, 
      base_visit_interval, day_send_rate, ext_data_01, 
      ext_data_02, ext_data_03, ext_data_04, 
      ext_data_05, status, sort, 
      create_user, create_time, update_user, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{taskId,jdbcType=BIGINT}, #{ifVisitWindow,jdbcType=BIT}, #{startVisitValue,jdbcType=INTEGER}, 
      #{endVisitValue,jdbcType=INTEGER}, #{sendRate,jdbcType=VARCHAR}, #{baseVisitId,jdbcType=BIGINT}, 
      #{baseFormId,jdbcType=BIGINT}, #{baseVariableId,jdbcType=BIGINT}, #{baseVisitValue,jdbcType=TIMESTAMP}, 
      #{baseVisitInterval,jdbcType=INTEGER}, #{daySendRate,jdbcType=INTEGER}, #{extData01,jdbcType=VARCHAR}, 
      #{extData02,jdbcType=VARCHAR}, #{extData03,jdbcType=VARCHAR}, #{extData04,jdbcType=VARCHAR}, 
      #{extData05,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, 
      #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectPatientPlanDetail">
    insert into project_patient_plan_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="ifVisitWindow != null">
        if_visit_window,
      </if>
      <if test="startVisitValue != null">
        start_visit_value,
      </if>
      <if test="endVisitValue != null">
        end_visit_value,
      </if>
      <if test="sendRate != null">
        send_rate,
      </if>
      <if test="baseVisitId != null">
        base_visit_id,
      </if>
      <if test="baseFormId != null">
        base_form_id,
      </if>
      <if test="baseVariableId != null">
        base_variable_id,
      </if>
      <if test="baseVisitValue != null">
        base_visit_value,
      </if>
      <if test="baseVisitInterval != null">
        base_visit_interval,
      </if>
      <if test="daySendRate != null">
        day_send_rate,
      </if>
      <if test="extData01 != null">
        ext_data_01,
      </if>
      <if test="extData02 != null">
        ext_data_02,
      </if>
      <if test="extData03 != null">
        ext_data_03,
      </if>
      <if test="extData04 != null">
        ext_data_04,
      </if>
      <if test="extData05 != null">
        ext_data_05,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="ifVisitWindow != null">
        #{ifVisitWindow,jdbcType=BIT},
      </if>
      <if test="startVisitValue != null">
        #{startVisitValue,jdbcType=INTEGER},
      </if>
      <if test="endVisitValue != null">
        #{endVisitValue,jdbcType=INTEGER},
      </if>
      <if test="sendRate != null">
        #{sendRate,jdbcType=VARCHAR},
      </if>
      <if test="baseVisitId != null">
        #{baseVisitId,jdbcType=BIGINT},
      </if>
      <if test="baseFormId != null">
        #{baseFormId,jdbcType=BIGINT},
      </if>
      <if test="baseVariableId != null">
        #{baseVariableId,jdbcType=BIGINT},
      </if>
      <if test="baseVisitValue != null">
        #{baseVisitValue,jdbcType=TIMESTAMP},
      </if>
      <if test="baseVisitInterval != null">
        #{baseVisitInterval,jdbcType=INTEGER},
      </if>
      <if test="daySendRate != null">
        #{daySendRate,jdbcType=INTEGER},
      </if>
      <if test="extData01 != null">
        #{extData01,jdbcType=VARCHAR},
      </if>
      <if test="extData02 != null">
        #{extData02,jdbcType=VARCHAR},
      </if>
      <if test="extData03 != null">
        #{extData03,jdbcType=VARCHAR},
      </if>
      <if test="extData04 != null">
        #{extData04,jdbcType=VARCHAR},
      </if>
      <if test="extData05 != null">
        #{extData05,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectPatientPlanDetailExample" resultType="java.lang.Long">
    select count(*) from project_patient_plan_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_patient_plan_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.ifVisitWindow != null">
        if_visit_window = #{record.ifVisitWindow,jdbcType=BIT},
      </if>
      <if test="record.startVisitValue != null">
        start_visit_value = #{record.startVisitValue,jdbcType=INTEGER},
      </if>
      <if test="record.endVisitValue != null">
        end_visit_value = #{record.endVisitValue,jdbcType=INTEGER},
      </if>
      <if test="record.sendRate != null">
        send_rate = #{record.sendRate,jdbcType=VARCHAR},
      </if>
      <if test="record.baseVisitId != null">
        base_visit_id = #{record.baseVisitId,jdbcType=BIGINT},
      </if>
      <if test="record.baseFormId != null">
        base_form_id = #{record.baseFormId,jdbcType=BIGINT},
      </if>
      <if test="record.baseVariableId != null">
        base_variable_id = #{record.baseVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.baseVisitValue != null">
        base_visit_value = #{record.baseVisitValue,jdbcType=TIMESTAMP},
      </if>
      <if test="record.baseVisitInterval != null">
        base_visit_interval = #{record.baseVisitInterval,jdbcType=INTEGER},
      </if>
      <if test="record.daySendRate != null">
        day_send_rate = #{record.daySendRate,jdbcType=INTEGER},
      </if>
      <if test="record.extData01 != null">
        ext_data_01 = #{record.extData01,jdbcType=VARCHAR},
      </if>
      <if test="record.extData02 != null">
        ext_data_02 = #{record.extData02,jdbcType=VARCHAR},
      </if>
      <if test="record.extData03 != null">
        ext_data_03 = #{record.extData03,jdbcType=VARCHAR},
      </if>
      <if test="record.extData04 != null">
        ext_data_04 = #{record.extData04,jdbcType=VARCHAR},
      </if>
      <if test="record.extData05 != null">
        ext_data_05 = #{record.extData05,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_patient_plan_detail
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      if_visit_window = #{record.ifVisitWindow,jdbcType=BIT},
      start_visit_value = #{record.startVisitValue,jdbcType=INTEGER},
      end_visit_value = #{record.endVisitValue,jdbcType=INTEGER},
      send_rate = #{record.sendRate,jdbcType=VARCHAR},
      base_visit_id = #{record.baseVisitId,jdbcType=BIGINT},
      base_form_id = #{record.baseFormId,jdbcType=BIGINT},
      base_variable_id = #{record.baseVariableId,jdbcType=BIGINT},
      base_visit_value = #{record.baseVisitValue,jdbcType=TIMESTAMP},
      base_visit_interval = #{record.baseVisitInterval,jdbcType=INTEGER},
      day_send_rate = #{record.daySendRate,jdbcType=INTEGER},
      ext_data_01 = #{record.extData01,jdbcType=VARCHAR},
      ext_data_02 = #{record.extData02,jdbcType=VARCHAR},
      ext_data_03 = #{record.extData03,jdbcType=VARCHAR},
      ext_data_04 = #{record.extData04,jdbcType=VARCHAR},
      ext_data_05 = #{record.extData05,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectPatientPlanDetail">
    update project_patient_plan_detail
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="ifVisitWindow != null">
        if_visit_window = #{ifVisitWindow,jdbcType=BIT},
      </if>
      <if test="startVisitValue != null">
        start_visit_value = #{startVisitValue,jdbcType=INTEGER},
      </if>
      <if test="endVisitValue != null">
        end_visit_value = #{endVisitValue,jdbcType=INTEGER},
      </if>
      <if test="sendRate != null">
        send_rate = #{sendRate,jdbcType=VARCHAR},
      </if>
      <if test="baseVisitId != null">
        base_visit_id = #{baseVisitId,jdbcType=BIGINT},
      </if>
      <if test="baseFormId != null">
        base_form_id = #{baseFormId,jdbcType=BIGINT},
      </if>
      <if test="baseVariableId != null">
        base_variable_id = #{baseVariableId,jdbcType=BIGINT},
      </if>
      <if test="baseVisitValue != null">
        base_visit_value = #{baseVisitValue,jdbcType=TIMESTAMP},
      </if>
      <if test="baseVisitInterval != null">
        base_visit_interval = #{baseVisitInterval,jdbcType=INTEGER},
      </if>
      <if test="daySendRate != null">
        day_send_rate = #{daySendRate,jdbcType=INTEGER},
      </if>
      <if test="extData01 != null">
        ext_data_01 = #{extData01,jdbcType=VARCHAR},
      </if>
      <if test="extData02 != null">
        ext_data_02 = #{extData02,jdbcType=VARCHAR},
      </if>
      <if test="extData03 != null">
        ext_data_03 = #{extData03,jdbcType=VARCHAR},
      </if>
      <if test="extData04 != null">
        ext_data_04 = #{extData04,jdbcType=VARCHAR},
      </if>
      <if test="extData05 != null">
        ext_data_05 = #{extData05,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectPatientPlanDetail">
    update project_patient_plan_detail
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      task_id = #{taskId,jdbcType=BIGINT},
      if_visit_window = #{ifVisitWindow,jdbcType=BIT},
      start_visit_value = #{startVisitValue,jdbcType=INTEGER},
      end_visit_value = #{endVisitValue,jdbcType=INTEGER},
      send_rate = #{sendRate,jdbcType=VARCHAR},
      base_visit_id = #{baseVisitId,jdbcType=BIGINT},
      base_form_id = #{baseFormId,jdbcType=BIGINT},
      base_variable_id = #{baseVariableId,jdbcType=BIGINT},
      base_visit_value = #{baseVisitValue,jdbcType=TIMESTAMP},
      base_visit_interval = #{baseVisitInterval,jdbcType=INTEGER},
      day_send_rate = #{daySendRate,jdbcType=INTEGER},
      ext_data_01 = #{extData01,jdbcType=VARCHAR},
      ext_data_02 = #{extData02,jdbcType=VARCHAR},
      ext_data_03 = #{extData03,jdbcType=VARCHAR},
      ext_data_04 = #{extData04,jdbcType=VARCHAR},
      ext_data_05 = #{extData05,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>