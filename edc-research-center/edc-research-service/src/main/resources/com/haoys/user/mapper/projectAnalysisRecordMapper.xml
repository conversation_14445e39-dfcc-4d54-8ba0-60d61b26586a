<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectAnalysisRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectAnalysisRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="sample_id" jdbcType="BIGINT" property="sampleId" />
    <result column="sample_name" jdbcType="VARCHAR" property="sampleName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch_code, sample_id, sample_name, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectAnalysisRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_analysis_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_analysis_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_analysis_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectAnalysisRecordExample">
    delete from project_analysis_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectAnalysisRecord">
    insert into project_analysis_record (id, batch_code, sample_id, 
      sample_name, create_time)
    values (#{id,jdbcType=BIGINT}, #{batchCode,jdbcType=VARCHAR}, #{sampleId,jdbcType=BIGINT}, 
      #{sampleName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectAnalysisRecord">
    insert into project_analysis_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="sampleId != null">
        sample_id,
      </if>
      <if test="sampleName != null">
        sample_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null">
        #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="sampleName != null">
        #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectAnalysisRecordExample" resultType="java.lang.Long">
    select count(*) from project_analysis_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <update id="updateByExampleSelective" parameterType="map">
    update project_analysis_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sampleId != null">
        sample_id = #{record.sampleId,jdbcType=BIGINT},
      </if>
      <if test="record.sampleName != null">
        sample_name = #{record.sampleName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_analysis_record
    set id = #{record.id,jdbcType=BIGINT},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      sample_id = #{record.sampleId,jdbcType=BIGINT},
      sample_name = #{record.sampleName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectAnalysisRecord">
    update project_analysis_record
    <set>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="sampleId != null">
        sample_id = #{sampleId,jdbcType=BIGINT},
      </if>
      <if test="sampleName != null">
        sample_name = #{sampleName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectAnalysisRecord">
    update project_analysis_record
    set batch_code = #{batchCode,jdbcType=VARCHAR},
      sample_id = #{sampleId,jdbcType=BIGINT},
      sample_name = #{sampleName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getProjectAnalysisRecordForPage" resultType="com.haoys.user.domain.vo.ProjectAnalysisRecordVo">
    SELECT
        project_prescription.sample_id sampleId,project_prescription.patient_name patientName,
        project_prescription.age, project_prescription.diagnostic_desc diagnosticDesc,project_prescription.content
    FROM project_analysis_code
    INNER JOIN project_analysis_record ON project_analysis_code.batch_code = project_analysis_record.batch_code
    INNER JOIN project_prescription ON project_prescription.sample_id = project_analysis_record.sample_id
    WHERE project_analysis_code.batch_code = #{batchCode}
  </select>

  <select id="queryAgeLineAnalysis" resultType="java.util.Map">
        SELECT
          project_prescription.age,
          count(project_prescription.age) count
      FROM
          project_analysis_code
          INNER JOIN project_analysis_record ON project_analysis_code.batch_code = project_analysis_record.batch_code
          INNER JOIN project_prescription ON project_prescription.sample_id = project_analysis_record.sample_id
      WHERE
          project_analysis_code.batch_code = #{batchCode} GROUP BY project_prescription.age ORDER BY project_prescription.age

  </select>

</mapper>