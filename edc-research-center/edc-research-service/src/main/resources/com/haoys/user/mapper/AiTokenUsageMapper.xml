<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.AiTokenUsageMapper">

    <resultMap id="BaseResultMap" type="com.haoys.user.domain.entity.AiTokenUsage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="input_tokens" jdbcType="INTEGER" property="inputTokens"/>
        <result column="output_tokens" jdbcType="INTEGER" property="outputTokens"/>
        <result column="total_tokens" jdbcType="INTEGER" property="totalTokens"/>
        <result column="input_cost" jdbcType="DECIMAL" property="inputCost"/>
        <result column="output_cost" jdbcType="DECIMAL" property="outputCost"/>
        <result column="total_cost" jdbcType="DECIMAL" property="totalCost"/>
        <result column="usage_date" jdbcType="DATE" property="usageDate"/>
        <result column="usage_hour" jdbcType="TINYINT" property="usageHour"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, user_name, model_type, model_name, session_id, message_id,
        input_tokens, output_tokens, total_tokens, input_cost, output_cost, total_cost,
        usage_date, usage_hour, create_time
    </sql>

    <insert id="insert" parameterType="com.haoys.user.domain.entity.AiTokenUsage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_token_usage (
            user_id, user_name, model_type, model_name, session_id, message_id,
            input_tokens, output_tokens, total_tokens, input_cost, output_cost, total_cost,
            usage_date, usage_hour, create_time
        ) VALUES (
            #{userId}, #{userName}, #{modelType}, #{modelName}, #{sessionId}, #{messageId},
            #{inputTokens}, #{outputTokens}, #{totalTokens}, #{inputCost}, #{outputCost}, #{totalCost},
            #{usageDate}, #{usageHour}, #{createTime}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_token_usage
        WHERE id = #{id}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_token_usage
        WHERE user_id = #{userId}
        <if test="startDate != null and endDate != null">
            AND usage_date BETWEEN #{startDate} AND #{endDate}
        </if>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="selectUserUsageStats" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(input_tokens), 0) as inputTokens,
            COALESCE(SUM(output_tokens), 0) as outputTokens,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(input_cost), 0) as inputCost,
            COALESCE(SUM(output_cost), 0) as outputCost,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        WHERE user_id = #{userId}
        <if test="startDate != null and endDate != null">
            AND usage_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <select id="selectUserTodayUsage" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(input_tokens), 0) as inputTokens,
            COALESCE(SUM(output_tokens), 0) as outputTokens,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(input_cost), 0) as inputCost,
            COALESCE(SUM(output_cost), 0) as outputCost,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        WHERE user_id = #{userId}
        AND usage_date = CURDATE()
    </select>

    <select id="selectUserMonthUsage" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(input_tokens), 0) as inputTokens,
            COALESCE(SUM(output_tokens), 0) as outputTokens,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(input_cost), 0) as inputCost,
            COALESCE(SUM(output_cost), 0) as outputCost,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        WHERE user_id = #{userId}
        AND YEAR(usage_date) = YEAR(CURDATE())
        AND MONTH(usage_date) = MONTH(CURDATE())
    </select>

    <select id="selectUsageByModel" resultType="java.util.Map">
        SELECT
            model_type as modelType,
            model_name as modelName,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        <if test="startDate != null and endDate != null">
            WHERE usage_date BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY model_type, model_name
        ORDER BY totalTokens DESC
    </select>

    <select id="selectDailyUsageTrend" resultType="java.util.Map">
        SELECT
            usage_date as date,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        WHERE user_id = #{userId}
        AND usage_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY usage_date
        ORDER BY usage_date ASC
    </select>

    <select id="selectHourlyUsageDistribution" resultType="java.util.Map">
        SELECT
            usage_hour as hour,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        WHERE user_id = #{userId}
        AND usage_date = #{date}
        GROUP BY usage_hour
        ORDER BY usage_hour ASC
    </select>

    <select id="selectUserDailyCost" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_cost), 0)
        FROM ai_token_usage
        WHERE user_id = #{userId}
        AND usage_date = #{date}
    </select>

    <select id="selectUserMonthlyCost" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_cost), 0)
        FROM ai_token_usage
        WHERE user_id = #{userId}
        AND YEAR(usage_date) = #{year}
        AND MONTH(usage_date) = #{month}
    </select>

    <select id="selectSystemUsageStats" resultType="java.util.Map">
        SELECT
            COALESCE(SUM(input_tokens), 0) as inputTokens,
            COALESCE(SUM(output_tokens), 0) as outputTokens,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(input_cost), 0) as inputCost,
            COALESCE(SUM(output_cost), 0) as outputCost,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount,
            COUNT(DISTINCT user_id) as userCount
        FROM ai_token_usage
        <if test="startDate != null and endDate != null">
            WHERE usage_date BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>

    <select id="selectTopUsers" resultType="java.util.Map">
        SELECT
            user_id as userId,
            user_name as userName,
            COALESCE(SUM(total_tokens), 0) as totalTokens,
            COALESCE(SUM(total_cost), 0) as totalCost,
            COUNT(*) as requestCount
        FROM ai_token_usage
        <if test="startDate != null and endDate != null">
            WHERE usage_date BETWEEN #{startDate} AND #{endDate}
        </if>
        GROUP BY user_id, user_name
        ORDER BY totalTokens DESC
        LIMIT #{limit}
    </select>

</mapper>
