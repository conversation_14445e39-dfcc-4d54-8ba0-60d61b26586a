<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateGroupLableMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateGroupLable">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="lable_name" jdbcType="VARCHAR" property="lableName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="leaf_node" jdbcType="BIT" property="leafNode" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, lable_name, parent_id, resource_type, code, leaf_node, 
    sort, status, create_time, create_user, update_time, update_user, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateGroupLableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_group_lable
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_group_lable
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_group_lable
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateGroupLableExample">
    delete from template_group_lable
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateGroupLable">
    insert into template_group_lable (id, project_id, plan_id, 
      visit_id, lable_name, parent_id, 
      resource_type, code, leaf_node, 
      sort, status, create_time, 
      create_user, update_time, update_user, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{lableName,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, 
      #{resourceType,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{leafNode,jdbcType=BIT}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateGroupLable">
    insert into template_group_lable
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="lableName != null">
        lable_name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="leafNode != null">
        leaf_node,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="lableName != null">
        #{lableName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="leafNode != null">
        #{leafNode,jdbcType=BIT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateGroupLableExample" resultType="java.lang.Long">
    select count(*) from template_group_lable
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_group_lable
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.lableName != null">
        lable_name = #{record.lableName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.leafNode != null">
        leaf_node = #{record.leafNode,jdbcType=BIT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_group_lable
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      lable_name = #{record.lableName,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      leaf_node = #{record.leafNode,jdbcType=BIT},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateGroupLable">
    update template_group_lable
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="lableName != null">
        lable_name = #{lableName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="leafNode != null">
        leaf_node = #{leafNode,jdbcType=BIT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateGroupLable">
    update template_group_lable
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      lable_name = #{lableName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      leaf_node = #{leafNode,jdbcType=BIT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>