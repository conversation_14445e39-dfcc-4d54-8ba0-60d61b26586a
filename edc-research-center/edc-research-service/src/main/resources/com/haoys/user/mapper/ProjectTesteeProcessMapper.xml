<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeProcessMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeProcess">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_expand_id" jdbcType="BIGINT" property="formExpandId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="owner_org_id" jdbcType="VARCHAR" property="ownerOrgId" />
    <result column="complate_status" jdbcType="VARCHAR" property="complateStatus" />
    <result column="changle_count" jdbcType="INTEGER" property="changleCount" />
    <result column="form_complate_status" jdbcType="VARCHAR" property="formComplateStatus" />
    <result column="table_complate_status" jdbcType="VARCHAR" property="tableComplateStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, form_expand_id, testee_id, owner_org_id, 
    complate_status, changle_count, form_complate_status, table_complate_status, create_time, 
    update_time, create_user_id, update_user_id, status, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeProcessExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_testee_process
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_process
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeProcessExample">
    delete from project_testee_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeProcess">
    insert into project_testee_process (id, project_id, plan_id, 
      visit_id, form_id, form_expand_id, 
      testee_id, owner_org_id, complate_status, 
      changle_count, form_complate_status, table_complate_status, 
      create_time, update_time, create_user_id, 
      update_user_id, status, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formExpandId,jdbcType=BIGINT}, 
      #{testeeId,jdbcType=BIGINT}, #{ownerOrgId,jdbcType=VARCHAR}, #{complateStatus,jdbcType=VARCHAR}, 
      #{changleCount,jdbcType=INTEGER}, #{formComplateStatus,jdbcType=VARCHAR}, #{tableComplateStatus,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{updateUserId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeProcess">
    insert into project_testee_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formExpandId != null">
        form_expand_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="ownerOrgId != null">
        owner_org_id,
      </if>
      <if test="complateStatus != null">
        complate_status,
      </if>
      <if test="changleCount != null">
        changle_count,
      </if>
      <if test="formComplateStatus != null">
        form_complate_status,
      </if>
      <if test="tableComplateStatus != null">
        table_complate_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formExpandId != null">
        #{formExpandId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="ownerOrgId != null">
        #{ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="complateStatus != null">
        #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="changleCount != null">
        #{changleCount,jdbcType=INTEGER},
      </if>
      <if test="formComplateStatus != null">
        #{formComplateStatus,jdbcType=VARCHAR},
      </if>
      <if test="tableComplateStatus != null">
        #{tableComplateStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeProcessExample" resultType="java.lang.Long">
    select count(*) from project_testee_process
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_process
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formExpandId != null">
        form_expand_id = #{record.formExpandId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.ownerOrgId != null">
        owner_org_id = #{record.ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.complateStatus != null">
        complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.changleCount != null">
        changle_count = #{record.changleCount,jdbcType=INTEGER},
      </if>
      <if test="record.formComplateStatus != null">
        form_complate_status = #{record.formComplateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.tableComplateStatus != null">
        table_complate_status = #{record.tableComplateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_process
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_expand_id = #{record.formExpandId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      owner_org_id = #{record.ownerOrgId,jdbcType=VARCHAR},
      complate_status = #{record.complateStatus,jdbcType=VARCHAR},
      changle_count = #{record.changleCount,jdbcType=INTEGER},
      form_complate_status = #{record.formComplateStatus,jdbcType=VARCHAR},
      table_complate_status = #{record.tableComplateStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeProcess">
    update project_testee_process
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formExpandId != null">
        form_expand_id = #{formExpandId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="ownerOrgId != null">
        owner_org_id = #{ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="complateStatus != null">
        complate_status = #{complateStatus,jdbcType=VARCHAR},
      </if>
      <if test="changleCount != null">
        changle_count = #{changleCount,jdbcType=INTEGER},
      </if>
      <if test="formComplateStatus != null">
        form_complate_status = #{formComplateStatus,jdbcType=VARCHAR},
      </if>
      <if test="tableComplateStatus != null">
        table_complate_status = #{tableComplateStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeProcess">
    update project_testee_process
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_expand_id = #{formExpandId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      owner_org_id = #{ownerOrgId,jdbcType=VARCHAR},
      complate_status = #{complateStatus,jdbcType=VARCHAR},
      changle_count = #{changleCount,jdbcType=INTEGER},
      form_complate_status = #{formComplateStatus,jdbcType=VARCHAR},
      table_complate_status = #{tableComplateStatus,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--查询参与者表单录入状态-->
  <select id="getProjectTesteeFormProcess" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from project_testee_process
    where project_id = #{projectId}
    and owner_org_id = #{projectOrgId}
    and plan_id = #{planId}
    and visit_id = #{visitId}
    and form_id = #{formId}
    <if test="formExpandId != null and formExpandId !=''">
      and form_expand_id = #{formExpandId}
    </if>
    and testee_id = #{testeeId}
  </select>

</mapper>