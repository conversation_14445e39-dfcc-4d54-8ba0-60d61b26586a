<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemRequestLogMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemRequestLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_name" jdbcType="VARCHAR" property="requestName" />
    <result column="request_method" jdbcType="VARCHAR" property="requestMethod" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="project_record_log" jdbcType="BIT" property="projectRecordLog" />
    <result column="request_ip" jdbcType="VARCHAR" property="requestIp" />
    <result column="operator_type" jdbcType="VARCHAR" property="operatorType" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="request_param" jdbcType="VARCHAR" property="requestParam" />
    <result column="response_result" jdbcType="VARCHAR" property="responseResult" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, request_name, request_method, method_name, request_url, project_record_log, request_ip, 
    operator_type, business_type, user_name, real_name, location, status, data_from, 
    request_param, response_result, error_message, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemRequestLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_request_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_request_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemRequestLogExample">
    delete from system_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemRequestLog">
    insert into system_request_log (id, request_name, request_method, 
      method_name, request_url, project_record_log, 
      request_ip, operator_type, business_type, 
      user_name, real_name, location, 
      status, data_from, request_param, 
      response_result, error_message, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{requestName,jdbcType=VARCHAR}, #{requestMethod,jdbcType=VARCHAR}, 
      #{methodName,jdbcType=VARCHAR}, #{requestUrl,jdbcType=VARCHAR}, #{projectRecordLog,jdbcType=BIT}, 
      #{requestIp,jdbcType=VARCHAR}, #{operatorType,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{userName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{dataFrom,jdbcType=VARCHAR}, #{requestParam,jdbcType=VARCHAR}, 
      #{responseResult,jdbcType=VARCHAR}, #{errorMessage,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemRequestLog">
    insert into system_request_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="requestName != null">
        request_name,
      </if>
      <if test="requestMethod != null">
        request_method,
      </if>
      <if test="methodName != null">
        method_name,
      </if>
      <if test="requestUrl != null">
        request_url,
      </if>
      <if test="projectRecordLog != null">
        project_record_log,
      </if>
      <if test="requestIp != null">
        request_ip,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="requestParam != null">
        request_param,
      </if>
      <if test="responseResult != null">
        response_result,
      </if>
      <if test="errorMessage != null">
        error_message,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="requestName != null">
        #{requestName,jdbcType=VARCHAR},
      </if>
      <if test="requestMethod != null">
        #{requestMethod,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectRecordLog != null">
        #{projectRecordLog,jdbcType=BIT},
      </if>
      <if test="requestIp != null">
        #{requestIp,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="requestParam != null">
        #{requestParam,jdbcType=VARCHAR},
      </if>
      <if test="responseResult != null">
        #{responseResult,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemRequestLogExample" resultType="java.lang.Long">
    select count(*) from system_request_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_request_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.requestName != null">
        request_name = #{record.requestName,jdbcType=VARCHAR},
      </if>
      <if test="record.requestMethod != null">
        request_method = #{record.requestMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.methodName != null">
        method_name = #{record.methodName,jdbcType=VARCHAR},
      </if>
      <if test="record.requestUrl != null">
        request_url = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.projectRecordLog != null">
        project_record_log = #{record.projectRecordLog,jdbcType=BIT},
      </if>
      <if test="record.requestIp != null">
        request_ip = #{record.requestIp,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorType != null">
        operator_type = #{record.operatorType,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.realName != null">
        real_name = #{record.realName,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.requestParam != null">
        request_param = #{record.requestParam,jdbcType=VARCHAR},
      </if>
      <if test="record.responseResult != null">
        response_result = #{record.responseResult,jdbcType=VARCHAR},
      </if>
      <if test="record.errorMessage != null">
        error_message = #{record.errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_request_log
    set id = #{record.id,jdbcType=BIGINT},
      request_name = #{record.requestName,jdbcType=VARCHAR},
      request_method = #{record.requestMethod,jdbcType=VARCHAR},
      method_name = #{record.methodName,jdbcType=VARCHAR},
      request_url = #{record.requestUrl,jdbcType=VARCHAR},
      project_record_log = #{record.projectRecordLog,jdbcType=BIT},
      request_ip = #{record.requestIp,jdbcType=VARCHAR},
      operator_type = #{record.operatorType,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      real_name = #{record.realName,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      request_param = #{record.requestParam,jdbcType=VARCHAR},
      response_result = #{record.responseResult,jdbcType=VARCHAR},
      error_message = #{record.errorMessage,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemRequestLog">
    update system_request_log
    <set>
      <if test="requestName != null">
        request_name = #{requestName,jdbcType=VARCHAR},
      </if>
      <if test="requestMethod != null">
        request_method = #{requestMethod,jdbcType=VARCHAR},
      </if>
      <if test="methodName != null">
        method_name = #{methodName,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        request_url = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="projectRecordLog != null">
        project_record_log = #{projectRecordLog,jdbcType=BIT},
      </if>
      <if test="requestIp != null">
        request_ip = #{requestIp,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="requestParam != null">
        request_param = #{requestParam,jdbcType=VARCHAR},
      </if>
      <if test="responseResult != null">
        response_result = #{responseResult,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        error_message = #{errorMessage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemRequestLog">
    update system_request_log
    set request_name = #{requestName,jdbcType=VARCHAR},
      request_method = #{requestMethod,jdbcType=VARCHAR},
      method_name = #{methodName,jdbcType=VARCHAR},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      project_record_log = #{projectRecordLog,jdbcType=BIT},
      request_ip = #{requestIp,jdbcType=VARCHAR},
      operator_type = #{operatorType,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      real_name = #{realName,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      request_param = #{requestParam,jdbcType=VARCHAR},
      response_result = #{responseResult,jdbcType=VARCHAR},
      error_message = #{errorMessage,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>


   <!--分页查询系统操作日志列表-->
  <select id="selectRequestLogListForPage" parameterType="com.haoys.user.domain.entity.SystemRequestLogQuery" resultType="com.haoys.user.domain.entity.SystemRequestLogQuery">
    select
      system_request_log.*, code, name
      from system_request_log
    LEFT JOIN
    (
      select id,trim(both '"' from request_param -> '$.code') code, trim(both '"' from request_param -> '$.name') name from system_request_log WHERE request_name = '保存编辑项目'
      UNION ALL
      SELECT system_request_log.id,project.code, project.name
      FROM project
      INNER JOIN system_request_log on request_name = '删除项目' and project.id = system_request_log.request_param
    )a ON system_request_log.id = a.id
      <where>
          <if test="userName != null and userName != ''">
              AND
            (
            real_name like concat('%', #{userName}, '%')
              or
            user_name like concat('%', #{userName}, '%')
            )

          </if>
          <if test="businessType != null and businessType != ''">
              AND business_type = #{businessType}
          </if>
          <if test="businessTypes != null and businessTypes.length > 0">
              AND business_type in
              <foreach collection="businessTypes" item="businessType" open="(" separator="," close=")">
                  #{businessType}
              </foreach>
          </if>
          <if test="status != null">
              AND status = #{status}
          </if>

        <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
          and date_format(create_time,'%Y-%m-%d') between date_format(#{beginTime},'%Y-%m-%d') and date_format(#{endTime},'%Y-%m-%d')
        </if>
        <if test="code != null and code != ''">
          AND code like concat('%', #{code}, '%')
        </if>
        <if test="name != null and name != ''">
          AND name like concat('%', #{name}, '%')
        </if>
      </where>
      order by id desc
  </select>

  <delete id="deleteOperLogByIds" parameterType="Long">
      delete from system_request_log where id in
      <foreach collection="array" item="operId" open="(" separator="," close=")">
          #{operId}
      </foreach>
  </delete>

  <update id="clearSystemRequestLog">
      truncate table system_request_log
  </update>

  <select id="selectSystemRequestLogList" parameterType="com.haoys.user.domain.entity.SystemRequestLogQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from system_request_log
    <where>
      <if test="userName != null and userName != ''">
        AND
        (
        real_name like concat('%', #{userName}, '%')
        or
        user_name like concat('%', #{userName}, '%')
        )

      </if>
      <if test="businessType != null and businessType != ''">
        AND business_type = #{businessType}
      </if>
      <if test="businessTypes != null and businessTypes.length > 0">
        AND business_type in
        <foreach collection="businessTypes" item="businessType" open="(" separator="," close=")">
          #{businessType}
        </foreach>
      </if>
      <if test="status != null">
        AND status = #{status}
      </if>

      <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
        and date_format(create_time,'%Y-%m-%d %H:%T:%s') &gt;= date_format(#{beginTime},'%Y-%m-%d %H:%T:%s')
      </if>
      <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
        and date_format(create_time,'%Y-%m-%d %H:%T:%s') &lt;= date_format(#{endTime},'%Y-%m-%d %H:%T:%s')
      </if>

    </where>
    order by id desc
  </select>

</mapper>