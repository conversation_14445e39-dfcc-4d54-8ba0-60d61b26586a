<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectDictionaryMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectDictionary">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="option_value" jdbcType="VARCHAR" property="optionValue" />
    <result column="score_value" jdbcType="DECIMAL" property="scoreValue" />
    <result column="en_name" jdbcType="VARCHAR" property="enName" />
    <result column="unit_value" jdbcType="VARCHAR" property="unitValue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="dic_type" jdbcType="VARCHAR" property="dicType" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, parent_id, code, name, option_value, score_value, en_name, unit_value, 
    description, dic_type, data_from, sort, status, create_user_id, create_time, update_user_id, 
    update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectDictionaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_dictionary
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from project_dictionary
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectDictionaryExample">
    delete from project_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectDictionary">
    insert into project_dictionary (id, project_id, parent_id, 
      code, name, option_value, 
      score_value, en_name, unit_value, 
      description, dic_type, data_from, 
      sort, status, create_user_id, 
      create_time, update_user_id, update_time, 
      tenant_id, platform_id)
    values (#{id,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT}, #{parentId,jdbcType=VARCHAR}, 
      #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{optionValue,jdbcType=VARCHAR}, 
      #{scoreValue,jdbcType=DECIMAL}, #{enName,jdbcType=VARCHAR}, #{unitValue,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{dicType,jdbcType=VARCHAR}, #{dataFrom,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectDictionary">
    insert into project_dictionary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="optionValue != null">
        option_value,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="enName != null">
        en_name,
      </if>
      <if test="unitValue != null">
        unit_value,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="dicType != null">
        dic_type,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="optionValue != null">
        #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="enName != null">
        #{enName,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="dicType != null">
        #{dicType,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectDictionaryExample" resultType="java.lang.Long">
    select count(*) from project_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>


  <select id="selectProjectDictionaryList"  resultMap="BaseResultMap">
    select  * from project_dictionary where 1=1
    <if test="projectId != null">
      and project_id = #{projectId}
    </if>
    <if test="parentId != null">
      and parent_Id = #{parentId}
    </if>
    <if test="name != null and name!=''">
      and (name like concat('%', #{name}, '%')
      or code like concat('%', #{name}, '%')
      or description like concat('%', #{name}, '%')
          )
    </if>
    <if test="status != null and status!=''">
      and status = #{status}
    </if>
    <if test="dicType != null">
      and dic_type = #{dicType}
    </if>
    order by sort, create_Time desc
  </select>


  <select id="getDictList" resultType="com.haoys.user.model.Dictionary">
    select
        id,
        project_id as projectId,
        parent_id as parentId,
        code,
        name,
        option_value as value,
        description
    from project_dictionary where 1=1
    <if test="projectId != null">
      and project_id = #{projectId}
    </if>
    <if test="parentId != null">
      and parent_Id = #{parentId}
    </if>
    <if test="status != null and status!=''">
      and status = #{status}
    </if>
    <if test="dicType != null">
      and dic_type = #{dicType}
    </if>
    order by sort, create_Time desc
  </select>


  <select id="selectExportList" resultType="com.haoys.user.domain.dto.DictExportDto">
    select
        id,
        project_id,
        parent_id,
        code,
        name,
        option_value,
        score_value,
        en_name,
        unit_value,
        description,
        dic_type,
        sort,
        status
    from project_dictionary where 1=1
    <if test="projectId != null">
      and project_id = #{projectId}
    </if>
    <if test="parentId != null">
      and parent_Id = #{parentId}
    </if>
    <if test="name != null and name!=''">
      and (name like concat('%', #{name}, '%')
      or code like concat('%', #{name}, '%')
      or description like concat('%', #{name}, '%')
      )
    </if>
    <if test="status != null and status!=''">
      and status = #{status}
    </if>
    <if test="dicType != null">
      and dic_type = #{dicType}
    </if>
    order by sort, create_Time desc
  </select>


  <select id="exportDictItem" resultType="com.haoys.user.domain.dto.DictItemExportDto">
    select
    d2.name,
    d2.code,
    d1.name as optionName,
    d1.option_value as optionValue,
    d1.en_name as enName
    from project_dictionary d1
    join project_dictionary d2 on d1.parent_id=d2.id
    where 1=1
    <if test="projectId != null">
      and d1.project_id = #{projectId}
    </if>
    <if test="parentId != null">
      and d1.parent_Id = #{parentId}
    </if>
    <if test="name != null and name!=''">
      and (d1.name like concat('%', #{name}, '%')
      or d1.code like concat('%', #{name}, '%')
      or d1.description like concat('%', #{name}, '%')
      )
    </if>
    order by d1.sort, d1.create_Time desc
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    update project_dictionary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.optionValue != null">
        option_value = #{record.optionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.scoreValue != null">
        score_value = #{record.scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="record.enName != null">
        en_name = #{record.enName,jdbcType=VARCHAR},
      </if>
      <if test="record.unitValue != null">
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.dicType != null">
        dic_type = #{record.dicType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_dictionary
    set id = #{record.id,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      option_value = #{record.optionValue,jdbcType=VARCHAR},
      score_value = #{record.scoreValue,jdbcType=DECIMAL},
      en_name = #{record.enName,jdbcType=VARCHAR},
      unit_value = #{record.unitValue,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      dic_type = #{record.dicType,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectDictionary">
    update project_dictionary
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="optionValue != null">
        option_value = #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue,jdbcType=DECIMAL},
      </if>
      <if test="enName != null">
        en_name = #{enName,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        unit_value = #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="dicType != null">
        dic_type = #{dicType,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectDictionary">
    update project_dictionary
    set project_id = #{projectId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      option_value = #{optionValue,jdbcType=VARCHAR},
      score_value = #{scoreValue,jdbcType=DECIMAL},
      en_name = #{enName,jdbcType=VARCHAR},
      unit_value = #{unitValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      dic_type = #{dicType,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <select id="getProjectDictionaryByParentIdAndCode" resultMap="BaseResultMap">
    select * from project_dictionary where project_id = #{projectId} and code = #{code} and parent_id = #{parentId} and status = '0'
  </select>
</mapper>
