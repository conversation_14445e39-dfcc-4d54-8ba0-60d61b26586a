<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateVariableViewBaseMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateVariableViewBase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="option_name" jdbcType="VARCHAR" property="optionName" />
    <result column="option_value" jdbcType="VARCHAR" property="optionValue" />
    <result column="enable_table" jdbcType="BIT" property="enableTable" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, form_id, form_detail_id, form_table_id, label, option_name, option_value,
    enable_table, status, create_user_id, update_user_id, create_time, update_time, tenant_id,
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateVariableViewBaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_variable_view_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from template_variable_view_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_variable_view_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateVariableViewBaseExample">
    delete from template_variable_view_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateVariableViewBase">
    insert into template_variable_view_base (id, project_id, form_id,
      form_detail_id, form_table_id, label,
      option_name, option_value, enable_table,
      status, create_user_id, update_user_id,
      create_time, update_time, tenant_id,
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT},
      #{formDetailId,jdbcType=BIGINT}, #{formTableId,jdbcType=BIGINT}, #{label,jdbcType=VARCHAR},
      #{optionName,jdbcType=VARCHAR}, #{optionValue,jdbcType=VARCHAR}, #{enableTable,jdbcType=BIT},
      #{status,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR},
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateVariableViewBase">
    insert into template_variable_view_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="optionName != null">
        option_name,
      </if>
      <if test="optionValue != null">
        option_value,
      </if>
      <if test="enableTable != null">
        enable_table,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="optionName != null">
        #{optionName,jdbcType=VARCHAR},
      </if>
      <if test="optionValue != null">
        #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="enableTable != null">
        #{enableTable,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateVariableViewBaseExample" resultType="java.lang.Long">
    select count(*) from template_variable_view_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_variable_view_base
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.optionName != null">
        option_name = #{record.optionName,jdbcType=VARCHAR},
      </if>
      <if test="record.optionValue != null">
        option_value = #{record.optionValue,jdbcType=VARCHAR},
      </if>
      <if test="record.enableTable != null">
        enable_table = #{record.enableTable,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_variable_view_base
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      label = #{record.label,jdbcType=VARCHAR},
      option_name = #{record.optionName,jdbcType=VARCHAR},
      option_value = #{record.optionValue,jdbcType=VARCHAR},
      enable_table = #{record.enableTable,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateVariableViewBase">
    update template_variable_view_base
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="optionName != null">
        option_name = #{optionName,jdbcType=VARCHAR},
      </if>
      <if test="optionValue != null">
        option_value = #{optionValue,jdbcType=VARCHAR},
      </if>
      <if test="enableTable != null">
        enable_table = #{enableTable,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateVariableViewBase">
    update template_variable_view_base
    set project_id = #{projectId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      label = #{label,jdbcType=VARCHAR},
      option_name = #{optionName,jdbcType=VARCHAR},
      option_value = #{optionValue,jdbcType=VARCHAR},
      enable_table = #{enableTable,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getTemplateFormVariableViewConfig" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from template_variable_view_base
    where project_id = #{projectId} and form_id = #{baseFormId} and form_detail_id = #{baseFormDetailId}
    <if test="baseFormTableId != null and enableTable == false">
      and form_table_id = #{baseFormTableId}
    </if>
    <if test="optionValue != null">
      and option_value = #{optionValue}
    </if>
    and status = '0'
  </select>

  <select id="getTemplateVariableBaseInfo" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from template_variable_view_base
    where project_id = #{projectId} and form_id = #{formId} and form_detail_id = #{formDetailId}
    <if test="formTableId != null and formTableId != ''">
      and form_table_id = #{formTableId}
    </if>
    <if test="optionValueId != null">
      and option_value = #{optionValueId}
    </if>
    and status = '0'
  </select>

  <select id="getTemplateVariableOptionListByVariableId" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    from template_variable_view_base
    where 1 = 1
    <if test="projectId != null and projectId !=''">
      AND template_variable_view_base.project_id = #{projectId}
    </if>
    <if test="projectId == null and projectId ==''">
      AND template_variable_view_base.project_id is null
    </if>
      and form_id = #{formId} and form_detail_id = #{formDetailId}
    <if test="formTableId != null and formTableId != ''">
      and form_table_id = #{formTableId}
    </if>
    and status = '0'
  </select>

  <delete id="deleteTemplateFormVariableViewConfig">
    delete from template_variable_view_base where project_id = #{projectId} and form_id = #{formId} and form_detail_id = #{formDetailId}
  </delete>
</mapper>
