<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectFormSignSdvMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectFormSignSdv">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_detail_value" jdbcType="VARCHAR" property="formDetailValue" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="form_result_id" jdbcType="BIGINT" property="formResultId" />
    <result column="form_result_table_id" jdbcType="BIGINT" property="formResultTableId" />
    <result column="form_result_table_rowno" jdbcType="BIGINT" property="formResultTableRowno" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="role_code" jdbcType="VARCHAR" property="roleCode" />
    <result column="sign_status" jdbcType="BIT" property="signStatus" />
    <result column="s_check_status" jdbcType="BIT" property="sCheckStatus" />
    <result column="d_check_status" jdbcType="BIT" property="dCheckStatus" />
    <result column="m_check_status" jdbcType="BIT" property="mCheckStatus" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="audit_source_flag" jdbcType="BIT" property="auditSourceFlag" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="operator_time" jdbcType="TIMESTAMP" property="operatorTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, testee_id, visit_id, form_id, form_detail_id, form_detail_value, 
    form_table_id, form_result_id, form_result_table_id, form_result_table_rowno, label, 
    form_name, role_code, sign_status, s_check_status, d_check_status, m_check_status, 
    status, audit_source_flag, expand, operator_time, create_user_id, create_time, update_user_id, 
    update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectFormSignSdvExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_form_sign_sdv
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_form_sign_sdv
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_form_sign_sdv
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectFormSignSdvExample">
    delete from project_form_sign_sdv
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectFormSignSdv">
    insert into project_form_sign_sdv (id, project_id, testee_id, 
      visit_id, form_id, form_detail_id, 
      form_detail_value, form_table_id, form_result_id, 
      form_result_table_id, form_result_table_rowno, 
      label, form_name, role_code, 
      sign_status, s_check_status, d_check_status, 
      m_check_status, status, audit_source_flag, 
      expand, operator_time, create_user_id, 
      create_time, update_user_id, update_time, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formDetailId,jdbcType=BIGINT}, 
      #{formDetailValue,jdbcType=VARCHAR}, #{formTableId,jdbcType=BIGINT}, #{formResultId,jdbcType=BIGINT}, 
      #{formResultTableId,jdbcType=BIGINT}, #{formResultTableRowno,jdbcType=BIGINT}, 
      #{label,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR}, #{roleCode,jdbcType=VARCHAR}, 
      #{signStatus,jdbcType=BIT}, #{sCheckStatus,jdbcType=BIT}, #{dCheckStatus,jdbcType=BIT}, 
      #{mCheckStatus,jdbcType=BIT}, #{status,jdbcType=VARCHAR}, #{auditSourceFlag,jdbcType=BIT}, 
      #{expand,jdbcType=VARCHAR}, #{operatorTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectFormSignSdv">
    insert into project_form_sign_sdv
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formDetailValue != null">
        form_detail_value,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="formResultId != null">
        form_result_id,
      </if>
      <if test="formResultTableId != null">
        form_result_table_id,
      </if>
      <if test="formResultTableRowno != null">
        form_result_table_rowno,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="roleCode != null">
        role_code,
      </if>
      <if test="signStatus != null">
        sign_status,
      </if>
      <if test="sCheckStatus != null">
        s_check_status,
      </if>
      <if test="dCheckStatus != null">
        d_check_status,
      </if>
      <if test="mCheckStatus != null">
        m_check_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="auditSourceFlag != null">
        audit_source_flag,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="operatorTime != null">
        operator_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formDetailValue != null">
        #{formDetailValue,jdbcType=VARCHAR},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultId != null">
        #{formResultId,jdbcType=BIGINT},
      </if>
      <if test="formResultTableId != null">
        #{formResultTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultTableRowno != null">
        #{formResultTableRowno,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="roleCode != null">
        #{roleCode,jdbcType=VARCHAR},
      </if>
      <if test="signStatus != null">
        #{signStatus,jdbcType=BIT},
      </if>
      <if test="sCheckStatus != null">
        #{sCheckStatus,jdbcType=BIT},
      </if>
      <if test="dCheckStatus != null">
        #{dCheckStatus,jdbcType=BIT},
      </if>
      <if test="mCheckStatus != null">
        #{mCheckStatus,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="auditSourceFlag != null">
        #{auditSourceFlag,jdbcType=BIT},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="operatorTime != null">
        #{operatorTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectFormSignSdvExample" resultType="java.lang.Long">
    select count(*) from project_form_sign_sdv
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_form_sign_sdv
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailValue != null">
        form_detail_value = #{record.formDetailValue,jdbcType=VARCHAR},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.formResultId != null">
        form_result_id = #{record.formResultId,jdbcType=BIGINT},
      </if>
      <if test="record.formResultTableId != null">
        form_result_table_id = #{record.formResultTableId,jdbcType=BIGINT},
      </if>
      <if test="record.formResultTableRowno != null">
        form_result_table_rowno = #{record.formResultTableRowno,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.roleCode != null">
        role_code = #{record.roleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.signStatus != null">
        sign_status = #{record.signStatus,jdbcType=BIT},
      </if>
      <if test="record.sCheckStatus != null">
        s_check_status = #{record.sCheckStatus,jdbcType=BIT},
      </if>
      <if test="record.dCheckStatus != null">
        d_check_status = #{record.dCheckStatus,jdbcType=BIT},
      </if>
      <if test="record.mCheckStatus != null">
        m_check_status = #{record.mCheckStatus,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.auditSourceFlag != null">
        audit_source_flag = #{record.auditSourceFlag,jdbcType=BIT},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorTime != null">
        operator_time = #{record.operatorTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_form_sign_sdv
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_detail_value = #{record.formDetailValue,jdbcType=VARCHAR},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      form_result_id = #{record.formResultId,jdbcType=BIGINT},
      form_result_table_id = #{record.formResultTableId,jdbcType=BIGINT},
      form_result_table_rowno = #{record.formResultTableRowno,jdbcType=BIGINT},
      label = #{record.label,jdbcType=VARCHAR},
      form_name = #{record.formName,jdbcType=VARCHAR},
      role_code = #{record.roleCode,jdbcType=VARCHAR},
      sign_status = #{record.signStatus,jdbcType=BIT},
      s_check_status = #{record.sCheckStatus,jdbcType=BIT},
      d_check_status = #{record.dCheckStatus,jdbcType=BIT},
      m_check_status = #{record.mCheckStatus,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      audit_source_flag = #{record.auditSourceFlag,jdbcType=BIT},
      expand = #{record.expand,jdbcType=VARCHAR},
      operator_time = #{record.operatorTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectFormSignSdv">
    update project_form_sign_sdv
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formDetailValue != null">
        form_detail_value = #{formDetailValue,jdbcType=VARCHAR},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultId != null">
        form_result_id = #{formResultId,jdbcType=BIGINT},
      </if>
      <if test="formResultTableId != null">
        form_result_table_id = #{formResultTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultTableRowno != null">
        form_result_table_rowno = #{formResultTableRowno,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="roleCode != null">
        role_code = #{roleCode,jdbcType=VARCHAR},
      </if>
      <if test="signStatus != null">
        sign_status = #{signStatus,jdbcType=BIT},
      </if>
      <if test="sCheckStatus != null">
        s_check_status = #{sCheckStatus,jdbcType=BIT},
      </if>
      <if test="dCheckStatus != null">
        d_check_status = #{dCheckStatus,jdbcType=BIT},
      </if>
      <if test="mCheckStatus != null">
        m_check_status = #{mCheckStatus,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="auditSourceFlag != null">
        audit_source_flag = #{auditSourceFlag,jdbcType=BIT},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="operatorTime != null">
        operator_time = #{operatorTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectFormSignSdv">
    update project_form_sign_sdv
    set project_id = #{projectId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_detail_value = #{formDetailValue,jdbcType=VARCHAR},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      form_result_id = #{formResultId,jdbcType=BIGINT},
      form_result_table_id = #{formResultTableId,jdbcType=BIGINT},
      form_result_table_rowno = #{formResultTableRowno,jdbcType=BIGINT},
      label = #{label,jdbcType=VARCHAR},
      form_name = #{formName,jdbcType=VARCHAR},
      role_code = #{roleCode,jdbcType=VARCHAR},
      sign_status = #{signStatus,jdbcType=BIT},
      s_check_status = #{sCheckStatus,jdbcType=BIT},
      d_check_status = #{dCheckStatus,jdbcType=BIT},
      m_check_status = #{mCheckStatus,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      audit_source_flag = #{auditSourceFlag,jdbcType=BIT},
      expand = #{expand,jdbcType=VARCHAR},
      operator_time = #{operatorTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>