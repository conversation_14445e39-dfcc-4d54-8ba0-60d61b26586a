<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.AiChatSessionMapper">

    <resultMap id="BaseResultMap" type="com.haoys.user.domain.entity.AiChatSession">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="model_type" jdbcType="VARCHAR" property="modelType"/>
        <result column="model_name" jdbcType="VARCHAR" property="modelName"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="total_tokens" jdbcType="BIGINT" property="totalTokens"/>
        <result column="total_cost" jdbcType="DECIMAL" property="totalCost"/>
        <result column="message_count" jdbcType="INTEGER" property="messageCount"/>
        <result column="last_message_time" jdbcType="TIMESTAMP" property="lastMessageTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, session_id, user_id, user_name, title, model_type, model_name, status,
        total_tokens, total_cost, message_count, last_message_time, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.haoys.user.domain.entity.AiChatSession" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_chat_session (
            session_id, user_id, user_name, title, model_type, model_name, status,
            total_tokens, total_cost, message_count, last_message_time, create_time, update_time
        ) VALUES (
            #{sessionId}, #{userId}, #{userName}, #{title}, #{modelType}, #{modelName}, #{status},
            #{totalTokens}, #{totalCost}, #{messageCount}, #{lastMessageTime}, #{createTime}, #{updateTime}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_session
        WHERE id = #{id}
    </select>

    <select id="selectBySessionId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_session
        WHERE session_id = #{sessionId}
    </select>

    <update id="updateBySessionId" parameterType="com.haoys.user.domain.entity.AiChatSession">
        UPDATE ai_chat_session
        SET user_name = #{userName},
            title = #{title},
            model_type = #{modelType},
            model_name = #{modelName},
            status = #{status},
            total_tokens = #{totalTokens},
            total_cost = #{totalCost},
            message_count = #{messageCount},
            last_message_time = #{lastMessageTime},
            update_time = #{updateTime}
        WHERE session_id = #{sessionId}
    </update>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_session
        WHERE user_id = #{userId}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ai_chat_session
        WHERE user_id = #{userId}
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <update id="updateStatus">
        UPDATE ai_chat_session
        SET status = #{status}, update_time = NOW()
        WHERE session_id = #{sessionId}
    </update>

    <update id="updateTokensAndCost">
        UPDATE ai_chat_session
        SET total_tokens = total_tokens + #{tokens},
            total_cost = total_cost + #{cost},
            update_time = NOW()
        WHERE session_id = #{sessionId}
    </update>

    <update id="updateMessageCountAndTime">
        UPDATE ai_chat_session
        SET message_count = #{messageCount},
            last_message_time = #{lastMessageTime},
            update_time = NOW()
        WHERE session_id = #{sessionId}
    </update>

    <delete id="deleteBySessionId" parameterType="java.lang.String">
        DELETE FROM ai_chat_session
        WHERE session_id = #{sessionId}
    </delete>

    <delete id="deleteTimeoutSessions" parameterType="java.lang.Integer">
        DELETE FROM ai_chat_session
        WHERE status = 1
        AND last_message_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
    </delete>

</mapper>
