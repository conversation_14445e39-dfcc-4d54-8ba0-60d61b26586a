<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.RctsTesteeInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.RctsTesteeInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="testee_code" jdbcType="VARCHAR" property="testeeCode" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="acronym" jdbcType="VARCHAR" property="acronym" />
    <result column="owner_org_id" jdbcType="VARCHAR" property="ownerOrgId" />
    <result column="owner_org_name" jdbcType="VARCHAR" property="ownerOrgName" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="randomized_number" jdbcType="VARCHAR" property="randomizedNumber" />
    <result column="join_group_name" jdbcType="VARCHAR" property="joinGroupName" />
    <result column="randomized_time" jdbcType="TIMESTAMP" property="randomizedTime" />
    <result column="research_status" jdbcType="VARCHAR" property="researchStatus" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, testee_id, testee_code, real_name, acronym, owner_org_id, owner_org_name, 
    age, gender, randomized_number, join_group_name, randomized_time, research_status, 
    status, expand, create_time, update_time, create_user_id, update_user_id, tenant_id, 
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.RctsTesteeInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rcts_testee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rcts_testee_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rcts_testee_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.RctsTesteeInfoExample">
    delete from rcts_testee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.RctsTesteeInfo">
    insert into rcts_testee_info (id, project_id, testee_id, 
      testee_code, real_name, acronym, 
      owner_org_id, owner_org_name, age, 
      gender, randomized_number, join_group_name, 
      randomized_time, research_status, status, 
      expand, create_time, update_time, 
      create_user_id, update_user_id, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, 
      #{testeeCode,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, #{acronym,jdbcType=VARCHAR}, 
      #{ownerOrgId,jdbcType=VARCHAR}, #{ownerOrgName,jdbcType=VARCHAR}, #{age,jdbcType=INTEGER}, 
      #{gender,jdbcType=VARCHAR}, #{randomizedNumber,jdbcType=VARCHAR}, #{joinGroupName,jdbcType=VARCHAR}, 
      #{randomizedTime,jdbcType=TIMESTAMP}, #{researchStatus,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{expand,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.RctsTesteeInfo">
    insert into rcts_testee_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="testeeCode != null">
        testee_code,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="acronym != null">
        acronym,
      </if>
      <if test="ownerOrgId != null">
        owner_org_id,
      </if>
      <if test="ownerOrgName != null">
        owner_org_name,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="randomizedNumber != null">
        randomized_number,
      </if>
      <if test="joinGroupName != null">
        join_group_name,
      </if>
      <if test="randomizedTime != null">
        randomized_time,
      </if>
      <if test="researchStatus != null">
        research_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeCode != null">
        #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="acronym != null">
        #{acronym,jdbcType=VARCHAR},
      </if>
      <if test="ownerOrgId != null">
        #{ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="ownerOrgName != null">
        #{ownerOrgName,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=INTEGER},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="randomizedNumber != null">
        #{randomizedNumber,jdbcType=VARCHAR},
      </if>
      <if test="joinGroupName != null">
        #{joinGroupName,jdbcType=VARCHAR},
      </if>
      <if test="randomizedTime != null">
        #{randomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="researchStatus != null">
        #{researchStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.RctsTesteeInfoExample" resultType="java.lang.Long">
    select count(*) from rcts_testee_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rcts_testee_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeCode != null">
        testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.realName != null">
        real_name = #{record.realName,jdbcType=VARCHAR},
      </if>
      <if test="record.acronym != null">
        acronym = #{record.acronym,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerOrgId != null">
        owner_org_id = #{record.ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerOrgName != null">
        owner_org_name = #{record.ownerOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.age != null">
        age = #{record.age,jdbcType=INTEGER},
      </if>
      <if test="record.gender != null">
        gender = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedNumber != null">
        randomized_number = #{record.randomizedNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.joinGroupName != null">
        join_group_name = #{record.joinGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.randomizedTime != null">
        randomized_time = #{record.randomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.researchStatus != null">
        research_status = #{record.researchStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rcts_testee_info
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      real_name = #{record.realName,jdbcType=VARCHAR},
      acronym = #{record.acronym,jdbcType=VARCHAR},
      owner_org_id = #{record.ownerOrgId,jdbcType=VARCHAR},
      owner_org_name = #{record.ownerOrgName,jdbcType=VARCHAR},
      age = #{record.age,jdbcType=INTEGER},
      gender = #{record.gender,jdbcType=VARCHAR},
      randomized_number = #{record.randomizedNumber,jdbcType=VARCHAR},
      join_group_name = #{record.joinGroupName,jdbcType=VARCHAR},
      randomized_time = #{record.randomizedTime,jdbcType=TIMESTAMP},
      research_status = #{record.researchStatus,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.RctsTesteeInfo">
    update rcts_testee_info
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeCode != null">
        testee_code = #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="acronym != null">
        acronym = #{acronym,jdbcType=VARCHAR},
      </if>
      <if test="ownerOrgId != null">
        owner_org_id = #{ownerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="ownerOrgName != null">
        owner_org_name = #{ownerOrgName,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=INTEGER},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="randomizedNumber != null">
        randomized_number = #{randomizedNumber,jdbcType=VARCHAR},
      </if>
      <if test="joinGroupName != null">
        join_group_name = #{joinGroupName,jdbcType=VARCHAR},
      </if>
      <if test="randomizedTime != null">
        randomized_time = #{randomizedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="researchStatus != null">
        research_status = #{researchStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.RctsTesteeInfo">
    update rcts_testee_info
    set project_id = #{projectId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      testee_code = #{testeeCode,jdbcType=VARCHAR},
      real_name = #{realName,jdbcType=VARCHAR},
      acronym = #{acronym,jdbcType=VARCHAR},
      owner_org_id = #{ownerOrgId,jdbcType=VARCHAR},
      owner_org_name = #{ownerOrgName,jdbcType=VARCHAR},
      age = #{age,jdbcType=INTEGER},
      gender = #{gender,jdbcType=VARCHAR},
      randomized_number = #{randomizedNumber,jdbcType=VARCHAR},
      join_group_name = #{joinGroupName,jdbcType=VARCHAR},
      randomized_time = #{randomizedTime,jdbcType=TIMESTAMP},
      research_status = #{researchStatus,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getTesteeInfoPage" resultType="com.haoys.user.domain.vo.rcts.TesteeInfoVo">
    SELECT id, project_id , testee_code, acronym, randomized_Number,join_Group_Name,randomized_Time, research_Status
    FROM rcts_testee_info
    where 1=1
    <if test="projectId != null">
      and project_id = #{projectId}
    </if>
    <if test="orgId != null">
      and owner_org_id = #{orgId}
    </if>
    <if test="researchStatus != null">
      and research_status = #{researchStatus}
    </if>
    <if test="queryParam1 != null" >
      and (testee_code like CONCAT('%',#{testeeCode},'%') or acronym like CONCAT('%',#{acronym},'%') )
    </if>
  </select>

  <select id="getRctsTesteeInfo" resultMap="BaseResultMap">
    select * from rcts_testee_info where project_id = #{projectId} and owner_org_id = #{projectOrgId} and testee_id = #{testeeId} and status = '0'
  </select>

</mapper>