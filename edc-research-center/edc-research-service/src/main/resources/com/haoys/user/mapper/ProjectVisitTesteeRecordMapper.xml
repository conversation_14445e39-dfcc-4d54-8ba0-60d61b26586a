<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectVisitTesteeRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectVisitTesteeRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="follow_up_start_time" jdbcType="TIMESTAMP" property="followUpStartTime" />
    <result column="follow_up_end_time" jdbcType="TIMESTAMP" property="followUpEndTime" />
    <result column="follow_up_real_time" jdbcType="TIMESTAMP" property="followUpRealTime" />
    <result column="follow_up_next_time" jdbcType="TIMESTAMP" property="followUpNextTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, testee_id, follow_up_start_time, follow_up_end_time, 
    follow_up_real_time, follow_up_next_time, create_time, update_time, create_user_id, 
    update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectVisitTesteeRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_visit_testee_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_visit_testee_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_visit_testee_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectVisitTesteeRecordExample">
    delete from project_visit_testee_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectVisitTesteeRecord">
    insert into project_visit_testee_record (id, project_id, plan_id, 
      visit_id, testee_id, follow_up_start_time, 
      follow_up_end_time, follow_up_real_time, 
      follow_up_next_time, create_time, update_time, 
      create_user_id, update_user_id, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{testeeId,jdbcType=BIGINT}, #{followUpStartTime,jdbcType=TIMESTAMP}, 
      #{followUpEndTime,jdbcType=TIMESTAMP}, #{followUpRealTime,jdbcType=TIMESTAMP}, 
      #{followUpNextTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectVisitTesteeRecord">
    insert into project_visit_testee_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="followUpStartTime != null">
        follow_up_start_time,
      </if>
      <if test="followUpEndTime != null">
        follow_up_end_time,
      </if>
      <if test="followUpRealTime != null">
        follow_up_real_time,
      </if>
      <if test="followUpNextTime != null">
        follow_up_next_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="followUpStartTime != null">
        #{followUpStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followUpEndTime != null">
        #{followUpEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followUpRealTime != null">
        #{followUpRealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followUpNextTime != null">
        #{followUpNextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectVisitTesteeRecordExample" resultType="java.lang.Long">
    select count(*) from project_visit_testee_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_visit_testee_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.followUpStartTime != null">
        follow_up_start_time = #{record.followUpStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.followUpEndTime != null">
        follow_up_end_time = #{record.followUpEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.followUpRealTime != null">
        follow_up_real_time = #{record.followUpRealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.followUpNextTime != null">
        follow_up_next_time = #{record.followUpNextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_visit_testee_record
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      follow_up_start_time = #{record.followUpStartTime,jdbcType=TIMESTAMP},
      follow_up_end_time = #{record.followUpEndTime,jdbcType=TIMESTAMP},
      follow_up_real_time = #{record.followUpRealTime,jdbcType=TIMESTAMP},
      follow_up_next_time = #{record.followUpNextTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectVisitTesteeRecord">
    update project_visit_testee_record
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="followUpStartTime != null">
        follow_up_start_time = #{followUpStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followUpEndTime != null">
        follow_up_end_time = #{followUpEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followUpRealTime != null">
        follow_up_real_time = #{followUpRealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="followUpNextTime != null">
        follow_up_next_time = #{followUpNextTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectVisitTesteeRecord">
    update project_visit_testee_record
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      follow_up_start_time = #{followUpStartTime,jdbcType=TIMESTAMP},
      follow_up_end_time = #{followUpEndTime,jdbcType=TIMESTAMP},
      follow_up_real_time = #{followUpRealTime,jdbcType=TIMESTAMP},
      follow_up_next_time = #{followUpNextTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--实际访视汇总查询-->
  <select id="getProjectVisitFollowRealTimeList" resultType="com.haoys.user.model.ProjectVisitTesteeRecord">
      SELECT
          project_visit_config.id visitId,
          project_visit_config.visit_name visitName
      FROM
          project_visit_testee_record
          INNER JOIN project_visit_config ON project_visit_config.id = project_visit_testee_record.visit_id
          AND project_visit_config.project_id = project_visit_testee_record.project_id
          INNER JOIN project_testee_info ON project_testee_info.id = project_visit_testee_record.testee_id
      WHERE 1 = 1
          AND project_visit_testee_record.project_id = #{projectId}
          <if test="orgIds != null and orgIds != ''">
            AND project_testee_info.owner_org_id IN (${orgIds})
          </if>
          AND project_visit_testee_record.follow_up_start_time IS NOT NULL
          AND project_visit_testee_record.follow_up_end_time IS NOT NULL
          AND project_visit_testee_record.follow_up_real_time IS NOT NULL
          /*AND project_visit_testee_record.follow_up_real_time BETWEEN project_visit_testee_record.follow_up_start_time AND project_visit_testee_record.follow_up_end_time*/
          AND project_visit_testee_record.follow_up_real_time BETWEEN date_add(curdate(), interval - day(curdate()) + 1 day) AND NOW()
  </select>

  <!--计划访视汇总查询-->
  <select id="getProjectPlannedVisitList" resultType="com.haoys.user.model.ProjectVisitTesteeRecord">
    SELECT
        project_visit_config.id,
        project_visit_config.visit_name
    FROM
        project_visit_testee_record
        INNER JOIN project_visit_config ON project_visit_config.id = project_visit_testee_record.visit_id
        AND project_visit_config.project_id = project_visit_testee_record.project_id
        INNER JOIN project_testee_info ON project_testee_info.id = project_visit_testee_record.testee_id
    WHERE 1 = 1
        AND project_visit_testee_record.project_id = #{projectId}
        <if test="orgIds != null and orgIds != ''">
            AND project_testee_info.owner_org_id IN (${orgIds})
        </if>
        AND (project_visit_testee_record.follow_up_start_time BETWEEN date_add(curdate(), interval - day(curdate()) + 1 day) AND last_day(curdate())
        OR project_visit_testee_record.follow_up_end_time BETWEEN date_add(curdate(), interval - day(curdate()) + 1 day) AND last_day(curdate()))
  </select>

  <!--访视超窗统计-->
  <select id="getProjectOverdueVisitFollowRealTimeNullCount" resultType="com.haoys.user.model.ProjectVisitTesteeRecord">
    SELECT
        project_visit_config.id,
        project_visit_config.visit_name,
        project_visit_testee_record.follow_up_start_time,
        project_visit_testee_record.follow_up_end_time
    FROM
        project_visit_testee_record
        INNER JOIN project_visit_config ON project_visit_config.id = project_visit_testee_record.visit_id
        AND project_visit_config.project_id = project_visit_testee_record.project_id
        INNER JOIN project_testee_info ON project_testee_info.id = project_visit_testee_record.testee_id
    WHERE 1 = 1
        AND project_visit_testee_record.project_id =  #{projectId}
        <if test="orgIds != null and orgIds != ''">
          AND project_testee_info.owner_org_id IN (${orgIds})
        </if>
        <if test="nextFollowRealNullValue != null and nextFollowRealNullValue != ''">
          AND project_visit_testee_record.follow_up_real_time IS NULL
        </if>
        AND ( now() >  project_visit_testee_record.follow_up_end_time )
  </select>

  <!--逾期超窗并录入访视时间-->
  <select id="getProjectOverdueVisitFollowRealTimeNotNullCount" resultType="com.haoys.user.model.ProjectVisitTesteeRecord">
    SELECT
        project_visit_config.id,
        project_visit_config.visit_name,
        project_visit_testee_record.follow_up_start_time,
        project_visit_testee_record.follow_up_end_time
        FROM
        project_visit_testee_record
        INNER JOIN project_visit_config ON project_visit_config.id = project_visit_testee_record.visit_id
        AND project_visit_config.project_id = project_visit_testee_record.project_id
        INNER JOIN project_testee_info ON project_testee_info.id = project_visit_testee_record.testee_id
        WHERE 1 = 1
        AND project_visit_testee_record.project_id =  #{projectId}
    <if test="orgIds != null and orgIds != ''">
      AND project_testee_info.owner_org_id IN (${orgIds})
    </if>
    <if test="nextFollowRealNotNullValue != null and nextFollowRealNotNullValue != ''">
      AND project_visit_testee_record.follow_up_real_time IS NOT NULL
    </if>
    AND project_visit_testee_record.follow_up_end_time IS NOT NULL
    AND ( now() >  project_visit_testee_record.follow_up_end_time )
  </select>


</mapper>