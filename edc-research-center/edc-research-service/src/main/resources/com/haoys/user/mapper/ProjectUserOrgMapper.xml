<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectUserOrgMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectUserOrg">
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    project_id, user_id, org_id, org_code, create_time, create_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectUserOrgExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_user_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectUserOrgExample">
    delete from project_user_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectUserOrg">
    insert into project_user_org (project_id, user_id, org_id, 
      org_code, create_time, create_user_id, 
      tenant_id, platform_id)
    values (#{projectId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, 
      #{orgCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectUserOrg">
    insert into project_user_org
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectUserOrgExample" resultType="java.lang.Long">
    select count(*) from project_user_org
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_user_org
    <set>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgCode != null">
        org_code = #{record.orgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_user_org
    set project_id = #{record.projectId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_code = #{record.orgCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="selectOrgListByUserId" resultMap="BaseResultMap">
        select user_id, org_id, create_time, if_primary, project_id, org_type from project_user_org where
        user_id =#{userId} and org_id = #{orgId} and project_id is null and org_type = 1
  </select>

  <!--查询项目研究中心id集合-->
  <select id="getOrgIdsByProjectIdAndUserId" resultType="java.lang.String">
    select distinct org_id from project_user_org where 1=1
    <if test="projectId != null">
      and project_id =#{projectId}
    </if>
    <if test="userId != null">
      and user_id =#{userId}
    </if>
    <if test="code != null">
      and code =#{code}
    </if>
  </select>

  <!--根据项目和研究中心查询用户列表-->
  <select id="selectOrgListByProjectIdAndOrgId" resultType="com.haoys.user.model.ProjectUserOrg">
    SELECT
        project_user_org.project_id projectId, project_user_org.user_id userId
    FROM project_user_org INNER JOIN project_org_info ON project_org_info.org_id = project_user_org.org_id
    WHERE  project_org_info.project_id = #{projectId} AND project_org_info.id = #{projectOrgId}
  </select>

  <select id="getProjectUserJoinOrgListByUserId" resultType="com.haoys.user.domain.vo.project.ProjectUserOrgVo">
    select
      project_org_info.id projectOrgId, project_org_info.org_id orgId, project_org_info.`code`,
      system_org_info.org_name orgName, project_user_org.user_id userId
    from  project_user_org
    inner join project_org_info on project_org_info.org_id = project_user_org.org_id
    and project_org_info.project_id = project_user_org.project_id
    inner join system_org_info on system_org_info.org_id = project_org_info.org_id
    where project_user_org.project_id = #{projectId}
    and project_user_org.user_id = #{userId}
    and system_org_info.`status` = 0
  </select>

</mapper>