<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.RctsDrugRegisterManageMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.RctsDrugRegisterManage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="Material" jdbcType="VARCHAR" property="material" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="merchandise_name" jdbcType="VARCHAR" property="merchandiseName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="material_status" jdbcType="VARCHAR" property="materialStatus" />
    <result column="material_count" jdbcType="INTEGER" property="materialCount" />
    <result column="material_specification" jdbcType="VARCHAR" property="materialSpecification" />
    <result column="package_unit" jdbcType="VARCHAR" property="packageUnit" />
    <result column="min_package_unit" jdbcType="INTEGER" property="minPackageUnit" />
    <result column="warning_value" jdbcType="INTEGER" property="warningValue" />
    <result column="storage_requirement" jdbcType="VARCHAR" property="storageRequirement" />
    <result column="expiration_start_date" jdbcType="TIMESTAMP" property="expirationStartDate" />
    <result column="expiration_end_date" jdbcType="TIMESTAMP" property="expirationEndDate" />
    <result column="expiration_warning_value" jdbcType="INTEGER" property="expirationWarningValue" />
    <result column="warning_status" jdbcType="VARCHAR" property="warningStatus" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, Material, material_code, product_name, merchandise_name, manufacturer, 
    country, material_status, material_count, material_specification, package_unit, 
    min_package_unit, warning_value, storage_requirement, expiration_start_date, expiration_end_date, 
    expiration_warning_value, warning_status, status, expand, create_time, update_time, 
    create_user_id, update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.RctsDrugRegisterManageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rcts_drug_register_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rcts_drug_register_manage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rcts_drug_register_manage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.RctsDrugRegisterManageExample">
    delete from rcts_drug_register_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.RctsDrugRegisterManage">
    insert into rcts_drug_register_manage (id, project_id, Material, 
      material_code, product_name, merchandise_name, 
      manufacturer, country, material_status, 
      material_count, material_specification, package_unit, 
      min_package_unit, warning_value, storage_requirement, 
      expiration_start_date, expiration_end_date, 
      expiration_warning_value, warning_status, 
      status, expand, create_time, 
      update_time, create_user_id, update_user_id, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{material,jdbcType=VARCHAR}, 
      #{materialCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{merchandiseName,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{materialStatus,jdbcType=VARCHAR}, 
      #{materialCount,jdbcType=INTEGER}, #{materialSpecification,jdbcType=VARCHAR}, #{packageUnit,jdbcType=VARCHAR}, 
      #{minPackageUnit,jdbcType=INTEGER}, #{warningValue,jdbcType=INTEGER}, #{storageRequirement,jdbcType=VARCHAR}, 
      #{expirationStartDate,jdbcType=TIMESTAMP}, #{expirationEndDate,jdbcType=TIMESTAMP}, 
      #{expirationWarningValue,jdbcType=INTEGER}, #{warningStatus,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.RctsDrugRegisterManage">
    insert into rcts_drug_register_manage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="material != null">
        Material,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="merchandiseName != null">
        merchandise_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="materialStatus != null">
        material_status,
      </if>
      <if test="materialCount != null">
        material_count,
      </if>
      <if test="materialSpecification != null">
        material_specification,
      </if>
      <if test="packageUnit != null">
        package_unit,
      </if>
      <if test="minPackageUnit != null">
        min_package_unit,
      </if>
      <if test="warningValue != null">
        warning_value,
      </if>
      <if test="storageRequirement != null">
        storage_requirement,
      </if>
      <if test="expirationStartDate != null">
        expiration_start_date,
      </if>
      <if test="expirationEndDate != null">
        expiration_end_date,
      </if>
      <if test="expirationWarningValue != null">
        expiration_warning_value,
      </if>
      <if test="warningStatus != null">
        warning_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="material != null">
        #{material,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="merchandiseName != null">
        #{merchandiseName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="materialStatus != null">
        #{materialStatus,jdbcType=VARCHAR},
      </if>
      <if test="materialCount != null">
        #{materialCount,jdbcType=INTEGER},
      </if>
      <if test="materialSpecification != null">
        #{materialSpecification,jdbcType=VARCHAR},
      </if>
      <if test="packageUnit != null">
        #{packageUnit,jdbcType=VARCHAR},
      </if>
      <if test="minPackageUnit != null">
        #{minPackageUnit,jdbcType=INTEGER},
      </if>
      <if test="warningValue != null">
        #{warningValue,jdbcType=INTEGER},
      </if>
      <if test="storageRequirement != null">
        #{storageRequirement,jdbcType=VARCHAR},
      </if>
      <if test="expirationStartDate != null">
        #{expirationStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationEndDate != null">
        #{expirationEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationWarningValue != null">
        #{expirationWarningValue,jdbcType=INTEGER},
      </if>
      <if test="warningStatus != null">
        #{warningStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.RctsDrugRegisterManageExample" resultType="java.lang.Long">
    select count(*) from rcts_drug_register_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rcts_drug_register_manage
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.material != null">
        Material = #{record.material,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCode != null">
        material_code = #{record.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.merchandiseName != null">
        merchandise_name = #{record.merchandiseName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.materialStatus != null">
        material_status = #{record.materialStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCount != null">
        material_count = #{record.materialCount,jdbcType=INTEGER},
      </if>
      <if test="record.materialSpecification != null">
        material_specification = #{record.materialSpecification,jdbcType=VARCHAR},
      </if>
      <if test="record.packageUnit != null">
        package_unit = #{record.packageUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.minPackageUnit != null">
        min_package_unit = #{record.minPackageUnit,jdbcType=INTEGER},
      </if>
      <if test="record.warningValue != null">
        warning_value = #{record.warningValue,jdbcType=INTEGER},
      </if>
      <if test="record.storageRequirement != null">
        storage_requirement = #{record.storageRequirement,jdbcType=VARCHAR},
      </if>
      <if test="record.expirationStartDate != null">
        expiration_start_date = #{record.expirationStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expirationEndDate != null">
        expiration_end_date = #{record.expirationEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expirationWarningValue != null">
        expiration_warning_value = #{record.expirationWarningValue,jdbcType=INTEGER},
      </if>
      <if test="record.warningStatus != null">
        warning_status = #{record.warningStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rcts_drug_register_manage
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      Material = #{record.material,jdbcType=VARCHAR},
      material_code = #{record.materialCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      merchandise_name = #{record.merchandiseName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      country = #{record.country,jdbcType=VARCHAR},
      material_status = #{record.materialStatus,jdbcType=VARCHAR},
      material_count = #{record.materialCount,jdbcType=INTEGER},
      material_specification = #{record.materialSpecification,jdbcType=VARCHAR},
      package_unit = #{record.packageUnit,jdbcType=VARCHAR},
      min_package_unit = #{record.minPackageUnit,jdbcType=INTEGER},
      warning_value = #{record.warningValue,jdbcType=INTEGER},
      storage_requirement = #{record.storageRequirement,jdbcType=VARCHAR},
      expiration_start_date = #{record.expirationStartDate,jdbcType=TIMESTAMP},
      expiration_end_date = #{record.expirationEndDate,jdbcType=TIMESTAMP},
      expiration_warning_value = #{record.expirationWarningValue,jdbcType=INTEGER},
      warning_status = #{record.warningStatus,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.RctsDrugRegisterManage">
    update rcts_drug_register_manage
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="material != null">
        Material = #{material,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="merchandiseName != null">
        merchandise_name = #{merchandiseName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="materialStatus != null">
        material_status = #{materialStatus,jdbcType=VARCHAR},
      </if>
      <if test="materialCount != null">
        material_count = #{materialCount,jdbcType=INTEGER},
      </if>
      <if test="materialSpecification != null">
        material_specification = #{materialSpecification,jdbcType=VARCHAR},
      </if>
      <if test="packageUnit != null">
        package_unit = #{packageUnit,jdbcType=VARCHAR},
      </if>
      <if test="minPackageUnit != null">
        min_package_unit = #{minPackageUnit,jdbcType=INTEGER},
      </if>
      <if test="warningValue != null">
        warning_value = #{warningValue,jdbcType=INTEGER},
      </if>
      <if test="storageRequirement != null">
        storage_requirement = #{storageRequirement,jdbcType=VARCHAR},
      </if>
      <if test="expirationStartDate != null">
        expiration_start_date = #{expirationStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationEndDate != null">
        expiration_end_date = #{expirationEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="expirationWarningValue != null">
        expiration_warning_value = #{expirationWarningValue,jdbcType=INTEGER},
      </if>
      <if test="warningStatus != null">
        warning_status = #{warningStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.RctsDrugRegisterManage">
    update rcts_drug_register_manage
    set project_id = #{projectId,jdbcType=BIGINT},
      Material = #{material,jdbcType=VARCHAR},
      material_code = #{materialCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      merchandise_name = #{merchandiseName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      material_status = #{materialStatus,jdbcType=VARCHAR},
      material_count = #{materialCount,jdbcType=INTEGER},
      material_specification = #{materialSpecification,jdbcType=VARCHAR},
      package_unit = #{packageUnit,jdbcType=VARCHAR},
      min_package_unit = #{minPackageUnit,jdbcType=INTEGER},
      warning_value = #{warningValue,jdbcType=INTEGER},
      storage_requirement = #{storageRequirement,jdbcType=VARCHAR},
      expiration_start_date = #{expirationStartDate,jdbcType=TIMESTAMP},
      expiration_end_date = #{expirationEndDate,jdbcType=TIMESTAMP},
      expiration_warning_value = #{expirationWarningValue,jdbcType=INTEGER},
      warning_status = #{warningStatus,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <select id="getDrugRegisterPage" resultType="com.haoys.user.domain.vo.rcts.DrugRegisterVo">
    SELECT *
    FROM rcts_drug_register_manage
    where status='0'
    <if test="projectId != null">
      and project_id = #{projectId}
    </if>
    <if test="materialStatus != null">
      and material_status = #{materialStatus}
    </if>
    <if test="materialCode != null">
      and material_code = #{materialCode}
    </if>
    <if test="materialName != null">
      and concat(product_name, merchandise_name) like concat('%',#{materialName},'%')
    </if>

  </select>

</mapper>