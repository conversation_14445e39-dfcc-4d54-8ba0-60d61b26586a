<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectOrgUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectOrgUserRole">
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="owner_total_auth" jdbcType="BIT" property="ownerTotalAuth" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    role_id, user_id, project_id, owner_total_auth, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectOrgUserRoleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_org_user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_org_user_role
    where role_id = #{roleId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from project_org_user_role
    where role_id = #{roleId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectOrgUserRoleExample">
    delete from project_org_user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectOrgUserRole">
    insert into project_org_user_role (role_id, user_id, project_id, 
      owner_total_auth, tenant_id, platform_id
      )
    values (#{roleId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{ownerTotalAuth,jdbcType=BIT}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectOrgUserRole">
    insert into project_org_user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="ownerTotalAuth != null">
        owner_total_auth,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="ownerTotalAuth != null">
        #{ownerTotalAuth,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectOrgUserRoleExample" resultType="java.lang.Long">
    select count(*) from project_org_user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_org_user_role
    <set>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.ownerTotalAuth != null">
        owner_total_auth = #{record.ownerTotalAuth,jdbcType=BIT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_org_user_role
    set role_id = #{record.roleId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      owner_total_auth = #{record.ownerTotalAuth,jdbcType=BIT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectOrgUserRole">
    update project_org_user_role
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="ownerTotalAuth != null">
        owner_total_auth = #{ownerTotalAuth,jdbcType=BIT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where role_id = #{roleId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectOrgUserRole">
    update project_org_user_role
    set project_id = #{projectId,jdbcType=BIGINT},
      owner_total_auth = #{ownerTotalAuth,jdbcType=BIT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where role_id = #{roleId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
  </update>
  <select id="countByRoleId" resultType="java.lang.Integer">
    select count(1) from project_org_user_role where role_id=#{roleId}
  </select>

  <select id="selectUseRoleUser" resultType="com.haoys.user.model.SystemUserInfo">
    select u.id,u.username,u.real_name as realName
    from project_org_user_role role,
         system_user_info u
    where role.role_id=u.id and role_id = #{roleId}
  </select>


  <!--删除项目研究中心用户关联角色-->
  <delete id="deleteProjectOrgUserRoleByUserId">
    DELETE FROM project_org_user_role WHERE project_id =#{projectId} and user_id = #{userId}
  </delete>

  <!--查询用户项目研究中心角色关联信息-->
  <select id="getProjectOrgUserRoleListByProjectIdAndUserId" resultType="com.haoys.user.domain.dto.ProjectUserOrgRoleVo">
    SELECT
      ur.user_id userId,
      pr.id AS roleId,
      pr.name AS roleName,
      pr.enname AS ename,
      ore.org_Id as orgId,
      ore.org_Code as orgCode,
      ore.project_org_id projectOrgId,
      org.org_name as orgName,
      ur.owner_total_auth ownerTotalAuth
    FROM
        project_org_user_role ur
        JOIN project_org_role ore ON ur.role_id = ore.id
        JOIN project_role pr ON convert(pr.enname using utf8 ) = convert(ore.ename using utf8) and pr.project_id = #{projectId}
        JOIN system_org_info org ON org.org_id = ore.org_id
    WHERE
      ur.project_id = #{projectId}
      AND ur.user_id = #{userId}
  </select>

  <!--查询用户项目研究中心角色关联-->
  <select id="getProjectOrgUserRoleByUserIdAndRoleId" resultMap="BaseResultMap">
    select  * from project_org_user_role where project_id = #{projectId} and user_id = #{userId} and role_id = #{projectOrgRoleId}
  </select>

  <!--查询授权全部研究中心的项目用户-->
  <select id="getProjectUserOrgRoleByOwnerTotalAuth" resultType="com.haoys.user.domain.vo.auth.ProjectOrgUserRoleVo">
    SELECT
        project_org_role.project_id projectId,
        project_org_role.id roleId,
        project_org_role.role_name roleName,
        project_org_role.ename,
        project_org_role.resource_role_id resourceRoleId,
        project_org_role.org_id orgId,
        project_org_role.project_org_id projectOrgId,
        project_org_role.org_code orgCode,
        project_org_user_role.user_id userId
    FROM
        project_org_user_role
        INNER JOIN project_org_role ON project_org_role.id = project_org_user_role.role_id
    WHERE
        project_org_user_role.project_id = #{projectId} and owner_total_auth = true  GROUP BY project_org_role.ename
  </select>

</mapper>