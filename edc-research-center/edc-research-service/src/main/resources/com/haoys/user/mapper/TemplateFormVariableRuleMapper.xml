<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormVariableRuleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormVariableRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="VARCHAR" property="projectId" />
    <result column="formula_desc" jdbcType="VARCHAR" property="formulaDesc" />
    <result column="form_id" jdbcType="VARCHAR" property="formId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="from_detail_id" jdbcType="INTEGER" property="fromDetailId" />
    <result column="variable_id" jdbcType="VARCHAR" property="variableId" />
    <result column="rule_content" jdbcType="VARCHAR" property="ruleContent" />
    <result column="trigger_timing" jdbcType="INTEGER" property="triggerTiming" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.haoys.user.model.TemplateFormVariableRule">
    <result column="rule_desc" jdbcType="LONGVARCHAR" property="ruleDesc" />
    <result column="rule_details" jdbcType="LONGVARCHAR" property="ruleDetails" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, formula_desc, form_id, group_id, from_detail_id, variable_id, rule_content, 
    trigger_timing, status, create_time, create_user_id, update_time, update_user_id, 
    platform_id, tenant_id
  </sql>
  <sql id="Blob_Column_List">
    rule_desc, rule_details
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.haoys.user.model.TemplateFormVariableRuleExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from template_form_variable_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormVariableRuleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_variable_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from template_form_variable_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_form_variable_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormVariableRuleExample">
    delete from template_form_variable_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormVariableRule">
    insert into template_form_variable_rule (id, project_id, formula_desc, 
      form_id, group_id, from_detail_id, 
      variable_id, rule_content, trigger_timing, 
      status, create_time, create_user_id, 
      update_time, update_user_id, platform_id, 
      tenant_id, rule_desc, rule_details
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=VARCHAR}, #{formulaDesc,jdbcType=VARCHAR}, 
      #{formId,jdbcType=VARCHAR}, #{groupId,jdbcType=BIGINT}, #{fromDetailId,jdbcType=INTEGER}, 
      #{variableId,jdbcType=VARCHAR}, #{ruleContent,jdbcType=VARCHAR}, #{triggerTiming,jdbcType=INTEGER}, 
      #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{ruleDesc,jdbcType=LONGVARCHAR}, #{ruleDetails,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormVariableRule">
    insert into template_form_variable_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="formulaDesc != null">
        formula_desc,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="fromDetailId != null">
        from_detail_id,
      </if>
      <if test="variableId != null">
        variable_id,
      </if>
      <if test="ruleContent != null">
        rule_content,
      </if>
      <if test="triggerTiming != null">
        trigger_timing,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="ruleDesc != null">
        rule_desc,
      </if>
      <if test="ruleDetails != null">
        rule_details,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="formulaDesc != null">
        #{formulaDesc,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="fromDetailId != null">
        #{fromDetailId,jdbcType=INTEGER},
      </if>
      <if test="variableId != null">
        #{variableId,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null">
        #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="triggerTiming != null">
        #{triggerTiming,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null">
        #{ruleDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleDetails != null">
        #{ruleDetails,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormVariableRuleExample" resultType="java.lang.Long">
    select count(*) from template_form_variable_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_variable_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=VARCHAR},
      </if>
      <if test="record.formulaDesc != null">
        formula_desc = #{record.formulaDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.fromDetailId != null">
        from_detail_id = #{record.fromDetailId,jdbcType=INTEGER},
      </if>
      <if test="record.variableId != null">
        variable_id = #{record.variableId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleContent != null">
        rule_content = #{record.ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerTiming != null">
        trigger_timing = #{record.triggerTiming,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.ruleDesc != null">
        rule_desc = #{record.ruleDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.ruleDetails != null">
        rule_details = #{record.ruleDetails,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update template_form_variable_rule
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      formula_desc = #{record.formulaDesc,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=BIGINT},
      from_detail_id = #{record.fromDetailId,jdbcType=INTEGER},
      variable_id = #{record.variableId,jdbcType=VARCHAR},
      rule_content = #{record.ruleContent,jdbcType=VARCHAR},
      trigger_timing = #{record.triggerTiming,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      rule_desc = #{record.ruleDesc,jdbcType=LONGVARCHAR},
      rule_details = #{record.ruleDetails,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_variable_rule
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=VARCHAR},
      formula_desc = #{record.formulaDesc,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=BIGINT},
      from_detail_id = #{record.fromDetailId,jdbcType=INTEGER},
      variable_id = #{record.variableId,jdbcType=VARCHAR},
      rule_content = #{record.ruleContent,jdbcType=VARCHAR},
      trigger_timing = #{record.triggerTiming,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormVariableRule">
    update template_form_variable_rule
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="formulaDesc != null">
        formula_desc = #{formulaDesc,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="fromDetailId != null">
        from_detail_id = #{fromDetailId,jdbcType=INTEGER},
      </if>
      <if test="variableId != null">
        variable_id = #{variableId,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null">
        rule_content = #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="triggerTiming != null">
        trigger_timing = #{triggerTiming,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="ruleDesc != null">
        rule_desc = #{ruleDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="ruleDetails != null">
        rule_details = #{ruleDetails,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.haoys.user.model.TemplateFormVariableRule">
    update template_form_variable_rule
    set project_id = #{projectId,jdbcType=VARCHAR},
      formula_desc = #{formulaDesc,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=BIGINT},
      from_detail_id = #{fromDetailId,jdbcType=INTEGER},
      variable_id = #{variableId,jdbcType=VARCHAR},
      rule_content = #{ruleContent,jdbcType=VARCHAR},
      trigger_timing = #{triggerTiming,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      rule_desc = #{ruleDesc,jdbcType=LONGVARCHAR},
      rule_details = #{ruleDetails,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormVariableRule">
    update template_form_variable_rule
    set project_id = #{projectId,jdbcType=VARCHAR},
      formula_desc = #{formulaDesc,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=BIGINT},
      from_detail_id = #{fromDetailId,jdbcType=INTEGER},
      variable_id = #{variableId,jdbcType=VARCHAR},
      rule_content = #{ruleContent,jdbcType=VARCHAR},
      trigger_timing = #{triggerTiming,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>