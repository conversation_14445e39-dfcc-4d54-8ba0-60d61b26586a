<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemLoginLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="request_ip" jdbcType="VARCHAR" property="requestIp" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="browser" jdbcType="VARCHAR" property="browser" />
    <result column="os" jdbcType="VARCHAR" property="os" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_name, real_name, operate_type, request_ip, location, browser, os, data_from, 
    status, message, login_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemLoginLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_login_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_login_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemLoginLogExample">
    delete from system_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemLoginLog">
    insert into system_login_log (id, user_name, real_name, 
      operate_type, request_ip, location, 
      browser, os, data_from, 
      status, message, login_time
      )
    values (#{id,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{realName,jdbcType=VARCHAR}, 
      #{operateType,jdbcType=VARCHAR}, #{requestIp,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{browser,jdbcType=VARCHAR}, #{os,jdbcType=VARCHAR}, #{dataFrom,jdbcType=VARCHAR}, 
      #{status,jdbcType=CHAR}, #{message,jdbcType=VARCHAR}, #{loginTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemLoginLog">
    insert into system_login_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="realName != null">
        real_name,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="requestIp != null">
        request_ip,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="browser != null">
        browser,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="loginTime != null">
        login_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="requestIp != null">
        #{requestIp,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="browser != null">
        #{browser,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        #{os,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="loginTime != null">
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemLoginLogExample" resultType="java.lang.Long">
    select count(*) from system_login_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_login_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.realName != null">
        real_name = #{record.realName,jdbcType=VARCHAR},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=VARCHAR},
      </if>
      <if test="record.requestIp != null">
        request_ip = #{record.requestIp,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.browser != null">
        browser = #{record.browser,jdbcType=VARCHAR},
      </if>
      <if test="record.os != null">
        os = #{record.os,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=CHAR},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.loginTime != null">
        login_time = #{record.loginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_login_log
    set id = #{record.id,jdbcType=BIGINT},
      user_name = #{record.userName,jdbcType=VARCHAR},
      real_name = #{record.realName,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=VARCHAR},
      request_ip = #{record.requestIp,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      browser = #{record.browser,jdbcType=VARCHAR},
      os = #{record.os,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=CHAR},
      message = #{record.message,jdbcType=VARCHAR},
      login_time = #{record.loginTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemLoginLog">
    update system_login_log
    <set>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="realName != null">
        real_name = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="requestIp != null">
        request_ip = #{requestIp,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="browser != null">
        browser = #{browser,jdbcType=VARCHAR},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="loginTime != null">
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemLoginLog">
    update system_login_log
    set user_name = #{userName,jdbcType=VARCHAR},
      real_name = #{realName,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=VARCHAR},
      request_ip = #{requestIp,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      browser = #{browser,jdbcType=VARCHAR},
      os = #{os,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      status = #{status,jdbcType=CHAR},
      message = #{message,jdbcType=VARCHAR},
      login_time = #{loginTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>


    <select id="selectList" parameterType="com.haoys.user.domain.entity.SystemLoginLogQuery" resultType="com.haoys.user.domain.vo.system.SystemLoginLogVo">
      select
        system_login_log.*,system_org_info.org_name orgName,system_user_info.real_name realName
      from system_login_log
      LEFT JOIN system_user_info on system_login_log.user_name = system_user_info.username
      LEFT JOIN system_org_info on system_user_info.department = system_org_info.org_id
      <where>
          <if test="requestIp != null and requestIp != ''">
              AND request_ip like concat('%', #{requestIp}, '%')
          </if>
          <if test="status != null and status != ''">
              AND status = #{status}
          </if>
          <if test="userName != null and userName != ''">
            AND
            (
            system_login_log.real_name like concat('%', #{userName}, '%')
            or
            user_name like concat('%', #{userName}, '%')
            )
          </if>
          <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
            and date_format(system_login_log.login_time,'%Y-%m-%d') between date_format(#{beginTime},'%Y-%m-%d') and date_format(#{endTime},'%Y-%m-%d')
          </if>
          <if test="operateType != null and operateType != ''">
            AND operate_type = #{operateType}
          </if>
        </where>
        order by id desc
    </select>

    <delete id="deleteLogininforByIds" parameterType="Long">
        delete from system_login_log where id in
        <foreach collection="array" item="ids" open="(" separator="," close=")">
            #{infoId}
        </foreach>
    </delete>

    <update id="cleanLogininfor">
        truncate table system_login_log
    </update>

  <select id="selectSystemLoginLogList" parameterType="com.haoys.user.domain.entity.SystemLoginLogQuery" resultMap="BaseResultMap">
    select system_login_log.*,system_org_info.org_name orgName from system_login_log
    LEFT JOIN system_user_info on system_login_log.user_name = system_user_info.username
    LEFT JOIN system_org_info on system_user_info.department = system_org_info.org_id
    <where>
      <if test="requestIp != null and requestIp != ''">
        AND request_ip like concat('%', #{requestIp}, '%')
      </if>
      <if test="status != null and status != ''">
        AND status = #{status}
      </if>
      <if test="userName != null and userName != ''">
        AND
        (
        real_name like concat('%', #{userName}, '%')
        or
        user_name like concat('%', #{userName}, '%')
        )
      </if>
      <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
        and date_format(login_time,'%Y-%m-%d %H:%T:%s') &gt;= date_format(#{beginTime},'%Y-%m-%d %H:%T:%s')
      </if>
      <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
        and date_format(login_time,'%Y-%m-%d %H:%T:%s') &lt;= date_format(#{endTime},'%Y-%m-%d %H:%T:%s')
      </if>
      <if test="operateType != null and operateType != ''">
        AND operate_type = #{operateType}
      </if>
    </where>
    order by id desc
  </select>

</mapper>