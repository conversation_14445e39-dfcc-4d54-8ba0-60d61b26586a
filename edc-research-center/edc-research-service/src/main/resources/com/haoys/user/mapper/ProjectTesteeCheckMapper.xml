<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeCheckMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeCheck">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="visit_name" jdbcType="VARCHAR" property="visitName" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="testee_code" jdbcType="VARCHAR" property="testeeCode" />
    <result column="variable_id" jdbcType="BIGINT" property="variableId" />
    <result column="variable_table_id" jdbcType="BIGINT" property="variableTableId" />
    <result column="variable_result_id" jdbcType="BIGINT" property="variableResultId" />
    <result column="variable_name" jdbcType="VARCHAR" property="variableName" />
    <result column="variable_type" jdbcType="VARCHAR" property="variableType" />
    <result column="input_value" jdbcType="VARCHAR" property="inputValue" />
    <result column="field_text" jdbcType="VARCHAR" property="fieldText" />
    <result column="unit_value" jdbcType="VARCHAR" property="unitValue" />
    <result column="unit_text" jdbcType="VARCHAR" property="unitText" />
    <result column="original_value" jdbcType="VARCHAR" property="originalValue" />
    <result column="project_role_code" jdbcType="VARCHAR" property="projectRoleCode" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="visit_ip" jdbcType="VARCHAR" property="visitIp" />
    <result column="log_type" jdbcType="VARCHAR" property="logType" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, plan_name, visit_id, visit_name, form_id, form_name, testee_id, 
    testee_code, variable_id, variable_table_id, variable_result_id, variable_name, variable_type, 
    input_value, field_text, unit_value, unit_text, original_value, project_role_code, 
    type, content, expand, status, visit_ip, log_type, data_from, create_user_id, create_user_name, 
    create_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeCheckExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_testee_check
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_check
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeCheckExample">
    delete from project_testee_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeCheck">
    insert into project_testee_check (id, project_id, plan_id, 
      plan_name, visit_id, visit_name, 
      form_id, form_name, testee_id, 
      testee_code, variable_id, variable_table_id, 
      variable_result_id, variable_name, variable_type, 
      input_value, field_text, unit_value, 
      unit_text, original_value, project_role_code, 
      type, content, expand, 
      status, visit_ip, log_type, 
      data_from, create_user_id, create_user_name, 
      create_time, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{planName,jdbcType=VARCHAR}, #{visitId,jdbcType=BIGINT}, #{visitName,jdbcType=VARCHAR}, 
      #{formId,jdbcType=BIGINT}, #{formName,jdbcType=VARCHAR}, #{testeeId,jdbcType=BIGINT}, 
      #{testeeCode,jdbcType=VARCHAR}, #{variableId,jdbcType=BIGINT}, #{variableTableId,jdbcType=BIGINT}, 
      #{variableResultId,jdbcType=BIGINT}, #{variableName,jdbcType=VARCHAR}, #{variableType,jdbcType=VARCHAR}, 
      #{inputValue,jdbcType=VARCHAR}, #{fieldText,jdbcType=VARCHAR}, #{unitValue,jdbcType=VARCHAR}, 
      #{unitText,jdbcType=VARCHAR}, #{originalValue,jdbcType=VARCHAR}, #{projectRoleCode,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{visitIp,jdbcType=VARCHAR}, #{logType,jdbcType=VARCHAR}, 
      #{dataFrom,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createUserName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeCheck">
    insert into project_testee_check
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="planName != null">
        plan_name,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="visitName != null">
        visit_name,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="testeeCode != null">
        testee_code,
      </if>
      <if test="variableId != null">
        variable_id,
      </if>
      <if test="variableTableId != null">
        variable_table_id,
      </if>
      <if test="variableResultId != null">
        variable_result_id,
      </if>
      <if test="variableName != null">
        variable_name,
      </if>
      <if test="variableType != null">
        variable_type,
      </if>
      <if test="inputValue != null">
        input_value,
      </if>
      <if test="fieldText != null">
        field_text,
      </if>
      <if test="unitValue != null">
        unit_value,
      </if>
      <if test="unitText != null">
        unit_text,
      </if>
      <if test="originalValue != null">
        original_value,
      </if>
      <if test="projectRoleCode != null">
        project_role_code,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="visitIp != null">
        visit_ip,
      </if>
      <if test="logType != null">
        log_type,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createUserName != null">
        create_user_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="visitName != null">
        #{visitName,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeCode != null">
        #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="variableId != null">
        #{variableId,jdbcType=BIGINT},
      </if>
      <if test="variableTableId != null">
        #{variableTableId,jdbcType=BIGINT},
      </if>
      <if test="variableResultId != null">
        #{variableResultId,jdbcType=BIGINT},
      </if>
      <if test="variableName != null">
        #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="variableType != null">
        #{variableType,jdbcType=VARCHAR},
      </if>
      <if test="inputValue != null">
        #{inputValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null">
        #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="unitText != null">
        #{unitText,jdbcType=VARCHAR},
      </if>
      <if test="originalValue != null">
        #{originalValue,jdbcType=VARCHAR},
      </if>
      <if test="projectRoleCode != null">
        #{projectRoleCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="visitIp != null">
        #{visitIp,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null">
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeCheckExample" resultType="java.lang.Long">
    select count(*) from project_testee_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_check
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.planName != null">
        plan_name = #{record.planName,jdbcType=VARCHAR},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.visitName != null">
        visit_name = #{record.visitName,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeCode != null">
        testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.variableId != null">
        variable_id = #{record.variableId,jdbcType=BIGINT},
      </if>
      <if test="record.variableTableId != null">
        variable_table_id = #{record.variableTableId,jdbcType=BIGINT},
      </if>
      <if test="record.variableResultId != null">
        variable_result_id = #{record.variableResultId,jdbcType=BIGINT},
      </if>
      <if test="record.variableName != null">
        variable_name = #{record.variableName,jdbcType=VARCHAR},
      </if>
      <if test="record.variableType != null">
        variable_type = #{record.variableType,jdbcType=VARCHAR},
      </if>
      <if test="record.inputValue != null">
        input_value = #{record.inputValue,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldText != null">
        field_text = #{record.fieldText,jdbcType=VARCHAR},
      </if>
      <if test="record.unitValue != null">
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
      </if>
      <if test="record.unitText != null">
        unit_text = #{record.unitText,jdbcType=VARCHAR},
      </if>
      <if test="record.originalValue != null">
        original_value = #{record.originalValue,jdbcType=VARCHAR},
      </if>
      <if test="record.projectRoleCode != null">
        project_role_code = #{record.projectRoleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.visitIp != null">
        visit_ip = #{record.visitIp,jdbcType=VARCHAR},
      </if>
      <if test="record.logType != null">
        log_type = #{record.logType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserName != null">
        create_user_name = #{record.createUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_check
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      plan_name = #{record.planName,jdbcType=VARCHAR},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      visit_name = #{record.visitName,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_name = #{record.formName,jdbcType=VARCHAR},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      testee_code = #{record.testeeCode,jdbcType=VARCHAR},
      variable_id = #{record.variableId,jdbcType=BIGINT},
      variable_table_id = #{record.variableTableId,jdbcType=BIGINT},
      variable_result_id = #{record.variableResultId,jdbcType=BIGINT},
      variable_name = #{record.variableName,jdbcType=VARCHAR},
      variable_type = #{record.variableType,jdbcType=VARCHAR},
      input_value = #{record.inputValue,jdbcType=VARCHAR},
      field_text = #{record.fieldText,jdbcType=VARCHAR},
      unit_value = #{record.unitValue,jdbcType=VARCHAR},
      unit_text = #{record.unitText,jdbcType=VARCHAR},
      original_value = #{record.originalValue,jdbcType=VARCHAR},
      project_role_code = #{record.projectRoleCode,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      visit_ip = #{record.visitIp,jdbcType=VARCHAR},
      log_type = #{record.logType,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_user_name = #{record.createUserName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeCheck">
    update project_testee_check
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="planName != null">
        plan_name = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="visitName != null">
        visit_name = #{visitName,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeCode != null">
        testee_code = #{testeeCode,jdbcType=VARCHAR},
      </if>
      <if test="variableId != null">
        variable_id = #{variableId,jdbcType=BIGINT},
      </if>
      <if test="variableTableId != null">
        variable_table_id = #{variableTableId,jdbcType=BIGINT},
      </if>
      <if test="variableResultId != null">
        variable_result_id = #{variableResultId,jdbcType=BIGINT},
      </if>
      <if test="variableName != null">
        variable_name = #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="variableType != null">
        variable_type = #{variableType,jdbcType=VARCHAR},
      </if>
      <if test="inputValue != null">
        input_value = #{inputValue,jdbcType=VARCHAR},
      </if>
      <if test="fieldText != null">
        field_text = #{fieldText,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        unit_value = #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="unitText != null">
        unit_text = #{unitText,jdbcType=VARCHAR},
      </if>
      <if test="originalValue != null">
        original_value = #{originalValue,jdbcType=VARCHAR},
      </if>
      <if test="projectRoleCode != null">
        project_role_code = #{projectRoleCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="visitIp != null">
        visit_ip = #{visitIp,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        log_type = #{logType,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null">
        create_user_name = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeCheck">
    update project_testee_check
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      plan_name = #{planName,jdbcType=VARCHAR},
      visit_id = #{visitId,jdbcType=BIGINT},
      visit_name = #{visitName,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=BIGINT},
      form_name = #{formName,jdbcType=VARCHAR},
      testee_id = #{testeeId,jdbcType=BIGINT},
      testee_code = #{testeeCode,jdbcType=VARCHAR},
      variable_id = #{variableId,jdbcType=BIGINT},
      variable_table_id = #{variableTableId,jdbcType=BIGINT},
      variable_result_id = #{variableResultId,jdbcType=BIGINT},
      variable_name = #{variableName,jdbcType=VARCHAR},
      variable_type = #{variableType,jdbcType=VARCHAR},
      input_value = #{inputValue,jdbcType=VARCHAR},
      field_text = #{fieldText,jdbcType=VARCHAR},
      unit_value = #{unitValue,jdbcType=VARCHAR},
      unit_text = #{unitText,jdbcType=VARCHAR},
      original_value = #{originalValue,jdbcType=VARCHAR},
      project_role_code = #{projectRoleCode,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      visit_ip = #{visitIp,jdbcType=VARCHAR},
      log_type = #{logType,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_user_name = #{createUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--数据稽查日志分页列表-->
  <select id="getProjectCheckListForPage" resultType="com.haoys.user.domain.vo.testee.ProjectTesteeCheckVo">
    SELECT
        <include refid="Base_Column_List" />
    FROM project_testee_check
    WHERE 1 = 1 AND project_testee_check.project_id = #{projectId} AND project_testee_check.status = '0'
    <if test="testeeCode != null and testeeCode != ''">
      AND project_testee_check.testee_code = #{testeeCode}
    </if>
    <if test="createUserName != null and createUserName != ''">
      AND project_testee_check.create_user_name = #{createUserName}
    </if>
    <if test="type != null and type != ''">
      AND project_testee_check.type = #{type}
    </if>
    <if test="startDate != null and startDate != ''">
      AND project_testee_check.create_time &gt; #{startDate}
    </if>
    <if test="endDate != null and endDate != ''">
      AND project_testee_check.create_time &lt; #{endDate}
    </if>
    ORDER BY project_testee_check.create_time DESC
  </select>

  <update id="cleanAllProjectCheckLog">
    truncate table project_testee_check
  </update>

  <delete id="deleteProjectCheckLog">
    delete from project_testee_check where project_id =#{projectId} and visit_id =#{visitId} and form_id =#{formId} and testee_id =#{testeeId}
  </delete>



</mapper>