<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectMenuMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectMenu">
    <id column="project_id" jdbcType="BIGINT" property="projectId" />
    <id column="menu_id" jdbcType="BIGINT" property="menuId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    project_id, menu_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectMenuExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_menu
    where project_id = #{projectId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from project_menu
    where project_id = #{projectId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectMenuExample">
    delete from project_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectMenu">
    insert into project_menu (project_id, menu_id, tenant_id, 
      platform_id)
    values (#{projectId,jdbcType=BIGINT}, #{menuId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectMenu">
    insert into project_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="menuId != null">
        menu_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        #{menuId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectMenuExample" resultType="java.lang.Long">
    select count(*) from project_menu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_menu
    <set>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.menuId != null">
        menu_id = #{record.menuId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_menu
    set project_id = #{record.projectId,jdbcType=BIGINT},
      menu_id = #{record.menuId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectMenu">
    update project_menu
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where project_id = #{projectId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectMenu">
    update project_menu
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where project_id = #{projectId,jdbcType=BIGINT}
      and menu_id = #{menuId,jdbcType=BIGINT}
  </update>


    <insert id="batchSaveProjectMenuList">
        insert into project_menu(project_id, menu_id, tenant_id, platform_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.projectId} ,#{item.menuId}, #{item.tenantId}, #{item.platformId})
        </foreach>
    </insert>

    <delete id="deleteProjectMenuByProjectId" parameterType="java.lang.Long">
		delete from project_menu where project_id=#{projectId}
	</delete>

    <select id="selectProjectMenuByProjectId" parameterType="java.lang.Long" resultMap="BaseResultMap">
		select project_id,menu_id from project_menu where project_id = #{projectId}
	</select>

  <!--查询项目角色模版菜单信息-->
  <select id="selectProjectTemplateMenuInfo" resultType="com.haoys.user.domain.vo.auth.ProjectMenuVo">
    SELECT
        project_role.project_id projectId,project_role.`name`,project_role.enname,project_role_menu.menu_id menuId
    FROM
        project_role
    INNER JOIN project_role_menu ON project_role_menu.role_id = project_role.id
    INNER JOIN project_menu ON project_menu.menu_id = project_role_menu.menu_id AND project_menu.project_id = #{projectId}
    WHERE project_role.status = '0' and project_role.enname = #{roleCode} AND project_role.project_template = true AND project_role.project_id IS NULL
  </select>

  <!--查询项目角色模版菜单列表-->
  <select id="getProjectMenuListForTemplate" resultType="com.haoys.user.domain.entity.ProjectMenuQuery">
    select  project_id projectId, menu_id menuId from project_menu where project_menu.project_id = #{projectId}
  </select>


</mapper>