<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectResearchersInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectResearchersInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="group_info" jdbcType="VARCHAR" property="groupInfo" />
    <result column="hospital" jdbcType="VARCHAR" property="hospital" />
    <result column="dept" jdbcType="VARCHAR" property="dept" />
    <result column="open_bank" jdbcType="VARCHAR" property="openBank" />
    <result column="identity_card" jdbcType="VARCHAR" property="identityCard" />
    <result column="tel_phone" jdbcType="VARCHAR" property="telPhone" />
    <result column="id_card_face_photo" jdbcType="VARCHAR" property="idCardFacePhoto" />
    <result column="id_card_back_photo" jdbcType="VARCHAR" property="idCardBackPhoto" />
    <result column="bank_number" jdbcType="VARCHAR" property="bankNumber" />
    <result column="project_volunteer_phone" jdbcType="VARCHAR" property="projectVolunteerPhone" />
    <result column="project_volunteer" jdbcType="VARCHAR" property="projectVolunteer" />
    <result column="certificate" jdbcType="VARCHAR" property="certificate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, name, area, group_info, hospital, dept, open_bank, identity_card, 
    tel_phone, id_card_face_photo, id_card_back_photo, bank_number, project_volunteer_phone, 
    project_volunteer, certificate, status, create_time, create_user, update_time, update_user, 
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectResearchersInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_researchers_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_researchers_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_researchers_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectResearchersInfoExample">
    delete from project_researchers_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectResearchersInfo">
    insert into project_researchers_info (id, project_id, name, 
      area, group_info, hospital, 
      dept, open_bank, identity_card, 
      tel_phone, id_card_face_photo, id_card_back_photo, 
      bank_number, project_volunteer_phone, project_volunteer, 
      certificate, status, create_time, 
      create_user, update_time, update_user, 
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{groupInfo,jdbcType=VARCHAR}, #{hospital,jdbcType=VARCHAR}, 
      #{dept,jdbcType=VARCHAR}, #{openBank,jdbcType=VARCHAR}, #{identityCard,jdbcType=VARCHAR}, 
      #{telPhone,jdbcType=VARCHAR}, #{idCardFacePhoto,jdbcType=VARCHAR}, #{idCardBackPhoto,jdbcType=VARCHAR}, 
      #{bankNumber,jdbcType=VARCHAR}, #{projectVolunteerPhone,jdbcType=VARCHAR}, #{projectVolunteer,jdbcType=VARCHAR}, 
      #{certificate,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR}, 
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectResearchersInfo">
    insert into project_researchers_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="groupInfo != null">
        group_info,
      </if>
      <if test="hospital != null">
        hospital,
      </if>
      <if test="dept != null">
        dept,
      </if>
      <if test="openBank != null">
        open_bank,
      </if>
      <if test="identityCard != null">
        identity_card,
      </if>
      <if test="telPhone != null">
        tel_phone,
      </if>
      <if test="idCardFacePhoto != null">
        id_card_face_photo,
      </if>
      <if test="idCardBackPhoto != null">
        id_card_back_photo,
      </if>
      <if test="bankNumber != null">
        bank_number,
      </if>
      <if test="projectVolunteerPhone != null">
        project_volunteer_phone,
      </if>
      <if test="projectVolunteer != null">
        project_volunteer,
      </if>
      <if test="certificate != null">
        certificate,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="groupInfo != null">
        #{groupInfo,jdbcType=VARCHAR},
      </if>
      <if test="hospital != null">
        #{hospital,jdbcType=VARCHAR},
      </if>
      <if test="dept != null">
        #{dept,jdbcType=VARCHAR},
      </if>
      <if test="openBank != null">
        #{openBank,jdbcType=VARCHAR},
      </if>
      <if test="identityCard != null">
        #{identityCard,jdbcType=VARCHAR},
      </if>
      <if test="telPhone != null">
        #{telPhone,jdbcType=VARCHAR},
      </if>
      <if test="idCardFacePhoto != null">
        #{idCardFacePhoto,jdbcType=VARCHAR},
      </if>
      <if test="idCardBackPhoto != null">
        #{idCardBackPhoto,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null">
        #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="projectVolunteerPhone != null">
        #{projectVolunteerPhone,jdbcType=VARCHAR},
      </if>
      <if test="projectVolunteer != null">
        #{projectVolunteer,jdbcType=VARCHAR},
      </if>
      <if test="certificate != null">
        #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectResearchersInfoExample" resultType="java.lang.Long">
    select count(*) from project_researchers_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_researchers_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.groupInfo != null">
        group_info = #{record.groupInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.hospital != null">
        hospital = #{record.hospital,jdbcType=VARCHAR},
      </if>
      <if test="record.dept != null">
        dept = #{record.dept,jdbcType=VARCHAR},
      </if>
      <if test="record.openBank != null">
        open_bank = #{record.openBank,jdbcType=VARCHAR},
      </if>
      <if test="record.identityCard != null">
        identity_card = #{record.identityCard,jdbcType=VARCHAR},
      </if>
      <if test="record.telPhone != null">
        tel_phone = #{record.telPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.idCardFacePhoto != null">
        id_card_face_photo = #{record.idCardFacePhoto,jdbcType=VARCHAR},
      </if>
      <if test="record.idCardBackPhoto != null">
        id_card_back_photo = #{record.idCardBackPhoto,jdbcType=VARCHAR},
      </if>
      <if test="record.bankNumber != null">
        bank_number = #{record.bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.projectVolunteerPhone != null">
        project_volunteer_phone = #{record.projectVolunteerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.projectVolunteer != null">
        project_volunteer = #{record.projectVolunteer,jdbcType=VARCHAR},
      </if>
      <if test="record.certificate != null">
        certificate = #{record.certificate,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_researchers_info
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      group_info = #{record.groupInfo,jdbcType=VARCHAR},
      hospital = #{record.hospital,jdbcType=VARCHAR},
      dept = #{record.dept,jdbcType=VARCHAR},
      open_bank = #{record.openBank,jdbcType=VARCHAR},
      identity_card = #{record.identityCard,jdbcType=VARCHAR},
      tel_phone = #{record.telPhone,jdbcType=VARCHAR},
      id_card_face_photo = #{record.idCardFacePhoto,jdbcType=VARCHAR},
      id_card_back_photo = #{record.idCardBackPhoto,jdbcType=VARCHAR},
      bank_number = #{record.bankNumber,jdbcType=VARCHAR},
      project_volunteer_phone = #{record.projectVolunteerPhone,jdbcType=VARCHAR},
      project_volunteer = #{record.projectVolunteer,jdbcType=VARCHAR},
      certificate = #{record.certificate,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectResearchersInfo">
    update project_researchers_info
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="groupInfo != null">
        group_info = #{groupInfo,jdbcType=VARCHAR},
      </if>
      <if test="hospital != null">
        hospital = #{hospital,jdbcType=VARCHAR},
      </if>
      <if test="dept != null">
        dept = #{dept,jdbcType=VARCHAR},
      </if>
      <if test="openBank != null">
        open_bank = #{openBank,jdbcType=VARCHAR},
      </if>
      <if test="identityCard != null">
        identity_card = #{identityCard,jdbcType=VARCHAR},
      </if>
      <if test="telPhone != null">
        tel_phone = #{telPhone,jdbcType=VARCHAR},
      </if>
      <if test="idCardFacePhoto != null">
        id_card_face_photo = #{idCardFacePhoto,jdbcType=VARCHAR},
      </if>
      <if test="idCardBackPhoto != null">
        id_card_back_photo = #{idCardBackPhoto,jdbcType=VARCHAR},
      </if>
      <if test="bankNumber != null">
        bank_number = #{bankNumber,jdbcType=VARCHAR},
      </if>
      <if test="projectVolunteerPhone != null">
        project_volunteer_phone = #{projectVolunteerPhone,jdbcType=VARCHAR},
      </if>
      <if test="projectVolunteer != null">
        project_volunteer = #{projectVolunteer,jdbcType=VARCHAR},
      </if>
      <if test="certificate != null">
        certificate = #{certificate,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectResearchersInfo">
    update project_researchers_info
    set project_id = #{projectId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      group_info = #{groupInfo,jdbcType=VARCHAR},
      hospital = #{hospital,jdbcType=VARCHAR},
      dept = #{dept,jdbcType=VARCHAR},
      open_bank = #{openBank,jdbcType=VARCHAR},
      identity_card = #{identityCard,jdbcType=VARCHAR},
      tel_phone = #{telPhone,jdbcType=VARCHAR},
      id_card_face_photo = #{idCardFacePhoto,jdbcType=VARCHAR},
      id_card_back_photo = #{idCardBackPhoto,jdbcType=VARCHAR},
      bank_number = #{bankNumber,jdbcType=VARCHAR},
      project_volunteer_phone = #{projectVolunteerPhone,jdbcType=VARCHAR},
      project_volunteer = #{projectVolunteer,jdbcType=VARCHAR},
      certificate = #{certificate,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getProjectResearchersInfoByCreateUser" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from project_researchers_info where  create_user = #{userId,jdbcType=VARCHAR} limit 1
  </select>

</mapper>