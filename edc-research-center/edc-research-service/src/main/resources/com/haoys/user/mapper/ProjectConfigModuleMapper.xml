<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectConfigModuleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectConfigModule">
        <id column="module_id" jdbcType="BIGINT" property="moduleId" />
        <result column="module_name" jdbcType="VARCHAR" property="moduleName" />
        <result column="source_from" jdbcType="VARCHAR" property="sourceFrom" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
        <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    </resultMap>

    <!-- 基础列列表 -->
    <sql id="Base_Column_List">
        module_id, module_name, source_from, sort, create_user_id, 
        create_time, tenant_id, platform_id
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.haoys.user.model.ProjectConfigModule">
        INSERT INTO project_config_module (
            module_id, module_name, source_from, sort, create_user_id, 
            create_time, tenant_id, platform_id
        ) VALUES (
            #{moduleId,jdbcType=BIGINT}, #{moduleName,jdbcType=VARCHAR}, #{sourceFrom,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{createUserId,jdbcType=VARCHAR}, 
            #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectConfigModule">
        INSERT INTO project_config_module
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="moduleId != null">
                module_id,
            </if>
            <if test="moduleName != null">
                module_name,
            </if>
            <if test="sourceFrom != null">
                source_from,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="platformId != null">
                platform_id,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="moduleId != null">
                #{moduleId,jdbcType=BIGINT},
            </if>
            <if test="moduleName != null">
                #{moduleName,jdbcType=VARCHAR},
            </if>
            <if test="sourceFrom != null">
                #{sourceFrom,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                #{platformId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM project_config_module
        WHERE module_id = #{moduleId}
    </delete>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectConfigModule">
        UPDATE project_config_module SET
            module_name = #{moduleName,jdbcType=VARCHAR},
            source_from = #{sourceFrom,jdbcType=VARCHAR},
            sort = #{sort,jdbcType=INTEGER},
            create_user_id = #{createUserId,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            tenant_id = #{tenantId,jdbcType=VARCHAR},
            platform_id = #{platformId,jdbcType=VARCHAR}
        WHERE module_id = #{moduleId}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectConfigModule">
        UPDATE project_config_module
        <set>
            <if test="moduleName != null">
                module_name = #{moduleName,jdbcType=VARCHAR},
            </if>
            <if test="sourceFrom != null">
                source_from = #{sourceFrom,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="platformId != null">
                platform_id = #{platformId,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE module_id = #{moduleId}
    </update>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        WHERE module_id = #{moduleId}
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO project_config_module (
            module_id, module_name, source_from, sort, create_user_id, 
            create_time, tenant_id, platform_id
        ) VALUES
        <foreach collection="records" item="item" separator=",">
            (
                #{item.moduleId,jdbcType=BIGINT}, #{item.moduleName,jdbcType=VARCHAR}, #{item.sourceFrom,jdbcType=VARCHAR}, #{item.sort,jdbcType=INTEGER}, #{item.createUserId,jdbcType=VARCHAR}, 
                #{item.createTime,jdbcType=TIMESTAMP}, #{item.tenantId,jdbcType=VARCHAR}, #{item.platformId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 批量删除 -->
    <delete id="batchDeleteByIds" parameterType="java.util.List">
        DELETE FROM project_config_module
        WHERE module_id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="records" item="item" separator=";">
            UPDATE project_config_module SET
                module_name = #{item.moduleName,jdbcType=VARCHAR},
                source_from = #{item.sourceFrom,jdbcType=VARCHAR},
                sort = #{item.sort,jdbcType=INTEGER},
                create_user_id = #{item.createUserId,jdbcType=VARCHAR},
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
                tenant_id = #{item.tenantId,jdbcType=VARCHAR},
                platform_id = #{item.platformId,jdbcType=VARCHAR}
            WHERE module_id = #{item.moduleId}
        </foreach>
    </update>

    <!-- 根据条件查询列表 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 根据条件查询单个对象 -->
    <select id="selectOneByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
        LIMIT 1
    </select>

    <!-- 根据条件统计数量 -->
    <select id="countByCondition" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(*) FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 多条件AND查询 -->
    <select id="selectByMultipleConditions" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        <where>
            <if test="conditions != null">
                <foreach collection="conditions" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 范围查询 -->
    <select id="selectByRange" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        <where>
            <if test="field != null and field != ''">
                <if test="startValue != null">
                    AND ${field} &gt;= #{startValue}
                </if>
                <if test="endValue != null">
                    AND ${field} &lt;= #{endValue}
                </if>
            </if>
        </where>
    </select>

    <!-- 模糊查询 -->
    <select id="selectByLike" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        <where>
            <if test="field != null and field != '' and keyword != null and keyword != ''">
                ${field} LIKE CONCAT('%', #{keyword}, '%')
            </if>
        </where>
    </select>

    <!-- IN查询 -->
    <select id="selectByIn" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM project_config_module
        <where>
            <if test="field != null and field != '' and values != null and values.size() > 0">
                ${field} IN
                <foreach collection="values" item="value" open="(" separator="," close=")">
                    #{value}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 总数统计 -->
    <select id="countTotal" parameterType="java.util.Map" resultType="long">
        SELECT COUNT(*) FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 求和查询 -->
    <select id="sumByColumn" parameterType="java.util.Map" resultType="java.lang.Double">
        SELECT SUM(${column}) FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 平均值查询 -->
    <select id="avgByColumn" parameterType="java.util.Map" resultType="java.lang.Double">
        SELECT AVG(${column}) FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 最大值查询 -->
    <select id="maxByColumn" parameterType="java.util.Map" resultType="java.lang.Object">
        SELECT MAX(${column}) FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 最小值查询 -->
    <select id="minByColumn" parameterType="java.util.Map" resultType="java.lang.Object">
        SELECT MIN(${column}) FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
    </select>

    <!-- 分组统计 -->
    <select id="groupByColumn" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT ${groupColumn}, COUNT(*) as count FROM project_config_module
        <where>
            <if test="params != null">
                <foreach collection="params" index="key" item="value">
                    <if test="value != null">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
        </where>
        GROUP BY ${groupColumn}
    </select>

    <!-- 执行自定义SQL查询 -->
    <select id="executeCustomQuery" parameterType="java.util.Map" resultType="java.util.Map">
        ${sql}
    </select>

    <!-- 执行自定义SQL更新 -->
    <update id="executeCustomUpdate" parameterType="java.util.Map">
        ${sql}
    </update>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${tableName}
    </select>

    <!-- 统计总记录数 -->
    <select id="countAll" resultType="long">
        SELECT COUNT(*) FROM ${tableName}
    </select>

</mapper>
