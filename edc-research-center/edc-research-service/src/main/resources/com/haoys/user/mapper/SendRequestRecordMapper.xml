<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SendRequestRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SendRequestRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="resend_flag" jdbcType="VARCHAR" property="resendFlag" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="testee_name" jdbcType="VARCHAR" property="testeeName" />
    <result column="bussiness_id" jdbcType="VARCHAR" property="bussinessId" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.haoys.user.model.SendRequestRecord">
    <result column="params" jdbcType="LONGVARCHAR" property="params" />
    <result column="return_data" jdbcType="LONGVARCHAR" property="returnData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_name, url, status, resend_flag, project_id, testee_id, testee_name, 
    bussiness_id, operator, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    params, return_data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.haoys.user.model.SendRequestRecordExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from send_request_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.haoys.user.model.SendRequestRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from send_request_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from send_request_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from send_request_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SendRequestRecordExample">
    delete from send_request_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SendRequestRecord">
    insert into send_request_record (id, platform_name, url, 
      status, resend_flag, project_id, 
      testee_id, testee_name, bussiness_id, 
      operator, create_time, update_time, 
      params, return_data)
    values (#{id,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{resendFlag,jdbcType=VARCHAR}, #{projectId,jdbcType=BIGINT}, 
      #{testeeId,jdbcType=BIGINT}, #{testeeName,jdbcType=VARCHAR}, #{bussinessId,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{params,jdbcType=LONGVARCHAR}, #{returnData,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SendRequestRecord">
    insert into send_request_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="resendFlag != null">
        resend_flag,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="testeeName != null">
        testee_name,
      </if>
      <if test="bussinessId != null">
        bussiness_id,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="params != null">
        params,
      </if>
      <if test="returnData != null">
        return_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="resendFlag != null">
        #{resendFlag,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeName != null">
        #{testeeName,jdbcType=VARCHAR},
      </if>
      <if test="bussinessId != null">
        #{bussinessId,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params != null">
        #{params,jdbcType=LONGVARCHAR},
      </if>
      <if test="returnData != null">
        #{returnData,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SendRequestRecordExample" resultType="java.lang.Long">
    select count(*) from send_request_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update send_request_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.resendFlag != null">
        resend_flag = #{record.resendFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeName != null">
        testee_name = #{record.testeeName,jdbcType=VARCHAR},
      </if>
      <if test="record.bussinessId != null">
        bussiness_id = #{record.bussinessId,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.params != null">
        params = #{record.params,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.returnData != null">
        return_data = #{record.returnData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update send_request_record
    set id = #{record.id,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      resend_flag = #{record.resendFlag,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      testee_name = #{record.testeeName,jdbcType=VARCHAR},
      bussiness_id = #{record.bussinessId,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      params = #{record.params,jdbcType=LONGVARCHAR},
      return_data = #{record.returnData,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update send_request_record
    set id = #{record.id,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      resend_flag = #{record.resendFlag,jdbcType=VARCHAR},
      project_id = #{record.projectId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      testee_name = #{record.testeeName,jdbcType=VARCHAR},
      bussiness_id = #{record.bussinessId,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SendRequestRecord">
    update send_request_record
    <set>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="resendFlag != null">
        resend_flag = #{resendFlag,jdbcType=VARCHAR},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="testeeName != null">
        testee_name = #{testeeName,jdbcType=VARCHAR},
      </if>
      <if test="bussinessId != null">
        bussiness_id = #{bussinessId,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="params != null">
        params = #{params,jdbcType=LONGVARCHAR},
      </if>
      <if test="returnData != null">
        return_data = #{returnData,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.haoys.user.model.SendRequestRecord">
    update send_request_record
    set platform_name = #{platformName,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      resend_flag = #{resendFlag,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      testee_name = #{testeeName,jdbcType=VARCHAR},
      bussiness_id = #{bussinessId,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      params = #{params,jdbcType=LONGVARCHAR},
      return_data = #{returnData,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SendRequestRecord">
    update send_request_record
    set platform_name = #{platformName,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      resend_flag = #{resendFlag,jdbcType=VARCHAR},
      project_id = #{projectId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      testee_name = #{testeeName,jdbcType=VARCHAR},
      bussiness_id = #{bussinessId,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>