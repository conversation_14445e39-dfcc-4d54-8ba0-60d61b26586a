<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectUserRole">
    <id column="project_id" jdbcType="BIGINT" property="projectId" />
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    project_id, user_id, role_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectUserRoleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_user_role
    where project_id = #{projectId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
      and role_id = #{roleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    delete from project_user_role
    where project_id = #{projectId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
      and role_id = #{roleId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectUserRoleExample">
    delete from project_user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectUserRole">
    insert into project_user_role (project_id, user_id, role_id,
      tenant_id, platform_id)
    values (#{projectId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT},
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectUserRole">
    insert into project_user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectUserRoleExample" resultType="java.lang.Long">
    select count(*) from project_user_role
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <update id="updateByExampleSelective" parameterType="map">
    update project_user_role
    <set>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_user_role
    set project_id = #{record.projectId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      role_id = #{record.roleId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectUserRole">
    update project_user_role
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where project_id = #{projectId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
      and role_id = #{roleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectUserRole">
    update project_user_role
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where project_id = #{projectId,jdbcType=BIGINT}
      and user_id = #{userId,jdbcType=BIGINT}
      and role_id = #{roleId,jdbcType=BIGINT}
  </update>

  <insert id="batchProjectUserRole">
    insert into project_user_role(project_id,user_id, role_id, tenant_id, platform_id) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.projectId},#{item.userId},#{item.roleId},#{item.tenantId},#{item.platformId})
    </foreach>
  </insert>

  <delete id="deleteProjectUserRoleByProjectIdAndUserId">
      delete from project_user_role where project_id = #{projectId} and user_id = #{userId}
  </delete>

  <select id="getProjectRoleListByProjectIdAndUserId" resultType="com.haoys.user.domain.vo.auth.ProjectUserRoleVo">
    select * from project_user_role where project_id = #{projectId} and user_id = #{systemUserId} and role_id = #{roleId}
  </select>

</mapper>
