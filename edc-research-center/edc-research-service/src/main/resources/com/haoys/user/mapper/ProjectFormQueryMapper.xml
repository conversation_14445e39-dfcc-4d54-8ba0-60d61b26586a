<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectFormQueryMapper">

  <insert id="executeAction" parameterType="String">
	  ${queryFieldValue}
  </insert>

  <select id="findOneRowData" parameterType="String" resultType="java.util.Map">
      ${queryFieldValue}
  </select>

  <select id="findManyRowsData" parameterType="String" resultType="java.util.Map">
      ${queryFieldValue}
  </select>

  <select id="findCount" parameterType="String" resultType="long">
      ${queryFieldValue}
  </select>

</mapper>