<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectApplyUserMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectApplyUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="project_org_id" jdbcType="BIGINT" property="projectOrgId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="check_user_id" jdbcType="VARCHAR" property="checkUserId" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="project_leader" jdbcType="BIT" property="projectLeader" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="check_status" jdbcType="VARCHAR" property="checkStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, org_id, project_org_id, user_id, org_name, apply_time, check_user_id, 
    check_time, project_leader, status, check_status, create_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectApplyUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_apply_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from project_apply_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_apply_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectApplyUserExample">
    delete from project_apply_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectApplyUser">
    insert into project_apply_user (id, project_id, org_id, 
      project_org_id, user_id, org_name, 
      apply_time, check_user_id, check_time, 
      project_leader, status, check_status, 
      create_time, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, 
      #{projectOrgId,jdbcType=BIGINT}, #{userId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{applyTime,jdbcType=TIMESTAMP}, #{checkUserId,jdbcType=VARCHAR}, #{checkTime,jdbcType=TIMESTAMP}, 
      #{projectLeader,jdbcType=BIT}, #{status,jdbcType=VARCHAR}, #{checkStatus,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectApplyUser">
    insert into project_apply_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="projectOrgId != null">
        project_org_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="checkUserId != null">
        check_user_id,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="projectLeader != null">
        project_leader,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkUserId != null">
        #{checkUserId,jdbcType=VARCHAR},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectLeader != null">
        #{projectLeader,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectApplyUserExample" resultType="java.lang.Long">
    select count(*) from project_apply_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_apply_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.projectOrgId != null">
        project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.applyTime != null">
        apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checkUserId != null">
        check_user_id = #{record.checkUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.checkTime != null">
        check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.projectLeader != null">
        project_leader = #{record.projectLeader,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.checkStatus != null">
        check_status = #{record.checkStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_apply_user
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      project_org_id = #{record.projectOrgId,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      apply_time = #{record.applyTime,jdbcType=TIMESTAMP},
      check_user_id = #{record.checkUserId,jdbcType=VARCHAR},
      check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      project_leader = #{record.projectLeader,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      check_status = #{record.checkStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectApplyUser">
    update project_apply_user
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="projectOrgId != null">
        project_org_id = #{projectOrgId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkUserId != null">
        check_user_id = #{checkUserId,jdbcType=VARCHAR},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="projectLeader != null">
        project_leader = #{projectLeader,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectApplyUser">
    update project_apply_user
    set project_id = #{projectId,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=BIGINT},
      project_org_id = #{projectOrgId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      check_user_id = #{checkUserId,jdbcType=VARCHAR},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      project_leader = #{projectLeader,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      check_status = #{checkStatus,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getUserApplyListForPage" resultType="com.haoys.user.domain.vo.project.ProjectApplyUserVo">
    select project_apply_user.id, project_apply_user.project_id projectId, project_apply_user.user_id userId,
          system_user_info.real_name realName,system_user_info.mobile,
          project_apply_user.org_id orgId, project_apply_user.org_name orgName,
          project_apply_user.apply_time applyTime, check_user_id checkUserId,
          check_time checkTime, project_leader projectLeader, project_apply_user.status,
          check_status checkStatus, project_apply_user.create_time createTime
      from project_apply_user
      inner join project on project.id = project_apply_user.project_id
      inner join system_user_info on system_user_info.id = project_apply_user.user_id
    <if test="projectId != null and projectId != ''">
      and project_apply_user.project_id = #{projectId} and project_apply_user.project_leader = 0
    </if>
    <if test="projectCreateUser != null and projectCreateUser != ''">
      and project.create_user = #{projectCreateUser}
    </if>
    <if test="applyUserName != null and applyUserName != ''">
      and system_user_info.real_name LIKE CONCAT('%',#{applyUserName},'%')
    </if>
    <if test="status != null and status != ''">
      and project_apply_user.check_status = #{status}
    </if>
    <if test="orgId != null and orgId != ''">
      and project_apply_user.org_id = #{orgId}
    </if>
    <if test="startDate != null and startDate != ''">
      and project_apply_user.apply_time between #{startDate} and #{endDate}
    </if>
    order by project_apply_user.create_time desc

  </select>



</mapper>