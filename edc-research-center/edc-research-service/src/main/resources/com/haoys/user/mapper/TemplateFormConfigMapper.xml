<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormConfigMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="form_code" jdbcType="VARCHAR" property="formCode" />
    <result column="form_config" jdbcType="VARCHAR" property="formConfig" />
    <result column="form_type" jdbcType="VARCHAR" property="formType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="if_copy" jdbcType="BIT" property="ifCopy" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="testee_form" jdbcType="BIT" property="testeeForm" />
    <result column="custom_form" jdbcType="BIT" property="customForm" />
    <result column="join_group" jdbcType="BIT" property="joinGroup" />
    <result column="upload_resource_file" jdbcType="BIT" property="uploadResourceFile" />
    <result column="open_epro" jdbcType="BIT" property="openEpro" />
    <result column="global_scope" jdbcType="BIT" property="globalScope" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, project_id, group_name, form_name, form_code, form_config, form_type, 
    status, version, if_copy, sort, testee_form, custom_form, join_group, upload_resource_file, 
    open_epro, global_scope, create_time, update_time, create_user, update_user, tenant_id, 
    platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_form_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_form_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormConfigExample">
    delete from template_form_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormConfig">
    insert into template_form_config (id, template_id, project_id, 
      group_name, form_name, form_code, 
      form_config, form_type, status, 
      version, if_copy, sort, 
      testee_form, custom_form, join_group, 
      upload_resource_file, open_epro, global_scope, 
      create_time, update_time, create_user, 
      update_user, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{groupName,jdbcType=VARCHAR}, #{formName,jdbcType=VARCHAR}, #{formCode,jdbcType=VARCHAR}, 
      #{formConfig,jdbcType=VARCHAR}, #{formType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{version,jdbcType=VARCHAR}, #{ifCopy,jdbcType=BIT}, #{sort,jdbcType=INTEGER}, 
      #{testeeForm,jdbcType=BIT}, #{customForm,jdbcType=BIT}, #{joinGroup,jdbcType=BIT}, 
      #{uploadResourceFile,jdbcType=BIT}, #{openEpro,jdbcType=BIT}, #{globalScope,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, 
      #{updateUser,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormConfig">
    insert into template_form_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="formCode != null">
        form_code,
      </if>
      <if test="formConfig != null">
        form_config,
      </if>
      <if test="formType != null">
        form_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ifCopy != null">
        if_copy,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="testeeForm != null">
        testee_form,
      </if>
      <if test="customForm != null">
        custom_form,
      </if>
      <if test="joinGroup != null">
        join_group,
      </if>
      <if test="uploadResourceFile != null">
        upload_resource_file,
      </if>
      <if test="openEpro != null">
        open_epro,
      </if>
      <if test="globalScope != null">
        global_scope,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="formCode != null">
        #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formConfig != null">
        #{formConfig,jdbcType=VARCHAR},
      </if>
      <if test="formType != null">
        #{formType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="ifCopy != null">
        #{ifCopy,jdbcType=BIT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="testeeForm != null">
        #{testeeForm,jdbcType=BIT},
      </if>
      <if test="customForm != null">
        #{customForm,jdbcType=BIT},
      </if>
      <if test="joinGroup != null">
        #{joinGroup,jdbcType=BIT},
      </if>
      <if test="uploadResourceFile != null">
        #{uploadResourceFile,jdbcType=BIT},
      </if>
      <if test="openEpro != null">
        #{openEpro,jdbcType=BIT},
      </if>
      <if test="globalScope != null">
        #{globalScope,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormConfigExample" resultType="java.lang.Long">
    select count(*) from template_form_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.groupName != null">
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.formCode != null">
        form_code = #{record.formCode,jdbcType=VARCHAR},
      </if>
      <if test="record.formConfig != null">
        form_config = #{record.formConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.formType != null">
        form_type = #{record.formType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.ifCopy != null">
        if_copy = #{record.ifCopy,jdbcType=BIT},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.testeeForm != null">
        testee_form = #{record.testeeForm,jdbcType=BIT},
      </if>
      <if test="record.customForm != null">
        custom_form = #{record.customForm,jdbcType=BIT},
      </if>
      <if test="record.joinGroup != null">
        join_group = #{record.joinGroup,jdbcType=BIT},
      </if>
      <if test="record.uploadResourceFile != null">
        upload_resource_file = #{record.uploadResourceFile,jdbcType=BIT},
      </if>
      <if test="record.openEpro != null">
        open_epro = #{record.openEpro,jdbcType=BIT},
      </if>
      <if test="record.globalScope != null">
        global_scope = #{record.globalScope,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_config
    set id = #{record.id,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      group_name = #{record.groupName,jdbcType=VARCHAR},
      form_name = #{record.formName,jdbcType=VARCHAR},
      form_code = #{record.formCode,jdbcType=VARCHAR},
      form_config = #{record.formConfig,jdbcType=VARCHAR},
      form_type = #{record.formType,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      if_copy = #{record.ifCopy,jdbcType=BIT},
      sort = #{record.sort,jdbcType=INTEGER},
      testee_form = #{record.testeeForm,jdbcType=BIT},
      custom_form = #{record.customForm,jdbcType=BIT},
      join_group = #{record.joinGroup,jdbcType=BIT},
      upload_resource_file = #{record.uploadResourceFile,jdbcType=BIT},
      open_epro = #{record.openEpro,jdbcType=BIT},
      global_scope = #{record.globalScope,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormConfig">
    update template_form_config
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="formCode != null">
        form_code = #{formCode,jdbcType=VARCHAR},
      </if>
      <if test="formConfig != null">
        form_config = #{formConfig,jdbcType=VARCHAR},
      </if>
      <if test="formType != null">
        form_type = #{formType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="ifCopy != null">
        if_copy = #{ifCopy,jdbcType=BIT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="testeeForm != null">
        testee_form = #{testeeForm,jdbcType=BIT},
      </if>
      <if test="customForm != null">
        custom_form = #{customForm,jdbcType=BIT},
      </if>
      <if test="joinGroup != null">
        join_group = #{joinGroup,jdbcType=BIT},
      </if>
      <if test="uploadResourceFile != null">
        upload_resource_file = #{uploadResourceFile,jdbcType=BIT},
      </if>
      <if test="openEpro != null">
        open_epro = #{openEpro,jdbcType=BIT},
      </if>
      <if test="globalScope != null">
        global_scope = #{globalScope,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormConfig">
    update template_form_config
    set template_id = #{templateId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      group_name = #{groupName,jdbcType=VARCHAR},
      form_name = #{formName,jdbcType=VARCHAR},
      form_code = #{formCode,jdbcType=VARCHAR},
      form_config = #{formConfig,jdbcType=VARCHAR},
      form_type = #{formType,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      if_copy = #{ifCopy,jdbcType=BIT},
      sort = #{sort,jdbcType=INTEGER},
      testee_form = #{testeeForm,jdbcType=BIT},
      custom_form = #{customForm,jdbcType=BIT},
      join_group = #{joinGroup,jdbcType=BIT},
      upload_resource_file = #{uploadResourceFile,jdbcType=BIT},
      open_epro = #{openEpro,jdbcType=BIT},
      global_scope = #{globalScope,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--查询模板相关列表-不分页-->
  <select id="getCustomTemplateFormConfigList" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo">
    select
      template_form_config.id formId,
      template_form_config.template_id templateId, template.code templateCode,template.description,
      template.name templateName, template.status templateStatus, template_form_config.project_id,
      template_form_config.form_name formName, template_form_config.form_config formConfig,
      template_form_config.form_type formType, template_form_config.form_code formCode, template_form_config.status,
      template_form_config.sort, template_form_config.upload_resource_file uploadResourceFile,
      template_form_config.open_epro openEpro,template_form_config.global_scope globalScope,
      template_form_config.create_time, IFNULL(template_form_config.update_time,template_form_config.create_time) modifyTime,
      template_form_config.create_user, template_form_config.update_user
    from template_form_config inner join template on template.id = template_form_config.template_id
    where 1 = 1 and template_form_config.status = 0 <!--and (template_form_config.project_id is null)-->
    <if test="templateId != null and templateId != ''">
      and template_form_config.template_id = #{templateId}
    </if>
    <if test="templateName != null and templateName != ''">
      and template_form_config.form_name LIKE CONCAT('%',#{templateName},'%')
    </if>
    <if test="projectId != null and projectId != ''">
      and template.project_id = #{projectId}
    </if>
    <if test="configType != null and configType != ''">
      and template.config_type = #{configType}
    </if>
    <if test="templateStatus != null and templateStatus != ''">
      and template.status = #{templateStatus}
    </if>
    <if test="createUserId != null and createUserId != ''">
      and template_form_config.create_user = #{createUserId}
    </if>
    and template_form_config.tenant_id = #{tenantId}
    order by template.create_time desc
  </select>

  <!--查询数据分析平台表单信息-->
  <select id="getResearchTemplateFormVariableList" resultType="com.haoys.user.model.TemplateFormDetail">
    SELECT
        t.*
    FROM
        (
        SELECT
            template_form_config.project_id projectId,
            template_form_config.id formId,
            template_form_config.form_name formName,
            template_form_detail.id formDetailId,
            template_form_detail.id,
            template_form_detail.label,
            template_form_detail.type,
            template_form_detail.field_name fieldName,
            template_form_detail.sort,
            template_form_detail.create_time
        FROM
            template_form_detail
            INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
            INNER JOIN flow_plan_form ON flow_plan_form.project_id = template_form_config.project_id
              AND flow_plan_form.form_id = template_form_config.id and flow_plan_form.publish_status = true
        WHERE
            template_form_config.status = 0
            AND template_form_detail.status = 0
            AND template_form_config.project_id = #{projectId}
            AND flow_plan_form.plan_id = #{planId}
        UNION ALL
          SELECT
              template_form_config.project_id projectId,
              template_form_config.id formId,
              template_form_config.form_name formName,
              template_form_detail.id formDetailId,
              template_form_table.id,
              template_form_table.label,
              template_form_table.type,
              template_form_table.field_name fieldName,
              template_form_table.sort,
              template_form_table.create_time
          FROM
              template_form_table
              INNER JOIN template_form_detail ON template_form_detail.id = template_form_table.form_detail_id
              INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
              INNER JOIN flow_plan_form ON flow_plan_form.project_id = template_form_config.project_id
              AND flow_plan_form.form_id = template_form_config.id and flow_plan_form.publish_status = true
          WHERE
              template_form_config.status = 0
              AND template_form_detail.status = 0
              AND template_form_table.`status` = 0
              AND template_form_config.project_id = #{projectId}
              AND flow_plan_form.plan_id = #{planId}
        ) t
        ORDER BY
            t.formId ASC,
            t.create_time ASC
  </select>


  <!--根据表单id获取表单变量分页列表-->
  <select id="getTemplateFormVariableListByFormIdForPage" resultType="com.haoys.user.domain.template.TemplateFormVariableWrapper">
    SELECT
        t.projectId, t.templateId, t.templateName, t.formId, t.formName,t.saveFormValue,
        t.formDetailId, t.label, t.field_name fieldName,t.type, t.placeholder,
        t.langValue, t.sort, t.createTime
    FROM
        (
        SELECT
            template_form_config.project_id projectId,
            template.id templateId,
            template.name templateName,
            template_form_config.id formId,
            template_form_config.form_name formName,
            IF(template_form_config.form_name IS NULL, false, true) saveFormValue,
            template_form_detail.id formDetailId,
            template_form_detail.label,
            template_form_detail.field_name,
            template_form_detail.type,
            template_form_detail.placeholder,
            template_form_detail.lang_value langValue,
            template_form_detail.sort,
            template_form_detail.create_time createTime
        FROM
            template_form_detail
            INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
            INNER JOIN template ON template.id = template_form_config.template_id
        WHERE
            template_form_config.status = 0
            AND template_form_detail.status = 0
            AND template.config_type = '3'
            <if test="templateId != null and templateId != ''">
              AND template.id = #{templateId}
            </if>
            <if test="formDetailName != null and formDetailName != ''">
              AND template_form_detail.label LIKE CONCAT('%',#{formDetailName},'%')
            </if>
            <if test="type != null and type != ''">
              AND template_form_detail.type = #{type}
            </if>
        UNION ALL
          SELECT
              template_form_config.project_id projectId,
              template.id templateId,
              template.name templateName,
              template_form_config.id formId,
              template_form_config.form_name formName,
              IF(template_form_config.form_name IS NULL, false, true) saveFormValue,
              template_form_detail.id formDetailId,
              template_form_table.label,
              template_form_table.field_name,
              template_form_table.type,
              template_form_table.placeholder,
              template_form_table.lang_value langValue,
              template_form_table.sort,
              template_form_table.create_time createTime
          FROM
              template_form_table
              INNER JOIN template_form_detail ON template_form_detail.id = template_form_table.form_detail_id
              INNER JOIN template_form_config ON template_form_config.id = template_form_detail.form_id
              INNER JOIN template ON template.id = template_form_config.template_id
          WHERE
              template_form_config.status = 0
              AND template_form_detail.status = 0
              AND template_form_table.`status` = 0
              AND template.config_type = '3'
              <if test="templateId != null and templateId != ''">
                AND template.id = #{templateId}
              </if>
              <if test="formDetailName != null and formDetailName != ''">
                AND template_form_table.label LIKE CONCAT('%',#{formDetailName},'%')
              </if>
              <if test="type != null and type != ''">
                AND template_form_table.type = #{type}
              </if>
        ) t
        ORDER BY
            t.formId ASC,
            t.createTime ASC
  </select>

  <!--根据项目id查询所有表单集合-->
  <select id="getProjectFormListByProjectId" resultMap="BaseResultMap">
    select * from template_form_config where project_id = #{projectId}
    and status = '0' and testee_form = false
    <if test="queryTable != null and queryTable != '' and queryTable == '1'.toString()">
      AND template_form_config.id IN(SELECT form_id FROM template_form_detail WHERE type = 'table' AND status = '0' )
    </if>
     order by sort asc, create_time desc
  </select>

  <select id="getTemplateFormDetailConfigByFieldName" resultType="String">
    SELECT label FROM(
      SELECT label FROM template_form_table WHERE field_name = #{fieldName} AND `status` = '0'
      UNION ALL
      SELECT label FROM template_form_detail WHERE field_name = #{fieldName} AND `status` = '0'
      ) t WHERE 1=1 LIMIT 1
  </select>

  <!--根据方案查询表单列表-->
  <select id="getTemplateFormConfigListByPlanId" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo">
    SELECT
        template_form_config.id as formId, template_form_config.form_name formName, template_form_config.form_code formCode,
        template_form_config.form_config formConfig, template_form_config.form_type, template_form_config.status
    FROM template_form_config INNER JOIN flow_plan_form ON flow_plan_form.form_id = template_form_config.id
    WHERE flow_plan_form.project_id = #{projectId} AND template_form_config.`status` = '0'
    <if test="planId != null and planId != ''">
      AND flow_plan_form.plan_id = #{planId}
    </if>
    <if test="formName != null and formName != ''">
      AND template_form_config.form_name LIKE CONCAT('%',#{formName},'%')
    </if>
    <if test="excludeJoinGroupFromConfig != null">
      AND (template_form_config.join_group != #{excludeJoinGroupFromConfig} or template_form_config.join_group is null)
    </if>
  </select>

  <select id="getFormDetailBaseConfigListByFormId" resultType="com.haoys.user.model.TemplateFormDetail">
    select
      id, template_id, project_id, form_id, type, label, field_name, lang_value, title,
      content, hidden, model, require_type, required, show_title, show_content, placeholder,
      icon, rules, combobox, dic_resource, ref_dic_id, default_dic_value, custom_table,
      default_value, score_value, unit_value, style_data, panel_size, custom_testee, group_id,
      group_info, copy_variable_id, base_variable_id, enable_view_config, lab_config_scope,
      ext_data_1, ext_data_2, ext_data_3, ext_data_4, ext_data_5, expand, sort, status
    from template_form_detail
    where project_id = #{projectId} and form_id = #{formId} and status = '0'
    order by sort asc, create_time asc
  </select>

  <select id="getFormTableBaseConfigListByFormDetailId" resultType="com.haoys.user.model.TemplateFormTable">
    select * from template_form_table where form_detail_id = #{formDetailId} and status = '0' order by sort asc, create_time asc
  </select>

  <select id="getTemplateFormCode" resultType="java.lang.Boolean">
    select * from template_form_config
    where 1 = 1
    <if test="templateId != null and templateId != ''">
      and template_id = #{templateId}
    </if>
    <if test="projectId != null and projectId != ''">
      and project_id = #{projectId}
    </if>
     and form_code = #{formCode} and status = '0'
  </select>

  <select id="getTemplateFormDetailCode" resultType="java.lang.Boolean">
    select * from template_form_detail where project_id = #{projectId} and form_id = #{formId} and field_name = #{fieldName} and status = '0'
  </select>

  <select id="getTemplateFormTableCodeResult" resultType="java.lang.Boolean">
    select * from template_form_table where project_id = #{projectId} and form_detail_id = #{formDetailId} and field_name = #{fieldName} and status = '0'
  </select>

  <select id="getTemplateFormConfigListByProjectId" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo">
    SELECT
      template_form_config.id as formId, template_form_config.form_name formName, template_form_config.form_code formCode,
      template_form_config.form_config formConfig, template_form_config.form_type, template_form_config.version,
      template_form_config.status, flow_plan_form.publish_status publishStatus, flow_plan_form.publish_time publishTime,
      flow_plan_form.plan_id planId, flow_plan_form.plan_name planName, flow_plan_form.publish_user_id publishUserId
    FROM template_form_config
    LEFT JOIN flow_plan_form ON flow_plan_form.form_id = template_form_config.id
                    AND template_form_config.project_id = flow_plan_form.project_id
                    <if test="publishStatus != null">
                      AND flow_plan_form.publish_status = #{publishStatus}
                    </if>
    WHERE template_form_config.project_id = #{projectId} AND template_form_config.`status` = '0'
            AND template_form_config.testee_form = false
    <if test="templateId != null and templateId != ''">
      AND template_form_config.template_id = #{templateId}
    </if>
    <if test="formName != null and formName != ''">
      AND template_form_config.form_name LIKE CONCAT('%',#{formName},'%')
    </if>

  </select>

  <select id="getSystemDictionaryTypeReference" resultType="String">
    select form_id from template_form_detail where dic_resource = '1' and ref_dic_id = #{dictionaryId} and status = '0'
    UNION ALL
    select form_id from template_form_table where dic_resource = '1' and ref_dic_id = #{dictionaryId} and status = '0'
  </select>

  <select id="getProjectDictionaryTypeReference" resultType="String">
    select form_id from template_form_detail where project_id = #{projectId} and dic_resource != '1' and ref_dic_id = #{dictionaryId} and status = '0'
    UNION ALL
    select form_id from template_form_table where project_id = #{projectId} and dic_resource != '1' and ref_dic_id = #{dictionaryId} and status = '0'
  </select>

  <!--自定义标题列表-->
  <select id="getTesteeCustomTitleList" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo">
    select
        template_form_detail.*
    from template_form_detail inner join template_form_config on template_form_config.id = template_form_detail.form_id
    where template_form_config.testee_form = true
    and template_form_config.project_id = #{projectId} and template_form_detail.status = '0'
    <if test="fieldName != null and fieldName != ''">
      AND (template_form_detail.label LIKE CONCAT('%',#{fieldName},'%') OR template_form_detail.field_name LIKE CONCAT('%',#{fieldName},'%'))
    </if>
    <if test="showTitleView != null and showTitleView != ''">
      AND template_form_detail.show_title = ${showTitleView}
    </if>
    order by template_form_detail.sort
  </select>

  <!--参与者表单基本信息-->
  <select id="getTemplateTesteeFormConfig" resultMap="BaseResultMap">
    select * from template_form_config where project_id = #{projectId} and testee_form = true <!--limit 1-->
  </select>

  <!--参与者详情信息-->
  <select id="getTemplateTesteeFormDetailBaseInfo" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo">
    select
      template_form_detail.*
    from template_form_detail inner join template_form_config on template_form_config.id = template_form_detail.form_id
    where template_form_config.testee_form = true
      and template_form_config.project_id = #{projectId}
      and template_form_config.id = #{formId}
      <if test="showTitle != null">
        AND template_form_detail.show_title = ${showTitle}
      </if>
      and template_form_detail.status = '0'
    order by template_form_detail.sort ASC
  </select>

  <select id="getTemplateTesteeFormDetailConfig" resultType="com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo">
    select * from template_form_detail where project_id = #{projectId} and form_id = #{formId} and field_name = #{fieldName}
    and custom_testee = true and status = '0'
  </select>

  <select id="getTemplateTesteeJoinGroupFormConfig" resultType="com.haoys.user.domain.vo.ecrf.TemplateTesteeJoinGroupFormVo">
    SELECT
      template_form_config.id as formId, template_form_config.form_name formName, template_form_config.form_code formCode,
      template_form_config.form_config formConfig, template_form_config.form_type, template_form_config.status,
      flow_form_set.visit_id visitId, template_form_config.join_group enableRandomizedConfig
    FROM template_form_config
    INNER JOIN flow_plan_form ON flow_plan_form.form_id = template_form_config.id
    INNER JOIN flow_form_set ON flow_form_set.form_id = template_form_config.id
    WHERE flow_plan_form.project_id = #{projectId} AND template_form_config.`status` = '0'
      AND flow_plan_form.plan_id = #{planId} AND template_form_config.join_group = true order by flow_form_set.xh asc limit 1
  </select>

</mapper>