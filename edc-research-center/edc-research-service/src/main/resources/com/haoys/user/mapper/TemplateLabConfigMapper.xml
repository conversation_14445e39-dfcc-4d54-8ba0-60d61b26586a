<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateLabConfigMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateLabConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="variable_name" jdbcType="VARCHAR" property="variableName" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="lab_type" jdbcType="VARCHAR" property="labType" />
    <result column="gender_value" jdbcType="VARCHAR" property="genderValue" />
    <result column="if_upper_contains" jdbcType="BIT" property="ifUpperContains" />
    <result column="upper_limit_value" jdbcType="DECIMAL" property="upperLimitValue" />
    <result column="if_lower_contains" jdbcType="BIT" property="ifLowerContains" />
    <result column="lower_limit_value" jdbcType="DECIMAL" property="lowerLimitValue" />
    <result column="dic_resource" jdbcType="VARCHAR" property="dicResource" />
    <result column="ref_dic_id" jdbcType="BIGINT" property="refDicId" />
    <result column="normal_text" jdbcType="VARCHAR" property="normalText" />
    <result column="normal_value" jdbcType="VARCHAR" property="normalValue" />
    <result column="lab_resource" jdbcType="BIGINT" property="labResource" />
    <result column="apply_total_org" jdbcType="BIT" property="applyTotalOrg" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="selected_options" jdbcType="VARCHAR" property="selectedOptions" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, visit_id, form_id, group_id, form_detail_id, form_table_id, 
    variable_name, scope, lab_type, gender_value, if_upper_contains, upper_limit_value, 
    if_lower_contains, lower_limit_value, dic_resource, ref_dic_id, normal_text, normal_value, 
    lab_resource, apply_total_org, enabled, status, selected_options, create_time, update_time, 
    create_user_id, update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateLabConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_lab_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_lab_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_lab_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateLabConfigExample">
    delete from template_lab_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateLabConfig">
    insert into template_lab_config (id, project_id, plan_id, 
      visit_id, form_id, group_id, 
      form_detail_id, form_table_id, variable_name, 
      scope, lab_type, gender_value, 
      if_upper_contains, upper_limit_value, if_lower_contains, 
      lower_limit_value, dic_resource, ref_dic_id, 
      normal_text, normal_value, lab_resource, 
      apply_total_org, enabled, status, 
      selected_options, create_time, update_time, 
      create_user_id, update_user_id, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT}, 
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, 
      #{formDetailId,jdbcType=BIGINT}, #{formTableId,jdbcType=BIGINT}, #{variableName,jdbcType=VARCHAR}, 
      #{scope,jdbcType=VARCHAR}, #{labType,jdbcType=VARCHAR}, #{genderValue,jdbcType=VARCHAR}, 
      #{ifUpperContains,jdbcType=BIT}, #{upperLimitValue,jdbcType=DECIMAL}, #{ifLowerContains,jdbcType=BIT}, 
      #{lowerLimitValue,jdbcType=DECIMAL}, #{dicResource,jdbcType=VARCHAR}, #{refDicId,jdbcType=BIGINT}, 
      #{normalText,jdbcType=VARCHAR}, #{normalValue,jdbcType=VARCHAR}, #{labResource,jdbcType=BIGINT}, 
      #{applyTotalOrg,jdbcType=BIT}, #{enabled,jdbcType=BIT}, #{status,jdbcType=VARCHAR}, 
      #{selectedOptions,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUserId,jdbcType=VARCHAR}, #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateLabConfig">
    insert into template_lab_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="variableName != null">
        variable_name,
      </if>
      <if test="scope != null">
        scope,
      </if>
      <if test="labType != null">
        lab_type,
      </if>
      <if test="genderValue != null">
        gender_value,
      </if>
      <if test="ifUpperContains != null">
        if_upper_contains,
      </if>
      <if test="upperLimitValue != null">
        upper_limit_value,
      </if>
      <if test="ifLowerContains != null">
        if_lower_contains,
      </if>
      <if test="lowerLimitValue != null">
        lower_limit_value,
      </if>
      <if test="dicResource != null">
        dic_resource,
      </if>
      <if test="refDicId != null">
        ref_dic_id,
      </if>
      <if test="normalText != null">
        normal_text,
      </if>
      <if test="normalValue != null">
        normal_value,
      </if>
      <if test="labResource != null">
        lab_resource,
      </if>
      <if test="applyTotalOrg != null">
        apply_total_org,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="selectedOptions != null">
        selected_options,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="variableName != null">
        #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=VARCHAR},
      </if>
      <if test="labType != null">
        #{labType,jdbcType=VARCHAR},
      </if>
      <if test="genderValue != null">
        #{genderValue,jdbcType=VARCHAR},
      </if>
      <if test="ifUpperContains != null">
        #{ifUpperContains,jdbcType=BIT},
      </if>
      <if test="upperLimitValue != null">
        #{upperLimitValue,jdbcType=DECIMAL},
      </if>
      <if test="ifLowerContains != null">
        #{ifLowerContains,jdbcType=BIT},
      </if>
      <if test="lowerLimitValue != null">
        #{lowerLimitValue,jdbcType=DECIMAL},
      </if>
      <if test="dicResource != null">
        #{dicResource,jdbcType=VARCHAR},
      </if>
      <if test="refDicId != null">
        #{refDicId,jdbcType=BIGINT},
      </if>
      <if test="normalText != null">
        #{normalText,jdbcType=VARCHAR},
      </if>
      <if test="normalValue != null">
        #{normalValue,jdbcType=VARCHAR},
      </if>
      <if test="labResource != null">
        #{labResource,jdbcType=BIGINT},
      </if>
      <if test="applyTotalOrg != null">
        #{applyTotalOrg,jdbcType=BIT},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="selectedOptions != null">
        #{selectedOptions,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateLabConfigExample" resultType="java.lang.Long">
    select count(*) from template_lab_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_lab_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.variableName != null">
        variable_name = #{record.variableName,jdbcType=VARCHAR},
      </if>
      <if test="record.scope != null">
        scope = #{record.scope,jdbcType=VARCHAR},
      </if>
      <if test="record.labType != null">
        lab_type = #{record.labType,jdbcType=VARCHAR},
      </if>
      <if test="record.genderValue != null">
        gender_value = #{record.genderValue,jdbcType=VARCHAR},
      </if>
      <if test="record.ifUpperContains != null">
        if_upper_contains = #{record.ifUpperContains,jdbcType=BIT},
      </if>
      <if test="record.upperLimitValue != null">
        upper_limit_value = #{record.upperLimitValue,jdbcType=DECIMAL},
      </if>
      <if test="record.ifLowerContains != null">
        if_lower_contains = #{record.ifLowerContains,jdbcType=BIT},
      </if>
      <if test="record.lowerLimitValue != null">
        lower_limit_value = #{record.lowerLimitValue,jdbcType=DECIMAL},
      </if>
      <if test="record.dicResource != null">
        dic_resource = #{record.dicResource,jdbcType=VARCHAR},
      </if>
      <if test="record.refDicId != null">
        ref_dic_id = #{record.refDicId,jdbcType=BIGINT},
      </if>
      <if test="record.normalText != null">
        normal_text = #{record.normalText,jdbcType=VARCHAR},
      </if>
      <if test="record.normalValue != null">
        normal_value = #{record.normalValue,jdbcType=VARCHAR},
      </if>
      <if test="record.labResource != null">
        lab_resource = #{record.labResource,jdbcType=BIGINT},
      </if>
      <if test="record.applyTotalOrg != null">
        apply_total_org = #{record.applyTotalOrg,jdbcType=BIT},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.selectedOptions != null">
        selected_options = #{record.selectedOptions,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_lab_config
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      variable_name = #{record.variableName,jdbcType=VARCHAR},
      scope = #{record.scope,jdbcType=VARCHAR},
      lab_type = #{record.labType,jdbcType=VARCHAR},
      gender_value = #{record.genderValue,jdbcType=VARCHAR},
      if_upper_contains = #{record.ifUpperContains,jdbcType=BIT},
      upper_limit_value = #{record.upperLimitValue,jdbcType=DECIMAL},
      if_lower_contains = #{record.ifLowerContains,jdbcType=BIT},
      lower_limit_value = #{record.lowerLimitValue,jdbcType=DECIMAL},
      dic_resource = #{record.dicResource,jdbcType=VARCHAR},
      ref_dic_id = #{record.refDicId,jdbcType=BIGINT},
      normal_text = #{record.normalText,jdbcType=VARCHAR},
      normal_value = #{record.normalValue,jdbcType=VARCHAR},
      lab_resource = #{record.labResource,jdbcType=BIGINT},
      apply_total_org = #{record.applyTotalOrg,jdbcType=BIT},
      enabled = #{record.enabled,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      selected_options = #{record.selectedOptions,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateLabConfig">
    update template_lab_config
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="variableName != null">
        variable_name = #{variableName,jdbcType=VARCHAR},
      </if>
      <if test="scope != null">
        scope = #{scope,jdbcType=VARCHAR},
      </if>
      <if test="labType != null">
        lab_type = #{labType,jdbcType=VARCHAR},
      </if>
      <if test="genderValue != null">
        gender_value = #{genderValue,jdbcType=VARCHAR},
      </if>
      <if test="ifUpperContains != null">
        if_upper_contains = #{ifUpperContains,jdbcType=BIT},
      </if>
      <if test="upperLimitValue != null">
        upper_limit_value = #{upperLimitValue,jdbcType=DECIMAL},
      </if>
      <if test="ifLowerContains != null">
        if_lower_contains = #{ifLowerContains,jdbcType=BIT},
      </if>
      <if test="lowerLimitValue != null">
        lower_limit_value = #{lowerLimitValue,jdbcType=DECIMAL},
      </if>
      <if test="dicResource != null">
        dic_resource = #{dicResource,jdbcType=VARCHAR},
      </if>
      <if test="refDicId != null">
        ref_dic_id = #{refDicId,jdbcType=BIGINT},
      </if>
      <if test="normalText != null">
        normal_text = #{normalText,jdbcType=VARCHAR},
      </if>
      <if test="normalValue != null">
        normal_value = #{normalValue,jdbcType=VARCHAR},
      </if>
      <if test="labResource != null">
        lab_resource = #{labResource,jdbcType=BIGINT},
      </if>
      <if test="applyTotalOrg != null">
        apply_total_org = #{applyTotalOrg,jdbcType=BIT},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="selectedOptions != null">
        selected_options = #{selectedOptions,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateLabConfig">
    update template_lab_config
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      group_id = #{groupId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      variable_name = #{variableName,jdbcType=VARCHAR},
      scope = #{scope,jdbcType=VARCHAR},
      lab_type = #{labType,jdbcType=VARCHAR},
      gender_value = #{genderValue,jdbcType=VARCHAR},
      if_upper_contains = #{ifUpperContains,jdbcType=BIT},
      upper_limit_value = #{upperLimitValue,jdbcType=DECIMAL},
      if_lower_contains = #{ifLowerContains,jdbcType=BIT},
      lower_limit_value = #{lowerLimitValue,jdbcType=DECIMAL},
      dic_resource = #{dicResource,jdbcType=VARCHAR},
      ref_dic_id = #{refDicId,jdbcType=BIGINT},
      normal_text = #{normalText,jdbcType=VARCHAR},
      normal_value = #{normalValue,jdbcType=VARCHAR},
      lab_resource = #{labResource,jdbcType=BIGINT},
      apply_total_org = #{applyTotalOrg,jdbcType=BIT},
      enabled = #{enabled,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      selected_options = #{selectedOptions,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getProjectTemplateLabConfigListForPage" resultType="com.haoys.user.domain.vo.lab.TemplateLabConfigVo">
    select
      template_lab_config.*,GROUP_CONCAT(template_lab_org_info.project_org_id) projectOrgIds
    FROM
      template_lab_config
    left join template_lab_org_info on template_lab_org_info.lab_config_id = template_lab_config.id
    where template_lab_config.project_id = #{projectId} and template_lab_config.status = '0'
    <if test="labType != null and labType != ''">
      and template_lab_config.lab_type = #{labType}
    </if>
    <if test="variableName != null and variableName != ''">
      and template_lab_config.variable_name LIKE CONCAT('%',#{variableName},'%')
    </if>
    GROUP BY template_lab_config.id
  </select>

  <select id="getTemplateLabConfigByVariableId" resultType="com.haoys.user.domain.expand.TemplateLabConfigExpand">
    select
      template_lab_config.* from template_lab_config
    inner join template_lab_org_info on template_lab_org_info.lab_config_id = template_lab_config.id
    where 1 = 1 and template_lab_org_info.project_org_id = #{projectOrgId} and template_lab_config.scope = #{labConfigScope}
    <if test="visitId != null and visitId != ''">
      and template_lab_config.visit_id = #{visitId}
    </if>
    and template_lab_config.form_id = #{formId}
    and template_lab_config.form_detail_id = #{variableId} and template_lab_config.status = '0' and template_lab_config.enabled = true
  </select>

  <select id="getTemplateLabConfigByVariableIdAndTableId" resultType="com.haoys.user.domain.expand.TemplateLabConfigExpand">
    select
      template_lab_config.* from template_lab_config
    inner join template_lab_org_info on template_lab_org_info.lab_config_id = template_lab_config.id
    where 1 = 1 and template_lab_org_info.project_org_id = #{projectOrgId} and template_lab_config.scope = #{labConfigScope}
    <if test="visitId != null and visitId != ''">
      and template_lab_config.visit_id = #{visitId}
    </if>
    and template_lab_config.form_id = #{formId}
    and template_lab_config.form_detail_id = #{variableId} and template_lab_config.form_table_id = #{tableId}
    and template_lab_config.status = '0' and template_lab_config.enabled = true
  </select>

  <select id="getTemplateLabConfigByGroupIdAndVariableId" resultType="com.haoys.user.domain.expand.TemplateLabConfigExpand">
    select
      template_lab_config.* from template_lab_config
    inner join template_lab_org_info on template_lab_org_info.lab_config_id = template_lab_config.id
    where 1 = 1 and template_lab_org_info.project_org_id = #{projectOrgId} and template_lab_config.scope = #{labConfigScope}
    <if test="visitId != null and visitId != ''">
      and template_lab_config.visit_id = #{visitId}
    </if>
    and template_lab_config.form_id = #{formId}
    and template_lab_config.group_id = #{groupId} and template_lab_config.form_detail_id = #{variableId}
    and template_lab_config.status = '0' and template_lab_config.enabled = true
  </select>

  <select id="getTemplateLabConfigByGroupIdAndTableId" resultType="com.haoys.user.domain.expand.TemplateLabConfigExpand">
    select template_lab_config.* from template_lab_config
    inner join template_lab_org_info on template_lab_org_info.lab_config_id = template_lab_config.id
    where 1 = 1 and template_lab_org_info.project_org_id = #{projectOrgId} and template_lab_config.scope = #{labConfigScope}
    <if test="visitId != null and visitId != ''">
      and template_lab_config.visit_id = #{visitId}
    </if>
    and template_lab_config.form_id = #{formId}
    and template_lab_config.group_id = #{groupId} and template_lab_config.form_detail_id = #{variableId}
    and template_lab_config.form_table_id = #{tableId}
    and template_lab_config.status = '0' and template_lab_config.enabled = true
  </select>

  <select id="checkTemplateLabConfigParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
      template_lab_config
    where 1 = 1
    <if test="planId != null and planId != ''">
      and template_lab_config.plan_id = #{planId}
    </if>
    <if test="visitId != null and visitId != ''">
      and template_lab_config.visit_id = #{visitId}
    </if>
    <if test="formId != null and formId != ''">
      and template_lab_config.form_id = #{formId}
    </if>
    <if test="groupId != null and groupId != ''">
      and template_lab_config.group_id = #{groupId}
    </if>
    <if test="formDetailId != null and formDetailId != ''">
      and template_lab_config.form_detail_id = #{formDetailId}
    </if>
    <if test="formTableId != null and formTableId != ''">
      and template_lab_config.form_table_id = #{formTableId}
    </if>
    <if test="scope != null and scope != ''">
      and template_lab_config.scope = #{scope}
    </if>
    and status = '0'
  </select>
</mapper>