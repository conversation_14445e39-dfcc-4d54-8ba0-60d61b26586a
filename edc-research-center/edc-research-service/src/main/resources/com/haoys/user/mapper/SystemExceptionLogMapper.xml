<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemExceptionLogMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemExceptionLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="request_ip" jdbcType="VARCHAR" property="requestIp" />
    <result column="exception_message" jdbcType="VARCHAR" property="exceptionMessage" />
    <result column="stack_trace_message" jdbcType="VARCHAR" property="stackTraceMessage" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, user_name, request_url, request_ip, exception_message, stack_trace_message, 
    description, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemExceptionLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_exception_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_exception_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_exception_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemExceptionLogExample">
    delete from system_exception_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemExceptionLog">
    insert into system_exception_log (id, user_id, user_name, 
      request_url, request_ip, exception_message, 
      stack_trace_message, description, create_time
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{requestUrl,jdbcType=VARCHAR}, #{requestIp,jdbcType=VARCHAR}, #{exceptionMessage,jdbcType=VARCHAR}, 
      #{stackTraceMessage,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemExceptionLog">
    insert into system_exception_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="requestUrl != null">
        request_url,
      </if>
      <if test="requestIp != null">
        request_ip,
      </if>
      <if test="exceptionMessage != null">
        exception_message,
      </if>
      <if test="stackTraceMessage != null">
        stack_trace_message,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestIp != null">
        #{requestIp,jdbcType=VARCHAR},
      </if>
      <if test="exceptionMessage != null">
        #{exceptionMessage,jdbcType=VARCHAR},
      </if>
      <if test="stackTraceMessage != null">
        #{stackTraceMessage,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemExceptionLogExample" resultType="java.lang.Long">
    select count(*) from system_exception_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_exception_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.userName != null">
        user_name = #{record.userName,jdbcType=VARCHAR},
      </if>
      <if test="record.requestUrl != null">
        request_url = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.requestIp != null">
        request_ip = #{record.requestIp,jdbcType=VARCHAR},
      </if>
      <if test="record.exceptionMessage != null">
        exception_message = #{record.exceptionMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.stackTraceMessage != null">
        stack_trace_message = #{record.stackTraceMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_exception_log
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      user_name = #{record.userName,jdbcType=VARCHAR},
      request_url = #{record.requestUrl,jdbcType=VARCHAR},
      request_ip = #{record.requestIp,jdbcType=VARCHAR},
      exception_message = #{record.exceptionMessage,jdbcType=VARCHAR},
      stack_trace_message = #{record.stackTraceMessage,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemExceptionLog">
    update system_exception_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        request_url = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="requestIp != null">
        request_ip = #{requestIp,jdbcType=VARCHAR},
      </if>
      <if test="exceptionMessage != null">
        exception_message = #{exceptionMessage,jdbcType=VARCHAR},
      </if>
      <if test="stackTraceMessage != null">
        stack_trace_message = #{stackTraceMessage,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemExceptionLog">
    update system_exception_log
    set user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      request_ip = #{requestIp,jdbcType=VARCHAR},
      exception_message = #{exceptionMessage,jdbcType=VARCHAR},
      stack_trace_message = #{stackTraceMessage,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getSystemExceptionLogList" resultType="com.haoys.user.domain.vo.system.SystemExceptionLogVo">
    select * from system_exception_log where 1 = 1
    <if test="userName != null and userName !=''">
      and user_name = #{userName}
    </if>
    order by create_time desc
  </select>
</mapper>