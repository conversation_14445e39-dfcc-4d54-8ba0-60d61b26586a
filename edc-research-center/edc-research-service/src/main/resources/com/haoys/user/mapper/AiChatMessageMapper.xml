<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.AiChatMessageMapper">

    <resultMap id="BaseResultMap" type="com.haoys.user.domain.entity.AiChatMessage">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="session_id" jdbcType="VARCHAR" property="sessionId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="role" jdbcType="VARCHAR" property="role"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="file_info" jdbcType="VARCHAR" property="fileInfo"/>
        <result column="tokens" jdbcType="INTEGER" property="tokens"/>
        <result column="cost" jdbcType="DECIMAL" property="cost"/>
        <result column="response_time" jdbcType="INTEGER" property="responseTime"/>
        <result column="is_stream" jdbcType="TINYINT" property="isStream"/>
        <result column="error_info" jdbcType="LONGVARCHAR" property="errorInfo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, message_id, session_id, user_id, role, content, content_type, file_info,
        tokens, cost, response_time, is_stream, error_info, create_time
    </sql>

    <insert id="insert" parameterType="com.haoys.user.domain.entity.AiChatMessage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_chat_message (
            message_id, session_id, user_id, role, content, content_type, file_info,
            tokens, cost, response_time, is_stream, error_info, create_time
        ) VALUES (
            #{messageId}, #{sessionId}, #{userId}, #{role}, #{content}, #{contentType}, #{fileInfo},
            #{tokens}, #{cost}, #{responseTime}, #{isStream}, #{errorInfo}, #{createTime}
        )
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_message
        WHERE id = #{id}
    </select>

    <select id="selectByMessageId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_message
        WHERE message_id = #{messageId}
    </select>

    <select id="selectBySessionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_message
        WHERE session_id = #{sessionId}
        ORDER BY create_time ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="countBySessionId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ai_chat_message
        WHERE session_id = #{sessionId}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_message
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <update id="updateContent">
        UPDATE ai_chat_message
        SET content = #{content}
        WHERE message_id = #{messageId}
    </update>

    <update id="updateTokensAndCost">
        UPDATE ai_chat_message
        SET tokens = #{tokens}, cost = #{cost}
        WHERE message_id = #{messageId}
    </update>

    <update id="updateResponseTime">
        UPDATE ai_chat_message
        SET response_time = #{responseTime}
        WHERE message_id = #{messageId}
    </update>

    <update id="updateErrorInfo">
        UPDATE ai_chat_message
        SET error_info = #{errorInfo}
        WHERE message_id = #{messageId}
    </update>

    <delete id="deleteByMessageId" parameterType="java.lang.String">
        DELETE FROM ai_chat_message
        WHERE message_id = #{messageId}
    </delete>

    <delete id="deleteBySessionId" parameterType="java.lang.String">
        DELETE FROM ai_chat_message
        WHERE session_id = #{sessionId}
    </delete>

    <select id="selectContextMessages" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_message
        WHERE session_id = #{sessionId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

</mapper>
