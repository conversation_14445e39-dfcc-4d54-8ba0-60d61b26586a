<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectVisitConfigMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectVisitConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="pre_visit_id" jdbcType="BIGINT" property="preVisitId" />
    <result column="visit_name" jdbcType="VARCHAR" property="visitName" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="owner_peroid" jdbcType="VARCHAR" property="ownerPeroid" />
    <result column="follow_up_peroid" jdbcType="INTEGER" property="followUpPeroid" />
    <result column="follow_up_unit" jdbcType="VARCHAR" property="followUpUnit" />
    <result column="visit_window_start" jdbcType="INTEGER" property="visitWindowStart" />
    <result column="visit_window_end" jdbcType="INTEGER" property="visitWindowEnd" />
    <result column="visit_window_unit" jdbcType="VARCHAR" property="visitWindowUnit" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="config_data" jdbcType="VARCHAR" property="configData" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="update_user" jdbcType="BIGINT" property="updateUser" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, template_id, pre_visit_id, visit_name, visit_type, owner_peroid,
    follow_up_peroid, follow_up_unit, visit_window_start, visit_window_end, visit_window_unit,
    expand, config_data, create_time, update_time, create_user, update_user, status,
    sort, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectVisitConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_visit_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from project_visit_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_visit_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectVisitConfigExample">
    delete from project_visit_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectVisitConfig">
    insert into project_visit_config (id, project_id, plan_id,
      template_id, pre_visit_id, visit_name,
      visit_type, owner_peroid, follow_up_peroid,
      follow_up_unit, visit_window_start, visit_window_end,
      visit_window_unit, expand, config_data,
      create_time, update_time, create_user,
      update_user, status, sort,
      tenant_id, platform_id)
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT},
      #{templateId,jdbcType=BIGINT}, #{preVisitId,jdbcType=BIGINT}, #{visitName,jdbcType=VARCHAR},
      #{visitType,jdbcType=VARCHAR}, #{ownerPeroid,jdbcType=VARCHAR}, #{followUpPeroid,jdbcType=INTEGER},
      #{followUpUnit,jdbcType=VARCHAR}, #{visitWindowStart,jdbcType=INTEGER}, #{visitWindowEnd,jdbcType=INTEGER},
      #{visitWindowUnit,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, #{configData,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=BIGINT},
      #{updateUser,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER},
      #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectVisitConfig">
    insert into project_visit_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="preVisitId != null">
        pre_visit_id,
      </if>
      <if test="visitName != null">
        visit_name,
      </if>
      <if test="visitType != null">
        visit_type,
      </if>
      <if test="ownerPeroid != null">
        owner_peroid,
      </if>
      <if test="followUpPeroid != null">
        follow_up_peroid,
      </if>
      <if test="followUpUnit != null">
        follow_up_unit,
      </if>
      <if test="visitWindowStart != null">
        visit_window_start,
      </if>
      <if test="visitWindowEnd != null">
        visit_window_end,
      </if>
      <if test="visitWindowUnit != null">
        visit_window_unit,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="configData != null">
        config_data,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="preVisitId != null">
        #{preVisitId,jdbcType=BIGINT},
      </if>
      <if test="visitName != null">
        #{visitName,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="ownerPeroid != null">
        #{ownerPeroid,jdbcType=VARCHAR},
      </if>
      <if test="followUpPeroid != null">
        #{followUpPeroid,jdbcType=INTEGER},
      </if>
      <if test="followUpUnit != null">
        #{followUpUnit,jdbcType=VARCHAR},
      </if>
      <if test="visitWindowStart != null">
        #{visitWindowStart,jdbcType=INTEGER},
      </if>
      <if test="visitWindowEnd != null">
        #{visitWindowEnd,jdbcType=INTEGER},
      </if>
      <if test="visitWindowUnit != null">
        #{visitWindowUnit,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="configData != null">
        #{configData,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectVisitConfigExample" resultType="java.lang.Long">
    select count(*) from project_visit_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_visit_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.preVisitId != null">
        pre_visit_id = #{record.preVisitId,jdbcType=BIGINT},
      </if>
      <if test="record.visitName != null">
        visit_name = #{record.visitName,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        visit_type = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerPeroid != null">
        owner_peroid = #{record.ownerPeroid,jdbcType=VARCHAR},
      </if>
      <if test="record.followUpPeroid != null">
        follow_up_peroid = #{record.followUpPeroid,jdbcType=INTEGER},
      </if>
      <if test="record.followUpUnit != null">
        follow_up_unit = #{record.followUpUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.visitWindowStart != null">
        visit_window_start = #{record.visitWindowStart,jdbcType=INTEGER},
      </if>
      <if test="record.visitWindowEnd != null">
        visit_window_end = #{record.visitWindowEnd,jdbcType=INTEGER},
      </if>
      <if test="record.visitWindowUnit != null">
        visit_window_unit = #{record.visitWindowUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.configData != null">
        config_data = #{record.configData,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=BIGINT},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_visit_config
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      pre_visit_id = #{record.preVisitId,jdbcType=BIGINT},
      visit_name = #{record.visitName,jdbcType=VARCHAR},
      visit_type = #{record.visitType,jdbcType=VARCHAR},
      owner_peroid = #{record.ownerPeroid,jdbcType=VARCHAR},
      follow_up_peroid = #{record.followUpPeroid,jdbcType=INTEGER},
      follow_up_unit = #{record.followUpUnit,jdbcType=VARCHAR},
      visit_window_start = #{record.visitWindowStart,jdbcType=INTEGER},
      visit_window_end = #{record.visitWindowEnd,jdbcType=INTEGER},
      visit_window_unit = #{record.visitWindowUnit,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      config_data = #{record.configData,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=BIGINT},
      update_user = #{record.updateUser,jdbcType=BIGINT},
      status = #{record.status,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectVisitConfig">
    update project_visit_config
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="preVisitId != null">
        pre_visit_id = #{preVisitId,jdbcType=BIGINT},
      </if>
      <if test="visitName != null">
        visit_name = #{visitName,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        visit_type = #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="ownerPeroid != null">
        owner_peroid = #{ownerPeroid,jdbcType=VARCHAR},
      </if>
      <if test="followUpPeroid != null">
        follow_up_peroid = #{followUpPeroid,jdbcType=INTEGER},
      </if>
      <if test="followUpUnit != null">
        follow_up_unit = #{followUpUnit,jdbcType=VARCHAR},
      </if>
      <if test="visitWindowStart != null">
        visit_window_start = #{visitWindowStart,jdbcType=INTEGER},
      </if>
      <if test="visitWindowEnd != null">
        visit_window_end = #{visitWindowEnd,jdbcType=INTEGER},
      </if>
      <if test="visitWindowUnit != null">
        visit_window_unit = #{visitWindowUnit,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="configData != null">
        config_data = #{configData,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=BIGINT},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectVisitConfig">
    update project_visit_config
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      template_id = #{templateId,jdbcType=BIGINT},
      pre_visit_id = #{preVisitId,jdbcType=BIGINT},
      visit_name = #{visitName,jdbcType=VARCHAR},
      visit_type = #{visitType,jdbcType=VARCHAR},
      owner_peroid = #{ownerPeroid,jdbcType=VARCHAR},
      follow_up_peroid = #{followUpPeroid,jdbcType=INTEGER},
      follow_up_unit = #{followUpUnit,jdbcType=VARCHAR},
      visit_window_start = #{visitWindowStart,jdbcType=INTEGER},
      visit_window_end = #{visitWindowEnd,jdbcType=INTEGER},
      visit_window_unit = #{visitWindowUnit,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      config_data = #{configData,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=BIGINT},
      update_user = #{updateUser,jdbcType=BIGINT},
      status = #{status,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getMaxSort" resultType="java.lang.Integer">
    select max(sort) from project_visit_config where plan_id=#{templateId} and status=0
  </select>
</mapper>
