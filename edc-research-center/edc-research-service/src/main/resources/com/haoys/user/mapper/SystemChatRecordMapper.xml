<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemChatRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemChatRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="topic" jdbcType="VARCHAR" property="topic" />
    <result column="prompt_value" jdbcType="VARCHAR" property="promptValue" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="model_type" jdbcType="VARCHAR" property="modelType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, topic, prompt_value, content, model_type, status, create_user_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemChatRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_chat_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_chat_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_chat_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemChatRecordExample">
    delete from system_chat_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemChatRecord">
    insert into system_chat_record (id, topic, prompt_value, 
      content, model_type, status, 
      create_user_id, create_time)
    values (#{id,jdbcType=BIGINT}, #{topic,jdbcType=VARCHAR}, #{promptValue,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{modelType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemChatRecord">
    insert into system_chat_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="topic != null">
        topic,
      </if>
      <if test="promptValue != null">
        prompt_value,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="modelType != null">
        model_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="topic != null">
        #{topic,jdbcType=VARCHAR},
      </if>
      <if test="promptValue != null">
        #{promptValue,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemChatRecordExample" resultType="java.lang.Long">
    select count(*) from system_chat_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_chat_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.topic != null">
        topic = #{record.topic,jdbcType=VARCHAR},
      </if>
      <if test="record.promptValue != null">
        prompt_value = #{record.promptValue,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.modelType != null">
        model_type = #{record.modelType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_chat_record
    set id = #{record.id,jdbcType=BIGINT},
      topic = #{record.topic,jdbcType=VARCHAR},
      prompt_value = #{record.promptValue,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      model_type = #{record.modelType,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemChatRecord">
    update system_chat_record
    <set>
      <if test="topic != null">
        topic = #{topic,jdbcType=VARCHAR},
      </if>
      <if test="promptValue != null">
        prompt_value = #{promptValue,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="modelType != null">
        model_type = #{modelType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemChatRecord">
    update system_chat_record
    set topic = #{topic,jdbcType=VARCHAR},
      prompt_value = #{promptValue,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      model_type = #{modelType,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getSystemChatRecordByTopic" resultMap="BaseResultMap">
    select * from system_chat_record where topic = #{topic} and create_user_id = #{createUserId} and status = '0' limit 1
  </select>

  <select id="getChatContentList" resultMap="BaseResultMap">
    select * from system_chat_record where 1 = 1 and status = '0'
    <if test="createUserId != null">
      and create_user_id = #{createUserId,jdbcType=VARCHAR}
    </if>
    <if test="topic != null and topic != ''">
      and topic LIKE CONCAT('%', #{topic}, '%')
    </if>
    <if test="modelType != null and modelType != ''">
      and model_type = #{modelType}
    </if>
    order by create_time asc
  </select>

</mapper>