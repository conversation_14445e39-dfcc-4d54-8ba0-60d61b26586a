<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.FlowPlanFormInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.FlowPlanFormInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="plan_name" jdbcType="VARCHAR" property="planName" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="publish_status" jdbcType="BIT" property="publishStatus" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="publish_user_id" jdbcType="VARCHAR" property="publishUserId" />
    <result column="cancle_publish_time" jdbcType="TIMESTAMP" property="canclePublishTime" />
    <result column="cancle_publish_user_id" jdbcType="VARCHAR" property="canclePublishUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, project_id, plan_id, plan_name, form_id, form_name, status, publish_status, publish_time,
    publish_user_id, cancle_publish_time, cancle_publish_user_id, create_time, create_user_id,
    update_time, update_user_id, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.FlowPlanFormInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from flow_plan_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from flow_plan_form
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from flow_plan_form
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.FlowPlanFormInfoExample">
    delete from flow_plan_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.FlowPlanFormInfo">
    insert into flow_plan_form (id, project_id, plan_id,
      plan_name, form_id, form_name,
      status, publish_status, publish_time,
      publish_user_id, cancle_publish_time, cancle_publish_user_id,
      create_time, create_user_id, update_time,
      update_user_id, tenant_id, platform_id
      )
    values (#{id,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT},
      #{planName,jdbcType=VARCHAR}, #{formId,jdbcType=BIGINT}, #{formName,jdbcType=VARCHAR},
      #{status,jdbcType=VARCHAR}, #{publishStatus,jdbcType=BIT}, #{publishTime,jdbcType=TIMESTAMP},
      #{publishUserId,jdbcType=VARCHAR}, #{canclePublishTime,jdbcType=TIMESTAMP}, #{canclePublishUserId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateUserId,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{platformId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.FlowPlanFormInfo">
    insert into flow_plan_form
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="planName != null">
        plan_name,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="publishStatus != null">
        publish_status,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="publishUserId != null">
        publish_user_id,
      </if>
      <if test="canclePublishTime != null">
        cancle_publish_time,
      </if>
      <if test="canclePublishUserId != null">
        cancle_publish_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="planName != null">
        #{planName,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null">
        #{publishStatus,jdbcType=BIT},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishUserId != null">
        #{publishUserId,jdbcType=VARCHAR},
      </if>
      <if test="canclePublishTime != null">
        #{canclePublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canclePublishUserId != null">
        #{canclePublishUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.FlowPlanFormInfoExample" resultType="java.lang.Long">
    select count(*) from flow_plan_form
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update flow_plan_form
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.planName != null">
        plan_name = #{record.planName,jdbcType=VARCHAR},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.publishStatus != null">
        publish_status = #{record.publishStatus,jdbcType=BIT},
      </if>
      <if test="record.publishTime != null">
        publish_time = #{record.publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.publishUserId != null">
        publish_user_id = #{record.publishUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.canclePublishTime != null">
        cancle_publish_time = #{record.canclePublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.canclePublishUserId != null">
        cancle_publish_user_id = #{record.canclePublishUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUserId != null">
        update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update flow_plan_form
    set id = #{record.id,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      plan_name = #{record.planName,jdbcType=VARCHAR},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_name = #{record.formName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      publish_status = #{record.publishStatus,jdbcType=BIT},
      publish_time = #{record.publishTime,jdbcType=TIMESTAMP},
      publish_user_id = #{record.publishUserId,jdbcType=VARCHAR},
      cancle_publish_time = #{record.canclePublishTime,jdbcType=TIMESTAMP},
      cancle_publish_user_id = #{record.canclePublishUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{record.updateUserId,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.FlowPlanFormInfo">
    update flow_plan_form
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="planName != null">
        plan_name = #{planName,jdbcType=VARCHAR},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="publishStatus != null">
        publish_status = #{publishStatus,jdbcType=BIT},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishUserId != null">
        publish_user_id = #{publishUserId,jdbcType=VARCHAR},
      </if>
      <if test="canclePublishTime != null">
        cancle_publish_time = #{canclePublishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="canclePublishUserId != null">
        cancle_publish_user_id = #{canclePublishUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.FlowPlanFormInfo">
    update flow_plan_form
    set project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      plan_name = #{planName,jdbcType=VARCHAR},
      form_id = #{formId,jdbcType=BIGINT},
      form_name = #{formName,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      publish_status = #{publishStatus,jdbcType=BIT},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      publish_user_id = #{publishUserId,jdbcType=VARCHAR},
      cancle_publish_time = #{canclePublishTime,jdbcType=TIMESTAMP},
      cancle_publish_user_id = #{canclePublishUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="getFlowPlanFormBaseInfoByPlanIdAndFormId" resultMap="BaseResultMap">
    select * from flow_plan_form where project_id = #{projectId} and plan_id = #{planId} and form_id = #{formId} limit 1
  </select>

  <select id="getFormConfigListByPlanIdAndVisitId" resultType="com.haoys.user.domain.vo.flow.FlowPlanFormVo">
    SELECT
      flow_form_set.id formSetId,
      flow_form_set.plan_id planId,
      flow_form_set.visit_id visitId,
      flow_form_set.form_id formId,
      template_form_config.form_name formName,
      template_form_config.form_code formCode,
      template_form_config.form_type formType,
      flow_form_set.pc_ro_auth pcRoAuth,
      flow_form_set.pc_rw_auth pcRwAuth,
      flow_form_set.mo_ro_auth moRoAuth,
      flow_form_set.mo_rw_auth moRwAuth
    FROM
      flow_form_set
    INNER JOIN flow_plan_form ON flow_plan_form.plan_id = flow_form_set.plan_id AND flow_plan_form.form_id = flow_form_set.form_id
    INNER JOIN template_form_config ON template_form_config.id = flow_plan_form.form_id and template_form_config.`status`=0
    WHERE flow_form_set.project_id = #{projectId}
      AND flow_form_set.plan_id = #{planId}
      AND flow_form_set.visit_id = #{visitId}
     <if test="formId != null and formId !=''">
        AND flow_form_set.form_id = #{formId}
     </if>
      AND flow_form_set.status = '0'
      <!--AND flow_plan_form.publish_status = true-->
    order by flow_form_set.xh
  </select>

  <select id="getFormConfigListByProjectIdAndFormId" resultMap="BaseResultMap">
    select * from flow_plan_form where project_id = #{projectId} and form_id = #{formId} and status = '0'
  </select>

  <select id="getFormConfigListByProjectIdAndPlanIdAndFormId" resultMap="BaseResultMap">
    select * from flow_plan_form where project_id = #{projectId} and plan_id = #{planId} and form_id = #{formId} and status = '0'
  </select>
</mapper>
