<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.ProjectTesteeChallengeMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.ProjectTesteeChallenge">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="dvp_rule_id" jdbcType="BIGINT" property="dvpRuleId" />
    <result column="testee_id" jdbcType="BIGINT" property="testeeId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="plan_id" jdbcType="BIGINT" property="planId" />
    <result column="visit_id" jdbcType="BIGINT" property="visitId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_name" jdbcType="VARCHAR" property="formName" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="form_table_id" jdbcType="BIGINT" property="formTableId" />
    <result column="form_result_id" jdbcType="BIGINT" property="formResultId" />
    <result column="form_result_table_rowno" jdbcType="BIGINT" property="formResultTableRowno" />
    <result column="form_result_table_id" jdbcType="BIGINT" property="formResultTableId" />
    <result column="form_result_label" jdbcType="VARCHAR" property="formResultLabel" />
    <result column="form_result_key" jdbcType="VARCHAR" property="formResultKey" />
    <result column="form_result_value" jdbcType="VARCHAR" property="formResultValue" />
    <result column="form_result_original_value" jdbcType="VARCHAR" property="formResultOriginalValue" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="query_method" jdbcType="VARCHAR" property="queryMethod" />
    <result column="if_system" jdbcType="BIT" property="ifSystem" />
    <result column="reply_close_status" jdbcType="VARCHAR" property="replyCloseStatus" />
    <result column="close_reason" jdbcType="VARCHAR" property="closeReason" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="close_user" jdbcType="VARCHAR" property="closeUser" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="user_role_code" jdbcType="VARCHAR" property="userRoleCode" />
    <result column="receive_user_id" jdbcType="VARCHAR" property="receiveUserId" />
    <result column="receive_rule" jdbcType="VARCHAR" property="receiveRule" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="project_org_id" jdbcType="VARCHAR" property="projectOrgId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.haoys.user.model.ProjectTesteeChallenge">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, dvp_rule_id, testee_id, project_id, plan_id, visit_id, form_id, form_name,
    form_detail_id, form_table_id, form_result_id, form_result_table_rowno, form_result_table_id,
    form_result_label, form_result_key, form_result_value, form_result_original_value,
    type, query_method, if_system, reply_close_status, close_reason, close_time, close_user,
    status, user_role_code, receive_user_id, receive_rule, expand, create_user, create_time,
    update_user, update_time, tenant_id, platform_id, group_id, project_org_id
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.haoys.user.model.ProjectTesteeChallengeExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_testee_challenge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.haoys.user.model.ProjectTesteeChallengeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from project_testee_challenge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_testee_challenge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from project_testee_challenge
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.ProjectTesteeChallengeExample">
    delete from project_testee_challenge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.ProjectTesteeChallenge">
    insert into project_testee_challenge (id, code, dvp_rule_id,
      testee_id, project_id, plan_id,
      visit_id, form_id, form_name,
      form_detail_id, form_table_id, form_result_id,
      form_result_table_rowno, form_result_table_id,
      form_result_label, form_result_key, form_result_value,
      form_result_original_value, type, query_method,
      if_system, reply_close_status, close_reason,
      close_time, close_user, status,
      user_role_code, receive_user_id, receive_rule,
      expand, create_user, create_time,
      update_user, update_time, tenant_id,
      platform_id, group_id, project_org_id,
      content)
    values (#{id,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{dvpRuleId,jdbcType=BIGINT},
      #{testeeId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{planId,jdbcType=BIGINT},
      #{visitId,jdbcType=BIGINT}, #{formId,jdbcType=BIGINT}, #{formName,jdbcType=VARCHAR},
      #{formDetailId,jdbcType=BIGINT}, #{formTableId,jdbcType=BIGINT}, #{formResultId,jdbcType=BIGINT},
      #{formResultTableRowno,jdbcType=BIGINT}, #{formResultTableId,jdbcType=BIGINT},
      #{formResultLabel,jdbcType=VARCHAR}, #{formResultKey,jdbcType=VARCHAR}, #{formResultValue,jdbcType=VARCHAR},
      #{formResultOriginalValue,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{queryMethod,jdbcType=VARCHAR},
      #{ifSystem,jdbcType=BIT}, #{replyCloseStatus,jdbcType=VARCHAR}, #{closeReason,jdbcType=VARCHAR},
      #{closeTime,jdbcType=TIMESTAMP}, #{closeUser,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
      #{userRoleCode,jdbcType=VARCHAR}, #{receiveUserId,jdbcType=VARCHAR}, #{receiveRule,jdbcType=VARCHAR},
      #{expand,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateUser,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR},
      #{platformId,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, #{projectOrgId,jdbcType=VARCHAR},
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.ProjectTesteeChallenge">
    insert into project_testee_challenge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="dvpRuleId != null">
        dvp_rule_id,
      </if>
      <if test="testeeId != null">
        testee_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="visitId != null">
        visit_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formName != null">
        form_name,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="formTableId != null">
        form_table_id,
      </if>
      <if test="formResultId != null">
        form_result_id,
      </if>
      <if test="formResultTableRowno != null">
        form_result_table_rowno,
      </if>
      <if test="formResultTableId != null">
        form_result_table_id,
      </if>
      <if test="formResultLabel != null">
        form_result_label,
      </if>
      <if test="formResultKey != null">
        form_result_key,
      </if>
      <if test="formResultValue != null">
        form_result_value,
      </if>
      <if test="formResultOriginalValue != null">
        form_result_original_value,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="queryMethod != null">
        query_method,
      </if>
      <if test="ifSystem != null">
        if_system,
      </if>
      <if test="replyCloseStatus != null">
        reply_close_status,
      </if>
      <if test="closeReason != null">
        close_reason,
      </if>
      <if test="closeTime != null">
        close_time,
      </if>
      <if test="closeUser != null">
        close_user,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="userRoleCode != null">
        user_role_code,
      </if>
      <if test="receiveUserId != null">
        receive_user_id,
      </if>
      <if test="receiveRule != null">
        receive_rule,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="projectOrgId != null">
        project_org_id,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="dvpRuleId != null">
        #{dvpRuleId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formName != null">
        #{formName,jdbcType=VARCHAR},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultId != null">
        #{formResultId,jdbcType=BIGINT},
      </if>
      <if test="formResultTableRowno != null">
        #{formResultTableRowno,jdbcType=BIGINT},
      </if>
      <if test="formResultTableId != null">
        #{formResultTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultLabel != null">
        #{formResultLabel,jdbcType=VARCHAR},
      </if>
      <if test="formResultKey != null">
        #{formResultKey,jdbcType=VARCHAR},
      </if>
      <if test="formResultValue != null">
        #{formResultValue,jdbcType=VARCHAR},
      </if>
      <if test="formResultOriginalValue != null">
        #{formResultOriginalValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="queryMethod != null">
        #{queryMethod,jdbcType=VARCHAR},
      </if>
      <if test="ifSystem != null">
        #{ifSystem,jdbcType=BIT},
      </if>
      <if test="replyCloseStatus != null">
        #{replyCloseStatus,jdbcType=VARCHAR},
      </if>
      <if test="closeReason != null">
        #{closeReason,jdbcType=VARCHAR},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeUser != null">
        #{closeUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="userRoleCode != null">
        #{userRoleCode,jdbcType=VARCHAR},
      </if>
      <if test="receiveUserId != null">
        #{receiveUserId,jdbcType=VARCHAR},
      </if>
      <if test="receiveRule != null">
        #{receiveRule,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="projectOrgId != null">
        #{projectOrgId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.ProjectTesteeChallengeExample" resultType="java.lang.Long">
    select count(*) from project_testee_challenge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update project_testee_challenge
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.dvpRuleId != null">
        dvp_rule_id = #{record.dvpRuleId,jdbcType=BIGINT},
      </if>
      <if test="record.testeeId != null">
        testee_id = #{record.testeeId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=BIGINT},
      </if>
      <if test="record.visitId != null">
        visit_id = #{record.visitId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formName != null">
        form_name = #{record.formName,jdbcType=VARCHAR},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.formTableId != null">
        form_table_id = #{record.formTableId,jdbcType=BIGINT},
      </if>
      <if test="record.formResultId != null">
        form_result_id = #{record.formResultId,jdbcType=BIGINT},
      </if>
      <if test="record.formResultTableRowno != null">
        form_result_table_rowno = #{record.formResultTableRowno,jdbcType=BIGINT},
      </if>
      <if test="record.formResultTableId != null">
        form_result_table_id = #{record.formResultTableId,jdbcType=BIGINT},
      </if>
      <if test="record.formResultLabel != null">
        form_result_label = #{record.formResultLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.formResultKey != null">
        form_result_key = #{record.formResultKey,jdbcType=VARCHAR},
      </if>
      <if test="record.formResultValue != null">
        form_result_value = #{record.formResultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.formResultOriginalValue != null">
        form_result_original_value = #{record.formResultOriginalValue,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.queryMethod != null">
        query_method = #{record.queryMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.ifSystem != null">
        if_system = #{record.ifSystem,jdbcType=BIT},
      </if>
      <if test="record.replyCloseStatus != null">
        reply_close_status = #{record.replyCloseStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.closeReason != null">
        close_reason = #{record.closeReason,jdbcType=VARCHAR},
      </if>
      <if test="record.closeTime != null">
        close_time = #{record.closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.closeUser != null">
        close_user = #{record.closeUser,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.userRoleCode != null">
        user_role_code = #{record.userRoleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveUserId != null">
        receive_user_id = #{record.receiveUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveRule != null">
        receive_rule = #{record.receiveRule,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.projectOrgId != null">
        project_org_id = #{record.projectOrgId,jdbcType=VARCHAR},
      </if>


      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update project_testee_challenge
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      dvp_rule_id = #{record.dvpRuleId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_name = #{record.formName,jdbcType=VARCHAR},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      form_result_id = #{record.formResultId,jdbcType=BIGINT},
      form_result_table_rowno = #{record.formResultTableRowno,jdbcType=BIGINT},
      form_result_table_id = #{record.formResultTableId,jdbcType=BIGINT},
      form_result_label = #{record.formResultLabel,jdbcType=VARCHAR},
      form_result_key = #{record.formResultKey,jdbcType=VARCHAR},
      form_result_value = #{record.formResultValue,jdbcType=VARCHAR},
      form_result_original_value = #{record.formResultOriginalValue,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      query_method = #{record.queryMethod,jdbcType=VARCHAR},
      if_system = #{record.ifSystem,jdbcType=BIT},
      reply_close_status = #{record.replyCloseStatus,jdbcType=VARCHAR},
      close_reason = #{record.closeReason,jdbcType=VARCHAR},
      close_time = #{record.closeTime,jdbcType=TIMESTAMP},
      close_user = #{record.closeUser,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      user_role_code = #{record.userRoleCode,jdbcType=VARCHAR},
      receive_user_id = #{record.receiveUserId,jdbcType=VARCHAR},
      receive_rule = #{record.receiveRule,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      project_org_id = #{record.projectOrgId,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update project_testee_challenge
    set id = #{record.id,jdbcType=BIGINT},
      code = #{record.code,jdbcType=VARCHAR},
      dvp_rule_id = #{record.dvpRuleId,jdbcType=BIGINT},
      testee_id = #{record.testeeId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      plan_id = #{record.planId,jdbcType=BIGINT},
      visit_id = #{record.visitId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_name = #{record.formName,jdbcType=VARCHAR},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      form_table_id = #{record.formTableId,jdbcType=BIGINT},
      form_result_id = #{record.formResultId,jdbcType=BIGINT},
      form_result_table_rowno = #{record.formResultTableRowno,jdbcType=BIGINT},
      form_result_table_id = #{record.formResultTableId,jdbcType=BIGINT},
      form_result_label = #{record.formResultLabel,jdbcType=VARCHAR},
      form_result_key = #{record.formResultKey,jdbcType=VARCHAR},
      form_result_value = #{record.formResultValue,jdbcType=VARCHAR},
      form_result_original_value = #{record.formResultOriginalValue,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      query_method = #{record.queryMethod,jdbcType=VARCHAR},
      if_system = #{record.ifSystem,jdbcType=BIT},
      reply_close_status = #{record.replyCloseStatus,jdbcType=VARCHAR},
      close_reason = #{record.closeReason,jdbcType=VARCHAR},
      close_time = #{record.closeTime,jdbcType=TIMESTAMP},
      close_user = #{record.closeUser,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      user_role_code = #{record.userRoleCode,jdbcType=VARCHAR},
      receive_user_id = #{record.receiveUserId,jdbcType=VARCHAR},
      receive_rule = #{record.receiveRule,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      project_org_id = #{record.projectOrgId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.ProjectTesteeChallenge">
    update project_testee_challenge
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="dvpRuleId != null">
        dvp_rule_id = #{dvpRuleId,jdbcType=BIGINT},
      </if>
      <if test="testeeId != null">
        testee_id = #{testeeId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=BIGINT},
      </if>
      <if test="visitId != null">
        visit_id = #{visitId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formName != null">
        form_name = #{formName,jdbcType=VARCHAR},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="formTableId != null">
        form_table_id = #{formTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultId != null">
        form_result_id = #{formResultId,jdbcType=BIGINT},
      </if>
      <if test="formResultTableRowno != null">
        form_result_table_rowno = #{formResultTableRowno,jdbcType=BIGINT},
      </if>
      <if test="formResultTableId != null">
        form_result_table_id = #{formResultTableId,jdbcType=BIGINT},
      </if>
      <if test="formResultLabel != null">
        form_result_label = #{formResultLabel,jdbcType=VARCHAR},
      </if>
      <if test="formResultKey != null">
        form_result_key = #{formResultKey,jdbcType=VARCHAR},
      </if>
      <if test="formResultValue != null">
        form_result_value = #{formResultValue,jdbcType=VARCHAR},
      </if>
      <if test="formResultOriginalValue != null">
        form_result_original_value = #{formResultOriginalValue,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="queryMethod != null">
        query_method = #{queryMethod,jdbcType=VARCHAR},
      </if>
      <if test="ifSystem != null">
        if_system = #{ifSystem,jdbcType=BIT},
      </if>
      <if test="replyCloseStatus != null">
        reply_close_status = #{replyCloseStatus,jdbcType=VARCHAR},
      </if>
      <if test="closeReason != null">
        close_reason = #{closeReason,jdbcType=VARCHAR},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeUser != null">
        close_user = #{closeUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="userRoleCode != null">
        user_role_code = #{userRoleCode,jdbcType=VARCHAR},
      </if>
      <if test="receiveUserId != null">
        receive_user_id = #{receiveUserId,jdbcType=VARCHAR},
      </if>
      <if test="receiveRule != null">
        receive_rule = #{receiveRule,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="projectOrgId != null">
        project_org_id = #{projectOrgId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.haoys.user.model.ProjectTesteeChallenge">
    update project_testee_challenge
    set code = #{code,jdbcType=VARCHAR},
      dvp_rule_id = #{dvpRuleId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_name = #{formName,jdbcType=VARCHAR},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      form_result_id = #{formResultId,jdbcType=BIGINT},
      form_result_table_rowno = #{formResultTableRowno,jdbcType=BIGINT},
      form_result_table_id = #{formResultTableId,jdbcType=BIGINT},
      form_result_label = #{formResultLabel,jdbcType=VARCHAR},
      form_result_key = #{formResultKey,jdbcType=VARCHAR},
      form_result_value = #{formResultValue,jdbcType=VARCHAR},
      form_result_original_value = #{formResultOriginalValue,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      query_method = #{queryMethod,jdbcType=VARCHAR},
      if_system = #{ifSystem,jdbcType=BIT},
      reply_close_status = #{replyCloseStatus,jdbcType=VARCHAR},
      close_reason = #{closeReason,jdbcType=VARCHAR},
      close_time = #{closeTime,jdbcType=TIMESTAMP},
      close_user = #{closeUser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      user_role_code = #{userRoleCode,jdbcType=VARCHAR},
      receive_user_id = #{receiveUserId,jdbcType=VARCHAR},
      receive_rule = #{receiveRule,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      project_org_id = #{projectOrgId,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.ProjectTesteeChallenge">
    update project_testee_challenge
    set code = #{code,jdbcType=VARCHAR},
      dvp_rule_id = #{dvpRuleId,jdbcType=BIGINT},
      testee_id = #{testeeId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      plan_id = #{planId,jdbcType=BIGINT},
      visit_id = #{visitId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_name = #{formName,jdbcType=VARCHAR},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      form_table_id = #{formTableId,jdbcType=BIGINT},
      form_result_id = #{formResultId,jdbcType=BIGINT},
      form_result_table_rowno = #{formResultTableRowno,jdbcType=BIGINT},
      form_result_table_id = #{formResultTableId,jdbcType=BIGINT},
      form_result_label = #{formResultLabel,jdbcType=VARCHAR},
      form_result_key = #{formResultKey,jdbcType=VARCHAR},
      form_result_value = #{formResultValue,jdbcType=VARCHAR},
      form_result_original_value = #{formResultOriginalValue,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      query_method = #{queryMethod,jdbcType=VARCHAR},
      if_system = #{ifSystem,jdbcType=BIT},
      reply_close_status = #{replyCloseStatus,jdbcType=VARCHAR},
      close_reason = #{closeReason,jdbcType=VARCHAR},
      close_time = #{closeTime,jdbcType=TIMESTAMP},
      close_user = #{closeUser,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      user_role_code = #{userRoleCode,jdbcType=VARCHAR},
      receive_user_id = #{receiveUserId,jdbcType=VARCHAR},
      receive_rule = #{receiveRule,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      project_org_id = #{projectOrgId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getProjectChallengeListForPage" resultType="com.haoys.user.domain.vo.project.ProjectChallengeVo">
    SELECT
    project_testee_challenge.id,
    project_testee_info.code testeeCode,
    project_visit_user.status testeeStatusValue,
    project_testee_challenge.code,
    project_testee_info.id testeeId,
    project_testee_info.real_name testeeRealName,
    project_testee_info.owner_org_name testeeOrgName,
    project_testee_challenge.visit_id visitId,
    project_visit_config.visit_name visitName,
    project_testee_challenge.form_id formId,
    project_testee_challenge.form_name formName,
    project_testee_challenge.form_detail_id formDetailId,
    project_testee_challenge.form_table_id formTableId,
    project_testee_challenge.form_result_id formResultId,
    project_testee_challenge.form_result_table_rowno formResultTableRowno,
    project_testee_challenge.form_result_table_id formResultTableId,
    project_testee_challenge.form_result_label label,
    project_testee_challenge.form_result_value formResultValue,
    project_testee_challenge.form_result_original_value formResultOriginalValue,
    project_testee_challenge.content,
    project_testee_challenge.create_user createUserId,
    project_testee_challenge.create_time createTime,
    project_testee_challenge.type,
    project_testee_challenge.reply_close_status replyCloseStatus,
    project_testee_challenge.close_reason closeReason
    FROM
    project_testee_info
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_info.id AND project_visit_user.project_id = #{projectId}
    AND project_visit_user.review_status = '013002'
    INNER JOIN project_testee_challenge ON project_testee_challenge.testee_id = project_testee_info.id
    INNER JOIN project_visit_config ON project_visit_config.id = project_testee_challenge.visit_id
    LEFT JOIN template_form_detail ON template_form_detail.id = project_testee_challenge.form_detail_id
    LEFT JOIN template_form_table ON template_form_table.id = project_testee_challenge.form_table_id
    WHERE
    project_testee_challenge.project_id = #{projectId}
    <if test="visitId != null and visitId != ''">
      and project_testee_challenge.visit_id = #{visitId}
    </if>
    <if test="code != null">
      and project_testee_challenge.code = #{code}
    </if>
    <if test="testeeSearcherName != null">
      and project_testee_info.code like concat('%', #{testeeSearcherName}, '%') or project_testee_info.real_name like concat('%', #{testeeSearcherName}, '%')
    </if>
    <if test="realName != null">
      and project_testee_info.real_name = #{realName}
    </if>
    <if test="orgId != null and orgId != ''">
      and project_testee_info.owner_org_id IN (${orgId})
    </if>
    <if test="status != null">
      and project_visit_user.status IN(${status})
    </if>
    <if test="replyCloseStatus != null">
      and project_testee_challenge.reply_close_status IN (${replyCloseStatus})
    </if>
    <if test="createUser != null and queryFlag == false">
      AND project_testee_challenge.create_user = #{createUser}
    </if>
    <if test="testeeId != null and testeeId != ''">
      AND project_testee_info.id = #{testeeId}
    </if>
    order by project_testee_challenge.create_time desc
  </select>

  <select id="getProjectChallengeList" resultType="com.haoys.user.domain.vo.project.ProjectChallengeVo">
    SELECT
      project_testee_challenge.id,
      project_visit_user.testee_code testeeCode,
      project_testee_challenge.code,
      project_testee_info.id testeeId,
      project_testee_info.real_name testeeRealName,
      project_testee_challenge.visit_id visitId,
      project_testee_challenge.form_id formId,
      project_testee_challenge.form_name formName,
      project_testee_challenge.form_detail_id formDetailId,
      project_testee_challenge.form_table_id formTableId,
      project_testee_challenge.form_result_id formResultId,
      project_testee_challenge.form_result_table_rowno formResultTableRowno,
      project_testee_challenge.form_result_table_id formResultTableId,
      project_testee_challenge.form_result_label label,
      project_testee_challenge.form_result_value formResultValue,
      project_testee_challenge.form_result_original_value formResultOriginalValue,
      project_testee_challenge.content,
      project_testee_challenge.create_user createUserId,
      project_testee_challenge.create_time createTime,
      project_testee_challenge.type,
      project_testee_challenge.if_system ifSystem,
      project_testee_challenge.reply_close_status replyCloseStatus,
      project_testee_challenge.close_reason closeReason
    FROM
        project_testee_challenge
    inner join project_testee_info on project_testee_challenge.testee_id = project_testee_info.id
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_challenge.testee_id AND project_visit_user.project_id = project_testee_challenge.project_id
    WHERE
      project_testee_challenge.project_id = #{projectId}

    <if test="code != null and code != ''">
      AND project_testee_challenge.code like concat('%', #{code}, '%')
    </if>
    <if test="testeeSearcherName != null and testeeSearcherName != ''">
      and (project_testee_info.code like concat('%', #{testeeSearcherName}, '%') or project_testee_info.real_name like concat('%', #{testeeSearcherName}, '%'))
    </if>
    <if test="orgId != null and orgId != ''">
      and project_testee_challenge.project_org_id=${orgId}
    </if>
    <if test="replyCloseStatus != null and replyCloseStatus != ''">
      and project_testee_challenge.reply_close_status=${replyCloseStatus}
    </if>
    <if test="createUser != null">
      AND project_testee_challenge.create_user = #{createUser}
    </if>
    <if test="startDate != null and startDate != ''">
      AND project_testee_challenge.create_time <![CDATA[>=]]> #{startDate}
    </if>
    <if test="endDate != null and endDate != ''">
      AND project_testee_challenge.create_time <![CDATA[<=]]> #{endDate}
    </if>
    order by project_testee_challenge.create_time desc
  </select>
    <select id="list" resultType="com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo">
      SELECT
      DISTINCT
      soi.org_id as ownerOrgId,
      soi.org_name as ownerOrgName
      FROM
      project_user_org uor
      JOIN system_org_info soi on soi.org_id=uor.org_id
      WHERE
      uor.project_id = #{projectId}
      AND soi.org_id IN
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
      and uor.org_type=2
      GROUP BY
        soi.org_name
      ORDER BY
      CONVERT ( soi.org_name USING gbk ) COLLATE gbk_chinese_ci ASC

    </select>
  <select id="getChallengeListByOrgIds"
          resultType="com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo">
    SELECT
      SUM( CASE replyStatus WHEN '006001' THEN count ELSE 0 END ) AS newChallengeNum,
      SUM( CASE replyStatus WHEN '006002' THEN count ELSE 0 END ) AS receiveChallengeNum,
      SUM( CASE replyStatus WHEN '006003' THEN count ELSE 0 END ) AS closeChallengeNum,
      ownerOrgId,
      ownerOrgName
    FROM
      (
        SELECT
          count( 0 ) AS count,
		pt.owner_org_id ownerOrgId,
        soi.org_name AS ownerOrgName,
		ptc.reply_close_status AS replyStatus
        FROM
            project_testee_info pt
          JOIN system_org_info soi on soi.org_id=pt.owner_org_id
          JOIN project_visit_user pvu ON pt.id = pvu.testee_id
          AND pvu.review_status = '013002'
          JOIN project_testee_challenge ptc ON ptc.testee_id = pvu.testee_id
          AND ptc.project_id = pvu.project_id
        WHERE
          ptc.project_id = #{projectId}
          AND pt.owner_org_id IN
          <foreach collection="ids" open="(" separator="," close=")" item="item">
            #{item}
          </foreach>
        GROUP BY
          pt.owner_org_id,
          pt.owner_org_name,
          ptc.reply_close_status
      ) tmep GROUP BY ownerOrgName,ownerOrgId  ORDER BY
      CONVERT ( ownerOrgName USING gbk ) COLLATE gbk_chinese_ci ASC
  </select>

  <select id="challengeChart" resultType="com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo">
      SELECT
      COUNT(0) as count,
      ptc.reply_close_status as replyStatus
      FROM
    project_testee_info pt
      join project_visit_user pvu on pt.id=pvu.testee_id and pvu.review_status=#{reviewStatus}
      JOIN project_testee_challenge ptc ON ptc.testee_id = pvu.testee_id and ptc.project_id=pvu.project_id
      WHERE
      ptc.project_id = #{projectId}
      and pt.owner_org_id in
      <foreach collection="ids" open="(" separator="," close=")" item="item">
        #{item}
      </foreach>
      GROUP BY	ptc.reply_close_status
  </select>
  <select id="getSystemChallenge" resultType="Integer">
    SELECT
    COUNT(0) as count
    FROM
    project_testee_info pt
    join project_visit_user pvu on pt.id=pvu.testee_id and pvu.review_status=#{reviewStatus}
    JOIN project_testee_challenge ptc ON ptc.testee_id = pvu.testee_id and ptc.project_id=pvu.project_id
    WHERE
    ptc.project_id = #{projectId} and ptc.if_system=1
    and pt.owner_org_id in
    <foreach collection="ids" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>

  </select>

  <select id="getCraChallenge" resultType="Integer">
    SELECT
        COUNT(0) as count
    FROM
    project_testee_info pt
    join project_visit_user pvu on pt.id=pvu.testee_id and pvu.review_status=#{reviewStatus}
    JOIN project_testee_challenge ptc ON ptc.testee_id = pvu.testee_id and ptc.project_id=pvu.project_id
    WHERE
    ptc.project_id = #{projectId}
    <if test="roleCode != null and roleCode != ''">
      and ptc.user_role_code=#{roleCode}
    </if>
    and pt.owner_org_id in
    <foreach collection="ids" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>

  </select>
  <select id="selectCustomChallengeList" resultType="com.haoys.user.model.ProjectTesteeChallenge">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from project_testee_challenge
    where
    project_id = #{projectId}
    and project_org_id = #{projectOrgId}
    and visit_id = #{visitId}
    and form_id = #{formId}
    and testee_id = #{testeeId}
    <if test="!isSP">
      and (create_user=#{userId} or receive_user_id= #{userId}
      <if test="roleId != null and roleId.size() > 0 ">
        or receive_rule in
        <foreach collection="roleId" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      )
    </if>
    and if_system=0
  </select>
  <select id="getUserChallengeListForPage" resultType="com.haoys.user.domain.vo.project.ProjectChallengeVo">
    SELECT
    project_testee_challenge.id,
    project_visit_user.testee_code testeeCode,
    project_testee_challenge.code,
    project_testee_info.id testeeId,
    project_testee_info.real_name testeeRealName,
    project_testee_challenge.project_id projectId,
    project_testee_challenge.project_org_id projectOrgId,
    project_testee_challenge.visit_id visitId,
    project_testee_challenge.form_id formId,
    project_testee_challenge.form_name formName,
    project_testee_challenge.form_detail_id formDetailId,
    project_testee_challenge.form_table_id formTableId,
    project_testee_challenge.form_result_id formResultId,
    project_testee_challenge.form_result_table_rowno formResultTableRowno,
    project_testee_challenge.form_result_table_id formResultTableId,
    project_testee_challenge.form_result_label label,
    project_testee_challenge.form_result_value formResultValue,
    project_testee_challenge.form_result_original_value formResultOriginalValue,
    project_testee_challenge.content,
    project_testee_challenge.create_user createUserId,
    project_testee_challenge.create_time createTime,
    project_testee_challenge.type,
    project_testee_challenge.if_system ifSystem,
    project_testee_challenge.reply_close_status replyCloseStatus,
    project_testee_challenge.close_reason closeReason
    FROM
    project_testee_challenge
    inner join project_testee_info on project_testee_challenge.testee_id = project_testee_info.id
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_challenge.testee_id AND project_visit_user.project_id = project_testee_challenge.project_id
    WHERE 1=1
    and (project_testee_challenge.create_user=#{userId} or project_testee_challenge.receive_user_id= #{userId} or project_testee_challenge.receive_rule in
    <foreach collection="roleId" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
    )
    <if test="testeeSearcherName != null">
      and project_testee_info.code like concat('%', #{testeeSearcherName}, '%') or project_testee_info.real_name like concat('%', #{testeeSearcherName}, '%')
    </if>
    <if test="orgId != null and orgId != ''">
      and project_testee_challenge.project_org_id=${orgId}
    </if>
    <if test="replyCloseStatus != null">
      and project_testee_challenge.reply_close_status=${replyCloseStatus}
    </if>
    <if test="createUser != null">
      AND project_testee_challenge.create_user = #{createUser}
    </if>
    <if test="startDate != null and startDate != ''">
      AND project_testee_challenge.create_time <![CDATA[>=]]> #{startDate}
    </if>
    <if test="endDate != null and endDate != ''">
      AND project_testee_challenge.create_time <![CDATA[<=]]> #{endDate}
    </if>
    and project_testee_challenge.reply_close_status='006002'
    order by project_testee_challenge.create_time desc
  </select>
  <select id="getUserChallengeNum" resultType="java.lang.Long">
    SELECT
        count(project_testee_challenge.id)
    FROM
    project_testee_challenge
    inner join project_testee_info on project_testee_challenge.testee_id = project_testee_info.id
    INNER JOIN project_visit_user ON project_visit_user.testee_id = project_testee_challenge.testee_id AND project_visit_user.project_id = project_testee_challenge.project_id
    WHERE 1=1
    and (project_testee_challenge.create_user=#{userId} or project_testee_challenge.receive_user_id= #{userId} or project_testee_challenge.receive_rule in
    <foreach collection="roleId" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
    )
    and project_testee_challenge.reply_close_status='006002'
  </select>

</mapper>
