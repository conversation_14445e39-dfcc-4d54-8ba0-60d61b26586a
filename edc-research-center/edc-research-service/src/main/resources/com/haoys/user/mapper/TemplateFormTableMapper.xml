<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.TemplateFormTableMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.TemplateFormTable">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="template_id" jdbcType="BIGINT" property="templateId" />
    <result column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="form_detail_id" jdbcType="BIGINT" property="formDetailId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="lang_value" jdbcType="VARCHAR" property="langValue" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="placeholder" jdbcType="VARCHAR" property="placeholder" />
    <result column="panel_size" jdbcType="VARCHAR" property="panelSize" />
    <result column="hidden" jdbcType="BIT" property="hidden" />
    <result column="require_type" jdbcType="VARCHAR" property="requireType" />
    <result column="required" jdbcType="BIT" property="required" />
    <result column="show_title" jdbcType="BIT" property="showTitle" />
    <result column="show_content" jdbcType="BIT" property="showContent" />
    <result column="default_value" jdbcType="VARCHAR" property="defaultValue" />
    <result column="unit_value" jdbcType="VARCHAR" property="unitValue" />
    <result column="dic_resource" jdbcType="VARCHAR" property="dicResource" />
    <result column="ref_dic_id" jdbcType="VARCHAR" property="refDicId" />
    <result column="default_dic_value" jdbcType="VARCHAR" property="defaultDicValue" />
    <result column="options" jdbcType="VARCHAR" property="options" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="copy_variable_id" jdbcType="BIGINT" property="copyVariableId" />
    <result column="copy_table_id" jdbcType="BIGINT" property="copyTableId" />
    <result column="ext_data_1" jdbcType="VARCHAR" property="extData1" />
    <result column="ext_data_2" jdbcType="VARCHAR" property="extData2" />
    <result column="ext_data_3" jdbcType="VARCHAR" property="extData3" />
    <result column="ext_data_4" jdbcType="VARCHAR" property="extData4" />
    <result column="point_variable_id" jdbcType="BIGINT" property="pointVariableId" />
    <result column="enable_associate" jdbcType="BIT" property="enableAssociate" />
    <result column="condition_expression" jdbcType="VARCHAR" property="conditionExpression" />
    <result column="enable_view_config" jdbcType="BIT" property="enableViewConfig" />
    <result column="lab_config_scope" jdbcType="VARCHAR" property="labConfigScope" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="version_info" jdbcType="VARCHAR" property="versionInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, project_id, form_id, form_detail_id, type, model, label, field_name, 
    lang_value, title, content, placeholder, panel_size, hidden, require_type, required, 
    show_title, show_content, default_value, unit_value, dic_resource, ref_dic_id, default_dic_value, 
    options, expand, copy_variable_id, copy_table_id, ext_data_1, ext_data_2, ext_data_3, 
    ext_data_4, point_variable_id, enable_associate, condition_expression, enable_view_config, 
    lab_config_scope, sort, status, version_info, create_time, update_time, create_user, 
    update_user, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.TemplateFormTableExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from template_form_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from template_form_table
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from template_form_table
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.TemplateFormTableExample">
    delete from template_form_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.TemplateFormTable">
    insert into template_form_table (id, template_id, project_id, 
      form_id, form_detail_id, type, 
      model, label, field_name, 
      lang_value, title, content, 
      placeholder, panel_size, hidden, 
      require_type, required, show_title, 
      show_content, default_value, unit_value, 
      dic_resource, ref_dic_id, default_dic_value, 
      options, expand, copy_variable_id, 
      copy_table_id, ext_data_1, ext_data_2, 
      ext_data_3, ext_data_4, point_variable_id, 
      enable_associate, condition_expression, enable_view_config, 
      lab_config_scope, sort, status, 
      version_info, create_time, update_time, 
      create_user, update_user, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{templateId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, 
      #{formId,jdbcType=BIGINT}, #{formDetailId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR}, #{fieldName,jdbcType=VARCHAR}, 
      #{langValue,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, 
      #{placeholder,jdbcType=VARCHAR}, #{panelSize,jdbcType=VARCHAR}, #{hidden,jdbcType=BIT}, 
      #{requireType,jdbcType=VARCHAR}, #{required,jdbcType=BIT}, #{showTitle,jdbcType=BIT}, 
      #{showContent,jdbcType=BIT}, #{defaultValue,jdbcType=VARCHAR}, #{unitValue,jdbcType=VARCHAR}, 
      #{dicResource,jdbcType=VARCHAR}, #{refDicId,jdbcType=VARCHAR}, #{defaultDicValue,jdbcType=VARCHAR}, 
      #{options,jdbcType=VARCHAR}, #{expand,jdbcType=VARCHAR}, #{copyVariableId,jdbcType=BIGINT}, 
      #{copyTableId,jdbcType=BIGINT}, #{extData1,jdbcType=VARCHAR}, #{extData2,jdbcType=VARCHAR}, 
      #{extData3,jdbcType=VARCHAR}, #{extData4,jdbcType=VARCHAR}, #{pointVariableId,jdbcType=BIGINT}, 
      #{enableAssociate,jdbcType=BIT}, #{conditionExpression,jdbcType=VARCHAR}, #{enableViewConfig,jdbcType=BIT}, 
      #{labConfigScope,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{versionInfo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createUser,jdbcType=VARCHAR}, #{updateUser,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.TemplateFormTable">
    insert into template_form_table
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="formId != null">
        form_id,
      </if>
      <if test="formDetailId != null">
        form_detail_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="langValue != null">
        lang_value,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="placeholder != null">
        placeholder,
      </if>
      <if test="panelSize != null">
        panel_size,
      </if>
      <if test="hidden != null">
        hidden,
      </if>
      <if test="requireType != null">
        require_type,
      </if>
      <if test="required != null">
        required,
      </if>
      <if test="showTitle != null">
        show_title,
      </if>
      <if test="showContent != null">
        show_content,
      </if>
      <if test="defaultValue != null">
        default_value,
      </if>
      <if test="unitValue != null">
        unit_value,
      </if>
      <if test="dicResource != null">
        dic_resource,
      </if>
      <if test="refDicId != null">
        ref_dic_id,
      </if>
      <if test="defaultDicValue != null">
        default_dic_value,
      </if>
      <if test="options != null">
        options,
      </if>
      <if test="expand != null">
        expand,
      </if>
      <if test="copyVariableId != null">
        copy_variable_id,
      </if>
      <if test="copyTableId != null">
        copy_table_id,
      </if>
      <if test="extData1 != null">
        ext_data_1,
      </if>
      <if test="extData2 != null">
        ext_data_2,
      </if>
      <if test="extData3 != null">
        ext_data_3,
      </if>
      <if test="extData4 != null">
        ext_data_4,
      </if>
      <if test="pointVariableId != null">
        point_variable_id,
      </if>
      <if test="enableAssociate != null">
        enable_associate,
      </if>
      <if test="conditionExpression != null">
        condition_expression,
      </if>
      <if test="enableViewConfig != null">
        enable_view_config,
      </if>
      <if test="labConfigScope != null">
        lab_config_scope,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="versionInfo != null">
        version_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        #{projectId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="langValue != null">
        #{langValue,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="placeholder != null">
        #{placeholder,jdbcType=VARCHAR},
      </if>
      <if test="panelSize != null">
        #{panelSize,jdbcType=VARCHAR},
      </if>
      <if test="hidden != null">
        #{hidden,jdbcType=BIT},
      </if>
      <if test="requireType != null">
        #{requireType,jdbcType=VARCHAR},
      </if>
      <if test="required != null">
        #{required,jdbcType=BIT},
      </if>
      <if test="showTitle != null">
        #{showTitle,jdbcType=BIT},
      </if>
      <if test="showContent != null">
        #{showContent,jdbcType=BIT},
      </if>
      <if test="defaultValue != null">
        #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="dicResource != null">
        #{dicResource,jdbcType=VARCHAR},
      </if>
      <if test="refDicId != null">
        #{refDicId,jdbcType=VARCHAR},
      </if>
      <if test="defaultDicValue != null">
        #{defaultDicValue,jdbcType=VARCHAR},
      </if>
      <if test="options != null">
        #{options,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="copyVariableId != null">
        #{copyVariableId,jdbcType=BIGINT},
      </if>
      <if test="copyTableId != null">
        #{copyTableId,jdbcType=BIGINT},
      </if>
      <if test="extData1 != null">
        #{extData1,jdbcType=VARCHAR},
      </if>
      <if test="extData2 != null">
        #{extData2,jdbcType=VARCHAR},
      </if>
      <if test="extData3 != null">
        #{extData3,jdbcType=VARCHAR},
      </if>
      <if test="extData4 != null">
        #{extData4,jdbcType=VARCHAR},
      </if>
      <if test="pointVariableId != null">
        #{pointVariableId,jdbcType=BIGINT},
      </if>
      <if test="enableAssociate != null">
        #{enableAssociate,jdbcType=BIT},
      </if>
      <if test="conditionExpression != null">
        #{conditionExpression,jdbcType=VARCHAR},
      </if>
      <if test="enableViewConfig != null">
        #{enableViewConfig,jdbcType=BIT},
      </if>
      <if test="labConfigScope != null">
        #{labConfigScope,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="versionInfo != null">
        #{versionInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.TemplateFormTableExample" resultType="java.lang.Long">
    select count(*) from template_form_table
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update template_form_table
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=BIGINT},
      </if>
      <if test="record.projectId != null">
        project_id = #{record.projectId,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.formDetailId != null">
        form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldName != null">
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.langValue != null">
        lang_value = #{record.langValue,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.placeholder != null">
        placeholder = #{record.placeholder,jdbcType=VARCHAR},
      </if>
      <if test="record.panelSize != null">
        panel_size = #{record.panelSize,jdbcType=VARCHAR},
      </if>
      <if test="record.hidden != null">
        hidden = #{record.hidden,jdbcType=BIT},
      </if>
      <if test="record.requireType != null">
        require_type = #{record.requireType,jdbcType=VARCHAR},
      </if>
      <if test="record.required != null">
        required = #{record.required,jdbcType=BIT},
      </if>
      <if test="record.showTitle != null">
        show_title = #{record.showTitle,jdbcType=BIT},
      </if>
      <if test="record.showContent != null">
        show_content = #{record.showContent,jdbcType=BIT},
      </if>
      <if test="record.defaultValue != null">
        default_value = #{record.defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="record.unitValue != null">
        unit_value = #{record.unitValue,jdbcType=VARCHAR},
      </if>
      <if test="record.dicResource != null">
        dic_resource = #{record.dicResource,jdbcType=VARCHAR},
      </if>
      <if test="record.refDicId != null">
        ref_dic_id = #{record.refDicId,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultDicValue != null">
        default_dic_value = #{record.defaultDicValue,jdbcType=VARCHAR},
      </if>
      <if test="record.options != null">
        options = #{record.options,jdbcType=VARCHAR},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.copyVariableId != null">
        copy_variable_id = #{record.copyVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.copyTableId != null">
        copy_table_id = #{record.copyTableId,jdbcType=BIGINT},
      </if>
      <if test="record.extData1 != null">
        ext_data_1 = #{record.extData1,jdbcType=VARCHAR},
      </if>
      <if test="record.extData2 != null">
        ext_data_2 = #{record.extData2,jdbcType=VARCHAR},
      </if>
      <if test="record.extData3 != null">
        ext_data_3 = #{record.extData3,jdbcType=VARCHAR},
      </if>
      <if test="record.extData4 != null">
        ext_data_4 = #{record.extData4,jdbcType=VARCHAR},
      </if>
      <if test="record.pointVariableId != null">
        point_variable_id = #{record.pointVariableId,jdbcType=BIGINT},
      </if>
      <if test="record.enableAssociate != null">
        enable_associate = #{record.enableAssociate,jdbcType=BIT},
      </if>
      <if test="record.conditionExpression != null">
        condition_expression = #{record.conditionExpression,jdbcType=VARCHAR},
      </if>
      <if test="record.enableViewConfig != null">
        enable_view_config = #{record.enableViewConfig,jdbcType=BIT},
      </if>
      <if test="record.labConfigScope != null">
        lab_config_scope = #{record.labConfigScope,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.versionInfo != null">
        version_info = #{record.versionInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUser != null">
        create_user = #{record.createUser,jdbcType=VARCHAR},
      </if>
      <if test="record.updateUser != null">
        update_user = #{record.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update template_form_table
    set id = #{record.id,jdbcType=BIGINT},
      template_id = #{record.templateId,jdbcType=BIGINT},
      project_id = #{record.projectId,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      form_detail_id = #{record.formDetailId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      label = #{record.label,jdbcType=VARCHAR},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      lang_value = #{record.langValue,jdbcType=VARCHAR},
      title = #{record.title,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      placeholder = #{record.placeholder,jdbcType=VARCHAR},
      panel_size = #{record.panelSize,jdbcType=VARCHAR},
      hidden = #{record.hidden,jdbcType=BIT},
      require_type = #{record.requireType,jdbcType=VARCHAR},
      required = #{record.required,jdbcType=BIT},
      show_title = #{record.showTitle,jdbcType=BIT},
      show_content = #{record.showContent,jdbcType=BIT},
      default_value = #{record.defaultValue,jdbcType=VARCHAR},
      unit_value = #{record.unitValue,jdbcType=VARCHAR},
      dic_resource = #{record.dicResource,jdbcType=VARCHAR},
      ref_dic_id = #{record.refDicId,jdbcType=VARCHAR},
      default_dic_value = #{record.defaultDicValue,jdbcType=VARCHAR},
      options = #{record.options,jdbcType=VARCHAR},
      expand = #{record.expand,jdbcType=VARCHAR},
      copy_variable_id = #{record.copyVariableId,jdbcType=BIGINT},
      copy_table_id = #{record.copyTableId,jdbcType=BIGINT},
      ext_data_1 = #{record.extData1,jdbcType=VARCHAR},
      ext_data_2 = #{record.extData2,jdbcType=VARCHAR},
      ext_data_3 = #{record.extData3,jdbcType=VARCHAR},
      ext_data_4 = #{record.extData4,jdbcType=VARCHAR},
      point_variable_id = #{record.pointVariableId,jdbcType=BIGINT},
      enable_associate = #{record.enableAssociate,jdbcType=BIT},
      condition_expression = #{record.conditionExpression,jdbcType=VARCHAR},
      enable_view_config = #{record.enableViewConfig,jdbcType=BIT},
      lab_config_scope = #{record.labConfigScope,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      status = #{record.status,jdbcType=VARCHAR},
      version_info = #{record.versionInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_user = #{record.createUser,jdbcType=VARCHAR},
      update_user = #{record.updateUser,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.TemplateFormTable">
    update template_form_table
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=BIGINT},
      </if>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=BIGINT},
      </if>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="formDetailId != null">
        form_detail_id = #{formDetailId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="langValue != null">
        lang_value = #{langValue,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="placeholder != null">
        placeholder = #{placeholder,jdbcType=VARCHAR},
      </if>
      <if test="panelSize != null">
        panel_size = #{panelSize,jdbcType=VARCHAR},
      </if>
      <if test="hidden != null">
        hidden = #{hidden,jdbcType=BIT},
      </if>
      <if test="requireType != null">
        require_type = #{requireType,jdbcType=VARCHAR},
      </if>
      <if test="required != null">
        required = #{required,jdbcType=BIT},
      </if>
      <if test="showTitle != null">
        show_title = #{showTitle,jdbcType=BIT},
      </if>
      <if test="showContent != null">
        show_content = #{showContent,jdbcType=BIT},
      </if>
      <if test="defaultValue != null">
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="unitValue != null">
        unit_value = #{unitValue,jdbcType=VARCHAR},
      </if>
      <if test="dicResource != null">
        dic_resource = #{dicResource,jdbcType=VARCHAR},
      </if>
      <if test="refDicId != null">
        ref_dic_id = #{refDicId,jdbcType=VARCHAR},
      </if>
      <if test="defaultDicValue != null">
        default_dic_value = #{defaultDicValue,jdbcType=VARCHAR},
      </if>
      <if test="options != null">
        options = #{options,jdbcType=VARCHAR},
      </if>
      <if test="expand != null">
        expand = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="copyVariableId != null">
        copy_variable_id = #{copyVariableId,jdbcType=BIGINT},
      </if>
      <if test="copyTableId != null">
        copy_table_id = #{copyTableId,jdbcType=BIGINT},
      </if>
      <if test="extData1 != null">
        ext_data_1 = #{extData1,jdbcType=VARCHAR},
      </if>
      <if test="extData2 != null">
        ext_data_2 = #{extData2,jdbcType=VARCHAR},
      </if>
      <if test="extData3 != null">
        ext_data_3 = #{extData3,jdbcType=VARCHAR},
      </if>
      <if test="extData4 != null">
        ext_data_4 = #{extData4,jdbcType=VARCHAR},
      </if>
      <if test="pointVariableId != null">
        point_variable_id = #{pointVariableId,jdbcType=BIGINT},
      </if>
      <if test="enableAssociate != null">
        enable_associate = #{enableAssociate,jdbcType=BIT},
      </if>
      <if test="conditionExpression != null">
        condition_expression = #{conditionExpression,jdbcType=VARCHAR},
      </if>
      <if test="enableViewConfig != null">
        enable_view_config = #{enableViewConfig,jdbcType=BIT},
      </if>
      <if test="labConfigScope != null">
        lab_config_scope = #{labConfigScope,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="versionInfo != null">
        version_info = #{versionInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.TemplateFormTable">
    update template_form_table
    set template_id = #{templateId,jdbcType=BIGINT},
      project_id = #{projectId,jdbcType=BIGINT},
      form_id = #{formId,jdbcType=BIGINT},
      form_detail_id = #{formDetailId,jdbcType=BIGINT},
      type = #{type,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      label = #{label,jdbcType=VARCHAR},
      field_name = #{fieldName,jdbcType=VARCHAR},
      lang_value = #{langValue,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      placeholder = #{placeholder,jdbcType=VARCHAR},
      panel_size = #{panelSize,jdbcType=VARCHAR},
      hidden = #{hidden,jdbcType=BIT},
      require_type = #{requireType,jdbcType=VARCHAR},
      required = #{required,jdbcType=BIT},
      show_title = #{showTitle,jdbcType=BIT},
      show_content = #{showContent,jdbcType=BIT},
      default_value = #{defaultValue,jdbcType=VARCHAR},
      unit_value = #{unitValue,jdbcType=VARCHAR},
      dic_resource = #{dicResource,jdbcType=VARCHAR},
      ref_dic_id = #{refDicId,jdbcType=VARCHAR},
      default_dic_value = #{defaultDicValue,jdbcType=VARCHAR},
      options = #{options,jdbcType=VARCHAR},
      expand = #{expand,jdbcType=VARCHAR},
      copy_variable_id = #{copyVariableId,jdbcType=BIGINT},
      copy_table_id = #{copyTableId,jdbcType=BIGINT},
      ext_data_1 = #{extData1,jdbcType=VARCHAR},
      ext_data_2 = #{extData2,jdbcType=VARCHAR},
      ext_data_3 = #{extData3,jdbcType=VARCHAR},
      ext_data_4 = #{extData4,jdbcType=VARCHAR},
      point_variable_id = #{pointVariableId,jdbcType=BIGINT},
      enable_associate = #{enableAssociate,jdbcType=BIT},
      condition_expression = #{conditionExpression,jdbcType=VARCHAR},
      enable_view_config = #{enableViewConfig,jdbcType=BIT},
      lab_config_scope = #{labConfigScope,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      version_info = #{versionInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>