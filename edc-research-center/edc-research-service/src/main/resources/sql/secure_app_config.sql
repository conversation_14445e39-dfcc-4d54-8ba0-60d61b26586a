-- 安全应用配置表
CREATE TABLE IF NOT EXISTS `secure_app_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `app_secret` varchar(128) NOT NULL COMMENT '应用密钥',
  `app_name` varchar(100) NOT NULL COMMENT '应用名称',
  `app_description` varchar(500) DEFAULT NULL COMMENT '应用描述',
  `environment` varchar(20) NOT NULL DEFAULT 'dev' COMMENT '环境标识(dev,test,feature,prod)',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `code_expiration` int(11) NOT NULL DEFAULT 120 COMMENT 'Code有效期(秒)',
  `access_token_expiration` int(11) NOT NULL DEFAULT 3600 COMMENT 'AccessToken有效期(秒)',
  `max_daily_requests` int(11) DEFAULT 10000 COMMENT '每日最大请求次数',
  `allowed_ips` text COMMENT '允许的IP地址列表(JSON格式)',
  `webhook_url` varchar(500) DEFAULT NULL COMMENT '回调地址',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `update_user_id` bigint(20) DEFAULT NULL COMMENT '更新用户ID',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `platform_id` bigint(20) DEFAULT NULL COMMENT '平台ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_id_env` (`app_id`, `environment`),
  KEY `idx_environment` (`environment`),
  KEY `idx_status` (`status`),
  KEY `idx_tenant_platform` (`tenant_id`, `platform_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全应用配置表';

-- 插入默认配置数据
INSERT INTO `secure_app_config` (`app_id`, `app_secret`, `app_name`, `app_description`, `environment`, `status`, `code_expiration`, `access_token_expiration`) VALUES
('edc_app_dev', 'edc_secret_dev_2025_abcdef123456', 'EDC开发环境应用', 'EDC研究中心开发环境默认应用', 'dev', 1, 120, 3600),
('edc_app_test', 'edc_secret_test_2025_123456abcdef', 'EDC测试环境应用', 'EDC研究中心测试环境默认应用', 'test', 1, 120, 3600),
('edc_app_feature', 'edc_secret_feature_2025_fedcba654321', 'EDC功能环境应用', 'EDC研究中心功能环境默认应用', 'feature', 1, 120, 3600),
('edc_app_local', 'edc_secret_local_2025_local123456', 'EDC本地环境应用', 'EDC研究中心本地环境默认应用', 'local', 1, 120, 3600),
('edc_app_prod', 'edc_secret_prod_2025_prod987654321', 'EDC生产环境应用', 'EDC研究中心生产环境默认应用', 'prod', 1, 300, 7200);

-- 创建应用请求统计表
CREATE TABLE IF NOT EXISTS `secure_app_request_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `app_id` varchar(64) NOT NULL COMMENT '应用ID',
  `environment` varchar(20) NOT NULL COMMENT '环境标识',
  `request_type` varchar(20) NOT NULL COMMENT '请求类型(generate_code,get_token,validate_token)',
  `client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `request_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  `response_status` varchar(20) DEFAULT NULL COMMENT '响应状态(success,failed)',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `request_data` text COMMENT '请求数据(JSON格式)',
  `response_data` text COMMENT '响应数据(JSON格式)',
  PRIMARY KEY (`id`),
  KEY `idx_app_id_env` (`app_id`, `environment`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_request_type` (`request_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全应用请求日志表';
