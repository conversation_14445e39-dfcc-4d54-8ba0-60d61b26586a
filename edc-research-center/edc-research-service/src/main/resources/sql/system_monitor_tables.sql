-- 系统访问日志表
CREATE TABLE IF NOT EXISTS `system_access_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `session_id` varchar(128) DEFAULT NULL COMMENT '会话ID',
  `request_url` varchar(500) NOT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法(GET,POST,PUT,DELETE)',
  `request_ip` varchar(45) NOT NULL COMMENT '请求IP地址',
  `location` varchar(200) DEFAULT NULL COMMENT '访问位置',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `access_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  `response_time` bigint(20) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `response_status` int(11) DEFAULT NULL COMMENT '响应状态码',
  `request_params` text COMMENT '请求参数',
  `response_size` bigint(20) DEFAULT NULL COMMENT '响应大小(字节)',
  `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `access_type` varchar(20) DEFAULT 'WEB' COMMENT '访问类型(WEB,API,MOBILE)',
  `is_login_request` tinyint(1) DEFAULT 0 COMMENT '是否为登录请求(0-否,1-是)',
  `login_type` varchar(20) DEFAULT NULL COMMENT '登录类型(USERNAME_PASSWORD,PHONE_CODE,SSO,WECHAT)',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号(用于手机号登录)',
  `login_success` tinyint(1) DEFAULT NULL COMMENT '登录是否成功(0-失败,1-成功,NULL-非登录请求)',
  `login_failure_reason` varchar(200) DEFAULT NULL COMMENT '登录失败原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_name` (`user_name`),
  KEY `idx_request_ip` (`request_ip`),
  KEY `idx_access_time` (`access_time`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_request_url` (`request_url`(255)),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_login_request` (`is_login_request`),
  KEY `idx_login_type` (`login_type`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_login_success` (`login_success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统访问日志表';

-- 在线用户表
CREATE TABLE IF NOT EXISTS `system_online_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `user_name` varchar(100) NOT NULL COMMENT '用户名',
  `real_name` varchar(100) DEFAULT NULL COMMENT '真实姓名',
  `session_id` varchar(128) NOT NULL COMMENT '会话ID',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP地址',
  `login_location` varchar(200) DEFAULT NULL COMMENT '登录位置',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `last_access_time` datetime NOT NULL COMMENT '最后访问时间',
  `last_access_url` varchar(500) DEFAULT NULL COMMENT '最后访问URL',
  `access_count` int(11) DEFAULT 1 COMMENT '访问次数',
  `status` varchar(20) DEFAULT 'ONLINE' COMMENT '状态(ONLINE,OFFLINE,TIMEOUT)',
  `token` varchar(500) DEFAULT NULL COMMENT '访问令牌',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `login_type` varchar(20) DEFAULT NULL COMMENT '登录类型(USERNAME_PASSWORD,PHONE_CODE,SSO,WECHAT)',
  `phone_number` varchar(20) DEFAULT NULL COMMENT '手机号',
  `login_success_count` int(11) DEFAULT 1 COMMENT '成功登录次数',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `total_online_duration` bigint(20) DEFAULT 0 COMMENT '总在线时长(分钟)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_name` (`user_name`),
  KEY `idx_login_ip` (`login_ip`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_last_access_time` (`last_access_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_login_type` (`login_type`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_last_login_time` (`last_login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='在线用户表';

-- 系统访问统计表
CREATE TABLE IF NOT EXISTS `system_access_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_visits` bigint(20) DEFAULT 0 COMMENT '总访问次数',
  `unique_visitors` bigint(20) DEFAULT 0 COMMENT '独立访客数',
  `login_count` bigint(20) DEFAULT 0 COMMENT '登录次数',
  `unique_login_users` bigint(20) DEFAULT 0 COMMENT '独立登录用户数',
  `page_views` bigint(20) DEFAULT 0 COMMENT '页面浏览量',
  `api_calls` bigint(20) DEFAULT 0 COMMENT 'API调用次数',
  `avg_response_time` decimal(10,2) DEFAULT 0.00 COMMENT '平均响应时间(毫秒)',
  `max_online_users` int(11) DEFAULT 0 COMMENT '最大在线用户数',
  `error_count` bigint(20) DEFAULT 0 COMMENT '错误次数',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_date` (`stat_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统访问统计表';

-- 创建索引优化查询性能
-- 访问日志表的复合索引
CREATE INDEX `idx_access_log_user_time` ON `system_access_log` (`user_id`, `access_time`);
CREATE INDEX `idx_access_log_ip_time` ON `system_access_log` (`request_ip`, `access_time`);
CREATE INDEX `idx_access_log_url_time` ON `system_access_log` (`request_url`(100), `access_time`);

-- 在线用户表的复合索引
CREATE INDEX `idx_online_user_status_time` ON `system_online_user` (`status`, `last_access_time`);
CREATE INDEX `idx_online_user_login_time` ON `system_online_user` (`user_id`, `login_time`);
