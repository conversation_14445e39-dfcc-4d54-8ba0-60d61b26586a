package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeConfig;
import com.haoys.user.model.ProjectTesteeConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeConfigMapper {
    long countByExample(ProjectTesteeConfigExample example);

    int deleteByExample(ProjectTesteeConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeConfig record);

    int insertSelective(ProjectTesteeConfig record);

    List<ProjectTesteeConfig> selectByExample(ProjectTesteeConfigExample example);

    ProjectTesteeConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeConfig record, @Param("example") ProjectTesteeConfigExample example);

    int updateByExample(@Param("record") ProjectTesteeConfig record, @Param("example") ProjectTesteeConfigExample example);

    int updateByPrimaryKeySelective(ProjectTesteeConfig record);

    int updateByPrimaryKey(ProjectTesteeConfig record);
}