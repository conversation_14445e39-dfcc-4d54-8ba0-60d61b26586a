package com.haoys.user.mapper;

import com.haoys.user.model.ProjectQuestionnaireResult;
import com.haoys.user.model.ProjectQuestionnaireResultExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectQuestionnaireResultMapper {
    long countByExample(ProjectQuestionnaireResultExample example);

    int deleteByExample(ProjectQuestionnaireResultExample example);

    int insert(ProjectQuestionnaireResult record);

    int insertSelective(ProjectQuestionnaireResult record);

    List<ProjectQuestionnaireResult> selectByExample(ProjectQuestionnaireResultExample example);

    int updateByExampleSelective(@Param("record") ProjectQuestionnaireResult record, @Param("example") ProjectQuestionnaireResultExample example);

    int updateByExample(@Param("record") ProjectQuestionnaireResult record, @Param("example") ProjectQuestionnaireResultExample example);
}