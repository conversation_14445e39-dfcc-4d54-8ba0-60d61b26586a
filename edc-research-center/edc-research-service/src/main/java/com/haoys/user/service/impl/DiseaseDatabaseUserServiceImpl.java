package com.haoys.user.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.mapper.DiseaseDatabaseAuthMapper;
import com.haoys.user.mapper.DiseaseDatabaseUserMapper;
import com.haoys.user.model.DiseaseDatabaseAuth;
import com.haoys.user.model.DiseaseDatabaseUser;
import com.haoys.user.service.DiseaseDatabaseUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class DiseaseDatabaseUserServiceImpl implements DiseaseDatabaseUserService {

    private final DiseaseDatabaseUserMapper diseaseDatabaseUserMapper;
    private final DiseaseDatabaseAuthMapper diseaseDatabaseAuthMapper;


    @Override
    public DiseaseDatabaseUser getDiseaseDatabaseUser(String dataBaseId, String userId) {
        return diseaseDatabaseUserMapper.getDiseaseDatabaseUser(dataBaseId, userId);
    }

    @Override
    public void saveDiseaseDatabaseUser(String databaseId, String databaseName, Date validateStartDate, Date validateEndDate, String systemUserId) {
        DiseaseDatabaseUser diseaseDatabaseUser = new DiseaseDatabaseUser();
        diseaseDatabaseUser.setId(SnowflakeIdWorker.getUuid());
        diseaseDatabaseUser.setDatabaseId(NumberUtil.parseLong(databaseId));
        diseaseDatabaseUser.setDatabaseName(databaseName);
        diseaseDatabaseUser.setUserId(NumberUtil.parseLong(systemUserId));
        diseaseDatabaseUser.setCreateUserId(SecurityUtils.getUserIdValue());
        diseaseDatabaseUser.setCreateTime(new Date());
        diseaseDatabaseUser.setValidateStartDate(validateStartDate);
        diseaseDatabaseUser.setValidateEndDate(validateEndDate);
        diseaseDatabaseUserMapper.insertSelective(diseaseDatabaseUser);
    }

    @Override
    public void deleteDiseaseDatabaseUser(String id) {
        diseaseDatabaseUserMapper.deleteByPrimaryKey(Long.parseLong(id));
    }

    @Override
    public void deleteDiseaseDatabaseUserByUserId(String systemUserId) {
        diseaseDatabaseUserMapper.deleteDiseaseDatabaseUserByUserId(systemUserId);
    }

    @Override
    public List<DiseaseDatabaseUser> getDiseaseDatabaseListByUserId(String systemUserId) {
        return diseaseDatabaseUserMapper.getDiseaseDatabaseListByUserId(systemUserId);
    }

    @Override
    public DiseaseDatabaseAuth getDiseaseDatabaseAuth(String userId) {
        return diseaseDatabaseUserMapper.getDiseaseDatabaseAuth(userId);
    }

    @Override
    public void saveDiseaseDatabaseAuth(String userId, Boolean ownerTotalAuth) {
        DiseaseDatabaseAuth diseaseDatabaseAuth = diseaseDatabaseUserMapper.getDiseaseDatabaseAuth(userId);
        if (diseaseDatabaseAuth == null) {
            diseaseDatabaseAuth = new DiseaseDatabaseAuth();
            diseaseDatabaseAuth.setId(SnowflakeIdWorker.getUuid());
            diseaseDatabaseAuth.setUserId(NumberUtil.parseLong(userId));
            diseaseDatabaseAuth.setOwnerTotalAuth(ownerTotalAuth);
            diseaseDatabaseAuth.setCreateUserId(SecurityUtils.getUserIdValue());
            diseaseDatabaseAuth.setCreateTime(new Date());
            diseaseDatabaseAuthMapper.insertSelective(diseaseDatabaseAuth);
        }else{
            diseaseDatabaseAuth.setOwnerTotalAuth(ownerTotalAuth);
            diseaseDatabaseAuthMapper.updateByPrimaryKeySelective(diseaseDatabaseAuth);
        }
    }
}
