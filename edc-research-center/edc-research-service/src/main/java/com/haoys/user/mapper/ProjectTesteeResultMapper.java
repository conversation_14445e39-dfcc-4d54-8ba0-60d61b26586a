package com.haoys.user.mapper;

import com.haoys.user.domain.param.testee.QueryTesteeFormParam;
import com.haoys.user.domain.param.testee.QueryTesteeGroupParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormAndTableParam;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeVariableResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormVariableComplateResultVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeResultExportVo;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeResultExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeResultMapper {
    long countByExample(ProjectTesteeResultExample example);

    int deleteByExample(ProjectTesteeResultExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeResult record);

    int insertSelective(ProjectTesteeResult record);

    List<ProjectTesteeResult> selectByExample(ProjectTesteeResultExample example);

    ProjectTesteeResult selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeResult record, @Param("example") ProjectTesteeResultExample example);

    int updateByExample(@Param("record") ProjectTesteeResult record, @Param("example") ProjectTesteeResultExample example);

    int updateByPrimaryKeySelective(ProjectTesteeResult record);

    int updateByPrimaryKey(ProjectTesteeResult record);


    /**
     * 科研数据分析平台
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    List<Map<String,Object>> getTesteeFormResultList(@Param("projectId")String projectId, @Param("visitId")String visitId,
                                                     @Param("testeeId")String testeeId);


    /**
     * 查询数据分析平台样本数据提交结果
     * @param projectId
     * @param planId
     * @param projectOrgId
     * @param variableId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeResultWrapperVo> getProjectTesteeFormAndTableResultList(@Param("projectId") String projectId, @Param("planId") String planId,
                                                                              @Param("projectOrgId") String projectOrgId,
                                                                              @Param("variableId") String variableId, @Param("testeeId") String testeeId);

    List<Map<String, Object>> getResearchTesteeResultList(String projectId, String testeeId);

    ProjectTesteeResult getLastTesteeVisitFollowTime(String projectId, String visitId, String formId, String followVisitViewName, String testeeId);

    ProjectTesteeResult getProjectTesteeGroupResult(String projectId, String visitId, String formId, String groupVariableId, String groupId, String testeeId);

    /**
     * 参与者数据导出-普通表单提交记录
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeResultExportVo> getProjectTesteeFormResultByVariableIds(String projectId, String visitId, String formId, String formDetailId, String testeeId, String orgId);

    /**
     * 导出参与者分页列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgId
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param conditionValue
     * @param conditionTableValue
     * @return
     */
    List<ProjectTesteeExportViewVo> getExportProjectTesteeListForPage(String projectId, String code, String realName, String orgId, String ownerDoctor, String status, String sortField, String sortType, String conditionValue, String conditionTableValue);

    /**
     * 导出患者列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgIds
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param queryTesteeFormParamList
     * @param queryTesteeFormAndTableParamList
     * @param queryTesteeGroupParamList
     * @return
     */
    List<Map<String,Object>> getExportProjectTesteeListForPageVersion2(String projectId, String code, String realName, String orgIds, String ownerDoctor, String status, String sortField, String sortType, List<QueryTesteeFormParam> queryTesteeFormParamList, List<QueryTesteeFormAndTableParam> queryTesteeFormAndTableParamList, List<QueryTesteeGroupParam> queryTesteeGroupParamList);


    /**
     * 参与者表单提交记录
     * @param projectId
     * @param testeeId
     * @param conditionValue
     * @param conditionTableValue
     * @return
     */
    List<ProjectTesteeExportViewVo.FormVariableVo> getExportProjectTesteeVariableValue(String projectId, String testeeId, String conditionValue, String conditionTableValue);


    /**
     * 查询患者表单数据
     * @param projectId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeVariableResultVo> getProjectTesteeVariableResultFromMysqlDataSource(String projectId, String testeeId);

    /**
     * 查询参与者普通表单变量提交记录
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    ProjectTesteeResult getTesteeFormResultValueByFormDetailId(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId);
    /**
     * 根据题组的分组id进行删除
     * @param groupId 分组id
     * @param status 状态
     * @param userId 更新人的id
     * @return 删除结果
     */
    int deleteByGroupId(@Param("groupId") String groupId,@Param("status") String status,@Param("userId") String userId);

    List<String> getSystemDictionaryOptionReference(@Param("dictionaryId") String dictionaryId);
    
    List<String> getProjectDictionaryOptionReference(@Param("projectId") String projectId, @Param("dictionaryId") String dictionaryId);

    ProjectTesteeResultWrapperVo getTesteeFormBaseInfoByFormVariableId(String projectId, String formId, String formDetailId, String testeeId);

    ProjectTesteeFormVariableComplateResultVo getProjectTesteeFormVariableIdComplateCount(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId);

    ProjectTesteeFormVariableComplateResultVo getProjectTesteeTableVariableIdComplateCount(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId);

    List<ProjectTesteeResultVo> getProjectTesteeGroupResultList(@Param("projectId") Long projectId,
                                                                @Param("visitId") Long visitId,
                                                                @Param("formId") Long formId,
                                                                @Param("formDetailId") Long formDetailId,
                                                                @Param("testeeId") Long testeeId);
}