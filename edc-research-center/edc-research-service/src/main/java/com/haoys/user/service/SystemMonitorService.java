package com.haoys.user.service;

import com.haoys.user.common.api.PageResult;
import com.haoys.user.domain.param.monitor.SystemMonitorParam;
import com.haoys.user.domain.vo.monitor.SystemAccessLogVo;
import com.haoys.user.domain.vo.monitor.SystemAccessStatisticsVo;
import com.haoys.user.domain.vo.monitor.SystemOnlineUserVo;
import com.haoys.user.domain.vo.system.SystemExceptionLogVo;

import java.util.List;
import java.util.Map;

/**
 * 系统监控服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface SystemMonitorService {

    /**
     * 记录用户访问日志
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param realName 真实姓名
     * @param sessionId 会话ID
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestIp 请求IP
     * @param userAgent 用户代理
     * @param responseTime 响应时间
     * @param responseStatus 响应状态
     */
    void recordAccessLog(String userId, String userName, String realName, String sessionId,
                        String requestUrl, String requestMethod, String requestIp,
                        String userAgent, Long responseTime, Integer responseStatus);

    /**
     * 记录登录日志
     *
     * @param userId 用户ID
     * @param userName 用户名
     * @param realName 真实姓名
     * @param sessionId 会话ID
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestIp 请求IP
     * @param userAgent 用户代理
     * @param responseTime 响应时间
     * @param responseStatus 响应状态
     * @param loginType 登录类型
     * @param phoneNumber 手机号
     * @param loginSuccess 登录是否成功
     * @param failureReason 失败原因
     */
    void recordLoginLog(String userId, String userName, String realName, String sessionId,
                       String requestUrl, String requestMethod, String requestIp,
                       String userAgent, Long responseTime, Integer responseStatus,
                       String loginType, String phoneNumber, Boolean loginSuccess, String failureReason);

    /**
     * 更新在线用户信息
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param realName 真实姓名
     * @param sessionId 会话ID
     * @param loginIp 登录IP
     * @param userAgent 用户代理
     * @param token 访问令牌
     */
    void updateOnlineUser(String userId, String userName, String realName, String sessionId,
                         String loginIp, String userAgent, String token);

    /**
     * 记录用户登录并更新在线用户信息
     *
     * @param userId 用户ID
     * @param userName 用户名
     * @param realName 真实姓名
     * @param sessionId 会话ID
     * @param loginIp 登录IP
     * @param userAgent 用户代理
     * @param token 访问令牌
     * @param loginType 登录类型
     * @param phoneNumber 手机号
     */
    void recordUserLogin(String userId, String userName, String realName, String sessionId,
                        String loginIp, String userAgent, String token, String loginType, String phoneNumber);

    /**
     * 移除离线用户
     * 
     * @param sessionId 会话ID
     */
    void removeOfflineUser(String sessionId);

    /**
     * 清理过期的在线用户
     * 
     * @return 清理数量
     */
    int cleanExpiredOnlineUsers();

    /**
     * 分页查询访问日志
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<SystemAccessLogVo> getAccessLogList(SystemMonitorParam.AccessLogQueryParam param);

    /**
     * 分页查询在线用户
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<SystemOnlineUserVo> getOnlineUserList(SystemMonitorParam.OnlineUserQueryParam param);

    /**
     * 分页查询异常日志
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<SystemExceptionLogVo> getExceptionLogList(SystemMonitorParam.ExceptionLogQueryParam param);

    /**
     * 获取访问统计概览
     * 
     * @return 统计概览
     */
    SystemAccessStatisticsVo.StatisticsOverview getStatisticsOverview();

    /**
     * 分页查询访问统计
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    PageResult<SystemAccessStatisticsVo> getAccessStatisticsList(SystemMonitorParam.StatisticsQueryParam param);

    /**
     * 生成每日统计数据
     * 
     * @param statDate 统计日期(格式: yyyy-MM-dd)
     * @return 是否成功
     */
    boolean generateDailyStatistics(String statDate);

    /**
     * 获取实时统计数据
     * 
     * @return 实时统计数据
     */
    Map<String, Object> getRealTimeStatistics();

    /**
     * 获取访问趋势数据
     * 
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getAccessTrend(int days);

    /**
     * 获取热门访问页面
     * 
     * @param limit 限制数量
     * @return 热门页面列表
     */
    List<Map<String, Object>> getPopularPages(int limit);

    /**
     * 获取访问地域分布
     * 
     * @param limit 限制数量
     * @return 地域分布数据
     */
    List<Map<String, Object>> getLocationDistribution(int limit);

    /**
     * 强制下线用户
     * 
     * @param sessionId 会话ID
     * @return 是否成功
     */
    boolean forceOfflineUser(String sessionId);

    /**
     * 批量强制下线用户
     *
     * @param sessionIds 会话ID列表
     * @return 成功下线的数量
     */
    int batchForceOfflineUsers(List<String> sessionIds);

    /**
     * 获取登录统计信息
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 登录统计信息
     */
    Map<String, Object> getLoginStatistics(String startDate, String endDate);

    /**
     * 获取按登录类型分组的统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 登录类型统计
     */
    List<Map<String, Object>> getLoginTypeStatistics(String startDate, String endDate);

    /**
     * 获取今日登录统计
     *
     * @return 今日登录统计
     */
    Map<String, Object> getTodayLoginStatistics();

    /**
     * 获取用户登录历史
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 登录历史
     */
    List<Map<String, Object>> getUserLoginHistory(String userId, int limit);

    /**
     * 获取访问日志详情
     *
     * @param id 日志ID
     * @return 访问日志详情
     */
    SystemAccessLogVo getAccessLogDetail(String id);

    /**
     * 获取在线用户详情
     *
     * @param sessionId 会话ID
     * @return 在线用户详情
     */
    SystemOnlineUserVo getOnlineUserDetail(String sessionId);

    /**
     * 获取异常日志详情
     *
     * @param id 日志ID
     * @return 异常日志详情
     */
    SystemExceptionLogVo getExceptionLogDetail(String id);

    /**
     * 获取在线用户统计信息
     *
     * @return 在线用户统计
     */
    Map<String, Object> getOnlineUserStatistics();

    /**
     * 获取异常日志统计信息
     *
     * @return 异常日志统计
     */
    Map<String, Object> getExceptionLogStatistics();
}
