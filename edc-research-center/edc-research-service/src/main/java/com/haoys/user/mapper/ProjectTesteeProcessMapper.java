package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeProcess;
import com.haoys.user.model.ProjectTesteeProcessExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectTesteeProcessMapper {
    long countByExample(ProjectTesteeProcessExample example);

    int deleteByExample(ProjectTesteeProcessExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeProcess record);

    int insertSelective(ProjectTesteeProcess record);

    List<ProjectTesteeProcess> selectByExample(ProjectTesteeProcessExample example);

    ProjectTesteeProcess selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeProcess record, @Param("example") ProjectTesteeProcessExample example);

    int updateByExample(@Param("record") ProjectTesteeProcess record, @Param("example") ProjectTesteeProcessExample example);

    int updateByPrimaryKeySelective(ProjectTesteeProcess record);

    int updateByPrimaryKey(ProjectTesteeProcess record);
    
    /**
     * 查询项目表单录入状态
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param testeeId
     * @return
     */
    ProjectTesteeProcess getProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId);
}