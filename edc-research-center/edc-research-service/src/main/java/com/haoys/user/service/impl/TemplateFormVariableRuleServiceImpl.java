package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.mapper.TemplateFormVariableFunMapper;
import com.haoys.user.mapper.TemplateFormVariableRuleMapper;
import com.haoys.user.model.TemplateFormVariableFun;
import com.haoys.user.model.TemplateFormVariableFunExample;
import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.model.TemplateFormVariableRuleExample;
import com.haoys.user.service.TemplateFormVariableRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 配置表单-公式计算service
 */

@Service
public class TemplateFormVariableRuleServiceImpl implements TemplateFormVariableRuleService {

    @Autowired
    private TemplateFormVariableRuleMapper templateFormVariableRuleMapper;

    @Autowired
    private TemplateFormVariableFunMapper templateFormVariableFunMapper;
    /**
     * 获取公式配置信息
     * @param projectId 项目id
     * @param formId  表单id
     * @param variableId 变量id
     * @param groupId 组id
     * @return
     */
    @Override
    public TemplateFormVariableRule getFormVariableRule(String projectId,String groupId, String formId, String variableId) {
        TemplateFormVariableRuleExample example = new TemplateFormVariableRuleExample();
        TemplateFormVariableRuleExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(projectId)){
            criteria.andProjectIdEqualTo(projectId);
        }
        criteria.andFormIdEqualTo(formId);
        criteria.andVariableIdEqualTo(variableId);
        if(StringUtils.isNotBlank(groupId)){
            criteria.andGroupIdEqualTo(Long.parseLong(groupId));
        }else {
            criteria.andGroupIdIsNull();
        }
        List<TemplateFormVariableRule> templateFormVariableRules = templateFormVariableRuleMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtil.isNotEmpty(templateFormVariableRules)){
            return templateFormVariableRules.get(0);
        }
        return new TemplateFormVariableRule();
    }

    /**
     * 获取公式配置信息
     * @param projectId 项目id
     * @param formId  表单id
     * @param variableId 变量id
     * @param groupId 组id
     * @return
     */
    @Override
    public List<TemplateFormVariableRule> getFormVariableRuleList(String projectId,String groupId, String formId) {
        TemplateFormVariableRuleExample example = new TemplateFormVariableRuleExample();
        TemplateFormVariableRuleExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(projectId)){
            criteria.andProjectIdEqualTo(projectId);
        }
        criteria.andFormIdEqualTo(formId);
        if(StringUtils.isNotBlank(groupId)){
            criteria.andGroupIdEqualTo(Long.parseLong(groupId));
        }else {
            criteria.andGroupIdIsNull();
        }
        return templateFormVariableRuleMapper.selectByExampleWithBLOBs(example);
    }


    /**
     * 保存或者更新公式
     * @param rule 公式信息
     * @return
     */
    @Override
    public CommonResult<TemplateFormVariableRule> saveFormVariableRule(TemplateFormVariableRule rule) {

        int i=0;

        if (rule.getId()!=null){
            // 更新
            TemplateFormVariableRule variableRule = templateFormVariableRuleMapper.selectByPrimaryKey(rule.getId());
            if (variableRule!=null){
                variableRule.setRuleDesc(rule.getRuleDesc());
                variableRule.setRuleDetails(rule.getRuleDetails());
                variableRule.setFormulaDesc(rule.getFormulaDesc());
                variableRule.setTriggerTiming(rule.getTriggerTiming());
                variableRule.setRuleContent(rule.getRuleContent());
                variableRule.setUpdateTime(new Date());
                variableRule.setGroupId(rule.getGroupId());
                variableRule.setFromDetailId(rule.getFromDetailId());
                i= templateFormVariableRuleMapper.updateByPrimaryKeyWithBLOBs(variableRule);
            }
        }else {
            rule.setId(SnowflakeIdWorker.getUuid());
            rule.setCreateTime(new Date());
            rule.setUpdateTime(new Date());
            rule.setCreateUserId(SecurityUtils.getUserId().toString());
            rule.setUpdateUserId(SecurityUtils.getUserId().toString());
            rule.setTenantId(SecurityUtils.getSystemTenantId());
            rule.setRuleContent(rule.getRuleContent());
            rule.setPlatformId(SecurityUtils.getSystemPlatformId());
            rule.setStatus(BusinessConfig.VALID_STATUS);
            rule.setGroupId(rule.getGroupId());
            rule.setFromDetailId(rule.getFromDetailId());
            i= templateFormVariableRuleMapper.insert(rule);
        }
        return i>0?CommonResult.success(null):CommonResult.failed();
    }

    @Override
    public Map<Integer,List<TemplateFormVariableFun>> getFormVariableFun() {
        TemplateFormVariableFunExample example = new TemplateFormVariableFunExample();
        TemplateFormVariableFunExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(Integer.parseInt(BusinessConfig.VALID_STATUS));
        example.setOrderByClause("fun_type asc,id asc ");
        List<TemplateFormVariableFun> list = templateFormVariableFunMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(list)){
            LinkedHashMap<Integer, List<TemplateFormVariableFun>> funMap =
                    list.stream().collect(Collectors.groupingBy(TemplateFormVariableFun::getFunType, LinkedHashMap::new, Collectors.toList()));
            return funMap;
        }
        return new HashMap<>();
    }
}
