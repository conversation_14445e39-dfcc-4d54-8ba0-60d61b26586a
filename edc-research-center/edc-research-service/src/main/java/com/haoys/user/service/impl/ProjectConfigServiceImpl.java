package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.domain.vo.project.ProjectConfigVo;
import com.haoys.user.mapper.ProjectConfigBaseMapper;
import com.haoys.user.mapper.ProjectConfigValueMapper;
import com.haoys.user.model.ProjectConfigBase;
import com.haoys.user.model.ProjectConfigValue;
import com.haoys.user.model.ProjectConfigValueExample;
import com.haoys.user.service.ProjectConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ProjectConfigServiceImpl extends BaseService implements ProjectConfigService {

    @Autowired
    private ProjectConfigValueMapper projectConfigValueMapper;

    @Autowired
    private ProjectConfigBaseMapper projectConfigBaseMapper;

    @Override
    public CommonResult<Object> saveProjectConfigValue(List<ProjectConfigValue> projectConfigValues) {
        if (CollectionUtil.isNotEmpty(projectConfigValues)){
            // 根据配置的id和项目的id清楚数据
            List<Long> configIds = new ArrayList<>();
            projectConfigValues.forEach(cfg-> configIds.add(cfg.getConfigId()));
            ProjectConfigValueExample example = new ProjectConfigValueExample();
            ProjectConfigValueExample.Criteria criteria = example.createCriteria();
            criteria.andConfigIdIn(configIds);
            criteria.andProjectIdEqualTo(projectConfigValues.get(0).getProjectId());
            projectConfigValueMapper.deleteByExample(example);
            // 保存配置项的值
            String userId = SecurityUtils.getUserId().toString();
            String tenantId = SecurityUtils.getSystemTenantId();
            String platformId = SecurityUtils.getSystemPlatformId();
            projectConfigValues.forEach(cfg-> {
                cfg.setCreateTime(new Date());
                cfg.setUpdateTime(new Date());
                cfg.setCreateUserId(userId);
                cfg.setTenantId(tenantId);
                cfg.setPlatformId(platformId);
                projectConfigValueMapper.insert(cfg);
            });
            return CommonResult.success("");
        }
        return CommonResult.failed();
    }
    /**
     * 获取项目配置
     * @param projectId 项目id
     * @param moduleName 模块名称（非必传）
     * @return 项目配置
     */
    @Override
    public CommonResult<List<ProjectConfigVo>> getProjectConfigByProjectId(String projectId, String moduleName) {
        List<ProjectConfigVo> list = projectConfigValueMapper.getConfigList(projectId,moduleName,null);
        return CommonResult.success(list);
    }

    /**
     * 获取配置
     * @param projectId 项目id
     * @param configCode 配置code（非必传）
     * @return 配置
     */
    @Override
    public ProjectConfigVo getProjectBaseConfig(String projectId, String configCode) {
        List<ProjectConfigVo> list = projectConfigValueMapper.getConfigList(projectId,null,configCode);
        return CollectionUtil.isNotEmpty(list)?(list.get(0)):null;
    }
    /**
     * 保存配置项
     * @param configBase
     * @return
     */
    @Override
    public CommonResult<Object> saveProjectConfig(ProjectConfigBase configBase) {
        int insert = projectConfigBaseMapper.insert(configBase);
        return insert>0?CommonResult.success(insert):CommonResult.failed();
    }

}
