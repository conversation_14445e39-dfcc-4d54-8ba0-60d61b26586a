package com.haoys.user.mapper;

import com.haoys.user.domain.entity.ProjectUserRoleQuery;
import com.haoys.user.domain.vo.auth.ProjectUserRoleVo;
import com.haoys.user.model.ProjectUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目用户和角色数据层
 */
public interface ProjectUserRoleMapper {

    /**
     * 批量新增项目用户角色信息
     *
     * @param projectUserRoleQuery 项目用户角色列表
     * @return 结果
     */
    int batchProjectUserRole(List<ProjectUserRoleQuery> projectUserRoleQuery);

    /**
     * 根据项目id和用户id删除项目用户角色信息
     * @param projectId 项目id
     * @param userId    用户id
     * @return
     */
    int deleteProjectUserRoleByProjectIdAndUserId(@Param("projectId") String projectId, @Param("userId") String userId);

    /**
     * 查询项目管理员角色信息
     * @param projectId
     * @param systemUserId
     * @param roleId
     * @return
     */
    ProjectUserRoleVo getProjectRoleListByProjectIdAndUserId(String projectId, String systemUserId, String roleId);

    int insert(ProjectUserRole record);
}
