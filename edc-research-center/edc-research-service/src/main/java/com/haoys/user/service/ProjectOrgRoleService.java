package com.haoys.user.service;


import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.dto.ProjectUserOrgRoleVo;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.vo.auth.ProjectOrgUserRoleVo;
import com.haoys.user.domain.vo.project.ProjectOrgRoleVo;
import com.haoys.user.model.ProjectOrgRole;

import java.util.List;

/**
 * 所属中心角色
 */
public interface ProjectOrgRoleService {


    /**
     * 根据条件分页查询项目角色数据
     * @param projectRoleQuery 所属中心角色信息的搜索条件
     * @return 所属中心角色信息
     */
    List<ProjectRoleQuery> selectProjectOrgRoleList(ProjectRoleQuery projectRoleQuery);

    /**
     * 根据项目id和code查询研究中心角色信息
     * @param projectId
     * @param projectOrgId
     * @param projectRoleName
     * @return
     */
    ProjectOrgRole getProjectOrgRoleByProjectOrgIdAndRoleName(String projectId, String projectOrgId, String projectRoleName);

    /**
     * 校验名称或者英文名称是否已经存在
     * @param projectRoleQuery
     * @return
     */
    Boolean checkProjectRoleNameUnique(ProjectRoleQuery projectRoleQuery);


    /**
     * 所属中心创建初始化中心角色信息。
     * @param projectId         项目id
     * @param systemOrgId       系统中心的id
     * @param projectOrgId      项目研究中心id
     * @param projectOrgCode    项目研究中心code
     * @param projectRoleName
     * @param createUserId
     */
    CustomResult<String> initProjectOrgTemplateRole(String projectId, String systemOrgId, String projectOrgId, String projectOrgCode, String projectRoleName, String createUserId);

    /**
     *  新增
     * @param projectRoleQuery
     * @return
     */
    CommonResult insertProjectOrgRole(ProjectRoleQuery projectRoleQuery);
    /**
     *  更新
     * @param projectRoleQuery
     * @return
     */
    CommonResult updateProjectOrgRole(ProjectRoleQuery projectRoleQuery);

    /**
     * 删除
     * @param projectRoleQuery  参数
     * @return
     */
    CommonResult deleteProjectOrgRoleById(ProjectRoleQuery projectRoleQuery);

    /**
     * 根据roleId查询研究中心角色
     * @param projectOrgRoleId
     * @return
     */
    ProjectRoleQuery getProjectOrgRoleAndMenuIdsByRoleId(Long projectOrgRoleId);


    /**
     * 启用或停用
     * @param projectRoleQuery
     * @return
     */
    CommonResult updateProjectOrgRoleStatus(ProjectRoleQuery projectRoleQuery);

    /**
     * 保存项目研究中心用户和角色关联关系
     * @param projectId
     * @param userId
     * @param projectOrgRoleId
     * @param ownerTotalAuth
     */
    void saveProjectUserOrgRole(String projectId, String userId, String projectOrgRoleId, Boolean ownerTotalAuth);

    /**
     * 删除项目研究中心用户关联角色
     * @param projectId
     * @param userId
     */
    void deleteProjectOrgUserRoleByUserId(String projectId, String userId);

    /**
     * 根据项目id和用户id获取
     * @param projectId
     * @param userId
     * @return
     */
    List<ProjectUserOrgRoleVo> getProjectOrgUserRoleListByProjectIdAndUserId(String projectId, String userId);

    /**
     * 根据projectOrgCode查询项目研究中心角色列表
     * @param projectId
     * @param orgId
     * @param projectOrgCode
     * @return
     */
    List<ProjectOrgRole> getProjectOrgRoleByProjectOrgCode(Long projectId, Long orgId, String projectOrgCode);

    /**
     * 查询授权全部研究中心的项目用户
     * @param projectId
     * @return
     */
    List<ProjectOrgUserRoleVo> getProjectUserOrgRoleByOwnerTotalAuth(Long projectId);

    /**
     * 获取角色列表
     * @param projectId
     * @param projectOrgId
     * @param status
     * @param id
     * @param ename
     * @param pageSize
     * @param pageNum
     * @return
     */
    List<ProjectOrgRoleVo> getProjectOrgRoleListForPage(Long projectId, Long projectOrgId, Integer status, Long id, String ename, Integer pageSize, Integer pageNum);

    /**
     * 获取角色下拉列表
     * @param projectId
     * @param projectOrgId
     * @return
     */
    List<ProjectOrgRoleVo> getRoseList(Long projectId, Long projectOrgId);

    /**
     * 新增/复制角色
     * @param role
     * @param menuIds
     * @return
     */
    CommonResult saveProjectOrgRole(ProjectOrgRole role, String menuIds);

    /**
     * 编辑/复制获取回显数据
     * @param id
     * @return
     */
    ProjectOrgRole getProjectOrgRoleBaseInfo(Long id);

    /**
     * 编辑/复制
     * @param id
     * @param role
     * @return
     */
    int updateProjectOrgRole(Long id, ProjectOrgRole role);

    /**
     * 删除角色
     * @param ids
     * @return
     */
    int deleteProjectOrgRole(List<Long> ids);
    
    ProjectOrgRole getProjectOrgRoleByOrgRoleCode(String projectId, String projectOrgId, String code, String loginTenantId, String loginPlatformId);
}
