package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewConfigVo;
import com.haoys.user.model.TemplateVariableViewConfig;
import com.haoys.user.model.TemplateVariableViewConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateVariableViewConfigMapper {
    long countByExample(TemplateVariableViewConfigExample example);

    int deleteByExample(TemplateVariableViewConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateVariableViewConfig record);

    int insertSelective(TemplateVariableViewConfig record);

    List<TemplateVariableViewConfig> selectByExample(TemplateVariableViewConfigExample example);

    TemplateVariableViewConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateVariableViewConfig record, @Param("example") TemplateVariableViewConfigExample example);

    int updateByExample(@Param("record") TemplateVariableViewConfig record, @Param("example") TemplateVariableViewConfigExample example);

    int updateByPrimaryKeySelective(TemplateVariableViewConfig record);

    int updateByPrimaryKey(TemplateVariableViewConfig record);

    /**
     * 根据optionId查询表单联动控制变量规则列表
     * @param projectId
     * @param formId
     * @param optionValueId
     * @return
     */
    List<TemplateFormVariableViewConfigVo> getTemplateFormVariableListByOptionId(String projectId, String formId, String optionValueId);

    void deleteTemplateVariableViewResultConfig(Long variableOptionValueId);

    List<TemplateVariableViewConfig> getTemplateVariableViewConfigListByDetailId(Long projectId, Long formId, Long formDetailId);
    
    List<TemplateFormVariableViewConfigVo> getTemplateFormVariableListByOptionIds(String projectId, String formId, String optionValueIds);
}