package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.constants.EmailMsgConstants;
import com.haoys.user.common.core.domain.entity.BaseSystemUser;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SendMessageUtil;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.enums.SystemUserReturnEnums;
import com.haoys.user.domain.param.auth.ProjectUserAuthParam;
import com.haoys.user.domain.param.system.SystemUserAuthParam;
import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.domain.vo.auth.ProjectUserRoleVo;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.vo.system.SystemDepartmentVo;
import com.haoys.user.domain.vo.system.SystemUserAuthVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.enums.ProjectRoleEnum;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.mapper.ProjectUserInfoMapper;
import com.haoys.user.mapper.ProjectUserRoleMapper;
import com.haoys.user.model.DiseaseDatabaseAuth;
import com.haoys.user.model.DiseaseDatabaseUser;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectOrgRole;
import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectRole;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.model.ProjectUserRole;
import com.haoys.user.model.SystemDepartment;
import com.haoys.user.model.SystemDepartmentExample;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.model.SystemOrgInfoExample;
import com.haoys.user.model.SystemRole;
import com.haoys.user.model.SystemTenant;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.SystemUserRole;
import com.haoys.user.service.CreateTokenService;
import com.haoys.user.service.DiseaseDatabaseUserService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectOrgRoleService;
import com.haoys.user.service.ProjectResearchersInfoService;
import com.haoys.user.service.ProjectRoleService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemMenuService;
import com.haoys.user.service.SystemOrgInfoService;
import com.haoys.user.service.SystemRoleService;
import com.haoys.user.service.SystemTenantService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TenantDepartmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class TenantDepartmentServiceImpl extends BaseService implements TenantDepartmentService {

    private final PasswordEncoder passwordEncoder;
    private final SendMessageUtil sendMessageUtil;
    private final SystemTenantService systemTenantService;
    private final SystemDepartmentService systemDepartmentService;
    private final SystemOrgInfoService systemOrgInfoService;
    private final SystemUserInfoService systemUserInfoService;
    private final SystemRoleService systemRoleService;
    private final SystemMenuService systemMenuService;
    private final ProjectBaseManageService projectBaseManageService;
    private final ProjectUserInfoMapper projectUserInfoMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final CreateTokenService createTokenService;
    private final ProjectRoleService projectRoleService;
    private final ProjectOrgRoleService projectOrgRoleService;
    private final OrganizationService organizationService;
    private final ProjectUserService projectUserService;
    private final SystemTenantUserService systemTenantUserService;
    private final DiseaseDatabaseUserService diseaseDatabaseUserService;
    private final ProjectResearchersInfoService projectResearchersInfoService;


    @Value("${user.act-page}")
    private String actPage;
    @Value("${form.audit}")
    private Boolean formAudit;


    @Override
    public Map<String, Object> getDepartmentList(String id) {
        Map<String, Object> dataMap = new HashMap<>();
        SystemDepartmentExample example = new SystemDepartmentExample();
        SystemDepartmentExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(id);
        criteria.andStatusEqualTo("0");
        List<SystemDepartment> systemDepartmentList = systemDepartmentService.selectByExample(example);
        List<SystemDepartmentVo> systemDepartmentVoList = new ArrayList<SystemDepartmentVo>();
        if (systemDepartmentList.size() > 0) {
            for (SystemDepartment sd : systemDepartmentList) {
                int userCount = systemUserInfoService.selectCountUser(sd.getId(), SecurityUtils.getSystemPlatformId(), SecurityUtils.getSystemTenantId());
                sd.setCountUser(userCount);
                SystemDepartmentVo systemDepartmentVo = new SystemDepartmentVo();
                BeanUtils.copyProperties(sd, systemDepartmentVo);
                systemDepartmentVoList.add(systemDepartmentVo);

            }
        }
        //新增显示外部部门
        SystemDepartmentVo vo = new SystemDepartmentVo();
        vo.setName("外部用户");
        vo.setId(-1L);
        int userCount = systemUserInfoService.selectCountOutUser(SecurityUtils.getSystemTenantId(), SecurityUtils.getSystemPlatformId(), Constants.USER_TYPE_VALUE_04);
        vo.setCountUser(userCount);
        systemDepartmentVoList.add(vo);

        dataMap.put("systemDepartmentList", systemDepartmentVoList);
        dataMap.put("systemTenant", systemTenantService.selectByPrimaryKey(Long.valueOf(id)));

        return dataMap;
    }

    @Override
    public int addDepartment(String name, String personCharge, String description) {
        //校验部门名称是否重复
        SystemDepartmentExample exampleSd = new SystemDepartmentExample();
        SystemDepartmentExample.Criteria criteriaSd = exampleSd.createCriteria();
        criteriaSd.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteriaSd.andPlatformIdEqualTo(SecurityUtils.getSystemPlatformId());
        criteriaSd.andNameEqualTo(name);
        List<SystemDepartment> systemDepartmentList = systemDepartmentService.selectByExample(exampleSd);
        if (systemDepartmentList.size() > 0) {
            return -1;
        }

        //获取中心
        SystemOrgInfoExample example = new SystemOrgInfoExample();
        SystemOrgInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteria.andPlatformIdEqualTo(SecurityUtils.getSystemPlatformId());
        criteria.andIsAuthEqualTo(BusinessConfig.SYSTEM_ORG_INFO_IS_AUTH);
        List<SystemOrgInfo> systemOrgInfoList = systemOrgInfoService.selectByExample(example);

        SystemDepartment systemDepartmentVo = new SystemDepartment();
        systemDepartmentVo.setName(name);
        systemDepartmentVo.setPersonCharge(personCharge);
        systemDepartmentVo.setDescription(description);
        systemDepartmentVo.setTenantId(SecurityUtils.getSystemTenantId());
        systemDepartmentVo.setPlatformId(SecurityUtils.getSystemPlatformId());
        systemDepartmentVo.setCreateUser(SecurityUtils.getUserId().toString());
        systemDepartmentVo.setCreateTime(new Date());
        if (!systemOrgInfoList.isEmpty()) {
            systemDepartmentVo.setOrgId(systemOrgInfoList.get(0).getOrgId());
            systemDepartmentVo.setOrgName(systemOrgInfoList.get(0).getOrgName());
        }
        systemDepartmentVo.setId(SnowflakeIdWorker.getUuid());
        systemDepartmentVo.setStatus(String.valueOf(BusinessConfig.ENABLED_STATUS));
        return systemDepartmentService.insert(systemDepartmentVo);
    }

    @Override
    public int editDepartment(Long id, String name, String personCharge, String description) {
        SystemDepartment systemDepartment = systemDepartmentService.selectByPrimaryKey(id);
        systemDepartment.setName(name);
        systemDepartment.setPersonCharge(personCharge);
        systemDepartment.setDescription(description);
        systemDepartment.setUpdateUser(SecurityUtils.getUserId().toString());
        systemDepartment.setUpdateTime(new Date());
        return systemDepartmentService.updateByPrimaryKey(systemDepartment);
    }

    @Override
    public String deleteDepartment(Long id) {
        List<SystemUserInfo> tenantUserList = systemTenantService.selectTenantUserByEnterprise(id);
        if (CollectionUtil.isNotEmpty(tenantUserList)) {
            return "该部门下存在用户，不能删除";
        }
        SystemDepartment systemDepartment = systemDepartmentService.selectByPrimaryKey(id);
        systemDepartment.setStatus(BusinessConfig.NO_VALID_STATUS);
        systemDepartmentService.updateByPrimaryKey(systemDepartment);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public CommonPage<SystemUserInfoVo> getTenantUserListForPage(String enterpriseId, String realName, String username, Integer status,
                                                                 Boolean activeStatus, Boolean lockStatus, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);

        SystemUserInfoParam systemUserInfoParam = new SystemUserInfoParam();
        if (realName != null && !realName.isEmpty()) {
            systemUserInfoParam.setRealName(realName);
        }
        if (activeStatus != null) {
            systemUserInfoParam.setActiveStatus(activeStatus);
        }
        if (lockStatus != null) {
            if (lockStatus) {
                systemUserInfoParam.setLockStatus(lockStatus);
            }
        }
        if (lockStatus == null && status != null) {
            systemUserInfoParam.setStatus(status);
        }
        systemUserInfoParam.setRegisterFrom(Constants.USER_TYPE_VALUE_04);
        systemUserInfoParam.setSealFlag(false);
        systemUserInfoParam.setEnterprise(enterpriseId);
        systemUserInfoParam.setTenantId(SecurityUtils.getSystemTenantId());
        systemUserInfoParam.setPlatformId(SecurityUtils.getSystemPlatformId());
        List<SystemUserInfo> systemUserInfoList = systemUserInfoService.getSystemUserListForPage(systemUserInfoParam);
        List<SystemUserInfoVo> systemUserInfoVoList = new ArrayList<>();
        for (SystemUserInfo systemUserInfo : systemUserInfoList) {
            SystemUserInfoVo systemUserInfoVo = new SystemUserInfoVo();
            BeanUtils.copyProperties(systemUserInfo, systemUserInfoVo);
            List<SystemRole> systemRoleList = systemRoleService.selectRoleByUserId(systemUserInfo.getId());
            Map<String, List<SystemRole>> collect = systemRoleList.stream().collect(Collectors.groupingBy(SystemRole::getName));
            systemRoleList.stream().collect(Collectors.groupingBy(SystemRole::getName));
            collect.forEach((k, v) -> {
                String systemRoleName = v.stream().map(SystemRole::getName).collect(Collectors.joining(","));
                systemUserInfoVo.setRoleName(systemRoleName);
            });
            if (StringUtils.isNotEmpty(systemUserInfoVo.getMobile())) {
                systemUserInfoVo.setMobile(DesensitizeUtil.aesDecrypt(systemUserInfoVo.getMobile()));
                //systemUserInfoVo.setMobile(DesensitizedUtil.mobilePhone(DesensitizeUtil.aesDecrypt(systemUserInfoVo.getMobile())));
            }
            // 查询研究者信息
            if(formAudit){
                ProjectResearchersInfo researchersInfo = projectResearchersInfoService.getProjectResearcherInfo(systemUserInfo.getId().toString());
                if (researchersInfo != null) {
                    // modify user groupInfo
                    if(StringUtils.isEmpty(systemUserInfo.getRoleDesc()) || !researchersInfo.getGroupInfo().equals(systemUserInfo.getRoleDesc())){
                        systemUserInfo.setRoleDesc(researchersInfo.getGroupInfo());
                        AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.updateSystemUserValue(systemUserInfo), 500);
                        //systemUserInfoService.updateSystemUser(systemUserInfo);
                    }
                }
            }
            systemUserInfoVoList.add(systemUserInfoVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, systemUserInfoVoList);
    }

    @Override
    public int editDepartmentUserStatus(Long id) {
        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(id.toString());
        if (systemTenantUser != null) {
            if ("0".equals(systemTenantUser.getStatus())) {
                systemTenantUser.setStatus(BusinessConfig.NO_VALID_STATUS);
            } else {
                systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
            }
            return systemTenantUserService.updateSystemTenantUser(systemTenantUser);

        }
        return 0;
    }

    @Override
    public CustomResult saveSystemUserAndProjectUser(SystemUserAuthParam systemUserAuthParam) {
        CustomResult customResult = new CustomResult();
        boolean defaultActiveStatus = true;
        String systemUserId = systemUserAuthParam.getSystemUserId();
        String platformId = SecurityUtils.getSystemPlatformId();
        String tenantId = SecurityUtils.getSystemTenantId();
        SystemRole systemRole = systemRoleService.selectByPrimaryKey(Long.valueOf(systemUserAuthParam.getRoleId()));
        boolean companyOwnerUser = Constants.SYSTEM_MANAGE_ROLE.equals(systemRole.getEnglishName());
        if (StringUtils.isEmpty(systemUserId)) {
            List<String> registerTypeList = Arrays.asList(Constants.USER_TYPE_VALUE_02, Constants.USER_TYPE_VALUE_10);
            String accountName = registerTypeList.contains(systemUserAuthParam.getRegisterType()) ? systemUserAuthParam.getEmail() : systemUserAuthParam.getMobile();
            SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfoByUserName(accountName);
            if (systemUserInfo == null) {
                //写入用户信息
                SystemUserInfoParam systemUserInfoParam = new SystemUserInfoParam();
                if (Constants.USER_TYPE_VALUE_01.equals(systemUserAuthParam.getRegisterType())) {
                    systemUserInfoParam.setMobile(accountName);
                    Pattern p = Pattern.compile("^1[3-9]\\d{9}$");
                    Matcher m = p.matcher(accountName);
                    if (!m.matches()) {
                        customResult.setMessage(SystemUserReturnEnums.E20004.getMessage());
                        return customResult;
                    }
                }
                if (Constants.USER_TYPE_VALUE_02.equals(systemUserAuthParam.getRegisterType())) {
                    systemUserInfoParam.setEmail(systemUserAuthParam.getEmail());
                    if (StringUtils.isNotBlank(accountName) && !accountName.contains("@")) {
                        customResult.setMessage(SystemUserReturnEnums.E20013.getMessage());
                        return customResult;
                    }
                }
                systemUserInfoParam.setUsername(accountName);
                if (systemUserAuthParam.getRealName() != null) {
                    systemUserInfoParam.setRealName(systemUserAuthParam.getRealName());
                }
                systemUserInfoParam.setCreateUserId(systemUserAuthParam.getCreateUserId());
                systemUserInfoParam.setUserType(systemUserAuthParam.getRegisterType());
                systemUserInfoParam.setRegisterFrom(Constants.USER_TYPE_VALUE_06);
                // 如果默认密码不为空这设置为密码
                if(systemUserAuthParam.getStatus().equals(BusinessConfig.TEMP_STATUS)){
                    systemUserInfoParam.setDefaultPwd(systemUserAuthParam.getDefaultPwd());
                }
                systemUserInfoParam.setSealFlag(false);

                customResult = systemUserInfoService.saveSystemUserInfo(systemUserInfoParam);

                systemUserId = customResult.getData().toString();

                //用户绑定角色
                SystemUserRole systemUserRole = new SystemUserRole();
                systemUserRole.setUserId(Long.valueOf(systemUserId));
                systemUserRole.setRoleId(Long.valueOf(systemUserAuthParam.getRoleId()));
                systemUserRole.setTenantId(tenantId);
                systemUserRole.setPlatformId(platformId);
                systemRoleService.insertSystemUserRole(systemUserRole);

                if (ResultCode.SUCCESS.getCode() == customResult.getCode()) {
                    long userId = Long.parseLong(systemUserId);
                    systemUserInfoParam.setId(userId);
                    //绑定企业用户
                    bindSystemTenantUser(systemUserId, systemUserAuthParam.getPositional(), systemUserAuthParam.getEnterprise(), systemRole.getEnglishName(), systemUserAuthParam.getOwnerTotalAuth(),systemUserAuthParam.getStatus());
                    systemUserAuthParam.setSystemUserId(customResult.getData().toString());
                    systemUserInfo = new SystemUserInfo();
                    BeanUtils.copyProperties(systemUserInfoParam, systemUserInfo);
                    systemUserInfo.setUserType(systemUserAuthParam.getRegisterType());
                    if(!systemUserAuthParam.getStatus().equals(BusinessConfig.TEMP_STATUS)){
                        sendSystemMailMessage(SecurityUtils.getSystemTenantId(), systemUserAuthParam.getEnterprise(), systemUserInfo);
                    }
                }
                customResult.setMessage(ResultCode.SUCCESS.getMessage());
            } else {
                systemUserAuthParam.setSystemUserId(systemUserInfo.getId().toString());
                // 已经存在，需要判断是否已经激活，
                SystemUserInfo userBaseInfo = systemUserInfoService.getUserBaseInfo(systemUserInfo.getId());
                SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserAuthParam.getSystemUserId());
                if (systemTenantUser != null && systemTenantUser.getActiveStatus() != null && !systemTenantUser.getActiveStatus()) {
                    if(!systemUserAuthParam.getStatus().equals(BusinessConfig.TEMP_STATUS)){
                        sendSystemMailMessage(SecurityUtils.getSystemTenantId(), systemUserAuthParam.getEnterprise(), userBaseInfo);
                    }
                }
            }
        } else {
            SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfo(Long.parseLong(systemUserAuthParam.getSystemUserId()));
            if (systemUserInfo == null) {
                throw new ServiceException(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode() + "", ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
            }
            systemUserInfo.setRealName(systemUserAuthParam.getRealName());
            systemUserInfo.setSealFlag(false);//修改时将用户状态从已删除变为未删除
            systemUserInfo.setUpdateTime(new Date());
            systemUserInfo.setUpdateUser(systemUserAuthParam.getCreateUserId());
            if(systemUserAuthParam.getStatus().equals(BusinessConfig.TEMP_STATUS)){
                systemUserInfo.setPassword(passwordEncoder.encode(systemUserAuthParam.getDefaultPwd()));
                systemUserInfo.setStatus(BusinessConfig.TEMP_STATUS);
            }else {
                systemUserInfo.setPassword(null);
                systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
            }
            
            // 修改研究者信息
            if (formAudit){
                ProjectResearchersInfo researchersInfo = systemUserAuthParam.getResearchersInfo();
                if (researchersInfo != null){
                    researchersInfo.setCreateUser(systemUserId);
                    researchersInfo.setName(systemUserAuthParam.getRealName());
                    researchersInfo.setTelPhone(systemUserAuthParam.getMobile());
                    projectResearchersInfoService.saveOrUpdate(researchersInfo);
                    
                    if(StringUtils.isEmpty(systemUserInfo.getRoleDesc()) || !researchersInfo.getGroupInfo().equals(systemUserInfo.getRoleDesc())){
                        systemUserInfo.setRoleDesc(systemUserInfo.getRoleDesc());
                    }
                }
            }
            
            systemUserInfoService.updateByPrimaryKeySelective(systemUserInfo);
            
            //修改用户相关企业信息
            SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfo.getId().toString());
            if (systemTenantUser == null) {
                bindSystemTenantUser(systemUserInfo.getId().toString(), systemUserAuthParam.getPositional(), systemUserAuthParam.getEnterprise(), systemRole.getEnglishName(), systemUserAuthParam.getOwnerTotalAuth(), systemUserAuthParam.getStatus());
            } else {
                systemTenantUser.setOwnerTotalAuth(systemUserAuthParam.getOwnerTotalAuth());
                systemTenantUser.setCompanyOwnerUser(Constants.SYSTEM_MANAGE_ROLE.equals(systemRole.getEnglishName()));
                systemTenantUser.setEnterprise("-1".equals(systemUserAuthParam.getEnterprise()) ? null : systemUserAuthParam.getEnterprise());
                systemTenantUser.setPositional(systemUserAuthParam.getPositional());
                systemTenantUser.setDataFrom(Constants.USER_LOGIN_RESOURCE_2);
                if(systemUserAuthParam.getStatus().equals(BusinessConfig.TEMP_STATUS)){
                    systemTenantUser.setActiveStatus(true);
                }
                systemTenantUserService.updateSystemTenantUser(systemTenantUser);
            }
            //修改角色信息
            systemRoleService.deleteSystemUserRoleByUserId(systemUserInfo.getId(), tenantId, platformId);
            SystemUserRole systemUserRole = new SystemUserRole();
            systemUserRole.setUserId(systemUserInfo.getId());
            systemUserRole.setRoleId(Long.valueOf(systemUserAuthParam.getRoleId()));
            systemUserRole.setTenantId(tenantId);
            systemUserRole.setPlatformId(platformId);
            systemRoleService.insertSystemUserRole(systemUserRole);
            defaultActiveStatus = systemTenantUser.getActiveStatus() != null && systemTenantUser.getActiveStatus();

            if(!systemUserAuthParam.getStatus().equals(BusinessConfig.TEMP_STATUS)){
                if (!defaultActiveStatus) {
                    sendSystemMailMessage(SecurityUtils.getSystemTenantId(), systemUserInfo.getEnterprise(), systemUserInfo);
                    customResult.setMessage(ResultCode.SUCCESS.getMessage());
                }
                if (systemTenantUser != null && systemTenantUser.getActiveStatus() != null && !systemTenantUser.getActiveStatus()) {
                    // 发送邮箱
                    sendSystemMailMessage(SecurityUtils.getSystemTenantId(), systemUserAuthParam.getEnterprise(), systemUserInfo);
                }
            }
        }

        // 临床项目进行授权
        if (systemUserAuthParam.getProjectAuth()) {
            saveBindProjectUserAuthorization(systemRole, systemUserId, systemUserAuthParam.getProjectIds(), systemUserAuthParam, defaultActiveStatus, companyOwnerUser, tenantId, platformId);
        }

        // 专病库进行授权
        if (systemUserAuthParam.getDiseaseDataBaseAuth()) {
            diseaseDatabaseUserService.deleteDiseaseDatabaseUserByUserId(systemUserId);
            List<SystemUserAuthParam.DiseaseDatabase> diseaseDatabaseList = systemUserAuthParam.getDiseaseDatabaseList();
            diseaseDatabaseUserService.saveDiseaseDatabaseAuth(systemUserId, systemUserAuthParam.getOwnerTotalDatabaseAuth());
            for (SystemUserAuthParam.DiseaseDatabase diseaseDatabase : diseaseDatabaseList) {
                String databaseIds = diseaseDatabase.getDatabaseId().toString();
                String databaseNames = diseaseDatabase.getDatabaseName().toString();
                String[] databaseArray = databaseIds.split("#");
                String[] databaseNameArray = databaseNames.split("#");
                for (int i = 0; i < databaseArray.length; i++) {
                    diseaseDatabaseUserService.saveDiseaseDatabaseUser(databaseArray[i], databaseNameArray[i], diseaseDatabase.getValidateStartDate(), diseaseDatabase.getValidateEndDate(), systemUserId);
                }
            }
        }
        return customResult;
    }

    public void saveBindProjectUserAuthorization(SystemRole systemRole, String systemUserId, List<String> projectIds, SystemUserAuthParam systemUserAuthParam,
                                                 Boolean defaultActiveStatus, Boolean companyOwnerUser,
                                                 String tenantId, String platformId) {
        //List<Project> projectList = projectBaseManageService.getProjectUserInfoList(projectIds);
        List<ProjectUserInfo> projectUserList = projectUserInfoMapper.getProjectUserListByUserId(systemUserId);
        for (ProjectUserInfo projectUserInfo : projectUserList) {
            Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectUserInfo.getProjectId().toString());
            if (!projectBaseInfo.getCreateUser().equals(systemUserId)) {
                projectUserInfoMapper.deleteProjectUserInfo(projectUserInfo.getProjectId().toString(), systemUserId);
                projectUserRoleMapper.deleteProjectUserRoleByProjectIdAndUserId(projectUserInfo.getProjectId().toString(), systemUserId);
                log.error("projectUserInfo delete......{}", projectUserInfo.getUserId().toString());
            }
        }

        if(Constants.SYSTEM_MANAGE_ROLE.equals(systemRole.getEnglishName())) {
            for (String projectId : projectIds) {
                updateProjectManageUserBindAuth(projectId, systemUserId, tenantId, platformId);
            }
        }
        if(Constants.SYSTEM_USER_ROLE.equals(systemRole.getEnglishName())){
            List<ProjectUserAuthParam.ProjectOrgRoleInfo> projectOrgRoleList = systemUserAuthParam.getProjectOrgRoleList();
            String systemUserIdValue = systemUserAuthParam.getSystemUserId();
            for (ProjectUserAuthParam.ProjectOrgRoleInfo projectOrgRoleInfo : projectOrgRoleList) {
                String projectId = projectOrgRoleInfo.getProjectId();
                // 项目PA 新增同步项目角色
                if (systemUserIdValue.equals(systemUserAuthParam.getCreateUserId())) {
                        updateProjectManageUserBindAuth(projectId, systemUserIdValue, tenantId, platformId);
                } else {
                    //设置研究中心角色
                    projectUserService.deleteProjectUser(projectId, systemUserAuthParam.getSystemUserId(), true);
                    bindTenantProjectUser(projectId, systemUserAuthParam.getSystemUserId(), defaultActiveStatus, companyOwnerUser);
                    List<ProjectUserAuthParam.ProjectOrgInfo> projectOrgList = projectOrgRoleInfo.getProjectOrgList();
                    for (ProjectUserAuthParam.ProjectOrgInfo projectOrgInfo : projectOrgList) {
                        //查询项目研究中心是否完成写入
                        String projectOrgRoleId;
                        ProjectRoleQuery projectRoleQuery = projectRoleService.getProjectRoleByRoleId(projectOrgRoleInfo.getRoleId());
                        if (projectRoleQuery != null) {
                            projectOrgRoleInfo.setRoleName(projectRoleQuery.getName());
                        }
                        ProjectOrgRole projectOrgRole = projectOrgRoleService.getProjectOrgRoleByProjectOrgIdAndRoleName(projectId, projectOrgInfo.getProjectOrgCode(), projectOrgRoleInfo.getRoleName());
                        if (projectOrgRole == null) {
                            CustomResult result = projectOrgRoleService.initProjectOrgTemplateRole(projectId, projectOrgInfo.getOrgId().toString(), projectOrgInfo.getProjectOrgId().toString(), projectOrgInfo.getProjectOrgCode(), projectOrgRoleInfo.getRoleName(), systemUserAuthParam.getCreateUserId());
                            projectOrgRoleId = result.getData().toString();
                        } else {
                            projectOrgRoleId = projectOrgRole.getId().toString();
                        }
                        //保存项目成员记录(研究中心和研究中心角色)
                        organizationService.saveProjectUserOrgInfo(projectId, systemUserAuthParam.getSystemUserId(), projectOrgInfo.getOrgId().toString(), projectOrgInfo.getProjectOrgCode());
                        projectOrgRoleService.saveProjectUserOrgRole(projectId, systemUserAuthParam.getSystemUserId(), projectOrgRoleId, projectOrgRoleInfo.getOwnerTotalAuth());
                    }
                }
            }
        }
    }


    public void sendSystemMailMessage(String tenantId, String departmentId, SystemUserInfo user) {
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.execute(() -> {
            String departmentName = "";
            if ("-1".equals(departmentId)) {
                departmentName = "外部用户";
            } else {
                if (StringUtils.isNotBlank(tenantId)) {
                    SystemTenant systemTenant = systemTenantService.selectByPrimaryKey(Long.valueOf(tenantId));
                    if (systemTenant != null) {
                        departmentName = systemTenant.getName();
//                        if (StringUtils.isNotEmpty(departmentId)){
//                            SystemDepartment systemDepartment = systemDepartmentService.selectByPrimaryKey(Long.parseLong(departmentId));
//                            departmentName =departmentName+"-"+systemDepartment.getName();
//                        }
                    }
                }
            }
            departmentName = "【" + departmentName + "】";
            // 发送短信或者邮箱
            if (Constants.USER_TYPE_VALUE_01.equals(user.getUserType())) {
                // 手机号邀请
                sendMessageUtil.sendInviteMessage(user.getUsername(), departmentName);
            } else if (Constants.USER_TYPE_VALUE_02.equals(user.getUserType())) {
                // 获取token
                String tokenValue = createTokenService.createToken(user);
                // 邮箱邀请
                String path = actPage + "register?" + "token=" + tokenValue;
                String contents = EmailMsgConstants.CONTENT.replace("####", departmentName).replace("@@@@", path);
                sendMessageUtil.sendMail(user.getEmail(), contents);
            }
        });
    }

    public Set<String> getMenuPermission(BaseSystemUser user) {
        Set<String> perms = new HashSet<>();
        if (user.isAdmin()) {
            perms.add("*:*:*");
        } else {
            perms.addAll(systemMenuService.selectMenuPermsByUserId(user.getId()));
        }
        return perms;
    }

    @Override
    public List<SystemDepartmentVo> getDepartmentDrop(String id) {
        SystemDepartmentExample example = new SystemDepartmentExample();
        SystemDepartmentExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(id);
        List<SystemDepartment> systemDepartmentList = systemDepartmentService.selectByExample(example);
        List<SystemDepartmentVo> sdv = new ArrayList<>();
        for (SystemDepartment sd : systemDepartmentList) {
            SystemDepartmentVo systemDepartmentVo = new SystemDepartmentVo();
            BeanUtils.copyProperties(systemDepartmentVo, sd);
            sdv.add(systemDepartmentVo);
        }
        return sdv;
    }

    @Override
    public CustomResult deleteSystemUserById(String userId) {
        SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfo(Long.valueOf(userId));
        if (systemUserInfo == null) {
            throw new ServiceException(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode() + "", ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
        }
        return systemUserInfoService.deleteSystemUserInfo(userId);
    }

    @Override
    public SystemUserAuthVo getSystemUserInfo(String systemUserId) {
        SystemUserAuthVo systemUserAuthVo = new SystemUserAuthVo();
        List<ProjectUserVo> projectUserVoList = new ArrayList<>();
        // 查询用户基本信息
        SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfo(NumberUtil.parseLong(systemUserId));
        if (systemUserInfo == null) {
            throw new ServiceException(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode() + "", ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
        }
        SystemUserInfoVo systemUserInfoVo = new SystemUserInfoVo();
        BeanUtils.copyProperties(systemUserInfo, systemUserInfoVo);
        if (StringUtils.isNotEmpty(systemUserInfo.getMobile())){
            systemUserInfoVo.setMobile(DesensitizeUtil.aesDecrypt(systemUserInfo.getMobile()));
        }
        systemUserAuthVo.setSystemUserInfoVo(systemUserInfoVo);

        // 系统角色
        List<SystemRole> systemRoleList = systemRoleService.selectRoleByUserId(NumberUtil.parseLong(systemUserId));
        systemUserAuthVo.setSystemRoleList(systemRoleList);

        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfo.getId().toString());
        boolean ownerTotalAuth = (systemTenantUser == null || systemTenantUser.getOwnerTotalAuth() != null) && systemTenantUser.getOwnerTotalAuth();
        systemUserAuthVo.setOwnerTotalAuth(ownerTotalAuth);

        Map<Long, List<ProjectRoleVo>> projectRoleMap = new HashMap<>();
        Map<Long, List<ProjectOrgVo>> projectOrgMap = new HashMap<>();
        // 查询用户授权项目
        List<ProjectUserInfo> projectUserInfoList = projectUserService.getProjectUserListByUserId(systemUserId);
        projectUserInfoList.forEach(projectUser -> {
            ProjectUserVo projectUserVo = projectUserService.getProjectUserInfo(projectUser.getProjectId().toString(), systemUserId);
            projectUserVo.setProjectCreateUser(projectUser.getUserId().toString().equals(systemUserId) || projectUserVo.getPaRole());
            ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
            projectRoleQuery.setProjectId(projectUser.getProjectId());
            List<ProjectRoleVo> projectRoleList = projectRoleService.getProjectRoleListForCombobox(projectRoleQuery);
            projectRoleMap.put(projectUser.getProjectId(), projectRoleList);
            List<ProjectOrgVo> projectOrgList = organizationService.getProjectOrgListByCondition(projectUser.getProjectId().toString(), null, null);
            projectOrgMap.put(projectUser.getProjectId(), projectOrgList);
            projectUserVo.setProjectRoleMap(projectRoleMap);
            projectUserVo.setProjectOrgMap(projectOrgMap);
            projectUserVoList.add(projectUserVo);
        });

        systemUserAuthVo.setProjectUserList(projectUserVoList);
        // 专病授权列表
        List<SystemUserAuthVo.DiseaseDatabase> diseaseDatabaseAuthList = new ArrayList<>();
        List<DiseaseDatabaseUser> diseaseDatabaseList = diseaseDatabaseUserService.getDiseaseDatabaseListByUserId(systemUserId);
        diseaseDatabaseList.forEach(diseaseDatabase -> {
            SystemUserAuthVo.DiseaseDatabase database = new SystemUserAuthVo.DiseaseDatabase();
            BeanUtils.copyProperties(diseaseDatabase, database);
            diseaseDatabaseAuthList.add(database);
        });
        systemUserAuthVo.setDiseaseDatabaseList(diseaseDatabaseAuthList);
        DiseaseDatabaseAuth diseaseDatabaseAuth = diseaseDatabaseUserService.getDiseaseDatabaseAuth(systemUserId);
        systemUserAuthVo.setOwnerTotalDatabaseAuth(diseaseDatabaseAuth != null && diseaseDatabaseAuth.getOwnerTotalAuth());

        // 获取研究者信息
        ProjectResearchersInfo researchersInfo = projectResearchersInfoService.getProjectResearcherInfo(systemUserId);
        systemUserAuthVo.setResearchersInfo(researchersInfo);
        return systemUserAuthVo;
    }

    public void updateProjectManageUserBindAuth(String projectId, String userId, String tenantId, String platformId) {
        ProjectRole projectRole = projectRoleService.getProjectManageRoleInfoByProjectRoleCode(projectId, ProjectRoleEnum.PROJECT_PA.getCode(), tenantId);
        if (projectRole != null) {
            ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectId, userId);
            if (projectUserInfo == null) {
                ProjectUserInfo userInfo = new ProjectUserInfo();
                userInfo.setId(SnowflakeIdWorker.getUuid());
                userInfo.setProjectId(NumberUtil.parseLong(projectId));
                userInfo.setUserId(NumberUtil.parseLong(userId));
                userInfo.setActiveStatus(true);
                userInfo.setRejectStatus(false);
                userInfo.setPaRole(true);
                userInfo.setStatus(BusinessConfig.VALID_STATUS);
                userInfo.setCreateTime(new Date());
                userInfo.setTenantId(tenantId);
                userInfo.setPlatformId(platformId);
                projectUserInfoMapper.insert(userInfo);
            }else{
                projectUserInfo.setRejectStatus(false);
                projectUserInfo.setActiveStatus(true);
                projectUserInfo.setPaRole(true);
                projectUserInfoMapper.updateByPrimaryKeySelective(projectUserInfo);
            }

            ProjectUserRoleVo projectUserRoleVo = projectUserRoleMapper.getProjectRoleListByProjectIdAndUserId(projectId, userId, projectRole.getId().toString());
            if (projectUserRoleVo == null) {
                ProjectUserRole projectUserRole = new ProjectUserRole();
                projectUserRole.setProjectId(NumberUtil.parseLong(projectId));
                projectUserRole.setUserId(NumberUtil.parseLong(userId));
                projectUserRole.setRoleId(projectRole.getId());
                projectUserRole.setTenantId(tenantId);
                projectUserRole.setPlatformId(platformId);
                projectUserRoleMapper.insert(projectUserRole);
            }
        }
    }

    /**
     * 企业添加成员-绑定项目成员
     * @param projectId
     * @param systemUserId
     * @param defaultActiveStatus
     * @param companyOwnerUser
     */
    public void bindTenantProjectUser(String projectId, String systemUserId, boolean defaultActiveStatus, boolean companyOwnerUser) {
        ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectId, systemUserId);
        if (projectUserInfo == null) {
            ProjectUserInfo projectUserInfoParam = new ProjectUserInfo();
            projectUserInfoParam.setId(SnowflakeIdWorker.getUuid());
            projectUserInfoParam.setProjectId(Long.parseLong(projectId));
            projectUserInfoParam.setUserId(Long.parseLong(systemUserId));
            projectUserInfoParam.setStatus(BusinessConfig.VALID_STATUS);
            projectUserInfoParam.setActiveStatus(defaultActiveStatus);
            projectUserInfoParam.setRejectStatus(false);
            projectUserInfoParam.setCreateTime(new Date());
            // 确认是否项目PA角色
            projectUserInfoParam.setPaRole(companyOwnerUser);
            projectUserInfoMapper.insertSelective(projectUserInfoParam);
        }else{
            projectUserInfo.setPaRole(companyOwnerUser);
            projectUserInfo.setRejectStatus(false);
            projectUserInfo.setActiveStatus(true);
            projectUserInfoMapper.updateByPrimaryKeySelective(projectUserInfo);
        }
    }

    /**
     * 绑定企业用户
     *
     * @param userId
     * @param positional
     * @param enterprise
     * @param englishName
     */
    public void bindSystemTenantUser(String userId, String positional, String enterprise, String englishName, Boolean ownerTotalAuth, Integer status) {
        SystemTenantUser systemTenantUser = new SystemTenantUser();
        systemTenantUser.setId(SnowflakeIdWorker.getUuid());
        systemTenantUser.setUserId(Long.valueOf(userId));
        systemTenantUser.setCreateTime(new Date());
        systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
        systemTenantUser.setTenantId(SecurityUtils.getSystemTenantId());
        systemTenantUser.setPlatformId(SecurityUtils.getSystemPlatformId());
        // 登录后完成激活,如果是默认密码，则直接激活，否则激活状态为未激活
        if(BusinessConfig.TEMP_STATUS.equals(status)){
            systemTenantUser.setActiveStatus(true);
        }else{
            systemTenantUser.setActiveStatus(false);
        }
        systemTenantUser.setPositional(positional);
        systemTenantUser.setOwnerTotalAuth(ownerTotalAuth);
        //判断是否为外部用户
        systemTenantUser.setEnterprise("-1".equals(enterprise) ? null : enterprise);
        systemTenantUser.setCompanyOwnerUser(Constants.SYSTEM_MANAGE_ROLE.equals(englishName));
        systemTenantUser.setDataFrom(Constants.USER_LOGIN_RESOURCE_2);
        systemTenantUserService.insertSystemTenantUser(systemTenantUser);
    }
}
