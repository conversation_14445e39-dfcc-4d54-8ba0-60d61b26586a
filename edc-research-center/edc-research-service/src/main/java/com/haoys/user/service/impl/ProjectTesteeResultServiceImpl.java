package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.TesteeVariableRecord;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormAndTableParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormParam;
import com.haoys.user.domain.param.testee.QueryTesteeGroupParam;
import com.haoys.user.domain.vo.ecrf.ProjectFormResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailAndTableResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeVariableResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormVariableComplateResultVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeResultExportVo;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.mapper.ProjectTesteeResultMapper;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.ProjectTesteeFile;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeResultExample;
import com.haoys.user.model.ProjectTesteeTable;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormDvpRuleService;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class ProjectTesteeResultServiceImpl implements ProjectTesteeResultService {

    @Autowired
    private ProjectTesteeResultMapper projectTesteeResultMapper;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ProjectTesteeTableService projectTesteeTableService;
    @Autowired
    private ProjectTesteeFileService projectTesteeFileService;
    @Autowired
    private TemplateFormDvpRuleService templateFormDvpRuleService;
    @Autowired
    private FlowPlanService flowPlanService;
    
    
    @Override
    public ProjectTesteeResult selectByPrimaryKey(Long id) {
        return projectTesteeResultMapper.selectByPrimaryKey(id);
    }

    @Override
    public void insert(ProjectTesteeResult projectTesteeResult) {
        projectTesteeResultMapper.insert(projectTesteeResult);
    }

    @Override
    public void updateByPrimaryKeySelective(ProjectTesteeResult projectTesteeResultVo) {
        projectTesteeResultMapper.updateByPrimaryKeySelective(projectTesteeResultVo);
    }

    @Override
    public List<ProjectTesteeResultWrapperVo> getProjectTesteeFormAndTableResultList(String projectId, String planId, String projectOrgId, String variableId, String testeeId){
        return projectTesteeResultMapper.getProjectTesteeFormAndTableResultList(projectId, planId, projectOrgId, variableId, testeeId);
    }

    @Override
    public CustomResult saveProjectTesteeTableRowRecord(ProjectTesteeTableParam projectTesteeTableParam){
        CustomResult customResult = new CustomResult();

        List<ProjectFormResultVo> resultList = new ArrayList<>();
        List<ProjectTesteeTable> tableRowList = new ArrayList<>();
        boolean insertValue = false;

        Long tableNumber = SnowflakeIdWorker.getUuid();
        List<ProjectTesteeTableParam.TableRowData> tableRowDataList = projectTesteeTableParam.getTableRowDataList();

        for (ProjectTesteeTableParam.TableRowData rowData : tableRowDataList) {
            if(rowData.getFieldValue() == null){continue;}
            ProjectTesteeTable projectTesteeTable = new ProjectTesteeTable();
            projectTesteeTable.setProjectId(projectTesteeTableParam.getProjectId());
            projectTesteeTable.setPlanId(projectTesteeTableParam.getPatientPlanId());
            projectTesteeTable.setVisitId(projectTesteeTableParam.getVisitId());
            projectTesteeTable.setFormId(projectTesteeTableParam.getFormId());
            projectTesteeTable.setFormExpandId(projectTesteeTableParam.getFormExpandId());
            if(projectTesteeTableParam.getTesteeGroupId() != null){
                projectTesteeTable.setGroupId(projectTesteeTableParam.getTesteeGroupId());
            }
            if(projectTesteeTableParam.getFormDetailId() != null){
                projectTesteeTable.setFormDetailId(projectTesteeTableParam.getFormDetailId());
            }
            projectTesteeTable.setFormTableId(rowData.getFormTableId());
            projectTesteeTable.setTesteeId(projectTesteeTableParam.getTesteeId());

            projectTesteeTable.setLabel(rowData.getLabel());
            projectTesteeTable.setFieldName(rowData.getFieldName());
            projectTesteeTable.setFieldValue(rowData.getFieldValue() == null ? "" : rowData.getFieldValue());
            projectTesteeTable.setFieldText(rowData.getFieldText() == null ? "" : rowData.getFieldText());
            projectTesteeTable.setUnitValue(rowData.getUnitValue());
            projectTesteeTable.setUnitText(rowData.getUnitText());
            projectTesteeTable.setRowNo(tableNumber);
            projectTesteeTable.setSort(rowData.getSort());
            projectTesteeTable.setComplateStatus(StringUtils.isBlank(rowData.getFieldValue()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
            if(rowData.getTesteeResultId() != null){
                ProjectTesteeTable projectTesteeTableRow = projectTesteeTableService.getProjectTesteeTableRowRecordByTableId(rowData.getTesteeResultId());
                projectTesteeTableRow.setFieldValue(rowData.getFieldValue());
                projectTesteeTableRow.setFieldText(rowData.getFieldText());
                projectTesteeTableRow.setUnitValue(rowData.getUnitValue());
                projectTesteeTableRow.setUnitText(rowData.getUnitText());
                if(projectTesteeTableParam.getFormDetailId() != null){
                    projectTesteeTableRow.setFormDetailId(projectTesteeTableParam.getFormDetailId());
                }
                if(projectTesteeTableParam.getResourceVariableId() != null){
                    projectTesteeTableRow.setResourceVariableId(rowData.getResourceVariableId());
                }
                if(rowData.getResourceTableId() != null){
                    projectTesteeTableRow.setResourceTableId(rowData.getResourceTableId());
                }
                projectTesteeTableRow.setComplateStatus(StringUtils.isBlank(rowData.getFieldValue()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
                if(StringUtils.isNotEmpty(rowData.getStatus())){
                    projectTesteeTableRow.setStatus(rowData.getStatus());
                }
                projectTesteeTableRow.setRowNo(projectTesteeTableRow.getRowNo());
                projectTesteeTableRow.setUpdateTime(new Date());
                projectTesteeTableRow.setUpdateUser(projectTesteeTableParam.getCreateUserId());
                projectTesteeTableService.updateProjectTesteeTableRowRecord(projectTesteeTableRow);
            }else{
                ProjectFormResultVo projectFormResultVo = new ProjectFormResultVo();
                projectTesteeTable.setId(SnowflakeIdWorker.getUuid());
                if(projectTesteeTableParam.getTaskSubmitTime() == null){
                    projectTesteeTable.setCreateTime(new Date());
                }else{
                    projectTesteeTable.setCreateTime(projectTesteeTableParam.getTaskSubmitTime());
                }
                if(projectTesteeTableParam.getRowNo() != null){
                    projectTesteeTable.setRowNo(projectTesteeTableParam.getRowNo());
                }

                if(rowData.getResourceVariableId() != null){
                    projectTesteeTable.setResourceVariableId(rowData.getResourceVariableId());
                }
                if(rowData.getResourceTableId() != null){
                    projectTesteeTable.setResourceTableId(rowData.getResourceTableId());
                }

                projectTesteeTable.setStatus(BusinessConfig.VALID_STATUS);
                projectTesteeTable.setCreateUser(projectTesteeTableParam.getCreateUserId());
                projectTesteeTable.setTenantId(SecurityUtils.getSystemTenantId());
                projectTesteeTable.setPlatformId(SecurityUtils.getSystemPlatformId());
                tableRowList.add(projectTesteeTable);
                //projectTesteeTableService.saveProjectTesteeTableRowRecord(projectTesteeTable);
                insertValue = true;
                projectFormResultVo.setProjectId(projectTesteeTableParam.getProjectId().toString());
                projectFormResultVo.setPlanId(projectTesteeTableParam.getPatientPlanId().toString());
                projectFormResultVo.setVisitId(projectTesteeTableParam.getVisitId().toString());
                projectFormResultVo.setFormId(projectTesteeTableParam.getFormId().toString());
                projectFormResultVo.setFormDetailId(projectTesteeTableParam.getFormDetailId().toString());
                projectFormResultVo.setRowNo(tableNumber.toString());
                projectFormResultVo.setFormTableId(rowData.getFormTableId().toString());
                projectFormResultVo.setFormTableResultId(projectTesteeTable.getId().toString());
                resultList.add(projectFormResultVo);
            }
        }
        if(insertValue){
            projectTesteeTableService.saveBatchProjectTesteeTableRowRecord(tableRowList);
        }
        List<Long> variableImageList = projectTesteeTableParam.getVariableImageList();
        for (Long fileId : variableImageList) {
            ProjectTesteeFile projectTesteeFile = projectTesteeFileService.getProjectTesteeFileByFileId(fileId);
            if(projectTesteeFile != null){
                projectTesteeFile.setTableNumber(tableNumber);
                projectTesteeFileService.updateProjectTesteeFileByFileId(projectTesteeFile);
            }

        }
        customResult.setData(resultList.size());
        return customResult;
    }
    @Override
    public List<Map<String, Object>> getTesteeFormResultList(String projectId, String visitId, String testeeId) {
        return projectTesteeResultMapper.getTesteeFormResultList(projectId, visitId, testeeId);
    }

    @Override
    public ProjectTesteeResult getProjectTesteeFormOneResult(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId) {
        ProjectTesteeResultExample projectTesteeResultExample = new ProjectTesteeResultExample();
        ProjectTesteeResultExample.Criteria criteria = projectTesteeResultExample.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        if(StringUtils.isNotEmpty(formExpandId)){criteria.andFormExpandIdEqualTo(Long.parseLong(formExpandId));}
        criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectTesteeResult> projectTesteeResults = projectTesteeResultMapper.selectByExample(projectTesteeResultExample);
        if(projectTesteeResults != null && projectTesteeResults.size() >0){
            return projectTesteeResults.get(0);
        }
        return null;
    }

    @Override
    public String saveProjectTesteeFormTableResult(String projectId, String planId, String visitId, String formId, String formDetailId, String groupId, String testeeId,
                                                   String complateStatus, String tenantId, String platformId) {
        ProjectTesteeResult projectTesteeResult = getProjectTesteeFormOneResult(projectId, planId, visitId, formId, "", formDetailId, testeeId);
        if(projectTesteeResult == null){
            ProjectTesteeResult projectTesteeResultParam = new ProjectTesteeResult();
            projectTesteeResultParam.setId(SnowflakeIdWorker.getUuid());
            projectTesteeResultParam.setProjectId(Long.parseLong(projectId));
            projectTesteeResultParam.setPlanId(Long.parseLong(planId));
            projectTesteeResultParam.setVisitId(Long.parseLong(visitId));
            projectTesteeResultParam.setFormId(Long.parseLong(formId));
            projectTesteeResultParam.setFormDetailId(Long.parseLong(formDetailId));

            //设置key和label属性
            TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
            if(StringUtils.isNotEmpty(groupId)){
                projectTesteeResultParam.setGroupId(Long.parseLong(groupId));
                //TemplateFormGroupVariable templateGroupInfoByBaseVariable = templateFormGroupService.getTemplateGroupInfoByBaseVariableId(projectId, visitId, formId, formDetailId, "", groupId, testeeId);
                //templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfig(templateGroupInfoByBaseVariable.getResourceVariableId());
            }

            if(templateFormDetailConfig != null){
                projectTesteeResultParam.setFieldName(templateFormDetailConfig.getFieldName());
                projectTesteeResultParam.setLabel(templateFormDetailConfig.getLabel());
            }
            projectTesteeResultParam.setFieldValue("");
            //complateStatus 此状态为整体表格录入状态
            projectTesteeResultParam.setComplateStatus(complateStatus);
            projectTesteeResultParam.setTesteeId(Long.parseLong(testeeId));
            projectTesteeResultParam.setCreateTime(new Date());
            projectTesteeResultParam.setTenantId(tenantId);
            projectTesteeResultParam.setPlatformId(platformId);
            projectTesteeResultMapper.insertSelective(projectTesteeResultParam);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public List<ProjectTesteeFormDetailAndTableResultVo> getTemplateFormDetailAndTableResult(String projectId, String planId, String visitId, String formId, String formDetailId, String tableId) {
        List<ProjectTesteeFormDetailAndTableResultVo> dataList = new ArrayList<>();
        ProjectTesteeResultExample example = new ProjectTesteeResultExample();
        ProjectTesteeResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));

        if(StringUtils.isNotEmpty(planId)){
            criteria.andPlanIdEqualTo(Long.parseLong(planId));
        }
        if(StringUtils.isNotEmpty(visitId)){
            criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        }
        if(StringUtils.isNotEmpty(formId)){
            criteria.andFormIdEqualTo(Long.parseLong(formId));
        }
        if(StringUtils.isNotEmpty(formDetailId)){
            criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        }
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectTesteeResult> projectTesteeResults = projectTesteeResultMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectTesteeResults)){
            for (ProjectTesteeResult projectTesteeResult : projectTesteeResults) {
                ProjectTesteeFormDetailAndTableResultVo projectTesteeFormDetailAndTableResultVo = new ProjectTesteeFormDetailAndTableResultVo();
                projectTesteeFormDetailAndTableResultVo.setProjectId(projectTesteeResult.getProjectId().toString());
                projectTesteeFormDetailAndTableResultVo.setVisitId(projectTesteeResult.getVisitId().toString());
                projectTesteeFormDetailAndTableResultVo.setFormId(projectTesteeResult.getFormId().toString());
                projectTesteeFormDetailAndTableResultVo.setFormDetailId(projectTesteeResult.getFormDetailId() == null ? "" : projectTesteeResult.getFormDetailId().toString());
                projectTesteeFormDetailAndTableResultVo.setFormResultId(projectTesteeResult.getId().toString());
                projectTesteeFormDetailAndTableResultVo.setFieldName(projectTesteeResult.getFieldName());
                projectTesteeFormDetailAndTableResultVo.setFieldValue(projectTesteeResult.getFieldValue());
                if(StringUtils.isNotEmpty(tableId)){
                    List<ProjectTesteeTableVo> projectTesteeTableRowFieldRecords = projectTesteeTableService.getProjectTesteeTableRowFieldRecord(projectId, tableId);
                    if(CollectionUtil.isNotEmpty(projectTesteeTableRowFieldRecords)){
                        List<ProjectTesteeFormDetailAndTableResultVo.ProjectTesteeTableResultVo> tableList = new ArrayList<>();
                        for (ProjectTesteeTableVo projectTesteeTableRowFieldRecord : projectTesteeTableRowFieldRecords) {
                            ProjectTesteeFormDetailAndTableResultVo.ProjectTesteeTableResultVo projectTesteeTableResultVo = new ProjectTesteeFormDetailAndTableResultVo.ProjectTesteeTableResultVo();
                            projectTesteeTableResultVo.setFormResultTableId(projectTesteeTableRowFieldRecord.getId().toString());
                            projectTesteeTableResultVo.setFormTableId(projectTesteeTableRowFieldRecord.getFormTableId().toString());
                            projectTesteeTableResultVo.setFieldName(projectTesteeTableRowFieldRecord.getFieldName());
                            projectTesteeTableResultVo.setFieldValue(projectTesteeTableRowFieldRecord.getFieldValue());
                            tableList.add(projectTesteeTableResultVo);
                        }
                        projectTesteeFormDetailAndTableResultVo.setDataList(tableList);
                    }
                }
                dataList.add(projectTesteeFormDetailAndTableResultVo);
            }
        }
        return dataList;
    }

    @Override
    public void saveProjectTesteeBaseFormVariableResult(ProjectTesteeResult projectTesteeResult) {
        projectTesteeResultMapper.insert(projectTesteeResult);
    }
    
    @Override
    public void saveProjectTesteeResult(String projectId, String visitId, String formId, String variableId, String label,
                                        String fieldName, String fieldText, String fieldValue, String testeeId,
                                        String createUserId, String dataFrom, String tenantId, String platformId) {
        ProjectTesteeResult projectTesteeResult = new ProjectTesteeResult();
        projectTesteeResult.setId(SnowflakeIdWorker.getUuid());
        projectTesteeResult.setProjectId(NumberUtil.parseLong(projectId));
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo != null){
            projectTesteeResult.setPlanId(flowPlanInfo.getId());
        }
        projectTesteeResult.setVisitId(NumberUtil.parseLong(visitId));
        projectTesteeResult.setFormId(NumberUtil.parseLong(formId));
        projectTesteeResult.setFormDetailId(NumberUtil.parseLong(variableId));
        projectTesteeResult.setTesteeId(NumberUtil.parseLong(testeeId));
        projectTesteeResult.setLabel(label);
        projectTesteeResult.setFieldName(fieldName);
        projectTesteeResult.setFieldValue(fieldValue);
        projectTesteeResult.setComplateStatus(StringUtils.isEmpty(projectTesteeResult.getFieldValue()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
        projectTesteeResult.setFieldText(fieldText);
        projectTesteeResult.setTesteeInput(false);
        projectTesteeResult.setDataFrom(dataFrom);
        projectTesteeResult.setCreateUser(createUserId);
        projectTesteeResult.setCreateTime(new Date());
        projectTesteeResult.setTenantId(tenantId);
        projectTesteeResult.setPlatformId(platformId);
        projectTesteeResultMapper.insertSelective(projectTesteeResult);
    }

    @Override
    public List<ProjectTesteeResult> getTesteeVisitFormResultDetail(String projectId, String planId, String visitId, String formId, String testeeId) {
        ProjectTesteeResultExample example = new ProjectTesteeResultExample();
        ProjectTesteeResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        if(StringUtils.isNotEmpty(planId)){
            criteria.andPlanIdEqualTo(Long.parseLong(planId));
        }
        if(StringUtils.isNotEmpty(visitId)){
            criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        }
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        return projectTesteeResultMapper.selectByExample(example);
    }

    @Override
    public ProjectTesteeResult getProjectTesteeGroupResult(String projectId, String visitId, String formId, String groupVariableId, String groupId, String testeeId) {
        return projectTesteeResultMapper.getProjectTesteeGroupResult(projectId, visitId, formId, groupVariableId, groupId, testeeId);
    }

    @Override
    public List<ProjectTesteeResultExportVo> getProjectTesteeFormResultByVariableIds(String projectId, String visitId, String formId, String formDetailId, String testeeId, String orgId) {
        return projectTesteeResultMapper.getProjectTesteeFormResultByVariableIds(projectId, visitId, formId, formDetailId, testeeId, orgId);
    }

    @Override
    public List<ProjectTesteeExportViewVo> getExportProjectTesteeListForPage(String projectId, String code, String realName, String orgId, String ownerDoctor, String status, String sortField, String sortType, String conditionValue, String conditionTableValue) {
        return projectTesteeResultMapper.getExportProjectTesteeListForPage(projectId, code, realName, orgId, ownerDoctor, status, sortField, sortType, conditionValue, conditionTableValue);
    }

    @Override
    public List<Map<String,Object>> getExportProjectTesteeListForPageVersion2(String projectId, String code, String realName, String orgIds, String ownerDoctor, String status, String sortField, String sortType, List<QueryTesteeFormParam> queryTesteeFormParamList, List<QueryTesteeFormAndTableParam> queryTesteeFormAndTableParamList, List<QueryTesteeGroupParam> queryTesteeGroupParamList) {
        return projectTesteeResultMapper.getExportProjectTesteeListForPageVersion2(projectId, code, realName, orgIds, ownerDoctor, status, sortField, sortType, queryTesteeFormParamList, queryTesteeFormAndTableParamList, queryTesteeGroupParamList);
    }

    @Override
    public List<ProjectTesteeExportViewVo.FormVariableVo> getExportProjectTesteeVariableValue(String projectId, String testeeId, String conditionValue, String conditionTableValue) {
        return projectTesteeResultMapper.getExportProjectTesteeVariableValue(projectId, testeeId, conditionValue, conditionTableValue);
    }

    @Override
    public List<ProjectTesteeVariableResultVo> getProjectTesteeVariableResultFromMysqlDataSource(String projectId, String testeeId) {
        return projectTesteeResultMapper.getProjectTesteeVariableResultFromMysqlDataSource(projectId, testeeId);
    }

    @Override
    public void deleteTesteeFormResultDetailById(long testeeResultId) {
        projectTesteeResultMapper.deleteByPrimaryKey(testeeResultId);
    }

    @Override
    public ProjectTesteeResult getTesteeFormResultValueByFormDetailId(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId) {
        ProjectTesteeResult testeeFormResultValue = projectTesteeResultMapper.getTesteeFormResultValueByFormDetailId(projectId, planId, visitId, formId, formExpandId, formDetailId, testeeId);
        /*if(testeeFormResultValue == null || StringUtils.isEmpty(testeeFormResultValue.getFieldValue())){
            TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
            if(templateFormDetailConfig.getBaseVariableId() != null){
                ProjectTesteeVariableSync data = projectTesteeVariableSyncService.getVariableSyncByBaseVariableId(templateFormDetailConfig.getBaseVariableId());
                if(data != null && data.getId() != null){
                    visitId = data.getSourceVisitId().toString();
                    formId = data.getSourceFormId().toString();
                    formDetailId = data.getSourceVariableId().toString();
                    ProjectTesteeResult testeeFormVariableSyncResult = projectTesteeResultMapper.getTesteeFormResultValueByFormDetailId(projectId, planId, visitId, formId, formDetailId, testeeId);
                    if(testeeFormVariableSyncResult != null){
                        ProjectTesteeResult testeeFormResult = new ProjectTesteeResult();
                        // 查询是否设置了默认值
                        templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
                        testeeFormResult.setFieldValue(templateFormDetailConfig.getDefaultValue());
                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                        if(variableTypeList.contains(templateFormDetailConfig.getType())){
                            testeeFormResult.setFieldValue(templateFormDetailConfig.getDefaultDicValue());
                        }
                        return testeeFormResult;
                    }
                }
            }
        }*/
        return testeeFormResultValue;
    }

    @Override
    public List<ProjectTesteeResult> selectProjectTesteeFormVariableValues(ProjectTesteeResultExample example) {
        return projectTesteeResultMapper.selectByExample(example);
    }

    @Override
    public void initProjectUserFormVariableInfo() {
    }

    @Override
    public Boolean getSystemDictionaryOptionReference(String dictionaryId) {
        return projectTesteeResultMapper.getSystemDictionaryOptionReference(dictionaryId) != null;
    }

    @Override
    public Boolean getProjectDictionaryOptionReference(String projectId, String dictionaryId) {
        return projectTesteeResultMapper.getProjectDictionaryOptionReference(projectId, dictionaryId) != null;
    }

    @Override
    public ProjectTesteeResultWrapperVo getTesteeFormBaseInfoByFormVariableId(String projectId, String formId, String formDetailId, String testeeId) {
        return projectTesteeResultMapper.getTesteeFormBaseInfoByFormVariableId(projectId, formId, formDetailId, testeeId);
    }

    @Override
    public ProjectTesteeResultWrapperVo saveTesteeBaseFormVariableInfo(String projectId, String formId, String variableId, String label, String fieldName, String fieldText, String value, String testeeId, String createUserId, String dataFrom) {
        ProjectTesteeResultWrapperVo projectTesteeResultWrapperVo = new ProjectTesteeResultWrapperVo();
        ProjectTesteeResult projectTesteeResult = new ProjectTesteeResult();
        projectTesteeResult.setId(SnowflakeIdWorker.getUuid());
        projectTesteeResult.setProjectId(NumberUtil.parseLong(projectId));
        projectTesteeResult.setFormId(NumberUtil.parseLong(formId));
        projectTesteeResult.setFormDetailId(NumberUtil.parseLong(variableId));
        projectTesteeResult.setTesteeId(NumberUtil.parseLong(testeeId));
        projectTesteeResult.setLabel(label);
        projectTesteeResult.setFieldName(fieldName);
        projectTesteeResult.setFieldValue(value);
        projectTesteeResult.setFieldText(fieldText);
        projectTesteeResult.setTesteeInput(true);
        projectTesteeResult.setDataFrom(dataFrom);
        projectTesteeResult.setCreateUser(createUserId);
        projectTesteeResult.setCreateTime(new Date());
        projectTesteeResult.setTenantId(SecurityUtils.getSystemTenantId());
        projectTesteeResult.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectTesteeResultMapper.insertSelective(projectTesteeResult);
        BeanUtils.copyProperties(projectTesteeResult, projectTesteeResultWrapperVo);
        return projectTesteeResultWrapperVo;
    }

    @Override
    @DS("slave_2")
    public void saveTesteeVariableRecord() throws IOException {
        InputStream resourceAsStream = Resources.getResourceAsStream("com/haoys/user/mapper/ProjectTesteeResultMapper.xml");
        SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(resourceAsStream);
        SqlSession session = sqlSessionFactory.openSession();
        System.out.println("===== 开始插入数据 =====");
        long startTime = System.currentTimeMillis();
        int waitTime = 10;
        try {
            List<TesteeVariableRecord> testeeVariableRecordList = new ArrayList<>();
            Long projectId = SnowflakeIdWorker.getUuid();
            for (int i = 1; i <= 300000; i++) {
                TesteeVariableRecord testeeVariableRecord = new TesteeVariableRecord();
                testeeVariableRecord.setProjectId(projectId);
                testeeVariableRecord.setPlanId(SnowflakeIdWorker.getUuid());
                testeeVariableRecord.setVisitId(SnowflakeIdWorker.getUuid());
                testeeVariableRecord.setFormId(SnowflakeIdWorker.getUuid());
                testeeVariableRecord.setFormDetailId(SnowflakeIdWorker.getUuid());
                testeeVariableRecord.setTesteeId(SnowflakeIdWorker.getUuid());
                testeeVariableRecord.setFieldName(RandomStringUtils.randomNumeric(22));
                testeeVariableRecord.setFieldValue(RandomStringUtils.randomNumeric(16));
                testeeVariableRecord.setCreateTime(new Date());
                testeeVariableRecordList.add(testeeVariableRecord);
                if (i % 1000 == 0) {
                    session.insert("batchInsertTesteeRecord", testeeVariableRecordList);
                    // 每 1000 条数据提交一次事务
                    session.commit();
                    testeeVariableRecordList.clear();

                    // 等待一段时间
                    Thread.sleep(waitTime * 1000);
                }
            }
            // 最后插入剩余的数据
            if(!CollectionUtil.isEmpty(testeeVariableRecordList)) {
                session.insert("batchInsertTesteeRecord", testeeVariableRecordList);
                session.commit();
            }
            long spendTime = System.currentTimeMillis()-startTime;
            System.out.println("成功插入 30 万条数据,耗时："+spendTime+"毫秒");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            session.close();
        }
        // projectTesteeResultMapper.saveTesteeVariableRecord();
    }

    @Override
    public List<ProjectTesteeResultVo> getProjectTesteeGroupResultList(Long projectId, Long visitId, Long formId, Long formDetailId, Long testeeId) {
        return projectTesteeResultMapper.getProjectTesteeGroupResultList(projectId,visitId,formId,formDetailId,testeeId);
    }

    @Override
    public List<Map<String, Object>> getResearchTesteeResultList(String projectId, String testeeId) {
        return projectTesteeResultMapper.getResearchTesteeResultList(projectId, testeeId);
    }

    @Override
    public List<ProjectTesteeResult> getProjectTesteeVisitTimeResult(String projectId, String visitId) {
        ProjectTesteeResultExample projectTesteeResultExample = new ProjectTesteeResultExample();
        ProjectTesteeResultExample.Criteria criteria = projectTesteeResultExample.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andLabelEqualTo(BusinessConfig.PROJECT_LABEL_VISIT_NAME);
        return projectTesteeResultMapper.selectByExample(projectTesteeResultExample);
    }

    @Override
    public ProjectTesteeFormVariableComplateResultVo getProjectTesteeFormVariableIdComplateCount(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId) {
        return projectTesteeResultMapper.getProjectTesteeFormVariableIdComplateCount(projectId, projectOrgId, planId, visitId, formId, testeeId);
    }

    @Override
    public ProjectTesteeFormVariableComplateResultVo getProjectTesteeTableVariableIdComplateCount(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId) {
        return projectTesteeResultMapper.getProjectTesteeTableVariableIdComplateCount(projectId, projectOrgId, planId, visitId, formId, testeeId);
    }

    @Override
    public ProjectTesteeResult getLastTesteeVisitFollowTime(String projectId, String visitId, String formId, String followVisitViewName, String testeeId) {
        return projectTesteeResultMapper.getLastTesteeVisitFollowTime(projectId, visitId, formId, followVisitViewName, testeeId);
    }

    @Override
    public ProjectTesteeResult getTesteeFormDetailResult(Long testeeResultId) {
        return projectTesteeResultMapper.selectByPrimaryKey(testeeResultId);
    }

    @Override
    public List<ProjectTesteeResult> getTesteeAllFormResults(String projectId, String testeeId) {
        ProjectTesteeResultExample example = new ProjectTesteeResultExample();
        ProjectTesteeResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);

        // 按访视、表单、字段排序，便于后续处理
        example.setOrderByClause("visit_id, form_id, form_detail_id");

        return projectTesteeResultMapper.selectByExample(example);
    }
}
