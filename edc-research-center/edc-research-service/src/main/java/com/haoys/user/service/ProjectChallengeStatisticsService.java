package com.haoys.user.service;


import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.project.ProjectChallengeStatisticsParam;
import com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo;

/**
 * 质疑统计service
 */
public interface ProjectChallengeStatisticsService {

    /**
     * 获取质疑统计列表
     * @param param 搜索参数
     * @return 分页；列表
     */
    CommonPage<ProjectChallengeStatisticsVo> list(ProjectChallengeStatisticsParam param);

    /**
     * 质疑统计柱状图
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    ProjectChallengeStatisticsVo challengeChart(ProjectChallengeStatisticsParam param);
    /**
     * 角色质疑统计柱状图
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    ProjectChallengeStatisticsVo challengeRoleChart(ProjectChallengeStatisticsParam param);
}
