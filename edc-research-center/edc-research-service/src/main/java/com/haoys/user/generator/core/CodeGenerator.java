package com.haoys.user.generator.core;

import lombok.extern.slf4j.Slf4j;
import com.haoys.user.generator.config.GeneratorConfig;
import com.haoys.user.generator.metadata.DatabaseMetadataReader;
import com.haoys.user.generator.model.TableInfo;
import com.haoys.user.generator.template.TemplateEngine;

import java.io.File;
import java.sql.SQLException;

/**
 * 核心代码生成器
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
public class CodeGenerator {
    
    private final GeneratorConfig config;
    private final DatabaseMetadataReader metadataReader;
    private final TemplateEngine templateEngine;
    
    public CodeGenerator(GeneratorConfig config) {
        this.config = config;
        this.metadataReader = new DatabaseMetadataReader(config);
        this.templateEngine = new TemplateEngine(config);
    }
    
    /**
     * 执行代码生成
     */
    public void generate() throws Exception {
        log.info("开始生成代码...");
        
        // 1. 读取表信息
        TableInfo tableInfo = readTableInfo();
        
        // 2. 创建输出目录
        createOutputDirectories();
        
        // 3. 生成各种文件
        generateFiles(tableInfo);
        
        log.info("代码生成完成！");
    }
    
    /**
     * 读取表信息
     */
    private TableInfo readTableInfo() throws SQLException {
        log.info("读取表信息: {}", config.getTableName());
        return metadataReader.readTableInfo(config.getTableName());
    }
    
    /**
     * 创建输出目录
     */
    private void createOutputDirectories() {
        log.info("创建输出目录...");

        createDirectory(config.getModelPath());
        createDirectory(config.getMapperPath());
        createDirectory(config.getMapperXmlPath());
        createDirectory(config.getServicePath());
        createDirectory(config.getServiceImplPath());
        // 使用自定义的controllerPath
        if (config.getControllerPath() != null && !config.getControllerPath().isEmpty()) {
            createDirectory(config.getControllerPath());
        } else {
            createDirectory(config.getControllerPath());
        }
        createDirectory(config.getTestClassPath());

        log.info("输出目录创建完成");
    }
    
    /**
     * 创建目录
     */
    private void createDirectory(String path) {
        File directory = new File(path);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                log.debug("创建目录: {}", path);
            } else {
                log.warn("创建目录失败: {}", path);
            }
        }
    }
    
    /**
     * 生成各种文件
     */
    private void generateFiles(TableInfo tableInfo) throws Exception {
        log.info("生成代码文件...");
        
        // 1. 生成实体类
        generateEntity(tableInfo);
        
        // 2. 生成Mapper接口
        generateMapper(tableInfo);
        
        // 3. 生成Mapper XML
        generateMapperXml(tableInfo);
        
        // 4. 生成Service接口
        generateService(tableInfo);
        
        // 5. 生成Service实现类
        generateServiceImpl(tableInfo);
        
        // 6. 生成Controller
        generateController(tableInfo);
        
        // 7. 生成测试类
        generateTest(tableInfo);
        
        log.info("代码文件生成完成");
    }
    
    /**
     * 生成实体类
     */
    private void generateEntity(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + ".java";
        String filePath = config.getModelPath() + "/" + fileName;
        
        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }
        
        log.info("生成实体类: {}", fileName);
        templateEngine.generateEntity(tableInfo, filePath);
    }
    
    /**
     * 生成Mapper接口
     */
    private void generateMapper(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + "Mapper.java";
        String filePath = config.getMapperPath() + "/" + fileName;
        
        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }
        
        log.info("生成Mapper接口: {}", fileName);
        templateEngine.generateMapper(tableInfo, filePath);
    }
    
    /**
     * 生成Mapper XML
     */
    private void generateMapperXml(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + "Mapper.xml";
        String filePath = config.getMapperXmlPath() + "/" + fileName;
        
        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }
        
        log.info("生成Mapper XML: {}", fileName);
        templateEngine.generateMapperXml(tableInfo, filePath);
    }
    
    /**
     * 生成Service接口
     */
    private void generateService(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + "Service.java";
        String filePath = config.getServicePath() + "/" + fileName;
        
        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }
        
        log.info("生成Service接口: {}", fileName);
        templateEngine.generateService(tableInfo, filePath);
    }
    
    /**
     * 生成Service实现类
     */
    private void generateServiceImpl(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + "ServiceImpl.java";
        String filePath = config.getServiceImplPath() + "/" + fileName;
        
        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }
        
        log.info("生成Service实现类: {}", fileName);
        templateEngine.generateServiceImpl(tableInfo, filePath);
    }
    
    /**
     * 生成Controller
     */
    private void generateController(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + "Controller.java";
        // 使用自定义的controllerPath，如果没有设置则使用默认路径
        String filePath;
        if (config.getControllerPath() != null && !config.getControllerPath().isEmpty()) {
            filePath = config.getControllerPath() + "/" + fileName;
        } else {
            filePath = config.getControllerPath() + "/" + fileName;
        }

        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }

        log.info("生成Controller: {}", fileName);
        templateEngine.generateController(tableInfo, filePath);
    }
    
    /**
     * 生成测试类
     */
    private void generateTest(TableInfo tableInfo) throws Exception {
        String fileName = tableInfo.getEntityName() + "ServiceTest.java";
        String filePath = config.getTestClassPath() + "/" + fileName;
        
        if (shouldSkipFile(filePath)) {
            log.info("跳过已存在的文件: {}", fileName);
            return;
        }
        
        log.info("生成测试类: {}", fileName);
        templateEngine.generateTest(tableInfo, filePath);
    }
    
    /**
     * 判断是否应该跳过文件生成
     */
    private boolean shouldSkipFile(String filePath) {
        File file = new File(filePath);
        return file.exists() && !config.isOverwriteFiles();
    }
}
