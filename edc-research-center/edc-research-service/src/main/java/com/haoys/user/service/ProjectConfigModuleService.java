package com.haoys.user.service;

import com.haoys.user.model.ProjectConfigModule;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CommonPage;
import java.util.List;
import java.util.Map;

/**
 * 项目配置分组表Service接口
 * 提供完整的CRUD操作、批量操作、复杂查询、缓存管理等功能
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 * @date 2025-07-11 00:31:50
 */
public interface ProjectConfigModuleService {

    // ========== 基础CRUD操作 ==========

    /**
     * 创建记录
     * @param record 实体对象
     * @return 创建结果
     */
    CommonResult<ProjectConfigModule> create(ProjectConfigModule record);

    /**
     * 根据主键删除
     * @param id 主键
     * @return 删除结果
     */
    CommonResult<Void> deleteById(Long id);

    /**
     * 更新记录
     * @param record 实体对象
     * @return 更新结果
     */
    CommonResult<ProjectConfigModule> update(ProjectConfigModule record);

    /**
     * 根据主键查询
     * @param id 主键
     * @return 查询结果
     */
    CommonResult<ProjectConfigModule> getById(Long id);

    // ========== 批量操作 ==========

    /**
     * 批量创建
     * @param records 实体对象列表
     * @return 创建结果
     */
    CommonResult<List<ProjectConfigModule>> batchCreate(List<ProjectConfigModule> records);

    /**
     * 批量删除
     * @param ids 主键列表
     * @return 删除结果
     */
    CommonResult<Void> batchDeleteByIds(List<Long> ids);

    /**
     * 批量更新
     * @param records 实体对象列表
     * @return 更新结果
     */
    CommonResult<List<ProjectConfigModule>> batchUpdate(List<ProjectConfigModule> records);

    // ========== 复杂查询操作 ==========

    /**
     * 根据条件查询列表
     * @param params 查询参数
     * @return 查询结果
     */
    CommonResult<List<ProjectConfigModule>> listByCondition(Map<String, Object> params);

    /**
     * 根据条件查询单个对象
     * @param params 查询参数
     * @return 查询结果
     */
    CommonResult<ProjectConfigModule> getOneByCondition(Map<String, Object> params);

    /**
     * 根据条件分页查询
     * @param params 查询参数
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页查询结果
     */
    CommonResult<CommonPage<ProjectConfigModule>> pageByCondition(Map<String, Object> params, Integer pageNum, Integer pageSize);

    /**
     * 根据条件统计数量
     * @param params 查询参数
     * @return 统计结果
     */
    CommonResult<Long> countByCondition(Map<String, Object> params);

    /**
     * 多条件AND查询
     * @param conditions 条件映射 (字段名 -> 值)
     * @return 查询结果
     */
    CommonResult<List<ProjectConfigModule>> listByMultipleConditions(Map<String, Object> conditions);

    /**
     * 范围查询
     * @param field 字段名
     * @param startValue 起始值
     * @param endValue 结束值
     * @return 查询结果
     */
    CommonResult<List<ProjectConfigModule>> listByRange(String field, Object startValue, Object endValue);

    /**
     * 模糊查询
     * @param field 字段名
     * @param keyword 关键词
     * @return 查询结果
     */
    CommonResult<List<ProjectConfigModule>> listByLike(String field, String keyword);

    /**
     * IN查询
     * @param field 字段名
     * @param values 值列表
     * @return 查询结果
     */
    CommonResult<List<ProjectConfigModule>> listByIn(String field, List<Object> values);

    /**
     * 总数统计
     * @param params 查询参数
     * @return 统计结果
     */
    CommonResult<Long> countTotal(Map<String, Object> params);

    // ========== 复杂查询操作 ==========

    /**
     * 复杂组合查询
     * 支持多种查询条件的组合：精确匹配、范围查询、模糊查询、IN查询等
     * @param queryParams 查询参数映射
     *        - exactConditions: Map<String, Object> 精确匹配条件
     *        - rangeField: String, startValue: Object, endValue: Object 范围查询
     *        - likeField: String, keyword: String 模糊查询
     *        - inField: String, values: List<Object> IN查询
     * @return 查询结果
     */
    CommonResult<List<ProjectConfigModule>> complexQuery(Map<String, Object> queryParams);

    /**
     * 分页复杂查询
     * @param queryParams 查询参数映射（同complexQuery）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页查询结果
     */
    CommonResult<CommonPage<ProjectConfigModule>> complexPageQuery(Map<String, Object> queryParams, Integer pageNum, Integer pageSize);

    // ========== 数据校验 ==========

    /**
     * 校验数据有效性
     * @param record 实体对象
     * @return 校验结果
     */
    CommonResult<Void> validateData(ProjectConfigModule record);

    /**
     * 检查记录是否存在
     * @param id 主键
     * @return 检查结果
     */
    CommonResult<Boolean> existsById(Long id);

    // ========== 自定义业务方法 ==========
    // 可以在这里添加特定的业务方法
    // 例如：复杂的业务逻辑、数据校验、事务处理等

}
