package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPrescription;
import com.haoys.user.model.ProjectPrescriptionExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPrescriptionMapper {
    long countByExample(ProjectPrescriptionExample example);

    int deleteByExample(ProjectPrescriptionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPrescription record);

    int insertSelective(ProjectPrescription record);

    List<ProjectPrescription> selectByExample(ProjectPrescriptionExample example);

    ProjectPrescription selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPrescription record, @Param("example") ProjectPrescriptionExample example);

    int updateByExample(@Param("record") ProjectPrescription record, @Param("example") ProjectPrescriptionExample example);

    int updateByPrimaryKeySelective(ProjectPrescription record);

    int updateByPrimaryKey(ProjectPrescription record);
}