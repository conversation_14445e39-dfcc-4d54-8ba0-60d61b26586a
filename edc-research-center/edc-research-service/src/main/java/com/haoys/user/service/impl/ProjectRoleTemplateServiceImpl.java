package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.mapper.ProjectRoleMenuMapper;
import com.haoys.user.model.ProjectRole;
import com.haoys.user.model.ProjectRoleExample;
import com.haoys.user.model.ProjectRoleMenu;
import com.haoys.user.model.ProjectRoleMenuExample;
import com.haoys.user.service.ProjectRoleService;
import com.haoys.user.service.ProjectRoleTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ProjectRoleTemplateServiceImpl extends BaseService implements ProjectRoleTemplateService {

    @Autowired
    private ProjectRoleService projectRoleService;
    @Autowired
    private ProjectRoleMenuMapper projectRoleMenuMapper;

    /**
     * 获取项目角色模板
     * @param baesProjectRole
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectRoleVo> getProjectTemplateRoleListForPage(ProjectRole baesProjectRole, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectRoleVo> dataList = new ArrayList<>();
        ProjectRoleExample example = new ProjectRoleExample();
        ProjectRoleExample.Criteria criteria = example.createCriteria();
        criteria.andProjectTemplateEqualTo(true);
        if(baesProjectRole != null){
            if(baesProjectRole.getStatus() != null){
                criteria.andStatusEqualTo(baesProjectRole.getStatus());
            }
            if(baesProjectRole.getId() != null && !"-1".equals(baesProjectRole.getId().toString())){
                criteria.andIdEqualTo(baesProjectRole.getId());
            }
            if(!StringUtils.isEmpty(baesProjectRole.getEnname())){
                criteria.andEnnameLike("%" + baesProjectRole.getEnname() + "%");
            }
        }
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        List<ProjectRole> projectRoleList = projectRoleService.selectByExample(example);
        for (ProjectRole projectRole : projectRoleList) {
            ProjectRoleVo projectRoleVo = new ProjectRoleVo();
            BeanUtils.copyProperties(projectRole, projectRoleVo, new String[]{"id"});
            projectRoleVo.setId(projectRole.getId().toString());
            dataList.add(projectRoleVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    /**
     * 获取角色下拉列表
     * @return
     */
    @Override
    public List<ProjectRole> getRoseList() {
        ProjectRoleExample example = new ProjectRoleExample();
        ProjectRoleExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteria.andProjectTemplateEqualTo(true);
        return projectRoleService.selectByExample(example);
    }

    /**
     * 编辑/复制获取回显数据
     * @param id
     * @return
     */
    @Override
    public ProjectRole getRoseDate(Long id){
        ProjectRole projectRole =projectRoleService.selectByPrimaryKey(id);
        List<String> menuIdList = projectRoleMenuMapper.selectByRoleId(id);
        Long[] arr = new Long[menuIdList.size()];
        for(int i = 0 ; i < menuIdList.size() ; i++){
            arr[i] = Long.valueOf(menuIdList.get(i));
        }
        projectRole.setMenuIds(arr);
        return projectRole;
    }

    /**
     * 添加角色
     * @param projectRole
     * @return
     */
    @Override
    public int saveProjectTemplateRole(ProjectRole projectRole) {
        projectRole.setId(SnowflakeIdWorker.getUuid());
        projectRole.setCreateTime(new Date());
        projectRole.setProjectTemplate(true);
        projectRole.setTenantId(SecurityUtils.getSystemTenantId());
        projectRole.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectRoleService.insertSelective(projectRole);
        return insertRoleMenu(projectRole);
    }

    /**
     * 编辑角色
     * @param id
     * @param projectRole
     * @return
     */
    @Override
    public int updateProjectRole(Long id, ProjectRole projectRole) {
        projectRole.setId(id);
        projectRole.setTenantId(SecurityUtils.getSystemTenantId());
        projectRole.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectRole.setUpdateTime(new Date());
        projectRoleService.updateByPrimaryKeySelective(projectRole);
        //先删除原有关系
        ProjectRoleMenuExample exampleParam = new ProjectRoleMenuExample();
        exampleParam.createCriteria().andRoleIdEqualTo(id);
        projectRoleMenuMapper.deleteByExample(exampleParam);
        return insertRoleMenu(projectRole);
    }

    /**
     * 删除角色
     * @param ids
     * @return
     */
    @Override
    public int deleteProjectRole(List<Long> ids) {
        ProjectRoleExample example = new ProjectRoleExample();
        example.createCriteria().andIdIn(ids);
        int count = projectRoleService.deleteByExample(example);
        //先删除原有关系
        ProjectRoleMenuExample exampleParam = new ProjectRoleMenuExample();
        exampleParam.createCriteria().andRoleIdIn(ids);
        projectRoleMenuMapper.deleteByExample(exampleParam);
        return count;
    }

    /**
     * 修改角色状态
     * @param id
     * @return
     */
    @Override
    public int editStatus(Long id){
        ProjectRole projectRole = projectRoleService.selectByPrimaryKey(id);
        if (projectRole.getStatus() == 0) {
            projectRole.setStatus(BusinessConfig.DISABLED_STATUS);
        } else {
            projectRole.setStatus(BusinessConfig.ENABLED_STATUS);
        }

        return projectRoleService.updateByPrimaryKeySelective(projectRole);
    }

    /**
     * 角色复制
     * @param id
     * @return
     */
    @Override
    public int copyProjectRole(Long id){
        //获取角色信息
        ProjectRole projectRole = projectRoleService.selectByPrimaryKey(id);
        ProjectRoleMenuExample example = new ProjectRoleMenuExample();
        ProjectRoleMenuExample.Criteria criteria = example.createCriteria();
        criteria.andRoleIdEqualTo(id);
        //获取角色菜单
        List<ProjectRoleMenu>  projectRoleMenuList = projectRoleMenuMapper.selectByExample(example);
        //复制
        projectRole.setId(null);
        int  num = projectRoleService.insertSelective(projectRole);
        if(!projectRoleMenuList.isEmpty()){
            Long[] menuIds = new Long[projectRoleMenuList.size()];
            for(int i = 0 ; i < projectRoleMenuList.size() ; i++){
                menuIds[i] = projectRoleMenuList.get(i).getMenuId();
            }
            projectRole.setMenuIds(menuIds);
            insertRoleMenu(projectRole);
        }
        return num;
    }
    /**
     * 新增角色菜单信息
     */
    public int insertRoleMenu(ProjectRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<ProjectRoleMenu> list = new ArrayList<>();
        for (Long menuId : role.getMenuIds()) {
            ProjectRoleMenu rm = new ProjectRoleMenu();
            rm.setRoleId(role.getId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0) {
            for(int i = 0 ; i < list.size() ; i++){
                rows = projectRoleMenuMapper.insert(list.get(i));
                rows++;
            }
        }
        return list.size();
    }
}
