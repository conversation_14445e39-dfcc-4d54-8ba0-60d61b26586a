package com.haoys.user.generator.model;

import lombok.Data;

/**
 * 列信息模型
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Data
public class ColumnInfo {
    
    /**
     * 列名
     */
    private String columnName;
    
    /**
     * 列注释
     */
    private String columnComment;
    
    /**
     * JDBC类型
     */
    private int jdbcType;
    
    /**
     * JDBC类型名称
     */
    private String jdbcTypeName;
    
    /**
     * Java类型
     */
    private String javaType;
    
    /**
     * Java属性名
     */
    private String javaProperty;
    
    /**
     * 列大小
     */
    private int columnSize;
    
    /**
     * 小数位数
     */
    private int decimalDigits;
    
    /**
     * 是否可为空
     */
    private boolean nullable;
    
    /**
     * 默认值
     */
    private String defaultValue;
    
    /**
     * 是否自动递增
     */
    private boolean autoIncrement;
    
    /**
     * 是否为BLOB类型
     */
    private boolean blobType;
    
    /**
     * 获取Java属性的首字母大写形式（用于getter/setter方法名）
     */
    public String getJavaPropertyCapitalized() {
        if (javaProperty == null || javaProperty.isEmpty()) {
            return javaProperty;
        }
        return Character.toUpperCase(javaProperty.charAt(0)) + javaProperty.substring(1);
    }
    
    /**
     * 获取MyBatis的jdbcType
     */
    public String getMybatisJdbcType() {
        switch (jdbcType) {
            case java.sql.Types.TINYINT:
                return "TINYINT";
            case java.sql.Types.SMALLINT:
                return "SMALLINT";
            case java.sql.Types.INTEGER:
                return "INTEGER";
            case java.sql.Types.BIGINT:
                return "BIGINT";
            case java.sql.Types.FLOAT:
                return "FLOAT";
            case java.sql.Types.DOUBLE:
            case java.sql.Types.NUMERIC:
            case java.sql.Types.DECIMAL:
                return "DECIMAL";
            case java.sql.Types.BOOLEAN:
            case java.sql.Types.BIT:
                return "BIT";
            case java.sql.Types.CHAR:
                return "CHAR";
            case java.sql.Types.VARCHAR:
                return "VARCHAR";
            case java.sql.Types.LONGVARCHAR:
                return "LONGVARCHAR";
            case java.sql.Types.DATE:
                return "DATE";
            case java.sql.Types.TIME:
                return "TIME";
            case java.sql.Types.TIMESTAMP:
                return "TIMESTAMP";
            case java.sql.Types.BLOB:
                return "BLOB";
            case java.sql.Types.CLOB:
                return "CLOB";
            case java.sql.Types.LONGVARBINARY:
                return "LONGVARBINARY";
            default:
                if ("TEXT".equalsIgnoreCase(jdbcTypeName) || 
                    "LONGTEXT".equalsIgnoreCase(jdbcTypeName) ||
                    "MEDIUMTEXT".equalsIgnoreCase(jdbcTypeName)) {
                    return "VARCHAR";
                }
                return "VARCHAR";
        }
    }
    
    /**
     * 是否需要导入Date类
     */
    public boolean needsDateImport() {
        return "Date".equals(javaType) || "Time".equals(javaType);
    }
    
    /**
     * 是否为字符串类型
     */
    public boolean isStringType() {
        return "String".equals(javaType);
    }
    
    /**
     * 是否为数字类型
     */
    public boolean isNumberType() {
        return "Byte".equals(javaType) || 
               "Short".equals(javaType) || 
               "Integer".equals(javaType) || 
               "Long".equals(javaType) || 
               "Float".equals(javaType) || 
               "Double".equals(javaType);
    }
    
    /**
     * 是否为布尔类型
     */
    public boolean isBooleanType() {
        return "Boolean".equals(javaType);
    }
    
    /**
     * 获取列的描述信息
     */
    public String getColumnDescription() {
        if (columnComment != null && !columnComment.trim().isEmpty()) {
            return columnComment;
        }
        return javaProperty;
    }
    
    /**
     * 获取Swagger注解的value值
     */
    public String getSwaggerValue() {
        String description = getColumnDescription();
        // 转义双引号
        if (description.contains("\"")) {
            description = description.replace("\"", "'");
        }
        return description;
    }
}
