package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.crf.TemplateFormLogicParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormLogicVo;

import java.util.List;

public interface TemplateFormLogicService {

    /**
     * 保存逻辑显示条件
     * @param templateFormLogicParam
     * @return
     */
    CustomResult saveFormVariableLogicData(TemplateFormLogicParam templateFormLogicParam);

    /**
     * 查询变量逻辑条件设置
     * @param templateId
     * @param projectId     项目id
     * @param visitId       访视id
     * @param formId        表单项id
     * @param formDetailId  表单项详情id
     * @return
     */
    List<TemplateFormLogicVo> getFormVariableLogicList(String templateId, String projectId, String visitId, String formId, String formDetailId, String testeeId);

    /**
     * 根据模板templateId查询变量逻辑条件集合
     * @param templateId
     * @return
     */
    List<TemplateFormLogicVo> getFormLogicConfigListByTemplateId(String templateId, String formId, String detailId);

    /**
     * 删除表单逻辑条件设置
     * @param id
     * @return
     */
    CustomResult deleteFormLogicData(String id);
}
