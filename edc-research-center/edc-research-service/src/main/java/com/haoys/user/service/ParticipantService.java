package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.participant.ProjectParticipantViewConfigParam;
import com.haoys.user.domain.vo.participant.ProjectParticipantViewConfigVo;

public interface ParticipantService {

    /**
     * 查询参与者基本扩展信息
     * @param projectId
     * @param id
     * @return
     */
    ProjectParticipantViewConfigVo getParticipantBaseInfo(String projectId, String id);

    /**
     * 保存参与者基本配置信息
     * @param projectParticipantViewConfigParam
     * @return
     */
    CustomResult saveProjectParticipantViewConfig(ProjectParticipantViewConfigParam projectParticipantViewConfigParam);

    /**
     * 查询参与者编号是否存在
     * @param projectId
     * @param projectOrgId
     * @param testeeCode
     * @param testeeId
     * @return
     */
    CommonResult getProjectTesteeCodeResult(String projectId, String projectOrgId, String testeeCode, String testeeId);

}
