package com.haoys.user.mapper;

import com.haoys.user.model.ProjectFormAudit;
import com.haoys.user.model.ProjectFormAuditExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectFormAuditMapper {
    long countByExample(ProjectFormAuditExample example);

    int deleteByExample(ProjectFormAuditExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectFormAudit record);

    int insertSelective(ProjectFormAudit record);

    List<ProjectFormAudit> selectByExample(ProjectFormAuditExample example);

    ProjectFormAudit selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectFormAudit record, @Param("example") ProjectFormAuditExample example);

    int updateByExample(@Param("record") ProjectFormAudit record, @Param("example") ProjectFormAuditExample example);

    int updateByPrimaryKeySelective(ProjectFormAudit record);

    int updateByPrimaryKey(ProjectFormAudit record);
}
