package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.project.ProjectCheckDataParam;
import com.haoys.user.domain.param.project.ProjectCheckQueryParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeCheckVo;

public interface ProjectTesteeCheckService {

    CommonPage<ProjectTesteeCheckVo> getProjectCheckListForPage(ProjectCheckQueryParam projectCheckQueryParam);

    String saveProjectCheckRequestLog(ProjectCheckDataParam projectCheckDataParam);

    void updateProjectCheckLogForBase(String projectId, String visitId, String formId, String testeeId, boolean tanantAdmin);
}
