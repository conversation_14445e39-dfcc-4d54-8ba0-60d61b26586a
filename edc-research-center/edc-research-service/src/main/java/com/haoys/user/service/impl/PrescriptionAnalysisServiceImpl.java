package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.SendHttpRequestHelper;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.PrescriptionAnalysisParam;
import com.haoys.user.domain.vo.PrescriptionAnalysisVo;
import com.haoys.user.domain.vo.ProjectAnalysisRecordVo;
import com.haoys.user.mapper.ProjectAnalysisCodeMapper;
import com.haoys.user.mapper.ProjectAnalysisRecordMapper;
import com.haoys.user.mapper.ProjectPharmacopeiaMapper;
import com.haoys.user.mapper.ProjectPrescriptionMapper;
import com.haoys.user.mapper.ProjectPrescriptionMixMapper;
import com.haoys.user.mapper.ProjectPropertyTasteMapper;
import com.haoys.user.mapper.ProjectRefinementMapper;
import com.haoys.user.mapper.ProjectTasteStatMapper;
import com.haoys.user.model.ProjectAnalysisCode;
import com.haoys.user.model.ProjectAnalysisCodeExample;
import com.haoys.user.model.ProjectAnalysisRecord;
import com.haoys.user.model.ProjectPharmacopeia;
import com.haoys.user.model.ProjectPharmacopeiaExample;
import com.haoys.user.model.ProjectPrescriptionMix;
import com.haoys.user.model.ProjectPrescriptionMixExample;
import com.haoys.user.model.ProjectPropertyTaste;
import com.haoys.user.model.ProjectPropertyTasteExample;
import com.haoys.user.model.ProjectRefinement;
import com.haoys.user.model.ProjectRefinementExample;
import com.haoys.user.model.ProjectTasteStat;
import com.haoys.user.model.ProjectTasteStatExample;
import com.haoys.user.participle.BoxPlot;
import com.haoys.user.participle.BoxPlotDrawHandler;
//import com.haoys.mis.participle.OCRParticipleUtil;
import com.haoys.user.service.PrescriptionAnalysisService;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class PrescriptionAnalysisServiceImpl extends BaseService implements PrescriptionAnalysisService {

    @Autowired
    private ProjectAnalysisCodeMapper projectAnalysisCodeMapper;
    @Autowired
    private ProjectAnalysisRecordMapper projectAnalysisRecordMapper;
    @Autowired
    private ProjectPharmacopeiaMapper projectPharmacopeiaMapper;
    @Autowired
    private ProjectPrescriptionMapper projectPrescriptionMapper;

    @Autowired
    private ProjectPrescriptionMixMapper projectPrescriptionMixMapper;
    @Autowired
    private ProjectPropertyTasteMapper projectPropertyTasteMapper;
    @Autowired
    private ProjectTasteStatMapper projectTasteStatMapper;
    @Autowired
    private ProjectRefinementMapper projectRefinementMapper;

    private final String MEDICINAL_PROPERTY = "甘,苦,辛,酸,咸,涩,淡";
    private final String MEDICINAL_TASTE = "寒,热,温,凉,平";
    private final String MEDICINAL_TASTE1 = "胃,脾,大肠,肾,肺,心,肝,膀胱,胆,三焦,小肠,心包";



    @Override
    public CommonPage<PrescriptionAnalysisVo> getPrescriptionAnalysisForPage(String projectId, String name, String userId, Integer pageNum, Integer pageSize) {
        List<PrescriptionAnalysisVo> dataList = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectAnalysisCodeExample example = new ProjectAnalysisCodeExample();
        ProjectAnalysisCodeExample.Criteria criteria = example.createCriteria();
        criteria.andPlatformIdEqualTo(projectId);
        if(StringUtils.isNotEmpty(name)){
            criteria.andTitleLike(name);
        }
        criteria.andSealFlagEqualTo(false);
        example.setOrderByClause("create_time desc");
        List<ProjectAnalysisCode> projectAnalysisCodeList = projectAnalysisCodeMapper.selectByExample(example);
        for (ProjectAnalysisCode projectAnalysisCode : projectAnalysisCodeList) {
            PrescriptionAnalysisVo prescriptionAnalysisVo = new PrescriptionAnalysisVo();
            BeanUtils.copyProperties(projectAnalysisCode, prescriptionAnalysisVo);
            dataList.add(prescriptionAnalysisVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }


    public CommonPage<ProjectAnalysisRecordVo> getProjectAnalysisRecordForPage(String batchCode, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectAnalysisRecordVo> projectAnalysisRecordList = projectAnalysisRecordMapper.getProjectAnalysisRecordForPage(batchCode);
        return commonPageListWrapper(pageNum, pageSize, page, projectAnalysisRecordList);
    }

    @Override
    public CustomResult savePrescriptionAnalysis(PrescriptionAnalysisParam prescriptionAnalysisParam) {
        //保存接口返回的药方分析结果信息
        CustomResult customResult = new CustomResult();
        ProjectAnalysisCode projectAnalysisCode = new ProjectAnalysisCode();
        BeanUtils.copyProperties(prescriptionAnalysisParam, projectAnalysisCode);
        projectAnalysisCode.setId(SnowflakeIdWorker.getUuid());
        projectAnalysisCode.setBatchCode(DateUtil.formatDate2String(new Date(),DateUtil.SMSDATE) + RandomStringUtils.randomNumeric(8));
        projectAnalysisCode.setSealFlag(false);
        if(prescriptionAnalysisParam.getSealFlag() != null){
            if(prescriptionAnalysisParam.getId() != null){
                ProjectAnalysisCode projectAnalysisCodeVo = projectAnalysisCodeMapper.selectByPrimaryKey(prescriptionAnalysisParam.getId());
                if(projectAnalysisCodeVo != null){
                    projectAnalysisCodeVo.setSealFlag(prescriptionAnalysisParam.getSealFlag());
                    projectAnalysisCodeMapper.updateByPrimaryKey(projectAnalysisCodeVo);
                    return customResult;
                }
            }
        }
        projectAnalysisCode.setCreateTime(new Date());
        int count = projectAnalysisCodeMapper.insertSelective(projectAnalysisCode);
        if(count >0){
            List<PrescriptionAnalysisParam.ProjectAnalysisRecordParam> sampleList = prescriptionAnalysisParam.getSampleList();
            for (PrescriptionAnalysisParam.ProjectAnalysisRecordParam projectAnalysisRecordParam : sampleList) {
                ProjectAnalysisRecord projectAnalysisRecord = new ProjectAnalysisRecord();
                BeanUtils.copyProperties(projectAnalysisRecordParam, projectAnalysisRecord);
                projectAnalysisRecord.setId(SnowflakeIdWorker.getUuid());
                projectAnalysisRecord.setCreateTime(new Date());
                projectAnalysisRecord.setBatchCode(projectAnalysisCode.getBatchCode());
                projectAnalysisRecordMapper.insertSelective(projectAnalysisRecord);
            }
        }
        return customResult;
    }

    @Override
    public List<Map<Integer, Object>> queryAgeLineAnalysis(String batchCode) {
        //Map<Integer, Object> dataMap = new HashMap<>();
        List<Map<Integer, Object>> dataList = projectAnalysisRecordMapper.queryAgeLineAnalysis(batchCode);
        return dataList;
    }

    @Override
    public BoxPlot queryAgeBoxPlotAnalysis(String batchCode) {
        List<Double> dataList = new ArrayList<>();
        List<ProjectAnalysisRecordVo> projectAnalysisRecordList = projectAnalysisRecordMapper.getProjectAnalysisRecordForPage(batchCode);
        for (ProjectAnalysisRecordVo projectAnalysisRecordVo : projectAnalysisRecordList) {
            if(projectAnalysisRecordVo.getAge() != null){
                dataList.add(Double.parseDouble(projectAnalysisRecordVo.getAge().toString()));
            }
        }
        double[] params = new double[dataList.size()];
        for (int i = 0; i < params.length; i++) {
            params[i] = dataList.get(i);
        }
        //Double[] value = (Double[])dataList.toArray(new Double[dataList.size()]);
        BoxPlot plot = BoxPlotDrawHandler.plot(params);
        return plot;
    }

    @Override
    public List<Map.Entry<String, Integer>> querySymptomCount(String batchCode) {
        List<ProjectAnalysisRecordVo> projectAnalysisRecordList = projectAnalysisRecordMapper.getProjectAnalysisRecordForPage(batchCode);
        String content = projectAnalysisRecordList.stream().map(tempVo -> tempVo.getDiagnosticDesc()).collect(Collectors.joining(","));
        return null;
//        return OCRParticipleUtil.querySymptomAndMedicineCount(content);
    }

    @Override
    public List<Map.Entry<String, Integer>> queryMedicineCount(String batchCode) {
        List<ProjectAnalysisRecordVo> projectAnalysisRecordList = projectAnalysisRecordMapper.getProjectAnalysisRecordForPage(batchCode);
        String content = projectAnalysisRecordList.stream().map(tempVo -> tempVo.getContent()).collect(Collectors.joining(","));
//        return OCRParticipleUtil.querySymptomAndMedicineCount(content);
        return null;
    }

    @Override
    public CommonPage<ProjectPrescriptionMix> queryPrescriptionCombinationForPage(String batchCode, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectPrescriptionMixExample example = new ProjectPrescriptionMixExample();
        ProjectPrescriptionMixExample.Criteria criteria = example.createCriteria();
        criteria.andBatchCodeEqualTo(batchCode);
        List<ProjectPrescriptionMix> projectPrescriptionMixList = projectPrescriptionMixMapper.selectByExampleWithBLOBs(example);
        return commonPageListWrapper(pageNum, pageSize, page, projectPrescriptionMixList);
    }

    @Override
    public void getPrescriptionCombinationRelation(String batchCode) {

    }

    @Override
    public ProjectTasteStat getPrescriptionTasteStat(String batchCode) {
        ProjectTasteStatExample example = new ProjectTasteStatExample();
        ProjectTasteStatExample.Criteria criteria = example.createCriteria();
        criteria.andBatchCodeEqualTo(batchCode);
        List<ProjectTasteStat> projectTasteStatList = projectTasteStatMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtil.isNotEmpty(projectTasteStatList)){
            return projectTasteStatList.get(0);
        }
        return null;
    }

    @Override
    public ProjectPropertyTaste getPrescriptionPropertyTaste(String batchCode) {
        ProjectPropertyTasteExample example = new ProjectPropertyTasteExample();
        ProjectPropertyTasteExample.Criteria criteria = example.createCriteria();
        criteria.andBatchCodeEqualTo(batchCode);
        List<ProjectPropertyTaste> projectPropertyTasteList = projectPropertyTasteMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtil.isNotEmpty(projectPropertyTasteList)){
            return projectPropertyTasteList.get(0);
        }
        return null;
    }

    @Override
    public ProjectRefinement getPrescriptionRefinement(String batchCode) {
        ProjectRefinementExample example = new ProjectRefinementExample();
        ProjectRefinementExample.Criteria criteria = example.createCriteria();
        criteria.andBatchCodeEqualTo(batchCode);
        List<ProjectRefinement> projectRefinementList = projectRefinementMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtil.isNotEmpty(projectRefinementList)){
            return projectRefinementList.get(0);
        }
        return null;
    }

    @Override
    public String updatePythonTask(String requestUrl, String batchCode) {
        final String[] result = {""};
        //创建线程池 newFixedThreadPool(4) 创建一个定长线程池，可控制线程最大并发数为4(根据需求自行修改)
        ExecutorService newFixedThreadPool = Executors.newFixedThreadPool(4);
            newFixedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    String params = "batchCode=" + batchCode;
                    result[0] = SendHttpRequestHelper.sendGet(requestUrl, params);
                }
            });

        return result[0];
    }


    @Override
    public CommonPage<ProjectPharmacopeia> getProjectPharmacopeiaForPage(String name, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectPharmacopeiaExample example = new ProjectPharmacopeiaExample();
        example.setOrderByClause("create_time desc");
        List<ProjectPharmacopeia> projectPharmacopeiaList = projectPharmacopeiaMapper.selectByExample(example);
        for (ProjectPharmacopeia projectPharmacopeia : projectPharmacopeiaList) {
            String natureFlav = projectPharmacopeia.getNatureFlav();
            String returnEssence = projectPharmacopeia.getReturnEssence();
            String[] targetValueArray = MEDICINAL_PROPERTY.split(",");
            String tempValue = "";
            for (String targetValue : targetValueArray) {
                if(natureFlav.contains(targetValue)){
                    tempValue += targetValue.concat(",");
                }
            }
            if(StringUtils.isNotEmpty(tempValue)){
                //projectPharmacopeia.setMedicinalProperty(tempValue.substring(0,tempValue.length()-1));
            }

            System.out.println("getMedicinalProperty-" + projectPharmacopeia.getMedicinalProperty());

            tempValue = "";
            String[] valueArray = MEDICINAL_TASTE.split(",");
            for (String value : valueArray) {
                if(natureFlav.contains(value)){
                    tempValue += value.concat(",");
                }
            }
            if(StringUtils.isNotEmpty(tempValue)){
                //projectPharmacopeia.setMedicinalTaste(tempValue.substring(0,tempValue.length()-1));
            }

            System.out.println("getMedicinalTaste-" + projectPharmacopeia.getMedicinalTaste());


            tempValue = "";
            String[] contentValueArray = MEDICINAL_TASTE1.split(",");
            for (String value : contentValueArray) {
                if(StringUtils.isNotEmpty(returnEssence) && returnEssence.contains(value)){
                    tempValue += value.concat("经").concat(",");
                }
            }
            if(StringUtils.isNotEmpty(tempValue)){
                projectPharmacopeia.setContent(tempValue.substring(0,tempValue.length()-1));
            }

            System.out.println("getContent-" + projectPharmacopeia.getContent());


            projectPharmacopeiaMapper.updateByPrimaryKey(projectPharmacopeia);

        }
        return commonPageListWrapper(pageNum, pageSize, page, projectPharmacopeiaList);
    }
}
