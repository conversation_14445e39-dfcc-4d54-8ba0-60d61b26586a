package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.TemplateFormGroupVariableVo;
import com.haoys.user.model.TemplateFormGroupVariable;
import com.haoys.user.model.TemplateFormGroupVariableExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TemplateFormGroupVariableMapper {
    long countByExample(TemplateFormGroupVariableExample example);

    int deleteByExample(TemplateFormGroupVariableExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormGroupVariable record);

    int insertSelective(TemplateFormGroupVariable record);

    List<TemplateFormGroupVariable> selectByExample(TemplateFormGroupVariableExample example);

    TemplateFormGroupVariable selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormGroupVariable record, @Param("example") TemplateFormGroupVariableExample example);

    int updateByExample(@Param("record") TemplateFormGroupVariable record, @Param("example") TemplateFormGroupVariableExample example);

    int updateByPrimaryKeySelective(TemplateFormGroupVariable record);

    int updateByPrimaryKey(TemplateFormGroupVariable record);

    int deleteTemplateFormGroupVariableByGroupId(@Param("groupId") String groupId, @Param("validStatus") String validStatus, @Param("userId") String userId);

    TemplateFormGroupVariable getTemplateGroupInfoByBaseVariableId(String projectId, String planId, String visitId, String formId, String resourceVariableId, String resourceGroupId, String testeeGroupId, String testeeId);

    List<TemplateFormGroupVariableVo> getProjectTesteeFormGroupListByFormId(String projectId, String planId, String visitId, String formId, String variableGroupTableId, String variableTableId, String testeeId);
}
