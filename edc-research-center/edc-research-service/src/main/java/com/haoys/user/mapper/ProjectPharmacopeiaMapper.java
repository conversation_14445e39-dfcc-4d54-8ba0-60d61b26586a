package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPharmacopeia;
import com.haoys.user.model.ProjectPharmacopeiaExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPharmacopeiaMapper {
    long countByExample(ProjectPharmacopeiaExample example);

    int deleteByExample(ProjectPharmacopeiaExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPharmacopeia record);

    int insertSelective(ProjectPharmacopeia record);

    List<ProjectPharmacopeia> selectByExample(ProjectPharmacopeiaExample example);

    ProjectPharmacopeia selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPharmacopeia record, @Param("example") ProjectPharmacopeiaExample example);

    int updateByExample(@Param("record") ProjectPharmacopeia record, @Param("example") ProjectPharmacopeiaExample example);

    int updateByPrimaryKeySelective(ProjectPharmacopeia record);

    int updateByPrimaryKey(ProjectPharmacopeia record);
}