package com.haoys.user.domain.vo.patient;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.ecrf.TemplateTableRowHeadVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PatientTaskFormVariableVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "已提交记录id-编辑时设置")
    private Long formResultId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单变量id")
    private Long formDetailId;

    @ApiModelProperty(value = "表单变量名称")
    private String label;

    @ApiModelProperty(value = "表单变量key")
    private String key;

    @ApiModelProperty(value = "表单变量类型")
    private String type;

    @ApiModelProperty(value = "表单变量填充数据")
    private String options;

    @ApiModelProperty(value = "表单变量是否隐藏")
    private Boolean hidden;

    @ApiModelProperty(value = "表单变量是否必填")
    private Boolean required;

    @ApiModelProperty(value = "表单提交结果")
    private String result;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "扩展属性-显示逻辑条件集合")
    private String extData1;

    @ApiModelProperty(value = "扩展属性-显示逻辑条件集合")
    private String extData2;

    @ApiModelProperty(value = "表格head定义规则")
    private List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();

    @ApiModelProperty(value = "扩展属性-行记录提交数据")
    private Object rows;

    @ApiModelProperty(value = "表格行记录描述")
    private Object RowDesc;

}
