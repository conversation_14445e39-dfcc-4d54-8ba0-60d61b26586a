package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import com.haoys.user.generator.model.ColumnInfo;
import java.util.Map;

/**
 * 实体类模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class EntityTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String currentDate = (String) context.get("currentDate");
        boolean enableSwagger = (Boolean) context.get("enableSwagger");
        boolean hasDateColumns = (Boolean) context.get("hasDateColumns");
        
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(modelPackage).append(";\n\n");
        
        // 导入语句
        sb.append("import java.io.Serializable;\n");
        if (hasDateColumns) {
            sb.append("import java.util.Date;\n");
        }
        if (enableSwagger) {
            sb.append("import io.swagger.annotations.ApiModel;\n");
            sb.append("import io.swagger.annotations.ApiModelProperty;\n");
        }
        sb.append("import lombok.Data;\n");
        sb.append("import lombok.EqualsAndHashCode;\n");
        sb.append("import lombok.experimental.Accessors;\n\n");
        
        // 类注释
        sb.append("/**\n");
        sb.append(" * ").append(tableInfo.getTableDescription()).append("实体类\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" * @version 2.0.0\n");
        sb.append(" * @since 1.0.0\n");
        sb.append(" * @date ").append(currentDate).append("\n");
        sb.append(" */\n");
        
        // 类注解
        sb.append("@Data\n");
        sb.append("@EqualsAndHashCode(callSuper = false)\n");
        sb.append("@Accessors(chain = true)\n");
        if (enableSwagger) {
            sb.append("@ApiModel(value = \"").append(entityName).append("\", description = \"")
              .append(tableInfo.getTableDescription()).append("\")\n");
        }
        
        // 类声明
        sb.append("public class ").append(entityName).append(" implements Serializable {\n\n");
        
        // 序列化版本号
        sb.append("    private static final long serialVersionUID = 1L;\n\n");
        
        // 字段声明
        for (ColumnInfo column : tableInfo.getColumns()) {
            // 字段注释
            if (enableSwagger) {
                sb.append("    @ApiModelProperty(value = \"").append(column.getSwaggerValue()).append("\"");
                if (!column.isNullable()) {
                    sb.append(", required = true");
                }
                sb.append(")\n");
            } else {
                sb.append("    /**\n");
                sb.append("     * ").append(column.getColumnDescription()).append("\n");
                sb.append("     */\n");
            }
            
            // 字段声明
            sb.append("    private ").append(column.getJavaType()).append(" ")
              .append(column.getJavaProperty()).append(";\n\n");
        }
        
        // 类结束
        sb.append("}\n");
        
        return sb.toString();
    }
}
