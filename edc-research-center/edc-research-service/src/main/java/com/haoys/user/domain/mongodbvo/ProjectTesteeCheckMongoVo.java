package com.haoys.user.domain.mongodbvo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProjectTesteeCheckMongoVo {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "方案id")
    private Long planId;

    @ApiModelProperty(value = "方案名称")
    private String planName;

    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "访视阶段名称")
    private String visitName;

    @ApiModelProperty(value = "表单id")
    private Long formId;

    @ApiModelProperty(value = "表单名称")
    private String formName;

    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;
    
    private Boolean customTestsee = false;

    @ApiModelProperty(value = "表单变量id")
    private Long variableId;

    @ApiModelProperty(value = "表单表格列id")
    private Long variableTableId;

    @ApiModelProperty(value = "表单提交结果")
    private Long variableResultId;

    @ApiModelProperty(value = "变量名称")
    private String variableName;

    @ApiModelProperty(value = "变量类型")
    private String variableType;

    @ApiModelProperty(value = "最新提交记录")
    private String inputValue;
    
    private String fieldText;
    
    private String unitValue;
    
    private String unitText;

    @ApiModelProperty(value = "原始提交记录")
    private String originalValue;

    @ApiModelProperty(value = "操作人角色code")
    private String projectRoleCode;

    @ApiModelProperty(value = "操作类型")
    private String type;

    @ApiModelProperty(value = "稽查轨迹内容")
    private String content;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "数据状态 0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "访问IP")
    private String visitIp;
    
    @ApiModelProperty(value = "数据来源")
    private String dataFrom;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人用户名")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;


}
