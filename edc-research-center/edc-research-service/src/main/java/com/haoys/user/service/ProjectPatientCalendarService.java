package com.haoys.user.service;

import com.haoys.user.domain.vo.patient.PatientTaskCalendarDetailVo;
import com.haoys.user.model.ProjectPatientCalendar;

import java.util.Date;
import java.util.List;

public interface ProjectPatientCalendarService {

    PatientTaskCalendarDetailVo getCurrentWholeMonthTask(String projectId, String testeeId, String planId, String taskDateTime);

    ProjectPatientCalendar getCurrentCalendarPatientTask(String projectId, String planId, String testeeId);

    /**
     * 每日任务记录
     * @param projectId
     * @param planId
     * @param testeeId
     * @param checkTask     是否有推送任务
     */
    void saveCurrentDateCalendarPatientTask(String projectId, String planId, String testeeId, boolean checkTask);

    /**
     * 新增每日任务明细
     * @param projectId
     * @param planId
     * @param testeeId
     * @param checkTask
     */
    void savePatientCalendarTask(String projectId, String planId, String testeeId, boolean checkTask);

    void updatePatientCalendarTask(String projectId, String planId, String testeeId, String complateState);

    PatientTaskCalendarDetailVo getCustomBetweenTimeTask(String projectId, String testeeId, String planId, Date bindTime, Date date);

    List<ProjectPatientCalendar> getAllCalendarPatientTaskList(String projectId);

    /**
     * 修改任务日历信息
     * @param projectPatientCalendar
     */
    void updatePatientCalendarTaskById(ProjectPatientCalendar projectPatientCalendar);

    /**
     * 查询指定任务日期的数据
     * @param projectId
     * @param planId
     * @param testeeId
     * @param taskDate
     * @return
     */
    ProjectPatientCalendar getCurrentCalendarPatientTaskByTaskDate(String projectId, String planId, String testeeId, String taskDate);

    /**
     * 更新指定任务日期表单信息
     * @param projectId
     * @param planId
     * @param testeeId
     * @param code
     * @param taskDate
     */
    void updatePatientCalendarTaskByTaskDate(String projectId, String planId, String testeeId, String code, String taskDate);
}
