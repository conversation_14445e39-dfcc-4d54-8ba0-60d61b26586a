package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectPatientTaskParam;
import com.haoys.user.domain.vo.project.ProjectTaskVo;

import java.util.List;

public interface ProjectTaskManageService {

    /**
     * 项目ePro任务列表
     * @param projectId
     * @param name
     * @param userId
     * @param pageSize
     * @param pageNum
     * @return
     */
    CommonPage<ProjectTaskVo> getProjectTaskListForPage(String projectId, String name, String userId, Integer pageSize, Integer pageNum);

    /**
     * 创建任务
     * @param projectPatientTaskParam
     * @return
     */
    CustomResult saveProjectTask(ProjectPatientTaskParam projectPatientTaskParam);

    /**
     * 查询任务详情
     * @param taskId
     * @return
     */
    ProjectTaskVo getProjectTaskInfo(String taskId);

    /**
     * 删除任务
     * @param taskId
     * @param operatorUserId
     * @return
     */
    CustomResult deleteProjectTask(String taskId, String operatorUserId);

    /**
     * 查询项目任务列表
     * @param projectId
     * @return
     */
    List<ProjectTaskVo> getProjectTaskList(String projectId);

    /**
     * 查询任务变量是否已启用
     * @param projectId
     * @param formDetailId
     * @return
     */
    ProjectTaskVo.ProjectPatientTaskVariableVo getProjectTaskFormDetailEnabled(String projectId, String formDetailId);
}
