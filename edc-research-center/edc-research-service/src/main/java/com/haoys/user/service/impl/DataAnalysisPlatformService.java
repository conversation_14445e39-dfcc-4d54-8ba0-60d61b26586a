package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.io.resource.MultiResource;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.wrapper.OpenAiResultVo;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.common.thirdservice.PlatformBussinessConfig;
import com.haoys.user.common.thirdservice.PlatformConfig;
import com.haoys.user.common.util.*;
import com.haoys.user.config.LLMConfig;
import com.haoys.user.domain.param.ConversationParam;
import com.haoys.user.domain.param.SyncResearchDataExcelParam;
import com.haoys.user.domain.param.SyncResearchDataParam;
import com.haoys.user.domain.param.testee.ProjectTesteeExportParam;
import com.haoys.user.domain.vo.ExportTesteeVo;
import com.haoys.user.domain.vo.QuantitativeAnalysisExcelVo;
import com.haoys.user.domain.vo.QuantitativeAnalysisExcelWrapperVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.model.Project;
import com.haoys.user.model.SystemChatRecord;
import com.haoys.user.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class DataAnalysisPlatformService {

    private final PlatformConfig platformConfig;
    private final SystemUserInfoService systemUserInfoService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final TemplateConfigService templateConfigService;
    private final ProjectTesteeResultService projectTesteeResultService;
    private final SendRequestRecordService sendRequestRecordService;
    private final ProjectBaseManageService projectBaseManageService;
    private final AnalysisPlatformUserRecordService analysisPlatformUserRecordService;
    private final ProjectTesteeExportManageService projectTesteeExportManageService;
    private final SystemChatRecordService systemChatRecordService;
    private final LLMConfig llmConfig;



    public String getResearchRedirectUrl(String userName) {
        String url = platformConfig.getResearch_url() + PlatformBussinessConfig.RESEACHER_REQUEST_REDIRECT_URL;
        Map<String, Object> params = new HashMap<>();
        params.put("username", userName);
        params.put("password", PlatformBussinessConfig.RESEACHER_DEFAULT_PASSWORD);
        String codeUrl = platformConfig.getResearch_url() + PlatformBussinessConfig.RESEACHER_REQUEST_CODE_DATA_URL;
        String codeMap = HttpUtil.get(codeUrl);
        Map<String, Object> codeDataMap = JSON.parseObject(codeMap, new TypeReference<HashMap<String, Object>>() {
        }.getType());
        String code = codeDataMap.get("verifyCode").toString();
        params.put("captcha", code);
        String data = HttpUtil.post(url, params);
        if (StringUtils.isNotBlank(data)) {
            Map<String, Object> dataMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>() {
            }.getType());
            code = dataMap.get("code").toString();
            if ("300".equals(code)) {
                return platformConfig.getResearch_url() + dataMap.get("url").toString();
            }
        }
        return url + "?username=" + userName + "&password=" + PlatformBussinessConfig.RESEACHER_DEFAULT_PASSWORD + "&captcha=" + code;
    }

    /**
     * 查询参与者样本-用于科研数据分析
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param operator
     * @return
     */
    public CustomResult updateSyncResearchData(String projectId, String projectOrgId, String planId, String operator) {
        CustomResult customResult = new CustomResult();
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        String status = "1";
        SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(operator);
        SyncResearchDataParam syncResearchDataParam = new SyncResearchDataParam();
        syncResearchDataParam.setLoginName(systemUserInfoExtendVo.getUsername());
        syncResearchDataParam.setTitleName(projectBaseInfo.getName() + DateUtil.getCurrentdate() +"样本数据");
        syncResearchDataParam.setDataDesc(DateUtil.getCurrenttime() + "样本数据详情");
        List<Map<String, SyncResearchDataParam.TableRowData>> dataMapList = new ArrayList<>();
        //查询参与者列表
        List<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeAnalysisListByProjectId(projectId, planId, projectOrgId);
        List<String> testeeIds = new ArrayList<>();
        projectTesteeList.forEach(testee->{
            testeeIds.add(testee.getId().toString());
        });
        ProjectTesteeExportParam projectTesteeExportParam = new ProjectTesteeExportParam();
        projectTesteeExportParam.setProjectId(projectId);
        projectTesteeExportParam.setOrgId(projectOrgId);
        projectTesteeExportParam.setTesteeIds(testeeIds);
        List<List<ExportTesteeVo>> testeeFormInfo = projectTesteeExportManageService.getSimpleDataAnalysisList(projectTesteeExportParam);
        for (List<ExportTesteeVo> exportTesteeVoList : testeeFormInfo) {
            Map<String, SyncResearchDataParam.TableRowData> dataMap = new HashMap<>();
            for (ExportTesteeVo exportTesteeVo: exportTesteeVoList){
                String fieldLabel = exportTesteeVo.getFieldLabel();
                String fieldName = exportTesteeVo.getFieldName();
                String fieldValue = exportTesteeVo.getFieldValue() == null ? "" : exportTesteeVo.getFieldValue().toString();SyncResearchDataParam.TableRowData tableRowData = new SyncResearchDataParam.TableRowData();
                /*String regRule = "[`\\\\~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%…&*（）——+|{}【】‘；：”“’。，、？']";
                if(StringUtils.isNotEmpty(fieldValue)){
                    fieldValue = fieldValue.replaceAll("\\p{Punct}", "");
                    fieldValue = fieldValue.replaceAll(regRule, "");
                }*/
                tableRowData.setTitleNameZh(fieldLabel);
                tableRowData.setData(fieldValue);
                dataMap.put(fieldName, tableRowData);
            }
            dataMapList.add(dataMap);
        }
        syncResearchDataParam.setDataList(dataMapList);
        String url = platformConfig.getResearch_url() + PlatformBussinessConfig.RESEACHER_REQUEST_SYNC_DATA_URL;
        String params = JSON.toJSONString(syncResearchDataParam);
        String data = SendHttpRequestHelper.doPostJson(url, params);
        Map<String, Object> dataResultMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>(){}.getType());
        if ((Boolean) dataResultMap.get("flag")){status = "0";}
        sendRequestRecordService.saveSendRequestRecord(url, params, data, status, null, operator);
        return customResult;
    }

    public CustomResult updateSyncResearchExcelData(String projectId, String projectOrgId, String planId, String operator) {
        CustomResult customResult = new CustomResult();
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        String status = "1";
        List<Map<String, String>> simpleList = new ArrayList<>();
        SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(operator);
        SyncResearchDataExcelParam syncResearchDataExcelParam = new SyncResearchDataExcelParam();
        syncResearchDataExcelParam.setLoginName(systemUserInfoExtendVo.getUsername());
        syncResearchDataExcelParam.setTitleName(projectBaseInfo.getName() + DateUtil.getCurrentdate() +"数据集");
        syncResearchDataExcelParam.setDataDesc(DateUtil.getCurrenttime() + "数据详情");
        List<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeAnalysisListByProjectId(projectId, planId, projectOrgId);
        if(CollectionUtil.isEmpty(projectTesteeList)){
            customResult.setCode(ResultCode.PROJECT_TESTEE_VARIABLE_VALUE_SYNS_MESSAGE.getCode());
            customResult.setMessage(ResultCode.PROJECT_TESTEE_VARIABLE_VALUE_SYNS_MESSAGE.getMessage());
            return customResult;
        }
        List<String> testeeIds = new ArrayList<>();
        projectTesteeList.forEach(testee->{
            testeeIds.add(testee.getId().toString());
        });
        ProjectTesteeExportParam projectTesteeExportParam = new ProjectTesteeExportParam();
        projectTesteeExportParam.setProjectId(projectId);
        projectTesteeExportParam.setOrgId(projectOrgId);
        projectTesteeExportParam.setTesteeIds(testeeIds);
        List<List<ExportTesteeVo>> testeeFormInfo = projectTesteeExportManageService.getSimpleDataAnalysisList(projectTesteeExportParam);
        for (List<ExportTesteeVo> exportTesteeVoList : testeeFormInfo) {
            Map<String, String> dataMap = new HashMap<>();
            for (ExportTesteeVo exportTesteeVo : exportTesteeVoList) {
                String visitName = "";
                if(StringUtils.isNotEmpty(exportTesteeVo.getVisitId())){
                    ProjectVisitVo projectVisitBaseConfig = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(exportTesteeVo.getVisitId());
                    visitName = projectVisitBaseConfig.getVisitName();
                }
                String fieldLabel = exportTesteeVo.getFieldLabel();
                String fieldValue = exportTesteeVo.getFieldValue() == null ? "" : exportTesteeVo.getFieldValue().toString();
                /*String regRule = "[`\\\\~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%…&*（）——+|{}【】‘；：”“’。，、？']";
                if(StringUtils.isNotEmpty(fieldValue)){
                    fieldValue = fieldValue.replaceAll("\\p{Punct}", "");
                    fieldValue = fieldValue.replaceAll(regRule, "");
                }*/
                if(fieldLabel.length() >= 10){
                    fieldLabel = fieldLabel.substring(0,10);
                }
                dataMap.put(visitName.concat("-").concat(fieldLabel), exportTesteeVo.getFieldValue() == null ? "" : fieldValue);
            }
            simpleList.add(dataMap);
        }
        syncResearchDataExcelParam.setDataList(simpleList);
        String sendUrl = platformConfig.getResearch_url() + PlatformBussinessConfig.RESEACHER_REQUEST_SYNC_DATA_URL;
        String params = JSON.toJSONString(syncResearchDataExcelParam);
        String data = SendHttpRequestHelper.doPostJson(sendUrl, params);
        Map<String, Object> dataResultMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>(){}.getType());
        if ((Boolean) dataResultMap.get("flag")){status = "0";}
        sendRequestRecordService.saveSendRequestRecord(sendUrl, params, data, status, null, operator);
        return customResult;
    }


    /**
     * 同步用户数据
     * @param operator
     * @return
     */
    public CustomResult updateSyncPlatformUserAccount(String operator) {
        CustomResult customResult = new CustomResult();
        String status = "1";
        SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(operator);
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("loginName", systemUserInfoExtendVo.getUsername());
        userMap.put("password", PlatformBussinessConfig.RESEACHER_DEFAULT_PASSWORD);
        userMap.put("userType", platformConfig.getSync_type());
        String params = JSON.toJSONString(userMap);
        String url = platformConfig.getResearch_url() + PlatformBussinessConfig.RESEACHER_REQUEST_SYNC_USER_URL;
        String data = HttpUtil.post(url, params);
        log.info("同步用户数据返回数据：[{}], url:{}, params:{}", data, url, params);
        Map<String, Object> dataMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>(){}.getType());
        Boolean flag = (Boolean) dataMap.get("flag");
        if (flag) {
            status = "0";
            // record system_user_info account_name
            String systemTenantId = SecurityUtils.getSystemTenantId();
            String systemPlatformId = SecurityUtils.getSystemPlatformId();
            analysisPlatformUserRecordService.saveAnalysisPlatformUserAccount(operator, systemUserInfoExtendVo.getUsername(), systemTenantId, systemPlatformId);
        }
        sendRequestRecordService.saveSendRequestRecord(url, params, data, status, systemUserInfoExtendVo.getUsername(), operator);
        return customResult;
    }


    /**
     * 导出诊断统计表
     * @param file
     * @return
     */
    public CustomResult exportDiagnosticQuantitativeAnalysis(MultipartFile file) {
        CustomResult customResult = new CustomResult();
        List<QuantitativeAnalysisExcelVo> dataInfo = new ArrayList<>();
        ExcelUtil<QuantitativeAnalysisExcelVo> excelUtil = new ExcelUtil<>(QuantitativeAnalysisExcelVo.class);
        try {
            List<QuantitativeAnalysisExcelVo> dataList = excelUtil.importExcel(file.getInputStream());
            for (QuantitativeAnalysisExcelVo quantitativeAnalysisExcelVo : dataList) {
                String participleText = quantitativeAnalysisExcelVo.getParticipleText();
                String[] participleTextArray = participleText.split(",");
                quantitativeAnalysisExcelVo = quantitativeAnalysisExcelVoWrapper(quantitativeAnalysisExcelVo, participleTextArray, participleText);
                dataInfo.add(quantitativeAnalysisExcelVo);
            }
            CommonResult commonResult = excelUtil.exportExcel(dataInfo, "诊断统计表", "诊断分析结果");
            customResult.setData(commonResult.getData());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
        return customResult;
    }

    private QuantitativeAnalysisExcelVo quantitativeAnalysisExcelVoWrapper(QuantitativeAnalysisExcelVo quantitativeAnalysisExcelVo, String[] participleTextArray, String participleText) {
        String var1 = quantitativeAnalysisExcelVo.getVar1();
        String var2 = quantitativeAnalysisExcelVo.getVar2();
        String var3 = quantitativeAnalysisExcelVo.getVar3();
        String var4 = quantitativeAnalysisExcelVo.getVar4();
        String var5 = quantitativeAnalysisExcelVo.getVar5();
        String var6 = quantitativeAnalysisExcelVo.getVar6();
        String var7 = quantitativeAnalysisExcelVo.getVar7();
        String var8 = quantitativeAnalysisExcelVo.getVar8();
        String var9 = quantitativeAnalysisExcelVo.getVar9();
        String var10 = quantitativeAnalysisExcelVo.getVar10();
        String var11 = quantitativeAnalysisExcelVo.getVar11();
        String var12 = quantitativeAnalysisExcelVo.getVar12();
        String var13 = quantitativeAnalysisExcelVo.getVar13();
        String var14 = quantitativeAnalysisExcelVo.getVar14();
        String var15 = quantitativeAnalysisExcelVo.getVar15();
        String var16 = quantitativeAnalysisExcelVo.getVar16();
        String var17 = quantitativeAnalysisExcelVo.getVar17();
        String var18 = quantitativeAnalysisExcelVo.getVar18();
        String var19 = quantitativeAnalysisExcelVo.getVar19();
        String var20 = quantitativeAnalysisExcelVo.getVar20();


        for (String value : participleTextArray) {
            if (participleText.contains(var1)) {
                quantitativeAnalysisExcelVo.setVar1("1");
            } else {
                quantitativeAnalysisExcelVo.setVar1("0");
            }

            if (participleText.contains(var2)) {
                quantitativeAnalysisExcelVo.setVar2("1");
            } else {
                quantitativeAnalysisExcelVo.setVar2("0");
            }

            if (participleText.contains(var3)) {
                quantitativeAnalysisExcelVo.setVar3("1");
            } else {
                quantitativeAnalysisExcelVo.setVar3("0");
            }

            if (participleText.contains(var4)) {
                quantitativeAnalysisExcelVo.setVar4("1");
            } else {
                quantitativeAnalysisExcelVo.setVar4("0");
            }

            if (participleText.contains(var5)) {
                quantitativeAnalysisExcelVo.setVar5("1");
            } else {
                quantitativeAnalysisExcelVo.setVar5("0");
            }

            if (participleText.contains(var6)) {
                quantitativeAnalysisExcelVo.setVar6("1");
            } else {
                quantitativeAnalysisExcelVo.setVar6("0");
            }

            if (participleText.contains(var7)) {
                quantitativeAnalysisExcelVo.setVar7("1");
            } else {
                quantitativeAnalysisExcelVo.setVar7("0");
            }

            if (participleText.contains(var8)) {
                quantitativeAnalysisExcelVo.setVar8("1");
            } else {
                quantitativeAnalysisExcelVo.setVar8("0");
            }

            if (participleText.contains(var9)) {
                quantitativeAnalysisExcelVo.setVar9("1");
            } else {
                quantitativeAnalysisExcelVo.setVar9("0");
            }

            if (participleText.contains(var10)) {
                quantitativeAnalysisExcelVo.setVar10("1");
            } else {
                quantitativeAnalysisExcelVo.setVar10("0");
            }

            //************************************************

            if (participleText.contains(var11)) {
                quantitativeAnalysisExcelVo.setVar11("1");
            } else {
                quantitativeAnalysisExcelVo.setVar11("0");
            }

            if (participleText.contains(var12)) {
                quantitativeAnalysisExcelVo.setVar12("1");
            } else {
                quantitativeAnalysisExcelVo.setVar12("0");
            }

            if (participleText.contains(var13)) {
                quantitativeAnalysisExcelVo.setVar13("1");
            } else {
                quantitativeAnalysisExcelVo.setVar13("0");
            }

            if (participleText.contains(var14)) {
                quantitativeAnalysisExcelVo.setVar14("1");
            } else {
                quantitativeAnalysisExcelVo.setVar14("0");
            }

            if (participleText.contains(var15)) {
                quantitativeAnalysisExcelVo.setVar15("1");
            } else {
                quantitativeAnalysisExcelVo.setVar15("0");
            }

            if (participleText.contains(var16)) {
                quantitativeAnalysisExcelVo.setVar16("1");
            } else {
                quantitativeAnalysisExcelVo.setVar16("0");
            }

            if (participleText.contains(var17)) {
                quantitativeAnalysisExcelVo.setVar17("1");
            } else {
                quantitativeAnalysisExcelVo.setVar17("0");
            }

            if (participleText.contains(var18)) {
                quantitativeAnalysisExcelVo.setVar18("1");
            } else {
                quantitativeAnalysisExcelVo.setVar18("0");
            }

            if (participleText.contains(var19)) {
                quantitativeAnalysisExcelVo.setVar19("1");
            } else {
                quantitativeAnalysisExcelVo.setVar19("0");
            }

            if (participleText.contains(var20)) {
                quantitativeAnalysisExcelVo.setVar20("1");
            } else {
                quantitativeAnalysisExcelVo.setVar20("0");
            }
        }
        return quantitativeAnalysisExcelVo;
    }

    public CustomResult exportParticipleAnalysis(MultipartFile file) {
        CustomResult customResult = new CustomResult();
        List<QuantitativeAnalysisExcelWrapperVo> dataInfo = new ArrayList<>();
        ExcelUtil<QuantitativeAnalysisExcelWrapperVo> excelUtil = new ExcelUtil<>(QuantitativeAnalysisExcelWrapperVo.class);
        try {
            List<QuantitativeAnalysisExcelWrapperVo> dataList = excelUtil.importExcel(file.getInputStream());
            for (QuantitativeAnalysisExcelWrapperVo quantitativeAnalysisExcelVo : dataList) {
                String participleText = quantitativeAnalysisExcelVo.getParticipleText();
                quantitativeAnalysisExcelVo = countQuantitativeAnalysisExcel(participleText, quantitativeAnalysisExcelVo);
                dataInfo.add(quantitativeAnalysisExcelVo);
            }
            CommonResult commonResult = excelUtil.exportExcel(dataInfo, "药方统计表", "药方分析结果");
            customResult.setData(commonResult.getData());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
        return customResult;
    }

    private QuantitativeAnalysisExcelWrapperVo countQuantitativeAnalysisExcel(String participleText, QuantitativeAnalysisExcelWrapperVo quantitativeAnalysisExcelVo) {
        List<String> countList = new ArrayList<>();
        String[] valueArray = quantitativeAnalysisExcelVo.getPrescriptionA().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionA("A");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionA("");
        }


        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionB().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionB("B");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionB("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionC().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionC("C");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionC("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionD().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionD("D");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionD("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionE().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionE("E");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionE("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionF().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionF("F");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionF("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionG().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionG("G");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionG("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionH().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionH("H");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionH("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionI().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionI("I");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionI("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionJ().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionJ("J");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionJ("");
        }

        countList.clear();
        valueArray = quantitativeAnalysisExcelVo.getPrescriptionK().split(",");
        for (String value : valueArray) {
            if (participleText.contains(value)) {
                countList.add(value);
            }
        }
        if (countList.size() == valueArray.length) {
            quantitativeAnalysisExcelVo.setPrescriptionK("K");
        }else{
            quantitativeAnalysisExcelVo.setPrescriptionK("");
        }


        return quantitativeAnalysisExcelVo;
    }

    public CustomResult getCalculatorContent(String question) {
        CustomResult customResult = new CustomResult();
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("question", question);
        String params = JSON.toJSONString(paramsMap);
        String data = SendHttpRequestHelper.doPostJson(Constants.CHAT_GPT_OPEN_AI_URL, params);
        Map<String, Object> dataResultMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>(){}.getType());
        /*String status = dataResultMap.get("status").toString();
        if(status.equals(ResultCode.FAILED.getCode()+"")){
            String error = dataResultMap.get("error").toString();
            customResult.setMessage(error);
            customResult.setCode(ResultCode.SUCCESS.getCode());
            return customResult;
        }*/
        String code = dataResultMap.get("code").toString();
        if(code.equals(String.valueOf(ResultCode.SUCCESS.getCode()))){
            OpenAiResultVo openAiResultVo = JSON.parseObject(dataResultMap.get("data").toString(), OpenAiResultVo.class);
            customResult.setData(openAiResultVo.getMessage().getContent());
        }
        return customResult;
    }


    public CustomResult getChatContent(ConversationParam conversationParam) {
        CustomResult customResult = new CustomResult();
        Map<String,Object> dataMap = new HashMap<>();
        String params = JSON.toJSONString(conversationParam);
        JSONObject jsonObject = JSONObject.parseObject(params);
        log.info("request params:{}",jsonObject.toJSONString());
        String result = HttpUtil.post(llmConfig.getChatbot_url(), params);
        dataMap.put("topic", conversationParam.getTopic());
        dataMap.put("result", result);
        customResult.setData(dataMap);
        return customResult;
    }

    public CustomResult getChatContentList(String topic, String modelType, String queryAll) {
        CustomResult customResult = new CustomResult<>();
        List<SystemChatRecord> chatRecordList = systemChatRecordService.getChatContentList(topic, modelType, SecurityUtils.getUserIdValue());
        customResult.setData(chatRecordList);
        return customResult;
    }

    public SystemChatRecord getSystemChatContentByTopic(String topic) {
        SystemChatRecord systemChatRecord = systemChatRecordService.getSystemChatContentByTopic(topic,SecurityUtils.getUserIdValue());
        return systemChatRecord;
    }

    public String saveChatContent(String topic, String promptValue, String modelType, String conversationParam) {
        return systemChatRecordService.saveSystemChatRecord(topic, promptValue, modelType, conversationParam);
    }

    public void removeChatContent(String conversationIds) {
        systemChatRecordService.systemChatRecordService(conversationIds);
    }
    
    public Map<String, Object> uploadAnalysisData(MultipartFile[] multipartFiles, String question) {
        try {
            MultiResource multiResource = new MultiResource(
                    Arrays.stream(multipartFiles)
                            .map(multipartFile -> {
                                try {
                                    //String extName = FileUtil.extName(multipartFile.getOriginalFilename());
                                    //String originalFilename = SnowflakeIdWorker.getUuidValue().concat(String.valueOf(CharUtil.DOT)).concat(extName);
                                    return new InputStreamResource(multipartFile.getInputStream(), multipartFile.getOriginalFilename());
                                } catch (Exception e) {
                                    throw new ServiceException("输入流打开失败");
                                }
                            }).collect(Collectors.toList())
            );
            if(StringUtils.isNotEmpty(question)){
                HttpResponse httpResponse = HttpRequest.post(llmConfig.getData_analysis_url())
                        //.timeout(myConfig.getLongTimeout())
                        .form("file", multiResource)
                        .form("Question", question)
                        .execute();
                return JSON.parseObject(httpResponse.body().toString(), new TypeReference<HashMap<String, Object>>(){}.getType());
            }else{
                HttpResponse httpResponse = HttpRequest.post(llmConfig.getReport_url())
                        .timeout(60000)
                        .form("file", multiResource)
                        .execute();
                String data = httpResponse.body().toString();
                Map<String, Object> dataMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>() {}.getType());
                dataMap.put("viewUrl", llmConfig.getView_url() + dataMap.get("html_path"));
                return dataMap;
            }
        } catch (HttpException e) {
            throw new ServiceException(ResultCode.UNSUPPORTED_UPLOAD_FILE_FORMAT);
        }
    }
    
    public CustomResult getThirdChatContent(Map<String, Object> params) {
        CustomResult customResult = new CustomResult();
        String conversationParam = JSON.toJSONString(params);
        String dataMap = HttpUtil.post(llmConfig.getChatbot_backup_url(), conversationParam);
        Map<String, Object> result = JSON.parseObject(dataMap, new TypeReference<HashMap<String, Object>>() {}.getType());
        customResult.setData(result);
        return customResult;
    }
}
