package com.haoys.user.service.impl;

import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.mapper.SystemTenantUserMapper;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemTenantUserExample;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SystemTenantUserServiceImpl extends BaseService implements SystemTenantUserService {
    
    @Autowired
    private SystemDepartmentService systemDepartmentService;
    @Autowired
    private SystemTenantUserMapper systemTenantUserMapper;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    
    

    @Override
    public SystemTenantUser getSystemTenantUserByUserId(String userId){
       return systemTenantUserMapper.getSystemTenantUserByUserId(userId, SecurityUtils.getSystemTenantId(), SecurityUtils.getSystemPlatformId());
    }
    
    @Override
    public SystemTenantUser getSystemTenantUserByUserId(String userId, String tenantId, String platformId){
        return systemTenantUserMapper.getSystemTenantUserByUserId(userId, tenantId, platformId);
    }

    @Override
    public int updateSystemTenantUser(SystemTenantUser record){
        return systemTenantUserMapper.updateByPrimaryKey(record);
    }

    @Override
    public int insertSystemTenantUser(SystemTenantUser record){
        return systemTenantUserMapper.insert(record);
    }

    @Override
    public List<SystemTenantUser> getSystemTenantUserList(SystemTenantUserExample example) {
        return systemTenantUserMapper.selectByExample(example);
    }

    @Override
    public void deleteSystemTenantUserByPrimaryKey(Long id) {
        systemTenantUserMapper.deleteByPrimaryKey(id);
    }
    
}
