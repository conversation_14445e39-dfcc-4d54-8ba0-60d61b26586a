package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.expand.ProjectVisitUserExpand;
import com.haoys.user.domain.param.project.ProjectTesteeStatisticsParam;
import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo;
import com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeVisitPercentVo;
import com.haoys.user.domain.vo.project.SysDepartmentVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.expand.ProjectUserExpand;
import com.haoys.user.mapper.ProjectTesteeInfoMapper;
import com.haoys.user.mapper.ProjectUserOrgMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.ProjectResearchersInfoService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeStatisticsService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class ProjectTesteeStatisticsServiceImpl  extends BaseService implements ProjectTesteeStatisticsService {

    @Autowired
    private ProjectTesteeInfoMapper projectTesteeInfoMapper;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private SystemDepartmentService systemDepartmentService;
    @Autowired
    private ProjectUserOrgMapper projectUserOrgMapper;
    @Autowired
    private ProjectUserService projectUserService;
    @Autowired
    private ProjectResearchersInfoService projectResearchersInfoService;
    @Autowired
    private FlowFormSetService flowFormSetService;
    
    
    
    /**
     * 获取访视统计列表
     * @param param 搜索参数
     * @return 分页；列表
     */
    @Override
    public CommonPage<ProjectTesteeStatisticsVo> list(ProjectTesteeStatisticsParam param) {
        ProjectUserInfoWrapper projectUserDataVo = projectUserService.getProjectUserRoleInfoByUserId(param.getProjectId(), "", param.getUserId());
        Page<Object> page = null;
        List<ProjectTesteeStatisticsVo> projectCheckList=new ArrayList<>();
        if(projectUserDataVo != null){
            //String ename = projectUserDataVo.getRoleCode();
            String userId = param.getUserId();
            /*if(ProjectRoleEnum.PROJECT_PA.getCode().equals(ename) *//*|| "DM".equals(ename)*//*){
                userId=null;
            }*/
            List<String> ids =  projectUserOrgMapper.getOrgIdsByProjectIdAndUserId(param.getProjectId(), userId, "");
            page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
            if (CollectionUtil.isNotEmpty(ids)){
                param.setIds(ids);
                // 获取用户拥有的所属中心的数据
                // 获取参与者和参与者关联的访视信息（一个参与者有多个访视信息）
                projectCheckList = projectTesteeInfoMapper.list(param);
                if (CollectionUtil.isNotEmpty(projectCheckList)) {
                    // 根据参与者获取访视访id集合
                    projectCheckList.forEach(vo -> {
                        // 查询访视进度
                        ProjectTesteeVisitPercentVo projectTesteeVisitPercent = projectTesteeInfoService.getProjectVisitTesteeComplateStatus(vo.getProjectId().toString(), "", "", vo.getVisitId().toString(), vo.getId().toString());
                        if(StringUtils.isNotEmpty(projectTesteeVisitPercent.getComplateVisitStatus())){
                            // 设置访视进度
                            vo.setViewPlan(FormVariableComplateStatus.getValue(projectTesteeVisitPercent.getComplateVisitStatus()));
                        }
                    });
                }
            }
        }
        return commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, projectCheckList);
    }
    
    @Override
    public ProjectTesteeProcessCountVo getProjectTesteeProcessCount(String projectId, String visitId, String createUserId) {
        return projectTesteeInfoMapper.getProjectTesteeProcessCount(projectId, visitId, createUserId);
    }
    
    @Override
    public ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditCount(String projectId, String visitId, String createUserId) {
        return projectTesteeInfoMapper.getProjectTesteeFormAuditCount(projectId, visitId, createUserId);
    }
    
    @Override
    public int getProjectTesteeCount(String projectId, String createUserId) {
        return projectTesteeInfoMapper.getProjectTesteeInputCount(projectId, createUserId);
    }
    
    /**
     * @param projectVisitConfigList
     * @param projectId
     * @param enterpriseId
     * @param group
     * @param realName
     * @param userName
     * @param status
     * @param activeStatus
     * @param lockStatus
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectUserExpand> getProjectUserExpandListForPage(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String enterpriseId, String group, String realName, String userName, Integer status, Boolean activeStatus, Boolean lockStatus, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        SystemUserInfoParam systemUserInfoParam = new SystemUserInfoParam();
        
        if(StringUtils.isNotEmpty(userName)){
            systemUserInfoParam.setUsername(userName);
        }
        if(StringUtils.isNotEmpty(realName)){
            systemUserInfoParam.setRealName(realName);
        }
        if(StringUtils.isNotEmpty(enterpriseId)){
            systemUserInfoParam.setEnterprise(enterpriseId);
        }
        if(StringUtils.isNotEmpty(group)){
            systemUserInfoParam.setRoleDesc(group);
        }
        if (activeStatus != null) {
            systemUserInfoParam.setActiveStatus(activeStatus);
        }
        if (lockStatus != null) {
            if (lockStatus) {
                systemUserInfoParam.setLockStatus(lockStatus);
            }
        }
        if (lockStatus == null && status != null) {
            systemUserInfoParam.setStatus(status);
        }
        systemUserInfoParam.setCompanyOwnerUser(false);
        systemUserInfoParam.setRegisterFrom(Constants.USER_TYPE_VALUE_04);
        systemUserInfoParam.setSealFlag(false);
        systemUserInfoParam.setTenantId(SecurityUtils.getSystemTenantId());
        systemUserInfoParam.setPlatformId(SecurityUtils.getSystemPlatformId());
        List<SystemUserInfo> systemUserInfoList = systemUserInfoService.getSystemUserListForPage(systemUserInfoParam);
        List<ProjectUserExpand> projectUserExpandList = new ArrayList<>();
        for (SystemUserInfo systemUserInfo : systemUserInfoList) {
            String createUserId = systemUserInfo.getId().toString();
            ProjectUserExpand projectUserExpand = new ProjectUserExpand();
            BeanUtils.copyProperties(systemUserInfo, projectUserExpand);
            if (StringUtils.isNotEmpty(projectUserExpand.getMobile())) {
                projectUserExpand.setMobile(DesensitizeUtil.aesDecrypt(projectUserExpand.getMobile()));
            }
            
            if(StringUtils.isNotEmpty(systemUserInfo.getRoleDesc())){
                Dictionary dictionaryGroupInfo = dictionaryService.getDictionaryInfo(systemUserInfo.getRoleDesc());
                if(dictionaryGroupInfo != null){
                    projectUserExpand.setGroupName(dictionaryGroupInfo.getName());
                }
            }
            
            if(StringUtils.isNotEmpty(systemUserInfo.getEnterprise())){
                SysDepartmentVo sysDepartmentVo = systemDepartmentService.getDepartment(NumberUtil.parseLong(systemUserInfo.getEnterprise()));
                if(sysDepartmentVo != null){
                    projectUserExpand.setRegionName(sysDepartmentVo.getName());
                }
            }
            
            ProjectResearchersInfo projectResearcherInfo = projectResearchersInfoService.getProjectResearcherInfo(createUserId);
            if(projectResearcherInfo != null){
                com.haoys.user.domain.entity.ProjectResearchersInfo projectResearchersInfo = new com.haoys.user.domain.entity.ProjectResearchersInfo();
                BeanUtils.copyProperties(projectResearcherInfo, projectResearchersInfo);
                projectUserExpand.setProjectResearchersInfo(projectResearchersInfo);
            }
            
            int projectTesteeCount = this.getProjectTesteeCount(projectId, createUserId);
            projectUserExpand.setTestee_count(projectTesteeCount);
            List<ProjectUserExpand.DynamicColumnHeadRowData> dynamicColumnHeadRowList = new ArrayList<>();
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                String visitId = projectVisitConfig.getId().toString();
                
                // 患者统计
                ProjectUserExpand.DynamicColumnHeadRowData dynamicColumnHeadRowData = new ProjectUserExpand.DynamicColumnHeadRowData();
                dynamicColumnHeadRowData.setVisitId(visitId);
                dynamicColumnHeadRowData.setVisitName(projectVisitConfig.getVisitName());
                
                ProjectTesteeProcessCountVo projectTesteeProcessCountVo = this.getProjectTesteeProcessCount(projectId, visitId, createUserId);
                if(projectTesteeProcessCountVo != null){
                    dynamicColumnHeadRowData.setSubmit_count(projectTesteeProcessCountVo.getSubmitCount());
                    dynamicColumnHeadRowData.setTemporary_count(projectTesteeProcessCountVo.getTemporaryCount());
                }
                ProjectTesteeFormAduitCountVo projectTesteeFormAduitCountVo = this.getProjectTesteeFormAuditCount(projectId, visitId, createUserId);
                if(projectTesteeFormAduitCountVo != null){
                    dynamicColumnHeadRowData.setCheck_count(projectTesteeFormAduitCountVo.getCheckCount());
                }
                dynamicColumnHeadRowList.add(dynamicColumnHeadRowData);
            }
            projectUserExpand.setDynamicColumnHeadRowList(dynamicColumnHeadRowList);
            projectUserExpandList.add(projectUserExpand);
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectUserExpandList);
    }
    
    /**
     * @param projectVisitConfigList
     * @param projectId
     * @param testeeCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectUserExpand> getProjectMedicalReviewList(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectVisitUser> projectVisitUserList = projectTesteeInfoService.getProjectUncheckTesteeUserList(projectId, testeeCode);
        List<ProjectUserExpand> projectUserExpandList = new ArrayList<>();
        for (ProjectVisitUser projectVisitUser : projectVisitUserList) {
            ProjectUserExpand projectUserExpand = new ProjectUserExpand();
            String testeeId = projectVisitUser.getTesteeId().toString();
            projectUserExpand.setTesteeCode(projectVisitUser.getTesteeCode());
            projectUserExpand.setTesteeId(testeeId);
            projectUserExpand.setProjectOrgId(projectVisitUser.getOwnerOrgId());
            List<ProjectUserExpand.DynamicColumnHeadRowData> dynamicColumnHeadRowList = new ArrayList<>();
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                String visitId = projectVisitConfig.getId().toString();
                
                // 患者统计
                ProjectUserExpand.DynamicColumnHeadRowData dynamicColumnHeadRowData = new ProjectUserExpand.DynamicColumnHeadRowData();
                dynamicColumnHeadRowData.setVisitId(visitId);
                dynamicColumnHeadRowData.setVisitName(projectVisitConfig.getVisitName());
                
                ProjectTesteeProcessCountVo projectTesteeProcessCountVo = this.getProjectTesteeProcessValue(projectId, visitId, testeeId);
                if(projectTesteeProcessCountVo != null){
                    dynamicColumnHeadRowData.setSubmit_count(projectTesteeProcessCountVo.getSubmitCount());
                }
                ProjectTesteeFormAduitCountVo projectTesteeFormAduitCountVo = this.getProjectTesteeFormAuditValue(projectId, visitId, testeeId);
                if(projectTesteeFormAduitCountVo != null){
                    dynamicColumnHeadRowData.setCheck_count(projectTesteeFormAduitCountVo.getCheckCount());
                }
                dynamicColumnHeadRowList.add(dynamicColumnHeadRowData);
            }
            projectUserExpand.setDynamicColumnHeadRowList(dynamicColumnHeadRowList);
            projectUserExpandList.add(projectUserExpand);
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectUserExpandList);
    }
    
    /**
     * @param projectVisitConfigList
     * @param projectId
     * @param testeeCode
     * @param realName
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public CommonPage<ProjectUserExpand> getProjectTesteeListForBoRui(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, String realName, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectVisitUserExpand> projectVisitUserList = projectTesteeInfoService.getProjectTesteeUserListForBoRui(projectId, testeeCode, realName);
        List<ProjectUserExpand> projectUserExpandList = new ArrayList<>();
        for (ProjectVisitUserExpand projectVisitUser : projectVisitUserList) {
            ProjectUserExpand projectUserExpand = new ProjectUserExpand();
            String testeeId = projectVisitUser.getTesteeId().toString();
            projectUserExpand.setTesteeCode(projectVisitUser.getTesteeCode());
            projectUserExpand.setTesteeId(testeeId);
            projectUserExpand.setAcronym(projectVisitUser.getAcronym());
            projectUserExpand.setProjectOrgId(projectVisitUser.getOwnerOrgId());
            
            projectUserExpand.setArea(projectVisitUser.getArea());
            projectUserExpand.setGroupInfo(projectVisitUser.getGroupInfo());
            projectUserExpand.setHospital(projectVisitUser.getHospital());
            projectUserExpand.setDept(projectVisitUser.getDept());
            projectUserExpand.setRealName(projectVisitUser.getRealName());
            
            
            if(StringUtils.isNotEmpty(projectUserExpand.getGroupInfo())){
                Dictionary dictionaryGroupInfo = dictionaryService.getDictionaryInfo(projectUserExpand.getGroupInfo());
                if(dictionaryGroupInfo != null){
                    projectUserExpand.setGroupName(dictionaryGroupInfo.getName());
                }
            }
            
            SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectVisitUser.getProjectResearchUserId());
            if(systemUserInfoExtendVo != null){
                if(StringUtils.isNotEmpty(systemUserInfoExtendVo.getEnterprise())){
                    SysDepartmentVo sysDepartmentVo = systemDepartmentService.getDepartment(NumberUtil.parseLong(systemUserInfoExtendVo.getEnterprise()));
                    if(sysDepartmentVo != null){
                        projectUserExpand.setRegionName(sysDepartmentVo.getName());
                    }
                }
            }
            
            List<ProjectUserExpand.DynamicColumnHeadRowData> dynamicColumnHeadRowList = new ArrayList<>();
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                String visitId = projectVisitConfig.getId().toString();
                
                // 患者统计
                ProjectUserExpand.DynamicColumnHeadRowData dynamicColumnHeadRowData = new ProjectUserExpand.DynamicColumnHeadRowData();
                dynamicColumnHeadRowData.setVisitId(visitId);
                dynamicColumnHeadRowData.setVisitName(projectVisitConfig.getVisitName());
                List<FlowFormSet> flowFormSetList = flowFormSetService.getProjectVisitListByVisitId(visitId);
                dynamicColumnHeadRowData.setTotal_count(flowFormSetList.size());
                
                ProjectTesteeProcessCountVo projectTesteeProcessCountVo = this.getProjectTesteeProcessValue(projectId, visitId, testeeId);
                if(projectTesteeProcessCountVo != null){
                    dynamicColumnHeadRowData.setSubmit_count(projectTesteeProcessCountVo.getSubmitCount());
                }
                ProjectTesteeFormAduitCountVo projectTesteeFormAduitCountVo = this.getProjectTesteeFormAuditValue(projectId, visitId, testeeId);
                if(projectTesteeFormAduitCountVo != null){
                    dynamicColumnHeadRowData.setCheck_count(projectTesteeFormAduitCountVo.getCheckCount());
                }
                dynamicColumnHeadRowList.add(dynamicColumnHeadRowData);
            }
            projectUserExpand.setDynamicColumnHeadRowList(dynamicColumnHeadRowList);
            projectUserExpandList.add(projectUserExpand);
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectUserExpandList);
    }
    
    public ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditValue(String projectId, String visitId, String testeeId) {
        return projectTesteeInfoMapper.getProjectTesteeFormAuditValue(projectId, visitId, testeeId);
    }
    
    public ProjectTesteeProcessCountVo getProjectTesteeProcessValue(String projectId, String visitId, String testeeId) {
        return projectTesteeInfoMapper.getProjectTesteeProcessValue(projectId, visitId, testeeId);
    }
}
