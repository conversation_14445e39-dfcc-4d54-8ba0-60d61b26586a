package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import java.util.Map;

/**
 * 测试类模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class TestTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String servicePackage = (String) context.get("servicePackage");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String serviceName = (String) context.get("serviceName");
        String entityNameLowerCase = (String) context.get("entityNameLowerCase");
        String primaryKeyType = (String) context.get("primaryKeyType");
        String primaryKeyProperty = (String) context.get("primaryKeyProperty");
        String currentDate = (String) context.get("currentDate");
        
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(servicePackage).append(";\n\n");
        
        // 导入语句
        sb.append("import ").append(modelPackage).append(".").append(entityName).append(";\n");
        sb.append("import com.haoys.user.common.api.CommonResult;\n");
        sb.append("import lombok.extern.slf4j.Slf4j;\n");
        sb.append("import org.junit.jupiter.api.Test;\n");
        sb.append("import org.junit.jupiter.api.BeforeEach;\n");
        sb.append("import org.junit.jupiter.api.DisplayName;\n");
        sb.append("import org.springframework.beans.factory.annotation.Autowired;\n");
        sb.append("import org.springframework.boot.test.context.SpringBootTest;\n");
        sb.append("import org.springframework.test.context.ActiveProfiles;\n");
        sb.append("import org.springframework.transaction.annotation.Transactional;\n");
        sb.append("import java.util.Date;\n");
        sb.append("import java.util.List;\n");
        sb.append("import java.util.Map;\n");
        sb.append("import java.util.HashMap;\n");
        sb.append("import static org.junit.jupiter.api.Assertions.*;\n\n");
        
        // 类注释
        sb.append("/**\n");
        sb.append(" * ").append(tableInfo.getTableDescription()).append("Service测试类\n");
        sb.append(" * 测试完整的CRUD操作、批量操作、复杂查询等功能\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" * @version 2.0.0\n");
        sb.append(" * @since 1.0.0\n");
        sb.append(" * @date ").append(currentDate).append("\n");
        sb.append(" */\n");
        
        // 类注解
        sb.append("@Slf4j\n");
        sb.append("@SpringBootTest\n");
        sb.append("@ActiveProfiles(\"test\")\n");
        sb.append("@Transactional\n");
        
        // 类声明
        sb.append("public class ").append(entityName).append("ServiceTest {\n\n");
        
        // 注入Service
        sb.append("    @Autowired\n");
        sb.append("    private ").append(serviceName).append(" ").append(entityNameLowerCase).append("Service;\n\n");
        
        // 测试数据
        sb.append("    private ").append(entityName).append(" test").append(entityName).append(";\n");
        sb.append("    private ").append(primaryKeyType).append(" test").append(capitalize(primaryKeyProperty)).append(";\n\n");
        
        // 初始化方法
        generateSetupMethod(sb, entityName, entityNameLowerCase, tableInfo);
        
        // 测试方法
        generateTestMethods(sb, entityName, entityNameLowerCase, primaryKeyProperty, tableInfo);
        
        sb.append("}\n");
        
        return sb.toString();
    }
    
    private static void generateSetupMethod(StringBuilder sb, String entityName, String entityNameLowerCase, TableInfo tableInfo) {
        sb.append("    @BeforeEach\n");
        sb.append("    void setUp() {\n");
        sb.append("        // 创建测试数据\n");
        sb.append("        test").append(entityName).append(" = new ").append(entityName).append("();\n");
        
        // 为每个字段设置测试值
        tableInfo.getColumns().forEach(column -> {
            if (!column.isAutoIncrement()) {
                String testValue = generateTestValue(column.getJavaType(), column.getJavaProperty());
                sb.append("        test").append(entityName).append(".set").append(column.getJavaPropertyCapitalized())
                  .append("(").append(testValue).append(");\n");
            }
        });
        
        sb.append("    }\n\n");
    }
    
    private static void generateTestMethods(StringBuilder sb, String entityName, String entityNameLowerCase, 
                                          String primaryKeyProperty, TableInfo tableInfo) {
        
        // 测试创建
        sb.append("    @Test\n");
        sb.append("    @DisplayName(\"测试创建").append(tableInfo.getTableDescription()).append("\")\n");
        sb.append("    void testCreate() {\n");
        sb.append("        log.info(\"开始测试创建").append(tableInfo.getTableDescription()).append("\");\n");
        sb.append("        \n");
        sb.append("        CommonResult<").append(entityName).append("> result = ").append(entityNameLowerCase).append("Service.create(test").append(entityName).append(");\n");
        sb.append("        \n");
        sb.append("        assertNotNull(result);\n");
        sb.append("        assertTrue(result.isSuccess());\n");
        sb.append("        assertNotNull(result.getData());\n");
        sb.append("        assertNotNull(result.getData().get").append(capitalize(primaryKeyProperty)).append("());\n");
        sb.append("        \n");
        sb.append("        test").append(capitalize(primaryKeyProperty)).append(" = result.getData().get").append(capitalize(primaryKeyProperty)).append("();\n");
        sb.append("        log.info(\"创建").append(tableInfo.getTableDescription()).append("成功，ID: {}\", test").append(capitalize(primaryKeyProperty)).append(");\n");
        sb.append("    }\n\n");
        
        // 测试查询
        sb.append("    @Test\n");
        sb.append("    @DisplayName(\"测试查询").append(tableInfo.getTableDescription()).append("\")\n");
        sb.append("    void testGetById() {\n");
        sb.append("        log.info(\"开始测试查询").append(tableInfo.getTableDescription()).append("\");\n");
        sb.append("        \n");
        sb.append("        // 先创建数据\n");
        sb.append("        CommonResult<").append(entityName).append("> createResult = ").append(entityNameLowerCase).append("Service.create(test").append(entityName).append(");\n");
        sb.append("        assertTrue(createResult.isSuccess());\n");
        sb.append("        test").append(capitalize(primaryKeyProperty)).append(" = createResult.getData().get").append(capitalize(primaryKeyProperty)).append("();\n");
        sb.append("        \n");
        sb.append("        // 查询数据\n");
        sb.append("        CommonResult<").append(entityName).append("> result = ").append(entityNameLowerCase).append("Service.getById(test").append(capitalize(primaryKeyProperty)).append(");\n");
        sb.append("        \n");
        sb.append("        assertNotNull(result);\n");
        sb.append("        assertTrue(result.isSuccess());\n");
        sb.append("        assertNotNull(result.getData());\n");
        sb.append("        assertEquals(test").append(capitalize(primaryKeyProperty)).append(", result.getData().get").append(capitalize(primaryKeyProperty)).append("());\n");
        sb.append("        \n");
        sb.append("        log.info(\"查询").append(tableInfo.getTableDescription()).append("成功: {}\", result.getData());\n");
        sb.append("    }\n\n");
        
        // 测试更新
        sb.append("    @Test\n");
        sb.append("    @DisplayName(\"测试更新").append(tableInfo.getTableDescription()).append("\")\n");
        sb.append("    void testUpdate() {\n");
        sb.append("        log.info(\"开始测试更新").append(tableInfo.getTableDescription()).append("\");\n");
        sb.append("        \n");
        sb.append("        // 先创建数据\n");
        sb.append("        CommonResult<").append(entityName).append("> createResult = ").append(entityNameLowerCase).append("Service.create(test").append(entityName).append(");\n");
        sb.append("        assertTrue(createResult.isSuccess());\n");
        sb.append("        ").append(entityName).append(" created = createResult.getData();\n");
        sb.append("        \n");
        sb.append("        // 修改数据（只有当表中存在updateTime字段时才设置）\n");
        // 检查是否有updateTime字段，如果没有则修改其他字段
        boolean hasUpdateTime = tableInfo.getColumns().stream()
                .anyMatch(column -> "updateTime".equals(column.getJavaProperty()));
        if (hasUpdateTime) {
            sb.append("        created.setUpdateTime(new Date());\n");
        } else {
            // 如果没有updateTime字段，修改其他可修改的字段
            tableInfo.getColumns().stream()
                .filter(column -> {
                    boolean isPrimaryKey = tableInfo.getPrimaryKey() != null &&
                                         tableInfo.getPrimaryKey().equals(column.getColumnName());
                    return !isPrimaryKey && !column.isAutoIncrement() &&
                           !"createTime".equals(column.getJavaProperty()) &&
                           !"createUserId".equals(column.getJavaProperty()) &&
                           !"createUser".equals(column.getJavaProperty());
                })
                .findFirst()
                .ifPresent(column -> {
                    String testValue = generateTestValue(column.getJavaType(), column.getJavaProperty());
                    sb.append("        created.set").append(capitalize(column.getJavaProperty()))
                      .append("(").append(testValue).append(");\n");
                });
        }
        sb.append("        \n");
        sb.append("        // 更新数据\n");
        sb.append("        CommonResult<").append(entityName).append("> result = ").append(entityNameLowerCase).append("Service.update(created);\n");
        sb.append("        \n");
        sb.append("        assertNotNull(result);\n");
        sb.append("        assertTrue(result.isSuccess());\n");
        sb.append("        assertNotNull(result.getData());\n");
        sb.append("        \n");
        sb.append("        log.info(\"更新").append(tableInfo.getTableDescription()).append("成功: {}\", result.getData());\n");
        sb.append("    }\n\n");
        
        // 测试删除
        sb.append("    @Test\n");
        sb.append("    @DisplayName(\"测试删除").append(tableInfo.getTableDescription()).append("\")\n");
        sb.append("    void testDelete() {\n");
        sb.append("        log.info(\"开始测试删除").append(tableInfo.getTableDescription()).append("\");\n");
        sb.append("        \n");
        sb.append("        // 先创建数据\n");
        sb.append("        CommonResult<").append(entityName).append("> createResult = ").append(entityNameLowerCase).append("Service.create(test").append(entityName).append(");\n");
        sb.append("        assertTrue(createResult.isSuccess());\n");
        sb.append("        test").append(capitalize(primaryKeyProperty)).append(" = createResult.getData().get").append(capitalize(primaryKeyProperty)).append("();\n");
        sb.append("        \n");
        sb.append("        // 删除数据\n");
        sb.append("        CommonResult<Void> result = ").append(entityNameLowerCase).append("Service.deleteById(test").append(capitalize(primaryKeyProperty)).append(");\n");
        sb.append("        \n");
        sb.append("        assertNotNull(result);\n");
        sb.append("        assertTrue(result.isSuccess());\n");
        sb.append("        \n");
        sb.append("        // 验证删除\n");
        sb.append("        CommonResult<").append(entityName).append("> getResult = ").append(entityNameLowerCase).append("Service.getById(test").append(capitalize(primaryKeyProperty)).append(");\n");
        sb.append("        assertFalse(getResult.isSuccess());\n");
        sb.append("        \n");
        sb.append("        log.info(\"删除").append(tableInfo.getTableDescription()).append("成功\");\n");
        sb.append("    }\n\n");
        
        // 测试条件查询
        sb.append("    @Test\n");
        sb.append("    @DisplayName(\"测试条件查询").append(tableInfo.getTableDescription()).append("\")\n");
        sb.append("    void testListByCondition() {\n");
        sb.append("        log.info(\"开始测试条件查询").append(tableInfo.getTableDescription()).append("\");\n");
        sb.append("        \n");
        sb.append("        // 先创建数据\n");
        sb.append("        CommonResult<").append(entityName).append("> createResult = ").append(entityNameLowerCase).append("Service.create(test").append(entityName).append(");\n");
        sb.append("        assertTrue(createResult.isSuccess());\n");
        sb.append("        \n");
        sb.append("        // 构造查询条件\n");
        sb.append("        Map<String, Object> params = new HashMap<>();\n");
        sb.append("        // params.put(\"字段名\", \"查询值\");\n");
        sb.append("        \n");
        sb.append("        // 条件查询\n");
        sb.append("        CommonResult<List<").append(entityName).append(">> result = ").append(entityNameLowerCase).append("Service.listByCondition(params);\n");
        sb.append("        \n");
        sb.append("        assertNotNull(result);\n");
        sb.append("        assertTrue(result.isSuccess());\n");
        sb.append("        assertNotNull(result.getData());\n");
        sb.append("        \n");
        sb.append("        log.info(\"条件查询").append(tableInfo.getTableDescription()).append("成功，结果数量: {}\", result.getData().size());\n");
        sb.append("    }\n\n");
    }
    
    private static String generateTestValue(String javaType, String propertyName) {
        switch (javaType) {
            case "String":
                return "\"test_" + propertyName + "\"";
            case "Integer":
                return "1";
            case "Long":
                return "1L";
            case "Double":
                return "1.0";
            case "Float":
                return "1.0f";
            case "Boolean":
                return "true";
            case "Date":
                return "new Date()";
            case "Byte":
                return "(byte) 1";
            case "Short":
                return "(short) 1";
            default:
                return "null";
        }
    }
    
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }
}
