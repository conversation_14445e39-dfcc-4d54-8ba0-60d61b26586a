package com.haoys.user.service.impl;


import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.vo.flow.FlowPlanFormVo;
import com.haoys.user.mapper.FlowPlanFormInfoMapper;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.FlowPlanFormInfo;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.service.FlowPlanFormService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.TemplateConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class FlowPlanFormServiceImpl implements FlowPlanFormService {

    private final FlowPlanFormInfoMapper flowPlanFormInfoMapper;
    private final TemplateConfigService templateConfigService;
    private final FlowPlanService flowPlanService;

    @Override
    public CustomResult insertFlowPlanFormInfo(String projectId, String planId, String formId, boolean publishSatus, String createUserId) {
        CustomResult customResult = new CustomResult();
        FlowPlanFormInfo flowPlanFormInfo = flowPlanFormInfoMapper.getFlowPlanFormBaseInfoByPlanIdAndFormId(projectId, planId, formId);
        if(flowPlanFormInfo == null){
            FlowPlanFormInfo planFormInfo = new FlowPlanFormInfo();
            planFormInfo.setId(SnowflakeIdWorker.getUuid());
            planFormInfo.setProjectId(Long.parseLong(projectId));
            planFormInfo.setPlanId(Long.parseLong(planId));
            //设置方案名称
            FlowPlan flowPlan = flowPlanService.getFlowPlanInfoByPlanId(planId);
            planFormInfo.setPlanName(flowPlan.getPlanName());
            planFormInfo.setFormId(Long.parseLong(formId));
            TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(Long.parseLong(formId));
            planFormInfo.setFormName(templateFormConfig.getFormName());
            planFormInfo.setPublishUserId(createUserId);
            planFormInfo.setPublishStatus(publishSatus);
            planFormInfo.setPublishTime(new Date());
            planFormInfo.setCreateTime(new Date());
            planFormInfo.setCreateUserId(createUserId);
            planFormInfo.setStatus(BusinessConfig.VALID_STATUS);
            planFormInfo.setTenantId(SecurityUtils.getSystemTenantId());
            planFormInfo.setPlatformId(SecurityUtils.getSystemPlatformId());
            flowPlanFormInfoMapper.insertSelective(planFormInfo);
        }else{
            flowPlanFormInfo.setUpdateUserId(createUserId);
            flowPlanFormInfo.setUpdateTime(new Date());
            flowPlanFormInfo.setPublishStatus(publishSatus);
            flowPlanFormInfo.setPublishUserId(createUserId);
            flowPlanFormInfo.setPublishTime(new Date());
            // 如果是取消发布记录时间和操作人
            if (publishSatus == false){
                flowPlanFormInfo.setCanclePublishTime(new Date());
                flowPlanFormInfo.setCanclePublishUserId(createUserId);
            }
            flowPlanFormInfoMapper.updateByPrimaryKeySelective(flowPlanFormInfo);
        }
        return customResult;
    }

    @Override
    public CustomResult batchPublishFormTemplateFormConfig(String projectId, String planId, String formIds, String userId) {
        CustomResult customResult = new CustomResult();
        String[] formIdsArray = formIds.split(",");
        for (String formId : formIdsArray) {
            customResult = templateConfigService.savePublishTemplateFormConfig(projectId, planId, formId, true, userId);
            //insertFlowPlanFormInfo(projectId, planId, formId, true, userId);
        }
        return customResult;
    }

    @Override
    public List<FlowPlanFormVo> getFormConfigListByPlanIdAndVisitId(String projectId, String planId, String visitId, String formId) {
        return flowPlanFormInfoMapper.getFormConfigListByPlanIdAndVisitId(projectId, planId, visitId, formId);
    }

    @Override
    public List<FlowPlanFormInfo> getFormConfigListByProjectIdAndFormId(String projectId, String formId) {
        return flowPlanFormInfoMapper.getFormConfigListByProjectIdAndFormId(projectId, formId);
    }
    
    @Override
    public FlowPlanFormInfo getFormConfigListByProjectIdAndFormId(String projectId, String planId, String formId) {
        return flowPlanFormInfoMapper.getFormConfigListByProjectIdAndPlanIdAndFormId(projectId, planId, formId);
    }
}
