package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormDvpVal;
import com.haoys.user.model.TemplateFormDvpValExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormDvpValMapper {
    long countByExample(TemplateFormDvpValExample example);

    int deleteByExample(TemplateFormDvpValExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormDvpVal record);

    int insertSelective(TemplateFormDvpVal record);

    List<TemplateFormDvpVal> selectByExample(TemplateFormDvpValExample example);

    TemplateFormDvpVal selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormDvpVal record, @Param("example") TemplateFormDvpValExample example);

    int updateByExample(@Param("record") TemplateFormDvpVal record, @Param("example") TemplateFormDvpValExample example);

    int updateByPrimaryKeySelective(TemplateFormDvpVal record);

    int updateByPrimaryKey(TemplateFormDvpVal record);
}