package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.vo.patient.PatientTaskFormValueVo;
import com.haoys.user.model.ProjectPatientResult;

import java.util.Date;
import java.util.List;

public interface ProjectPatientResultService {

    /**
     * 初始化项目任务方案列表
     */
    List<PatientTaskFormValueVo.PatientTaskFormVo> initProjectPatientTaskList();

    CustomResult savePatientResult(ProjectPatientResult projectPatientResult);

    /**
     * 更新指定项目任务方案
     * @param projectId     项目id
     * @param planId        方案id
     */
    void initProjectPatientTask(String projectId, String planId);

    int getPatientCurrentDateTaskCount(Long projectId, Long planId, Long taskId, Long testeeId);

    int getPatientCurrentDateTableTaskCount(Long projectId, Long planId, Long taskId, Long testeeId);

    int getPatientTaskCountByTaskDateAndSendRate(Long projectId, Long planId, Long taskId, Long testeeId, Date taskDate, Date nextTaskDate, String sendRate);

    ProjectPatientResult getRecentPatientTask(Long projectId, Long planId, Long taskId, Long testeeId);

    boolean getPatientTaskComplateResult(String projectId, String planId, String testeeId, String taskDate);

    PatientTaskFormValueVo getPatientTaskCalendarDetailByTaskDate(String projectId, String planId, String testeeId, String taskDate);

    ProjectPatientResult getCurrentDatePatientTask(String projectId, String planId, String taskId, Date taskDate, String testeeId);

    /**
     * 查询匹配前置条件的任务列表
     * @param projectId
     * @param planId
     * @param testeeId
     * @return
     */
    PatientTaskFormValueVo getPatientComputeTaskList(String projectId, String planId, String testeeId);

    void saveCurrentDatePatientTask(String projectId, String planId, String taskId, String testeeId);

    /**
     * 核对更新任务日历完成状态
     * @param projectId
     */
    void updateCheckProjectPatientTask(String projectId);
}
