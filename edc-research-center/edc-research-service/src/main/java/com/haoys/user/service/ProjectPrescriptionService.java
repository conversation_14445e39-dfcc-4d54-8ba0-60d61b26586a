package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectPrescriptionParam;
import com.haoys.user.domain.vo.ProjectPrescriptionVo;

public interface ProjectPrescriptionService {
    
    CustomResult saveProjectPrescription(ProjectPrescriptionParam projectPrescriptionParam);

    CommonPage<ProjectPrescriptionVo> getProjectPrescriptionListForPage(String projectId, String name, String ageStart, String ageEnd, String diagnosticDesc, String userId, Integer pageSize, Integer pageNum);
}
