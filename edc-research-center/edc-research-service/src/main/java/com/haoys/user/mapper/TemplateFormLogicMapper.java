package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormLogic;
import com.haoys.user.model.TemplateFormLogicExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormLogicMapper {
    long countByExample(TemplateFormLogicExample example);

    int deleteByExample(TemplateFormLogicExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormLogic record);

    int insertSelective(TemplateFormLogic record);

    List<TemplateFormLogic> selectByExample(TemplateFormLogicExample example);

    TemplateFormLogic selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormLogic record, @Param("example") TemplateFormLogicExample example);

    int updateByExample(@Param("record") TemplateFormLogic record, @Param("example") TemplateFormLogicExample example);

    int updateByPrimaryKeySelective(TemplateFormLogic record);

    int updateByPrimaryKey(TemplateFormLogic record);
}