package com.haoys.user.service;

import com.haoys.user.domain.param.file.ChunkUploadParam;
import com.haoys.user.domain.param.file.MergeChunkParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 分片文件上传服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 */
public interface ChunkedFileUploadService {

    /**
     * 检查文件上传状态
     * 
     * @param fileHash 文件MD5哈希值
     * @param fileName 文件名称
     * @param fileSize 文件大小
     * @param userId 用户ID
     * @return 上传状态信息
     */
    Map<String, Object> checkUploadStatus(String fileHash, String fileName, Long fileSize, Long userId);

    /**
     * 上传文件分片
     * 
     * @param file 分片文件
     * @param param 上传参数
     * @return 上传结果
     */
    Map<String, Object> uploadChunk(MultipartFile file, ChunkUploadParam param);

    /**
     * 合并文件分片
     * 
     * @param param 合并参数
     * @return 合并结果
     */
    UploadFileResultVo mergeChunks(MergeChunkParam param);

    /**
     * 直接上传小文件
     * 
     * @param file 文件
     * @param projectId 项目ID
     * @param folderName 文件夹名称
     * @param userId 用户ID
     * @return 上传结果
     */
    UploadFileResultVo directUpload(MultipartFile file, String projectId, String folderName, Long userId);

    /**
     * 获取上传进度
     * 
     * @param fileHash 文件哈希值
     * @param userId 用户ID
     * @return 进度信息
     */
    Map<String, Object> getUploadProgress(String fileHash, Long userId);

    /**
     * 取消上传
     * 
     * @param fileHash 文件哈希值
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelUpload(String fileHash, Long userId);

    /**
     * 获取上传配置
     * 
     * @return 配置信息
     */
    Map<String, Object> getUploadConfig();

    /**
     * 清理过期的分片文件
     * 
     * @return 清理的文件数量
     */
    int cleanupExpiredChunks();

    /**
     * 下载文件
     *
     * @param response HTTP响应
     * @param fileHash 文件哈希值
     * @param userId 用户ID
     */
    void downloadFile(HttpServletResponse response, String fileHash, Long userId);

    /**
     * 计算文件MD5哈希值
     *
     * @param file 文件
     * @return MD5哈希值
     */
    String calculateFileMD5(MultipartFile file);

    /**
     * 根据文件哈希值查询文件信息
     *
     * @param fileHash 文件MD5哈希值
     * @param userId 用户ID
     * @return 文件信息
     */
    Map<String, Object> getFileInfoByHash(String fileHash, Long userId);

    /**
     * 验证文件MD5哈希值
     *
     * @param file 文件
     * @param expectedHash 期望的哈希值
     * @return 是否匹配
     */
    boolean validateFileMD5(MultipartFile file, String expectedHash);
}
