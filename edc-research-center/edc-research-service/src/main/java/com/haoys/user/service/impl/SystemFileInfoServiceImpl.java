package com.haoys.user.service.impl;

import com.haoys.user.mapper.SystemFileInfoMapper;
import com.haoys.user.model.SystemFileInfo;
import com.haoys.user.service.SystemFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SystemFileInfoServiceImpl implements SystemFileInfoService {
    @Autowired
    private SystemFileInfoMapper systemFileInfoMapper;

    @Override
    public SystemFileInfo selectSystemFileInfoById(Long id) {
        return systemFileInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int insert(SystemFileInfo record) {
        return systemFileInfoMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(SystemFileInfo record) {
        return systemFileInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public SystemFileInfo selectByPrimaryKey(Long id){
        return systemFileInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int deleteByPrimaryKey(Long id){
        return systemFileInfoMapper.deleteByPrimaryKey(id);
    }

}
