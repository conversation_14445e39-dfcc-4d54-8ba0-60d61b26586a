package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.mobile.MobileUserRegisterParam;
import com.haoys.user.model.ProjectOrgInfo;

import java.util.List;

/**
 * SRC-ePRO用户service
 */
public interface MobileUserService {

    /**
     * 参与者用户注册
     * @param mobileUserRegisterParam 注册信息
     * @return 注册结果
     */
    CommonResult registerMobileUserInfo(MobileUserRegisterParam mobileUserRegisterParam);

    CommonResult mobileUserJoinProject(String projectId, String orgId, String userId);

    List<ProjectOrgInfo> getProjectBaseInfoByIdentCode(String identCode);

}
