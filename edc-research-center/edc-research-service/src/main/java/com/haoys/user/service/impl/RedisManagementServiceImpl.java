package com.haoys.user.service.impl;

import cn.hutool.core.util.StrUtil;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.config.RedisManagementConfig;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.RedisManagementVo;
import com.haoys.user.domain.vo.SecureTokenVo;
import com.haoys.user.service.RedisManagementService;
import com.haoys.user.service.SecureTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Redis数据管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Slf4j
@Service
public class RedisManagementServiceImpl implements RedisManagementService {

    @Autowired
    private RedisManagementConfig redisManagementConfig;

    @Autowired
    private SecureTokenService secureTokenService;

    @Autowired
    private RedisTemplateService redisTemplateService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public RedisManagementVo.SecretVerifyResponse verifySecret(String secretKey) {
        RedisManagementVo.SecretVerifyResponse response = new RedisManagementVo.SecretVerifyResponse();
        response.setVerifyTime(LocalDateTime.now());

        try {
            // 验证配置秘钥
            boolean valid = redisManagementConfig.getSecretKey().equals(secretKey);
            response.setValid(valid);
            
            if (valid) {
                response.setNextStep("请使用code和refreshCode获取AccessToken");
                log.info("Redis管理秘钥验证成功");
            } else {
                response.setNextStep("秘钥验证失败，请检查配置");
                log.warn("Redis管理秘钥验证失败");
            }
        } catch (Exception e) {
            log.error("验证Redis管理秘钥失败: {}", e.getMessage(), e);
            response.setValid(false);
            response.setNextStep("验证过程出现异常");
        }

        return response;
    }

    @Override
    public RedisManagementVo.AccessTokenResponse getAccessToken(String code, String refreshCode) {
        try {
            // 使用SecureTokenService获取AccessToken
            SecureTokenParam.GetAccessTokenParam param = new SecureTokenParam.GetAccessTokenParam();
            param.setCode(code);
            param.setRefreshCode(refreshCode);

            SecureTokenVo.AccessTokenResponse tokenResponse = secureTokenService.getAccessToken(param);

            RedisManagementVo.AccessTokenResponse response = new RedisManagementVo.AccessTokenResponse()
                    .setAccessToken(tokenResponse.getAccessToken())
                    .setExpireTime(tokenResponse.getExpireTime())
                    .setExpiresIn(tokenResponse.getExpiresIn())
                    .setUserId(tokenResponse.getUserId());

            log.info("Redis管理AccessToken获取成功: userId={}", response.getUserId());
            return response;
        } catch (Exception e) {
            log.error("获取Redis管理AccessToken失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取AccessToken失败: " + e.getMessage());
        }
    }

    @Override
    public RedisManagementVo.TokenValidateResponse validateToken(String accessToken) {
        RedisManagementVo.TokenValidateResponse response = new RedisManagementVo.TokenValidateResponse();
        response.setValidateTime(LocalDateTime.now());

        try {
            // 使用SecureTokenService验证AccessToken
            SecureTokenVo.ValidateResponse validateResponse = secureTokenService.validateAccessToken(accessToken);

            response.setValid(validateResponse.getValid())
                    .setRemainingTime(validateResponse.getRemainingTime())
                    .setUserId(validateResponse.getUserId());

            log.debug("Redis管理Token验证: valid={}, userId={}", response.getValid(), response.getUserId());
            return response;
        } catch (Exception e) {
            log.error("验证Redis管理Token失败: {}", e.getMessage(), e);
            response.setValid(false);
            response.setRemainingTime(0L);
            return response;
        }
    }

    @Override
    public RedisManagementVo.KeyQueryResponse queryKey(String key) {
        RedisManagementVo.KeyQueryResponse response = new RedisManagementVo.KeyQueryResponse();
        response.setKey(key);
        response.setQueryTime(LocalDateTime.now());

        try {
            // 检查key是否存在
            Boolean exists = redisTemplateService.hasKey(key);
            response.setExists(exists);

            if (Boolean.TRUE.equals(exists)) {
                // 获取数据类型
                String type = redisTemplate.type(key).code();
                response.setType(type);

                // 获取数据值
                Object value = redisTemplateService.get(key);
                response.setValue(value);

                // 获取TTL
                Long ttl = redisTemplateService.getExpire(key);
                response.setTtl(ttl);

                log.info("查询Redis Key成功: key={}, type={}, ttl={}", key, type, ttl);
            } else {
                log.info("Redis Key不存在: key={}", key);
            }
        } catch (Exception e) {
            log.error("查询Redis Key失败: key={}, error={}", key, e.getMessage(), e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.PatternQueryResponse queryPattern(String pattern, Integer limit) {
        RedisManagementVo.PatternQueryResponse response = new RedisManagementVo.PatternQueryResponse();
        response.setPattern(pattern);
        response.setQueryTime(LocalDateTime.now());

        try {
            // 使用SCAN命令查询匹配的keys
            Set<String> keys = redisTemplateService.keys(pattern);
            List<String> keyList = new ArrayList<>();

            if (keys != null) {
                int count = 0;
                for (String key : keys) {
                    if (limit != null && count >= limit) {
                        response.setTruncated(true);
                        break;
                    }
                    keyList.add(key);
                    count++;
                }
            }

            response.setKeys(keyList);
            response.setCount(keyList.size());
            response.setTruncated(response.getTruncated() != null ? response.getTruncated() : false);

            log.info("模糊查询Redis Keys成功: pattern={}, count={}, truncated={}", 
                    pattern, response.getCount(), response.getTruncated());
        } catch (Exception e) {
            log.error("模糊查询Redis Keys失败: pattern={}, error={}", pattern, e.getMessage(), e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.PrefixQueryResponse queryPrefix(String prefix, Integer limit) {
        RedisManagementVo.PrefixQueryResponse response = new RedisManagementVo.PrefixQueryResponse();
        response.setPrefix(prefix);
        response.setQueryTime(LocalDateTime.now());

        try {
            // 构造前缀匹配模式
            String pattern = prefix + "*";
            
            // 使用SCAN命令查询匹配的keys
            Set<String> keys = redisTemplateService.keys(pattern);
            List<String> keyList = new ArrayList<>();

            if (keys != null) {
                int count = 0;
                for (String key : keys) {
                    if (limit != null && count >= limit) {
                        response.setTruncated(true);
                        break;
                    }
                    keyList.add(key);
                    count++;
                }
            }

            response.setKeys(keyList);
            response.setCount(keyList.size());
            response.setTruncated(response.getTruncated() != null ? response.getTruncated() : false);

            log.info("前缀查询Redis Keys成功: prefix={}, count={}, truncated={}", 
                    prefix, response.getCount(), response.getTruncated());
        } catch (Exception e) {
            log.error("前缀查询Redis Keys失败: prefix={}, error={}", prefix, e.getMessage(), e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.DeleteResponse deleteKey(String key) {
        RedisManagementVo.DeleteResponse response = new RedisManagementVo.DeleteResponse();
        response.setKey(key);
        response.setDeleteTime(LocalDateTime.now());

        try {
            // 检查key是否存在
            Boolean existed = redisTemplateService.hasKey(key);
            response.setExisted(existed);

            // 删除key
            Boolean success = redisTemplateService.del(key);
            response.setSuccess(success);

            log.info("删除Redis Key: key={}, existed={}, success={}", key, existed, success);
        } catch (Exception e) {
            log.error("删除Redis Key失败: key={}, error={}", key, e.getMessage(), e);
            response.setSuccess(false);
            throw new RuntimeException("删除失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.BatchDeleteResponse deleteBatch(List<String> keys) {
        RedisManagementVo.BatchDeleteResponse response = new RedisManagementVo.BatchDeleteResponse();
        response.setRequestKeys(keys);
        response.setDeleteTime(LocalDateTime.now());

        List<String> successKeys = new ArrayList<>();
        List<String> failedKeys = new ArrayList<>();

        try {
            // 批量删除
            Long deletedCount = redisTemplateService.del(keys);
            response.setDeletedCount(deletedCount);

            // 验证删除结果
            for (String key : keys) {
                Boolean exists = redisTemplateService.hasKey(key);
                if (Boolean.FALSE.equals(exists)) {
                    successKeys.add(key);
                } else {
                    failedKeys.add(key);
                }
            }

            response.setSuccessKeys(successKeys);
            response.setFailedKeys(failedKeys);

            log.info("批量删除Redis Keys: total={}, success={}, failed={}, deletedCount={}", 
                    keys.size(), successKeys.size(), failedKeys.size(), deletedCount);
        } catch (Exception e) {
            log.error("批量删除Redis Keys失败: keys={}, error={}", keys, e.getMessage(), e);
            response.setDeletedCount(0L);
            response.setSuccessKeys(successKeys);
            response.setFailedKeys(keys);
            throw new RuntimeException("批量删除失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.PatternDeleteResponse deletePattern(String pattern) {
        RedisManagementVo.PatternDeleteResponse response = new RedisManagementVo.PatternDeleteResponse();
        response.setPattern(pattern);
        response.setDeleteTime(LocalDateTime.now());

        try {
            // 先查询匹配的keys
            Set<String> keys = redisTemplateService.keys(pattern);
            List<String> keyList = new ArrayList<>();
            
            if (keys != null) {
                keyList.addAll(keys);
            }

            response.setMatchedKeys(keyList);

            if (!keyList.isEmpty()) {
                // 批量删除
                Long deletedCount = redisTemplateService.del(keyList);
                response.setDeletedCount(deletedCount);
                
                log.info("模糊删除Redis Keys: pattern={}, matched={}, deleted={}", 
                        pattern, keyList.size(), deletedCount);
            } else {
                response.setDeletedCount(0L);
                log.info("模糊删除Redis Keys: pattern={}, 没有匹配的keys", pattern);
            }
        } catch (Exception e) {
            log.error("模糊删除Redis Keys失败: pattern={}, error={}", pattern, e.getMessage(), e);
            response.setDeletedCount(0L);
            throw new RuntimeException("模糊删除失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.PrefixDeleteResponse deletePrefix(String prefix) {
        RedisManagementVo.PrefixDeleteResponse response = new RedisManagementVo.PrefixDeleteResponse();
        response.setPrefix(prefix);
        response.setDeleteTime(LocalDateTime.now());

        try {
            // 构造前缀匹配模式
            String pattern = prefix + "*";
            
            // 先查询匹配的keys
            Set<String> keys = redisTemplateService.keys(pattern);
            List<String> keyList = new ArrayList<>();
            
            if (keys != null) {
                keyList.addAll(keys);
            }

            response.setMatchedKeys(keyList);

            if (!keyList.isEmpty()) {
                // 批量删除
                Long deletedCount = redisTemplateService.del(keyList);
                response.setDeletedCount(deletedCount);
                
                log.info("前缀删除Redis Keys: prefix={}, matched={}, deleted={}", 
                        prefix, keyList.size(), deletedCount);
            } else {
                response.setDeletedCount(0L);
                log.info("前缀删除Redis Keys: prefix={}, 没有匹配的keys", prefix);
            }
        } catch (Exception e) {
            log.error("前缀删除Redis Keys失败: prefix={}, error={}", prefix, e.getMessage(), e);
            response.setDeletedCount(0L);
            throw new RuntimeException("前缀删除失败: " + e.getMessage());
        }

        return response;
    }

    @Override
    public RedisManagementVo.AuthCodeGenerateResponse generateAuthCode() {
        RedisManagementVo.AuthCodeGenerateResponse response = new RedisManagementVo.AuthCodeGenerateResponse();
        response.setGenerateTime(LocalDateTime.now());

        try {
            // 基于配置秘钥和当前时间生成AuthCode
            String secretKey = redisManagementConfig.getSecretKey();
            long timestamp = System.currentTimeMillis();
            String authCode = "AUTH_" + secretKey.hashCode() + "_" + timestamp;

            // 设置1小时过期时间
            long expiresIn = 3600L;
            LocalDateTime expireTime = LocalDateTime.now().plusSeconds(expiresIn);

            response.setAuthCode(authCode)
                    .setExpireTime(expireTime)
                    .setExpiresIn(expiresIn);

            // 将AuthCode存储到Redis中，设置过期时间
            String cacheKey = "redis:management:authcode:" + authCode;
            redisTemplateService.set(cacheKey, "valid", expiresIn);

            log.info("Redis管理AuthCode生成成功: authCode={}, expireTime={}", authCode, expireTime);
            return response;
        } catch (Exception e) {
            log.error("生成Redis管理AuthCode失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成AuthCode失败: " + e.getMessage());
        }
    }

    @Override
    public RedisManagementVo.AuthCodeQueryResponse queryAllKeysByAuthCode(String authCode, Integer page, Integer size) {
        RedisManagementVo.AuthCodeQueryResponse response = new RedisManagementVo.AuthCodeQueryResponse();
        response.setQueryTime(LocalDateTime.now());
        response.setPage(page);
        response.setSize(size);

        try {
            // 验证AuthCode
            String cacheKey = "redis:management:authcode:" + authCode;
            Object cacheValueObj = redisTemplateService.get(cacheKey);
            String cacheValue = cacheValueObj != null ? cacheValueObj.toString() : null;
            if (!"valid".equals(cacheValue)) {
                throw new RuntimeException("AuthCode无效或已过期");
            }

            // 获取所有keys
            Set<String> allKeys = redisTemplateService.keys("*");
            List<String> keyList = new ArrayList<>(allKeys != null ? allKeys : new ArrayList<>());

            // 计算分页
            long total = keyList.size();
            int totalPages = (int) Math.ceil((double) total / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, keyList.size());

            response.setTotal(total)
                    .setTotalPages(totalPages);

            if (startIndex < keyList.size()) {
                List<String> pageKeys = keyList.subList(startIndex, endIndex);
                List<RedisManagementVo.AuthCodeQueryResponse.KeyInfo> keyInfos = new ArrayList<>();

                // 获取每个key的详细信息
                for (String key : pageKeys) {
                    try {
                        RedisManagementVo.AuthCodeQueryResponse.KeyInfo keyInfo =
                                new RedisManagementVo.AuthCodeQueryResponse.KeyInfo();
                        keyInfo.setKey(key);

                        // 获取数据类型
                        String type = redisTemplate.type(key).code();
                        keyInfo.setType(type);

                        // 获取TTL
                        Long ttl = redisTemplateService.getExpire(key);
                        keyInfo.setTtl(ttl);

                        // 获取值（限制大小避免内存问题）
                        Object value = redisTemplateService.get(key);
                        if (value != null) {
                            String valueStr = value.toString();
                            if (valueStr.length() > 1000) {
                                valueStr = valueStr.substring(0, 1000) + "...(truncated)";
                            }
                            keyInfo.setValue(valueStr);
                        }

                        keyInfos.add(keyInfo);
                    } catch (Exception e) {
                        log.warn("获取key详情失败: key={}, error={}", key, e.getMessage());
                        // 继续处理其他key
                    }
                }

                response.setKeys(keyInfos);
            } else {
                response.setKeys(new ArrayList<>());
            }

            log.info("AuthCode查询所有keys完成: authCode={}, total={}, page={}, size={}",
                    authCode, total, page, size);
            return response;
        } catch (Exception e) {
            log.error("AuthCode查询所有keys失败: authCode={}, error={}", authCode, e.getMessage(), e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public RedisManagementVo.PageQueryResponse queryKeysWithPagination(String queryType, String queryCondition, Integer page, Integer size) {
        RedisManagementVo.PageQueryResponse response = new RedisManagementVo.PageQueryResponse();
        response.setQueryTime(LocalDateTime.now());
        response.setPage(page);
        response.setSize(size);

        try {
            Set<String> matchedKeys;

            // 根据查询类型获取匹配的keys
            switch (queryType.toLowerCase()) {
                case "prefix":
                    matchedKeys = redisTemplateService.keys(queryCondition + "*");
                    break;
                case "pattern":
                    matchedKeys = redisTemplateService.keys(queryCondition);
                    break;
                case "all":
                    matchedKeys = redisTemplateService.keys("*");
                    break;
                default:
                    throw new IllegalArgumentException("不支持的查询类型: " + queryType);
            }

            List<String> keyList = new ArrayList<>(matchedKeys != null ? matchedKeys : new ArrayList<>());

            // 计算分页
            long total = keyList.size();
            int totalPages = (int) Math.ceil((double) total / size);
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, keyList.size());

            response.setTotal(total)
                    .setTotalPages(totalPages);

            if (startIndex < keyList.size()) {
                List<String> pageKeys = keyList.subList(startIndex, endIndex);
                response.setKeys(pageKeys);
            } else {
                response.setKeys(new ArrayList<>());
            }

            log.info("分页查询keys完成: type={}, condition={}, total={}, page={}, size={}",
                    queryType, queryCondition, total, page, size);
            return response;
        } catch (Exception e) {
            log.error("分页查询keys失败: type={}, condition={}, error={}", queryType, queryCondition, e.getMessage(), e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }
}
