package com.haoys.user.service.impl;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.mongodbvo.ProjectTesteeCheckMongoVo;
import com.haoys.user.domain.param.project.ProjectCheckDataParam;
import com.haoys.user.domain.param.project.ProjectCheckQueryParam;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeCheckVo;
import com.haoys.user.enums.ProjectCheckEnum;
import com.haoys.user.mapper.ProjectTesteeCheckMapper;
import com.haoys.user.model.ProjectTesteeCheck;
import com.haoys.user.mongodb.MongoTemplateUtil;
import com.haoys.user.mongodb.MongodbConstants;
import com.haoys.user.mongodb.PageModel;
import com.haoys.user.service.ProjectTesteeCheckService;
import com.haoys.user.service.SystemUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProjectTesteeCheckServiceImpl extends BaseService implements ProjectTesteeCheckService {

    
    @Autowired(required = false)
    private MongoTemplateUtil mongoTemplateUtil;
    @Autowired
    private ProjectTesteeCheckMapper projectTesteeCheckMapper;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    
    @Value("${mongodb.enabled}")
    private Boolean enableMongodbRecord;

    @Override
    public CommonPage<ProjectTesteeCheckVo> getProjectCheckListForPage(ProjectCheckQueryParam projectCheckQueryParam) {
        Page<Object> page = PageHelper.startPage(projectCheckQueryParam.getPageNum(), projectCheckQueryParam.getPageSize());
        List<ProjectTesteeCheckVo> projectCheckList;
        Integer pageNum = projectCheckQueryParam.getPageNum();
        Integer pageSize = projectCheckQueryParam.getPageSize();
        if(enableMongodbRecord){
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("projectId", projectCheckQueryParam.getProjectId());
            paramMap.put("status", BusinessConfig.VALID_STATUS);
            if(StringUtils.isNotEmpty(projectCheckQueryParam.getTesteeCode())){
                paramMap.put("testeeCode", projectCheckQueryParam.getTesteeCode());
            }
            if(StringUtils.isNotEmpty(projectCheckQueryParam.getType())){
                paramMap.put("type", projectCheckQueryParam.getType());
            }
            if(StringUtils.isNotEmpty(projectCheckQueryParam.getCreateUserName())){
                paramMap.put("createUserName", projectCheckQueryParam.getCreateUserName());
            }
            if(StringUtils.isNotEmpty(projectCheckQueryParam.getVariableName())){
                paramMap.put("variableName", projectCheckQueryParam.getVariableName());
            }
            PageModel pageModel = MongoTemplateUtil.findDataLsitPageCondition(ProjectTesteeCheckMongoVo.class, MongodbConstants.MONGO_DB_PROJECT_CHECK_COLLECTION_NAME, paramMap, pageNum, pageSize, Sort.Direction.DESC, "createTime");
            projectCheckList = (List<ProjectTesteeCheckVo>) pageModel.getList();
            page.setPages(pageModel.getPages());
            page.setTotal(pageModel.getTotal());
            return commonPageListWrapper(projectCheckQueryParam.getPageNum(), projectCheckQueryParam.getPageSize(), page, projectCheckList);
        }else{
            projectCheckList = projectTesteeCheckMapper.getProjectCheckListForPage(projectCheckQueryParam);
            for (ProjectTesteeCheckVo projectTesteeCheckVo : projectCheckList) {
                projectTesteeCheckVo.setTypeView(ProjectCheckEnum.getValue(projectTesteeCheckVo.getType()));
                SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectTesteeCheckVo.getCreateUserId());
                if(systemUserInfoExtendVo != null){projectTesteeCheckVo.setCreateRealName(systemUserInfoExtendVo.getRealName());}
            }
            return commonPageListWrapper(projectCheckQueryParam.getPageNum(), projectCheckQueryParam.getPageSize(), page, projectCheckList);
        }
    }

    @Override
    public String saveProjectCheckRequestLog(ProjectCheckDataParam projectCheckDataParam) {
        ProjectTesteeCheck record = new ProjectTesteeCheck();
        BeanUtils.copyProperties(projectCheckDataParam, record);
        record.setId(SnowflakeIdWorker.getUuid());
        record.setStatus(BusinessConfig.VALID_STATUS);
        if(projectCheckDataParam.getCreateTime() == null){
            record.setCreateTime(new Date());
        }
        record.setCreateUserId(projectCheckDataParam.getCreateUserId());
        if(enableMongodbRecord){
            ProjectTesteeCheckMongoVo projectTesteeCheckMongoVo = new ProjectTesteeCheckMongoVo();
            BeanUtils.copyProperties(record, projectTesteeCheckMongoVo);
            projectTesteeCheckMongoVo.setCustomTestsee(projectCheckDataParam.getCustomTestsee());
            mongoTemplateUtil.saveOne(MongodbConstants.MONGO_DB_PROJECT_CHECK_COLLECTION_NAME, projectTesteeCheckMongoVo);
            
        }else{
            projectTesteeCheckMapper.insertSelective(record);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public void updateProjectCheckLogForBase(String projectId, String visitId, String formId, String testeeId, boolean adminUser) {
        /*if(adminUser){
            projectTesteeCheckMapper.cleanAllProjectCheckLog();
        }else{
            projectTesteeCheckMapper.deleteProjectCheckLog(projectId, visitId, formId, testeeId);
        }*/
    }
}
