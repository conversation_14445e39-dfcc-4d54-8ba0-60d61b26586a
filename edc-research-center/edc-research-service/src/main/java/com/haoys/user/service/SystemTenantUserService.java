package com.haoys.user.service;

import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemTenantUserExample;

import java.util.List;

public interface SystemTenantUserService {

    SystemTenantUser getSystemTenantUserByUserId(String userId);
    
    SystemTenantUser getSystemTenantUserByUserId(String userId, String tenantId, String platformId);

    int updateSystemTenantUser(SystemTenantUser record);

    int insertSystemTenantUser(SystemTenantUser record);

    List<SystemTenantUser> getSystemTenantUserList(SystemTenantUserExample example);

    void deleteSystemTenantUserByPrimaryKey(Long id);
    
}
