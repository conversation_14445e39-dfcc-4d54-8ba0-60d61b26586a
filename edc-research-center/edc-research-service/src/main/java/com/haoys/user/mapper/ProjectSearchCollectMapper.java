package com.haoys.user.mapper;

import com.haoys.user.model.ProjectSearchCollect;
import com.haoys.user.model.ProjectSearchCollectExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectSearchCollectMapper {
    long countByExample(ProjectSearchCollectExample example);

    int deleteByExample(ProjectSearchCollectExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectSearchCollect record);

    int insertSelective(ProjectSearchCollect record);

    List<ProjectSearchCollect> selectByExample(ProjectSearchCollectExample example);

    ProjectSearchCollect selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectSearchCollect record, @Param("example") ProjectSearchCollectExample example);

    int updateByExample(@Param("record") ProjectSearchCollect record, @Param("example") ProjectSearchCollectExample example);

    int updateByPrimaryKeySelective(ProjectSearchCollect record);

    int updateByPrimaryKey(ProjectSearchCollect record);
}