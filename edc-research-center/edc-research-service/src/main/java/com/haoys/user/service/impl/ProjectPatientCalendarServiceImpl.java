package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.enums.PatientTaskComplateEnum;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.vo.patient.PatientPreconditionVo;
import com.haoys.user.domain.vo.patient.PatientTaskCalendarDetailVo;
import com.haoys.user.domain.vo.patient.ProjectPatientCalendarVo;
import com.haoys.user.domain.vo.project.ProjectPlanVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.mapper.ProjectPatientCalendarMapper;
import com.haoys.user.model.ProjectPatientCalendar;
import com.haoys.user.model.ProjectPatientCalendarExample;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.service.ProjectPatientCalendarService;
import com.haoys.user.service.ProjectPlanManageService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ProjectPatientCalendarServiceImpl implements ProjectPatientCalendarService {

    @Resource
    private ProjectPatientCalendarMapper projectPatientCalendarMapper;
    @Resource
    private ProjectPlanManageService projectPlanManageService;
    @Resource
    private ProjectVisitConfigService projectVisitConfigService;
    @Resource
    private TemplateConfigService templateConfigService;

    @Override
    public PatientTaskCalendarDetailVo getCurrentWholeMonthTask(String projectId, String testeeId, String planId, String taskDateTime) {
        String startMonthDate = DateUtil.getFirstDay4Month(new Date());
        String endMonthDate = DateUtil.getLastDay4Month(new Date());
        if(StringUtils.isNotEmpty(taskDateTime)){
            startMonthDate = DateUtil.getFirstDay4Month(DateUtil.getAutoParseDate(taskDateTime, DateUtil.SECONDFORMAT));
            endMonthDate = DateUtil.getLastDay4Month(DateUtil.getAutoParseDate(taskDateTime, DateUtil.SECONDFORMAT));
        }
        PatientTaskCalendarDetailVo patientTaskCalendarDetailVo = new PatientTaskCalendarDetailVo();
        List<ProjectPatientCalendarVo> dataList = new ArrayList<>();
        ProjectPatientCalendarExample example = new ProjectPatientCalendarExample();
        ProjectPatientCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTaskDateBetween(DateUtil.getAutoParseDate(startMonthDate), DateUtil.getAutoParseDate(endMonthDate));
        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarMapper.selectByExample(example);
        for (ProjectPatientCalendar projectPatientCalendar : projectPatientCalendarList) {
            ProjectPatientCalendarVo projectPatientCalendarVo = new ProjectPatientCalendarVo();
            BeanUtils.copyProperties(projectPatientCalendar, projectPatientCalendarVo);
            dataList.add(projectPatientCalendarVo);
        }
        patientTaskCalendarDetailVo.setDataList(dataList);
        return patientTaskCalendarDetailVo;
    }

    @Override
    public void savePatientCalendarTask(String projectId, String planId, String testeeId, boolean checkTask) {
        ProjectPatientCalendarExample example = new ProjectPatientCalendarExample();
        ProjectPatientCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarMapper.selectByExample(example);
        if(CollectionUtil.isEmpty(projectPatientCalendarList)){
            ProjectPatientCalendar record = new ProjectPatientCalendar();
            record.setId(SnowflakeIdWorker.getUuid());
            record.setProjectId(Long.parseLong(projectId));
            record.setPlanId(Long.parseLong(planId));
            record.setTesteeId(Long.parseLong(testeeId));
            record.setTaskDate(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
            record.setCheckTaskResult(checkTask);
            record.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_01.getCode());
            if(!checkTask){
                record.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
            }
            //查询前置条件
            ProjectPlanVo projectPlanInfo = projectPlanManageService.getProjectPlanInfo(planId);

            Long visitId = projectPlanInfo.getVisitId();
            if(visitId != null){
                Long formId = projectPlanInfo.getFormId();
                Long formDetailId = projectPlanInfo.getFormDetailId();
                String formDetailValue = projectPlanInfo.getFormDetailValue();

                PatientPreconditionVo patientPreconditionVo = new PatientPreconditionVo();
                patientPreconditionVo.setVisitId(visitId);
                ProjectVisitVo projectVisitVo = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(visitId.toString());
                patientPreconditionVo.setVisitName(projectVisitVo.getVisitName());
                patientPreconditionVo.setFormId(formId);
                TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(formId);
                patientPreconditionVo.setFormName(templateFormConfig.getFormName());
                patientPreconditionVo.setFormDetailId(formDetailId);
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId.toString());
                patientPreconditionVo.setFormDetailName(templateFormDetailConfig.getLabel());
                patientPreconditionVo.setFormDetailValue(formDetailValue);
                record.setConfigInfo(JSON.toJSONString(patientPreconditionVo));
            }
            record.setCreateTime(new Date());
            projectPatientCalendarMapper.insert(record);
        }
    }

    @Override
    public void updatePatientCalendarTask(String projectId, String planId, String testeeId, String complateState) {
        updatePatientCalendarTaskByTaskDate(projectId, planId, testeeId, null, complateState);
    }

    public void updatePatientCalendarTaskByTaskDate(String projectId, String planId, String testeeId, String taskDate, String complateState) {
        ProjectPatientCalendarExample example = new ProjectPatientCalendarExample();
        ProjectPatientCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));

        if(StringUtils.isNotEmpty(taskDate)){
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(taskDate, DateUtil.DEFAULTFORMAT));
        }else{
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        }

        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectPatientCalendarList)){
            ProjectPatientCalendar record = projectPatientCalendarList.get(0);
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode().equals(complateState)){
                record.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
            }
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(complateState)){
                record.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
            }
            projectPatientCalendarMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public PatientTaskCalendarDetailVo getCustomBetweenTimeTask(String projectId, String testeeId, String planId, Date bindTime, Date nowDate) {
        PatientTaskCalendarDetailVo patientTaskCalendarDetailVo = new PatientTaskCalendarDetailVo();
        List<ProjectPatientCalendarVo> dataList = new ArrayList<>();
        ProjectPatientCalendarExample example = new ProjectPatientCalendarExample();
        ProjectPatientCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTaskDateBetween(bindTime, nowDate);
        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarMapper.selectByExample(example);
        for (ProjectPatientCalendar projectPatientCalendar : projectPatientCalendarList) {
            ProjectPatientCalendarVo projectPatientCalendarVo = new ProjectPatientCalendarVo();
            BeanUtils.copyProperties(projectPatientCalendar, projectPatientCalendarVo);
            dataList.add(projectPatientCalendarVo);
        }
        patientTaskCalendarDetailVo.setDataList(dataList);
        return patientTaskCalendarDetailVo;
    }

    @Override
    public List<ProjectPatientCalendar> getAllCalendarPatientTaskList(String projectId) {
        ProjectPatientCalendarExample example = new ProjectPatientCalendarExample();
        ProjectPatientCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarMapper.selectByExample(example);
        return projectPatientCalendarList;
    }

    @Override
    public void updatePatientCalendarTaskById(ProjectPatientCalendar projectPatientCalendar) {
        projectPatientCalendarMapper.updateByPrimaryKeySelective(projectPatientCalendar);
    }

    @Override
    public ProjectPatientCalendar getCurrentCalendarPatientTaskByTaskDate(String projectId, String planId, String testeeId, String taskDate) {
        return this.getCurrentCalendarPatientTask(projectId, planId, testeeId, taskDate);
    }

    public ProjectPatientCalendar getCurrentCalendarPatientTask(String projectId, String planId, String testeeId, String taskDate) {
        ProjectPatientCalendarExample example = new ProjectPatientCalendarExample();
        ProjectPatientCalendarExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        if(StringUtils.isNotEmpty(taskDate)){
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(taskDate, DateUtil.DEFAULTFORMAT));
        }else{
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        }
        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectPatientCalendarList)){
            return projectPatientCalendarList.get(0);
        }
        return null;
    }

    @Override
    public ProjectPatientCalendar getCurrentCalendarPatientTask(String projectId, String planId, String testeeId) {
        return getCurrentCalendarPatientTask(projectId, planId, testeeId, null);
    }

    @Override
    public void saveCurrentDateCalendarPatientTask(String projectId, String planId, String testeeId, boolean checkTask) {
        savePatientCalendarTask(projectId, planId, testeeId, checkTask);
    }
}
