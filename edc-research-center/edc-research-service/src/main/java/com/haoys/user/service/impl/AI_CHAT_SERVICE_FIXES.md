# AiChatServiceImpl 用户ID获取问题修复报告

## 问题描述

### 1. 主要问题
- `AiChatServiceImpl#createSession`方法中因获取`String userId = SecurityUtils.getUserIdValue();`userId为空导致数据插入失败
- 其他方法中也存在类似的用户ID获取问题
- 需要为匿名用户设置每日使用大模型的次数限定为20次

### 2. 根本原因
- `SecurityUtils.getUserIdValue()`在用户未登录时返回`null`
- 直接使用`null`值进行数据库插入操作导致失败
- 缺少匿名用户的使用限制机制

## 修复方案

### 1. 创建安全的用户ID获取方法

**新增常量定义**:
```java
/**
 * 匿名用户标识
 */
private static final String ANONYMOUS_USER = "anonymousUser";

/**
 * 匿名用户每日使用限制
 */
private static final int ANONYMOUS_DAILY_LIMIT = 20;
```

**新增安全获取方法**:
```java
/**
 * 安全地获取用户ID
 * 如果未获取到当前登录用户，使用anonymousUser保存数据
 * 
 * @return 用户ID，未登录时返回"anonymousUser"
 */
private String safeGetUserId() {
    try {
        String userId = SecurityUtils.getUserIdValue();
        if (StrUtil.isNotBlank(userId)) {
            return userId;
        }
    } catch (Exception e) {
        log.debug("获取登录用户ID失败: {}", e.getMessage());
    }
    return ANONYMOUS_USER;
}

/**
 * 安全地获取用户名
 * 如果未获取到当前登录用户，使用"匿名用户"
 * 
 * @return 用户名，未登录时返回"匿名用户"
 */
private String safeGetUserName() {
    try {
        String userName = SecurityUtils.getUserName();
        if (StrUtil.isNotBlank(userName)) {
            return userName;
        }
    } catch (Exception e) {
        log.debug("获取登录用户名失败: {}", e.getMessage());
    }
    return "匿名用户";
}
```

### 2. 实现匿名用户使用限制

**新增限制检查方法**:
```java
/**
 * 检查匿名用户每日使用限制
 * 
 * @param userId 用户ID
 * @return 是否超过限制
 */
private boolean checkAnonymousUserDailyLimit(String userId) {
    if (!ANONYMOUS_USER.equals(userId)) {
        return false; // 非匿名用户不受限制
    }
    
    try {
        // 查询匿名用户今日使用次数
        Map<String, Object> todayUsage = tokenUsageMapper.selectUserTodayUsage(userId);
        if (todayUsage != null) {
            Object requestCountObj = todayUsage.get("requestCount");
            if (requestCountObj != null) {
                int requestCount = Integer.parseInt(requestCountObj.toString());
                if (requestCount >= ANONYMOUS_DAILY_LIMIT) {
                    log.warn("匿名用户今日使用次数已达上限: {}/{}", requestCount, ANONYMOUS_DAILY_LIMIT);
                    return true;
                }
            }
        }
    } catch (Exception e) {
        log.error("检查匿名用户使用限制失败", e);
        // 出现异常时为了安全起见，认为已超限
        return true;
    }
    
    return false;
}
```

## 修复的方法列表

### 1. createSession方法
**修复前**:
```java
String userId = SecurityUtils.getUserIdValue();
String userName = SecurityUtils.getUserName();
```

**修复后**:
```java
String userId = safeGetUserId();
String userName = safeGetUserName();

// 检查匿名用户每日使用限制
if (checkAnonymousUserDailyLimit(userId)) {
    return CommonResult.failed("匿名用户每日使用次数已达上限: " + ANONYMOUS_DAILY_LIMIT + " 次，请登录后继续使用");
}
```

### 2. sendMessage方法
**新增限制检查**:
```java
// 2. 检查匿名用户每日使用限制
String currentUserId = safeGetUserId();
if (checkAnonymousUserDailyLimit(currentUserId)) {
    return CommonResult.failed("匿名用户每日使用次数已达上限: " + ANONYMOUS_DAILY_LIMIT + " 次，请登录后继续使用");
}
```

### 3. sendMessageStream方法
**新增限制检查**:
```java
// 1. 检查匿名用户每日使用限制
String currentUserId = safeGetUserId();
if (checkAnonymousUserDailyLimit(currentUserId)) {
    emitter.completeWithError(new RuntimeException("匿名用户每日使用次数已达上限: " + ANONYMOUS_DAILY_LIMIT + " 次，请登录后继续使用"));
    return;
}
```

### 4. 其他方法的用户ID获取修复
以下方法中的`SecurityUtils.getUserIdValue()`都替换为`safeGetUserId()`:

- `getSession()` - 权限检查
- `getUserSessions()` - 用户ID获取和权限检查
- `getSessionMessages()` - 权限检查
- `endSession()` - 权限检查
- `deleteSession()` - 权限检查
- `getUserTokenUsage()` - 用户ID获取和权限检查

## 修复效果

### 1. ✅ 解决数据插入失败问题
- 未登录用户现在使用"anonymousUser"作为用户ID
- 数据库插入操作不再因为null值而失败
- 匿名用户可以正常创建会话和发送消息

### 2. ✅ 实现匿名用户使用限制
- 匿名用户每日最多使用20次AI对话
- 超过限制时返回友好的错误提示
- 鼓励用户登录后继续使用

### 3. ✅ 提高系统稳定性
- 所有用户ID获取操作都使用安全的方法
- 避免因用户未登录导致的系统异常
- 增强了系统的容错能力

### 4. ✅ 保持功能完整性
- 登录用户的所有功能保持不变
- 权限检查机制正常工作
- 数据统计和分析功能正常

## 测试建议

### 1. 匿名用户测试
- 测试未登录状态下创建会话
- 测试未登录状态下发送消息
- 测试匿名用户每日20次限制
- 测试超过限制后的错误提示

### 2. 登录用户测试
- 确认登录用户功能不受影响
- 测试权限检查是否正常
- 测试数据统计是否正确

### 3. 边界情况测试
- 测试SecurityUtils异常情况
- 测试数据库查询异常情况
- 测试并发访问情况

## 注意事项

### 1. 数据库影响
- 匿名用户的数据会以"anonymousUser"作为用户ID存储
- 需要考虑匿名用户数据的清理策略
- 统计分析时需要区分匿名用户和真实用户

### 2. 性能考虑
- 每次匿名用户请求都会查询今日使用次数
- 可以考虑使用Redis缓存来优化性能
- 建议定期清理匿名用户的历史数据

### 3. 安全考虑
- 匿名用户限制可以防止滥用
- 建议监控匿名用户的使用模式
- 可以根据实际情况调整每日限制次数

## 总结

通过本次修复，成功解决了AiChatServiceImpl中因用户ID获取失败导致的数据插入问题，并实现了匿名用户的使用限制机制。修复后的系统具有更好的稳定性和用户体验，同时保持了原有功能的完整性。
