package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormVariableFun;
import com.haoys.user.model.TemplateFormVariableFunExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormVariableFunMapper {
    long countByExample(TemplateFormVariableFunExample example);

    int deleteByExample(TemplateFormVariableFunExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TemplateFormVariableFun record);

    int insertSelective(TemplateFormVariableFun record);

    List<TemplateFormVariableFun> selectByExample(TemplateFormVariableFunExample example);

    TemplateFormVariableFun selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TemplateFormVariableFun record, @Param("example") TemplateFormVariableFunExample example);

    int updateByExample(@Param("record") TemplateFormVariableFun record, @Param("example") TemplateFormVariableFunExample example);

    int updateByPrimaryKeySelective(TemplateFormVariableFun record);

    int updateByPrimaryKey(TemplateFormVariableFun record);
}