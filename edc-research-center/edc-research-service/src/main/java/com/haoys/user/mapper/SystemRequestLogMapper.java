package com.haoys.user.mapper;

import com.haoys.user.domain.entity.SystemRequestLogQuery;
import com.haoys.user.model.SystemRequestLog;
import com.haoys.user.model.SystemRequestLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemRequestLogMapper {
    long countByExample(SystemRequestLogExample example);

    int deleteByExample(SystemRequestLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemRequestLog record);

    int insertSelective(SystemRequestLog record);

    List<SystemRequestLog> selectByExample(SystemRequestLogExample example);

    SystemRequestLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemRequestLog record, @Param("example") SystemRequestLogExample example);

    int updateByExample(@Param("record") SystemRequestLog record, @Param("example") SystemRequestLogExample example);

    int updateByPrimaryKeySelective(SystemRequestLog record);

    int updateByPrimaryKey(SystemRequestLog record);

    int deleteOperLogByIds(Long[] operIds);

    void clearSystemRequestLog();

    List<SystemRequestLogQuery> selectRequestLogListForPage(SystemRequestLogQuery operLog);

    List<SystemRequestLog> selectSystemRequestLogList(SystemRequestLogQuery operLog);
}