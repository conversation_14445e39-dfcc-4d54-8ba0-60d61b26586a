package com.haoys.user.service;


import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.testee.ProjectTesteeExportParam;
import com.haoys.user.domain.vo.ExportTesteeVo;
import com.haoys.user.model.ProjectTesteeExport;
import com.haoys.user.model.ProjectTesteeExportManage;

import java.util.List;

/**
 * 参与者导出
 */

public interface ProjectTesteeExportManageService {

    /**
     * 导出列表
     *
     * @param projectId
     * @param orgId
     * @param exportName
     * @param name
     * @return
     */
    CommonResult<List<ProjectTesteeExportManage>> list(Long projectId, Long orgId, String exportName, Integer exportType);

    /**
     * 导出参与者
     * @param exportParam
     * @return
     */
    CommonResult<Object> saveProjectTesteeRecordExport(ProjectTesteeExportParam exportParam);

    /**
     * 获取参与者的填写的表单的信息（全量）
     * testeeIds 参与者id集合
     * projectId 项目id
     * orgId 机构id
     */
    List<List<ExportTesteeVo>> getSimpleDataAnalysisList(ProjectTesteeExportParam exportParam);

    /**
     * 获取导出详情
     * @param projectId
     * @param orgId
     * @param exportType
     * @return
     */
    CommonResult<ProjectTesteeExportParam> getExportInfo(Long projectId, Long orgId, Integer exportType);

    /**
     * 获取导出进度
     * @param exportFileId 导出文件ID
     * @return 导出进度信息
     */
    CommonResult<ProjectTesteeExport> getExportProgress(Long exportFileId);

    /**
     * 获取下载管理
     * @return
     */
    CommonResult<List<ProjectTesteeExportManage>> getDownList();


}
