package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.export.ProjectSearchCollectParam;
import com.haoys.user.domain.vo.export.ProjectSearchCollectVo;

public interface KeyWordSearchCollectService {

    CustomResult saveDiseaseSearchCollectRecord(ProjectSearchCollectParam projectSearchCollectParam);

    CustomResult deleteDiseaseSearchCollectRecord(String id);

    CommonPage<ProjectSearchCollectVo> getDiseaseSearchCollectListForPage(String dataBaseId, String diseaseType, String systemUseType, String searchType, Integer pageNum, Integer pageSize);
}
