package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectConfigVo;
import com.haoys.user.model.ProjectConfigValue;
import com.haoys.user.model.ProjectConfigValueExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectConfigValueMapper {
    long countByExample(ProjectConfigValueExample example);

    int deleteByExample(ProjectConfigValueExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectConfigValue record);

    int insertSelective(ProjectConfigValue record);

    List<ProjectConfigValue> selectByExample(ProjectConfigValueExample example);

    ProjectConfigValue selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectConfigValue record, @Param("example") ProjectConfigValueExample example);

    int updateByExample(@Param("record") ProjectConfigValue record, @Param("example") ProjectConfigValueExample example);

    int updateByPrimaryKeySelective(ProjectConfigValue record);

    int updateByPrimaryKey(ProjectConfigValue record);

    /**
     * 获取项目配置
     * @param projectId 项目id
     * @param moduleName 模块名称
     * @param configCode 配置code
     * @return 配置的结合
     */
    List<ProjectConfigVo> getConfigList(@Param("projectId") String projectId, @Param("moduleName") String moduleName,@Param("configCode") String configCode);
}
