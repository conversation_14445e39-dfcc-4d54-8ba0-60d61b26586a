package com.haoys.user.service.impl;

import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.vo.ecrf.TemplateVariableVo;
import com.haoys.user.enums.ProjectRoleEnum;
import com.haoys.user.mapper.ProjectMapper;
import com.haoys.user.mapper.ProjectRoleMapper;
import com.haoys.user.mapper.ProjectUserInfoMapper;
import com.haoys.user.mapper.ProjectUserRoleMapper;
import com.haoys.user.mapper.TemplateFormBaseMapper;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectExample;
import com.haoys.user.model.ProjectRole;
import com.haoys.user.model.ProjectRoleExample;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.model.ProjectUserInfoExample;
import com.haoys.user.model.ProjectUserRole;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormTable;
import com.haoys.user.service.BackStageManageService;
import com.haoys.user.service.TemplateConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class BackStageManageServiceImpl extends BaseService implements BackStageManageService {

    private final ProjectMapper projectMapper;
    private final ProjectUserInfoMapper projectUserInfoMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectRoleMapper projectRoleMapper;
    private final TemplateConfigService templateConfigService;
    private final TemplateFormBaseMapper templateFormBaseMapper;

    @Override
    public void updateProjectUserAuthForBackStage(Long userId){
        //获取租户所有项目
        ProjectExample example = new ProjectExample();
        ProjectExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteria.andPlatformIdEqualTo(SecurityUtils.getSystemPlatformId());
        List<Project> projectList = projectMapper.selectByExample(example);

        //获取编辑用户下的项目
        ProjectUserInfoExample projectUserInfoExample = new ProjectUserInfoExample();
        ProjectUserInfoExample.Criteria projectUserInfoExampleCriteria = projectUserInfoExample.createCriteria();
        projectUserInfoExampleCriteria.andUserIdEqualTo(userId);
        List<ProjectUserInfo> projectUserInfoList = projectUserInfoMapper.selectByExample(projectUserInfoExample);
        //去重
        ArrayList<Long> projectIdlist = new ArrayList<>();
        for(Project p :projectList){
            projectIdlist.add(p.getId());
        }
        ArrayList<Long> projectIdlistByprojectIdlistIdlist = new ArrayList<>();
        for(ProjectUserInfo pu :projectUserInfoList){
            projectIdlistByprojectIdlistIdlist.add(pu.getProjectId());
        }
        projectIdlistByprojectIdlistIdlist.forEach(i -> {
            if(projectIdlist.contains(i)){
                projectIdlist.remove(i);
            }
        });
        if(!projectIdlist.isEmpty()){
            for(Long pId : projectIdlist){
                add(pId, userId);//添加项目权限
            }
        }
    }

    @Override
    public Map<String, Object> updateFormVariableFieldCode() {
        Map<String, Object> dataMap = new HashMap<>();
        // 查询所有表单变量
        List<TemplateVariableVo> variableList = templateConfigService.getTemplateVariableDetailConfig();
        for (TemplateVariableVo templateVariableVo : variableList) {
            if(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE_COLUMN.equals(templateVariableVo.getType())){
                TemplateFormTable templateFormTable = templateConfigService.getTemplateFormTableConfigById(templateVariableVo.getVariableId());
                templateFormTable.setFieldName(RandomStringUtils.randomAlphanumeric(6));
                templateConfigService.updateTemplateFormTableConfigById(templateFormTable);
                dataMap.put(templateVariableVo.getLabel() +", "+ templateVariableVo.getFieldName(), templateFormTable.getFieldName());
            }else {
                TemplateFormDetail templateFormDetail = templateConfigService.getTemplateFormDetailConfigByVariableId(templateVariableVo.getVariableId().toString());
                templateFormDetail.setFieldName(RandomStringUtils.randomAlphanumeric(6));
                templateConfigService.updateTemplateFormDetailConfigById(templateFormDetail);
                dataMap.put(templateVariableVo.getLabel() +", "+ templateVariableVo.getFieldName(), templateFormDetail.getFieldName());
            }
        }
        return dataMap;
    }
    
    @Override
    public Map<String, Object> getSystemQueryReturnRecord(String queryConditionValue) {
        return templateFormBaseMapper.getSystemQueryReturnRecord(queryConditionValue);
    }
    
    @Override
    public List<Map<String, Object>> getSystemQueryReturnTableRecord(String queryConditionValue) {
        return templateFormBaseMapper.getSystemQueryReturnTableRecord(queryConditionValue);
    }
    
    /**
     * 授权添加
     * @param projectId
     * @param userId
     */
    public void add(Long projectId, Long userId){
        //获取项目管理员
        ProjectRoleExample projectRoleExample = new ProjectRoleExample();
        ProjectRoleExample.Criteria projectRoleExampleCriteria = projectRoleExample.createCriteria();
        projectRoleExampleCriteria.andEnnameEqualTo(ProjectRoleEnum.PROJECT_PA.getCode());
        projectRoleExampleCriteria.andProjectIdEqualTo(projectId);
        List<ProjectRole> projectRoleList = projectRoleMapper.selectByExample(projectRoleExample);
        if(projectRoleList.size() > 0){
            //添加项目
            ProjectUserInfo projectUserInfo = new ProjectUserInfo();
            projectUserInfo.setProjectId(projectId);
            projectUserInfo.setUserId(userId);
            projectUserInfo.setStatus(BusinessConfig.VALID_STATUS);
            projectUserInfo.setActiveStatus(true);
            projectUserInfo.setRejectStatus(true);
            projectUserInfo.setCreateTime(new Date());
            projectUserInfo.setTenantId(SecurityUtils.getSystemTenantId());
            projectUserInfo.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectUserInfo.setId(SnowflakeIdWorker.getUuid());
            projectUserInfo.setPaRole(true);
            projectUserInfoMapper.insert(projectUserInfo);

            //授权项目角色
            ProjectUserRole projectUserRole = new ProjectUserRole();
            projectUserRole.setProjectId(projectId);
            projectUserRole.setUserId(userId);
            projectUserRole.setRoleId(projectRoleList.get(0).getId());
            projectUserRole.setTenantId(SecurityUtils.getSystemTenantId());
            projectUserRole.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectUserRoleMapper.insert(projectUserRole);
        }
    }
}
