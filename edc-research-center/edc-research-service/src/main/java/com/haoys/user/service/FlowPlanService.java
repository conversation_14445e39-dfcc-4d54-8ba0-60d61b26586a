package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.model.FlowPlan;

import java.util.List;

/**
 * 流程配置-创建计划服务类
 */
public interface FlowPlanService {
    /**
     * 创建计划
     * @param plan
     * @return
     */
    CommonResult create(FlowPlan plan);
    /**
     * 更新计划
     * @param plan
     * @return
     */
    CommonResult update(FlowPlan plan);
    /**
     * 删除
     * @param planId 计划id
     * @return
     */
    CommonResult delete(String planId);

    /**
     * 获取列表
     * @param projectId 项目id
     * @return 列表结果
     */
    List<FlowPlan> getProjectFlowPlanList(Long projectId);

    /**
     * 发布计划
     * @param plan 计划
     * @return
     */
    CommonResult publish(FlowPlan plan);

    /**
     * 撤销发布计划
     * @param id 流程id
     * @return
     */
    CommonResult<Object> unPublish(Long id);
    /**
     * 初始化一条计划数据
     * @return
     */
    int initPlan(Long projectId);

    /**
     * 根据项目id 获取已经发布的计划
     * @param projectId 项目id
     * @return 计划信息
     */
    FlowPlan getPlanByProjectId(String projectId);

    /**
     * 查询计划方案基本信息
     * @param planId
     * @return
     */
    FlowPlan getFlowPlanInfoByPlanId(String planId);

    CommonResult<Object> isHavPlan(Long projectId);

}
