package com.haoys.user.mapper;

import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.model.ProjectOrgRole;
import com.haoys.user.model.ProjectOrgRoleExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectOrgRoleMapper {
    long countByExample(ProjectOrgRoleExample example);

    int deleteByExample(ProjectOrgRoleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectOrgRole record);

    int insertSelective(ProjectOrgRole record);

    List<ProjectOrgRole> selectByExample(ProjectOrgRoleExample example);

    ProjectOrgRole selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectOrgRole record, @Param("example") ProjectOrgRoleExample example);

    int updateByExample(@Param("record") ProjectOrgRole record, @Param("example") ProjectOrgRoleExample example);

    int updateByPrimaryKeySelective(ProjectOrgRole record);

    int updateByPrimaryKey(ProjectOrgRole record);


    List<ProjectRoleQuery> selectProjectOrgRoleList(ProjectRoleQuery projectRoleQuery);

    /**
     * 根据项目id 和角色名称或者英文名称 校验唯一
     * @param projectId  项目id
     * @param name 角色名称
     * @param enname 英文名称
     * @return 角色信息
     */
    ProjectOrgRole getProjectRoleInfoByRoleNameOrEnName(@Param("projectId")String projectId, @Param("projectOrgId")String projectOrgId, @Param("roleName")String roleName,@Param("enname") String enname);

    /**
     * 删除角色
     * @param roleId 角色id
     * @return
     */
    int deleteProjectOrgRoleById(Long roleId);

    /**
     * 批量插入
     * @param roles 角色
     * @return
     */
    int batchInsert(List<ProjectOrgRole> roles);

    /**
     * 根据项目id、项目研究中心id、角色名称
     * @param projectId
     * @param projectOrgId
     * @param projectRoleName
     * @return
     */
    ProjectOrgRole getProjectOrgRoleByProjectOrgIdAndRoleName(String projectId, String projectOrgId, String projectRoleName);

    List<ProjectOrgRole> getProjectOrgRoleByProjectOrgCode(Long projectId, Long orgId, String projectOrgCode);

    List<ProjectRoleVo> getProjectOrgRoleListByUserId(String projectId, String orgId, String userId);
    
    ProjectOrgRole getProjectOrgRoleByOrgRoleCode(String projectId, String projectOrgId, String code, String loginTenantId, String loginPlatformId);
}
