package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.haoys.user.common.annotation.ProjectVariableRecordFlag;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.DateUtils;
import com.haoys.user.common.util.IphoneValidationUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectUserOrgRoleVo;
import com.haoys.user.domain.expand.FlowFormSetValueExpand;
import com.haoys.user.domain.expand.ProjectVisitUserExpand;
import com.haoys.user.domain.expand.TemplateLabConfigExpand;
import com.haoys.user.domain.param.QueryTesteeExportParam;
import com.haoys.user.domain.param.crf.ProjectTesteeFormProcessParam;
import com.haoys.user.domain.param.crf.TemplateFormDetailParam;
import com.haoys.user.domain.param.project.ProjectTesteeBatchUploadParam;
import com.haoys.user.domain.param.project.ProjectTesteeCustomTableParam;
import com.haoys.user.domain.param.project.ProjectTesteeParam;
import com.haoys.user.domain.param.project.ProjectTesteeResultParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableColumnParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.param.project.ProjectTesteeViewConfigParam;
import com.haoys.user.domain.param.testee.ProjectTesteeFillInfoParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormAndTableParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormParam;
import com.haoys.user.domain.param.testee.QueryTesteeGroupParam;
import com.haoys.user.domain.rcts.RandomizedLayerVo;
import com.haoys.user.domain.vo.ecrf.ProjectFormResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormTableConfigVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableBody;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDvpRuleVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupVariableVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormLogicVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableRowHeadVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.ecrf.TesteeFormConfigVo;
import com.haoys.user.domain.vo.flow.FlowPlanFormVo;
import com.haoys.user.domain.vo.participant.ParticipantHeadRowViewVo;
import com.haoys.user.domain.vo.participant.ProjectParticipantViewConfigVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeVisitPercentVo;
import com.haoys.user.domain.vo.project.ProjectChallengeVo;
import com.haoys.user.domain.vo.project.ProjectTesteeOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.rcts.RandomizedParamsVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeBaseVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeConfigVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportExceptionVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormVariableComplateResultVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeImportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVariableSyncVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeViewConfigVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo;
import com.haoys.user.domain.vo.testee.TesteeChallengeVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.enums.PatientTaskComplateEnum;
import com.haoys.user.enums.ProjectChallengeButtonEnum;
import com.haoys.user.enums.ProjectChallengeEnum;
import com.haoys.user.enums.ProjectPatientEnum;
import com.haoys.user.enums.ProjectTesteeEnum;
import com.haoys.user.enums.ProjectTesteeReviewEnum;
import com.haoys.user.enums.TesteeCustomFieldEnum;
import com.haoys.user.enums.TesteeSortFieldEnum;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.mapper.ProjectOrgInfoMapper;
import com.haoys.user.mapper.ProjectOrgUserRoleMapper;
import com.haoys.user.mapper.ProjectTesteeChallengeMapper;
import com.haoys.user.mapper.ProjectTesteeInfoMapper;
import com.haoys.user.mapper.ProjectTesteeProcessMapper;
import com.haoys.user.mapper.ProjectTesteeResultMapper;
import com.haoys.user.mapper.SystemRoleMapper;
import com.haoys.user.mapper.TemplateFormDvpRuleMapper;
import com.haoys.user.mapper.TemplateFormDvpValMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowFormSetExpand;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.Organization;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectDictionary;
import com.haoys.user.model.ProjectFormAudit;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectPatientResult;
import com.haoys.user.model.ProjectTesteeChallenge;
import com.haoys.user.model.ProjectTesteeChallengeExample;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectTesteeProcess;
import com.haoys.user.model.ProjectTesteeProcessExample;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeResultExample;
import com.haoys.user.model.ProjectTesteeTable;
import com.haoys.user.model.ProjectTesteeTableExample;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitTesteeRecord;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.ProjectVisitUserExample;
import com.haoys.user.model.RctsRandomizedBlindRecord;
import com.haoys.user.model.SystemRole;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormDvpRule;
import com.haoys.user.model.TemplateFormDvpRuleExample;
import com.haoys.user.model.TemplateFormDvpVal;
import com.haoys.user.model.TemplateFormDvpValExample;
import com.haoys.user.model.TemplateFormGroupVariable;
import com.haoys.user.model.TemplateFormTable;
import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.model.TemplateVariableViewBase;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.FlowPlanFormService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectDictionaryService;
import com.haoys.user.service.ProjectFormAuditService;
import com.haoys.user.service.ProjectPatientResultService;
import com.haoys.user.service.ProjectTesteeChallengeService;
import com.haoys.user.service.ProjectTesteeConfigService;
import com.haoys.user.service.ProjectTesteeDvpService;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.ProjectTesteeVariableSyncService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.ProjectVisitUserService;
import com.haoys.user.service.RandomizedControlledTrialsService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormDvpRuleService;
import com.haoys.user.service.TemplateFormGroupService;
import com.haoys.user.service.TemplateFormLogicService;
import com.haoys.user.service.TemplateFormVariableRuleService;
import com.haoys.user.service.TemplateFormVariableViewConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectTesteeInfoServiceImpl extends BaseService implements ProjectTesteeInfoService {

    private final ProjectTesteeInfoMapper projectTesteeInfoMapper;
    private final ProjectTesteeProcessMapper projectTesteeProcessMapper;
    private final SystemUserInfoService systemUserInfoService;
    private final OrganizationService organizationService;
    private final TemplateConfigService templateConfigService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectVisitUserService projectVisitUserService;
    private final ProjectTesteeResultService projectTesteeResultService;
    private final ProjectTesteeTableService projectTesteeTableService;
    private final TemplateFormGroupService templateFormGroupService;
    private final ProjectTesteeChallengeService projectTesteeChallengeService;
    private final ProjectTesteeChallengeMapper projectTesteeChallengeMapper;
    private final ProjectTesteeFileService projectTesteeFileService;
    private final TemplateFormLogicService templateFormLogicService;
    private final ProjectTesteeConfigService projectTesteeConfigService;
    private final ProjectPatientResultService projectPatientResultService;
    private final ProjectUserService projectUserService;
    private final ProjectTesteeDvpService projectTesteeDVPService;
    
    private final RedisTemplateService redisTemplateService;
    private final ProjectBaseManageService projectBaseManageService;
    private final DictionaryService dictionaryService;
    private final ProjectTesteeVariableSyncService projectTesteeVariableSyncService;
    private final TemplateFormVariableViewConfigService templateFormVariableViewConfigService;
    private final ProjectDictionaryService projectDictionaryService;
    private final ProjectOrgInfoMapper projectOrgInfoMapper;
    private final TemplateFormVariableRuleService variableRuleService;
    private final TemplateFormDvpRuleService templateFormDvpRuleService;
    private final TemplateFormDvpRuleMapper templateFormDvpRuleMapper;
    private final FlowPlanService flowPlanService;
    private final FlowFormSetService flowFormSetService;
    private final FlowPlanFormService flowPlanFormService;
    private final ProjectFormAuditService projectFormAuditService;

    private final TemplateFormDvpValMapper templateFormDvpValMapper;
    private final ProjectTesteeResultMapper projectTesteeResultMapper;
    private final ProjectOrgUserRoleMapper projectOrgUserRoleMapper;
    private final SystemRoleMapper systemRoleMapper;
    private static final String challenge_split="_";
    
    private final RandomizedControlledTrialsService randomizedControlledTrialsService;

    /**
     * 是否开启密码策略
     */
    @Value("${form.audit}")
    private Boolean formAudit;

    @Override
    public CustomResult saveProjectTesteeBaseInfo(ProjectTesteeParam projectTesteeParam) {
        CustomResult customResult = new CustomResult();
        if (checkProjectTesteeCode(projectTesteeParam)) {
            customResult.setMessage(projectTesteeParam.getTesteeCode() + BusinessConfig.PROJECT_TESTEE_CODE_FOUND);
            return customResult;
        }
        if (checkProjectTesteeMobile(projectTesteeParam)) {
            customResult.setMessage(projectTesteeParam.getContant() + BusinessConfig.PROJECT_TESTEE_MOBILE_FOUND);
            return customResult;
        }

        ProjectTesteeInfo projectTestee = new ProjectTesteeInfo();
        if(projectTesteeParam.getId() == null) {
            BeanUtils.copyProperties(projectTesteeParam, projectTestee);
            projectTestee.setId(SnowflakeIdWorker.getUuid());
            if(StringUtils.isEmpty(projectTesteeParam.getContant())){
                projectTestee.setContant(null);
            }
            projectTestee.setCreateTime(new Date());
            projectTestee.setCreateUser(projectTesteeParam.getOperator());
            projectTestee.setStatus(BusinessConfig.VALID_STATUS);
            projectTestee.setTenantId(SecurityUtils.getSystemTenantId());
            projectTestee.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectTesteeInfoMapper.insert(projectTestee);
        }else {
            ProjectTesteeInfo projectTesteeInfo = projectTesteeInfoMapper.selectByPrimaryKey(projectTesteeParam.getId());
            if (projectTesteeInfo != null) {
                projectTestee.setId(projectTesteeParam.getId());
                BeanUtils.copyProperties(projectTesteeParam, projectTesteeInfo);
                if(StringUtils.isEmpty(projectTesteeParam.getContant())){
                    projectTesteeInfo.setContant(null);
                }
                projectTesteeInfo.setUpdateTime(new Date());
                projectTesteeInfo.setUpdateUser(projectTesteeParam.getOperator());
                if(StringUtils.isNotBlank(projectTesteeParam.getTenantId())){
                    projectTesteeInfo.setTenantId(projectTesteeParam.getTenantId());
                }
                if(StringUtils.isNotBlank(projectTesteeParam.getPlatformId())){
                    projectTesteeInfo.setPlatformId(projectTesteeParam.getPlatformId());
                }
                projectTesteeInfoMapper.updateByPrimaryKeySelective(projectTesteeInfo);
            }
        }
        String testeeId = projectTesteeParam.getId() == null ? "" : projectTesteeParam.getId().toString();
        ProjectVisitUser projectTesteeUserInfo = getProjectTesteeUserInfo(projectTesteeParam.getProjectId().toString(), projectTesteeParam.getProjectOrgId(), testeeId);
        if(projectTesteeUserInfo == null){
            ProjectVisitUser projectVisitUser = new ProjectVisitUser();
            projectVisitUser.setProjectId(projectTesteeParam.getProjectId());
            projectVisitUser.setTesteeId(projectTestee.getId());
            projectVisitUser.setTesteeCode(projectTesteeParam.getTesteeCode());
            projectVisitUser.setInformedDate(projectTesteeParam.getInformedDate());
            projectVisitUser.setVisitCardNo(projectTesteeParam.getVisitCardNo());
            if(StringUtils.isNotEmpty(projectTesteeParam.getOwnerOrgId())){
                projectVisitUser.setOwnerOrgId(projectTesteeParam.getOwnerOrgId());
                ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectTesteeParam.getOwnerOrgId());
                Organization organizationInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo != null ? projectOrgInfo.getOrgId().toString() : "");
                projectVisitUser.setOwnerOrgName(organizationInfo != null ? organizationInfo.getName() : "");
            }
            //参与者入组研究状态
            projectVisitUser.setResearchStatus(projectTesteeParam.getResearchStatus());
            projectVisitUser.setStatus(BusinessConfig.VALID_STATUS);
            //医生创建病历设置为已审核
            projectVisitUser.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
            projectVisitUser.setBindResult(false);

            if (!projectTesteeParam.getBindTestee()) {
                projectVisitUser.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
            }
            //患者端自建病历需要审核
            if (projectTesteeParam.getBindTesteeCheck()) {
                projectVisitUser.setSelfRecord(false);
                projectVisitUser.setReviewFlag(true);
            } else {
                projectVisitUser.setSelfRecord(true);
                projectVisitUser.setReviewFlag(false);
            }

            //设置项目患者绑定来源
            if (projectTesteeParam.getBindResource() != null) {
                projectVisitUser.setBindResource(projectTesteeParam.getBindResource());
            } else {
                projectVisitUser.setBindResource(Constants.BIND_RESORCE_01);
            }
            projectVisitUser.setOwnerDoctorId(projectTesteeParam.getOperator());
            projectVisitUser.setCreateUserId(projectTesteeParam.getOperator());
            projectVisitUser.setCreateTime(new Date());
            projectVisitUser.setTenantId(SecurityUtils.getSystemTenantId());
            projectVisitUser.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectVisitUserService.insert(projectVisitUser);
        }else{
            ProjectVisitUserExample example = new ProjectVisitUserExample();
            ProjectVisitUserExample.Criteria criteria = example.createCriteria();
            criteria.andProjectIdEqualTo(projectTesteeParam.getProjectId());
            criteria.andTesteeIdEqualTo(projectTesteeParam.getId());
            projectTesteeUserInfo.setTesteeCode(projectTesteeParam.getTesteeCode());
            projectTesteeUserInfo.setInformedDate(projectTesteeParam.getInformedDate());
            projectTesteeUserInfo.setResearchStatus(projectTesteeParam.getResearchStatus());
            projectTesteeUserInfo.setOwnerDoctorId(projectTesteeParam.getOperator());
            if(StringUtils.isNotBlank(projectTesteeParam.getTenantId())){
                projectTesteeUserInfo.setTenantId(projectTesteeParam.getTenantId());
            }
            if(StringUtils.isNotBlank(projectTesteeParam.getPlatformId())){
                projectTesteeUserInfo.setPlatformId(projectTesteeParam.getPlatformId());
            }
            projectVisitUserService.updateByExampleSelective(projectTesteeUserInfo, example);
        }


        //验证sys_user是否存在
        /*SysUser sysUserInfo = systemUserService.getSystemUserInfoByMobile(projectTesteeParam.getContant());
        if (sysUserInfo == null) {
            SysUserParam sysUserParam = new SysUserParam();
            sysUserParam.setUsername("Testee_" + RandomStringUtils.randomAlphanumeric(16).toLowerCase());
            sysUserParam.setOrgId(projectTesteeParam.getOwnerOrgId());
            sysUserParam.setRealName(projectTesteeParam.getRealName());
            sysUserParam.setMobile(projectTesteeParam.getContant());
            sysUserParam.setUserType(Constants.USER_TYPE_VALUE_04);
            sysUserParam.setSealFlag(false);
            sysUserParam.setCreateUserId(projectTesteeParam.getOperator());
            CustomResult customResultVo = systemUserService.saveSystemUser(sysUserParam);
            String message = "init insert into testee: " + customResultVo.getMessage();
            String className = "com.haoys.mis.service.impl.ProjectTesteeServiceImpl";
            String methodName = "saveTestee()";
            AsyncManager.me().execute(AsyncFactory.insertSystemPointLog(projectTestee.getId().toString(), projectTestee.getRealName(), className, methodName, JSON.toJSONString(projectTesteeParam), message));
        }*/
        customResult.setData(projectTestee.getId().toString());
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }


    private boolean checkProjectTesteeCode(ProjectTesteeParam projectTesteeParam) {
        String testeeId = projectTesteeParam.getId() == null ? "" : projectTesteeParam.getId().toString();
        ProjectVisitUser projectVisitUserInfo = projectVisitUserService.getProjectVisitUserInfo(projectTesteeParam.getProjectId().toString(),projectTesteeParam.getOwnerOrgId(), testeeId);
        //ProjectTesteeInfo projectTestee = projectTesteeInfoMapper.selectByPrimaryKey(projectTesteeParam.getId());
        if (projectVisitUserInfo == null || (projectTesteeParam.getTesteeCode() != null && !projectTesteeParam.getTesteeCode().equals(projectVisitUserInfo.getTesteeCode()))) {
            List<ProjectTesteeInfo> projectTestees = projectTesteeInfoMapper.getTesteeCodeByProjectId(projectTesteeParam.getProjectId().toString(), projectTesteeParam.getOwnerOrgId(), projectTesteeParam.getCode());
            return projectTestees.size() > 0;
        }
        return false;
    }

    private boolean checkProjectTesteeMobile(ProjectTesteeParam projectTesteeParam) {
        ProjectTesteeInfo projectTestee = projectTesteeInfoMapper.selectByPrimaryKey(projectTesteeParam.getId());
        if (projectTestee == null || (projectTesteeParam.getContant() != null && !projectTesteeParam.getContant().equals(projectTestee.getContant()))) {
            ProjectTesteeVo projectTesteeVo = getProjectTesteeBaseInfoByMobile(projectTesteeParam.getProjectId().toString(), projectTesteeParam.getContant());
            return projectTesteeVo != null;
        }
        return false;
    }


    @Override
    public CustomResult saveProjectTesteeUserInfo(Long userId, String mobile, String realName) {
        CustomResult customResult = new CustomResult();
        //验证手机号是否存在
        ProjectTesteeVo testee = getTesteeBaseInfoByMobile(null, mobile);
        if (testee != null) {
            throw new ServiceException(ProjectPatientEnum.PROJECT_PATIENT_CODE_01.getCode()+"", ProjectPatientEnum.PROJECT_PATIENT_CODE_01.getMessage());
        }
        ProjectTesteeInfo projectTestee = new ProjectTesteeInfo();
        projectTestee.setId(SnowflakeIdWorker.getUuid());
        projectTestee.setUserId(userId);
        projectTestee.setRealName(realName);
        projectTestee.setContant(mobile);
        projectTestee.setStatus(BusinessConfig.VALID_STATUS);
        projectTestee.setCreateTime(new Date());
        projectTestee.setCreateUser(userId.toString());
        projectTesteeInfoMapper.insert(projectTestee);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }


    @Override
    public String saveBatchProjectTesteeInfo(List<ProjectTesteeImportVo> dataList, String projectId, String oprator, List<ProjectTesteeExportExceptionVo> errorList) {
        if (CollectionUtil.isEmpty(dataList)) {
            throw new ServiceException("导入参与者数据不能为空");
        }
        LinkedHashMap<String, ProjectTesteeParam> dataMap = new LinkedHashMap<>();
        int successNum = 0;
        int failureNum = 0;
        StringBuilder msg = new StringBuilder();
        for (ProjectTesteeImportVo projectTesteeImportVo : dataList) {
            ProjectTesteeParam projectTesteeParam = new ProjectTesteeParam();

            BeanUtils.copyProperties(projectTesteeImportVo, projectTesteeParam);
            projectTesteeParam.setProjectId(Long.parseLong(projectId));

            ProjectTesteeExportExceptionVo projectTesteeExportExceptionVo = new ProjectTesteeExportExceptionVo();
            BeanUtils.copyProperties(projectTesteeImportVo, projectTesteeExportExceptionVo);

            if (projectTesteeImportVo.getBirthdayViewValue() != null) {
                String testeeBirthDate = DateUtil.formatDate2String(projectTesteeImportVo.getBirthdayViewValue(), DateUtil.DEFAULTFORMAT);
                projectTesteeExportExceptionVo.setBirthdayViewValueStr(testeeBirthDate);
            }

            if (StringUtils.isEmpty(projectTesteeImportVo.getCode())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者编号不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            //验证code是否存在
            boolean checkProjectTesteeCode = checkProjectTesteeCode(projectTesteeParam);
            if (checkProjectTesteeCode) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者编号已经存在");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            if (StringUtils.isEmpty(projectTesteeImportVo.getRealName())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者姓名不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            //验证手机号
            if (StringUtils.isEmpty(projectTesteeImportVo.getContant())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者联系方式不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }


            if (!IphoneValidationUtil.isPhone(projectTesteeImportVo.getContant())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者手机号不符合规则");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            //验证性别
            if (StringUtils.isEmpty(projectTesteeImportVo.getGender())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者性别不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }
            List<String> gengerList = Arrays.asList("男", "女", "未知");
            if (!gengerList.contains(projectTesteeImportVo.getGender())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者性别只能输入男、女、未知");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }


            //验证出生日期
            if (projectTesteeImportVo.getBirthdayViewValue() == null) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者出生日期不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            String testeeBirthDate = DateUtil.formatDate2String(projectTesteeImportVo.getBirthdayViewValue(), DateUtil.DEFAULTFORMAT);
            projectTesteeExportExceptionVo.setBirthdayViewValueStr(testeeBirthDate);
            if (StringUtils.isEmpty(testeeBirthDate)) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者出生日期格式不符合规则，请设置为yyyy-MM-dd");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }
            projectTesteeParam.setBirthday(projectTesteeImportVo.getBirthdayViewValue());

            //验证所属中心
            if (StringUtils.isEmpty(projectTesteeImportVo.getOwnerOrgName())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者所属中心名称不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            //验证所属主管医生
            if (StringUtils.isEmpty(projectTesteeImportVo.getOwnerDoctorName())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者主管医生不能为空");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }
            List<ProjectUserVo> projectUserList = projectUserService.selectProjectUserList(projectId, "", "", projectTesteeImportVo.getOwnerDoctorName(), "", "", "");
            if (CollectionUtil.isEmpty(projectUserList)) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("未查询到匹配的参与者主管医生");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }

            ProjectUserVo projectUserVo = projectUserList.get(0);
            /*if (!projectTesteeImportVo.getOwnerOrgName().equals(projectUserVo.getOrgName())) {
                failureNum++;
                projectTesteeExportExceptionVo.setMessage("参与者主管医生所在中心与录入中心不匹配");
                errorList.add(projectTesteeExportExceptionVo);
                continue;
            }*/
            projectTesteeParam.setOwnerDoctorId(projectUserVo.getId().toString());
            projectTesteeParam.setOwnerDoctorName(projectUserVo.getRealName());
            //projectTesteeParam.setOwnerOrgId(projectUserVo.getOrgId());
            //projectTesteeParam.setOwnerOrgName(projectUserVo.getOrgName());

            projectTesteeParam.setOperator(oprator);
            projectTesteeParam.setBindTesteeCheck(false);

            //CustomResult customResult = saveTestee(projectTesteeParam);
            dataMap.put(projectTesteeParam.getCode(), projectTesteeParam);
        }
        /*Set<Map.Entry<String, ProjectTesteeParam>> entrySet = dataMap.entrySet();
        for (Map.Entry<String, ProjectTesteeParam> entry : entrySet) {
            CustomResult customResult  = saveTestee(entry.getValue());
            if(BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult.getMessage())){
                successNum++;
            }else{
                failureNum++;
                ProjectTesteeExportExceptionVo projectTesteeExportExceptionVo = new ProjectTesteeExportExceptionVo();
                projectTesteeExportExceptionVo.setMessage(customResult.getMessage());
                errorList.add(projectTesteeExportExceptionVo);
            }
        }*/
        ListIterator<Map.Entry<String, ProjectTesteeParam>> i = new ArrayList<>(dataMap.entrySet()).listIterator(dataMap.size());
        while (i.hasPrevious()) {
            Map.Entry<String, ProjectTesteeParam> entry = i.previous();
            System.out.println("entry.getValue()=================" + JSON.toJSONString(entry.getValue()));
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            CustomResult customResult = saveProjectTesteeBaseInfo(entry.getValue());
            //CustomResult customResult  = new CustomResult();
            if (BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult.getMessage())) {
                successNum++;
            } else {
                failureNum++;
                ProjectTesteeExportExceptionVo projectTesteeExportExceptionVo = new ProjectTesteeExportExceptionVo();
                BeanUtils.copyProperties(entry.getValue(), projectTesteeExportExceptionVo);
                projectTesteeExportExceptionVo.setMessage(customResult.getMessage());
                if (entry.getValue().getBirthday() != null) {
                    projectTesteeExportExceptionVo.setBirthdayViewValueStr(DateUtil.formatDate2(entry.getValue().getBirthday()));
                }
                errorList.add(projectTesteeExportExceptionVo);
            }
        }

        if (failureNum > 0) {
            msg.insert(0, "成功导入 " + successNum + " 条数据，导入失败" + failureNum + "条数据，请核对后重新导入");
        } else {
            msg.insert(0, "成功导入 " + successNum + " 条数据");
        }
        return msg.toString();
    }

    @Override
    public ProjectTesteeVo getTesteeBaseInfoByTesteeCode(String projectId, String testeeCode) {
        return projectTesteeInfoMapper.getTesteeBaseInfoByTesteeCode(projectId, testeeCode);
    }

    @Override
    public CustomResult saveBatchTesteeVisitFormDetail(List<ProjectTesteeBatchUploadParam> projectTesteeResultParams, String userId) {
        CustomResult customResult = new CustomResult();
        for (ProjectTesteeBatchUploadParam projectTesteeBatchUploadParam : projectTesteeResultParams) {
            List<ProjectTesteeBatchUploadParam.TesteeFormResultValue> dataList = projectTesteeBatchUploadParam.getDataList();
            for (ProjectTesteeBatchUploadParam.TesteeFormResultValue testeeFormResultValue : dataList) {

                ProjectTesteeResult projectTesteeResult = new ProjectTesteeResult();
                projectTesteeResult.setProjectId(projectTesteeBatchUploadParam.getProjectId());
                projectTesteeResult.setTesteeId(projectTesteeBatchUploadParam.getTesteeId());

                projectTesteeResult.setVisitId(testeeFormResultValue.getVisitId());
                projectTesteeResult.setFormId(testeeFormResultValue.getFormId());
                projectTesteeResult.setFormDetailId(testeeFormResultValue.getFormDetailId());
                projectTesteeResult.setLabel(testeeFormResultValue.getLabel());
                projectTesteeResult.setFieldName(testeeFormResultValue.getFieldName());
                projectTesteeResult.setFieldValue(testeeFormResultValue.getFieldValue().toString());
                projectTesteeResult.setStatus(BusinessConfig.VALID_STATUS);
                projectTesteeResult.setCreateUser(userId);
                projectTesteeResult.setCreateTime(new Date());
                saveTesteeVisitFormVariable(projectTesteeResult);
            }
        }
        return customResult;
    }

    @Override
    public void updateProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId, String status, String complateStatus, String operator, String tenantId, String platformId) {
        saveProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId, status, operator, complateStatus, complateStatus, null, tenantId, platformId);
    }

    @Override
    public void updateProjectTesteeTableProcess(String projectId, String projectOrgId, String planId, String visitId, String testeeId, String status, String operator) {
        //更新访视表格记录
    }

    @Override
    public CustomResult deleteTesteeFormDetailById(String ids, String opreator) {
        CustomResult customResult = new CustomResult();
        String[] idArray = ids.split(",");
        for (String id : idArray) {
            ProjectTesteeResult testeeFormResult = projectTesteeResultService.getTesteeFormDetailResult(Long.parseLong(id));
            if (testeeFormResult != null) {
                projectTesteeResultService.deleteTesteeFormResultDetailById(Long.parseLong(id));
            }
        }
        return customResult;
    }

    @Override
    public CustomResult updateTesteeFormDetailById(String id, String fieldValue, String opreator) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeResult testeeFormResult = projectTesteeResultService.getTesteeFormDetailResult(Long.parseLong(id));
        if (testeeFormResult != null) {
            testeeFormResult.setFieldValue(fieldValue);
            testeeFormResult.setUpdateUser(opreator);
            testeeFormResult.setUpdateTime(new Date());
            projectTesteeResultService.updateByPrimaryKeySelective(testeeFormResult);
        }
        return customResult;
    }

    @Override
    public void initTesteeFormBaseInfo(String projectId) {
        TemplateFormConfigVo templateTesteeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
        TesteeCustomFieldEnum[] testeeCustomFieldEnums = TesteeCustomFieldEnum.values();
        for (TesteeCustomFieldEnum testeeCustomFieldEnum : testeeCustomFieldEnums) {
            String fieldName = testeeCustomFieldEnum.getFieldName();
            TemplateFormDetailVo templateFormDetailVo = templateConfigService.getTemplateTesteeFormDetailConfig(projectId, templateTesteeFormBaseInfo.getFormId(), fieldName);
            if(templateFormDetailVo != null){continue;}
            TemplateFormDetailParam templateFormDetailParam = new TemplateFormDetailParam();
            templateFormDetailParam.setProjectId(Long.parseLong(projectId));
            templateFormDetailParam.setFormId(Long.parseLong(templateTesteeFormBaseInfo.getFormId()));
            templateFormDetailParam.setLabel(testeeCustomFieldEnum.getFieldLable());
            templateFormDetailParam.setFieldName(fieldName);
            templateFormDetailParam.setType(testeeCustomFieldEnum.getType());
            templateFormDetailParam.setShowTitle(true);
            if(StringUtils.isNotBlank(testeeCustomFieldEnum.getDefaultValue())){
                templateFormDetailParam.setExpand(JSON.toJSONString(testeeCustomFieldEnum.getDefaultValue()));
            }
            if(BusinessConfig.PROJECT_VISIT_CRF_FORM_DATE.equals(testeeCustomFieldEnum.getType())){
                templateFormDetailParam.setExpand("{\"dataFormat\":\"yyyy-MM-dd\",\"isUK\":\"0\"}");
            }
            if(StringUtils.isNotEmpty(testeeCustomFieldEnum.getDicResource())){
                templateFormDetailParam.setDicResource(testeeCustomFieldEnum.getDicResource());
            }
            if(StringUtils.isNotEmpty(testeeCustomFieldEnum.getRefDicId())){
                templateFormDetailParam.setRefDicId(testeeCustomFieldEnum.getRefDicId());
            }
            templateFormDetailParam.setCustomTestee(true);
            templateFormDetailParam.setTitle("system_default");
            templateFormDetailParam.setModel(SnowflakeIdWorker.getUuidValue());
            templateConfigService.saveTemplateTesteeFormDetailConfig(templateFormDetailParam);
        }
    }

    @Override
    public void updateProjectTesteeBaseInfoVariableInputValues(String projectId, String testeeId, String operator, String systemTenantId, String systemPlatformId) {
        // 查询参与者基本表单
        ProjectTesteeVo projectTesteeBaseInfo = this.getProjectTesteeBaseInfo(projectId, testeeId);
        ProjectTesteeInfo projectTesteeParam = new ProjectTesteeInfo();
        BeanUtils.copyProperties(projectTesteeBaseInfo, projectTesteeParam);
        projectTesteeParam.setCreateUser(operator);
        TemplateFormConfigVo templateTesteeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
        List<TemplateFormDetailVo> templateFormConfigBaseVariableList = templateConfigService.getTemplateTesteeFormDetailBaseInfo(projectId, templateTesteeFormBaseInfo.getFormId(), true);
        for (TemplateFormDetailVo templateFormDetailVo : templateFormConfigBaseVariableList) {
            ProjectTesteeResultWrapperVo projectTesteeResult = projectTesteeResultService.getTesteeFormBaseInfoByFormVariableId(projectId, templateTesteeFormBaseInfo.getFormId(), templateFormDetailVo.getId().toString(), testeeId);
            if(projectTesteeResult != null){
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_GENDER.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setGender(projectTesteeResult.getUnitValue());
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_REAL_NAME.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setRealName(projectTesteeResult.getFieldValue());
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_ACRONYM.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setAcronym(projectTesteeResult.getFieldValue());
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_VISIT_CARD_NO.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    ProjectVisitUser projectVisitUserInfo = projectVisitUserService.getProjectVisitUserInfo(projectId, null, testeeId);
                    if(projectVisitUserInfo != null){
                        projectVisitUserInfo.setVisitCardNo(projectTesteeResult.getFieldValue());
                        projectVisitUserService.updateProjectVisitUserById(projectVisitUserInfo);
                    }
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_INFORMED_DATE.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setExpand(projectTesteeResult.getFieldValue());
                }
                // 定制需求
                if(BusinessConfig.QUERY_FIELD_INPUT_VALUE_1.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setInputValue1(projectTesteeResult.getFieldValue());
                }
                if(BusinessConfig.QUERY_FIELD_INPUT_VALUE_2.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setInputValue2(projectTesteeResult.getFieldValue());
                }
                if(BusinessConfig.QUERY_FIELD_INPUT_VALUE_3.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setInputValue3(projectTesteeResult.getFieldValue());
                }
                if(BusinessConfig.QUERY_FIELD_INPUT_VALUE_4.equalsIgnoreCase(templateFormDetailVo.getFieldName())){
                    projectTesteeParam.setInputValue4(projectTesteeResult.getFieldValue());
                }
            }
        }
        updateProjectTesteeBaseInfo(projectTesteeParam);
        // 同步知情同意书
        TemplateFormConfig templateFormConfig = templateConfigService.getProjectTemplateFormICF(projectId, BusinessConfig.PROJECT_TESTE_INFORMED_CONSENT_FORMS);
        if(templateFormConfig != null){
            log.info("synchronize informed consent date");
            FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId);
            ProjectVisitConfig projectVisitConfig = projectVisitConfigService.getProjectVisitConfigByVisitName(projectId, BusinessConfig.PROJECT_TESTE_INFORMED_CONSENT_FORMS_VISIT_NAME);
            if(projectVisitConfig == null){return;}
            TemplateFormDetail templateFormDetail = templateConfigService.getTemplateVariableValueForICFDATE(projectId, templateFormConfig.getId().toString(), BusinessConfig.PROJECT_TESTE_INFORMED_CONSENT_FORMS_DATE);
            //ProjectTesteeResultWrapperVo projectTesteeResult = projectTesteeResultService.getTesteeFormBaseInfoByFormVariableId(projectId, templateFormConfig.getId().toString(), templateFormDetail.getId().toString(), testeeId);
            ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getTesteeFormResultValueByFormDetailId(projectId, flowPlan.getId().toString(), projectVisitConfig.getId().toString(), templateFormConfig.getId().toString(), "", templateFormDetail.getId().toString(), testeeId);
            if(projectTesteeResult == null){
                log.info("modify informed consent date");
                projectTesteeResultService.saveProjectTesteeResult(projectId, projectVisitConfig.getId().toString(), templateFormConfig.getId().toString(), templateFormDetail.getId().toString(), templateFormDetail.getLabel(), templateFormDetail.getFieldName(), null, projectTesteeParam.getExpand(), testeeId, operator, BusinessConfig.TESTEE_INPUT_DATA_FROM_4, systemTenantId, systemPlatformId);
            }
        }
    }

    private void updateProjectTesteeBaseInfo(ProjectTesteeInfo projectTesteeParam) {
        projectTesteeInfoMapper.updateByPrimaryKeySelective(projectTesteeParam);
    }

    @Override
    public CommonPage<ProjectTesteeWrapperVo> getProjectTesteeDataViewList(ProjectTesteeFillInfoParam param) {
        Page<Object> page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<ProjectTesteeWrapperVo> list = this.getProjectTesteeBaseDataViewList(param.getProjectId().toString(), param.getTesteeCode(),
                param.getProjectOrgId(), param.getStatus(),param.getResearchStatus(), "create_time", "desc");
        CommonPage commonPage = commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, list);
        return commonPage;
    }

    @Override
    public int saveProjectTesteeInfo(ProjectTesteeInfo testeeInfo) {
        return projectTesteeInfoMapper.insert(testeeInfo);
    }

    @Override
    public ProjectTesteeOrgVo getTesteeProjectOrgDetail(String projectId, String projectOrgId, String testeeId) {
        ProjectTesteeOrgVo projectTesteeOrgVo = new ProjectTesteeOrgVo();
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        projectTesteeOrgVo.setProjectName(projectBaseInfo.getName());

        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectOrgId);
        Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
        projectTesteeOrgVo.setProjectOrgName(systemOrganizationInfo.getName());

        ProjectTesteeVo projectTesteeBaseInfo = projectTesteeInfoMapper.getProjectTesteeBaseInfoForVersion2(projectId, testeeId);
        projectTesteeOrgVo.setRealName(projectTesteeBaseInfo.getRealName());
        projectTesteeOrgVo.setTesteeCode(projectTesteeBaseInfo.getTesteeCode());
        projectTesteeOrgVo.setVisitCardNo(projectTesteeBaseInfo.getVisitCardNo());
        return projectTesteeOrgVo;
    }

    @Override
    public List<ProjectTesteeInfo> getProjectTesteeListByIds(String projectId, String projectOrgId, List<String> testeeIds) {
        return projectTesteeInfoMapper.getProjectTesteeListByIds(projectId, projectOrgId,testeeIds);
    }

    private void saveTesteeVisitFormVariable(ProjectTesteeResult projectTesteeResult) {
        if (projectTesteeResult.getId() == null) {
            projectTesteeResult.setId(SnowflakeIdWorker.getUuid());
            projectTesteeResultService.saveProjectTesteeBaseFormVariableResult(projectTesteeResult);
        }
    }

    @Override
    public ProjectTesteeVo getProjectTesteeBaseInfoByMobile(String projectId, String mobile) {
        return getTesteeBaseInfoByMobile(projectId, mobile);
    }

    @Override
    public CustomResult updateTesteeReviewStatus(String projectId, String testeeId, String testeeCode, String status, String bindUserId) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeVo projectTesteeVo = getProjectTesteeBaseInfo(projectId, testeeId);
        if (projectTesteeVo == null) {
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        if (StringUtils.isNotEmpty(testeeCode)) {
            ProjectTesteeParam projectTesteeParam = new ProjectTesteeParam();
            BeanUtils.copyProperties(projectTesteeVo, projectTesteeParam);
            projectTesteeParam.setCode(testeeCode);
            projectTesteeParam.setProjectId(Long.parseLong(projectId));
            projectTesteeParam.setBindTesteeCheck(true);
            projectTesteeParam.setBindUserId(bindUserId);
            projectTesteeParam.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
            customResult = saveProjectTesteeBaseInfo(projectTesteeParam);
            if (BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult.getCode())) {
                return customResult;
            }
        }

        ProjectVisitUser projectVisitUser = getProjectTesteeUserInfo(projectId, null, testeeId);
        if (projectVisitUser != null) {
            projectVisitUser.setReviewTime(new Date());
            projectVisitUser.setReviewUserId(bindUserId);
            projectVisitUser.setReviewStatus(status);
            if (ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName().equals(status)) {
                projectVisitUser.setBindResult(true);
            }
            updateProjectVisitUser(projectVisitUser);
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_02, formType = BusinessConfig.PROJECT_PATIENT_FORM)
    public CustomResult saveTesteeReviewConfig(ProjectTesteeViewConfigParam projectTesteeViewConfigParam) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeInfo projectTestee = projectTesteeInfoMapper.selectByPrimaryKey(projectTesteeViewConfigParam.getId());
        ProjectTesteeVo projectTesteeVo = new ProjectTesteeVo();
        if (projectTestee != null) {
            BeanUtils.copyProperties(projectTestee, projectTesteeVo);
        }
        //查询参与者配置信息
        ProjectTesteeConfigVo projectTesteeConfigVo = projectTesteeConfigService.getProjectTesteeConfig(projectTesteeViewConfigParam.getProjectId());
        if (projectTesteeConfigVo == null) {
            customResult.setMessage(BusinessConfig.PROJECT_TESTEE_CONFIG_NOT_FOUND);
            return customResult;
        }
        ProjectTesteeParam projectTesteeParam = new ProjectTesteeParam();
        BeanUtils.copyProperties(projectTesteeViewConfigParam, projectTesteeParam, new String[]{"ownerDoctor", "dataList"});
        projectTesteeParam.setProjectId(Long.parseLong(projectTesteeViewConfigParam.getProjectId()));
        projectTesteeParam.setBindTestee(true);
        customResult = saveProjectTesteeBaseInfo(projectTesteeParam);
        if (!customResult.getMessage().equals(BusinessConfig.RETURN_MESSAGE_DEFAULT)) {
            return customResult;
        }

        String bindConfig = projectTesteeConfigVo.getBindConfig();
        List<TesteeFormConfigVo> testeeFormConfigVoList = JSON.parseArray(bindConfig, TesteeFormConfigVo.class);
        for (TesteeFormConfigVo testeeFormConfigVo : testeeFormConfigVoList) {
            ProjectTesteeResultParam projectTesteeResultParam = new ProjectTesteeResultParam();
            List<ProjectTesteeResultParam.TesteeFormResultValue> testeeFormResultValueList = new ArrayList<>();
            projectTesteeResultParam.setProjectId(Long.parseLong(projectTesteeViewConfigParam.getProjectId()));
            projectTesteeResultParam.setTesteeId(projectTestee.getId());
            projectTesteeResultParam.setOperator(projectTesteeViewConfigParam.getCreateUserId());
            TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(testeeFormConfigVo.getId().toString());
            if (templateFormDetailConfig != null) {
                TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(templateFormDetailConfig.getFormId());
                //projectTesteeResultParam.setVisitId(templateFormConfig.getVisitId());
                projectTesteeResultParam.setFormId(templateFormConfig.getId());
                List<ProjectTesteeViewConfigParam.FormDataParam> dataList = projectTesteeViewConfigParam.getDataList();
                for (ProjectTesteeViewConfigParam.FormDataParam formDataParam : dataList) {
                    if (formDataParam.getLabel().equals(testeeFormConfigVo.getLabel())) {
                        ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue = new ProjectTesteeResultParam.TesteeFormResultValue();
                        // TODO 需求调整 请注意设置访视id来源
                        ProjectTesteeResult testeeFormOneResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectTesteeViewConfigParam.getProjectId(), "", "XXXXX", templateFormConfig.getId().toString(), "", testeeFormConfigVo.getId().toString(), projectTestee.getId().toString());
                        if (testeeFormOneResult != null) {
                            testeeFormResultValue.setTesteeResultId(testeeFormOneResult.getId());
                        }
                        testeeFormResultValue.setFormDetailId(templateFormDetailConfig.getId());
                        testeeFormResultValue.setLabel(testeeFormConfigVo.getLabel());
                        testeeFormResultValue.setFieldName(templateFormDetailConfig.getFieldName());
                        testeeFormResultValue.setFieldValue(formDataParam.getValue());
                        testeeFormResultValueList.add(testeeFormResultValue);
                    }
                }
                projectTesteeResultParam.setDataList(testeeFormResultValueList);
            }
            saveTesteeVisitFormDetail(projectTesteeResultParam);
        }
        return customResult;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_01, formType = BusinessConfig.PROJECT_VISIT_CRF_FORM)
    public CustomResult saveBindTesteeQRCode(String projectId, String userId, String testeeId, String realName, String ownerOrgId, String ownerDoctorId, String selfRecord) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeVo projectTesteeVo = getProjectTesteeBaseInfo(projectId, testeeId);
        if (projectTesteeVo == null) {
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        //设置绑定关系
        ProjectTesteeParam projectTesteeParam = new ProjectTesteeParam();
        BeanUtils.copyProperties(projectTesteeVo, projectTesteeParam);
        projectTesteeParam.setProjectId(Long.parseLong(projectId));
        projectTesteeParam.setBindTestee(true);
        projectTesteeParam.setRealName(realName);
        projectTesteeParam.setOwnerOrgId(ownerOrgId);
        projectTesteeParam.setOwnerDoctorId(ownerDoctorId);
        projectTesteeParam.setBindUserId(userId);

        projectTesteeParam.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_01.getName());
        if (projectTesteeVo.getSelfRecord()) {
            projectTesteeParam.setBindResult(true);
            projectTesteeParam.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
            projectTesteeParam.setBindTesteeCheck(false);
        }
        projectTesteeParam.setSelfRecord("1".equals(selfRecord));
        if ("0".equals(selfRecord)) {
            projectTesteeParam.setBindTesteeCheck(true);
            //问题点 绑定状态 审核状态
            projectTesteeParam.setBindResult(false);
            projectTesteeParam.setBindResource(Constants.BIND_RESORCE_02);
        }
        customResult = saveProjectTesteeBaseInfo(projectTesteeParam);
        if (!customResult.getMessage().equals(BusinessConfig.RETURN_MESSAGE_DEFAULT)) {
            return customResult;
        }
        return customResult;
    }

    @Override
    public ProjectVisitUser getProjectTesteeUserInfo(String projectId, String projectOrgId, String testeeId) {
        return projectVisitUserService.getProjectVisitUserInfo(projectId, projectOrgId, testeeId);
    }

    @Override
    public List<ProjectVisitUser> getProjectVisitUserList(String projectId, String testeeCode) {
        ProjectVisitUserExample example = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        if(StringUtils.isNotEmpty(testeeCode)){criteria.andTesteeCodeEqualTo(testeeCode);}
        criteria.andStatusEqualTo(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_02.getName());
        return projectVisitUserService.selectByExample(example);
    }
    
    
    @Override
    public List<ProjectVisitUser> getProjectUncheckTesteeUserList(String projectId, String testeeCode) {
        return projectTesteeInfoMapper.getProjectUncheckTesteeUserList(projectId, testeeCode);
    }
    
    /**
     * @param projectId
     * @param testeeCode
     * @param realName
     * @return
     */
    @Override
    public List<ProjectVisitUserExpand> getProjectTesteeUserListForBoRui(String projectId, String testeeCode, String realName) {
        // 以参与者维度统计表单提交和审核情况
        return projectTesteeInfoMapper.getProjectTesteeUserListForBoRui(projectId, testeeCode, realName);
    }
    
    @Override
    public List<ProjectVisitUser> getProjectPatientUserList() {
        ProjectVisitUserExample example = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria criteria = example.createCriteria();
        criteria.andBindResultEqualTo(true);
        return projectVisitUserService.selectByExample(example);
    }

    @Override
    public ProjectVisitUser getPatientTaskProjectId(Long testeeId) {
        ProjectVisitUserExample example = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria criteria = example.createCriteria();
        criteria.andTesteeIdEqualTo(testeeId);
        //criteria.andReviewFlagEqualTo(true);
        //criteria.andBindResultEqualTo(true);
        List<ProjectVisitUser> projectVisitUserList = projectVisitUserService.selectByExample(example);
        if (CollectionUtil.isNotEmpty(projectVisitUserList)) {
            ProjectVisitUser projectVisitUser = projectVisitUserList.get(0);
            /*ProjectTesteeConfigVo projectTesteeConfig = projectTesteeConfigService.getProjectTesteeConfig(projectVisitUser.getProjectId().toString());
            if(projectTesteeConfig != null){
                boolean reviewFlag = projectVisitUser.getReviewFlag() == null ? false : projectVisitUser.getReviewFlag();
                if("1".equals(projectTesteeConfig.getBindType()) && reviewFlag){
                    return null;
                }
            }*/
            return projectVisitUser;
        }
        return null;
    }

    @Override
    public List<Map<Integer, Object>> getProjectGenderData(String projectId, String userId) {
        List<String> dataList = new ArrayList<>();
        String orgIds = null;
        List<OrganizationVo> projectUserOrgList = organizationService.getProjectUserOrgList(projectId, userId);
        if (CollectionUtil.isNotEmpty(projectUserOrgList)) {
            for (OrganizationVo organizationVo : projectUserOrgList) {
                dataList.add(organizationVo.getId().toString());
            }
            String orgIdValue = dataList.stream().collect(Collectors.joining(","));
            orgIds = getQueryWrapperParams(orgIdValue);
        }

        ProjectUserInfoWrapper projectUserDataVo = projectUserService.getProjectUserRoleInfoByUserId(projectId, "", userId);
        if (projectUserDataVo != null) {
            /*String ename = projectUserDataVo.getRoleCode();
            if (ProjectRoleEnum.PROJECT_PA.getCode().equals(ename) *//*|| "DM".equals(ename)*//*) {
                List<String> organizationValue = organizationService.getProjectUserOrgListByProjectId(projectId);
                String orgIdValue = organizationValue.stream().collect(Collectors.joining(","));
                orgIds = getQueryWrapperParams(orgIdValue);
            }*/
        }
        return projectTesteeInfoMapper.getProjectGenderData(projectId, userId, orgIds);
    }

    @Override
    public List<Map<Integer, Object>> getProjectAgeDistrbuteData(String projectId, String userId) {
        List<String> dataList = new ArrayList<>();
        String orgIds = null;
        List<OrganizationVo> projectUserOrgList = organizationService.getProjectUserOrgList(projectId, userId);
        if (CollectionUtil.isNotEmpty(projectUserOrgList)) {
            for (OrganizationVo organizationVo : projectUserOrgList) {
                dataList.add(organizationVo.getId().toString());
            }
            String orgIdValue = dataList.stream().collect(Collectors.joining(","));
            orgIds = getQueryWrapperParams(orgIdValue);
        }
        ProjectUserInfoWrapper projectUserDataVo = projectUserService.getProjectUserRoleInfoByUserId(projectId, "", userId);
        if (projectUserDataVo != null) {
            /*String ename = projectUserDataVo.getRoleCode();
            if (ProjectRoleEnum.PROJECT_PA.getCode().equals(ename) *//*|| "DM".equals(ename)*//*) {
                List<String> organizationValue = organizationService.getProjectUserOrgListByProjectId(projectId);
                String orgIdValue = organizationValue.stream().collect(Collectors.joining(","));
                orgIds = getQueryWrapperParams(orgIdValue);
            }*/
        }
        return projectTesteeInfoMapper.getProjectAgeDistrbuteData(projectId, userId, orgIds);
    }

    @Override
    public int getProjectTesteeCount(String projectId, String userId, String orgIds, boolean queryOwnerOrgId) {
        // 查询参与者总量
        Long testeeCount = projectTesteeInfoMapper.getProjectTesteeCount(projectId, orgIds, null);
        return Integer.parseInt(String.valueOf(testeeCount == null ? 0L : testeeCount));
    }

    @Override
    public List<ProjectTesteeProcess> getProjectTesteeProcessList(String projectId, String visitId, String testeeId) {
        ProjectTesteeProcessExample example = new ProjectTesteeProcessExample();
        ProjectTesteeProcessExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        List<ProjectTesteeProcess> projectTesteeProcesses = projectTesteeProcessMapper.selectByExample(example);
        return projectTesteeProcesses;
    }

    @Override
    public ProjectTesteeVisitPercentVo getProjectVisitTesteeComplateStatus(String projectId, String projectOrgId, String planId, String visitId, String testeeId) {
        ProjectTesteeVisitPercentVo projectTesteeVisitPercentVo = new ProjectTesteeVisitPercentVo();
        List<String> complateTesteeList = new ArrayList<>();
        List<String> recordTesteeList = new ArrayList<>();
        List<String> testeeRecordList = new ArrayList<>();
        List<FlowPlanFormVo> templateFormConfigList = flowPlanFormService.getFormConfigListByPlanIdAndVisitId(projectId, planId, visitId, "");
        List<ProjectTesteeProcess> projectTesteeProcessList = getProjectTesteeProcessList(projectId, visitId, testeeId);
        for (ProjectTesteeProcess projectTesteeProcess : projectTesteeProcessList) {
            if (FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode().equals(projectTesteeProcess.getComplateStatus())) {
                complateTesteeList.add(projectTesteeProcess.getTesteeId().toString());
            }
            if (FormVariableComplateStatus.FORM_VAR_FILL.getCode().equals(projectTesteeProcess.getComplateStatus())) {
                recordTesteeList.add(projectTesteeProcess.getTesteeId().toString());
            }
            if (FormVariableComplateStatus.FORM_VAR_FILL.getCode().equals(projectTesteeProcess.getComplateStatus())
                    || FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode().equals(projectTesteeProcess.getComplateStatus())) {
                testeeRecordList.add(projectTesteeProcess.getTesteeId().toString());
            }
        }
        double visitPercentValue = new BigDecimal((double)testeeRecordList.size()/ (double)templateFormConfigList.size()).doubleValue();
        String visitPercent = String.format("%.0f", visitPercentValue * 100);
        projectTesteeVisitPercentVo.setComplateVisitPercent(visitPercent);
        //log.error("###################### complateCount:{}, totalCount:{}, visitPercent:{}", testeeRecordList.size(), templateFormConfigList.size(), visitPercent);
        if (recordTesteeList.size() > 0 || (complateTesteeList.size()>0 && templateFormConfigList.size() > complateTesteeList.size())) {
            projectTesteeVisitPercentVo.setComplateVisitStatus(FormVariableComplateStatus.FORM_VAR_FILL.getCode());
            return projectTesteeVisitPercentVo;
        }
        if (complateTesteeList.size() > 0 && templateFormConfigList.size() == complateTesteeList.size()) {
            projectTesteeVisitPercentVo.setComplateVisitStatus(FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
            return projectTesteeVisitPercentVo;
        }
        projectTesteeVisitPercentVo.setComplateVisitStatus(FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode());
        return projectTesteeVisitPercentVo;
    }

    @Override
    public CustomResult saveProjectTesteeCustomTableRowRecord(ProjectTesteeCustomTableParam projectTesteeCustomTableParam) {
        CustomResult customResult = new CustomResult();

        List<List<ProjectTesteeTableParam.TableRowData>> rowList = projectTesteeCustomTableParam.getRowList();
        for (List<ProjectTesteeTableParam.TableRowData> tableRowData : rowList) {
            ProjectTesteeTableParam projectTesteeTableParam = new ProjectTesteeTableParam();
            BeanUtils.copyProperties(projectTesteeCustomTableParam, projectTesteeTableParam);
            projectTesteeTableParam.setTableRowDataList(tableRowData);
            customResult = saveProjectTesteeTableRowRecord(projectTesteeTableParam);
        }

        /*Semaphore seatSemaphore = new Semaphore(1, true);
        List<List<ProjectTesteeTableParam.TableRowData>> rowList = projectTesteeCustomTableParam.getRowList();
        for (List<ProjectTesteeTableParam.TableRowData> tableRowData : rowList) {
            InsertOcrTableRowInfo insertOcrTableRowInfo = new InsertOcrTableRowInfo(projectTesteeCustomTableParam, tableRowData, seatSemaphore);
            insertOcrTableRowInfo.start();
        }*/
        return customResult;
    }


    class InsertOcrTableRowInfo extends Thread {
        ProjectTesteeCustomTableParam projectTesteeCustomTableParam;
        List<ProjectTesteeTableParam.TableRowData> tableRowData;
        public volatile Semaphore seatSemaphore;

        public InsertOcrTableRowInfo(ProjectTesteeCustomTableParam projectTesteeCustomTableParam, List<ProjectTesteeTableParam.TableRowData> tableRowData, Semaphore seatSemaphore) {
            this.projectTesteeCustomTableParam = projectTesteeCustomTableParam;
            this.tableRowData = tableRowData;
            this.seatSemaphore = seatSemaphore;
        }

        @Override
        public void run() {
            try {
                seatSemaphore.acquire();
                ProjectTesteeTableParam projectTesteeTableParam = new ProjectTesteeTableParam();
                BeanUtils.copyProperties(projectTesteeCustomTableParam, projectTesteeTableParam);
                projectTesteeTableParam.setTableRowDataList(tableRowData);
                saveProjectTesteeTableRowRecord(projectTesteeTableParam);
                //Thread.sleep(500);
                seatSemaphore.release();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public void updateProjectVisitUser(ProjectVisitUser projectVisitUser) {
        ProjectVisitUserExample example = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectVisitUser.getProjectId());
        criteria.andTesteeIdEqualTo(projectVisitUser.getTesteeId());
        List<ProjectVisitUser> projectVisitUsers = projectVisitUserService.selectByExample(example);
        if (CollectionUtil.isNotEmpty(projectVisitUsers)) {
            ProjectVisitUser projectVisitUserVo = projectVisitUsers.get(0);
            BeanUtils.copyProperties(projectVisitUser, projectVisitUserVo);
            projectVisitUserService.updateByExample(projectVisitUserVo, example);
        }
    }


    private ProjectTesteeVo getTesteeBaseInfoByMobile(String projectId, String mobile) {
        ProjectTesteeVo projectTesteeVo = new ProjectTesteeVo();
        ProjectTesteeInfo projectTestee = projectTesteeInfoMapper.getTesteeInfoByMobile(mobile);
        if (projectTestee != null) {
            BeanUtils.copyProperties(projectTestee, projectTesteeVo);
            //查询项目绑定配置类型
            if (StringUtils.isNotEmpty(projectId)) {
                ProjectTesteeConfigVo projectTesteeConfigVo = projectTesteeConfigService.getProjectTesteeConfig(projectId);
                if (projectTesteeConfigVo != null) {
                    projectTesteeVo.setSelfRecord("2".equals(projectTesteeConfigVo.getBindType()));
                }
                ProjectVisitUserExample example = new ProjectVisitUserExample();
                ProjectVisitUserExample.Criteria criteria = example.createCriteria();
                criteria.andProjectIdEqualTo(Long.parseLong(projectId));
                criteria.andTesteeIdEqualTo(projectTestee.getId());
                List<ProjectVisitUser> projectVisitUsers = projectVisitUserService.selectByExample(example);
                if (CollectionUtil.isNotEmpty(projectVisitUsers)) {
                    ProjectVisitUser projectVisitUser = projectVisitUsers.get(0);
                    projectTesteeVo.setReviewStatus(projectVisitUser.getReviewStatus());
                    projectTesteeVo.setBindResource(projectVisitUser.getBindResource());
                }

            }
            return projectTesteeVo;
        }
        return null;
    }

    @Override
    public ProjectTesteeVo getProjectTesteeBaseInfo(String projectId, String testeeId) {
        ProjectTesteeInfo projectTestee = projectTesteeInfoMapper.selectByPrimaryKey(Long.parseLong(testeeId));
        ProjectTesteeVo projectTesteeVo = new ProjectTesteeVo();
        if (projectTestee != null) {
            BeanUtils.copyProperties(projectTestee, projectTesteeVo);
            /*if (StringUtils.isNotBlank(projectTestee.getOwnerOrgId())) {
                Organization organizationInfo = organizationService.getSystemOrganizationInfo(projectTestee.getOwnerOrgId());
                if (organizationInfo != null) {
                    projectTesteeVo.setOwnerOrgName(organizationInfo.getName());
                }
            }
            if (StringUtils.isNotBlank(projectTestee.getOwnerDoctorId())) {
                UserExtendVo userExtendVo = systemUserInfoService.getUserInfoByUserId(projectTestee.getOwnerDoctorId());
                if (userExtendVo != null) {
                    projectTesteeVo.setOwnerDoctorName(userExtendVo.getRealName());
                }
            }*/
            //查询参与者绑定详情
            ProjectVisitUser projectVisitUser = this.getProjectTesteeUserInfo(projectId, null, testeeId);
            if (projectVisitUser != null) {
                projectTesteeVo.setSelfRecord(projectVisitUser.getSelfRecord());
            }

        }
        //查询参与者配置信息
        ProjectTesteeConfigVo projectTesteeConfigVo = projectTesteeConfigService.getProjectTesteeConfig(projectId);
        if (projectTesteeConfigVo == null) {
            return projectTesteeVo;
        }
        List<ProjectTesteeVo.FormOptions> configList = new ArrayList<>();
        String bindConfig = projectTesteeConfigVo.getBindConfig();
        if (StringUtils.isNotEmpty(bindConfig)) {
            List<TesteeFormConfigVo> testeeFormConfigVoList = JSON.parseArray(bindConfig, TesteeFormConfigVo.class);
            for (TesteeFormConfigVo testeeFormConfigVo : testeeFormConfigVoList) {
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(testeeFormConfigVo.getId().toString());
                if (templateFormDetailConfig != null) {
                    ProjectTesteeVo.FormOptions formOptions = new ProjectTesteeVo.FormOptions();
                    if (templateFormDetailConfig.getLabel().equals(testeeFormConfigVo.getLabel())) {
                        formOptions.setId(templateFormDetailConfig.getId());
                        formOptions.setLabel(templateFormDetailConfig.getLabel());
                        formOptions.setKey(templateFormDetailConfig.getFieldName());
                        formOptions.setType(templateFormDetailConfig.getType());
                        formOptions.setOptions(templateFormDetailConfig.getCombobox());
                        formOptions.setRequired(templateFormDetailConfig.getRequired());
                        TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(templateFormDetailConfig.getFormId());
                        // TODO 需求调整 请注册设置访视id来源
                        ProjectTesteeResult testeeFormOneResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", "XXXXX", templateFormConfig.getId().toString(), "", testeeFormConfigVo.getId().toString(), testeeId);
                        if (testeeFormOneResult != null) {
                            formOptions.setValue(testeeFormOneResult.getFieldValue());
                        }

                        //如果是表格类型
                        if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailConfig.getType())) {
                            List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                            //设置头部显示信息
                            List<TemplateTableVo> projectTesteeTableRowList = projectTesteeTableService.getProjectTesteeTableRowHead(templateFormDetailConfig.getFormId().toString(), templateFormDetailConfig.getId().toString(), false);
                            for (TemplateTableVo templateTableVo : projectTesteeTableRowList) {
                                TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                                BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                                templateTableRowHeadVo.setOptions(templateTableVo.getExpand());
                                dataRowHeadList.add(templateTableRowHeadVo);
                            }
                            formOptions.setDataRowHeadList(dataRowHeadList);
                            //设置行记录显示内容 数据记录行号和数据信息
                            // TODO 需求调整 请注册设置访视id来源
                            List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, "", "#######", templateFormDetailConfig.getFormId().toString(), "", "", testeeId, "", null, "", "");
                            List<ProjectTesteeTableBody.RowDataDesc> dataList = new ArrayList<>();
                            for (Long rowNumber : rowNumberList) {
                                List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();

                                ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                                ProjectTesteeTableBody.ProjectTesteeTableData opratorCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                opratorCol.setFieldName(BusinessConfig.PROJECT_FORM_OPERATE_KEY);
                                opratorCol.setLabel(BusinessConfig.PROJECT_FORM_OPERATE_LABEL);
                                opratorCol.setFieldValue(BusinessConfig.PROJECT_FORM_OPERATE_VALUE);
                                ProjectTesteeTableBody.ProjectTesteeTableData rowNumberDataCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                rowNumberDataCol.setFieldName(BusinessConfig.PROJECT_FORM_NUMBER_KEY);
                                rowNumberDataCol.setLabel(BusinessConfig.PROJECT_FORM_NUMBER_LABEL);
                                rowNumberDataCol.setFieldValue(rowNumber.toString());
                                gridList.add(rowNumberDataCol);
                                // TODO 需求调整 请注册设置访视id来源
                                List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeTableListForPage(projectId, "", "#########", templateFormConfig.getId().toString(), "", "", testeeId, rowNumber);
                                for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                                    ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                    BeanUtils.copyProperties(projectTesteeTable, data);
                                    data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                                    rowDataDesc.setCreateTime(DateUtil.formatDate2String(projectTesteeTable.getCreateTime()));
                                    gridList.add(data);
                                }
                                gridList.add(opratorCol);

                                rowDataDesc.setRowNumber(rowNumber.toString());
                                rowDataDesc.setRowData(gridList);
                                dataList.add(rowDataDesc);
                            }
                            formOptions.setRows(JSON.toJSONString(dataList, SerializerFeature.DisableCircularReferenceDetect));
                        }
                        configList.add(formOptions);
                    }
                }
            }
        }
        projectTesteeVo.setDataList(configList);
        return projectTesteeVo;
    }

    @Override
    public CommonPage<ProjectTesteeVo> getProjectTesteeListForPage(String projectId, String code, String realName, String orgId,
                                                                   String ownerDoctor, String status, String reviewStatus,
                                                                   String sortField, String sortType, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectTesteeVo> dataList = new ArrayList();
        CommonPage<ProjectTesteeVo> commonPage = new CommonPage<>();
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);

        if (StringUtils.isBlank(sortField)) {
            sortField = TesteeSortFieldEnum.TESTEE_SORT_FIELD_05.getValue();
        }
        if (StringUtils.isBlank(sortType)) {
            sortType = "desc";
        }
        params.put("sortField", TesteeSortFieldEnum.getTextByCode(sortField));
        params.put("sortType", sortType);
        if (StringUtils.isNotBlank(code)) {
            params.put("code", code);
        }
        if (StringUtils.isNotBlank(realName)) {
            params.put("realName", realName);
        }
        if (StringUtils.isNotBlank(orgId)) {
            String orgIdList = getQueryWrapperParams(orgId);
            params.put("orgId", orgIdList);
        }
        if (StringUtils.isNotBlank(ownerDoctor)) {
            String ownerDoctorList = getQueryWrapperParams(ownerDoctor);
            params.put("ownerDoctor", ownerDoctorList);
        }
        if (StringUtils.isNotBlank(status)) {
            String statusList = getQueryWrapperParams(status);
            params.put("status", statusList);
        }
        if (StringUtils.isNotBlank(reviewStatus)) {
            params.put("reviewStatus", reviewStatus);
        }
        List<ProjectTesteeWrapperVo> testeeList = projectTesteeInfoMapper.getProjectTesteeListForPage(params);
        commonPage.setTotal(page.getTotal());
        commonPage.setTotalPage(page.getPages());
        commonPage.setPageNum(pageNum);
        commonPage.setPageSize(pageSize);
        for (ProjectTesteeWrapperVo projectTestee : testeeList) {
            ProjectTesteeVo projectTesteeVo = new ProjectTesteeVo();
            BeanUtils.copyProperties(projectTestee, projectTesteeVo);
            if (NumberUtil.isLong(projectTestee.getOwnerDoctorId())) {
                SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectTestee.getOwnerDoctorId());
                if (systemUserInfoExtendVo != null) {
                    projectTesteeVo.setOwnerDoctorId(systemUserInfoExtendVo.getRealName());
                }
            }
            String testeeStatus = projectTestee.getStatus();
            if (ProjectTesteeEnum.PROJECT_TESTEE_STATUS_01.getName().equals(testeeStatus)) {
                projectTesteeVo.setStatus(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_01.getValue());
            }
            if (ProjectTesteeEnum.PROJECT_TESTEE_STATUS_02.getName().equals(testeeStatus)) {
                projectTesteeVo.setStatus(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_02.getValue());
            }
            if (ProjectTesteeEnum.PROJECT_TESTEE_STATUS_03.getName().equals(testeeStatus)) {
                projectTesteeVo.setStatus(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_03.getValue());
            }
            if (ProjectTesteeEnum.PROJECT_TESTEE_STATUS_04.getName().equals(testeeStatus)) {
                projectTesteeVo.setStatus(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_04.getValue());
            }
            List<ProjectTesteeVo.VisitConfig> visitConfigList = new ArrayList();
            List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                ProjectTesteeVo.VisitConfig visitConfig = new ProjectTesteeVo.VisitConfig();
                visitConfig.setVisitId(projectVisitConfig.getId().toString());
                visitConfig.setVisitName(projectVisitConfig.getVisitName());
                //查询访视完成进度
                ProjectTesteeVisitPercentVo projectTesteeVisitPercentVo = getProjectVisitTesteeComplateStatus(projectId, orgId, "", projectVisitConfig.getId().toString(), projectTestee.getId().toString());
                visitConfig.setComplateStatus(projectTesteeVisitPercentVo.getComplateVisitStatus());
                //查询访视未关闭质疑总量
                int changleVisitCount = projectTesteeChallengeService.getProjectChallengeUnclosedCount(projectId, projectVisitConfig.getId().toString(), projectTestee.getId().toString(), null);
                visitConfig.setChangleCount(String.valueOf(changleVisitCount));
                visitConfigList.add(visitConfig);
            }
            projectTesteeVo.setVisitList(visitConfigList);

            dataList.add(projectTesteeVo);
        }
        commonPage.setList(dataList);
        return commonPage;
    }

    @Override
    public CommonPage<ProjectTesteeVo> getProjectTesteeBaseListForPage(String projectId, String projectOrgId, String testeeCode, String status,
                                                                       String sortField, String sortType, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectTesteeVo> dataList = new ArrayList();
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);

        requestParamsWrapper(testeeCode, projectOrgId, status, sortField, sortType, params);
        List<ProjectTesteeWrapperVo> testeeList = projectTesteeInfoMapper.getProjectTesteeListForPage(params);
        for (ProjectTesteeWrapperVo projectTestee : testeeList) {
            ProjectTesteeVo projectTesteeVo = new ProjectTesteeVo();
            BeanUtils.copyProperties(projectTestee, projectTesteeVo);
            dataList.add(projectTesteeVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public List<ProjectTesteeWrapperVo> getProjectTesteeBaseInfoList(String projectId, String testeeCode, String projectOrgId) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        requestParamsWrapper(testeeCode, projectOrgId, BusinessConfig.VALID_STATUS, "", "", params);
        return projectTesteeInfoMapper.getProjectTesteeListForPage(params);
    }

    @Override
    public List<ProjectTesteeWrapperVo> getProjectTesteeBaseDataViewList(String projectId, String testeeCode, String orgId, String status,String researchStatus,
                                                                         String sortField, String sortType) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("researchStatus", researchStatus);
        requestParamsWrapper(testeeCode, orgId, status, sortField, sortType, params);
        return projectTesteeInfoMapper.getProjectTesteeListForPage(params);
    }

    @Override
    public CommonPage<ProjectParticipantViewConfigVo> getProjectParticipantListForPage(ParticipantHeadRowViewVo participantHeadRowViewVo, String projectId, String projectOrgId, String testeeCode,
                                                                                       String realName, String enableAppDevice, String inheritor, String Instructor, String Batchnumber, String REDATE, String status, String researchStatus,
                                                                                       String sortType, String sortField, Integer pageNum, Integer pageSize) {
        String createUserId = "";
        if(StringUtils.isEmpty(status)){status = BusinessConfig.VALID_STATUS;}
        if (StringUtils.isBlank(sortField)) {sortField = TesteeSortFieldEnum.TESTEE_SORT_FIELD_06.getValue();}
        if (StringUtils.isBlank(sortType)) { sortType = "desc";}
        if (StringUtils.isNotBlank(enableAppDevice)) { createUserId = SecurityUtils.getUserIdValue();}

        // 查询参与者表单自定义标题记录
        Set<TemplateFormDetailVo> templateTesteeFormHeadRowList = new HashSet<>();
        List<ParticipantHeadRowViewVo.VariableHeadRowConfig> variableHeadRowConfigList = new ArrayList<>();

        TemplateFormConfigVo testeeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
        List<TemplateFormDetailVo> templateTesteeFormDetailList = templateConfigService.getTemplateTesteeFormDetailBaseInfo(projectId, testeeFormBaseInfo.getFormId(), true);
        for (TemplateFormDetailVo templateFormDetailVo : templateTesteeFormDetailList) {
            if (templateFormDetailVo.getShowTitle()) {
                templateTesteeFormHeadRowList.add(templateFormDetailVo);
            }
        }
        if(CollectionUtil.isNotEmpty(templateTesteeFormHeadRowList)){
            templateTesteeFormHeadRowList.forEach(templateFormDetail->{
                ParticipantHeadRowViewVo.VariableHeadRowConfig variableHeadRowConfig = new ParticipantHeadRowViewVo.VariableHeadRowConfig();
                BeanUtils.copyProperties(templateFormDetail, variableHeadRowConfig);
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_REAL_NAME.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_REAL_NAME);
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_ACRONYM.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_ACRONYM);
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_VISIT_CARD_NO.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_VISIT_CARD_NO);
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_GENDER.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_GENDER);
                }
                variableHeadRowConfigList.add(variableHeadRowConfig);
            });
        }
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectParticipantViewConfigVo> projectParticipantList = projectTesteeInfoMapper.getProjectParticipantListForPage(projectId, projectOrgId, realName, testeeCode, createUserId, researchStatus, inheritor, Instructor, Batchnumber, REDATE, sortField, sortType, status);
        FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlan == null){
            participantHeadRowViewVo.setVariableHeadRowList(variableHeadRowConfigList);
            participantHeadRowViewVo.setProjectFlowPublishStatus(flowPlan != null);
            return commonPageListWrapper(pageNum, pageSize, page, projectParticipantList);
        }
        String planId = flowPlan.getId().toString();
        for(ProjectParticipantViewConfigVo projectParticipantViewConfigVo : projectParticipantList) {
            List<ProjectParticipantViewConfigVo.FormVariableConfig> formVariableConfigList = new ArrayList<>();
            for (ParticipantHeadRowViewVo.VariableHeadRowConfig variableHeadRowConfig : variableHeadRowConfigList) {
                ProjectParticipantViewConfigVo.FormVariableConfig formVariableConfig = new ProjectParticipantViewConfigVo.FormVariableConfig();
                // 设置查询参与者基本信息
                ProjectTesteeResultWrapperVo projectTesteeResult;
                String queryKey = RedisKeyContants.PROJECT_TESTEE_VARIABLE + projectParticipantViewConfigVo.getId().toString().concat(DefineConstant.COLON_SIGN).concat(variableHeadRowConfig.getId().toString());
                Object cacheValue = redisTemplateService.get(queryKey);
                if(cacheValue != null){
                    projectTesteeResult = JSON.parseObject(cacheValue.toString(), ProjectTesteeResultWrapperVo.class);
                }else{
                    projectTesteeResult = projectTesteeResultService.getTesteeFormBaseInfoByFormVariableId(projectId, testeeFormBaseInfo.getFormId(), variableHeadRowConfig.getId().toString(), projectParticipantViewConfigVo.getId().toString());
                    redisTemplateService.set(queryKey, JSON.toJSONString(projectTesteeResult));
                }
                if(projectTesteeResult != null){
                    formVariableConfig.setId(projectTesteeResult.getFormDetailId());
                    formVariableConfig.setFieldName(projectTesteeResult.getFieldName());
                    if(StringUtils.isNotBlank(projectTesteeResult.getFieldValue())){
                        formVariableConfig.setValue(projectTesteeResult.getFieldValue());
                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_CHECKBOX);
                        if(variableTypeList.contains(projectTesteeResult.getType())){
                            formVariableConfig.setValue(projectTesteeResult.getFieldText());
                        }
                        formVariableConfig.setTesteeResultId(projectTesteeResult.getId());
                        formVariableConfig.setSort(projectTesteeResult.getSort() == null ? 0 : projectTesteeResult.getSort());
                        formVariableConfigList.add(formVariableConfig);
                    }else{
                        if(variableHeadRowConfig.getBaseVariableId() != null){
                            TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(variableHeadRowConfig.getId().toString());
                            ProjectTesteeVariableSyncVo testeeVariableSync = projectTesteeVariableSyncService.getVariableSyncByBaseVariableId(variableHeadRowConfig.getBaseVariableId());
                            if(testeeVariableSync != null && testeeVariableSync.getId() != null){
                                // String baseVariableId = testeeVariableSync.getBaseVariableId().toString();
                                String baseVisitId = testeeVariableSync.getSourceVisitId().toString();
                                String baseFormId = testeeVariableSync.getSourceFormId().toString();
                                String baseFormDetailId = testeeVariableSync.getSourceVariableId().toString();
                                ProjectTesteeResult testeeFormVariableSyncResult = projectTesteeResultService.getTesteeFormResultValueByFormDetailId(projectId, planId, baseVisitId, baseFormId, "", baseFormDetailId, projectParticipantViewConfigVo.getId().toString());
                                if(testeeFormVariableSyncResult != null){
                                    List<String> variableValueTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT, BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO);
                                    if(StringUtils.isBlank(testeeFormVariableSyncResult.getFieldValue())){
                                        // 查询是否设置了默认值
                                        formVariableConfig.setValue(templateFormDetailConfig.getDefaultValue());
                                        if(variableValueTypeList.contains(templateFormDetailConfig.getType())){
                                            formVariableConfig.setValue(templateFormDetailConfig.getDefaultDicValue());
                                        }
                                    }else {
                                        formVariableConfig.setValue(testeeFormVariableSyncResult.getFieldValue());
                                        formVariableConfig.setUnitValue(testeeFormVariableSyncResult.getUnitValue());
                                        if(variableValueTypeList.contains(templateFormDetailConfig.getType()) || variableValueTypeList.contains(testeeVariableSync.getFiledType())){
                                            if(BusinessConfig.SYSTEM_DICT.equals(templateFormDetailConfig.getDicResource())){
                                                Dictionary dictionaryInfo = dictionaryService.getDictionaryInfo(testeeFormVariableSyncResult.getFieldValue());
                                                if(dictionaryInfo != null){
                                                    formVariableConfig.setValue(dictionaryInfo.getName());
                                                }
                                            }
                                            if(BusinessConfig.PROJECT_DICT.equals(templateFormDetailConfig.getDicResource())){
                                                ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(testeeFormVariableSyncResult.getFieldValue());
                                                if(dictionaryInfo != null){
                                                    formVariableConfig.setValue(dictionaryInfo.getName());
                                                }
                                            }

                                        }
                                    }
                                    formVariableConfig.setSort(projectTesteeResult.getSort() == null ? 0 : projectTesteeResult.getSort());
                                    formVariableConfigList.add(formVariableConfig);
                                }
                            }
                        }
                    }
                }
            }
            if(CollectionUtil.isNotEmpty(formVariableConfigList)){
                formVariableConfigList.sort(Comparator.comparing(ProjectParticipantViewConfigVo.FormVariableConfig::getSort).thenComparing(ProjectParticipantViewConfigVo.FormVariableConfig::getSort));
                projectParticipantViewConfigVo.setFormVariableConfigArrayList(formVariableConfigList);
            }
        }
        variableHeadRowConfigList.sort(Comparator.comparing(ParticipantHeadRowViewVo.VariableHeadRowConfig::getSort).thenComparing(ParticipantHeadRowViewVo.VariableHeadRowConfig::getSort));
        participantHeadRowViewVo.setVariableHeadRowList(variableHeadRowConfigList);
        participantHeadRowViewVo.setProjectFlowPublishStatus(flowPlan != null);
        return commonPageListWrapper(pageNum, pageSize, page, projectParticipantList);
    }

    @Override
    public CommonPage<ProjectTesteeVo> getProjectTesteeBaseInfoListForPage(String projectId, String projectOrgId, String testeeCode, Integer pageNum, Integer pageSize) {
        CommonPage<ProjectTesteeVo> projectTesteeBaseList = getProjectTesteeBaseListForPage(projectId, projectOrgId, testeeCode, BusinessConfig.VALID_STATUS, "", "", pageNum, pageSize);
        return projectTesteeBaseList;
    }

    public void requestParamsWrapper(String testeeCode, String orgId, String status, String sortField, String sortType, Map<String, Object> params) {
        if (StringUtils.isEmpty(sortField)) {
            params.put("sortField", TesteeSortFieldEnum.TESTEE_SORT_FIELD_05.getValue());
        }else{
            params.put("sortField", TesteeSortFieldEnum.getTextByCode(sortField));
        }
        if (StringUtils.isEmpty(sortType)) {
            sortType = "desc";
        }
        params.put("sortType", sortType);
        if (StringUtils.isNotBlank(testeeCode)) {
            params.put("code", testeeCode);
        }
        if (StringUtils.isNotBlank(orgId)) {
            String orgIdList = getQueryWrapperParams(orgId);
            params.put("orgId", orgIdList);
        }
        if (StringUtils.isNotBlank(status)) {
            params.put("status", status);
        }
    }

    @Override
    public CommonPage<ProjectTesteeExportViewVo> getExportProjectTesteeListForPage(String projectId, String code, String realName, String orgId, String ownerDoctor, String status, String sortField, String sortType, String conditionValueInfo, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        StringBuilder conditionValue = new StringBuilder();
        StringBuilder conditionTableValue = new StringBuilder();
        if(StringUtils.isNotEmpty(conditionValueInfo)){
            List<QueryTesteeExportParam> queryTesteeExportParamList = JSON.parseArray(conditionValueInfo, QueryTesteeExportParam.class);
            for (int i = 0; i < queryTesteeExportParamList.size(); i++) {
            //for (QueryTesteeExportParam queryTesteeExportParam : queryTesteeExportParamList) {
                if(StringUtils.isEmpty(queryTesteeExportParamList.get(i).getVisitId())){
                    continue;
                }
                if(BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(queryTesteeExportParamList.get(i).getVarType())){
                    String formTableId = queryTesteeExportParamList.get(i).getFormTableId();
                    TemplateFormTable templateFormTableConfig = templateConfigService.getTemplateFormTableConfigById(Long.parseLong(formTableId));
                    conditionTableValue.append(queryTesteeExportParamList.get(i).getOperatorValue());
                    //conditionTableValue.append(" (project_testee_table.visit_id = " + "'" + queryTesteeExportParam.getVisitId() + "'");
                    //conditionTableValue.append(" and project_testee_table.form_detail_id = " + "'" + queryTesteeExportParam.getFormDetailId() + "'");
                    conditionTableValue.append(" OR project_testee_table.field_name = " + "'" + templateFormTableConfig.getFieldName() +  "'");
                    conditionTableValue.append(" AND project_testee_table.field_value LIKE " + "'%" + queryTesteeExportParamList.get(i).getInputValue() + "%' )");
                }else{
                    TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(queryTesteeExportParamList.get(i).getFormDetailId());
                    // conditionValue.append(queryTesteeExportParam.getOperatorValue());
                    if(i == 0){
                        conditionValue.append(queryTesteeExportParamList.get(i).getOperatorValue());
                    }else{
                        conditionValue.append(" OR ");
                    }
                    conditionValue.append(" (project_testee_result.visit_id = " + "'" + queryTesteeExportParamList.get(i).getVisitId() + "'");
                    conditionValue.append(" AND project_testee_result.field_name = " + "'" + templateFormDetailConfig.getFieldName() + "'");
                    conditionValue.append(" AND project_testee_result.field_value LIKE " + "'%" + queryTesteeExportParamList.get(i).getInputValue() + "%')");
                }
            }
        }
        if(StringUtils.isNotEmpty(conditionValue) && StringUtils.isEmpty(conditionTableValue)){
            conditionTableValue.append(" and project_testee_table.field_value = '---'");
        }
        if(StringUtils.isNotEmpty(conditionTableValue) && StringUtils.isEmpty(conditionValue)){
            conditionValue.append(" and project_testee_result.field_value = '---'");
        }
        List<ProjectTesteeExportViewVo> projectTesteeList = projectTesteeResultService.getExportProjectTesteeListForPage(projectId, code, realName, orgId, ownerDoctor, status, sortField, sortType, conditionValue.toString(), conditionTableValue.toString());
        for (ProjectTesteeExportViewVo projectTesteeExportViewVo : projectTesteeList) {
            if(StringUtils.isNotEmpty(conditionValue) || StringUtils.isNotEmpty(conditionTableValue)){
                List<ProjectTesteeExportViewVo.FormVariableVo> varValueList = projectTesteeResultService.getExportProjectTesteeVariableValue(projectId, projectTesteeExportViewVo.getTesteeId(), conditionValue.toString(), conditionTableValue.toString());
                log.error("varValueList:{}",JSON.toJSONString(varValueList));
                projectTesteeExportViewVo.setVariableList(varValueList);
            }
        }
        return commonPageListWrapper(pageNum, pageSize, page, projectTesteeList);
    }

    @Override
    public CommonPage<ProjectTesteeExportViewVo> getExportProjectTesteeListForPageVersion2(String projectId, String code, String realName, String orgIds, String ownerDoctor, String status, String sortField, String sortType, String conditionValueInfo, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<ProjectTesteeExportViewVo> returnTesteeInfoList = new ArrayList<>();

        List<QueryTesteeFormParam> queryTesteeFormParamList = new ArrayList<>();
        List<QueryTesteeFormAndTableParam> queryTesteeFormAndTableParamList = new ArrayList<>();

        List<QueryTesteeGroupParam> queryTesteeGroupParamList = new ArrayList<>();
        if(StringUtils.isNotEmpty(conditionValueInfo)){
            List<QueryTesteeExportParam> queryTesteeExportParamList = JSON.parseArray(conditionValueInfo, QueryTesteeExportParam.class);

            List<QueryTesteeFormAndTableParam> finalQueryTesteeFormAndTableParamList = queryTesteeFormAndTableParamList;
            queryTesteeExportParamList.forEach(data->{
                QueryTesteeFormAndTableParam queryTesteeFormAndTableParam = new QueryTesteeFormAndTableParam();
                queryTesteeFormAndTableParam.setGroupId(data.getGroupId());
                queryTesteeFormAndTableParam.setVariableId(StringUtils.isNotEmpty(data.getFormTableId()) ? data.getFormTableId() : data.getFormDetailId());
                queryTesteeFormAndTableParam.setVariableName(data.getConditionName());
                queryTesteeFormAndTableParam.setVariableType(data.getType());
                queryTesteeFormAndTableParam.setFieldName(data.getFieldName());
                queryTesteeFormAndTableParam.setOperatorValue(data.getOperatorValue());
                queryTesteeFormAndTableParam.setRelation(getFieldValueRelationWrapper(data.getRelation()));
                if(!queryTesteeFormAndTableParam.getFieldName().contains(BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER)){
                    queryTesteeFormAndTableParam.setInputValue("'"+data.getInputValue()+"'");
                }else{
                    queryTesteeFormAndTableParam.setInputValue(data.getInputValue());
                }
                finalQueryTesteeFormAndTableParamList.add(queryTesteeFormAndTableParam);
            });
        }
        List<QueryTesteeFormAndTableParam> queryTesteeFormAndTableParamWrapperList = queryTesteeFormAndTableParamList.stream().distinct().collect(Collectors.toList());
        queryTesteeFormAndTableParamList.clear();
        queryTesteeFormAndTableParamList.addAll(queryTesteeFormAndTableParamWrapperList);


        queryTesteeFormAndTableParamList = queryTesteeFormAndTableParamWrapperList.stream().collect(
            collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(QueryTesteeFormAndTableParam::getVariableId))), ArrayList::new)
        );


        if(StringUtils.isEmpty(sortField)){
            sortField = "project_testee.code";
        }
        if(StringUtils.isEmpty(sortType)){
            sortType = "DESC";
        }
        if(StringUtils.isNotEmpty(orgIds)){
            orgIds = getQueryWrapperParams(orgIds);
        }

        List<Map<String,Object>> projectTesteeList = projectTesteeResultService.getExportProjectTesteeListForPageVersion2(projectId, code, realName, orgIds, ownerDoctor, status, sortField, sortType, queryTesteeFormParamList, queryTesteeFormAndTableParamList, queryTesteeGroupParamList);
        projectTesteeList.forEach((testeeInfo->{
            ProjectTesteeExportViewVo projectTesteeExportViewVo = new ProjectTesteeExportViewVo();
            String testeeId = testeeInfo.get("testee_id").toString();
            ProjectTesteeVo testeeBaseInfo = this.getProjectTesteeBaseInfo(projectId, testeeId);
            BeanUtils.copyProperties(testeeBaseInfo, projectTesteeExportViewVo);
            projectTesteeExportViewVo.setJoinGroupTime(testeeBaseInfo.getCreateTime());
            projectTesteeExportViewVo.setTesteeId(testeeId);
            if(StringUtil.isNotEmpty(testeeBaseInfo.getOwnerDoctorId())){
                SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(testeeBaseInfo.getOwnerDoctorId());
                if(systemUserInfoExtendVo != null){
                    projectTesteeExportViewVo.setOwnerDoctorName(systemUserInfoExtendVo.getRealName());
                }
            }
            if(testeeBaseInfo.getBirthday() != null){
                String testeeBirthDay = DateUtil.formatDate2String(testeeBaseInfo.getBirthday(), DateUtil.DEFAULTFORMAT);
                projectTesteeExportViewVo.setBirthdayValue(testeeBirthDay);
            }
            Set<Map.Entry<String, Object>> entrySet = testeeInfo.entrySet();
            List<ProjectTesteeExportViewVo.FormVariableVo> dataList = new ArrayList<>();
            log.info("entrySet:{}", JSON.toJSONString(entrySet));
            for (Map.Entry<String, Object> entry : entrySet) {
                ProjectTesteeExportViewVo.FormVariableVo formVariableVo = new ProjectTesteeExportViewVo.FormVariableVo();
                String fieldName = entry.getKey();
                // input|textarea|radio|checkbox|number|date|slider|group
                if(fieldName.contains("input") || fieldName.contains("textarea") || fieldName.contains("radio") ||
                        fieldName.contains("checkbox") || fieldName.contains("number") || fieldName.contains("date") ||
                        fieldName.contains("slider")){
                    String fieldValue = entry.getValue() == null ? "" : entry.getValue().toString();
                    formVariableVo.setFieldValue(fieldValue);
                    formVariableVo.setGroupId(testeeInfo.get("group_id") == null ? "" : testeeInfo.get("group_id").toString());
                    formVariableVo.setVariableId(testeeInfo.get("variable_id") == null ? "" : testeeInfo.get("variable_id").toString());
                    /*if(StringUtils.isNotEmpty(formVariableVo.getGroupId())){
                        if(fieldName.contains("td")){
                            fieldName = fieldName.split("_")[1] +"_"+ fieldName.split("_")[2];
                        }else{
                            fieldName = fieldName.split("_")[1];
                        }
                    }*/
                    String fieldLabel = templateConfigService.getTemplateFormDetailConfigByFieldName(fieldName);

                    formVariableVo.setLabel(fieldLabel);
                    dataList.add(formVariableVo);
                }
                projectTesteeExportViewVo.setVariableList(dataList);

            }
            returnTesteeInfoList.add(projectTesteeExportViewVo);

        }));
        return commonPageListWrapper(pageNum, pageSize, page, returnTesteeInfoList);
    }

    private String getFieldValueRelationWrapper(String relation) {
        if(DefineConstant.CONTAINS.equals(relation)){ return DefineConstant.CONTAINS_CODE;}
        if(DefineConstant.NOT_CONTAINS.equals(relation)){ return DefineConstant.NOT_CONTAINS_CODE;}
        if(DefineConstant.LESS_THAN.equals(relation)){ return DefineConstant.LESS_THAN_CODE;}
        if(DefineConstant.GREATER_THAN.equals(relation)){ return DefineConstant.GREATER_THAN_CODE;}
        if(DefineConstant.EQUAL.equals(relation)){ return DefineConstant.EQUAL_CODE;}
        if(DefineConstant.NOT_EQUAL.equals(relation)){ return DefineConstant.NOT_EQUAL_CODE;}
        if(DefineConstant.LESS_THAN_EQUAL.equals(relation)){ return DefineConstant.LESS_THAN_EQUAL_CODE;}
        if(DefineConstant.GREATER_THAN_EQUAL.equals(relation)){ return DefineConstant.GREATER_THAN_EQUAL_CODE;}
        return null;
    }




    @Override
    public CommonPage<ProjectTesteeViewConfigVo> getTesteeReviewListForPage(String projectId, String code, String realName, String orgId,
                                                                            String ownerDoctor, String reviewStatus, Integer pageNum, Integer pageSize) {
        ProjectTesteeConfigVo projectTesteeConfigVo = projectTesteeConfigService.getProjectTesteeConfig(projectId);
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(code)) {
            params.put("code", code);
        }
        if (StringUtils.isNotBlank(realName)) {
            params.put("realName", realName);
        }
        if (StringUtils.isNotBlank(orgId)) {
            String orgIdList = getQueryWrapperParams(orgId);
            params.put("orgId", orgIdList);
        }
        if (StringUtils.isNotBlank(ownerDoctor)) {
            String ownerDoctorList = getQueryWrapperParams(ownerDoctor);
            params.put("ownerDoctor", ownerDoctorList);
        }
        if (StringUtils.isNotBlank(reviewStatus)) {
            params.put("reviewStatusValue", reviewStatus);
        }
        params.put("projectId", projectId);
        //需求变更医生端创建患者不再需要审核操作
        params.put("reviewFlag", true);
        List<ProjectTesteeViewConfigVo> dataList = new ArrayList();
        List<ProjectTesteeWrapperVo> testeeList = projectTesteeInfoMapper.getProjectTesteeListForPage(params);
        for (ProjectTesteeWrapperVo projectTestee : testeeList) {
            ProjectTesteeViewConfigVo projectTesteeViewConfigVo = new ProjectTesteeViewConfigVo();
            BeanUtils.copyProperties(projectTestee, projectTesteeViewConfigVo);
            if (NumberUtil.isLong(projectTestee.getOwnerDoctorId())) {
                SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectTestee.getOwnerDoctorId());
                if (systemUserInfoExtendVo != null) {
                    projectTesteeViewConfigVo.setOwnerDoctor(systemUserInfoExtendVo.getRealName());
                }
            }
            List<ProjectTesteeViewConfigVo.FormData> configList = new ArrayList<>();
            String bindConfig = projectTesteeConfigVo.getBindConfig();
            //Map<String,String> dataMap = JSON.parseObject(bindConfig, new TypeReference<HashMap<String,String>>(){}.getType());
            List<TesteeFormConfigVo> testeeFormConfigVoList = JSON.parseArray(bindConfig, TesteeFormConfigVo.class);
            for (TesteeFormConfigVo testeeFormConfigVo : testeeFormConfigVoList) {
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(testeeFormConfigVo.getId().toString());
                if (templateFormDetailConfig != null) {
                    ProjectTesteeViewConfigVo.FormData formData = new ProjectTesteeViewConfigVo.FormData();
                    if (templateFormDetailConfig.getLabel().equals(testeeFormConfigVo.getLabel())) {
                        formData.setId(testeeFormConfigVo.getId());
                        formData.setLabel(testeeFormConfigVo.getLabel());
                        formData.setKey(templateFormDetailConfig.getFieldName());
                        formData.setType(templateFormDetailConfig.getType());
                        formData.setOptions(templateFormDetailConfig.getCombobox());
                        formData.setRequired(templateFormDetailConfig.getRequired());
                        TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(templateFormDetailConfig.getFormId());
                        // TODO 需求调整 请注册设置访视id来源
                        ProjectTesteeResult testeeFormOneResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", "############", templateFormConfig.getId().toString(), "", testeeFormConfigVo.getId().toString(), projectTestee.getId().toString());
                        if (testeeFormOneResult != null) {
                            formData.setValue(testeeFormOneResult.getFieldValue());
                        }
                        configList.add(formData);
                    }
                }
            }
            projectTesteeViewConfigVo.setDataList(configList);
            dataList.add(projectTesteeViewConfigVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public List<TemplateFormConfigVo> getTesteeVisitFormList(String templateId, String projectId, String planId, String visitId) {
        List<TemplateFormConfigVo> templateFormConfigList = templateConfigService.getTemplateFormConfigListByPlanId(templateId, projectId, planId, null, null,null, false);
        return templateFormConfigList;
    }

    @Override
    public TesteeChallengeVo getProjectFormChallengeStatus(String projectId, String visitId, String formId, String testeeId) {
        TesteeChallengeVo testeeChallengeVo = new TesteeChallengeVo();
        List<String> challengeNoApplyList = new ArrayList<>();
        List<String> challengeApplyedList = new ArrayList<>();
        List<String> challengeClosedList = new ArrayList<>();

        List<ProjectChallengeVo> challengeDatalist = projectTesteeChallengeService.getProjectChallengeListByFormId(projectId, visitId, formId, testeeId);
        for (ProjectChallengeVo projectTesteeChallenge : challengeDatalist) {
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeNoApplyList.add(projectTesteeChallenge.getId().toString());
            }
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeApplyedList.add(projectTesteeChallenge.getId().toString());
            }
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeClosedList.add(projectTesteeChallenge.getId().toString());
            }
        }
        if (challengeDatalist.size() == 0) {
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_01.getName());
        }
        if (challengeDatalist.size() == 0 || challengeNoApplyList.size() > 0) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        }
        if (challengeNoApplyList.size() == 0 && challengeApplyedList.size() > 0) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        if (challengeDatalist.size() > 0 && challengeDatalist.size() == challengeClosedList.size()) {
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
        }
        return testeeChallengeVo;
    }

    @Override
    public TesteeChallengeVo getProjectFormDetailChallengeStatus(String projectId, String visitId, String formId, String formDetailId, String testeeId) {
        TesteeChallengeVo testeeChallengeVo = new TesteeChallengeVo();
        List<String> challengeNoApplyList = new ArrayList<>();
        List<String> challengeApplyedList = new ArrayList<>();
        List<String> challengeClosedList = new ArrayList<>();
        List<ProjectChallengeVo> challengelist = projectTesteeChallengeService.getProjectChallengeListByDetailId(projectId, visitId, formId, formDetailId, testeeId);
        for (ProjectChallengeVo projectTesteeChallenge : challengelist) {
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeNoApplyList.add(projectTesteeChallenge.getId().toString());
            }
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeApplyedList.add(projectTesteeChallenge.getId().toString());
            }
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeClosedList.add(projectTesteeChallenge.getId().toString());
            }
        }
        if (challengelist.size() == 0) {
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_01.getName());
        }
        if (challengelist.size() == 0 || challengeNoApplyList.size() > 0) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        }
        if (challengeNoApplyList.size() == 0 && challengeApplyedList.size() > 0) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        if (challengelist.size() > 0 && challengelist.size() == challengeClosedList.size()) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        return testeeChallengeVo;
    }

    @Override
    public CustomResult updateProjectTesteeStatus(String projectId, String ownerOrgId, String testeeIds, String researchStatus) {
        CustomResult customResult = new CustomResult();
        if(StringUtils.isNotEmpty(testeeIds)){
            String[] testeeIdArrays = testeeIds.split(",");
            for (String testeeId : testeeIdArrays) {
                ProjectTesteeVo projectTesteeVo = getProjectTesteeBaseInfo(projectId, testeeId);
                if (projectTesteeVo == null) {
                    customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                    return customResult;
                }
                // 判断手机号是否存在
                if (StringUtils.isNotEmpty(projectTesteeVo.getContant())) {
                    ProjectTesteeVo testee = getTesteeBaseInfoByMobile(projectId, projectTesteeVo.getContant());
                    if (testee != null) {
                        customResult.setCode(ResultCode.BAD_REQUEST.getCode());
                        customResult.setMessage(BusinessConfig.PROJECT_TESTEE_MOBILE_FOUND);
                        return customResult;
                    }
                }
                ProjectVisitUser projectVisitUser = projectVisitUserService.getProjectDeleteVisitUserInfo(projectId, ownerOrgId, testeeId);
                projectVisitUser.setStatus(BusinessConfig.VALID_STATUS);
                projectVisitUserService.updateProjectVisitUserById(projectVisitUser);
            }
        }
        return customResult;
    }

    @Override
    public CustomResult updateProjectTesteeJoinGroupStatus(String projectId, String projectOrgId, String testeeId) {
        CustomResult customResult = new CustomResult();
        // 查询项目随机配置
        RandomizedParamsVo randomizedParamsConfig = randomizedControlledTrialsService.getRandomizedParamsConfig(NumberUtil.parseLong(projectId));
        if(randomizedParamsConfig == null){
            customResult.setCode(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode());
            customResult.setMessage(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
            return customResult;
        }

        List<String> randomizedLayerValueList = new ArrayList<>();
        String randomizedLayerConfig = randomizedParamsConfig.getRandomizedLayerConfig();
        if(StringUtils.isNotEmpty(randomizedLayerConfig)){
            Map<String, Object> dataMap = JSON.parseObject(randomizedLayerConfig, new TypeReference<Map<String, Object>>(){}.getType());
            String randomizedLayerConfigValue = dataMap.get("list").toString();
            List<RandomizedLayerVo> randomizedLayerVoList = JSON.parseArray(randomizedLayerConfigValue, RandomizedLayerVo.class);
            for (RandomizedLayerVo randomizedLayerVo : randomizedLayerVoList) {
                String visitId = randomizedLayerVo.getVisitId();
                String formId = randomizedLayerVo.getFormId();
                String variableId = randomizedLayerVo.getVariableId();
                FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId);
                ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getTesteeFormResultValueByFormDetailId(projectId, flowPlan.getId().toString(), visitId, formId, "", variableId, testeeId);
                if(projectTesteeResult == null){
                    customResult.setCode(ResultCode.PROJECT_JOIN_GROUP_NOT_FOUND.getCode());
                    customResult.setMessage(ResultCode.PROJECT_JOIN_GROUP_NOT_FOUND.getMessage());
                    return customResult;
                }
                randomizedLayerValueList.add(projectTesteeResult.getUnitValue());
            }
        }

        if(randomizedParamsConfig.getRandomizedMethod().startsWith(BusinessConfig.RANDOM_METHOD_STATIC_TRIALS)){
            RctsRandomizedBlindRecord rctsRandomizedBlindRecord = randomizedControlledTrialsService.getProjectRandomizedConfigNumber(projectId, projectOrgId, testeeId);
            if(rctsRandomizedBlindRecord == null){
                // 查询符合分层因素的盲底数据记录 默认获取第一条记录 expandFactor
                RctsRandomizedBlindRecord rctsRandomizedBlindRecordInfo = randomizedControlledTrialsService.getProjectRandomizedConfigNumber(projectId, projectOrgId, testeeId);
                if(rctsRandomizedBlindRecordInfo == null){
                    String blindId = randomizedControlledTrialsService.getBlindId(NumberUtil.parseLong(projectId), randomizedLayerValueList);
                    rctsRandomizedBlindRecordInfo = randomizedControlledTrialsService.getRandodmBlindRecordByBlindId(blindId);
                    randomizedControlledTrialsService.updateRandomizedBlindRecordBindTetsee(projectId, "", blindId, testeeId, rctsRandomizedBlindRecordInfo.getRandomizedNumber());
                }
            }
        }
        if(randomizedParamsConfig.getRandomizedMethod().equals(BusinessConfig.RANDOM_METHOD_DYNAMIC_TRIALS)){
            ProjectTesteeVo projectTesteeBaseInfo = this.getProjectTesteeBaseInfo(projectId, testeeId);

            RctsRandomizedBlindRecord rctsRandomizedBlindRecord = randomizedControlledTrialsService.getProjectRandomizedConfigNumber(projectId, "", testeeId);
            if(rctsRandomizedBlindRecord == null){
                // 查询符合分层因素的盲底数据记录 默认获取第一条记录 expandFactor
                RctsRandomizedBlindRecord rctsRandomizedBlindRecordInfo = randomizedControlledTrialsService.getProjectRandomizedConfigNumber(projectId, "", testeeId);
                if(rctsRandomizedBlindRecordInfo == null){
                    String blindId = randomizedControlledTrialsService.getBlindId(NumberUtil.parseLong(projectId), randomizedLayerValueList);
                    rctsRandomizedBlindRecordInfo = randomizedControlledTrialsService.getRandodmBlindRecordByBlindId(blindId);
                    randomizedControlledTrialsService.updateRandomizedBlindRecordBindTetsee(projectId, projectOrgId, blindId, testeeId, rctsRandomizedBlindRecordInfo.getRandomizedNumber());
                }
            }


        }
        return customResult;
    }

    @Override
    public CustomResult getTesteeJoinGroupInfo(String projectId, String projectOrgId, String testeeId) {
        CustomResult customResult = new CustomResult();
        RandomizedParamsVo randomizedParamsConfig = randomizedControlledTrialsService.getRandomizedParamsConfig(NumberUtil.parseLong(projectId));
        if(randomizedParamsConfig == null){
            customResult.setCode(ResultCode.SYSTEM_CONFIG_RANDOM_NOT_FOUND.getCode());
            customResult.setMessage(ResultCode.SYSTEM_CONFIG_RANDOM_NOT_FOUND.getMessage());
            return customResult;
        }
        RctsRandomizedBlindRecord rctsRandomizedBlindRecord = randomizedControlledTrialsService.getProjectRandomizedConfigNumber(projectId, projectOrgId, testeeId);
        if(rctsRandomizedBlindRecord != null){
            customResult.setData(rctsRandomizedBlindRecord);
        }
        return customResult;
    }

    @Override
    public CustomResult removeTesteeBaseInfo(String projectId, String testeeId) {
        CustomResult customResult = new CustomResult();
        ProjectVisitUser projectVisitUser = projectVisitUserService.getProjectVisitUserInfo(projectId, null, testeeId);
        if(projectVisitUser != null){
            projectVisitUser.setStatus(BusinessConfig.NO_VALID_STATUS);
            projectVisitUserService.updateProjectVisitUserById(projectVisitUser);
        }
        ProjectTesteeInfo projectTesteeInfo = projectTesteeInfoMapper.selectByPrimaryKey(Long.parseLong(testeeId));
        if(projectTesteeInfo != null){
            projectTesteeInfo.setStatus(BusinessConfig.NO_VALID_STATUS);
            projectTesteeInfo.setUpdateTime(new Date());
            projectTesteeInfoMapper.updateByPrimaryKeySelective(projectTesteeInfo);
        }
        return customResult;
    }


    //TODO 需求不明确 待定
    @Override
    public CustomResult getTesteeProjectFormAndTableResult(String projectId, String testeeId) {
        CustomResult customResult = new CustomResult();
        Map<String, Object> dataMap = new HashMap<>();
        ProjectTesteeResultExample example = new ProjectTesteeResultExample();
        ProjectTesteeResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        /*List<ProjectTesteeResult> projectTesteeResults = projectTesteeResultService.selectByExample(example);
        dataMap.put("submitFormCount", projectTesteeResults.size());*/

        ProjectTesteeTableExample exampleTable = new ProjectTesteeTableExample();
        ProjectTesteeTableExample.Criteria criteriaTable = exampleTable.createCriteria();
        criteriaTable.andProjectIdEqualTo(Long.parseLong(projectId));
        criteriaTable.andTesteeIdEqualTo(Long.parseLong(testeeId));
        List<ProjectTesteeTable> projectTesteeTables = projectTesteeTableService.selectByExample(exampleTable);
        dataMap.put("submitFormTableCount", projectTesteeTables.size());
        customResult.setData(dataMap);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public List<TemplateFormDetailVo> getTesteeVisitFormDetail(String projectId, String planId, String visitId, String formId, String formExpandId, String variableTableId, String projectOrgId, String testeeId, String tableSort, String enableAppDevice) {
        if(StringUtils.isEmpty(planId)){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        }
        // 获取表单的质疑列表
        Map<String, ProjectTesteeChallenge> SystemChallengeList = getProjectSystemChallengeList(projectId, projectOrgId,visitId, formId,testeeId);
        Map<String, ProjectTesteeChallenge> CustomChallengeList = getProjectCustomChallengeList(projectId, projectOrgId,visitId, formId,testeeId);

        StringBuffer ruleStr = new StringBuffer();
        List<TemplateFormVariableRule> formVariableRuleList = variableRuleService.getFormVariableRuleList(projectId, null, formId);

        if (CollectionUtil.isNotEmpty(formVariableRuleList)){
            for (TemplateFormVariableRule templateFormVariableRule : formVariableRuleList) {
                ruleStr.append(templateFormVariableRule.getRuleDetails());
            }
        }

        FlowFormSetValueExpand flowFormSet = flowFormSetService.getFormConfigListByProjectIdAndFormId(projectId, planId, visitId, formId);
        TemplateFormConfigVo templateFormConfigVo = templateConfigService.getTemplateFormConfigBaseInfoByFormId(formId);
        // 多次录入表单-不良时间补偿更新数据
        if(BusinessConfig.TEMPLATE_CONFIG_INPUT_TYPE_2.equals(templateFormConfigVo.getFormType())){
            // flowFormSetService
            FlowFormSetExpand flowFormSetExpandObject = flowFormSetService.getFormSetExpandTemplateCopy(projectId, planId, visitId, formId, testeeId);
            if(flowFormSetExpandObject == null){
                FlowFormSetExpand flowFormSetExpand = new FlowFormSetExpand();
                BeanUtils.copyProperties(flowFormSet,flowFormSetExpand);
                flowFormSetExpand.setId(SnowflakeIdWorker.getUuid());
                flowFormSetExpand.setOwnerBaseId(flowFormSet.getId());
                flowFormSetExpand.setTemplateCopy(true);
                flowFormSetExpand.setCreateTime(new Date());
                flowFormSetExpand.setCreateUser(SecurityUtils.getUserIdValue());
                flowFormSetExpand.setUpdateTime(null);
                flowFormSetExpand.setUpdateUser(null);
                flowFormSetService.saveFormExpandObject(flowFormSetExpand);
            }
        }
        String formCreateUser="";
        //使用模板渲染数据
        List<TemplateFormDetailVo> templateFormDetailConfigList = templateConfigService.getTemplateFormDetailConfigListByFormId(null, formId, variableTableId, "1", "1", "0");
        for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailConfigList) {
            Date createTime = null;
            ProjectTesteeResult projectTesteeResultInfo = projectTesteeResultService.getTesteeFormResultValueByFormDetailId(projectId, planId, visitId, formId, formExpandId, templateFormDetailVo.getId().toString(), testeeId);
            if(projectTesteeResultInfo != null){
                if(StringUtils.isNotEmpty(projectTesteeResultInfo.getCreateUser())){
                    formCreateUser=projectTesteeResultInfo.getCreateUser();
                }
                createTime = projectTesteeResultInfo.getCreateTime();
                templateFormDetailVo.setFieldValue(projectTesteeResultInfo.getFieldValue());
                templateFormDetailVo.setFieldText(projectTesteeResultInfo.getFieldText());
                templateFormDetailVo.setTesteeResultId(projectTesteeResultInfo.getId());
                templateFormDetailVo.setUnitValue(projectTesteeResultInfo.getUnitValue());
                templateFormDetailVo.setUnitResultValue(projectTesteeResultInfo.getUnitValue());
                templateFormDetailVo.setOptionHidden(projectTesteeResultInfo.getOptionHidden());
                templateFormDetailVo.setScoreValue(projectTesteeResultInfo.getScoreValue());
                // 设置录入状态
                templateFormDetailVo.setComplateStatus(StringUtils.isBlank(projectTesteeResultInfo.getFieldValue()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
            }
            // 获取公式计算配置
            TemplateFormVariableRule templateFormVariableRule = variableRuleService.getFormVariableRule(projectId,null, formId, templateFormDetailVo.getId().toString());
            templateFormDetailVo.setVariableRule(templateFormVariableRule);
            // 字段映射配置
            if(projectTesteeResultInfo == null || ObjectUtil.isNull(projectTesteeResultInfo.getFieldValue())){
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(templateFormDetailVo.getId().toString());
                if(templateFormDetailConfig.getBaseVariableId() != null){
                    TemplateFormConfigVo templateTesteeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
                    templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(templateFormDetailConfig.getBaseVariableId().toString());
                    if(templateFormDetailConfig != null){
                        ProjectTesteeResult testeeFormVariableSyncResult = projectTesteeResultService.getTesteeFormBaseInfoByFormVariableId(projectId, templateTesteeFormBaseInfo.getFormId(), templateFormDetailConfig.getId().toString(), testeeId);
                        if(testeeFormVariableSyncResult != null){
                            if(StringUtils.isBlank(testeeFormVariableSyncResult.getFieldValue())){
                                // 查询是否设置了默认值
                                templateFormDetailVo.setFieldValue(templateFormDetailConfig.getDefaultValue());
                                List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                if(variableTypeList.contains(templateFormDetailConfig.getType())){
                                    templateFormDetailVo.setFieldValue(templateFormDetailConfig.getDefaultDicValue());
                                }
                            }else {
                                templateFormDetailVo.setFieldValue(testeeFormVariableSyncResult.getFieldValue());
                            }
                        }
                    }
                }
            }
            // 设置查询选项联动配置
            if(templateFormDetailVo.getEnableViewConfig()){
                List<TemplateVariableViewBase> templateVariableOptionList = templateFormVariableViewConfigService.getTemplateVariableOptionListByVariableId(projectId, formId, templateFormDetailVo.getId().toString(), "");
                templateFormDetailVo.setTemplateVariableViewBaseList(templateVariableOptionList);
            }
            // 如果存在多张图片则全部展示
            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE, BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW, BusinessConfig.PROJECT_VISIT_CRF_FORM_FILE);
            if (variableTypeList.contains(templateFormDetailVo.getType())) {
                List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, null, templateFormDetailVo.getId().toString(), null, "", "", "", "", "", true, "");
                templateFormDetailVo.setVariableImageList(variableImageList);
            }
            // 查询当前访视的计划访视周期
            if (templateFormDetailVo.getLabel().contains(BusinessConfig.PROJECT_LABEL_VISIT_REG_NAME)) {
                ProjectVisitTesteeRecord projectVisitTesteeRecord = projectVisitConfigService.getProjectVisitTesteeRecord(projectId, visitId, testeeId);
                if (projectVisitTesteeRecord != null) {
                    templateFormDetailVo.setNextFollowUpDateTime(projectVisitTesteeRecord.getFollowUpNextTime());
                }
            }
            // 显示逻辑条件
            boolean hiddenValue = templateFormDetailVo.getHidden() != null && templateFormDetailVo.getHidden();
            if (hiddenValue) {
                List<TemplateFormLogicVo> templateFormLogicVoList = templateFormLogicService.getFormVariableLogicList("", projectId, visitId, formId, templateFormDetailVo.getId().toString(), testeeId);
                templateFormDetailVo.setTemplateFormLogicList(templateFormLogicVoList);
            }

            // 设置实验室配置
            List<String> varTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_GROUP, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP, BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE);
            if(!varTypeList.contains(templateFormDetailVo.getType())){
                String labConfigScope = templateFormDetailVo.getLabConfigScope();
                if(StringUtils.isEmpty(labConfigScope)){
                    labConfigScope = flowFormSet.getLabConfigScope();
                }
                TemplateLabConfigExpand templateLabConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigScope, visitId, formId, BusinessConfig.LAB_QUERY_VARIABLE_TYPE, templateFormDetailVo.getType(), testeeId, "", templateFormDetailVo.getId().toString(), "", templateFormDetailVo.getFieldValue());
                templateFormDetailVo.setTemplateLabConfigExpand(templateLabConfigExpand);
            }
            // 设置质疑信息
            templateFormDetailVo.setSystemChallenge(SystemChallengeList.get(templateFormDetailVo.getId().toString()));
            templateFormDetailVo.setCustomChallenge(CustomChallengeList.get(templateFormDetailVo.getId().toString()));
            // 设置变量是否参与计算
            templateFormDetailVo.setIsCompute(StringUtils.contains(ruleStr,templateFormDetailVo.getId().toString()));

            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailVo.getType())) {
                List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                List<TemplateTableVo> projectTesteeTableRowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailVo.getId().toString(), false);
                for (TemplateTableVo templateTableVo : projectTesteeTableRowList) {
                    TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                    BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                    templateTableRowHeadVo.setIdViewValue(templateTableVo.getId().toString());
                    dataRowHeadList.add(templateTableRowHeadVo);
                }
                String formDetailId = templateFormDetailVo.getId().toString();
                List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, planId, visitId, formId, "", formDetailId, testeeId, "0", null, tableSort, "");
                List<ProjectTesteeTableBody.RowDataDesc> testeeTableRowList = new ArrayList<>();
                for (Long rowNumber : rowNumberList) {
                    List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();
                    ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                    List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeTableListForPage(projectId, planId, visitId, formId, formExpandId, formDetailId, testeeId, rowNumber);
                    ProjectTesteeTableBody.ProjectTesteeTableData opratorCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                    opratorCol.setFieldName(BusinessConfig.PROJECT_FORM_OPERATE_KEY);
                    opratorCol.setLabel(BusinessConfig.PROJECT_FORM_OPERATE_LABEL);
                    opratorCol.setFieldValue(BusinessConfig.PROJECT_FORM_OPERATE_VALUE);
                    ProjectTesteeTableBody.ProjectTesteeTableData rowNumberCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                    rowNumberCol.setFieldName(BusinessConfig.PROJECT_FORM_NUMBER_KEY);
                    rowNumberCol.setLabel(BusinessConfig.PROJECT_FORM_NUMBER_LABEL);
                    rowNumberCol.setFieldValue(rowNumber.toString());
                    gridList.add(rowNumberCol);
                    List<String> countList = new ArrayList<>();
                    for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                        if(StringUtils.isNotEmpty(projectTesteeTable.getCreateUser())){
                            formCreateUser=projectTesteeTable.getCreateUser();
                        }
                        ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                        BeanUtils.copyProperties(projectTesteeTable, data);
                        data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                        data.setFormTableViewId(projectTesteeTable.getFormTableId().toString());
                        data.setFieldName(projectTesteeTable.getFieldName());
                        data.setFieldValue(projectTesteeTable.getFieldValue());
                        data.setFieldText(projectTesteeTable.getFieldText());
                        data.setUnitValue(projectTesteeTable.getUnitValue());
                        data.setUnitText(projectTesteeTable.getUnitText());
                        List<String> variableInfoList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                        if(variableInfoList.contains(projectTesteeTable.getType()) && !"1".equals(enableAppDevice)){
                            data.setFieldValue(projectTesteeTable.getFieldText());
                        }
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(projectTesteeTable.getType()) && "1".equals(enableAppDevice)){
                            ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(projectTesteeTable.getUnitValue());
                            if(dictionaryInfo != null){
                                data.setUnitValue(dictionaryInfo.getName());
                            }
                        }

//                        // 查询表格变量规则
//                        TemplateFormVariableRule tRule = variableRuleService.getFormVariableRule(projectId,null, formId, templateFormDetailVo.getId().toString());
//                        data.setVariableRule(tRule);

                        // 如果存在多张图片则全部展示
                        List<String> stringList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE, BusinessConfig.PROJECT_VISIT_CRF_FORM_FILE);
                        if (stringList.contains(projectTesteeTable.getType())) {
                            List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, null, formDetailId, projectTesteeTable.getFormTableId().toString(), rowNumber.toString(), "", "", "", "", true, "");
                            data.setVariableImageList(variableImageList);
                        }
                        // 设置查询选项联动配置
                        boolean enableViewConfig = projectTesteeTable.getEnableViewConfig() != null && projectTesteeTable.getEnableViewConfig();
                        if(enableViewConfig){
                            List<TemplateVariableViewBase> templateVariableOptionList = templateFormVariableViewConfigService.getTemplateVariableOptionListByVariableId(projectId, formId, templateFormDetailVo.getId().toString(), projectTesteeTable.getFormTableId().toString());
                            data.setTemplateVariableViewBaseList(templateVariableOptionList);
                        }

                        // 实验室配置
                        String labConfigScope = projectTesteeTable.getLabConfigScope();
                        if(StringUtils.isEmpty(labConfigScope)){
                            labConfigScope = flowFormSet.getLabConfigScope();
                        }
                        TemplateLabConfigExpand templateLabConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigScope, visitId, formId, BusinessConfig.LAB_QUERY_TABLE_VARIABLE_TYPE, projectTesteeTable.getType(), testeeId, "", templateFormDetailVo.getId().toString(), projectTesteeTable.getFormTableId().toString(), projectTesteeTable.getFieldValue());
                        data.setTemplateLabConfigExpand(templateLabConfigExpand);

                        // 设置质疑信息
                        data.setSystemChallenge(SystemChallengeList.get(projectTesteeTable.getRowNo()+challenge_split+projectTesteeTable.getFormTableId()));
                        data.setCustomChallenge(CustomChallengeList.get(projectTesteeTable.getRowNo()+challenge_split+projectTesteeTable.getFormTableId()));
                        gridList.add(data);
                        if (PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectTesteeTable.getComplateStatus())) {
                            countList.add(projectTesteeTable.getComplateStatus());
                        }
                        // 设置变量是否参与计算
                        data.setIsCompute(StringUtils.contains(ruleStr,projectTesteeTable.getFormTableId().toString()));

                    }
                    gridList.add(opratorCol);
                    rowDataDesc.setRowName("tr" + rowNumber);
                    rowDataDesc.setRowNumber(rowNumber.toString());
                    rowDataDesc.setFormDetailId(templateFormDetailVo.getId().toString());
                    rowDataDesc.setRowData(gridList);
                    rowDataDesc.setCreateTime(DateUtil.formatDate2String(createTime));
                    //查询当前行记录完成状态
                    if (projectTesteeTableRowList.size() > countList.size() && countList.size() > 0) {
                        rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
                    }
                    if (projectTesteeTableRowList.size() == countList.size()) {
                        rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
                    }
                    testeeTableRowList.add(rowDataDesc);
                }
                //templateFormDetailVo.setRows(JSON.toJSONString(testeeTableRowList, SerializerFeature.DisableCircularReferenceDetect));
                templateFormDetailVo.setRows(testeeTableRowList);
            }
            // 读取模版字段组类型 type=dynamic_group 进行解析组装数据
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailVo.getType())) {
                // 查询参与者添加的字段组信息   暂定需求 一个组只有一个表格变量和多个普通变量
                List<TemplateFormDetailVo> templateFormGroupList = new ArrayList<>();
                List<TemplateFormGroupVariableVo> groupListByFormId = templateFormGroupService.getProjectTesteeFormGroupListByFormId(projectId, planId, visitId, formId, "", "", testeeId);
                for (TemplateFormGroupVariableVo templateFormGroupVariableVo : groupListByFormId) {
                    TemplateFormDetailVo templateFormGroupVo = new TemplateFormDetailVo();
                    templateFormGroupVo.setGroupId(templateFormGroupVariableVo.getGroupId());
                    Long groupId = templateFormDetailVo.getId();
                    List<TemplateFormDetailVo> templateFormDetailGroupList = templateConfigService.getTemplateFormDetailByGroupId(formId, groupId.toString(), "", false);
                    for (TemplateFormDetailVo templateFormGroupDetailVo : templateFormDetailGroupList) {
                        TemplateFormGroupVariable templateFormGroupVariable = templateFormGroupService.getTemplateGroupInfoByBaseVariableId(projectId, planId, visitId, formId, templateFormGroupDetailVo.getId().toString(), groupId.toString(), templateFormGroupVariableVo.getGroupId().toString(), testeeId);
                        if(templateFormGroupVariable == null){continue;}
                        ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeGroupResult(projectId, visitId, formId, templateFormGroupVariable.getId().toString(), templateFormGroupVariable.getGroupId().toString(), testeeId);
                        if (projectTesteeResult != null) {
                            templateFormGroupDetailVo.setTesteeResultId(projectTesteeResult.getId());
                            templateFormGroupDetailVo.setFieldValue(projectTesteeResult.getFieldValue());
                            templateFormGroupDetailVo.setFieldText(projectTesteeResult.getFieldText());
                            templateFormGroupDetailVo.setUnitValue(projectTesteeResult.getUnitValue());
                            templateFormGroupDetailVo.setUnitResultValue(projectTesteeResult.getUnitValue());
                        }
                        templateFormGroupDetailVo.setFieldName(templateFormGroupDetailVo.getFieldName());
                        templateFormGroupDetailVo.setBaseVariableId(templateFormGroupDetailVo.getId());
                        templateFormGroupDetailVo.setId(templateFormGroupVariable.getId());
                        templateFormGroupDetailVo.setTesteeGroupId(templateFormGroupVariable.getGroupId());
                        templateFormGroupDetailVo.setGroupName(templateFormGroupVariable.getGroupName());

                        // 查询表格变量规则
                        TemplateFormVariableRule tRule = variableRuleService.getFormVariableRule(projectId, templateFormGroupVariable.getGroupId().toString(),formId, templateFormGroupDetailVo.getId().toString());
                        templateFormGroupDetailVo.setVariableRule(tRule);

                        //如果存在多张图片则全部展示
                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(templateFormGroupDetailVo.getType())) {
                            List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, null, templateFormGroupVariable.getId().toString(), null, "", "testee", "", "", "", true, "");
                            templateFormGroupDetailVo.setVariableImageList(variableImageList);
                        }

                        // 字段组-普通变量实验室配置
                        String labConfigScope = templateFormGroupDetailVo.getLabConfigScope();
                        if(StringUtils.isEmpty(labConfigScope)){
                            labConfigScope = flowFormSet.getLabConfigScope();
                        }
                        TemplateLabConfigExpand templateLabConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigScope, visitId, formId, BusinessConfig.LAB_QUERY_GROUP_VARIABLE_TYPE, templateFormGroupDetailVo.getType(), testeeId, groupId.toString(), templateFormGroupDetailVo.getBaseVariableId().toString(), "", templateFormGroupDetailVo.getFieldValue());
                        templateFormGroupDetailVo.setTemplateLabConfigExpand(templateLabConfigExpand);

                        // 设置质疑信息
                        templateFormGroupDetailVo.setSystemChallenge(SystemChallengeList.get(templateFormGroupDetailVo.getTesteeGroupId()+challenge_split+templateFormGroupDetailVo.getBaseVariableId()));
                        templateFormGroupDetailVo.setCustomChallenge(CustomChallengeList.get(templateFormGroupDetailVo.getTesteeGroupId()+challenge_split+templateFormGroupDetailVo.getBaseVariableId()));
                        // 设置变量是否参与计算
                        templateFormGroupDetailVo.setIsCompute(StringUtils.contains(ruleStr,templateFormGroupDetailVo.getBaseVariableId().toString()));


                        if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormGroupDetailVo.getType())) {
                            List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                            List<TemplateTableVo> dataGroupRowHeadList = new ArrayList<>();
                            List<TemplateTableVo> tableHeadRowList = templateFormGroupDetailVo.getTableHeadRowList();
                            ProjectTesteeFormTableConfigVo projectTesteeFormTableConfig = projectTesteeTableService.getProjectTesteeFormTableConfig(projectId, planId, visitId, formId, templateFormGroupDetailVo.getBaseVariableId().toString(), templateFormGroupVariableVo.getGroupId().toString(), testeeId);
                            List<ProjectTesteeTableVo> projectTesteeTableRowList = projectTesteeFormTableConfig.getTableConfigRow();
                            for (ProjectTesteeTableVo templateTableVo : projectTesteeTableRowList) {
                                TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                                TemplateTableVo templateGroupTableVo = new TemplateTableVo();
                                BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                                BeanUtils.copyProperties(templateTableVo, templateGroupTableVo);
                                templateTableRowHeadVo.setIdViewValue(templateTableVo.getId().toString());
                                dataRowHeadList.add(templateTableRowHeadVo);
                                for (TemplateTableVo tableVo : tableHeadRowList) {
                                    if(tableVo.getFieldName().equals(templateTableVo.getFieldName())){
                                        templateGroupTableVo.setBaseVariableTableId(tableVo.getId());
                                    }
                                }
                                dataGroupRowHeadList.add(templateGroupTableVo);
                            }
                            templateFormGroupDetailVo.setTableHeadRowList(dataGroupRowHeadList);
                            List<Long> tableRowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, planId, visitId, formId, "", templateFormGroupVariable.getId().toString(), testeeId, "1", null, tableSort, templateFormGroupVariable.getGroupId().toString());
                            List<ProjectTesteeTableBody.RowDataDesc> dataList = new ArrayList<>();
                            for (Long rowNumber : tableRowNumberList) {
                                List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();
                                ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                                List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeGroupTableListForPage(projectId, visitId, formId, templateFormGroupVariable.getId().toString(), testeeId, rowNumber);
                                ProjectTesteeTableBody.ProjectTesteeTableData opratorCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                opratorCol.setFieldName(BusinessConfig.PROJECT_FORM_OPERATE_KEY);
                                opratorCol.setLabel(BusinessConfig.PROJECT_FORM_OPERATE_LABEL);
                                opratorCol.setFieldValue(BusinessConfig.PROJECT_FORM_OPERATE_VALUE);
                                ProjectTesteeTableBody.ProjectTesteeTableData rowNumberCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                rowNumberCol.setFieldName(BusinessConfig.PROJECT_FORM_NUMBER_KEY);
                                rowNumberCol.setLabel(BusinessConfig.PROJECT_FORM_NUMBER_LABEL);
                                rowNumberCol.setFieldValue(rowNumber.toString());
                                gridList.add(rowNumberCol);
                                List<String> countList = new ArrayList<>();
                                for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                                    ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                    BeanUtils.copyProperties(projectTesteeTable, data);
                                    data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                                    data.setFormTableViewId(projectTesteeTable.getFormTableId().toString());
                                    data.setUnitValue(projectTesteeTable.getUnitValue());
                                    data.setUnitText(projectTesteeTable.getUnitText());
                                    data.setFieldName(projectTesteeTable.getFieldName());
                                    data.setFieldValue(projectTesteeTable.getFieldValue());
                                    data.setFieldText(projectTesteeTable.getFieldText());

                                    // 查询表格变量规则
                                    TemplateFormVariableRule tRule2 = variableRuleService.getFormVariableRule(projectId, templateFormGroupDetailVo.getGroupId().toString(),formId, projectTesteeTable.getFormTableId().toString());
                                    data.setVariableRule(tRule2);

                                    List<String> variableInfoList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                    if(variableInfoList.contains(projectTesteeTable.getType()) && !"1".equals(enableAppDevice)){
                                        data.setFieldValue(projectTesteeTable.getFieldText());
                                    }
                                    //如果存在多张图片则全部展示
                                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(projectTesteeTable.getType())) {
                                        List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, null, templateFormGroupVariable.getId().toString(), projectTesteeTable.getFormTableId().toString(), rowNumber.toString(), "", "", "", "", true, "");
                                        data.setVariableImageList(variableImageList);
                                    }

                                    // 字段组-表格-实验室配置
                                    String labConfigGroupScope = projectTesteeTable.getLabConfigScope();
                                    if(StringUtils.isEmpty(labConfigScope)){
                                        labConfigGroupScope = flowFormSet.getLabConfigScope();
                                    }
                                    TemplateLabConfigExpand templateLabTableConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigGroupScope, visitId, formId, BusinessConfig.LAB_QUERY_GROUP_TABLE_VARIABLE_TYPE, projectTesteeTable.getType(), testeeId, groupId.toString(), projectTesteeTable.getResourceVariableId().toString(), projectTesteeTable.getResourceTableId().toString(), projectTesteeTable.getFieldValue());
                                    data.setTemplateLabConfigExpand(templateLabTableConfigExpand);

                                    // 设置质疑信息
                                    data.setSystemChallenge(SystemChallengeList.get(templateFormGroupDetailVo.getTesteeGroupId()+challenge_split+projectTesteeTable.getRowNo()+challenge_split+projectTesteeTable.getResourceTableId()));
                                    data.setCustomChallenge(CustomChallengeList.get(templateFormGroupDetailVo.getGroupId()+challenge_split+projectTesteeTable.getRowNo()+challenge_split+projectTesteeTable.getFormTableId()));
                                    gridList.add(data);
                                    if (PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectTesteeTable.getComplateStatus())) {
                                        countList.add(projectTesteeTable.getComplateStatus());
                                    }
                                    // 设置变量是否参与计算
                                    data.setIsCompute(StringUtils.contains(ruleStr,projectTesteeTable.getFormTableId().toString()));

                                }
                                gridList.add(opratorCol);
                                rowDataDesc.setRowNumber(rowNumber.toString());
                                rowDataDesc.setFormDetailId(templateFormGroupVariable.getId().toString());
                                rowDataDesc.setRowData(gridList);
                                //查询当前行记录完成状态
                                if (projectTesteeTableRowList.size() > countList.size() && countList.size() > 0) {
                                    rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
                                }
                                if (projectTesteeTableRowList.size() == countList.size()) {
                                    rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
                                }
                                dataList.add(rowDataDesc);
                            }
                            templateFormGroupDetailVo.setRows(dataList);
                        }
                    }
                    templateFormGroupVo.setGroupVariableList(templateFormDetailGroupList);
                    templateFormGroupList.add(templateFormGroupVo);
                }
                templateFormDetailVo.setTemplateFormGroupList(templateFormGroupList);
            }

            //设置分组信息
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_GROUP.equals(templateFormDetailVo.getType())) {
                Long groupId = templateFormDetailVo.getId();
                List<TemplateFormDetailVo> templateFormDetailList = templateConfigService.getTemplateFormDetailByGroupId(formId, groupId.toString(), "", false);
                for (TemplateFormDetailVo templateFormDetailCustomGroupVo : templateFormDetailList) {
                    Date createTimeGroup = null;
                    ProjectTesteeResult projectTesteeGroupResultInfo = projectTesteeResultService.getTesteeFormResultValueByFormDetailId(projectId, planId, visitId, formId, "", templateFormDetailCustomGroupVo.getId().toString(), testeeId);
                    if (projectTesteeGroupResultInfo != null) {
                        createTimeGroup = projectTesteeGroupResultInfo.getCreateTime();
                        templateFormDetailCustomGroupVo.setFieldValue(projectTesteeGroupResultInfo.getFieldValue());
                        templateFormDetailCustomGroupVo.setTesteeResultId(projectTesteeGroupResultInfo.getId());
                        templateFormDetailCustomGroupVo.setUnitResultValue(projectTesteeGroupResultInfo.getUnitValue());
                    }
                    //如果存在多张图片则全部展示
                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(templateFormDetailCustomGroupVo.getType())) {
                        List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, "", visitId, formId, testeeId, null, templateFormDetailCustomGroupVo.getId().toString(), null, "", "", "", "", "", true, "");
                        templateFormDetailCustomGroupVo.setVariableImageList(variableImageList);
                    }

                    //显示逻辑条件
                    boolean hiddenValueResult = templateFormDetailCustomGroupVo.getHidden() != null && templateFormDetailCustomGroupVo.getHidden();
                    if (hiddenValueResult) {
                        List<TemplateFormLogicVo> formLogicConfigList = templateFormLogicService.getFormVariableLogicList("", projectId, visitId, formId, templateFormDetailCustomGroupVo.getId().toString(), testeeId);
                        templateFormDetailCustomGroupVo.setTemplateFormLogicList(formLogicConfigList);
                    }
                    //设置表单项详情质疑标识
                    TesteeChallengeVo testeeChallengeGroupVo = getProjectFormDetailChallengeStatus(projectId, visitId, formId, templateFormDetailVo.getId().toString(), testeeId);
                    templateFormDetailCustomGroupVo.setChallengeStatus(testeeChallengeGroupVo.getChallengeStatus());
                    templateFormDetailCustomGroupVo.setChallengeButtonStatus(testeeChallengeGroupVo.getChallengeButtonStatus());

                    //查询变量逻辑核查条件
                    List<TemplateFormDvpRuleVo> formDetailDvpContentGroupList = projectTesteeDVPService.getFormDetailDvpContent(projectId, visitId, formId, templateFormDetailCustomGroupVo.getId().toString());
                    if (CollectionUtil.isNotEmpty(formDetailDvpContentGroupList)) {
                        templateFormDetailCustomGroupVo.setTemplateFormDvpRuleList(formDetailDvpContentGroupList);
                    }

                    // 设置质疑信息
                    templateFormDetailCustomGroupVo.setSystemChallenge(SystemChallengeList.get(templateFormDetailCustomGroupVo.getTesteeGroupId()+challenge_split+templateFormDetailCustomGroupVo.getBaseVariableId()));
                    templateFormDetailCustomGroupVo.setCustomChallenge(CustomChallengeList.get(templateFormDetailCustomGroupVo.getTesteeGroupId()+challenge_split+templateFormDetailCustomGroupVo.getBaseVariableId()));
                    templateFormDetailCustomGroupVo.setIsCompute(StringUtils.contains(ruleStr,templateFormDetailCustomGroupVo.getBaseVariableId().toString()));


//                    //查询当前变量是否存在未关闭的系统质疑
//                    List<ProjectChallengeVo> projectChallengeGroupList = projectTesteeChallengeService.getProjectSystemChallageData(projectId, visitId, formId, testeeId, templateFormDetailCustomGroupVo.getId().toString(), null, null, null, ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
//                    if (CollectionUtil.isNotEmpty(proj ectChallengeGroupList)) {
//                        templateFormDetailCustomGroupVo.setProjectChallengeList(projectChallengeGroupList);
//                    }

                    if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailCustomGroupVo.getType())) {
                        List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                        List<TemplateTableVo> projectTesteeTableRowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailCustomGroupVo.getId().toString(), false);
                        for (TemplateTableVo templateTableVo : projectTesteeTableRowList) {
                            TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                            BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                            templateTableRowHeadVo.setIdViewValue(templateTableVo.getId().toString());
                            dataRowHeadList.add(templateTableRowHeadVo);
                        }

                        List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, "", visitId, formId, "", templateFormDetailCustomGroupVo.getId().toString(), testeeId, "", null, tableSort, "");
                        List<ProjectTesteeTableBody.RowDataDesc> dataList = new ArrayList<>();
                        for (Long rowNumber : rowNumberList) {
                            List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();
                            ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                            List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeTableListForPage(projectId, "", visitId, formId, "", templateFormDetailCustomGroupVo.getId().toString(), testeeId, rowNumber);
                            ProjectTesteeTableBody.ProjectTesteeTableData opratorCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                            opratorCol.setFieldName(BusinessConfig.PROJECT_FORM_OPERATE_KEY);
                            opratorCol.setLabel(BusinessConfig.PROJECT_FORM_OPERATE_LABEL);
                            opratorCol.setFieldValue(BusinessConfig.PROJECT_FORM_OPERATE_VALUE);
                            ProjectTesteeTableBody.ProjectTesteeTableData rowNumberCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                            rowNumberCol.setFieldName(BusinessConfig.PROJECT_FORM_NUMBER_KEY);
                            rowNumberCol.setLabel(BusinessConfig.PROJECT_FORM_NUMBER_LABEL);
                            rowNumberCol.setFieldValue(rowNumber.toString());
                            gridList.add(rowNumberCol);
                            List<String> countList = new ArrayList<>();
                            for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                                ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                BeanUtils.copyProperties(projectTesteeTable, data);
                                data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                                data.setFormTableViewId(projectTesteeTable.getFormTableId().toString());
                                data.setUnitValue(projectTesteeTable.getUnitValue());
                                data.setFieldName(projectTesteeTable.getFieldName());
                                data.setFieldValue(projectTesteeTable.getFieldValue());
                                //如果存在多张图片则全部展示
                                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(projectTesteeTable.getType())) {
                                    List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, "", visitId, formId, testeeId, null, templateFormDetailCustomGroupVo.getId().toString(), projectTesteeTable.getFormTableId().toString(), rowNumber.toString(), "", "", "", "", true, "");
                                    data.setVariableImageList(variableImageList);
                                }
                                gridList.add(data);

                                if (PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectTesteeTable.getComplateStatus())) {
                                    countList.add(projectTesteeTable.getComplateStatus());
                                }
                            }
                            //查询表格行记录质疑状态
                            TesteeChallengeVo testeeChallenge = projectTesteeTableService.getProjectTableRowChallengeStatus(projectId, visitId, formId, rowNumber.toString(), testeeId);
                            gridList.add(opratorCol);
                            rowDataDesc.setRowNumber(rowNumber.toString());
                            rowDataDesc.setFormDetailId(templateFormDetailCustomGroupVo.getId().toString());
                            rowDataDesc.setRowData(gridList);
                            rowDataDesc.setCreateTime(DateUtil.formatDate2String(createTimeGroup));
                            rowDataDesc.setChallengeStatus(testeeChallenge.getChallengeStatus());
                            rowDataDesc.setChallengeButtonStatus(testeeChallenge.getChallengeButtonStatus());
                            //查询当前行记录完成状态
                            if (projectTesteeTableRowList.size() > countList.size() && countList.size() > 0) {
                                rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
                            }
                            if (projectTesteeTableRowList.size() == countList.size()) {
                                rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
                            }
                            dataList.add(rowDataDesc);
                        }
                        templateFormDetailCustomGroupVo.setRows(dataList);
                    }
                }
                //templateFormDetailVo.setGroupChildrenList(templateFormDetailList);
            }
        }
        // 设置表单填写人
        for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailConfigList) {
            templateFormDetailVo.setFormCreateUser(formCreateUser);
        }
        return templateFormDetailConfigList;
    }

    /**
     * 获取系统质疑信息
     * @param projectId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    private Map<String, ProjectTesteeChallenge> getProjectSystemChallengeList(String projectId, String projectOrgId, String visitId, String formId, String testeeId) {
        // 获取已经存在的系统质疑信息
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andProjectOrgIdEqualTo(projectOrgId);
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andIfSystemEqualTo(true);
        List<ProjectTesteeChallenge> list = projectTesteeChallengeMapper.selectByExampleWithBLOBs(example);
        Map<String, ProjectTesteeChallenge> challengeMap = new HashMap<>();
        list.forEach(challenge->{
            String key="";
            if (StringUtils.isNotEmpty(challenge.getGroupId())){
                if(challenge.getFormResultTableRowno()!=null){
                    key=challenge.getGroupId()+challenge_split+challenge.getFormResultTableRowno()+challenge_split+challenge.getFormTableId();
                }else {
                    key=challenge.getGroupId()+challenge_split+challenge.getFormDetailId();
                }
            }else if(StringUtils.isEmpty(challenge.getGroupId()) && challenge.getFormResultTableRowno()!=null){
                key=challenge.getFormResultTableRowno()+challenge_split+challenge.getFormTableId();
            }else {
                key=challenge.getFormDetailId().toString();
            }
            if (StringUtils.isNotEmpty(challenge.getGroupId())){
                if(challenge.getFormResultTableRowno()!=null){
                    key=challenge.getGroupId()+challenge_split+challenge.getFormResultTableRowno()+challenge_split+challenge.getFormTableId();
                }else {
                    key=challenge.getGroupId()+challenge_split+challenge.getFormDetailId();
                }
            }else if(StringUtils.isEmpty(challenge.getGroupId()) && challenge.getFormResultTableRowno()!=null){
                key=challenge.getFormResultTableRowno()+challenge_split+challenge.getFormTableId();
            }else {
                key=challenge.getFormDetailId().toString();
            }
            challengeMap.put(key,challenge);
        });
        return challengeMap;
    }


    /**
     * 获取手动质疑信息
     * @param projectId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    private Map<String, ProjectTesteeChallenge> getProjectCustomChallengeList(String projectId, String projectOrgId, String visitId, String formId, String testeeId) {
        Long userId = SecurityUtils.getUserId();
        Map<String, ProjectTesteeChallenge> challengeMap = new HashMap<>();

        if(userId!=null){
            boolean isSP = false;
            List<Long> roleId= new ArrayList<>();
            List<SystemRole> systemRoles = systemRoleMapper.selectRolePermissionByUserId(userId);
            if(systemRoles.size()>0){
                List<String> roles = systemRoles.stream().map(SystemRole::getEnglishName).collect(Collectors.toList());
                if(roles.contains("SP")){
                    isSP = true;
                }
            }
            if(!isSP){
                List<ProjectUserOrgRoleVo> baseSystemRoles = projectOrgUserRoleMapper.getProjectOrgUserRoleListByProjectIdAndUserId(projectId,userId.toString());
                List<Long> roleIds = baseSystemRoles.stream().map(ProjectUserOrgRoleVo::getRoleId).collect(Collectors.toList());
                roleId.addAll(roleIds);
            }
            // 获取已经存在的系统质疑信息
            List<ProjectTesteeChallenge> list = projectTesteeChallengeMapper.selectCustomChallengeList(projectId,projectOrgId,visitId,formId,testeeId,roleId, userId,isSP);
            list.forEach(challenge->{
                String key="";
                if (StringUtils.isNotEmpty(challenge.getGroupId())){
                    if(challenge.getFormResultTableRowno()!=null){
                        key=challenge.getGroupId()+challenge_split+challenge.getFormResultTableRowno()+challenge_split+challenge.getFormTableId();
                    }else {
                        key=challenge.getGroupId()+challenge_split+challenge.getFormDetailId();
                    }
                }else if(StringUtils.isEmpty(challenge.getGroupId()) && challenge.getFormResultTableRowno()!=null){
                    key=challenge.getFormResultTableRowno()+challenge_split+challenge.getFormTableId();
                }else {
                    key=challenge.getFormDetailId().toString();
                }
                challengeMap.put(key,challenge);
            });
        }
        return challengeMap;
    }


    private TemplateLabConfigExpand projectTemplateLabConfigWrapper(String projectId, String projectOrgId, String labConfigScope, String visitId, String formId, String queryGroupAndTableMethod, String variableType, String testeeId, String groupId, String variableId, String tableId, String fieldValue) {
        TemplateLabConfigExpand templateLabConfigExpand = null;
        if("form".equals(labConfigScope)){visitId = "";}
        if(BusinessConfig.LAB_QUERY_VARIABLE_TYPE.equals(queryGroupAndTableMethod)){
            templateLabConfigExpand = templateConfigService.getTemplateLabConfigByVariableId(projectOrgId, labConfigScope, visitId, formId, variableId);
        }
        if(BusinessConfig.LAB_QUERY_TABLE_VARIABLE_TYPE.equals(queryGroupAndTableMethod)){
            templateLabConfigExpand = templateConfigService.getTemplateLabConfigByVariableIdAndTableId(projectOrgId, labConfigScope, visitId, formId, variableId, tableId);
        }
        if(BusinessConfig.LAB_QUERY_GROUP_VARIABLE_TYPE.equals(queryGroupAndTableMethod)){
            templateLabConfigExpand = templateConfigService.getTemplateLabConfigByGroupIdAndVariableId(projectOrgId, labConfigScope, visitId, formId, groupId, variableId);
        }
        if(BusinessConfig.LAB_QUERY_GROUP_TABLE_VARIABLE_TYPE.equals(queryGroupAndTableMethod)){
            templateLabConfigExpand = templateConfigService.getTemplateLabConfigByGroupIdAndTableId(projectOrgId, labConfigScope, visitId, formId, groupId, variableId, tableId);
        }
        if(templateLabConfigExpand != null){
            ProjectTesteeVo projectTesteeBaseInfo = this.getProjectTesteeBaseInfo(projectId, testeeId);
            String genderValue = templateLabConfigExpand.getGenderValue();
            Boolean genderCondition = projectTesteeBaseInfo.getGender() != null && projectTesteeBaseInfo.getGender().equals(genderValue);
            if(BusinessConfig.LAB_TYPE_EXPERIMENT_1.equals(templateLabConfigExpand.getLabType())){
                Double lowerLimitValue = templateLabConfigExpand.getLowerLimitValue().doubleValue();
                Double upperLimitValue = templateLabConfigExpand.getUpperLimitValue().doubleValue();
                Boolean ifLowerContains = templateLabConfigExpand.getIfLowerContains();
                Boolean ifUpperContains = templateLabConfigExpand.getIfUpperContains();
                Map<String, Object> dataMap = new HashMap<>();

                StringBuilder upperLimitValueBuilder = new StringBuilder();
                if(ifUpperContains){
                    upperLimitValueBuilder.append("upperLimitValue < filedValue");
                }else{
                    upperLimitValueBuilder.append("upperLimitValue <= filedValue");
                }
                StringBuilder lowerLimitValueBuilder = new StringBuilder();
                if(ifLowerContains){
                    lowerLimitValueBuilder.append("lowerLimitValue > filedValue");
                }else{
                    lowerLimitValueBuilder.append("lowerLimitValue >= filedValue");
                }
                if(StringUtils.isNotBlank(fieldValue)){
                    boolean number = NumberUtil.isNumber(fieldValue);
                    if(!BusinessConfig.LAB_CONFIG_GENDER_COMMON.equals(genderValue) && genderCondition){
                        if(number){
                            dataMap.put("filedValue", Double.parseDouble(fieldValue));
                            dataMap.put("lowerLimitValue", lowerLimitValue);
                            dataMap.put("upperLimitValue", upperLimitValue);
                            Object compute = compute(upperLimitValueBuilder.toString(), dataMap);
                            if(compute instanceof Boolean){
                                Boolean computeBoolean = Boolean.parseBoolean(compute.toString());
                                templateLabConfigExpand.setUpperLimitState(computeBoolean);
                            }
                            compute = compute(lowerLimitValueBuilder.toString(), dataMap);
                            if(compute instanceof Boolean){
                                Boolean computeBoolean = Boolean.parseBoolean(compute.toString());
                                templateLabConfigExpand.setLowerLimitState(computeBoolean);
                            }
                            if(templateLabConfigExpand.getLowerLimitState() || templateLabConfigExpand.getUpperLimitState()){
                                templateLabConfigExpand.setVariableWarning(true);
                            }
                        }
                    }
                    if(BusinessConfig.LAB_CONFIG_GENDER_COMMON.equals(genderValue)){
                        if(number){
                            dataMap.put("filedValue", Double.parseDouble(fieldValue));
                            dataMap.put("lowerLimitValue", lowerLimitValue);
                            dataMap.put("upperLimitValue", upperLimitValue);
                            Object compute = compute(upperLimitValueBuilder.toString(), dataMap);
                            if(compute instanceof Boolean){
                                Boolean computeBoolean = Boolean.parseBoolean(compute.toString());
                                templateLabConfigExpand.setUpperLimitState(computeBoolean);
                            }
                            compute = compute(lowerLimitValueBuilder.toString(), dataMap);
                            if(compute instanceof Boolean){
                                Boolean computeBoolean = Boolean.parseBoolean(compute.toString());
                                templateLabConfigExpand.setLowerLimitState(computeBoolean);
                            }
                            if(templateLabConfigExpand.getLowerLimitState() || templateLabConfigExpand.getUpperLimitState()){
                                templateLabConfigExpand.setVariableWarning(true);
                            }
                        }
                    }
                }
            }
            if(BusinessConfig.LAB_TYPE_EXPERIMENT_2.equals(templateLabConfigExpand.getLabType())){
                if(!BusinessConfig.LAB_CONFIG_GENDER_COMMON.equals(genderValue) && genderCondition){
                    if(StringUtils.isNotBlank(fieldValue)){
                        // 针对多选判断
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(variableType)){
                            if(!fieldValue.contains(templateLabConfigExpand.getNormalValue())){
                                templateLabConfigExpand.setVariableWarning(true);
                            }
                        }else{
                            if(!fieldValue.equals(templateLabConfigExpand.getNormalValue())){
                                templateLabConfigExpand.setVariableWarning(true);
                            }
                        }
                    }
                }
                if(BusinessConfig.LAB_CONFIG_GENDER_COMMON.equals(genderValue)){
                    if(StringUtils.isNotBlank(fieldValue)){
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(variableType)){
                            if(!fieldValue.contains(templateLabConfigExpand.getNormalValue())){
                                templateLabConfigExpand.setVariableWarning(true);
                            }
                        }else{
                            if(!fieldValue.equals(templateLabConfigExpand.getNormalValue())){
                                templateLabConfigExpand.setVariableWarning(true);
                            }
                        }
                    }
                }
            }
        }
        return templateLabConfigExpand;
    }

    public Object compute(String rule,Map<String, Object> data) {
        Expression compiledExp = AviatorEvaluator.compile(rule);
        return compiledExp.execute(data);
    }

    @Override
    public List<TemplateFormDetailVo> getTesteeVisitFormTableDetailForSync(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String variableId, String variableGroupTableId, String variableTableId, String testeeId, String enableAppDevice, String tableSort) {
        FlowFormSet flowFormSet = flowFormSetService.getFormConfigListByProjectIdAndFormId(projectId, planId, visitId, formId);
        List<TemplateFormDetailVo> templateFormDetailConfigList = templateConfigService.getTemplateFormDetailConfigListByFormId(null, formId, variableId, "1", "1", "0");
        for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailConfigList) {
            //如果是表格类型 填充rows数据列
            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailVo.getType())) {
                //设置表格Header数据定义
                List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                List<TemplateTableVo> projectTesteeTableRowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailVo.getId().toString(), false);
                for (TemplateTableVo templateTableVo : projectTesteeTableRowList) {
                    TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                    BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                    templateTableRowHeadVo.setIdViewValue(templateTableVo.getId().toString());
                    dataRowHeadList.add(templateTableRowHeadVo);
                }
                String formDetailId = templateFormDetailVo.getId().toString();
                ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, planId, visitId, formId, formExpandId, formDetailId, testeeId);
                if(projectTesteeResult != null){
                    templateFormDetailVo.setTesteeResultId(projectTesteeResult.getId());
                }
                List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, planId, visitId, formId, formExpandId, formDetailId, testeeId, "0", null, tableSort, "");
                List<ProjectTesteeTableBody.RowDataDesc> testeeTableRowList = new ArrayList<>();
                for (Long rowNumber : rowNumberList) {
                    List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();
                    ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                    List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeTableListForPage(projectId, planId, visitId, formId, formExpandId, formDetailId, testeeId, rowNumber);
                    List<String> countList = new ArrayList<>();
                    for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                        ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                        BeanUtils.copyProperties(projectTesteeTable, data);
                        data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                        data.setFormTableViewId(projectTesteeTable.getFormTableId().toString());
                        data.setUnitValue(projectTesteeTable.getUnitValue());
                        data.setUnitText(projectTesteeTable.getUnitText());
                        data.setFieldName(projectTesteeTable.getFieldName());
                        data.setFieldValue(projectTesteeTable.getFieldValue());
                        List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                        if(variableTypeList.contains(projectTesteeTable.getType())){
                            data.setFieldValue(projectTesteeTable.getFieldText());
                        }
                        //如果存在多张图片则全部展示
                        List<String> stringList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE, BusinessConfig.PROJECT_VISIT_CRF_FORM_FILE);
                        if (stringList.contains(projectTesteeTable.getType())) {
                            List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, null, formDetailId, projectTesteeTable.getFormTableId().toString(), rowNumber.toString(), "", "", "", "", true, "");
                            data.setVariableImageList(variableImageList);
                        }
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(projectTesteeTable.getType()) && "1".equals(enableAppDevice)){
                            ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(projectTesteeTable.getUnitValue());
                            if(dictionaryInfo != null){
                                data.setUnitText(dictionaryInfo.getName());
                            }
                        }
                        // 实验室配置
                        String labConfigScope = projectTesteeTable.getLabConfigScope();
                        if(StringUtils.isEmpty(labConfigScope)){
                            labConfigScope = flowFormSet.getLabConfigScope();
                        }
                        TemplateLabConfigExpand templateLabConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigScope, visitId, "", BusinessConfig.LAB_QUERY_TABLE_VARIABLE_TYPE, "", testeeId, "", templateFormDetailVo.getId().toString(), projectTesteeTable.getFormTableId().toString(), projectTesteeTable.getFieldValue());
                        data.setTemplateLabConfigExpand(templateLabConfigExpand);

                        gridList.add(data);
                        if (PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectTesteeTable.getComplateStatus())) {
                            countList.add(projectTesteeTable.getComplateStatus());
                        }
                    }
                    rowDataDesc.setRowNumber(rowNumber.toString());
                    rowDataDesc.setFormDetailId(templateFormDetailVo.getId().toString());
                    rowDataDesc.setRowData(gridList);
                    //查询当前行记录完成状态
                    if (projectTesteeTableRowList.size() > countList.size() && countList.size() > 0) {
                        rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
                    }
                    if (projectTesteeTableRowList.size() == countList.size()) {
                        rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
                    }
                    testeeTableRowList.add(rowDataDesc);
                }
                templateFormDetailVo.setRows(testeeTableRowList);
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailVo.getType())) {
                // 查询参与者添加的字段组信息   暂定需求 一个组只有一个表格变量和多个普通变量
                List<TemplateFormDetailVo> templateFormGroupList = new ArrayList<>();
                List<TemplateFormGroupVariableVo> templateFormGroupVariableList = templateFormGroupService.getProjectTesteeFormGroupListByFormId(projectId, planId, visitId, formId, variableGroupTableId, variableTableId, testeeId);
                for (TemplateFormGroupVariableVo templateFormGroupVariableVo : templateFormGroupVariableList) {
                    TemplateFormDetailVo templateFormGroupVo = new TemplateFormDetailVo();
                    templateFormGroupVo.setGroupId(templateFormGroupVariableVo.getGroupId());
                    Long groupId = templateFormDetailVo.getId();
                    List<TemplateFormDetailVo> templateFormDetailGroupList = templateConfigService.getTemplateFormDetailByGroupId(formId, groupId.toString(), variableGroupTableId, false);
                    for (TemplateFormDetailVo templateFormGroupDetailVo : templateFormDetailGroupList) {
                        TemplateFormGroupVariable templateFormGroupVariable = templateFormGroupService.getTemplateGroupInfoByBaseVariableId(projectId, planId, visitId, formId, templateFormGroupDetailVo.getId().toString(), groupId.toString(), templateFormGroupVariableVo.getGroupId().toString(), testeeId);
                        if(templateFormGroupVariable == null){continue;}
                        ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeGroupResult(projectId, visitId, formId, templateFormGroupVariable.getId().toString(), templateFormGroupVariable.getGroupId().toString(), testeeId);
                        if (projectTesteeResult != null) {
                            templateFormGroupDetailVo.setFieldValue(projectTesteeResult.getFieldValue());
                            templateFormGroupDetailVo.setTesteeResultId(projectTesteeResult.getId());
                            templateFormGroupDetailVo.setUnitResultValue(projectTesteeResult.getUnitValue());
                        }
                        templateFormGroupDetailVo.setFieldName(templateFormGroupDetailVo.getFieldName());
                        templateFormGroupDetailVo.setBaseVariableId(templateFormGroupDetailVo.getId());
                        templateFormGroupDetailVo.setId(templateFormGroupVariable.getId());
                        templateFormGroupDetailVo.setTesteeGroupId(templateFormGroupVariable.getGroupId());
                        templateFormGroupDetailVo.setGroupName(templateFormGroupVariable.getGroupName());

                        // 实验室配置
                        String labConfigScope = templateFormGroupDetailVo.getLabConfigScope();
                        if(StringUtils.isEmpty(labConfigScope)){
                            labConfigScope = flowFormSet.getLabConfigScope();
                        }
                        TemplateLabConfigExpand templateLabConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigScope, visitId, "", BusinessConfig.LAB_QUERY_GROUP_VARIABLE_TYPE, "", testeeId, groupId.toString(), templateFormGroupDetailVo.getBaseVariableId().toString(), "", templateFormGroupDetailVo.getFieldValue());
                        templateFormGroupDetailVo.setTemplateLabConfigExpand(templateLabConfigExpand);


                        if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormGroupDetailVo.getType())) {
                            List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                            List<TemplateTableVo> dataGroupRowHeadList = new ArrayList<>();
                            List<TemplateTableVo> tableHeadRowList = templateFormGroupDetailVo.getTableHeadRowList();
                            ProjectTesteeFormTableConfigVo projectTesteeFormTableConfig = projectTesteeTableService.getProjectTesteeFormTableConfig(projectId, planId, visitId, formId, templateFormGroupDetailVo.getBaseVariableId().toString(), templateFormGroupVariableVo.getGroupId().toString(), testeeId);
                            List<ProjectTesteeTableVo> projectTesteeTableRowList = projectTesteeFormTableConfig.getTableConfigRow();
                            for (ProjectTesteeTableVo templateTableVo : projectTesteeTableRowList) {
                                TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                                TemplateTableVo templateGroupTableVo = new TemplateTableVo();
                                BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                                BeanUtils.copyProperties(templateTableVo, templateGroupTableVo);
                                templateTableRowHeadVo.setIdViewValue(templateTableVo.getId().toString());
                                dataRowHeadList.add(templateTableRowHeadVo);
                                for (TemplateTableVo tableVo : tableHeadRowList) {
                                    if(tableVo.getFieldName().equals(templateTableVo.getFieldName())){
                                        templateGroupTableVo.setBaseVariableTableId(tableVo.getId());
                                    }
                                }
                                dataGroupRowHeadList.add(templateGroupTableVo);
                            }
                            templateFormGroupDetailVo.setTableHeadRowList(dataGroupRowHeadList);
                            List<Long> tableRowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, planId, visitId, formId, "", templateFormGroupVariable.getId().toString(), testeeId, "1", null, tableSort, templateFormGroupVariable.getGroupId().toString());
                            List<ProjectTesteeTableBody.RowDataDesc> dataList = new ArrayList<>();
                            for (Long rowNumber : tableRowNumberList) {
                                List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();
                                ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                                List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeGroupTableListForPage(projectId, visitId, formId, templateFormGroupVariable.getId().toString(), testeeId, rowNumber);
                                List<String> countList = new ArrayList<>();
                                for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                                    ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                                    BeanUtils.copyProperties(projectTesteeTable, data);
                                    data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                                    data.setFormTableViewId(projectTesteeTable.getFormTableId().toString());
                                    data.setUnitValue(projectTesteeTable.getUnitValue());
                                    data.setUnitText(projectTesteeTable.getUnitText());
                                    data.setFieldName(projectTesteeTable.getFieldName());
                                    data.setFieldValue(projectTesteeTable.getFieldValue());
                                    List<String> variableInfoList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                    if(variableInfoList.contains(projectTesteeTable.getType())){
                                        data.setFieldValue(projectTesteeTable.getFieldText());
                                    }
                                    //如果存在多张图片则全部展示
                                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(projectTesteeTable.getType())) {
                                        List<ProjectTesteeFormImageVo> variableImageList = projectTesteeFileService.getProjectTesteeFormImageList(projectId, planId, visitId, formId, testeeId, null, templateFormGroupVariable.getId().toString(), projectTesteeTable.getFormTableId().toString(), rowNumber.toString(), "", "", "", "", true, "");
                                        data.setVariableImageList(variableImageList);
                                    }

                                    // 字段组-表格-实验室配置
                                    String labConfigGroupScope = projectTesteeTable.getLabConfigScope();
                                    if(StringUtils.isEmpty(labConfigScope)){
                                        labConfigGroupScope = flowFormSet.getLabConfigScope();
                                    }
                                    TemplateLabConfigExpand templateLabTableConfigExpand = projectTemplateLabConfigWrapper(projectId, projectOrgId, labConfigGroupScope, visitId, "", BusinessConfig.LAB_QUERY_GROUP_TABLE_VARIABLE_TYPE, "", testeeId, groupId.toString(), projectTesteeTable.getResourceVariableId().toString(), projectTesteeTable.getResourceTableId().toString(), projectTesteeTable.getFieldValue());
                                    data.setTemplateLabConfigExpand(templateLabTableConfigExpand);

                                    gridList.add(data);

                                    if (PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectTesteeTable.getComplateStatus())) {
                                        countList.add(projectTesteeTable.getComplateStatus());
                                    }
                                }
                                rowDataDesc.setRowNumber(rowNumber.toString());
                                rowDataDesc.setFormDetailId(templateFormGroupVariable.getId().toString());
                                rowDataDesc.setRowData(gridList);
                                //查询当前行记录完成状态
                                if (projectTesteeTableRowList.size() > countList.size() && countList.size() > 0) {
                                    rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
                                }
                                if (projectTesteeTableRowList.size() == countList.size()) {
                                    rowDataDesc.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
                                }
                                dataList.add(rowDataDesc);
                            }
                            templateFormGroupDetailVo.setRows(dataList);
                        }
                    }
                    templateFormGroupVo.setGroupVariableList(templateFormDetailGroupList);
                    templateFormGroupList.add(templateFormGroupVo);
                }
                templateFormDetailVo.setTemplateFormGroupList(templateFormGroupList);
            }
        }
        return templateFormDetailConfigList;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_02, formType = BusinessConfig.PROJECT_VISIT_CRF_FORM)
    public CustomResult saveTesteeVisitFormDetail(ProjectTesteeResultParam projectTesteeResultParam) {
        if(projectTesteeResultParam.getPlanId() == null){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectTesteeResultParam.getProjectId().toString());
            if(flowPlanInfo != null){
                projectTesteeResultParam.setPlanId(flowPlanInfo.getId());
            }
        }
        List<ProjectFormResultVo> formVariableResultList = new ArrayList<>();
        List<String> complateList = new ArrayList<>();
        List<ProjectTesteeResultParam.TesteeFormResultValue> dataList = projectTesteeResultParam.getDataList();
        for (ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue : dataList) {
            if(testeeFormResultValue.getFieldValue() == null){continue;}
            List<String> varialbeTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE,
                    BusinessConfig.PROJECT_VISIT_CRF_FORM_GROUP,
                    BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
            if(varialbeTypeList.contains(testeeFormResultValue.getType())){
                continue;
            }
            ProjectTesteeResult projectTesteeResult = new ProjectTesteeResult();
            projectTesteeResult.setProjectId(projectTesteeResultParam.getProjectId());
            projectTesteeResult.setPlanId(projectTesteeResultParam.getPlanId());
            projectTesteeResult.setVisitId(projectTesteeResultParam.getVisitId());
            projectTesteeResult.setFormId(projectTesteeResultParam.getFormId());
            projectTesteeResult.setFormExpandId(projectTesteeResultParam.getFormExpandId());
            projectTesteeResult.setFormDetailId(testeeFormResultValue.getFormDetailId());
            if (testeeFormResultValue.getTesteeGroupId() != null) {
                projectTesteeResult.setGroupId(testeeFormResultValue.getTesteeGroupId());
            }
            if(testeeFormResultValue.getResourceVariableId() != null){
                projectTesteeResult.setResourceVariableId(testeeFormResultValue.getResourceVariableId());
            }
            projectTesteeResult.setTesteeId(projectTesteeResultParam.getTesteeId());
            projectTesteeResult.setLabel(testeeFormResultValue.getLabel());
            projectTesteeResult.setFieldName(testeeFormResultValue.getFieldName());
            if (testeeFormResultValue.getFieldValue() != null) {
                projectTesteeResult.setFieldValue(testeeFormResultValue.getFieldValue());
                projectTesteeResult.setFieldText(testeeFormResultValue.getFieldText());
            }
            projectTesteeResult.setUnitValue(testeeFormResultValue.getUnitValue());
            projectTesteeResult.setUnitText(testeeFormResultValue.getUnitText());
            projectTesteeResult.setOptionHidden(testeeFormResultValue.getOptionHidden());
            projectTesteeResult.setScoreValue(testeeFormResultValue.getScoreValue());
            projectTesteeResult.setComplateStatus(StringUtils.isBlank(testeeFormResultValue.getFieldValue()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
            if(FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode().equalsIgnoreCase(projectTesteeResult.getComplateStatus())){
                complateList.add(projectTesteeResult.getComplateStatus());
            }
            ProjectFormResultVo projectFormResultVo = new ProjectFormResultVo();
            projectFormResultVo.setProjectId(projectTesteeResultParam.getProjectId().toString());
            projectFormResultVo.setVisitId(projectTesteeResultParam.getVisitId().toString());
            projectFormResultVo.setFormId(projectTesteeResultParam.getFormId().toString());

            if (testeeFormResultValue.getTesteeResultId() == null) {
                projectTesteeResult.setId(SnowflakeIdWorker.getUuid());
                projectTesteeResult.setStatus(BusinessConfig.VALID_STATUS);
                if (projectTesteeResultParam.getTaskSubmitTime() == null) {
                    projectTesteeResult.setCreateTime(new Date());
                } else {
                    projectTesteeResult.setCreateTime(projectTesteeResultParam.getTaskSubmitTime());
                }
                projectTesteeResult.setCreateUser(projectTesteeResultParam.getOperator());
                projectTesteeResult.setTenantId(SecurityUtils.getSystemTenantId());
                projectTesteeResult.setPlatformId(SecurityUtils.getSystemPlatformId());
                projectTesteeResultService.insert(projectTesteeResult);

                projectFormResultVo.setFormDetailId(testeeFormResultValue.getFormDetailId().toString());
                projectFormResultVo.setFormResultId(projectTesteeResult.getId().toString());
                projectFormResultVo.setComplateStatus(projectTesteeResult.getComplateStatus());
                formVariableResultList.add(projectFormResultVo);

            } else {
                ProjectTesteeResult projectTesteeResultVo = projectTesteeResultService.selectByPrimaryKey(testeeFormResultValue.getTesteeResultId());
                if (projectTesteeResultVo != null) {
                    projectTesteeResultVo.setFormExpandId(projectTesteeResultParam.getFormExpandId());
                    if (testeeFormResultValue.getFieldValue() != null) {
                        projectTesteeResultVo.setFieldValue(testeeFormResultValue.getFieldValue());
                        projectTesteeResultVo.setFieldText(testeeFormResultValue.getFieldText());
                    }
                    if(testeeFormResultValue.getResourceVariableId() != null){
                        projectTesteeResultVo.setResourceVariableId(testeeFormResultValue.getResourceVariableId());
                    }
                    projectTesteeResultVo.setComplateStatus(StringUtils.isEmpty(testeeFormResultValue.getFieldValue()) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
                    projectTesteeResultVo.setUnitValue(testeeFormResultValue.getUnitValue());
                    projectTesteeResultVo.setUnitText(testeeFormResultValue.getUnitText());
                    projectTesteeResultVo.setOptionHidden(testeeFormResultValue.getOptionHidden());
                    projectTesteeResultVo.setScoreValue(testeeFormResultValue.getScoreValue());
                    projectTesteeResultVo.setUpdateTime(new Date());
                    projectTesteeResultVo.setUpdateUser(projectTesteeResultParam.getOperator());
                    projectTesteeResultService.updateByPrimaryKeySelective(projectTesteeResultVo);

                    projectFormResultVo.setFormDetailId(projectTesteeResultVo.getFormDetailId().toString());
                    projectFormResultVo.setFormResultId(projectTesteeResultVo.getId().toString());
                    projectFormResultVo.setComplateStatus(projectTesteeResultVo.getComplateStatus());
                    formVariableResultList.add(projectFormResultVo);
                }
            }
        }
        String projectId = projectTesteeResultParam.getProjectId().toString();
        String projectOrgId = projectTesteeResultParam.getProjectOrgId().toString();
        String planId = projectTesteeResultParam.getPlanId().toString();
        String visitId = projectTesteeResultParam.getVisitId().toString();
        String formId = projectTesteeResultParam.getFormId().toString();
        String formExpandId = projectTesteeResultParam.getFormExpandId() == null ? "" : projectTesteeResultParam.getFormExpandId().toString();
        String testeeId = projectTesteeResultParam.getTesteeId().toString();
        String status = projectTesteeResultParam.getStatus();
        String operator = projectTesteeResultParam.getOperator();
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        //String complateStatus = getProjectTesteeFormComplateWrapper(projectId, projectOrgId, planId, visitId, formId, testeeId);
        String complateStatus = "";
        AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.updateProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId, status, operator, complateStatus, systemTenantId, systemPlatformId),500);
        
        projectTesteeResultParam.setTenantId(systemTenantId);
        projectTesteeResultParam.setPlatformId(systemPlatformId);
        // 判断表单是否有逻辑规则
        this.checkProjectTesteeFieldValueRule(projectTesteeResultParam);
        
        // 计算访视周期时间
        if (projectTesteeResultParam.getFollowUpRealTime() != null) {
            this.computeVisitDate(projectTesteeResultParam);
        }
        // 如果配置文件中设置开启表单审核功能，需要添加审核记录
        if (formAudit){
            this.addAudit(projectTesteeResultParam);
        }

        CustomResult customResult = new CustomResult();
        customResult.setData(formVariableResultList.size());
        return customResult;
    }

    private void addAudit(ProjectTesteeResultParam projectTesteeResultParam) {
        if(BusinessConfig.AUDIT_SUBMIT.equals(projectTesteeResultParam.getStatus())){
            ProjectFormAudit audit = projectFormAuditService.getProjectFormAuditInfo(projectTesteeResultParam.getProjectId(),
                    projectTesteeResultParam.getVisitId(),
                    projectTesteeResultParam.getFormId(),
                    projectTesteeResultParam.getTesteeId());
            if (audit!=null){
                audit.setAuditStatus(BusinessConfig.AUDIT_SUBMIT);
                audit.setSubmitUserId(SecurityUtils.getUserId());
                audit.setSubmitTime(new Date());
                projectFormAuditService.update(audit);
            }else {
                ProjectFormAudit projectFormAudit = new ProjectFormAudit();
                projectFormAudit.setId(SnowflakeIdWorker.getUuid());
                projectFormAudit.setProjectId(projectTesteeResultParam.getProjectId());
                projectFormAudit.setPlanId(projectTesteeResultParam.getPlanId());
                projectFormAudit.setVisitId(projectTesteeResultParam.getVisitId());
                projectFormAudit.setFormId(projectTesteeResultParam.getFormId());
                projectFormAudit.setTesteeId(projectTesteeResultParam.getTesteeId());
                projectFormAudit.setAuditStatus(BusinessConfig.AUDIT_SUBMIT);
                projectFormAudit.setStatus(BusinessConfig.VALID_STATUS);
                projectFormAudit.setSubmitTime(new Date());
                projectFormAudit.setTenantId(SecurityUtils.getSystemTenantId());
                projectFormAudit.setPlatformId(SecurityUtils.getSystemPlatformId());
                projectFormAudit.setSubmitUserId(SecurityUtils.getUserId());
                projectFormAuditService.create(projectFormAudit);
            }
        }
    }

    /**
     * 计算访视周期时间
     * @param projectTesteeResultParam
     */
    private void computeVisitDate(ProjectTesteeResultParam projectTesteeResultParam) {
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.execute(() -> {
            try {
                ProjectVisitVo projectVisitConfig = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(projectTesteeResultParam.getVisitId().toString());
                ProjectVisitTesteeRecord projectVisitTesteeRecord = projectVisitConfigService.getProjectVisitTesteeRecord(projectTesteeResultParam.getProjectId().toString(), projectTesteeResultParam.getVisitId().toString(), projectTesteeResultParam.getTesteeId().toString());
                if (projectVisitTesteeRecord == null) {
                    ProjectVisitTesteeRecord projectVisitTesteeRecordVo = new ProjectVisitTesteeRecord();
                    projectVisitTesteeRecordVo.setId(SnowflakeIdWorker.getUuid());
                    projectVisitTesteeRecordVo.setProjectId(projectTesteeResultParam.getProjectId());
                    projectVisitTesteeRecordVo.setPlanId(projectTesteeResultParam.getPlanId());
                    projectVisitTesteeRecordVo.setVisitId(projectTesteeResultParam.getVisitId());
                    projectVisitTesteeRecordVo.setTesteeId(projectTesteeResultParam.getTesteeId());
                    projectVisitTesteeRecordVo.setFollowUpRealTime(projectTesteeResultParam.getFollowUpRealTime());

                    projectVisitTesteeRecordVo.setCreateTime(new Date());
                    projectVisitTesteeRecordVo.setCreateUserId(projectTesteeResultParam.getOperator());
                    projectVisitConfigService.insertProjectVisitTesteeRecordSelective(projectVisitTesteeRecordVo);

                    ProjectVisitTesteeRecord projectNextVisitTesteeRecordVo = new ProjectVisitTesteeRecord();
                    projectNextVisitTesteeRecordVo.setProjectId(projectTesteeResultParam.getProjectId());
                    projectNextVisitTesteeRecordVo.setVisitId(projectTesteeResultParam.getVisitId());
                    projectNextVisitTesteeRecordVo.setTesteeId(projectTesteeResultParam.getTesteeId());

                    //计算并设置下一次访视的计划访视时间
                    ProjectVisitConfig preVisitConfig = projectVisitConfigService.getVisitConfigByPreVisitId(projectVisitConfig.getId().toString());
                    if (preVisitConfig != null) {
                        ProjectVisitTesteeRecord nextProjectVisitTesteeRecord = projectVisitConfigService.getProjectVisitTesteeRecord(projectTesteeResultParam.getProjectId().toString(), preVisitConfig.getId().toString(), projectTesteeResultParam.getTesteeId().toString());
                        if (nextProjectVisitTesteeRecord == null) {
                            ProjectVisitTesteeRecord nextProjectVisitTesteeRecordParams = new ProjectVisitTesteeRecord();
                            nextProjectVisitTesteeRecordParams.setId(SnowflakeIdWorker.getUuid());
                            nextProjectVisitTesteeRecordParams.setProjectId(projectTesteeResultParam.getProjectId());
                            nextProjectVisitTesteeRecordParams.setVisitId(preVisitConfig.getId());
                            nextProjectVisitTesteeRecordParams.setTesteeId(projectTesteeResultParam.getTesteeId());

                            Date followUpRealTime = projectVisitTesteeRecordVo.getFollowUpRealTime();
                            Date baseVisitTime = DateUtil.getDateAfter(followUpRealTime, projectVisitConfig.getFollowUpPeroid());

                            nextProjectVisitTesteeRecordParams.setFollowUpNextTime(baseVisitTime);
                            Date nextFollowStartDateTime = DateUtil.getDateAfter(baseVisitTime, projectVisitConfig.getVisitWindowStart());
                            Date nextFollowEndDateTime = DateUtil.getDateAfter(baseVisitTime, projectVisitConfig.getVisitWindowEnd());
                            nextProjectVisitTesteeRecordParams.setFollowUpStartTime(nextFollowStartDateTime);
                            nextProjectVisitTesteeRecordParams.setFollowUpEndTime(nextFollowEndDateTime);

                            nextProjectVisitTesteeRecordParams.setCreateTime(new Date());
                            nextProjectVisitTesteeRecordParams.setCreateUserId(projectTesteeResultParam.getOperator());
                            projectVisitConfigService.insertProjectVisitTesteeRecordSelective(nextProjectVisitTesteeRecordParams);
                        }
                    }
                } else {
                    projectVisitTesteeRecord.setFollowUpRealTime(projectTesteeResultParam.getFollowUpRealTime());
                    projectVisitTesteeRecord.setUpdateTime(new Date());
                    projectVisitTesteeRecord.setUpdateUserId(projectTesteeResultParam.getOperator());
                    projectVisitConfigService.updateProjectVisitTesteeRecord(projectVisitTesteeRecord);

                    ProjectVisitConfig preVisitConfig = projectVisitConfigService.getVisitConfigByPreVisitId(projectVisitConfig.getId().toString());
                    if (preVisitConfig != null) {
                        ProjectVisitTesteeRecord projectNextVisitTesteeRecordVo = projectVisitConfigService.getProjectVisitTesteeRecord(projectTesteeResultParam.getProjectId().toString(), preVisitConfig.getId().toString(), projectTesteeResultParam.getTesteeId().toString());
                        if (projectNextVisitTesteeRecordVo != null) {
                            //重新计算下次计划访视时间
                            if (preVisitConfig != null) {
                                Date followUpRealTime = projectVisitTesteeRecord.getFollowUpRealTime();
                                Date baseVisitTime = DateUtil.getDateAfter(followUpRealTime, projectVisitConfig.getFollowUpPeroid());
                                projectNextVisitTesteeRecordVo.setFollowUpNextTime(baseVisitTime);
                                Date nextFollowStartDateTime = DateUtil.getDateAfter(baseVisitTime, projectVisitConfig.getVisitWindowStart());
                                Date nextFollowEndDateTime = DateUtil.getDateAfter(baseVisitTime, projectVisitConfig.getVisitWindowEnd());
                                projectNextVisitTesteeRecordVo.setFollowUpStartTime(nextFollowStartDateTime);
                                projectNextVisitTesteeRecordVo.setFollowUpEndTime(nextFollowEndDateTime);
                                projectNextVisitTesteeRecordVo.setUpdateTime(new Date());
                                projectNextVisitTesteeRecordVo.setUpdateUserId(projectTesteeResultParam.getOperator());
                                projectVisitConfigService.updateProjectVisitTesteeRecord(projectNextVisitTesteeRecordVo);
                            }
                        } else {
                            //补偿更新 未录入的访视记录
                            ProjectVisitTesteeRecord projectNextVisitTesteeRecord = new ProjectVisitTesteeRecord();
                            projectNextVisitTesteeRecord.setId(SnowflakeIdWorker.getUuid());
                            projectNextVisitTesteeRecord.setProjectId(projectTesteeResultParam.getProjectId());
                            projectNextVisitTesteeRecord.setVisitId(preVisitConfig.getId());
                            projectNextVisitTesteeRecord.setTesteeId(projectTesteeResultParam.getTesteeId());

                            Date followUpRealTime = projectTesteeResultParam.getFollowUpRealTime();
                            Date baseVisitTime = DateUtil.getDateAfter(followUpRealTime, projectVisitConfig.getFollowUpPeroid());
                            projectNextVisitTesteeRecord.setFollowUpNextTime(baseVisitTime);

                            Date nextFollowStartDateTime = DateUtil.getDateAfter(baseVisitTime, projectVisitConfig.getVisitWindowStart());
                            Date nextFollowEndDateTime = DateUtil.getDateAfter(baseVisitTime, projectVisitConfig.getVisitWindowEnd());
                            projectNextVisitTesteeRecord.setFollowUpStartTime(nextFollowStartDateTime);
                            projectNextVisitTesteeRecord.setFollowUpEndTime(nextFollowEndDateTime);
                            projectNextVisitTesteeRecord.setCreateTime(new Date());
                            projectNextVisitTesteeRecord.setCreateUserId(projectTesteeResultParam.getOperator());
                            projectVisitConfigService.insertProjectVisitTesteeRecordSelective(projectNextVisitTesteeRecord);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        executorService.shutdown();
    }

    /**
     * 判断表单字段的逻辑规则是否符合设置，如果不符合设置逻辑信息
     * @param projectTesteeResultParam
     */
    private void checkProjectTesteeFieldValueRule(ProjectTesteeResultParam projectTesteeResultParam) {
        // 逻辑规则计算数据
        Map<String, Object> projectTesteeVariableResultMap = new HashMap<>();
        for (ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue : projectTesteeResultParam.getDataList()) {
            if (testeeFormResultValue.getFieldValue() != null) {
                // 设置规则信息数据
                Map<String, Object> dataMap = getTemplateFormRuleComputeData(projectTesteeResultParam.getVisitId() + challenge_split + projectTesteeResultParam.getFormId() + challenge_split + testeeFormResultValue.getFormDetailId(), testeeFormResultValue.getFieldValue());
                projectTesteeVariableResultMap.putAll(dataMap);
            }
        }
        // 获取表单所有参与计算的变量信息
        TemplateFormDvpValExample example = new TemplateFormDvpValExample();
        TemplateFormDvpValExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectTesteeResultParam.getProjectId());
        criteria.andVisitIdEqualTo(projectTesteeResultParam.getVisitId());
        criteria.andFormIdEqualTo(projectTesteeResultParam.getFormId());
        List<TemplateFormDvpVal> dvpValList = templateFormDvpValMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dvpValList)) {
            
            // 获取关于表单的规则配置
            List<Long> dvpIds = new ArrayList<>();
            dvpValList.forEach(val -> {
                if (!dvpIds.contains(val.getDvpRuleId())) {
                    dvpIds.add(val.getDvpRuleId());
                }
            });
            // 获取逻辑设置信息
            TemplateFormDvpRuleExample example0 = new TemplateFormDvpRuleExample();
            TemplateFormDvpRuleExample.Criteria criteria0 = example0.createCriteria();
            criteria0.andIdIn(dvpIds);
            criteria0.andStatusEqualTo(BusinessConfig.VALID_STATUS);
            List<TemplateFormDvpRule> formRule = templateFormDvpRuleMapper.selectByExample(example0);
            if (CollectionUtil.isNotEmpty(formRule)) {
                formRule.forEach(templateFormDvpRule -> {
                    // 获取所有参与计算的变量信息
                    this.setComputeData(projectTesteeResultParam, projectTesteeVariableResultMap, templateFormDvpRule);
                    // 字段组
                    if (templateFormDvpRule.getGroupId() != null) {
                        // 触发位置是字段组
                        if (templateFormDvpRule.getFormTableId() != null) {
                            // 字段组中的表格变量
                            List<ProjectTesteeTableResultExportVo> tableDataList = projectTesteeTableService.getProjectTesteeGroupTableResult(templateFormDvpRule.getProjectId().toString(), templateFormDvpRule.getVisitId().toString(), templateFormDvpRule.getFormId().toString(), templateFormDvpRule.getFormTableId().toString(), projectTesteeResultParam.getTesteeId().toString(), projectTesteeResultParam.getProjectOrgId().toString());
                            if (CollectionUtil.isNotEmpty(tableDataList)) {
                                // 循环表格数据进行规则校验
                                for (ProjectTesteeTableResultExportVo tableData : tableDataList) {
                                    
                                    String key = "$" + tableData.getVisitId() + challenge_split + tableData.getFormId() + challenge_split + tableData.getFormTableId() + "$";
                                    // 如果变量是单选，下拉，复选，则获取字典的值
                                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(tableData.getType()) ||
                                            BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(tableData.getType()) ||
                                            BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(tableData.getType())) {
                                        // 根据值获取字典的值
                                        if (StringUtils.isNotEmpty(tableData.getFieldValue())) {
                                            if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(tableData.getDicResource())) {
                                                Dictionary dictionary = dictionaryService.getDictionaryInfo(tableData.getFieldValue());
                                                projectTesteeVariableResultMap.put(key, formatValue(dictionary.getValue()));
                                            }
                                            if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(tableData.getDicResource()) || BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(tableData.getDicResource())) {
                                                ProjectDictionary dictionary = projectDictionaryService.getDictionaryInfo(tableData.getFieldValue());
                                                projectTesteeVariableResultMap.put(key, formatValue(dictionary.getValue()));
                                            }
                                        }
                                    } else {
                                        projectTesteeVariableResultMap.put(key, formatValue(tableData.getFieldValue()));
                                    }
                                    // 逻辑执行
                                    Object compute = templateFormDvpRuleService.compute(templateFormDvpRule.getArithmeticValue(), projectTesteeVariableResultMap);
                                    
                                    // 查询质疑是否已经存在，如果不存在且不符合逻辑，新增，否则不新增，
                                    // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                                    ProjectTesteeChallenge challenge0 = getProjectTesteeChallenge(templateFormDvpRule.getId(), Long.parseLong(tableData.getGroupId()), Long.parseLong(tableData.getRowNumber()), projectTesteeResultParam.getTesteeId());
                                    if (Boolean.parseBoolean(compute.toString())) {
                                        if (templateFormDvpRule.getEnabled()) {
                                            if (challenge0 == null) {
                                                // 保存质疑信息
                                                ProjectTesteeChallenge challenge = this.saveChallenge(projectTesteeResultParam, templateFormDvpRule.getId(), templateFormDvpRule);
                                                challenge.setFormResultTableRowno(Long.parseLong(tableData.getRowNumber()));
                                                challenge.setGroupId(tableData.getGroupId());
                                                projectTesteeChallengeMapper.insert(challenge);
                                            } else {
                                                if (challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName())) {
                                                    challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
                                                    projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                                }
                                            }
                                        }
                                    } else {
                                        // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                                        if (challenge0 != null && challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName())) {
                                            challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
                                            projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                        }
                                    }
                                    
                                }
                            }
                            
                        } else {
                            // 字段组中的普通变量
                            List<ProjectTesteeResultVo> results = projectTesteeResultService.getProjectTesteeGroupResultList(templateFormDvpRule.getProjectId(), templateFormDvpRule.getVisitId(), templateFormDvpRule.getFormId(), templateFormDvpRule.getFormDetailId(), projectTesteeResultParam.getTesteeId());
                            if (CollectionUtil.isNotEmpty(results)) {
                                // 循环表格数据进行规则校验
                                for (ProjectTesteeResultVo result : results) {
                                    
                                    String key = "$" + result.getVisitId() + challenge_split + result.getFormId() + challenge_split + result.getResourceVariableId() + "$";
                                    // 如果变量是单选，下拉，复选，则获取字典的值
                                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(result.getType()) ||
                                            BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(result.getType()) ||
                                            BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(result.getType())) {
                                        // 根据值获取字典的值
                                        if (StringUtils.isNotEmpty(result.getFieldValue())) {
                                            if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(result.getDicResource())) {
                                                Dictionary dictionary = dictionaryService.getDictionaryInfo(result.getFieldValue());
                                                projectTesteeVariableResultMap.put(key, formatValue(dictionary.getValue()));
                                            }
                                            if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(result.getDicResource()) || BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(result.getDicResource())) {
                                                ProjectDictionary dictionary = projectDictionaryService.getDictionaryInfo(result.getFieldValue());
                                                projectTesteeVariableResultMap.put(key, formatValue(dictionary.getValue()));
                                            }
                                        }
                                    } else {
                                        projectTesteeVariableResultMap.put(key, formatValue(result.getFieldValue()));
                                    }
                                    
                                    // 逻辑执行
                                    Object compute = templateFormDvpRuleService.compute(templateFormDvpRule.getArithmeticValue(), projectTesteeVariableResultMap);
                                    
                                    // 查询质疑是否已经存在，如果不存在且不符合逻辑，新增，否则不新增，
                                    // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                                    ProjectTesteeChallenge challenge0 = getProjectTesteeChallenge(templateFormDvpRule.getId(), result.getGroupId(), null, projectTesteeResultParam.getTesteeId());
                                    if (Boolean.parseBoolean(compute.toString())) {
                                        if (templateFormDvpRule.getEnabled()) {
                                            if (challenge0 == null) {
                                                // 保存质疑信息
                                                
                                                ProjectTesteeChallenge challenge = this.saveChallenge(projectTesteeResultParam, templateFormDvpRule.getId(), templateFormDvpRule);
                                                challenge.setGroupId(result.getGroupId().toString());
                                                projectTesteeChallengeMapper.insert(challenge);
                                                
                                            } else {
                                                if (challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName())) {
                                                    challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
                                                    projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                                }
                                            }
                                        }
                                    } else {
                                        // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                                        if (challenge0 != null && challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName())) {
                                            challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
                                            projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                        }
                                    }
                                    
                                }
                            }
                        }
                        // 表格
                    } else if (templateFormDvpRule.getFormTableId() != null) {
                        // 触发位置是表格
                        // 获取表格数据
                        List<ProjectTesteeTableResultExportVo> tableDataList = projectTesteeTableService.getProjectTesteeTableRecordsWithVariableIds(templateFormDvpRule.getProjectId().toString(), projectTesteeResultParam.getProjectOrgId().toString(), templateFormDvpRule.getVisitId().toString(), templateFormDvpRule.getFormId().toString(), null, templateFormDvpRule.getFormTableId().toString(), projectTesteeResultParam.getTesteeId().toString());
                        if (CollectionUtil.isNotEmpty(tableDataList)) {
                            // 循环表格数据进行规则校验
                            for (ProjectTesteeTableResultExportVo tableData : tableDataList) {
                                String key = "$" + tableData.getVisitId() + challenge_split + tableData.getFormId() + challenge_split + tableData.getFormTableId() + "$";
                                // 如果变量是单选，下拉，复选，则获取字典的值
                                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(tableData.getType()) ||
                                        BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(tableData.getType()) ||
                                        BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(tableData.getType())) {
                                    if (StringUtils.isNotEmpty(tableData.getFieldValue())) {
                                        // 根据值获取字典的值
                                        if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(tableData.getDicResource())) {
                                            Dictionary dictionary = dictionaryService.getDictionaryInfo(tableData.getFieldValue());
                                            projectTesteeVariableResultMap.put(key, formatValue(dictionary.getValue()));
                                        }
                                        if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(tableData.getDicResource()) || BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(tableData.getDicResource())) {
                                            ProjectDictionary dictionary = projectDictionaryService.getDictionaryInfo(tableData.getFieldValue());
                                            projectTesteeVariableResultMap.put(key, formatValue(dictionary.getValue()));
                                        }
                                    }
                                } else {
                                    projectTesteeVariableResultMap.put(key, formatValue(tableData.getFieldValue()));
                                }
                                // 逻辑执行
                                Object compute = templateFormDvpRuleService.compute(templateFormDvpRule.getArithmeticValue(), projectTesteeVariableResultMap);
                                
                                // 查询质疑是否已经存在，如果不存在且不符合逻辑，新增，否则不新增，
                                // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                                ProjectTesteeChallenge challenge0 = getProjectTesteeChallenge(templateFormDvpRule.getId(), null, Long.parseLong(tableData.getRowNumber()), projectTesteeResultParam.getTesteeId());
                                if (Boolean.parseBoolean(compute.toString())) {
                                    if (templateFormDvpRule.getEnabled()) {
                                        if (challenge0 == null) {
                                            // 保存质疑信息
                                            
                                            ProjectTesteeChallenge challenge = this.saveChallenge(projectTesteeResultParam, templateFormDvpRule.getId(), templateFormDvpRule);
                                            challenge.setFormResultTableRowno(Long.parseLong(tableData.getRowNumber()));
                                            projectTesteeChallengeMapper.insert(challenge);
                                            
                                        } else {
                                            if (challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName())) {
                                                challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
                                                projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                            }
                                        }
                                    }
                                } else {
                                    // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                                    if (challenge0 != null && challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName())) {
                                        challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
                                        projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                    }
                                }
                                
                            }
                        }
                    } else {
                        // 普通变量
                        Object compute = templateFormDvpRuleService.compute(templateFormDvpRule.getArithmeticValue(), projectTesteeVariableResultMap);
                        // 查询质疑是否已经存在，如果不存在且不符合逻辑，新增，否则不新增，
                        // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                        ProjectTesteeChallenge challenge0 = getProjectTesteeChallenge(templateFormDvpRule.getId(), null, null, projectTesteeResultParam.getTesteeId());
                        if (Boolean.parseBoolean(compute.toString())) {
                            if (templateFormDvpRule.getEnabled()) {
                                if (challenge0 == null) {
                                    // 保存质疑信息
                                    ProjectTesteeChallenge challenge = this.saveChallenge(projectTesteeResultParam, templateFormDvpRule.getId(), templateFormDvpRule);
                                    projectTesteeChallengeMapper.insert(challenge);
                                    
                                } else {
                                    if (challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName())) {
                                        challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
                                        projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                                    }
                                }
                            }
                        } else {
                            // 如果符合逻辑，但已经存在，并且是开启状态需要进行关闭
                            if (challenge0 != null && challenge0.getReplyCloseStatus().equals(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName())) {
                                challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
                                projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
                            }
                        }
                    }
                });
            }
        }
    }
    
    private void setComputeData(ProjectTesteeResultParam param, Map<String, Object> ruleData, TemplateFormDvpRule templateFormDvpRule) {
        List<TemplateFormDvpVal> formDvpValList = getDvpVals(templateFormDvpRule.getId());
        if (CollectionUtil.isNotEmpty(formDvpValList)) {
            formDvpValList.forEach(templateFormRuleValue -> {
                if (!templateFormRuleValue.getFormId().equals(param.getFormId())) {
                    // 获取变量的值
                    ProjectTesteeResultExample exampleRes = new ProjectTesteeResultExample();
                    ProjectTesteeResultExample.Criteria criteriaRes = exampleRes.createCriteria();
                    criteriaRes.andProjectIdEqualTo(templateFormRuleValue.getProjectId());
                    criteriaRes.andVisitIdEqualTo(templateFormRuleValue.getVisitId());
                    criteriaRes.andFormIdEqualTo(templateFormRuleValue.getFormId());
                    criteriaRes.andFormDetailIdEqualTo(templateFormRuleValue.getFormDetailId());
                    criteriaRes.andTesteeIdEqualTo(param.getTesteeId());
                    criteriaRes.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                    List<ProjectTesteeResult> results = projectTesteeResultMapper.selectByExample(exampleRes);
                    if (CollectionUtil.isNotEmpty(results)) {
                        ProjectTesteeResult result = results.get(0);
                        // 如果变量是单选，下拉，复选，则获取字典的值
                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormRuleValue.getFieldType())||
                                BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormRuleValue.getFieldType())||
                                BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(templateFormRuleValue.getFieldType())
                        ){
                            if (StringUtils.isNotEmpty(result.getFieldValue())){
                                // 根据值获取字典的值
                                if(BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormRuleValue.getDicResource())){
                                    Dictionary dictionary= dictionaryService.getDictionaryInfo(result.getFieldValue());
                                    ruleData.put("$" + result.getVisitId() + challenge_split + result.getFormId() + challenge_split + result.getFormDetailId() + "$", formatValue(dictionary.getValue()));
                                }
                                if(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormRuleValue.getDicResource())||BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormRuleValue.getDicResource())){
                                    ProjectDictionary dictionary= projectDictionaryService.getDictionaryInfo(result.getFieldValue());
                                    ruleData.put("$" + result.getVisitId() + challenge_split + result.getFormId() + challenge_split + result.getFormDetailId() + "$", formatValue(dictionary.getValue()));
                                }
                            }

                        }else {
                            ruleData.put("$" + result.getVisitId() + challenge_split + result.getFormId() + challenge_split + result.getFormDetailId() + "$", formatValue(result.getFieldValue()));

                        }
                    }
                }else {
                    // 如果变量是单选，下拉，复选，则获取字典的值
                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormRuleValue.getFieldType())||
                            BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormRuleValue.getFieldType())||
                            BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(templateFormRuleValue.getFieldType())
                    ){
                        // 从ruleData中获取值
                        String key="$" + templateFormRuleValue.getVisitId() + challenge_split + templateFormRuleValue.getFormId() + challenge_split + templateFormRuleValue.getFormDetailId() + "$";
                        Object o = ruleData.get(key);
                        if (ObjectUtil.isNotEmpty(o)){
                            // 根据值获取字典的值
                            if(BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormRuleValue.getDicResource())){
                                Dictionary dictionary= dictionaryService.getDictionaryInfo(o.toString());
                                if(dictionary != null){
                                    ruleData.put(key, formatValue(dictionary.getValue()));
                                }
                            }
                            if(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormRuleValue.getDicResource())||BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormRuleValue.getDicResource())){
                                ProjectDictionary dictionary= projectDictionaryService.getDictionaryInfo(o.toString());
                                if(dictionary != null){
                                    ruleData.put(key, formatValue(dictionary.getOptionValue()));
                                }
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 获取参与计算的变量集合
     * @return
     */
    private List<TemplateFormDvpVal> getDvpVals(Long ruleId) {
        TemplateFormDvpValExample example1 = new TemplateFormDvpValExample();
        TemplateFormDvpValExample.Criteria criteria1 = example1.createCriteria();
        criteria1.andDvpRuleIdEqualTo(ruleId);
        return templateFormDvpValMapper.selectByExample(example1);
    }

    /**
     * 根据质疑规则ID获取质疑信息
     * @param dvpRuleId
     * @return
     */
    @Nullable
    private ProjectTesteeChallenge getProjectTesteeChallenge(Long dvpRuleId,Long groupId,Long rowId,Long testeeId) {
        ProjectTesteeChallengeExample challengeExample = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria challengeExampleCriteria = challengeExample.createCriteria();
        challengeExampleCriteria.andDvpRuleIdEqualTo(dvpRuleId);
        if(groupId!=null){
            challengeExampleCriteria.andGroupIdEqualTo(groupId.toString());
        }
        if(rowId!=null){
            challengeExampleCriteria.andFormResultTableRownoEqualTo(rowId);
        }
        challengeExampleCriteria.andTesteeIdEqualTo(testeeId);
        List<ProjectTesteeChallenge> challenges = projectTesteeChallengeMapper.selectByExample(challengeExample);
        ProjectTesteeChallenge challenge0 = null;
        if (!challenges.isEmpty()) {
            challenge0 = challenges.get(0);
        }
        return challenge0;
    }

    private ProjectTesteeChallenge saveChallenge(ProjectTesteeResultParam projectTesteeResultParam, Long dvpRuleId, TemplateFormDvpRule formDvpRule) {
        // 生成质疑信息
        ProjectTesteeChallenge challenge = new ProjectTesteeChallenge();
        challenge.setId(SnowflakeIdWorker.getUuid());
        challenge.setProjectId(formDvpRule.getProjectId());
        challenge.setDvpRuleId(dvpRuleId);
        challenge.setTesteeId(projectTesteeResultParam.getTesteeId());
        challenge.setVisitId(formDvpRule.getVisitId());
        challenge.setFormId(formDvpRule.getFormId());
        challenge.setIfSystem(true);
        challenge.setProjectOrgId(projectTesteeResultParam.getProjectOrgId().toString());
        challenge.setGroupId(formDvpRule.getGroupId()==null?null:formDvpRule.getGroupId().toString());
        challenge.setReceiveUserId(SecurityUtils.getUserIdValue());
        challenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
        challenge.setContent(formDvpRule.getContent());
        challenge.setFormDetailId(formDvpRule.getFormDetailId());
        challenge.setFormTableId(formDvpRule.getFormTableId());
        challenge.setStatus(BusinessConfig.VALID_STATUS);
        challenge.setCreateUser(SecurityUtils.getUserIdValue());
        challenge.setCreateTime(new Date());
        challenge.setTenantId(projectTesteeResultParam.getTenantId());
        challenge.setPlatformId(projectTesteeResultParam.getPlatformId());
        return challenge;
    }

    /**
     * 设置规则信息数据
     */
    private Map<String, Object> getTemplateFormRuleComputeData(String key ,String value) {
        Map<String, Object> dataMap = new HashMap<>();
        String formatKey = "$" + key + "$";
        Object formatValue =formatValue(value);
        dataMap.put(formatKey, formatValue);
        return dataMap;
    }

    private static Object formatValue(String fieldValue) {
        if (StringUtils.isBlank(fieldValue) ){
            return "";
        }
        Object inputValue;
        Date date = DateUtils.isDate(fieldValue);
        if (date!= null) {
            inputValue = date;
        } else if (NumberUtil.isDouble(fieldValue)) {
            inputValue = Double.parseDouble(fieldValue);
        } else if (NumberUtil.isLong(fieldValue)) {
            inputValue = Long.parseLong(fieldValue);
        } else {
            inputValue = fieldValue;
        }
        return inputValue;
    }

    public String getProjectTesteeFormComplateWrapper(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId) {
        ProjectTesteeFormVariableComplateResultVo projectTesteeFormVariableComplateResultVo = projectTesteeResultService.getProjectTesteeFormVariableIdComplateCount(projectId, projectOrgId, planId, visitId, formId, testeeId);
        if(projectTesteeFormVariableComplateResultVo == null){
            projectTesteeFormVariableComplateResultVo = new ProjectTesteeFormVariableComplateResultVo();
            projectTesteeFormVariableComplateResultVo.setInputCount(0);
            projectTesteeFormVariableComplateResultVo.setComplateCount(0);
            projectTesteeFormVariableComplateResultVo.setVariableCount(projectTesteeFormVariableComplateResultVo.getVariableCount());
        }
        ProjectTesteeFormVariableComplateResultVo projectTesteeTableVariableComplateResultVo = projectTesteeResultService.getProjectTesteeTableVariableIdComplateCount(projectId, projectOrgId, planId, visitId, formId, testeeId);
        if(projectTesteeTableVariableComplateResultVo == null){
            projectTesteeTableVariableComplateResultVo = new ProjectTesteeFormVariableComplateResultVo();
            projectTesteeTableVariableComplateResultVo.setInputCount(0);
            projectTesteeTableVariableComplateResultVo.setComplateCount(0);
        }
        if(projectTesteeFormVariableComplateResultVo.getComplateCount() == 0 && projectTesteeFormVariableComplateResultVo.getInputCount() == 0){
            return FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode();
        }
        if(projectTesteeFormVariableComplateResultVo.getComplateCount() == projectTesteeFormVariableComplateResultVo.getVariableCount()){
            return FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode();
        }
        if(projectTesteeFormVariableComplateResultVo.getInputCount() > 0){
            return FormVariableComplateStatus.FORM_VAR_FILL.getCode();
        }
        return null;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_02, formType = BusinessConfig.PROJECT_VISIT_CRF_TABLE)
    public CustomResult saveProjectTesteeTableRowRecord(ProjectTesteeTableParam projectTesteeTableParam) {
        if(projectTesteeTableParam.getPatientPlanId() == null){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectTesteeTableParam.getProjectId().toString());
            if(flowPlanInfo != null){projectTesteeTableParam.setPatientPlanId(flowPlanInfo.getId());}
        }
        // 保存表格行记录
        CustomResult customResult = projectTesteeResultService.saveProjectTesteeTableRowRecord(projectTesteeTableParam);
        // 计算表格完成状态
        projectTesteeTableParam.setTenantId(SecurityUtils.getSystemTenantId());
        projectTesteeTableParam.setPlatformId(SecurityUtils.getSystemPlatformId());
        this.comPutefullStatus(projectTesteeTableParam);
        return customResult;
    }

    /**
     * 计算表格完成状态
     * @param projectTesteeTableParam
     */
    private void comPutefullStatus(ProjectTesteeTableParam projectTesteeTableParam) {
        ExecutorService executorService = Executors.newFixedThreadPool(1);
        executorService.execute(() -> {
            try {
                //计算表格完成状态
                String projectId = projectTesteeTableParam.getProjectId().toString();
                String planId = projectTesteeTableParam.getPatientPlanId().toString();
                String visitId = projectTesteeTableParam.getVisitId().toString();
                String formId = projectTesteeTableParam.getFormId().toString();
                String formDetailId = projectTesteeTableParam.getFormDetailId().toString();
                String tenantId = projectTesteeTableParam.getTenantId();
                String platFormId = projectTesteeTableParam.getPlatformId();
                String testeeGroupId = null;
                if (projectTesteeTableParam.getTesteeGroupId() != null) {
                    testeeGroupId = projectTesteeTableParam.getTesteeGroupId().toString();
                }
                String testeeId = projectTesteeTableParam.getTesteeId().toString();
                String tableComplateStatus = projectTesteeTableParam.getTableComplateStatus();
                //设置变量记录保存信息----表格变量特殊处理
                String result = projectTesteeResultService.saveProjectTesteeFormTableResult(projectId, projectTesteeTableParam.getPatientPlanId().toString(), visitId, formId, formDetailId, testeeGroupId, testeeId, tableComplateStatus, tenantId, platFormId);
                if (BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(result)) {
                    ProjectTesteeResult customTesteeFormResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, planId, visitId, formId, "", formDetailId, testeeId);
                    if (customTesteeFormResult != null) {
                        TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
                        if (templateFormDetailConfig != null) {
                            customTesteeFormResult.setLabel(templateFormDetailConfig.getLabel());
                            customTesteeFormResult.setFieldName(templateFormDetailConfig.getFieldName());
                            customTesteeFormResult.setCreateUser(customTesteeFormResult.getCreateUser());
                        }
                        customTesteeFormResult.setComplateStatus(projectTesteeTableParam.getTableComplateStatus());
                        projectTesteeResultService.updateByPrimaryKeySelective(customTesteeFormResult);
                    }
                }
                //设置当前表单表格进度
                //String operator = projectTesteeTableParam.getCreateUserId();
                //String complateStatus = getProjectTesteeFormComplateWrapper(projectId, planId, visitId, formId, testeeId);
                //AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.updateProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, testeeId, operator, complateStatus, SecurityUtils.getSystemTenantId(), SecurityUtils.getSystemPlatformId()));

                if (projectTesteeTableParam.getPatientSubmit()) {
                    //设置任务日历相关信息
                    ProjectPatientResult currentDatePatientTask = projectPatientResultService.getCurrentDatePatientTask(projectTesteeTableParam.getProjectId().toString(), projectTesteeTableParam.getPatientPlanId().toString(), projectTesteeTableParam.getTaskId().toString(), projectTesteeTableParam.getTaskDate(), projectTesteeTableParam.getTesteeId().toString());
                    if (currentDatePatientTask == null) {
                        //需要补偿同步患者端任务数据
                    } else {
                        currentDatePatientTask.setTaskRate(projectTesteeTableParam.getTaskRate());
                        currentDatePatientTask.setPushRate(projectTesteeTableParam.getPushRate());
                        currentDatePatientTask.setSubmitTime(new Date());
                        currentDatePatientTask.setComplateStatus(projectTesteeTableParam.getComplateStatus());
                        if (PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectTesteeTableParam.getComplateStatus())) {
                            currentDatePatientTask.setComplateTime(new Date());
                        }
                        if (projectTesteeTableParam.getTaskDate() != null) {
                            currentDatePatientTask.setTaskDate(projectTesteeTableParam.getTaskDate());
                        }
                        projectPatientResultService.savePatientResult(currentDatePatientTask);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        executorService.shutdown();
    }

    @Override
    public CustomResult saveProjectTesteeTableColumnRecord(ProjectTesteeTableColumnParam projectTesteeTableColumnParam) {
        CustomResult customResult = new CustomResult();
        String projectId = projectTesteeTableColumnParam.getProjectId().toString();
        String planId = projectTesteeTableColumnParam.getPlanId().toString();
        String visitId = projectTesteeTableColumnParam.getVisitId().toString();
        String formId = projectTesteeTableColumnParam.getFormId().toString();
        String formExpandId = projectTesteeTableColumnParam.getFormExpandId().toString();
        String formDetailId = projectTesteeTableColumnParam.getFormDetailId().toString();
        String formTableId = projectTesteeTableColumnParam.getFormTableId().toString();
        String testeeId = projectTesteeTableColumnParam.getTesteeId().toString();
        String testeeGroupId = projectTesteeTableColumnParam.getTesteeGroupId() == null ? "" : projectTesteeTableColumnParam.getTesteeGroupId().toString();
        List<ProjectTesteeTableVo> projectTesteeTableRowRecordList = projectTesteeTableService.getProjectTesteeTableColumnRecordList(projectId, planId, visitId, formId, formExpandId, formDetailId, formTableId, testeeGroupId, testeeId);
        for (ProjectTesteeTableVo projectTesteeTableVo : projectTesteeTableRowRecordList) {
            ProjectTesteeTable projectTesteeTable = new ProjectTesteeTable();
            projectTesteeTable.setId(projectTesteeTableVo.getId());
            projectTesteeTable.setFieldName(projectTesteeTableColumnParam.getFieldName());
            projectTesteeTable.setFieldValue(projectTesteeTableColumnParam.getFieldValue());
            projectTesteeTable.setUnitValue(projectTesteeTableColumnParam.getUnitValue());
            projectTesteeTable.setUpdateUser(SecurityUtils.getUserIdValue());
            projectTesteeTable.setUpdateTime(new Date());
            projectTesteeTableService.updateProjectTesteeTableRowRecord(projectTesteeTable);
        }
        return customResult;
    }

    @Override
    public CustomResult modifyTesteeVisitFormProcess(ProjectTesteeFormProcessParam projectTesteeFormProcessParam) {
        CustomResult customResult = new CustomResult();
        String projectId = projectTesteeFormProcessParam.getProjectId().toString();
        String projectOrgId = projectTesteeFormProcessParam.getProjectOrgId().toString();
        String formExpandId =  projectTesteeFormProcessParam.getFormExpandId() == null ? "" : projectTesteeFormProcessParam.getFormExpandId().toString();
        String status = projectTesteeFormProcessParam.getStatus() == null ? "" : projectTesteeFormProcessParam.getStatus();
        if(projectTesteeFormProcessParam.getPlanId() == null){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlanInfo != null){projectTesteeFormProcessParam.setPlanId(flowPlanInfo.getId());}
        }
        String operator = projectTesteeFormProcessParam.getOperator();
        String complateStatus = projectTesteeFormProcessParam.getComplateStatus();
        String formComplateStatus = projectTesteeFormProcessParam.getVariableCount() == projectTesteeFormProcessParam.getVariableComplateCount() ? FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode() : projectTesteeFormProcessParam.getVariableComplateCount() == 0 ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL.getCode();
        if (StringUtils.isNotEmpty(complateStatus)) {
            String planId = projectTesteeFormProcessParam.getPlanId().toString();
            String visitId = projectTesteeFormProcessParam.getVisitId().toString();
            String formId = projectTesteeFormProcessParam.getFormId().toString();
            String testeeId = projectTesteeFormProcessParam.getTesteeId().toString();
            String systemTenantId = SecurityUtils.getSystemTenantId();
            String systemPlatformId = SecurityUtils.getSystemPlatformId();
            saveProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId, status, operator, complateStatus, formComplateStatus, "", systemTenantId, systemPlatformId);
        }
        return customResult;
    }

    @Override // TODO
    public List<ProjectTesteeVo> getTesteeFormResultList(String projectId, String visitId, String testeeId) {
        projectTesteeResultService.getTesteeFormResultList(projectId, visitId, testeeId);
        return null;
    }

    @Override
    public List<ProjectTesteeVo> getProjectTesteeAnalysisListByProjectId(String projectId, String planId, String projectOrgId) {
        if (StringUtils.isNotEmpty(projectOrgId)) {projectOrgId = getQueryWrapperParams(projectOrgId);}
        return projectTesteeInfoMapper.getProjectTesteeAnalysisListByProjectId(projectId, planId, projectOrgId);
    }

    @Override
    public String saveProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId,
                                               String formExpandId, String testeeId, String status, String operator,
                                               String complateStatus, String formComplateStatus, String changleCount,
                                               String tenantId, String platformId) {
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo != null){planId = flowPlanInfo.getId().toString();}
        if(StringUtils.isEmpty(complateStatus)){
            complateStatus = getProjectTesteeFormComplateWrapper(projectId, projectOrgId, planId, visitId, formId, testeeId);
        }
        ProjectTesteeProcess projectTesteeProcess = getProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId);
        if (projectTesteeProcess == null) {
            ProjectTesteeProcess record = new ProjectTesteeProcess();
            record.setId(SnowflakeIdWorker.getUuid());
            record.setProjectId(Long.parseLong(projectId));
            if(StringUtils.isNotEmpty(projectOrgId)){
                record.setOwnerOrgId(projectOrgId);
            }
            record.setPlanId(Long.parseLong(planId));
            record.setVisitId(Long.parseLong(visitId));
            record.setFormId(Long.parseLong(formId));
            if(StringUtils.isNotEmpty(formExpandId)){
                record.setFormExpandId(Long.parseLong(formExpandId));
            }
            record.setTesteeId(Long.parseLong(testeeId));
            if (StringUtils.isNotEmpty(complateStatus)) {
                record.setComplateStatus(complateStatus);
            }
            if (StringUtils.isNotEmpty(formComplateStatus)) {
                record.setFormComplateStatus(formComplateStatus);
            }
            if (StringUtils.isNotEmpty(changleCount)) {
                record.setChangleCount(Integer.parseInt(changleCount));
            }
            record.setStatus(BusinessConfig.VALID_STATUS);
            if(StringUtils.isNotEmpty(status)){record.setStatus(status);}
            record.setCreateTime(new Date());
            record.setCreateUserId(operator);
            record.setTenantId(tenantId);
            record.setPlatformId(platformId);
            if(StringUtils.isEmpty(tenantId)){
                record.setTenantId(SecurityUtils.getSystemTenantId());
            }
            if(StringUtils.isEmpty(platformId)){
                record.setPlatformId(SecurityUtils.getSystemPlatformId());
            }
            projectTesteeProcessMapper.insertSelective(record);
        } else {
            if(StringUtils.isNotEmpty(formExpandId)){
                projectTesteeProcess.setFormExpandId(Long.parseLong(formExpandId));
            }
            if (StringUtils.isNotEmpty(complateStatus)) {
                projectTesteeProcess.setComplateStatus(complateStatus);
            }
            if (StringUtils.isNotEmpty(formComplateStatus)) {
                projectTesteeProcess.setFormComplateStatus(formComplateStatus);
            }
            if (StringUtils.isNotEmpty(changleCount)) {
                projectTesteeProcess.setChangleCount(Integer.parseInt(changleCount));
            }
            if(StringUtils.isNotEmpty(status)){projectTesteeProcess.setStatus(status);}
            projectTesteeProcess.setUpdateUserId(operator);
            projectTesteeProcess.setUpdateTime(new Date());
            projectTesteeProcessMapper.updateByPrimaryKeySelective(projectTesteeProcess);
        }
        return null;
    }

    @Override
    public ProjectTesteeProcess getProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId) {
        return projectTesteeProcessMapper.getProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId);
    }

    private void saveProjectTesteeTableProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId, String createUser, String complateStatus, String tableComplateStatus) {
        ProjectTesteeProcess projectTesteeProcess = getProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId);
        if (projectTesteeProcess == null) {
            ProjectTesteeProcess record = new ProjectTesteeProcess();
            record.setId(SnowflakeIdWorker.getUuid());
            record.setProjectId(Long.parseLong(projectId));
            if(StringUtils.isNotEmpty(projectOrgId)){
                record.setOwnerOrgId(projectOrgId);
            }
            record.setPlanId(Long.parseLong(planId));
            record.setVisitId(Long.parseLong(visitId));
            if(StringUtils.isNotEmpty(formExpandId)){
                record.setFormExpandId(Long.parseLong(formExpandId));
            }
            record.setFormId(Long.parseLong(formId));
            record.setTesteeId(Long.parseLong(testeeId));
            if (StringUtils.isNotEmpty(complateStatus)) {
                record.setComplateStatus(complateStatus);
            }
            if (StringUtils.isNotEmpty(tableComplateStatus)) {
                record.setTableComplateStatus(tableComplateStatus);
            }
            record.setStatus(BusinessConfig.VALID_STATUS);
            record.setCreateTime(new Date());
            record.setCreateUserId(createUser);
            record.setTenantId(SecurityUtils.getSystemTenantId());
            record.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectTesteeProcessMapper.insertSelective(record);
        } else {
            if (StringUtils.isNotEmpty(complateStatus)) {
                projectTesteeProcess.setComplateStatus(complateStatus);
            }
            if (StringUtils.isNotEmpty(tableComplateStatus)) {
                projectTesteeProcess.setTableComplateStatus(tableComplateStatus);
            }
            projectTesteeProcess.setUpdateTime(new Date());
            projectTesteeProcess.setUpdateUserId(createUser);
            projectTesteeProcessMapper.updateByPrimaryKeySelective(projectTesteeProcess);
        }
    }

    @Override
    public String getProjectTesteeTableCRF(String projectId, String visitId, String testeeId, String formDetailId) {
        TemplateFormDetail templateFormDetail = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
        if (templateFormDetail != null) {
            ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", visitId, templateFormDetail.getFormId().toString(), "", formDetailId, testeeId);
            if (projectTesteeResult != null) {
                return projectTesteeResult.getFieldValue();
            }
        }
        return "";
    }

    @Override
    public TesteeChallengeVo getProjectVisitChallengeStatus(String projectId, String visitId, String testeeId) {
        TesteeChallengeVo testeeChallengeVo = new TesteeChallengeVo();
        List<String> challengeNoApplyList = new ArrayList<>();
        List<String> challengeApplyedList = new ArrayList<>();
        List<String> challengeClosedList = new ArrayList<>();

        List<ProjectChallengeVo> challengeDatalist = projectTesteeChallengeService.getProjectChallengeListByVisitId(projectId, visitId, testeeId);
        for (ProjectChallengeVo projectTesteeChallenge : challengeDatalist) {
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeNoApplyList.add(projectTesteeChallenge.getId().toString());
            }
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeApplyedList.add(projectTesteeChallenge.getId().toString());
            }
            if (ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName().equals(projectTesteeChallenge.getReplyCloseStatus())) {
                challengeClosedList.add(projectTesteeChallenge.getId().toString());
            }
        }
        if (challengeDatalist.size() == 0) {
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_01.getName());
        }
        if (challengeDatalist.size() == 0 || challengeNoApplyList.size() > 0) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        }
        if (challengeNoApplyList.size() == 0 && challengeApplyedList.size() > 0) {
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
        }
        if (challengeDatalist.size() > 0 && challengeDatalist.size() == challengeClosedList.size()) {
            testeeChallengeVo.setChallengeButtonStatus(ProjectChallengeButtonEnum.PROJECT_CHALLENGE_BUTTON_STATUS_02.getName());
            testeeChallengeVo.setChallengeStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
        }
        return testeeChallengeVo;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_02)
    public CustomResult saveMobileProjectTesteeBaseInfo(ProjectTesteeParam projectTesteeParam) {
        CustomResult customResult = new CustomResult();
        if (checkProjectTesteeCode(projectTesteeParam)) {
            customResult.setMessage(projectTesteeParam.getTesteeCode() + BusinessConfig.PROJECT_TESTEE_CODE_FOUND);
            return customResult;
        }
        if (checkProjectTesteeMobile(projectTesteeParam)) {
            customResult.setMessage(projectTesteeParam.getContant() + BusinessConfig.PROJECT_TESTEE_MOBILE_FOUND);
            return customResult;
        }

        String tenantId = SecurityUtils.getSystemTenantId();
        String platformId = SecurityUtils.getSystemPlatformId();
        ProjectTesteeInfo projectTesteeInfo = projectTesteeInfoMapper.selectProjectTesteeInfoByUserId(projectTesteeParam.getUserId().toString(), tenantId, platformId);

        ProjectVisitUser pvu = getMobileProjectVisitUser(projectTesteeParam.getProjectId().toString(),projectTesteeParam.getOwnerOrgId(), projectTesteeInfo.getId().toString());
        if(pvu == null){
            ProjectVisitUser projectVisitUser = new ProjectVisitUser();
            projectVisitUser.setProjectId(projectTesteeParam.getProjectId());
            projectVisitUser.setTesteeId(projectTesteeInfo.getId());
            projectVisitUser.setTesteeCode(projectTesteeParam.getTesteeCode());
            if(StringUtils.isNotEmpty(projectTesteeParam.getOwnerOrgId())){
                projectVisitUser.setOwnerOrgId(projectTesteeParam.getOwnerOrgId());
                ProjectOrgInfo projectOrgInfo = projectOrgInfoMapper.selectByPrimaryKey(Long.valueOf(projectTesteeParam.getOwnerOrgId()));
                if(projectOrgInfo != null){
                    Organization organizationInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
                    projectVisitUser.setOwnerOrgName(organizationInfo != null ? organizationInfo.getName() : "");
                }
            }
            projectVisitUser.setInformedDate(projectTesteeParam.getInformedDate());
            projectVisitUser.setVisitCardNo(projectTesteeParam.getVisitCardNo());
            //参与者入组研究状态
            projectVisitUser.setResearchStatus(projectTesteeParam.getResearchStatus());
            projectVisitUser.setStatus(BusinessConfig.VALID_STATUS);
            //医生创建病历设置为已审核
            projectVisitUser.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
            projectVisitUser.setBindResult(false);

            if (!projectTesteeParam.getBindTestee()) {
                projectVisitUser.setReviewStatus(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_02.getName());
            }
            //患者端自建病历需要审核
            if (projectTesteeParam.getBindTesteeCheck()) {
                projectVisitUser.setSelfRecord(false);
                projectVisitUser.setReviewFlag(true);
            } else {
                projectVisitUser.setSelfRecord(true);
                projectVisitUser.setReviewFlag(false);
            }

            //设置项目患者绑定来源
            projectVisitUser.setBindResource(Constants.BIND_RESORCE_02);

            projectVisitUser.setCreateUserId(projectTesteeParam.getOperator());
            projectVisitUser.setCreateTime(new Date());
            projectVisitUser.setTenantId(SecurityUtils.getSystemTenantId());
            projectVisitUser.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectVisitUserService.insert(projectVisitUser);
            //补偿参与者信息
            saveProjectTesteeBaseResult(projectTesteeParam.getProjectId().toString(), projectTesteeParam, projectTesteeInfo);
        }else{
            pvu.setStatus(BusinessConfig.VALID_STATUS);
            projectVisitUserService.updateProjectVisitUserById(pvu);
        }
        customResult.setData(projectTesteeInfo.getId());
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    public void saveProjectTesteeBaseResult(String projectId, ProjectTesteeParam projectTesteeParam, ProjectTesteeInfo projectTesteeInfo){
        TemplateFormConfigVo templateTesteeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
        if (templateTesteeFormBaseInfo == null) {
            return;
        }
        String formId = templateTesteeFormBaseInfo.getFormId();
        // 查询参与者表单自定义标题记录
        Set<TemplateFormDetailVo> templateTesteeFormHeadRowList = new HashSet<>();
        List<ParticipantHeadRowViewVo.VariableHeadRowConfig> variableHeadRowConfigList = new ArrayList<>();

        TemplateFormConfigVo testeeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
        List<TemplateFormDetailVo> templateTesteeFormDetailList = templateConfigService.getTemplateTesteeFormDetailBaseInfo(projectId, testeeFormBaseInfo.getFormId(), true);
        for (TemplateFormDetailVo templateFormDetailVo : templateTesteeFormDetailList) {
            if (templateFormDetailVo.getShowTitle()) {
                templateTesteeFormHeadRowList.add(templateFormDetailVo);
            }
        }
        if(CollectionUtil.isNotEmpty(templateTesteeFormHeadRowList)){
            templateTesteeFormHeadRowList.forEach(templateFormDetail->{
                ParticipantHeadRowViewVo.VariableHeadRowConfig variableHeadRowConfig = new ParticipantHeadRowViewVo.VariableHeadRowConfig();
                BeanUtils.copyProperties(templateFormDetail, variableHeadRowConfig);
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_REAL_NAME.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_REAL_NAME);
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_ACRONYM.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_ACRONYM);
                }
                if(BusinessConfig.PROJECT_TESTEE_QUERY_FIELD_VISIT_CARD_NO.equalsIgnoreCase(templateFormDetail.getFieldName())){
                    variableHeadRowConfig.setQueryField(true);
                    variableHeadRowConfig.setQueryFieldValue(BusinessConfig.QUERY_FIELD_VISIT_CARD_NO);
                }
                variableHeadRowConfigList.add(variableHeadRowConfig);
            });
            // TODO
            for(ParticipantHeadRowViewVo.VariableHeadRowConfig vl : variableHeadRowConfigList){
                if(vl.getLabel().equals("性别") && StringUtils.isNotEmpty(projectTesteeInfo.getGender())){
                    List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId("***********", "", null);
                    for(Dictionary dl : dictionaryList){
                        if(projectTesteeInfo.getGender().equals(dl.getName())){
                            projectTesteeResultService.saveTesteeBaseFormVariableInfo(projectId, formId, vl.getId().toString(), vl.getLabel(), vl.getFieldName(), projectTesteeInfo.getGender(), dl.getId().toString(), projectTesteeInfo.getId().toString(), projectTesteeParam.getUserId().toString(), BusinessConfig.TESTEE_INPUT_DATA_FROM_2);
                        }
                    }
                }
                if(vl.getLabel().equals("出生日期") && projectTesteeInfo.getBirthday() != null){
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    projectTesteeResultService.saveTesteeBaseFormVariableInfo(projectId, formId, vl.getId().toString(), vl.getLabel(), vl.getFieldName(), vl.getUnitValue(), sdf.format(projectTesteeInfo.getBirthday()), projectTesteeInfo.getId().toString(), projectTesteeParam.getUserId().toString(), BusinessConfig.TESTEE_INPUT_DATA_FROM_2);

                }
                if(vl.getLabel().equals("姓名") && StringUtils.isNotEmpty(projectTesteeInfo.getRealName())){
                    projectTesteeResultService.saveTesteeBaseFormVariableInfo(projectId, formId, vl.getId().toString(), vl.getLabel(), vl.getFieldName(), vl.getUnitValue(), projectTesteeInfo.getRealName(), projectTesteeInfo.getId().toString(), projectTesteeParam.getUserId().toString(), BusinessConfig.TESTEE_INPUT_DATA_FROM_2);
                }
            }
        }
    }

    @Override
    public List<ProjectTesteeOrgVo> getTesteeJoinOrgListByUserId(String projectId, String userId) {
        return projectTesteeInfoMapper.getProjectTesteeBaseInfoByUserId(projectId, userId);
    }

    @Override
    public ProjectTesteeBaseVo selectProjectTesteeInfoByUserId(String userId, String tenantId, String platformId){
        return projectTesteeInfoMapper.selectProjectTesteeInfoByUserId(userId, tenantId, platformId);
    }

    @Override
    public ProjectVisitUser getMobileProjectVisitUser(String projectId, String ownerOrgId, String testeeId) {
        return projectVisitUserService.getMobileProjectVisitUser(projectId, ownerOrgId, testeeId);
    }

}
