package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.param.crf.TemplateVariableViewConfigParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewConfigVo;
import com.haoys.user.mapper.TemplateVariableViewBaseMapper;
import com.haoys.user.mapper.TemplateVariableViewConfigMapper;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormTable;
import com.haoys.user.model.TemplateVariableViewBase;
import com.haoys.user.model.TemplateVariableViewConfig;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormVariableViewConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class TemplateFormVariableViewConfigServiceImpl extends BaseService implements TemplateFormVariableViewConfigService {

    @Autowired
    private TemplateVariableViewBaseMapper templateVariableViewBaseMapper;
    @Autowired
    private TemplateVariableViewConfigMapper templateVariableViewConfigMapper;
    @Autowired
    private TemplateConfigService templateConfigService;


    @Override
    public CustomResult saveTemplateFormVariableViewConfig(TemplateVariableViewConfigParam templateVariableViewConfigParam) {
        CustomResult customResult = new CustomResult();
        Long projectId = templateVariableViewConfigParam.getProjectId();
        Long baseFormId = templateVariableViewConfigParam.getBaseFormId();
        Long baseFormDetailId = templateVariableViewConfigParam.getBaseFormDetailId();
        Long baseFormTableId = templateVariableViewConfigParam.getBaseFormTableId();
        Boolean enableTable = templateVariableViewConfigParam.getEnableTable();

        String optionName = templateVariableViewConfigParam.getOptionName();
        String optionValue = templateVariableViewConfigParam.getOptionValue();
        Long variableOptionValueId;
        TemplateVariableViewBase templateVariableViewBase = templateVariableViewBaseMapper.getTemplateFormVariableViewConfig(projectId, baseFormId, baseFormDetailId, baseFormTableId, enableTable, optionValue);
        if(templateVariableViewBase == null){
            TemplateVariableViewBase record = new TemplateVariableViewBase();
            BeanUtils.copyProperties(templateVariableViewConfigParam, record);
            record.setId(SnowflakeIdWorker.getUuid());
            record.setProjectId(projectId);
            record.setFormId(baseFormId);
            record.setFormDetailId(baseFormDetailId);
            record.setFormTableId(baseFormTableId);
            record.setOptionName(optionName);
            record.setOptionValue(optionValue);
            record.setLabel(templateVariableViewConfigParam.getBaseVariableName());
            record.setStatus(BusinessConfig.VALID_STATUS);
            record.setCreateTime(new Date());
            record.setCreateUserId(templateVariableViewConfigParam.getCreateUserId());
            record.setTenantId(SecurityUtils.getSystemTenantId());
            record.setPlatformId(SecurityUtils.getSystemPlatformId());
            templateVariableViewBaseMapper.insertSelective(record);
            variableOptionValueId = record.getId();
        }else {
            variableOptionValueId = templateVariableViewBase.getId();
        }
        List<TemplateVariableViewConfigParam.TemplateVariableViewResultConfigParam> dataList = templateVariableViewConfigParam.getDataList();
        if(CollectionUtil.isNotEmpty(dataList)){
            templateVariableViewConfigMapper.deleteTemplateVariableViewResultConfig(variableOptionValueId);
        }
        for (TemplateVariableViewConfigParam.TemplateVariableViewResultConfigParam templateVariableViewResultConfigParam : dataList) {
            TemplateVariableViewConfig templateVariableViewConfig = new TemplateVariableViewConfig();
            BeanUtils.copyProperties(templateVariableViewResultConfigParam, templateVariableViewConfig);
            templateVariableViewConfig.setId(SnowflakeIdWorker.getUuid());
            templateVariableViewConfig.setVariableOptionId(variableOptionValueId);
            templateVariableViewConfig.setProjectId(projectId);
            templateVariableViewConfig.setStatus(BusinessConfig.VALID_STATUS);
            templateVariableViewConfig.setCreateTime(new Date());
            templateVariableViewConfig.setCreateUserId(templateVariableViewConfigParam.getCreateUserId());
            templateVariableViewConfig.setTenantId(SecurityUtils.getSystemTenantId());
            templateVariableViewConfig.setPlatformId(SecurityUtils.getSystemPlatformId());
            templateVariableViewConfigMapper.insertSelective(templateVariableViewConfig);
        }

        /*if(templateVariableViewConfigParam.getDefaultViewConfig()){
            templateVariableViewBaseMapper.deleteTemplateFormVariableViewConfig(projectId, baseFormId, baseFormDetailId);
            List<TemplateVariableViewConfig> templateVariableViewConfigList = templateVariableViewConfigMapper.getTemplateVariableViewConfigListByDetailId(projectId, baseFormId, baseFormDetailId);
            for (TemplateVariableViewConfig templateVariableViewConfig : templateVariableViewConfigList) {
                templateVariableViewConfigMapper.deleteTemplateVariableViewResultConfig(templateVariableViewConfig.getVariableOptionId());
            }
            return customResult;
        }*/

        if(templateVariableViewConfigParam.getBaseFormTableId() != null){
            TemplateFormTable templateFormTable = templateConfigService.getTemplateFormTableConfigById(templateVariableViewConfigParam.getBaseFormTableId());
            templateFormTable.setEnableViewConfig(templateVariableViewConfigParam.getEnableViewConfig());
            templateConfigService.updateTemplateFormTableConfigById(templateFormTable);
        }
        if(templateVariableViewConfigParam.getBaseFormDetailId() != null){
            TemplateFormDetail templateFormDetail = templateConfigService.getTemplateFormDetailConfigByVariableId(templateVariableViewConfigParam.getBaseFormDetailId().toString());
            templateFormDetail.setEnableViewConfig(templateVariableViewConfigParam.getEnableViewConfig());
            templateConfigService.updateTemplateFormDetailConfigById(templateFormDetail);
        }
        return customResult;
    }

    @Override
    public List<TemplateFormVariableViewConfigVo> getTemplateFormVariableListByOptionId(String projectId, String formId, String optionValueId) {
        List<TemplateFormVariableViewConfigVo> templateFormVariableList = templateVariableViewConfigMapper.getTemplateFormVariableListByOptionId(projectId, formId, optionValueId);
        for (TemplateFormVariableViewConfigVo templateFormVariableViewConfigVo : templateFormVariableList) {
            TemplateFormDetail templateFormDetail = templateConfigService.getTemplateFormDetailConfigByVariableId(templateFormVariableViewConfigVo.getFormDetailId().toString());
            templateFormVariableViewConfigVo.setType(templateFormDetail.getType());
        }
        return templateFormVariableList;
    }
    
    @Override
    public List<TemplateFormVariableViewConfigVo> getTemplateFormVariableListByOptionIds(String projectId, String formId, String optionValueId) {
        String optionValueIds = getQueryWrapperParams(optionValueId);
        List<TemplateFormVariableViewConfigVo> templateFormVariableList = templateVariableViewConfigMapper.getTemplateFormVariableListByOptionIds(projectId, formId, optionValueIds);
        return templateFormVariableList;
    }
    
    @Override
    public TemplateVariableViewBase getTemplateVariableBaseInfo(String projectId, String formId, String formDetailId, String formTableId, String optionValueId) {
        return templateVariableViewBaseMapper.getTemplateVariableBaseInfo(projectId, formId, formDetailId, formTableId, optionValueId);
    }

    @Override
    public List<TemplateVariableViewBase> getTemplateVariableOptionListByVariableId(String projectId, String formId, String formDetailId, String formTableId) {
        return templateVariableViewBaseMapper.getTemplateVariableOptionListByVariableId(projectId, formId, formDetailId, formTableId);
    }


}
