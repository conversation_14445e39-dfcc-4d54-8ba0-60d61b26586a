package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.crf.TemplateFormDvpRuleParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormDvpRuleVo;

import java.util.List;

public interface ProjectTesteeDvpService {


    /**
     * 分页查询dvp设置列表
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<TemplateFormDvpRuleVo> getProjectTemplateDVPRuleList(String projectId, String visitId, String formId, String formDetailId, Integer pageNum, Integer pageSize);

    /**
     * 查询表单变量核查内容
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @return
     */
    List<TemplateFormDvpRuleVo> getFormDetailDvpContent(String projectId, String visitId, String formId, String formDetailId);

    /**
     * 查询表格变量核查内容
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param tableId
     * @return
     */
    List<TemplateFormDvpRuleVo> getTableDetailDvpContent(String projectId, String visitId, String formId, String formDetailId, String tableId);

    /**
     * 添加变量逻辑核查条件
     * @param templateFormDVPRuleParam
     * @return
     */
    CustomResult saveProjectTemplateFormDetailDvpRule(TemplateFormDvpRuleParam templateFormDVPRuleParam);

    /**
     * 启用、停用逻辑核查条件
     * @param id
     * @param enabled
     * @param userId
     * @return
     */
    CustomResult updateProjectTemplateFormDetailDvpRule(String id, String enabled, String userId);

    /**
     * 根据id查询逻辑核查规则
     * @param projectId
     * @param id
     * @return
     */
    TemplateFormDvpRuleVo getTemplateFormDvpRuleRecord(String projectId, String id);

    /**
     * 批量添加变量逻辑核查条件
     * @param params
     * @param userId
     * @return
     */
    CustomResult saveBatchProjectTemplateFormDetailDvpRule(List<TemplateFormDvpRuleParam> params, String userId);
}
