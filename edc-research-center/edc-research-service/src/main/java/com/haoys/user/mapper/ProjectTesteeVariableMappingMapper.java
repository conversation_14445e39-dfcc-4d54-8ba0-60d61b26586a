package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeVariableMapping;
import com.haoys.user.model.ProjectTesteeVariableMappingExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectTesteeVariableMappingMapper {
    long countByExample(ProjectTesteeVariableMappingExample example);

    int deleteByExample(ProjectTesteeVariableMappingExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeVariableMapping record);

    int insertSelective(ProjectTesteeVariableMapping record);

    List<ProjectTesteeVariableMapping> selectByExample(ProjectTesteeVariableMappingExample example);

    ProjectTesteeVariableMapping selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeVariableMapping record, @Param("example") ProjectTesteeVariableMappingExample example);

    int updateByExample(@Param("record") ProjectTesteeVariableMapping record, @Param("example") ProjectTesteeVariableMappingExample example);

    int updateByPrimaryKeySelective(ProjectTesteeVariableMapping record);

    int updateByPrimaryKey(ProjectTesteeVariableMapping record);
}
