package com.haoys.user.service;


import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.entity.SystemRequestLogQuery;
import com.haoys.user.model.SystemRequestLog;

import javax.servlet.http.HttpServletResponse;


/**
 * 操作日志
 */
public interface SystemRequestLogService {

    void insertOperlog(SystemRequestLog systemRequestLog);

    /**
     * 查询系统操作日志集合
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    CommonPage<SystemRequestLogQuery> selectRequestLogListForPage(SystemRequestLogQuery operLog, Integer pageNum, Integer pageSize);

    /**
     * 批量删除系统操作日志
     * 
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    int deleteOperLogByIds(Long[] operIds);

    /**
     * 查询操作日志详细
     * 
     * @param operId 操作ID
     * @return 操作日志对象
     */
    SystemRequestLogQuery selectOperLogById(Long operId);

    /**
     * 清空操作日志
     */
    void cleanOperLog();

    void exportSystemOperLog(HttpServletResponse response, SystemRequestLogQuery operLog);

}
