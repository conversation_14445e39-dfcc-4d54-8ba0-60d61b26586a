package com.haoys.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.haoys.user.common.file.FileUtils;
import com.haoys.user.common.util.FileOperationUtil;
import com.haoys.user.common.util.DateUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.common.util.MemoryMonitorUtil;
import com.haoys.user.config.MisConfig;
import com.haoys.user.domain.param.file.ChunkUploadParam;
import com.haoys.user.domain.param.file.MergeChunkParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.service.ChunkedFileUploadService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.user.storge.cloud.OssStorageFactory;
import com.haoys.user.storge.cloud.OssStorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 分片文件上传服务实现
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 */
@Service
@Slf4j
public class ChunkedFileUploadServiceImpl implements ChunkedFileUploadService {

    /**
     * 分片大小：2MB
     */
    private static final long DEFAULT_CHUNK_SIZE = 2 * 1024 * 1024L;
    
    /**
     * 文件大小阈值：20MB
     */
    private static final long CHUNK_THRESHOLD = 20 * 1024 * 1024L;
    
    /**
     * Redis键前缀
     */
    private static final String REDIS_KEY_PREFIX = "chunked_upload:";
    
    /**
     * 分片过期时间：24小时
     */
    private static final long CHUNK_EXPIRE_HOURS = 24;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private OssStorageConfig ossStorageConfig;

    @Override
    public Map<String, Object> checkUploadStatus(String fileHash, String fileName, Long fileSize, Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String redisKey = REDIS_KEY_PREFIX + fileHash + ":" + userId;
            Map<Object, Object> uploadInfo = redisTemplate.opsForHash().entries(redisKey);
            
            if (uploadInfo.isEmpty()) {
                // 首次上传
                result.put("status", "new");
                result.put("uploadedChunks", new ArrayList<>());
                result.put("needChunked", fileSize >= CHUNK_THRESHOLD);
                result.put("chunkSize", DEFAULT_CHUNK_SIZE);
                result.put("totalChunks", calculateTotalChunks(fileSize));
            } else {
                // 断点续传
                result.put("status", "resumable");
                result.put("uploadedChunks", getUploadedChunks(uploadInfo));
                result.put("needChunked", true);
                result.put("chunkSize", DEFAULT_CHUNK_SIZE);
                result.put("totalChunks", uploadInfo.get("totalChunks"));
                result.put("uploadedSize", calculateUploadedSize(uploadInfo));
            }
            
            result.put("fileHash", fileHash);
            result.put("fileName", fileName);
            result.put("fileSize", fileSize);
            
        } catch (Exception e) {
            log.error("检查上传状态失败: {}", e.getMessage(), e);
            throw new ServiceException("检查上传状态失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> uploadChunk(MultipartFile file, ChunkUploadParam param) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证分片哈希值
            String actualChunkHash = calculateFileHash(file.getInputStream());
            if (!actualChunkHash.equals(param.getChunkHash())) {
                throw new ServiceException("分片数据校验失败");
            }
            
            // 保存分片文件
            String chunkPath = saveChunkFile(file, param);
            
            // 更新Redis中的上传信息
            updateUploadProgress(param, chunkPath);
            
            // 检查是否所有分片都已上传
            boolean allChunksUploaded = checkAllChunksUploaded(param);
            
            result.put("chunkIndex", param.getChunkIndex());
            result.put("chunkPath", chunkPath);
            result.put("allChunksUploaded", allChunksUploaded);
            result.put("uploadedChunks", param.getChunkIndex() + 1);
            result.put("totalChunks", param.getTotalChunks());
            result.put("progress", calculateProgress(param.getChunkIndex() + 1, param.getTotalChunks()));
            
        } catch (Exception e) {
            log.error("上传分片失败: {}", e.getMessage(), e);
            throw new ServiceException("上传分片失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public UploadFileResultVo mergeChunks(MergeChunkParam param) {
        try {
            // 验证所有分片是否已上传
            if (!checkAllChunksUploaded(param.getFileHash(), param.getUserId(), param.getTotalChunks())) {
                throw new ServiceException("分片上传未完成，无法合并");
            }
            
            // 合并分片文件
            String mergedFilePath = mergeChunkFiles(param);
            
            // 验证合并后文件的完整性
            if (!validateMergedFile(mergedFilePath, param.getFileHash(), param.getFileSize())) {
                throw new ServiceException("文件合并后校验失败");
            }
            
            // 上传到最终存储位置
            String finalUrl = uploadToFinalStorage(mergedFilePath, param);
            
            // 清理分片文件
            if (param.getDeleteChunksAfterMerge()) {
                cleanupChunkFiles(param.getFileHash(), param.getUserId());
            }
            
            // 构建返回结果
            UploadFileResultVo result = new UploadFileResultVo();
            result.setFileName(param.getFileName());
            result.setOriginalFilename(param.getFileName());
            result.setFileSize(String.valueOf(param.getFileSize()));
            result.setUrl(finalUrl);
            
            return result;
            
        } catch (Exception e) {
            log.error("合并分片失败: {}", e.getMessage(), e);
            throw new ServiceException("合并分片失败: " + e.getMessage());
        }
    }

    @Override
    public UploadFileResultVo directUpload(MultipartFile file, String projectId, String folderName, Long userId) {
        InputStream fileInputStream = null;
        try {
            // 记录开始时的内存使用情况
            MemoryMonitorUtil.logMemoryUsage("文件上传开始");

            // 检查文件大小
            if (file.getSize() >= CHUNK_THRESHOLD) {
                throw new ServiceException("文件大小超过20MB，请使用分片上传");
            }

            // 检查是否有足够内存处理文件（使用3倍安全系数）
            if (!MemoryMonitorUtil.hasEnoughMemoryForFile(file.getSize(), 3.0)) {
                throw new ServiceException("系统内存不足，无法处理该文件，请稍后重试");
            }

            // 计算文件哈希值 - 使用流式处理避免重复读取
            fileInputStream = file.getInputStream();
            String fileHash = calculateFileHash(fileInputStream);

            // 重新获取输入流用于上传
            fileInputStream.close();
            fileInputStream = file.getInputStream();

            // 生成文件路径
            String filePath = generateFilePath(file.getOriginalFilename(), projectId, folderName);

            // 上传到存储服务
            OssStorageService storageService = OssStorageFactory.build();
            String fileUrl = storageService.upload(fileInputStream, filePath);

            // 记录上传完成后的内存使用情况
            MemoryMonitorUtil.logMemoryUsage("文件上传完成");

            // 如果内存使用率过高，建议进行垃圾回收
            MemoryMonitorUtil.suggestGCIfNeeded(75.0);

            // 构建返回结果
            UploadFileResultVo result = new UploadFileResultVo();
            result.setFileName(file.getOriginalFilename());
            result.setOriginalFilename(file.getOriginalFilename());
            result.setFileSize(String.valueOf(file.getSize()));
            result.setUrl(fileUrl);

            return result;

        } catch (Exception e) {
            log.error("直接上传文件失败: {}", e.getMessage(), e);
            throw new ServiceException("直接上传文件失败: " + e.getMessage());
        } finally {
            // 确保输入流被关闭
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.warn("关闭文件输入流失败: {}", e.getMessage());
                }
            }
        }
    }

    @Override
    public Map<String, Object> getUploadProgress(String fileHash, Long userId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String redisKey = REDIS_KEY_PREFIX + fileHash + ":" + userId;
            Map<Object, Object> uploadInfo = redisTemplate.opsForHash().entries(redisKey);
            
            if (uploadInfo.isEmpty()) {
                result.put("status", "not_found");
                result.put("progress", 0);
            } else {
                List<Integer> uploadedChunks = getUploadedChunks(uploadInfo);
                Integer totalChunks = (Integer) uploadInfo.get("totalChunks");
                
                result.put("status", "uploading");
                result.put("uploadedChunks", uploadedChunks.size());
                result.put("totalChunks", totalChunks);
                result.put("progress", calculateProgress(uploadedChunks.size(), totalChunks));
                result.put("uploadedSize", calculateUploadedSize(uploadInfo));
                result.put("fileSize", uploadInfo.get("fileSize"));
            }
            
        } catch (Exception e) {
            log.error("获取上传进度失败: {}", e.getMessage(), e);
            throw new ServiceException("获取上传进度失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public boolean cancelUpload(String fileHash, Long userId) {
        try {
            // 清理Redis中的上传信息
            String redisKey = REDIS_KEY_PREFIX + fileHash + ":" + userId;
            redisTemplate.delete(redisKey);
            
            // 清理分片文件
            cleanupChunkFiles(fileHash, userId);
            
            return true;
            
        } catch (Exception e) {
            log.error("取消上传失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUploadConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("chunkSize", DEFAULT_CHUNK_SIZE);
        config.put("chunkThreshold", CHUNK_THRESHOLD);
        config.put("maxFileSize", 100 * 1024 * 1024L); // 默认100MB
        config.put("allowedTypes", Arrays.asList("jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar".split(",")));
        config.put("storageType", ossStorageConfig.getOssType().getValue());
        return config;
    }

    @Override
    public int cleanupExpiredChunks() {
        // 实现清理过期分片文件的逻辑
        // 这里简化实现，实际应该扫描文件系统和Redis
        return 0;
    }

    @Override
    public void downloadFile(HttpServletResponse response, String fileHash, Long userId) {
        try {
            // 根据文件哈希值查询文件信息
            Map<String, Object> fileInfo = getFileInfoByHash(fileHash, userId);
            if (fileInfo == null || fileInfo.isEmpty()) {
                throw new ServiceException("文件不存在或无权限访问");
            }

            String fileName = (String) fileInfo.get("fileName");
            String filePath = (String) fileInfo.get("filePath");

            if (StringUtils.isEmpty(filePath)) {
                throw new ServiceException("文件路径不存在");
            }

            // 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 读取文件并写入响应
            Path file = Paths.get(filePath);
            if (!Files.exists(file)) {
                throw new ServiceException("文件不存在: " + filePath);
            }

            try (InputStream inputStream = Files.newInputStream(file);
                 OutputStream outputStream = response.getOutputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            throw new ServiceException("下载文件失败: " + e.getMessage());
        }
    }

    @Override
    public String calculateFileMD5(MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                throw new ServiceException("文件不能为空");
            }

            return calculateFileHash(file.getInputStream());

        } catch (Exception e) {
            log.error("计算文件MD5失败: {}", e.getMessage(), e);
            throw new ServiceException("计算文件MD5失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFileInfoByHash(String fileHash, Long userId) {
        try {
            if (StringUtils.isEmpty(fileHash)) {
                throw new ServiceException("文件哈希值不能为空");
            }

            String redisKey = REDIS_KEY_PREFIX + fileHash + ":" + userId;
            Map<Object, Object> uploadInfo = redisTemplate.opsForHash().entries(redisKey);

            if (uploadInfo.isEmpty()) {
                return new HashMap<>();
            }

            Map<String, Object> result = new HashMap<>();
            result.put("fileHash", fileHash);
            result.put("fileName", uploadInfo.get("fileName"));
            result.put("fileSize", uploadInfo.get("fileSize"));
            result.put("totalChunks", uploadInfo.get("totalChunks"));
            result.put("uploadedChunks", getUploadedChunks(uploadInfo));
            result.put("lastUpdateTime", uploadInfo.get("lastUpdateTime"));
            result.put("status", uploadInfo.get("status"));
            result.put("filePath", uploadInfo.get("filePath"));

            return result;

        } catch (Exception e) {
            log.error("查询文件信息失败: {}", e.getMessage(), e);
            throw new ServiceException("查询文件信息失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateFileMD5(MultipartFile file, String expectedHash) {
        try {
            if (file == null || file.isEmpty()) {
                return false;
            }

            if (StringUtils.isEmpty(expectedHash)) {
                return false;
            }

            String actualHash = calculateFileHash(file.getInputStream());
            return expectedHash.equals(actualHash);

        } catch (Exception e) {
            log.error("验证文件MD5失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // 私有辅助方法
    
    private int calculateTotalChunks(Long fileSize) {
        return (int) Math.ceil((double) fileSize / DEFAULT_CHUNK_SIZE);
    }
    
    private List<Integer> getUploadedChunks(Map<Object, Object> uploadInfo) {
        String chunksStr = (String) uploadInfo.get("uploadedChunks");
        if (StringUtils.isEmpty(chunksStr)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(chunksStr, Integer.class);
    }
    
    private long calculateUploadedSize(Map<Object, Object> uploadInfo) {
        List<Integer> uploadedChunks = getUploadedChunks(uploadInfo);
        return uploadedChunks.size() * DEFAULT_CHUNK_SIZE;
    }
    
    private double calculateProgress(int uploadedChunks, int totalChunks) {
        if (totalChunks == 0) return 0.0;
        return Math.round((double) uploadedChunks / totalChunks * 100.0 * 100.0) / 100.0;
    }
    
    private String calculateFileHash(InputStream inputStream) throws IOException {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192]; // 8KB缓冲区，避免一次性读取大文件
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                md5.update(buffer, 0, bytesRead);
            }

            byte[] digest = md5.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();

        } catch (NoSuchAlgorithmException e) {
            log.error("MD5算法不可用", e);
            throw new IOException("MD5算法不可用", e);
        }
    }
    
    private String saveChunkFile(MultipartFile file, ChunkUploadParam param) throws IOException {
        String chunkDir = MisConfig.getTempPath() + "chunks/" + param.getFileHash() + "/" + param.getUserId();
        Path chunkDirPath = Paths.get(chunkDir);
        Files.createDirectories(chunkDirPath);
        
        String chunkFileName = "chunk_" + param.getChunkIndex();
        Path chunkFilePath = chunkDirPath.resolve(chunkFileName);
        
        try (InputStream inputStream = file.getInputStream();
             OutputStream outputStream = Files.newOutputStream(chunkFilePath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        
        return chunkFilePath.toString();
    }
    
    private void updateUploadProgress(ChunkUploadParam param, String chunkPath) {
        String redisKey = REDIS_KEY_PREFIX + param.getFileHash() + ":" + param.getUserId();
        
        // 获取已上传的分片列表
        String chunksStr = (String) redisTemplate.opsForHash().get(redisKey, "uploadedChunks");
        List<Integer> uploadedChunks = StringUtils.isEmpty(chunksStr) ? 
            new ArrayList<>() : JSON.parseArray(chunksStr, Integer.class);
        
        if (!uploadedChunks.contains(param.getChunkIndex())) {
            uploadedChunks.add(param.getChunkIndex());
            Collections.sort(uploadedChunks);
        }
        
        // 更新Redis
        Map<String, Object> uploadInfo = new HashMap<>();
        uploadInfo.put("fileHash", param.getFileHash());
        uploadInfo.put("fileName", param.getFileName());
        uploadInfo.put("fileSize", param.getFileSize());
        uploadInfo.put("totalChunks", param.getTotalChunks());
        uploadInfo.put("uploadedChunks", JSON.toJSONString(uploadedChunks));
        uploadInfo.put("lastUpdateTime", System.currentTimeMillis());
        
        redisTemplate.opsForHash().putAll(redisKey, uploadInfo);
        redisTemplate.expire(redisKey, CHUNK_EXPIRE_HOURS, TimeUnit.HOURS);
    }
    
    private boolean checkAllChunksUploaded(ChunkUploadParam param) {
        return checkAllChunksUploaded(param.getFileHash(), param.getUserId(), param.getTotalChunks());
    }
    
    private boolean checkAllChunksUploaded(String fileHash, Long userId, Integer totalChunks) {
        String redisKey = REDIS_KEY_PREFIX + fileHash + ":" + userId;
        String chunksStr = (String) redisTemplate.opsForHash().get(redisKey, "uploadedChunks");
        
        if (StringUtils.isEmpty(chunksStr)) {
            return false;
        }
        
        List<Integer> uploadedChunks = JSON.parseArray(chunksStr, Integer.class);
        return uploadedChunks.size() == totalChunks;
    }
    
    private String mergeChunkFiles(MergeChunkParam param) throws IOException {
        String chunkDir = MisConfig.getTempPath() + "chunks/" + param.getFileHash() + "/" + param.getUserId();
        String mergedFilePath = MisConfig.getTempPath() + "merged/" + param.getFileHash() + "_" + param.getFileName();
        
        Path mergedFilePathObj = Paths.get(mergedFilePath);
        Files.createDirectories(mergedFilePathObj.getParent());
        
        try (OutputStream outputStream = Files.newOutputStream(mergedFilePathObj)) {
            for (int i = 0; i < param.getTotalChunks(); i++) {
                Path chunkFilePath = Paths.get(chunkDir, "chunk_" + i);
                if (!Files.exists(chunkFilePath)) {
                    throw new ServiceException("分片文件缺失: chunk_" + i);
                }
                
                try (InputStream inputStream = Files.newInputStream(chunkFilePath)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }
            }
        }
        
        return mergedFilePath;
    }
    
    private boolean validateMergedFile(String filePath, String expectedHash, Long expectedSize) throws IOException {
        Path file = Paths.get(filePath);
        
        // 检查文件大小
        if (Files.size(file) != expectedSize) {
            return false;
        }
        
        // 检查文件哈希值
        try (InputStream inputStream = Files.newInputStream(file)) {
            String actualHash = calculateFileHash(inputStream);
            return expectedHash.equals(actualHash);
        }
    }
    
    private String uploadToFinalStorage(String localFilePath, MergeChunkParam param) throws IOException {
        String finalPath = generateFilePath(param.getFileName(), param.getProjectId(), param.getFolderName());
        
        OssStorageService storageService = OssStorageFactory.build();
        try (InputStream inputStream = Files.newInputStream(Paths.get(localFilePath))) {
            return storageService.upload(inputStream, finalPath);
        }
    }
    
    private String generateFilePath(String fileName, String projectId, String folderName) {
        StringBuilder pathBuilder = new StringBuilder();
        
        if (StringUtils.isNotEmpty(projectId)) {
            pathBuilder.append("project/").append(projectId).append("/");
        }
        
        if (StringUtils.isNotEmpty(folderName)) {
            pathBuilder.append(folderName).append("/");
        }
        
        pathBuilder.append(DateUtils.dateTimeNow()).append("/");
        pathBuilder.append(UUID.randomUUID().toString()).append("_").append(fileName);
        
        return pathBuilder.toString();
    }
    
    private void cleanupChunkFiles(String fileHash, Long userId) {
        try {
            // 清理Redis
            String redisKey = REDIS_KEY_PREFIX + fileHash + ":" + userId;
            redisTemplate.delete(redisKey);
            
            // 清理本地分片文件
            String chunkDir = MisConfig.getTempPath() + "chunks/" + fileHash + "/" + userId;
            Path chunkDirPath = Paths.get(chunkDir);
            if (Files.exists(chunkDirPath)) {
                FileOperationUtil.delete(chunkDirPath.toFile());
            }
            
        } catch (Exception e) {
            log.warn("清理分片文件失败: {}", e.getMessage());
        }
    }
}
