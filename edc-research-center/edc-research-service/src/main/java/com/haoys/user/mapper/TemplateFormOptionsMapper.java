package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormOptions;
import com.haoys.user.model.TemplateFormOptionsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormOptionsMapper {
    long countByExample(TemplateFormOptionsExample example);

    int deleteByExample(TemplateFormOptionsExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormOptions record);

    int insertSelective(TemplateFormOptions record);

    List<TemplateFormOptions> selectByExample(TemplateFormOptionsExample example);

    TemplateFormOptions selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormOptions record, @Param("example") TemplateFormOptionsExample example);

    int updateByExample(@Param("record") TemplateFormOptions record, @Param("example") TemplateFormOptionsExample example);

    int updateByPrimaryKeySelective(TemplateFormOptions record);

    int updateByPrimaryKey(TemplateFormOptions record);
}