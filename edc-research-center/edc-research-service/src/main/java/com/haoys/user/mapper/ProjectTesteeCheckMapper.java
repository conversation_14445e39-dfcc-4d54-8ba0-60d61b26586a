package com.haoys.user.mapper;

import com.haoys.user.domain.param.project.ProjectCheckQueryParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeCheckVo;
import com.haoys.user.model.ProjectTesteeCheck;
import com.haoys.user.model.ProjectTesteeCheckExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeCheckMapper {
    long countByExample(ProjectTesteeCheckExample example);

    int deleteByExample(ProjectTesteeCheckExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeCheck record);

    int insertSelective(ProjectTesteeCheck record);

    List<ProjectTesteeCheck> selectByExample(ProjectTesteeCheckExample example);

    ProjectTesteeCheck selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeCheck record, @Param("example") ProjectTesteeCheckExample example);

    int updateByExample(@Param("record") ProjectTesteeCheck record, @Param("example") ProjectTesteeCheckExample example);

    int updateByPrimaryKeySelective(ProjectTesteeCheck record);

    int updateByPrimaryKey(ProjectTesteeCheck record);

    /**
     * 数据稽查列表
     * @param projectCheckQueryParam
     * @return
     */
    List<ProjectTesteeCheckVo> getProjectCheckListForPage(ProjectCheckQueryParam projectCheckQueryParam);

    void cleanAllProjectCheckLog();

    void deleteProjectCheckLog(String projectId, String visitId, String formId, String testeeId);
}