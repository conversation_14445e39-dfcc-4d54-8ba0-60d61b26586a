package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.ProjectVariableRecordFlag;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.participant.ProjectParticipantViewConfigParam;
import com.haoys.user.domain.param.project.ProjectTesteeParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDictionaryVo;
import com.haoys.user.domain.vo.participant.ProjectParticipantViewConfigVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.mapper.ProjectTesteeInfoMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.ProjectDictionary;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeVariableSync;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.ProjectVisitUserExample;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ParticipantService;
import com.haoys.user.service.ProjectDictionaryService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeVariableSyncService;
import com.haoys.user.service.ProjectVisitUserService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormVariableViewConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ParticipantServiceImpl implements ParticipantService {

    private final ProjectTesteeInfoMapper projectTesteeInfoMapper;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final TemplateConfigService templateConfigService;
    private final ProjectTesteeResultService projectTesteeResultService;
    private final DictionaryService dictionaryService;
    private final ProjectDictionaryService projectDictionaryService;
    private final FlowPlanService flowPlanService;
    private final RedisTemplateService redisTemplateService;
    private final ProjectVisitUserService projectVisitUserService;
    private final ProjectTesteeVariableSyncService projectTesteeVariableSyncService;
    private final TemplateFormVariableViewConfigService templateFormVariableViewConfigService;

    @Override
    public ProjectParticipantViewConfigVo getParticipantBaseInfo(String projectId, String testeeId) {
        ProjectParticipantViewConfigVo projectParticipantViewConfigVo = new ProjectParticipantViewConfigVo();
        if(StringUtils.isNotEmpty(testeeId)){
            ProjectTesteeInfo projectTestee = projectTesteeInfoMapper.selectByPrimaryKey(Long.parseLong(testeeId));
            if (projectTestee != null) {
                BeanUtils.copyProperties(projectTestee, projectParticipantViewConfigVo);
            }
        }
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo == null){throw new ServiceException("流程配置未发布");}
        String planId = flowPlanInfo.getId().toString();

        // 查询配置信息
        TemplateFormConfigVo templateTesteeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectId);
        if (templateTesteeFormBaseInfo == null) {
            return projectParticipantViewConfigVo;
        }
        List<ProjectParticipantViewConfigVo.FormVariableConfig> formVariableConfigArrayList = new ArrayList<>();
        List<TemplateFormDetailVo> templateFormDetailVoList = templateConfigService.getTemplateTesteeFormDetailBaseInfo(projectId, templateTesteeFormBaseInfo.getFormId(), null);
        for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailVoList) {
            ProjectParticipantViewConfigVo.FormVariableConfig formVariableConfig = new ProjectParticipantViewConfigVo.FormVariableConfig();
            BeanUtils.copyProperties(templateFormDetailVo, formVariableConfig);
            formVariableConfig.setBaseVariableValueId(templateFormDetailVo.getBaseVariableId());
            if(StringUtils.isNotEmpty(templateFormDetailVo.getDicResource())){
                List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();
                if(BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormDetailVo.getDicResource())){
                    List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId(templateFormDetailVo.getRefDicId(), "", "");
                    if(CollectionUtil.isNotEmpty(dictionaryList)){
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });
                    }
                }
                if(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormDetailVo.getDicResource())){
                    List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormDetailVo.getProjectId().toString(), templateFormDetailVo.getRefDicId(), "", Constants.PROJECT_DIC_TYPE);
                    if(CollectionUtil.isNotEmpty(dictionaryList)){
                        dictionaryList.forEach(dictionary -> {
                            TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                            templateFormDictionaryVo.setId(dictionary.getId().toString());
                            templateFormDictionaryVo.setName(dictionary.getName());
                            templateFormDictionaryList.add(templateFormDictionaryVo);
                        });
                    }
                    //设置默认值
                    List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_RADIO);
                    if(variableTypeList.contains(templateFormDetailVo.getType())){
                        String defaultDicValue = templateFormDetailVo.getDefaultDicValue();
                        if(StringUtils.isNotEmpty(defaultDicValue)){
                            ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(defaultDicValue);
                            if(dictionaryInfo != null){
                                formVariableConfig.setUnitValue(dictionaryInfo.getName());
                            }
                        }
                    }
                }
                if(BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormDetailVo.getDicResource())){
                    List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(templateFormDetailVo.getProjectId().toString(), templateFormDetailVo.getRefDicId(), "", Constants.UNIT_DIC_TYPE);
                    dictionaryList.forEach(dictionary -> {
                        TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                        templateFormDictionaryVo.setId(dictionary.getId().toString());
                        templateFormDictionaryVo.setName(dictionary.getName());
                        templateFormDictionaryList.add(templateFormDictionaryVo);
                    });
                }
                formVariableConfig.setTemplateFormDictionaryList(templateFormDictionaryList);
            }
            // 设置查询参与者基本信息
            ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getTesteeFormBaseInfoByFormVariableId(projectId, templateTesteeFormBaseInfo.getFormId(), templateFormDetailVo.getId().toString(), testeeId);
            if(projectTesteeResult != null){
                formVariableConfig.setTesteeResultId(projectTesteeResult.getId());
                formVariableConfig.setValue(projectTesteeResult.getFieldValue());
                formVariableConfig.setUnitValue(projectTesteeResult.getUnitValue());
                if(StringUtils.isBlank(projectTesteeResult.getFieldValue())){
                    // 查询是否设置源定义配置
                    if(formVariableConfig.getBaseVariableValueId() != null){
                        TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(templateFormDetailVo.getId().toString());
                        ProjectTesteeVariableSync data = projectTesteeVariableSyncService.getVariableSyncByBaseVariableId(formVariableConfig.getBaseVariableValueId());
                        if(data != null && data.getId() != null){
                            // String baseVariableId = data.getBaseVariableId().toString();
                            String baseVisitId = data.getSourceVisitId().toString();
                            String baseFormId = data.getSourceFormId().toString();
                            String baseFormDetailId = data.getSourceVariableId().toString();
                            ProjectTesteeResult testeeFormVariableSyncResult = projectTesteeResultService.getTesteeFormResultValueByFormDetailId(projectId, planId, baseVisitId, baseFormId, "", baseFormDetailId, testeeId);
                            if(testeeFormVariableSyncResult != null){
                                if(StringUtils.isBlank(testeeFormVariableSyncResult.getFieldValue())){
                                    // 查询是否设置了默认值
                                    formVariableConfig.setValue(templateFormDetailConfig.getDefaultValue());
                                    List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT);
                                    if(variableTypeList.contains(templateFormDetailConfig.getType())){
                                        formVariableConfig.setValue(templateFormDetailConfig.getDefaultDicValue());
                                        formVariableConfig.setUnitValue(templateFormDetailConfig.getUnitValue());
                                    }
                                }else {
                                    formVariableConfig.setValue(testeeFormVariableSyncResult.getFieldValue());
                                    formVariableConfig.setUnitValue(testeeFormVariableSyncResult.getUnitValue());
                                }
                            }
                        }
                    }
                }
            }
            formVariableConfigArrayList.add(formVariableConfig);
        }
        projectParticipantViewConfigVo.setFormVariableConfigArrayList(formVariableConfigArrayList);
        return projectParticipantViewConfigVo;
    }

    @Override
    @ProjectVariableRecordFlag(paramterType = BusinessConfig.PARAMTER_TYPE_02)
    public CustomResult saveProjectParticipantViewConfig(ProjectParticipantViewConfigParam projectParticipantViewConfigParam) {
        ProjectTesteeParam projectTesteeParam = new ProjectTesteeParam();
        BeanUtils.copyProperties(projectParticipantViewConfigParam, projectTesteeParam, "formVariableConfigArrayList");
        projectTesteeParam.setProjectId(Long.parseLong(projectParticipantViewConfigParam.getProjectId()));
        projectTesteeParam.setId(projectParticipantViewConfigParam.getId());
        projectTesteeParam.setOperator(projectParticipantViewConfigParam.getCreateUserId());

        ProjectVisitUserExample projectVisitUserExample = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria projectVisitUserExampleCriteria = projectVisitUserExample.createCriteria();
        projectVisitUserExampleCriteria.andProjectIdEqualTo(projectTesteeParam.getProjectId());
        projectVisitUserExampleCriteria.andOwnerOrgIdEqualTo(projectTesteeParam.getOwnerOrgId());
        projectVisitUserExampleCriteria.andTesteeCodeEqualTo(projectTesteeParam.getTesteeCode());
        List<ProjectVisitUser> projectVisitUsersList = projectVisitUserService.selectByExample(projectVisitUserExample);
        if(!projectVisitUsersList.isEmpty()){
            ProjectVisitUser projectVisitUser = projectVisitUsersList.get(0);
            if(!projectVisitUser.getTesteeId().equals(projectParticipantViewConfigParam.getId())){
                CustomResult customResult = new CustomResult();
                customResult.setMessage(projectParticipantViewConfigParam.getTesteeCode() + BusinessConfig.PROJECT_TESTEE_CODE_FOUND);
                return customResult;
            }
        }
        
        CustomResult customResult = projectTesteeInfoService.saveProjectTesteeBaseInfo(projectTesteeParam);
        if (ObjectUtil.notEqual(BusinessConfig.RETURN_MESSAGE_DEFAULT, customResult.getMessage())) {
            return customResult;
        }
        TemplateFormConfigVo templateTesteeFormBaseInfo = templateConfigService.getTemplateTesteeFormBaseInfo(projectParticipantViewConfigParam.getProjectId());
        if (templateTesteeFormBaseInfo == null) {
            return customResult;
        }
        String projectId = projectParticipantViewConfigParam.getProjectId();
        String formId = templateTesteeFormBaseInfo.getFormId();
        String testeeId = customResult.getData().toString();
        projectParticipantViewConfigParam.setResourceFormId(Long.parseLong(templateTesteeFormBaseInfo.getFormId()));
        List<TemplateFormDetailVo> templateFormDetailVoList = templateConfigService.getTemplateTesteeFormDetailBaseInfo(projectParticipantViewConfigParam.getProjectId(), templateTesteeFormBaseInfo.getFormId(), null);
        for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailVoList) {
            List<ProjectParticipantViewConfigParam.FormVariableConfigVo> variableConfigArrayList = projectParticipantViewConfigParam.getFormVariableConfigArrayList();
            for (ProjectParticipantViewConfigParam.FormVariableConfigVo formVariableConfigVo : variableConfigArrayList) {
                if(formVariableConfigVo.getId().equals(templateFormDetailVo.getId())){
                    //BeanUtils.copyProperties(templateFormDetailVo, formVariableConfigVo);
                    formVariableConfigVo.setFormId(Long.parseLong(templateTesteeFormBaseInfo.getFormId()));
                    ProjectTesteeResultWrapperVo projectTesteeResult = projectTesteeResultService.getTesteeFormBaseInfoByFormVariableId(projectParticipantViewConfigParam.getProjectId(), templateTesteeFormBaseInfo.getFormId(), templateFormDetailVo.getId().toString(), testeeId);
                    if(projectTesteeResult != null){
                        projectTesteeResult.setFieldValue(formVariableConfigVo.getValue());
                        projectTesteeResult.setFieldText(formVariableConfigVo.getTextValue());
                        projectTesteeResult.setUnitValue(formVariableConfigVo.getUnitValue());
                        projectTesteeResult.setUnitText(formVariableConfigVo.getUnitText());
                        projectTesteeResult.setSort(formVariableConfigVo.getSort());
                        projectTesteeResult.setUpdateTime(new Date());
                        projectTesteeResult.setUpdateUser(projectParticipantViewConfigParam.getCreateUserId());
                        projectTesteeResult.setDataFrom(BusinessConfig.TESTEE_INPUT_DATA_FROM_2);
                        projectTesteeResultService.updateByPrimaryKeySelective(projectTesteeResult);
                    }else{
                        projectTesteeResult = projectTesteeResultService.saveTesteeBaseFormVariableInfo(projectId, formId, formVariableConfigVo.getId().toString(), formVariableConfigVo.getLabel(), formVariableConfigVo.getFieldName(), formVariableConfigVo.getTextValue(), formVariableConfigVo.getValue(), testeeId, projectParticipantViewConfigParam.getCreateUserId(), BusinessConfig.TESTEE_INPUT_DATA_FROM_2);
                    }
                    projectTesteeResult.setType(templateFormDetailVo.getType());
                    // 载入缓存
                    redisTemplateService.set(RedisKeyContants.PROJECT_TESTEE_VARIABLE + testeeId.concat(DefineConstant.COLON_SIGN).concat(formVariableConfigVo.getId().toString()), JSON.toJSONString(projectTesteeResult));
                }
            }
        }
        // 异步更新参与者基本信息
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.updateProjectTesteeBaseInfoVariableInputValues(projectId, testeeId, SecurityUtils.getUserIdValue(), systemTenantId, systemPlatformId), 500);
        return customResult;
    }


    @Override
    public CommonResult getProjectTesteeCodeResult(String projectId, String projectOrgId, String testeeCode, String testeeId){
        ProjectVisitUserExample projectVisitUserExample = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria projectVisitUserExampleCriteria = projectVisitUserExample.createCriteria();
        projectVisitUserExampleCriteria.andProjectIdEqualTo(Long.parseLong(projectId));
        projectVisitUserExampleCriteria.andOwnerOrgIdEqualTo(projectOrgId);
        projectVisitUserExampleCriteria.andTesteeCodeEqualTo(testeeCode);
        List<ProjectVisitUser> projectVisitUsersList = projectVisitUserService.selectByExample(projectVisitUserExample);
        if(CollectionUtil.isNotEmpty(projectVisitUsersList)){
            ProjectVisitUser projectVisitUser = projectVisitUsersList.get(0);
            if(StringUtils.isBlank(testeeId) || !testeeId.equals(projectVisitUser.getTesteeId().toString())){
                return CommonResult.failed(testeeCode + BusinessConfig.PROJECT_TESTEE_CODE_FOUND);
            }
        }
        return CommonResult.success("");
    }


}
