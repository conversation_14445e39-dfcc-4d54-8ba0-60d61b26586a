package com.haoys.user.mapper;

import com.haoys.user.model.ProjectOrgMenu;
import com.haoys.user.model.ProjectOrgMenuExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectOrgMenuMapper {
    long countByExample(ProjectOrgMenuExample example);

    int deleteByExample(ProjectOrgMenuExample example);

    int insert(ProjectOrgMenu record);

    int insertSelective(ProjectOrgMenu record);

    List<ProjectOrgMenu> selectByExample(ProjectOrgMenuExample example);

    int updateByExampleSelective(@Param("record") ProjectOrgMenu record, @Param("example") ProjectOrgMenuExample example);

    int updateByExample(@Param("record") ProjectOrgMenu record, @Param("example") ProjectOrgMenuExample example);
}