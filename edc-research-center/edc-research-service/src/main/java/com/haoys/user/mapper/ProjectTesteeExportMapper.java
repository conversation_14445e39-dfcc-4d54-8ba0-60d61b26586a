package com.haoys.user.mapper;

import com.haoys.user.domain.param.TesteeExportHistoryParam;
import com.haoys.user.domain.param.testee.ProjectTesteeExportSelectParam;
import com.haoys.user.model.ProjectTesteeExport;
import com.haoys.user.model.ProjectTesteeExportExample;
import java.util.List;

import com.haoys.user.model.ProjectTesteeExportManage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectTesteeExportMapper {
    long countByExample(ProjectTesteeExportExample example);

    int deleteByExample(ProjectTesteeExportExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeExport record);

    int insertSelective(ProjectTesteeExport record);

    List<ProjectTesteeExport> selectByExample(ProjectTesteeExportExample example);

    ProjectTesteeExport selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeExport record, @Param("example") ProjectTesteeExportExample example);

    int updateByExample(@Param("record") ProjectTesteeExport record, @Param("example") ProjectTesteeExportExample example);

    int updateByPrimaryKeySelective(ProjectTesteeExport record);

    int updateByPrimaryKey(ProjectTesteeExport record);

    Long getPorjectTesteeCount(String projectId, String orgId);

    List<ProjectTesteeExport> selectList(TesteeExportHistoryParam param);

    int rmById(String id);
    int deleteById(String id);

    /**
     * 获取参与者导出数据
     * @param selectParam 导出参数
     * @return 导出列表
     */
    List<ProjectTesteeExportManage> selectList2(ProjectTesteeExportSelectParam selectParam);


    /**
     * 获取当前用户下载的文件
     * @param list
     * @return
     */
    List<ProjectTesteeExportManage> getDownList(List<Object> list);
}
