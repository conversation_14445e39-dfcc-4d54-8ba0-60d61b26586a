package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.PrescriptionAnalysisParam;
import com.haoys.user.domain.vo.PrescriptionAnalysisVo;
import com.haoys.user.domain.vo.ProjectAnalysisRecordVo;
import com.haoys.user.model.ProjectPharmacopeia;
import com.haoys.user.model.ProjectPrescriptionMix;
import com.haoys.user.model.ProjectPropertyTaste;
import com.haoys.user.model.ProjectRefinement;
import com.haoys.user.model.ProjectTasteStat;
import com.haoys.user.participle.BoxPlot;

import java.util.List;
import java.util.Map;

public interface PrescriptionAnalysisService {

    CommonPage<PrescriptionAnalysisVo> getPrescriptionAnalysisForPage(String projectId, String name, String userId, Integer pageNum, Integer pageSize);

    CommonPage<ProjectPharmacopeia>  getProjectPharmacopeiaForPage(String name, Integer pageNum, Integer pageSize);

    CommonPage<ProjectAnalysisRecordVo> getProjectAnalysisRecordForPage(String batchCode, Integer pageNum, Integer pageSize);

    CustomResult savePrescriptionAnalysis(PrescriptionAnalysisParam prescriptionAnalysisParam);

    List<Map<Integer, Object>> queryAgeLineAnalysis(String batchCode);

    BoxPlot queryAgeBoxPlotAnalysis(String batchCode);

    List<Map.Entry<String, Integer>> querySymptomCount(String batchCode);

    List<Map.Entry<String, Integer>> queryMedicineCount(String batchCode);

    CommonPage<ProjectPrescriptionMix> queryPrescriptionCombinationForPage(String batchCode, Integer pageNum, Integer pageSize);

    void getPrescriptionCombinationRelation(String batchCode);

    ProjectTasteStat getPrescriptionTasteStat(String batchCode);

    ProjectPropertyTaste getPrescriptionPropertyTaste(String batchCode);

    ProjectRefinement getPrescriptionRefinement(String batchCode);

    String updatePythonTask(String requestUrl, String batchCode);
}
