package com.haoys.user.service.impl;

import com.haoys.user.mapper.SystemPointLogMapper;
import com.haoys.user.model.SystemPointLog;
import com.haoys.user.service.SystemPointLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SystemPointLogServiceImpl implements SystemPointLogService {

    @Autowired
    private SystemPointLogMapper systemPointLogMapper;

    @Override
    public void insertSystemPointlog(SystemPointLog systemPointLog) {
        systemPointLogMapper.insert(systemPointLog);
    }
}
