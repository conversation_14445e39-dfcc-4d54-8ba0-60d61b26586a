package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVariableSyncVo;
import com.haoys.user.mapper.FlowFormSetMapper;
import com.haoys.user.mapper.ProjectTesteeVariableSyncMapper;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.ProjectTesteeVariableSync;
import com.haoys.user.model.ProjectTesteeVariableSyncExample;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.service.ProjectTesteeVariableSyncService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ProjectTesteeVariableSyncServiceImpl implements ProjectTesteeVariableSyncService {

    @Autowired
    private ProjectTesteeVariableSyncMapper projectTesteeVariableSyncMapper;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private FlowFormSetMapper flowFormSetMapper;
    @Autowired
    private TemplateConfigService templateConfigService;


    @Override
    public CommonResult<Object> create(ProjectTesteeVariableSync projectTesteeVariableSync) {
        // 先删除
        ProjectTesteeVariableSyncExample example = new ProjectTesteeVariableSyncExample();
        ProjectTesteeVariableSyncExample.Criteria criteria = example.createCriteria();
        criteria.andBaseVariableIdEqualTo(projectTesteeVariableSync.getBaseVariableId());
        projectTesteeVariableSyncMapper.deleteByExample(example);
        // 新增
        String userId = SecurityUtils.getUserId().toString();
        if (projectTesteeVariableSync.getSourceFormId()!=null && projectTesteeVariableSync.getSourceVisitId()!=null&& projectTesteeVariableSync.getSourceVisitId()!=null){
            projectTesteeVariableSync.setId(SnowflakeIdWorker.getUuid());
            projectTesteeVariableSync.setTenantId(SecurityUtils.getSystemTenantId());
            projectTesteeVariableSync.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectTesteeVariableSync.setCreateUserId(userId);
            projectTesteeVariableSync.setCreateTime(new Date());
            projectTesteeVariableSync.setStatus(BusinessConfig.ENABLED_STATUS.toString());
            projectTesteeVariableSyncMapper.insert(projectTesteeVariableSync);
            // 设置baseVariable
            TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(projectTesteeVariableSync.getBaseVariableId().toString());
            if (templateFormDetailConfig!=null){
                templateFormDetailConfig.setBaseVariableId(projectTesteeVariableSync.getBaseVariableId());
                templateConfigService.updateTemplateFormDetailConfigById(templateFormDetailConfig);
            }


        }
        return CommonResult.success(null);
    }

    @Override
    public CommonResult<ProjectTesteeVariableSyncVo> getByBaseVariableId(Long baseVariableId) {
        ProjectTesteeVariableSyncVo vo = new ProjectTesteeVariableSyncVo();
        ProjectTesteeVariableSyncExample example = new ProjectTesteeVariableSyncExample();
        ProjectTesteeVariableSyncExample.Criteria criteria = example.createCriteria();
        criteria.andBaseVariableIdEqualTo(baseVariableId);
        List<ProjectTesteeVariableSync> list = projectTesteeVariableSyncMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(list)){
            ProjectTesteeVariableSync sync = list.get(0);
            BeanUtils.copyProperties(sync,vo);
            // 获取流程信息
            List<ProjectVisitConfig> flowList = projectVisitConfigService.listByProjectId(sync.getProjectId());
            vo.setFlowList(flowList);
            // 获取表单信息
            List<FlowFormSet> formList = flowFormSetMapper.listByFlowId(vo.getSourceVisitId());
            vo.setFormList(formList);
            // 获取字段信息
            List<TemplateFormDetailVo> filedList = templateConfigService.getTemplateFormDetailConfigListByFormId(null, vo.getSourceFormId().toString(), "", "1", "1", "");
            vo.setFiledList(filedList);
            List<TemplateFormDetailVo> collect = filedList.stream().filter(y -> y.getId().equals(sync.getSourceVariableId())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)){
                vo.setFiledType(collect.get(0).getType());
            }

        }
        return CommonResult.success(vo);
    }

    @Override
    public ProjectTesteeVariableSyncVo getVariableSyncByBaseVariableId(Long baseVariableId) {
        CommonResult<ProjectTesteeVariableSyncVo> commonResult = getByBaseVariableId(baseVariableId);
        return commonResult.getData();
    }
}
