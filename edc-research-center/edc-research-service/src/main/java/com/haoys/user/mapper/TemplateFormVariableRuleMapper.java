package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.model.TemplateFormVariableRuleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormVariableRuleMapper {
    long countByExample(TemplateFormVariableRuleExample example);

    int deleteByExample(TemplateFormVariableRuleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormVariableRule record);

    int insertSelective(TemplateFormVariableRule record);

    List<TemplateFormVariableRule> selectByExampleWithBLOBs(TemplateFormVariableRuleExample example);

    List<TemplateFormVariableRule> selectByExample(TemplateFormVariableRuleExample example);

    TemplateFormVariableRule selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormVariableRule record, @Param("example") TemplateFormVariableRuleExample example);

    int updateByExampleWithBLOBs(@Param("record") TemplateFormVariableRule record, @Param("example") TemplateFormVariableRuleExample example);

    int updateByExample(@Param("record") TemplateFormVariableRule record, @Param("example") TemplateFormVariableRuleExample example);

    int updateByPrimaryKeySelective(TemplateFormVariableRule record);

    int updateByPrimaryKeyWithBLOBs(TemplateFormVariableRule record);

    int updateByPrimaryKey(TemplateFormVariableRule record);
}