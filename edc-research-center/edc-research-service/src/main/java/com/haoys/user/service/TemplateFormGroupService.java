package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.crf.TemplateFormGroupParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupVariableVo;
import com.haoys.user.model.TemplateFormGroupTable;
import com.haoys.user.model.TemplateFormGroupVariable;

import java.util.List;

public interface TemplateFormGroupService {

    /**
     * 参与者新增字段组
     * @param groupParam
     * @param userId
     * @return
     */
    CommonResult<Object> saveTesteeFormGroupDetail(TemplateFormGroupParam groupParam, String userId);

    /**
     * 根据组id删除组数据
     * @param groupId
     * @param userId 用户id
     * @return
     */
    CommonResult<Object> deleteProjectTesteeGroupInfoByGroupId(String groupId,String userId);

    /**
     * 查询参与者字段组记录
     * @param projectId         项目id
     * @param planId
     * @param visitId           访视id
     * @param formId            表单id
     * @param resourceVariableId 字段组变量id
     * @param resourceGroupId    分组id
     * @param testeeGroupId      参与者字段组id
     * @param testeeId          参与者id
     * @return
     */
    TemplateFormGroupVariable getTemplateGroupInfoByBaseVariableId(String projectId, String planId, String visitId, String formId, String resourceVariableId, String resourceGroupId, String testeeGroupId, String testeeId);

    /**
     * 查询当前表单字段分组集合
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param variableGroupTableId
     * @param variableTableId
     * @param testeeId
     * @return
     */
    List<TemplateFormGroupVariableVo> getProjectTesteeFormGroupListByFormId(String projectId, String planId, String visitId, String formId, String variableGroupTableId, String variableTableId, String testeeId);

    /**
     * 查询字段组表格header数据
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param groupId
     * @param baseGroupId
     * @param testeeId
     * @return
     */
    List<TemplateFormGroupTable> getTemplateGroupTableRowHeader(String projectId, String planId, String visitId, String formId, String formDetailId, String groupId, String baseGroupId, String testeeId);


    /**
     * 参与者端初始化字段组组
     * @return
     */
    CommonResult<Object> saveTesteeBaseFormVariableGroup(String projectId, String visitId, String formId, String testeeId, String userId);

    /**
     * 批量新增参与者字段组
     *
     * @param projectId
     * @param groupParamList
     * @param userId
     * @return
     */
    List<String> saveBatchTesteeFormGroupDetail(String projectId, List<TemplateFormGroupParam> groupParamList, String userId);
}
