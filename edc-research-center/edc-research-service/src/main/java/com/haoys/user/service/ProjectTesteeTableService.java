package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormTableConfigVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableRowDataVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableCountVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo;
import com.haoys.user.domain.vo.testee.TesteeChallengeVo;
import com.haoys.user.model.ProjectTesteeTable;
import com.haoys.user.model.ProjectTesteeTableExample;

import java.util.List;

public interface ProjectTesteeTableService {


    /**
     * 编辑保存表格行记录详情
     * @param projectTesteeTable
     * @return
     */
    int saveProjectTesteeTableRowRecord(ProjectTesteeTable projectTesteeTable);


    /**
     * 批量新增表格记录
     * @param tableRowList
     */
    int saveBatchProjectTesteeTableRowRecord(List<ProjectTesteeTable> tableRowList);


    /**
     * 编辑表格行记录详情
     * @param projectTesteeTable
     * @return
     */
    void updateProjectTesteeTableRowRecord(ProjectTesteeTable projectTesteeTable);

    /**
     * 根据编号删除行记录
     * @param rowNumber
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeGroupId
     * @param testeeId
     * @return
     */
    CustomResult deleteTesteeTableRowRecord(String rowNumber, String projectId, String planId, String visitId, String formId, String testeeGroupId, String testeeId);

    /**
     * 根据表格id删除整个表格记录
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeGroupId
     * @param testeeId
     * @return
     */
    CustomResult deleteTesteeTableRecord(String projectId, String visitId, String formId, String formDetailId, String testeeGroupId, String testeeId);



    /**
     * 通过表格行记录查询详情
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeId
     * @param queryMethod
     * @param rowNumber
     * @param openOCR
     * @param medicalType
     * @param testeeGroupId
     * @return
     */
    List<ProjectTesteeTableVo> getProjectTesteeTableRowRecord(String projectId, String planId, String visitId, String formId, String testeeId, String queryMethod, String rowNumber, String openOCR, String medicalType, String testeeGroupId);
    
    
    List<ProjectTesteeTableVo> getProjectTesteeTableRowRecordForRowNumber(String projectId, String planId, String visitId, String formId, String testeeId, String rowNumber, String testeeGroupId);
    
    
    /**
     * 查询指定表格列数据
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param formDetailId
     * @param formTableId
     * @param testeeGroupId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeTableVo> getProjectTesteeTableColumnRecordList(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String formTableId, String testeeGroupId, String testeeId);

    /**
     * 通过formDetailId查询整个表格记录
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @param testeeGroupId
     * @param tableSort
     * @return
     */
    List<ProjectTesteeFormDetailTableVo> getProjectTesteeFormDetailTableRow(String projectId, String visitId, String formId, String formDetailId, String testeeId, String testeeGroupId, String tableSort);


    /**
     * 查询参与者访视表格分页数据
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeTableWrapperVo> getProjectTesteeTableListForPage(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId, Long rowNumber);


    /**
     * 查询参与者访视字段组表格分页数据
     * @param projectId
     * @param visitId
     * @param formId
     * @param variableId
     * @param testeeId
     * @param rowNumber
     * @return
     */
    List<ProjectTesteeTableWrapperVo> getProjectTesteeGroupTableListForPage(String projectId, String visitId, String formId, String variableId, String testeeId, Long rowNumber);


    /**
     * 查询表格行总数
     *
     * @param projectId      项目id
     * @param planId
     * @param visitId        访视id
     * @param formId         表单id
     * @param formExpandId
     * @param formDetailId   表格id
     * @param testeeId       参与者id
     * @param queryFormGroup
     * @param taskDate       任务日期
     * @param tableSort
     * @param groupId
     * @return
     */
    List<Long> getProjectTesteeTableRowNumber(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId, String queryFormGroup, String taskDate, String tableSort, String groupId);

    /**
     * 查询动态表单和表格配置
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeGroupId
     * @param testeeId
     * @return
     */
    ProjectTesteeFormTableConfigVo getProjectTesteeFormTableConfig(String projectId, String planId, String visitId, String formId, String formDetailId, String testeeGroupId, String testeeId);

    /**
     * 查询指定字段列数据记录
     * @param projectId
     * @param tableId
     * @return
     */
    List<ProjectTesteeTableVo> getProjectTesteeTableRowFieldRecord(String projectId, String tableId);

    /**
     * 查询动态表格head配置记录
     * @param formId
     * @param formDetailId
     * @param queryIgnoreStatus
     * @return
     */
    List<TemplateTableVo> getProjectTesteeTableRowHead(String formId, String formDetailId, Boolean queryIgnoreStatus);

    /**
     * 查询表格行记录质疑状态
     * @param projectId     项目id
     * @param visitId       访视id
     * @param formId        表单项id
     * @param tableRowNo    表格行编号
     * @param testeeId      参与者id
     * @return
     */
    TesteeChallengeVo getProjectTableRowChallengeStatus(String projectId, String visitId, String formId, String tableRowNo, String testeeId);

    /**
     * 查询表格详情tableResultId质疑状态
     * @param projectId
     * @param visitId
     * @param formId
     * @param tableResultId
     * @param testeeId
     * @return
     */
    TesteeChallengeVo getProjectTableResultIdChallengeStatus(String projectId, String visitId, String formId, String tableResultId, String testeeId);

    /**
     * 按属性条件查询参与者表单表格记录
     * @param exampleTable
     * @return
     */
    List<ProjectTesteeTable> selectByExample(ProjectTesteeTableExample exampleTable);

    /**
     * 根据formTableResultId查询表格信息
     * @param formTableResultId
     * @return
     */
    ProjectTesteeTable getProjectTesteeTableRowRecordByTableId(Long formTableResultId);

    /**
     * 查询参与者录入的表格记录
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeTableRowDataVo> getProjectTesteeTableRowDataList(String projectId, String planId, String visitId, String formId, String formDetailId, String testeeId);


    /**
     * 查询参与者访视表格提交数据
     *
     * @param projectId
     * @param projectOrgId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param formTableId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeTableResultExportVo> getProjectTesteeTableRecordsWithVariableIds(String projectId, String projectOrgId, String visitId, String formId, String formDetailId, String formTableId, String testeeId);


    /**
     * 查询参与者访视表单和表格提交数据
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param formTableId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeFormAndTableResultExportVo> getProjectTesteeFormAndTableResultByVariableIds(String projectId, String visitId, String formId, String formDetailId, String formTableId, String testeeId, String orgId);

    /**
     * 查询参与者当前访视下所有表格汇总
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeFormAndTableCountVo> getProjectTesteeFormAndTableCountByVariableIds(String projectId, String visitId, String formId, String formDetailId, String testeeId, String orgId);


    /**
     * 获取题组的数据。
     *
     * @param projectId
     * @param visitId
     */
    List<ProjectTesteeTableResultExportVo> getProjectTesteeGroupVariableAndTable(String projectId, String projectOrgId, String visitId, String testeeId, String fromId);

    /**
     * 获取表单字段组中某个表格列的数据
     * @param projectId
     * @param visitId
     * @param fromId
     * @param formTableId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeTableResultExportVo> getProjectTesteeGroupTableResult(String projectId, String visitId, String fromId, String formTableId, String testeeId, String orgId);
}
