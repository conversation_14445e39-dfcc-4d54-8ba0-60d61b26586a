package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.domain.entity.BaseSystemUser;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.AESUtil256;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.MessageUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.BdpUserInfoConfig;
import com.haoys.user.domain.enums.SystemUserReturnEnums;
import com.haoys.user.domain.param.system.ActiveUserParam;
import com.haoys.user.domain.param.system.ExternalRegisterUserParam;
import com.haoys.user.domain.param.system.PatientUserParam;
import com.haoys.user.domain.param.system.RegisterUserParam;
import com.haoys.user.domain.param.system.SystemUserExcelParam;
import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.param.system.UpdateSystemUserPasswordParam;
import com.haoys.user.domain.vo.project.SysDepartmentVo;
import com.haoys.user.domain.vo.project.SystemUserExtendVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.mapper.SystemUserInfoBackMapper;
import com.haoys.user.mapper.SystemUserInfoMapper;
import com.haoys.user.mapper.SystemUserRoleMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.Organization;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.model.SystemRole;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemTenantUserExample;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.SystemUserInfoBack;
import com.haoys.user.model.SystemUserInfoExample;
import com.haoys.user.model.SystemUserPwd;
import com.haoys.user.model.SystemUserRole;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.PasswordCheckService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectResearchersInfoService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemDepartmentService;
import com.haoys.user.service.SystemRoleService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.SystemUserPwdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 后台用户管理Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class SystemUserInfoServiceImpl extends BaseService implements SystemUserInfoService {

    private final SystemUserInfoMapper systemUserInfoMapper;
    private final BdpUserInfoConfig bdpUserInfoConfig;
    private final PasswordEncoder passwordEncoder;
    private final SystemRoleService systemRoleService;
    private final RedisTemplateService redisTemplateService;
    
    private final OrganizationService organizationService;
    private final DictionaryService dictionaryService;
    private final SystemDepartmentService systemDepartmentService;
    private final SystemUserInfoBackMapper systemUserInfoBackMapper;

    private final PasswordCheckService passwordCheckService;
    private final SystemTenantUserService systemTenantUserService;
    private final SystemUserRoleMapper systemUserRoleMapper;
    private final SystemUserPwdService systemUserPwdService;
    
    private final ProjectUserService projectUserService;
    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final ProjectResearchersInfoService projectResearchersInfoService;
    private final ProjectBaseManageService projectBaseManageService;


    @Override
    public SystemUserInfoVo getSystemUserInfoByAccountName(String username) {
        SystemUserInfoVo systemUserInfoVo = new SystemUserInfoVo();

        // 手机号是加密的，所以需要把username 加密查是否有该手机号
        String aseMobile =DesensitizeUtil.aesEncrypt(username);

        SystemUserInfo systemUserInfo = systemUserInfoMapper.getSystemUserInfoByAccountName(username,aseMobile);
        if(systemUserInfo != null){
            boolean activeStatus = systemUserInfo.getActiveStatus() != null && systemUserInfo.getActiveStatus();
            systemUserInfo.setActiveStatus(activeStatus);
            BeanUtils.copyProperties(systemUserInfo, systemUserInfoVo);
            if(StringUtils.isNotEmpty(systemUserInfo.getDepartment())) {
                Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(systemUserInfo.getDepartment());
                if(systemOrganizationInfo != null){
                    systemUserInfoVo.setDepartmentName(systemOrganizationInfo.getName());
                }
            }
            boolean defaultAdmin = systemUserInfo.getDefaultAdmin() != null && systemUserInfo.getDefaultAdmin();
            systemUserInfoVo.setDefaultAdmin(defaultAdmin);
        }
        return systemUserInfo == null ? null : systemUserInfoVo;
    }

    @Override
    public SystemUserInfo getUserBaseInfoByUserName(String userName) {
        return systemUserInfoMapper.getUserInfoByUserName(userName);
    }

    @Override
    public CustomResult saveSystemUserInfo(SystemUserInfoParam systemUserInfoParam) {
        CustomResult customResult = new CustomResult();
        SystemUserInfo systemUserInfo = new SystemUserInfo();
        BeanUtils.copyProperties(systemUserInfoParam, systemUserInfo);
        systemUserInfo.setId(SnowflakeIdWorker.getUuid());
        systemUserInfo.setCreateUser(systemUserInfoParam.getCreateUserId());
        if (StringUtils.isNotEmpty(systemUserInfoParam.getDefaultPwd())){
            systemUserInfo.setStatus(BusinessConfig.TEMP_STATUS);
            systemUserInfo.setActiveStatus(true);
            systemUserInfo.setPassword(systemUserInfoParam.getDefaultPwd());
        }else{
            systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
            systemUserInfo.setActiveStatus(false);
        }
        systemUserInfo.setSealFlag(false);
        systemUserInfo.setCreateTime(new Date());

        if(StringUtils.isNotEmpty(systemUserInfoParam.getUsername())){
            SystemUserInfoExample example = new SystemUserInfoExample();
            SystemUserInfoExample.Criteria criteria = example.createCriteria();
            criteria.andUsernameEqualTo(systemUserInfo.getUsername());
            List<SystemUserInfo> systemUserInfoList = systemUserInfoMapper.selectByExample(example);
            if (!systemUserInfoList.isEmpty()) {
                throw new ServiceException(ResultCode.SYSTEM_USER_NAME_EXIST.getCode()+"",ResultCode.SYSTEM_USER_NAME_EXIST.getMessage());
            }
        }

        if(StringUtils.isNotEmpty(systemUserInfoParam.getMobile())){
            // 手机号是加密的，所以需要把username 加密查是否有该手机号
            String aseMobile = DesensitizeUtil.aesEncrypt(systemUserInfoParam.getMobile());
            //查询手机号用户是否存在
            SystemUserInfo userInfo = systemUserInfoMapper.checkMobileExists(aseMobile);
            if(userInfo != null){
                throw new ServiceException(ResultCode.SYSTEM_USER_MOBILE_EXIST.getCode()+"",ResultCode.SYSTEM_USER_MOBILE_EXIST.getMessage());
            }
            systemUserInfo.setMobile(aseMobile);
        }

        if(StringUtils.isNotEmpty(systemUserInfoParam.getEmail())){
            //查询邮箱用户是否存在
            SystemUserInfo userInfo = systemUserInfoMapper.checkEmailExists(systemUserInfoParam.getEmail());
            if(userInfo != null){
                throw new ServiceException(ResultCode.SYSTEM_USER_EMAIL_EXIST.getCode()+"",ResultCode.SYSTEM_USER_EMAIL_EXIST.getMessage());
            }
        }
        // 校验是否符合密码策略中的设置
        passwordCheckService.checkPwd(systemUserInfo.getPassword());

        //将密码进行加密操作
        //String encodePassword = passwordEncoder.encode(Constants.USER_DEFAULT_LOGIN_PASSWORD);
        //systemUserInfo.setLoginCode(Constants.USER_DEFAULT_LOGIN_PASSWORD);
        if(StringUtils.isNotBlank(systemUserInfo.getPassword())){
            String encodePassword = passwordEncoder.encode(systemUserInfo.getPassword());
            systemUserInfo.setPassword(encodePassword);
            systemUserInfo.setLoginCode(systemUserInfo.getPassword());
            systemUserInfo.setLastModifyPwdTime(new Date());
        }

        if(StringUtils.isEmpty(systemUserInfoParam.getMobile())){
            systemUserInfo.setMobile(null);
        }
        if(StringUtils.isEmpty(systemUserInfoParam.getEmail())){
            systemUserInfo.setEmail(null);
        }
        systemUserInfoMapper.insertSelective(systemUserInfo);
        customResult.setData(systemUserInfo.getId().toString());
        return customResult;
    }

    @Override
    public CustomResult savePatientUser(PatientUserParam patientUserParam) {
        checkVerificationCode(patientUserParam.getMobile(), patientUserParam.getCode());
        SystemUserInfoParam systemUserInfoParam = new SystemUserInfoParam();
        BeanUtils.copyProperties(patientUserParam, systemUserInfoParam);
        systemUserInfoParam.setUsername("Testee_" + RandomStringUtils.randomNumeric(10).toLowerCase());
        return saveSystemUserInfo(systemUserInfoParam);
    }
    /**
     * 邀请用户-激活用户
     * @param systemUserInfo 用户信息
     * @return 激活结果
     */
    @Override
    public CommonResult updateActiveSystemUserInfo(ActiveUserParam systemUserInfo) {
        log.info("request params:{}", JSON.toJSON(systemUserInfo));
        SystemUserInfo user = systemUserInfoMapper.selectByPrimaryKey(systemUserInfo.getId());
        if(user == null){
            return CommonResult.failed(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
        }
        // 校验登录名是否重复
        SystemUserInfoVo user0 = this.getSystemUserInfoByAccountName(systemUserInfo.getUsername());
        if (user0!=null && !Objects.equals(user0.getId(), systemUserInfo.getId())){
            return CommonResult.failed(SystemUserReturnEnums.E20017);
        }
        // userType":"100001"
        if (systemUserInfo.getUserType().equals(Constants.USER_TYPE_VALUE_01)){
            // 手机号来源
            SystemUserInfoVo user1 = this.getSystemUserInfoByAccountName(systemUserInfo.getMobile());
            if (user1!=null && !Objects.equals(user1.getId(), systemUserInfo.getId())){
                return CommonResult.failed(SystemUserReturnEnums.E20015);
            }
            String aseMobile =DesensitizeUtil.aesEncrypt(systemUserInfo.getMobile());
            systemUserInfo.setMobile(aseMobile);
            log.info("updateActiveSystemUserInfo mobile: {} aesMobile: {}", aseMobile, aseMobile);
        }
        if (systemUserInfo.getUserType().equals(Constants.USER_TYPE_VALUE_02)){
            // 邮箱来源
            SystemUserInfoVo user2 = this.getSystemUserInfoByAccountName(systemUserInfo.getEmail());
            if (user2!=null && !Objects.equals(user2.getId(), systemUserInfo.getId())){
                return CommonResult.failed(SystemUserReturnEnums.E20014);
            }
        }
        // 校验密码的规则
        if(StringUtils.isBlank(systemUserInfo.getPassword())){
            return CommonResult.failed(SystemUserReturnEnums.E20010);
        }else {
            // 校验是否符合密码策略中的设置
            passwordCheckService.checkPwd(systemUserInfo.getPassword());

            user.setPassword(new BCryptPasswordEncoder().encode(systemUserInfo.getPassword()));
        }
        if(StringUtils.isNotEmpty(systemUserInfo.getUsername())){
            user.setUsername(systemUserInfo.getUsername());
        }
        if(StringUtils.isNotEmpty(systemUserInfo.getRealName())){
            user.setRealName(systemUserInfo.getRealName());
        }
        if(StringUtils.isNotEmpty(systemUserInfo.getDepartment())){
            user.setDepartment(systemUserInfo.getDepartment());
        }
        user.setEmail(StringUtils.isNotEmpty(systemUserInfo.getEmail())?systemUserInfo.getEmail():null);
        user.setMobile(StringUtils.isNotEmpty(systemUserInfo.getMobile())?systemUserInfo.getMobile():null);
        user.setUpdateTime(new Date());
        user.setActiveStatus(true);
        // 改成激活时间
        //user.setUpdateUser(systemUserInfo.getUpdateUser());
        user.setLastModifyPwdTime(new Date());
        int re = systemUserInfoMapper.updateByPrimaryKeySelective(user);
        
        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(user.getId().toString(), systemUserInfo.getTenantId(), systemUserInfo.getPlatformId());
        log.error("systemTenantUser:{}", JSON.toJSONString(systemUserInfo));
        if(systemTenantUser == null){
            throw new ServiceException(ResultCode.SYSTEM_USER_ACTIVE_ERROR.getCode()+"",ResultCode.SYSTEM_USER_ACTIVE_ERROR.getMessage());
        }
        systemTenantUser.setActiveStatus(true);
        systemTenantUser.setDepartment(systemUserInfo.getDepartment());
        systemTenantUserService.updateSystemTenantUser(systemTenantUser);

        // 确认用户角色是否同步成功
        List<SystemRole> systemRoleList = systemRoleService.selectRoleByUserId(systemUserInfo.getId());
        if(CollectionUtil.isEmpty(systemRoleList)){
            List<SystemUserRole> userRoleList = new ArrayList<>();
            SystemUserRole systemUserRole = new SystemUserRole();
            systemUserRole.setUserId(systemUserInfo.getId());
            systemUserRole.setRoleId(Constants.SYSTEM_USER_DEFAULT_ROLE_ID);
            systemUserRole.setTenantId(SecurityUtils.getSystemTenantId());
            systemUserRole.setPlatformId(SecurityUtils.getSystemPlatformId());
            userRoleList.add(systemUserRole);
            systemRoleService.batchSaveUserRole(userRoleList);
        }

        if(StringUtils.isNotBlank(systemUserInfo.getProjectId())){
            projectUserService.updateStatus(systemUserInfo.getProjectId(),user.getId().toString());
        }
        
        
        if("bori_h5".equals(systemUserInfo.getLoginSource())){
            List<ProjectUserInfo> projectUserInfoList = projectUserService.getProjectUserListByUserId(systemUserInfo.getId().toString());
            for (ProjectUserInfo projectUserInfo : projectUserInfoList) {
                if(projectUserInfo != null && !projectUserInfo.getActiveStatus()){
                    projectUserService.updateStatus(projectUserInfo.getProjectId().toString(),user.getId().toString());
                }
            }
        }
        
        // 密码历史表中添加数据
        SystemUserPwd record = new SystemUserPwd();
        record.setPassword(DesensitizeUtil.aesEncrypt(systemUserInfo.getPassword()));
        record.setId(SnowflakeIdWorker.getUuid());
        record.setUserId(systemUserInfo.getId());
        record.setStatus(BusinessConfig.VALID_STATUS);
        record.setCreateTime(new Date());
        record.setTenantId(SecurityUtils.getSystemTenantId());
        record.setPlatformId(SecurityUtils.getSystemPlatformId());
        systemUserPwdService.insert(record);

        return re>0?CommonResult.success(SystemUserReturnEnums.S200):CommonResult.failed(SystemUserReturnEnums.E500);
    }

    @Override
    public CommonResult registerSystemUserInfo(RegisterUserParam registerUserParam) {
        SystemUserInfoVo systemUserInfoVo;
        if(registerUserParam.getRegisterType().equals(Constants.USER_TYPE_VALUE_01)){
            if (StringUtils.isBlank(registerUserParam.getMobile())){
                return CommonResult.failed(SystemUserReturnEnums.E20004);
            }
            // 校验手机号是否已经使用,并且已经激活
            systemUserInfoVo = this.getSystemUserInfoByAccountName(registerUserParam.getMobile());
            if (systemUserInfoVo != null && systemUserInfoVo.getActiveStatus()){
                String loginPlatformId = getPlatformIdFromLoginSource(registerUserParam.getLoginSource());
                SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfoVo.getId().toString(), registerUserParam.getTenantId(), loginPlatformId);
                if(systemTenantUser != null){
                    return CommonResult.failed(SystemUserReturnEnums.E20015);
                }
            }
            String aseMobile =DesensitizeUtil.aesEncrypt(registerUserParam.getMobile());
            registerUserParam.setMobile(aseMobile);
        }
        if(registerUserParam.getRegisterType().equals(Constants.USER_TYPE_VALUE_02)) {
            if (StringUtils.isBlank(registerUserParam.getEmail())){
                return CommonResult.failed(SystemUserReturnEnums.E20013);
            }
            // 校验邮箱是否已经使用,并且已经激活
            systemUserInfoVo = this.getSystemUserInfoByAccountName(registerUserParam.getEmail());
            if (systemUserInfoVo != null && systemUserInfoVo.getActiveStatus()){
                // 判断平台用户是否存在
                String loginPLatformId = getPlatformIdFromLoginSource(registerUserParam.getLoginSource());
                SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfoVo.getId().toString(), registerUserParam.getTenantId(), loginPLatformId);
                if(systemTenantUser != null){
                    return CommonResult.failed(SystemUserReturnEnums.E20014);
                }
            }
        }
        // 校验登录名是否已经使用
        systemUserInfoVo = this.getSystemUserInfoByAccountName(registerUserParam.getUsername());
        if (systemUserInfoVo != null && systemUserInfoVo.getActiveStatus()){
            String loginPLatformId = getPlatformIdFromLoginSource(registerUserParam.getLoginSource());
            SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfoVo.getId().toString(), registerUserParam.getTenantId(), loginPLatformId);
            if(systemTenantUser != null){
                return CommonResult.failed(SystemUserReturnEnums.E20017);
            }
        }
        // 校验是否符合密码策略中的设置
        passwordCheckService.checkPwd(registerUserParam.getPassword());

        boolean isUpdate = false;
        if (systemUserInfoVo==null){
            systemUserInfoVo = new SystemUserInfoVo();
            Long uuid = SnowflakeIdWorker.getUuid();
            systemUserInfoVo.setId(uuid);
            systemUserInfoVo.setCreateUser(uuid.toString());
        }else {
            isUpdate=true;
        }
        systemUserInfoVo.setPassword(new BCryptPasswordEncoder().encode(registerUserParam.getPassword()));
        systemUserInfoVo.setEmail(StringUtils.isNotEmpty(registerUserParam.getEmail())?registerUserParam.getEmail():null);
        systemUserInfoVo.setMobile(StringUtils.isNotEmpty(registerUserParam.getMobile())?registerUserParam.getMobile():null);
        systemUserInfoVo.setUserType(registerUserParam.getRegisterType());
        systemUserInfoVo.setRegisterFrom(Constants.USER_TYPE_VALUE_03);
        if(Constants.USER_LOGIN_RESOURCE_4.equals(registerUserParam.getLoginSource())){
            systemUserInfoVo.setRegisterFrom(Constants.USER_TYPE_VALUE_04);
        }
        if(Constants.USER_LOGIN_RESOURCE_5.equals(registerUserParam.getLoginSource())){
            systemUserInfoVo.setRegisterFrom(Constants.USER_TYPE_VALUE_05);
        }
        systemUserInfoVo.setUsername(registerUserParam.getUsername());
        systemUserInfoVo.setRealName(registerUserParam.getRealName());
        systemUserInfoVo.setDepartment(registerUserParam.getDepartment());

        //患者端edc-h5 医护端edc-medical-personnel
        List<String> stringList = Arrays.asList(Constants.USER_LOGIN_RESOURCE_4);
        if(stringList.contains(registerUserParam.getLoginSource())){
            ProjectTesteeInfo testeeInfo = new ProjectTesteeInfo();
            testeeInfo.setId(SnowflakeIdWorker.getUuid());
            testeeInfo.setUserId(systemUserInfoVo.getId());
            testeeInfo.setRealName(registerUserParam.getRealName());
            if(registerUserParam.getBirthday() != null){
                testeeInfo.setBirthday(registerUserParam.getBirthday());
            }
            if(StringUtils.isNotEmpty(registerUserParam.getGender())){
                testeeInfo.setGender(registerUserParam.getGender());
            }
            testeeInfo.setStatus(BusinessConfig.ENABLED_STATUS.toString());
            testeeInfo.setCreateTime(new Date());
            testeeInfo.setCreateUser(systemUserInfoVo.getId().toString());
            testeeInfo.setTenantId(registerUserParam.getTenantId());
            testeeInfo.setPlatformId(registerUserParam.getPlatformId());
            projectTesteeInfoService.saveProjectTesteeInfo(testeeInfo);
            String loginPlatformId = getPlatformIdFromLoginSource(registerUserParam.getLoginSource());
            registerUserParam.setPlatformId(loginPlatformId);
        }

        systemUserInfoVo.setSealFlag(false);
        systemUserInfoVo.setActiveStatus(true);
        systemUserInfoVo.setCompanyOwnerUser(false);
        systemUserInfoVo.setDefaultAdmin(false);
        systemUserInfoVo.setStatus(BusinessConfig.ENABLED_STATUS);
        systemUserInfoVo.setCreateTime(new Date());
        SystemUserInfo systemUserInfo = new SystemUserInfo();
        BeanUtils.copyProperties(systemUserInfoVo, systemUserInfo);
        int insert;
        if(isUpdate){
            insert = systemUserInfoMapper.updateByPrimaryKey(systemUserInfo);
        }else {
            insert = systemUserInfoMapper.insert(systemUserInfo);
        }
        // 同步系统默认角色
        List<SystemUserRole> userRoleList = new ArrayList<>();
        List<SystemRole> systemRoles = systemRoleService.selectRoleByUserId(systemUserInfoVo.getId());
        if(CollectionUtil.isEmpty(systemRoles)){
            SystemUserRole systemUserRole = new SystemUserRole();
            systemUserRole.setUserId(systemUserInfoVo.getId());
            systemUserRole.setRoleId(Constants.SYSTEM_USER_DEFAULT_ROLE_ID);
            systemUserRole.setTenantId(registerUserParam.getTenantId());
            systemUserRole.setPlatformId(registerUserParam.getPlatformId());
            userRoleList.add(systemUserRole);
            systemRoleService.batchSaveUserRole(userRoleList);
        }

        SystemTenantUser tenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfoVo.getId().toString(), registerUserParam.getTenantId(), registerUserParam.getPlatformId());
        if(tenantUser == null){
            SystemTenantUser systemTenantUser = new SystemTenantUser();
            systemTenantUser.setId(SnowflakeIdWorker.getUuid());
            systemTenantUser.setUserId(systemUserInfoVo.getId());
            systemTenantUser.setCreateTime(new Date());
            systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
            systemTenantUser.setActiveStatus(true);
            systemTenantUser.setSystemAccount(false);
            systemTenantUser.setCompanyOwnerUser(false);
            systemTenantUser.setDepartment(registerUserParam.getDepartment());
            systemTenantUser.setDataFrom(registerUserParam.getLoginSource());
            systemTenantUser.setTenantId(registerUserParam.getTenantId());
            systemTenantUser.setPlatformId(registerUserParam.getPlatformId());
            systemTenantUserService.insertSystemTenantUser(systemTenantUser);
        }

        // 密码历史表中添加数据
        SystemUserPwd record = new SystemUserPwd();
        record.setPassword(DesensitizeUtil.aesEncrypt(registerUserParam.getPassword()));
        record.setId(SnowflakeIdWorker.getUuid());
        record.setUserId(systemUserInfo.getId());
        record.setStatus(BusinessConfig.VALID_STATUS);
        record.setCreateTime(new Date());
        record.setTenantId(registerUserParam.getTenantId());
        record.setPlatformId(registerUserParam.getPlatformId());
        systemUserPwdService.insert(record);

        return insert > 0 ? CommonResult.success(SystemUserReturnEnums.S200) : CommonResult.failed(SystemUserReturnEnums.E500);
    }

    @Override
    public CommonResult registerExternalSystemUserInfo(String ticket, String loginSource) {
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("ticket", ticket);
        dataMap.put("siteId", "103");
        String data = HttpUtil.post(bdpUserInfoConfig.getBdp_check_ticket_url(), dataMap);
        log.info("bdp_check_ticket_url:{}, data:{}", bdpUserInfoConfig.getBdp_check_ticket_url(), data);
        HashMap<String, Object> dataMapValue = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>(){}.getType());
        Object loginId = dataMapValue.get("data");
        if(loginId == null){
            String message = dataMapValue.get("msg").toString();
            return CommonResult.failed(message);
        }
        String body = HttpUtil.get(bdpUserInfoConfig.getBdp_get_user_url().concat("/").concat(loginId.toString()));
        log.info("getUserInfoByUserId url:{}, body:{}" , bdpUserInfoConfig.getBdp_get_user_url().concat("/" + loginId), body);
        HashMap<String, Object> bodyMap = JSON.parseObject(body, new TypeReference<HashMap<String, Object>>(){}.getType());
        Object userDataMap = bodyMap.get("data") == null ? "" : bodyMap.get("data");
        ExternalRegisterUserParam externalRegisterUserParam = JSON.parseObject(userDataMap.toString(), ExternalRegisterUserParam.class);

        String userAccount = externalRegisterUserParam.getUserAccount();
        if (StringUtils.isBlank(externalRegisterUserParam.getMobileNumber())){
            return CommonResult.failed(SystemUserReturnEnums.E20004);
        }
        // 手机号是否需要解密
        String paramMobile = externalRegisterUserParam.getMobileNumber();
        if(StringUtils.isNotBlank(paramMobile)){
            AESUtil256 aesHelper = null;
            try {
                aesHelper = new AESUtil256();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            try {
                String mobile = aesHelper.decryptStr(paramMobile);
                externalRegisterUserParam.setMobileNumber(mobile);
            } catch (Exception e) {
                throw new ServiceException("登录手机号不正确");
            }
        }

        String loginTenantId = getTenantIdFromLoginSource();
        String loginPlatformId = getPlatformIdFromLoginSource(loginSource);
        Long systemUserId;
        // 校验手机号是否已经使用,并且已经激活
        String aesMobile = DesensitizeUtil.aesEncrypt(externalRegisterUserParam.getMobileNumber());
        SystemUserInfoVo systemUserInfoByMobile = this.getSystemUserInfoByAccountName(externalRegisterUserParam.getMobileNumber());
        if(systemUserInfoByMobile == null){
            SystemUserInfo systemUserInfo = new SystemUserInfo();
            systemUserInfo.setId(SnowflakeIdWorker.getUuid());
            systemUserInfo.setUsername(userAccount);
            systemUserInfo.setBpdUserId(externalRegisterUserParam.getUserId());
            systemUserInfo.setRealName(externalRegisterUserParam.getRealName());
            systemUserInfo.setMobile(aesMobile);
            systemUserInfo.setEmail(externalRegisterUserParam.getEmail());
            systemUserInfo.setUserType(Constants.USER_TYPE_VALUE_02);
            systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
            systemUserInfo.setActiveStatus(true);
            systemUserInfo.setLockStatus(false);
            systemUserInfo.setSealFlag(false);
            systemUserInfo.setRegisterFrom(Constants.USER_TYPE_VALUE_08);
            systemUserInfo.setCreateTime(new Date());
            systemUserInfo.setCreateUser(systemUserInfo.getId().toString());
            systemUserInfo.setLoginTime(new Date());
            systemUserInfoMapper.insert(systemUserInfo);
            systemUserId = systemUserInfo.getId();
        }else{
            systemUserId = systemUserInfoByMobile.getId();
        }
        SystemTenantUser systemTenantUserResult = systemTenantUserService.getSystemTenantUserByUserId(systemUserId.toString(), loginTenantId, loginPlatformId);
        if(systemTenantUserResult == null){
            SystemTenantUser systemTenantUser = new SystemTenantUser();
            systemTenantUser.setId(SnowflakeIdWorker.getUuid());
            systemTenantUser.setUserId(systemUserId);
            systemTenantUser.setCreateTime(new Date());
            systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
            systemTenantUser.setActiveStatus(true);
            systemTenantUser.setSystemAccount(false);
            systemTenantUser.setCompanyOwnerUser(false);
            systemTenantUser.setDataFrom(loginSource);
            systemTenantUser.setTenantId(loginTenantId);
            systemTenantUser.setPlatformId(loginPlatformId);
            systemTenantUserService.insertSystemTenantUser(systemTenantUser);
        }
        // 同步系统默认角色
        List<SystemUserRole> userRoleList = new ArrayList<>();
        List<SystemRole> systemRoles = systemRoleService.selectRoleByUserId(systemUserId);
        if(CollectionUtil.isEmpty(systemRoles)){
            SystemUserRole systemUserRole = new SystemUserRole();
            systemUserRole.setUserId(systemUserId);
            systemUserRole.setRoleId(Constants.SYSTEM_USER_DEFAULT_ROLE_ID);
            systemUserRole.setTenantId(loginTenantId);
            systemUserRole.setPlatformId(loginPlatformId);
            userRoleList.add(systemUserRole);
            systemRoleService.batchSaveUserRole(userRoleList);
        }
        return CommonResult.success(externalRegisterUserParam);
    }

    private String getTenantIdFromLoginSource() {
        return Constants.SYSTEM_PLATFORM_EDC;
    }

    public String getPlatformIdFromLoginSource(String loginSource) {
        List<String> loginSourceList = Arrays.asList(Constants.USER_LOGIN_RESOURCE_1, Constants.USER_LOGIN_RESOURCE_2, Constants.USER_LOGIN_RESOURCE_3, Constants.USER_LOGIN_RESOURCE_5);
        if(loginSourceList.contains(loginSource)){
            return Constants.SYSTEM_PLATFORM_EDC;
        }
        if(Constants.USER_LOGIN_RESOURCE_4.equals(loginSource)){
            return Constants.SYSTEM_PLATFORM_EPRO;
        }
        return Constants.SYSTEM_PLATFORM_EDC;
    }

    @Override
    public SystemUserInfo getSystemUserByAccountNameOrEmail(String username) {
        return systemUserInfoMapper.getSystemUserByAccountNameOrEmail(username);
    }

    private void checkVerificationCode(String mobile,String code) {
        if(DateUtil.getCurrentDate().replace("-","").equals(code)){
            return;
        }
        String verificationCode = (String) redisTemplateService.get(RedisKeyContants.SEND_MOBILE_MESSAGE_CODE + mobile);
        if(!code.equals(verificationCode)){
            throw new ServiceException(MessageUtils.message("user.jcaptcha.error", null));
        }
    }

    @Override
    public SystemUserInfo getUserBaseInfo(Long id) {
        return systemUserInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SystemUserInfo> getSysUserList(String keyword, Integer pageSize, Integer pageNum) {
        PageHelper.startPage(pageNum, pageSize);
        SystemUserInfoExample example = new SystemUserInfoExample();
        SystemUserInfoExample.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(keyword)) {
            criteria.andUsernameLike("%" + keyword + "%");
            example.or(example.createCriteria().andNickNameLike("%" + keyword + "%"));
        }
        return systemUserInfoMapper.selectByExample(example);
    }

    @Override
    public CustomResult updateSystemUserInfo(SystemUserInfoParam systemUserInfoParam) {
        CustomResult customResult = new CustomResult();
        SystemUserInfo systemUserInfo = systemUserInfoMapper.selectByPrimaryKey(systemUserInfoParam.getId());
        if(systemUserInfo == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        BeanUtils.copyProperties(systemUserInfoParam, systemUserInfo);
        systemUserInfo.setUpdateUser(systemUserInfoParam.getCreateUserId());
        systemUserInfo.setUpdateTime(new Date());

        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        if(StringUtils.isNotBlank(systemUserInfoParam.getPassword()) && !passwordEncoder.matches(systemUserInfoParam.getPassword(), systemUserInfo.getPassword())){
            log.info("update userInfo username:{} , password:{} " ,systemUserInfo.getUsername(), systemUserInfoParam.getPassword());
            systemUserInfo.setPassword(passwordEncoder.encode(systemUserInfoParam.getPassword()));
            systemUserInfo.setLastModifyPwdTime(new Date());
        }
        if (StringUtils.isNotBlank(systemUserInfoParam.getMobile())){
            systemUserInfo.setMobile(DesensitizeUtil.aesEncrypt(systemUserInfoParam.getMobile()));
        }
        systemUserInfoMapper.updateByPrimaryKeySelective(systemUserInfo);
        boolean activeStatus = systemUserInfo.getActiveStatus() != null && systemUserInfo.getActiveStatus();
        if(!activeStatus){
            organizationService.saveSystemUserOrgInfo(systemUserInfo.getId().toString(), systemUserInfoParam.getOrgId());
        }
        if(StringUtils.isNotBlank(systemUserInfoParam.getOrgId())){
            organizationService.deleteSystemUserOrgInfo(systemUserInfo.getId().toString());
            organizationService.saveSystemUserOrgInfo(systemUserInfo.getId().toString(), systemUserInfoParam.getOrgId());
        }
        updateSystemUserRole(systemUserInfoParam.getId(), systemUserInfoParam.getRoleIdList());
        return customResult;
    }

    @Override
    public CustomResult deleteSystemUserInfo(String userId) {
        CustomResult customResult = new CustomResult();
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        // 运营账户禁止删除
        SystemUserInfo systemUserInfo = systemUserInfoMapper.selectByPrimaryKey(Long.parseLong(userId));
        if(systemUserInfo.getCompanyOwnerUser() != null && systemUserInfo.getCompanyOwnerUser() && systemUserInfo.getActiveStatus()){
            customResult.setMessage("平台运营账号不能删除");
            return customResult;
        }
        //验证数据
        List<ProjectApplyUser> projectUserList = projectUserService.getProjectUserResultByUserId(userId);
        if(CollectionUtil.isNotEmpty(projectUserList)){
            customResult.setMessage("该用户已参与科研项目，不能删除了");
            return customResult;
        }
        // 数据复制到备份数据库中
        SystemUserInfoBack userInfoBack= new SystemUserInfoBack();
        BeanUtils.copyProperties(systemUserInfo,userInfoBack);
        SystemUserInfoBack systemUserInfoBack = systemUserInfoBackMapper.selectByPrimaryKey(systemUserInfo.getId());
        if(systemUserInfoBack == null){
            systemUserInfoBackMapper.insertSelective(userInfoBack);
        }
        SystemTenantUserExample example = new SystemTenantUserExample();
        SystemTenantUserExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteria.andPlatformIdEqualTo(SecurityUtils.getSystemPlatformId());
        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(userId, systemTenantId, systemPlatformId);
        if(systemTenantUser != null){
            systemTenantUserService.deleteSystemTenantUserByPrimaryKey(systemTenantUser.getId());
        }
        systemUserInfoMapper.deleteUserById(userId);
        systemUserRoleMapper.deleteSystemUserRoleByUserId(Long.valueOf(userId), systemTenantId, systemPlatformId);
        organizationService.deleteSystemUserOrgInfo(userId);
        // 如果存在研究者信息 则关联删除
        projectResearchersInfoService.deleteResearchersInfoByUserId(userId);
        return customResult;
    }

    public void updateSystemUserRole(Long userId, List<Long> roleIds) {
        //建立新关系
        if (!CollectionUtils.isEmpty(roleIds)) {
            systemRoleService.deleteUserRole(userId);
            List<SystemUserRole> list = new ArrayList<>();
            for (Long roleId : roleIds) {
                SystemUserRole roleRelation = new SystemUserRole();
                roleRelation.setUserId(userId);
                roleRelation.setRoleId(roleId);
                list.add(roleRelation);
            }
            systemRoleService.batchSaveUserRole(list);
        }
    }


    @Override
    public String updateUserPassword(UpdateSystemUserPasswordParam userInfoParam) {
        SystemUserInfoVo systemUserInfoVo = this.getSystemUserInfoByAccountName(userInfoParam.getUsername());
        if(systemUserInfoVo == null){
            return BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND;
        }
        // 校验用户是否已经激活，没有激活不循序修改
        SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(systemUserInfoVo.getId().toString(), userInfoParam.getTenantId(), userInfoParam.getPlatformId());
        if(systemTenantUser != null && !systemTenantUser.getActiveStatus()){
            return BusinessConfig.USER_ACTIVE_ERROR;
        }
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

        // 校验是否符合密码策略中的设置
        passwordCheckService.checkPwd(userInfoParam.getNewPassword());

        //校验密码历史是否已经存在，并超过可允许重复的次数
        if (!passwordCheckService.checkPwdHis(systemUserInfoVo.getId(),userInfoParam.getNewPassword())){
            return BusinessConfig.USER_LOGIN_PASSWORD_HIS_REPEAT;
        }

        if(BusinessConfig.UPDATE_PASSWORD_TYPE_02.equals(userInfoParam.getType())){
            if(StringUtils.isNotEmpty(userInfoParam.getInitPassword())){
                // 校验当前的密码和原密码是否相同
                if(passwordEncoder.matches(userInfoParam.getNewPassword(), systemUserInfoVo.getPassword())){
                    return BusinessConfig.USER_LOGIN_PASSWORD_MESSAGE_02;
                }
            }
            SystemUserInfo systemUserInfo = new SystemUserInfo();
            BeanUtils.copyProperties(systemUserInfoVo, systemUserInfo);
            String res = updateDefaultPassword(systemUserInfo, userInfoParam);
            if(BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(res)){
                redisTemplateService.del(RedisKeyContants.EDC_LOGIN_USER_LOCK.concat(userInfoParam.getUsername()));
                // 删除锁定状态
                if (systemTenantUser!=null) {
                    systemTenantUser.setLockStatus(false);
                    systemTenantUser.setLockTime(null);
                    systemTenantUserService.updateSystemTenantUser(systemTenantUser);
                }
            }
            return res;

        }
        if(BusinessConfig.UPDATE_PASSWORD_TYPE_01.equals(userInfoParam.getType())){
            if(!passwordEncoder.matches(userInfoParam.getOldPassword(), systemUserInfoVo.getPassword())){
                return BusinessConfig.USER_LOGIN_PASSWORD_MESSAGE_01;
            }
        }
        if(passwordEncoder.matches(userInfoParam.getNewPassword(), systemUserInfoVo.getPassword())){
            return BusinessConfig.USER_LOGIN_PASSWORD_MESSAGE_02;
        }
        systemUserInfoVo.setPassword(passwordEncoder.encode(userInfoParam.getNewPassword()));
        if(StringUtils.isNotEmpty(userInfoParam.getInitPassword())){
            systemUserInfoVo.setLoginCode(DesensitizeUtil.aesEncrypt(userInfoParam.getInitPassword()));
            systemUserInfoVo.setPassword(passwordEncoder.encode(userInfoParam.getInitPassword()));
        }
        SystemUserInfo systemUserInfo = new SystemUserInfo();
        BeanUtils.copyProperties(systemUserInfoVo, systemUserInfo);
        systemUserInfo.setLastModifyPwdTime(new Date());
        systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
        updateSystemUser(systemUserInfo);
        // 密码历史表中添加数据
        SystemUserPwd record = new SystemUserPwd();
        record.setPassword(DesensitizeUtil.aesEncrypt(userInfoParam.getNewPassword()));
        record.setId(SnowflakeIdWorker.getUuid());
        record.setUserId(systemUserInfoVo.getId());
        record.setStatus(BusinessConfig.VALID_STATUS);
        record.setCreateTime(new Date());
        record.setTenantId(userInfoParam.getTenantId());
        record.setPlatformId(userInfoParam.getPlatformId());
        systemUserPwdService.insert(record);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    private String updateDefaultPassword(SystemUserInfo systemUserInfo, UpdateSystemUserPasswordParam userInfoParam){
        if(StringUtils.isEmpty(userInfoParam.getNewPassword())){
            systemUserInfo.setPassword(passwordEncoder.encode(Constants.USER_DEFAULT_LOGIN_PASSWORD));
        }else{
            systemUserInfo.setPassword(passwordEncoder.encode(userInfoParam.getNewPassword()));
        }
        systemUserInfo.setUpdateUser(userInfoParam.getCreateUserId());
        systemUserInfo.setLastModifyPwdTime(new Date());
        updateSystemUser(systemUserInfo);
        log.info("current loginUserId: {}, updateUserDefaultPassword: {} " ,userInfoParam.getCreateUserId(), systemUserInfo.getUsername());
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }


    @Override
    public SystemUserInfoExtendVo getUserInfoByMobile(String mobile){
        SystemUserInfoExtendVo systemUserInfoExtendVo = new SystemUserInfoExtendVo();
        SystemUserInfoExample example = new SystemUserInfoExample();
        SystemUserInfoExample.Criteria criteria = example.createCriteria();
        criteria.andMobileEqualTo(mobile);
        List<SystemUserInfo> sysUserList = systemUserInfoMapper.selectByExample(example);
        if(sysUserList != null && sysUserList.size() >0){
            SystemUserInfo umsAdmin = sysUserList.get(0);
            BeanUtils.copyProperties(umsAdmin, systemUserInfoExtendVo);
            List<OrganizationVo> userOrgList = organizationService.getUserOrgList(umsAdmin.getId().toString());
            if(userOrgList != null){
                systemUserInfoExtendVo.setUserOrganizationInfo(userOrgList);
            }
            return systemUserInfoExtendVo;
        }
        return null;
    }


    @Override
    public SystemUserInfoExtendVo getUserInfoByUserName(String username){
        SystemUserInfoExtendVo systemUserInfoExtendVo = new SystemUserInfoExtendVo();
        SystemUserInfoVo umsAdmin = getSystemUserInfoByAccountName(username);
        BeanUtils.copyProperties(umsAdmin, systemUserInfoExtendVo);
        List<OrganizationVo> userOrgList = organizationService.getUserOrgList(umsAdmin.getId().toString());
        if(userOrgList != null && userOrgList.size() >0){
            systemUserInfoExtendVo.setUserOrganizationInfo(userOrgList);
        }
        return systemUserInfoExtendVo;
    }

    @Override
    public SystemUserInfoExtendVo getSystemUserInfoByUserId(String userId) {
        if(StringUtils.isBlank(userId)){
            return null;
        }
        SystemUserInfo systemUserInfo = systemUserInfoMapper.selectByPrimaryKey(Long.parseLong(userId));
        if(systemUserInfo == null){ return null; }
        SystemUserInfoExtendVo systemUserInfoExtendVo = new SystemUserInfoExtendVo();
        BeanUtils.copyProperties(systemUserInfo, systemUserInfoExtendVo);
        if(StringUtils.isNotEmpty(systemUserInfo.getDepartment())) {
            Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(systemUserInfo.getDepartment());
            if(systemOrganizationInfo != null){
                systemUserInfoExtendVo.setDepartmentName(systemOrganizationInfo.getName());
            }
        }
        if(StringUtils.isNotEmpty(systemUserInfo.getPositional())){
            String positionalName = dictionaryService.getDictNameById(Long.parseLong(systemUserInfo.getPositional()));
            systemUserInfoExtendVo.setPositionalName(positionalName);
        }
        if (StringUtils.isNotBlank(systemUserInfo.getMobile())){
            systemUserInfoExtendVo.setMobile(DesensitizeUtil.aesDecrypt(systemUserInfo.getMobile()));
        }
        return systemUserInfoExtendVo;
    }

    @Override
    public List<SystemUserExtendVo> getProjectUserList(String projectId, String ifPrimary, String username, String realName, String orgId, String mobile, String roleCode){
        List<SystemUserExtendVo> projectUserList = new ArrayList<>();
        Map<String, Object> params = requestParamsWrapper(username, realName, mobile, orgId, roleCode);
        if(StringUtils.isNotBlank(projectId)){
            params.put("projectId", projectId);
        }
        if(StringUtils.isNotBlank(ifPrimary)){
            params.put("ifPrimary", ifPrimary.equals("1"));
        }
        params.put("queryProjectUser", true);
        List<SystemUserExtendVo> systemUserList = systemUserInfoMapper.getSystemUserList(params);

        for (SystemUserExtendVo systemUserExtendVo : systemUserList) {
            /*ProjectUserVo projectUserInfo = projectUserService.getProjectUserDataByUserId(projectId, systemUserExtendVo.getId().toString());
            if(projectUserInfo != null) {
                String ename = projectUserInfo.getRoleCode();
                systemUserExtendVo.setRoleName(projectUserInfo.getRoleName());
                systemUserExtendVo.setRoleEName(ename);
            }
            if(StringUtils.isEmpty(roleCode)){
                projectUserList.add(systemUserExtendVo);
            }else{
                if(projectUserInfo != null){
                    String ename = projectUserInfo.getRoleCode();
                    if(ename.equals(roleCode)){
                        projectUserList.add(systemUserExtendVo);
                    }
                }
            }*/
        }
        return projectUserList;
    }

    @Override
    public CommonPage<SystemUserExtendVo> getSystemUserListForPage(String username, String realName, String mobile, String orgId, Integer pageNum, Integer pageSize){
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<SystemUserExtendVo> dataList = new ArrayList<>();
        Map<String, Object> paramsMap = requestParamsWrapper(username, realName, mobile, orgId, null);
        paramsMap.put("ifPrimary", true);
        //是否查询项目成员标识
        paramsMap.put("queryProjectUser", false);
        List<SystemUserExtendVo> userList = systemUserInfoMapper.getSystemUserList(paramsMap);
        for (SystemUserExtendVo systemUserExtendVo : userList) {
            SystemUserExtendVo umsAdminExtend = new SystemUserExtendVo();
            BeanUtils.copyProperties(systemUserExtendVo, umsAdminExtend);
            String department = systemUserExtendVo.getDepartment();
            String positional = systemUserExtendVo.getPositional();
            if(StringUtils.isNotBlank(department)){
                SysDepartmentVo sysDepartmentVo = systemDepartmentService.getDepartment(Long.parseLong(department));
                if(sysDepartmentVo != null){
                    umsAdminExtend.setDepartment(sysDepartmentVo.getName());
                }
            }
            String positionalName = dictionaryService.getDictNameById(Long.parseLong(positional));
            umsAdminExtend.setPositional(positionalName);
            dataList.add(umsAdminExtend);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    private Map<String, Object> requestParamsWrapper(String username, String realName, String mobile, String orgId, String roleCode) {
        Map<String,Object> paramsMap = new HashMap<>();
        if(StringUtils.isNotBlank(username)){
            paramsMap.put("username", username);
        }
        if(StringUtils.isNotBlank(realName)){
            paramsMap.put("realName", realName);
        }
        if(StringUtils.isNotBlank(orgId)){
            paramsMap.put("orgId", getQueryWrapperParams(orgId));
        }
        if(StringUtils.isNotBlank(mobile)){
            paramsMap.put("mobile", mobile);
        }
        if(StringUtils.isNotBlank(roleCode)){
            paramsMap.put("roleCode", roleCode);
        }
        return paramsMap;
    }

    @Override
    public String checkPhoneUnique(BaseSystemUser user) {
        Long userId = StringUtils.isNull(user.getId()) ? -1L : user.getId();
        SystemUserInfo info = systemUserInfoMapper.checkMobileExists(user.getMobile());
        if(StringUtils.isNotNull(info) && info.getId().longValue() != userId.longValue()){
           return BusinessConfig.RETURN_MESSAGE_FAIL;
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public int updateSystemUser(SystemUserInfo systemUserInfo) {
        return systemUserInfoMapper.updateByPrimaryKeySelective(systemUserInfo);
    }

    @Override
    public int insertSystemUser(SystemUserInfo user) {
        return systemUserInfoMapper.insert(user);
    }

    @Override
    public CommonResult updateUserStatus(String userId,String projectId, Integer status) {
        ProjectUserInfo projectUserInfo = projectUserService.getProjectUserInfoByUserId(projectId, userId);
        if(projectUserInfo == null){ return null; }
        projectUserInfo.setStatus(status.toString());
        projectUserService.updateProjectUserInfoById(projectUserInfo);
        return CommonResult.success(null);
    }

    @Override
    public SystemUserInfo getSystemUserInfoByMobile(String mobile) {
        return systemUserInfoMapper.checkMobileExists(mobile);
    }


    @Override
    public List<SystemUserInfo> selectByExample(SystemUserInfoExample example){
        return systemUserInfoMapper.selectByExample(example);
    }

    @Override
    public int selectCountUser(Long departmentId, String platformId, String tenantId){
        return systemUserInfoMapper.selectCountUser(departmentId, platformId, tenantId);
    }

    @Override
    public SystemUserInfo selectByPrimaryKey(Long id){
        return systemUserInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SystemUserInfo record){
        return systemUserInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int selectCountOutUser(String tenantId, String platformId, String userType){
        return systemUserInfoMapper.selectCountOutUser(tenantId, platformId, userType);

    }
    @Override
    public List<SystemUserInfo> getSystemUserListForPage(SystemUserInfoParam systemUserInfoParam){
        return systemUserInfoMapper.selectSystemUserListForPage(systemUserInfoParam);

    }

    @Override
    public List<SystemUserInfo> selectJoinSystemTenantUser(SystemUserInfoParam param){
        return systemUserInfoMapper.selectJoinSystemTenantUser(param);

    }

    @Override
    public CommonResult registerResearchProjectUserUserInfo(String mobile, String loginSource) {
        ExternalRegisterUserParam externalRegisterUserParam = new ExternalRegisterUserParam();
        String loginTenantId = getTenantIdFromLoginSource();
        String loginPlatformId = getPlatformIdFromLoginSource(loginSource);
        externalRegisterUserParam.setMobileNumber(mobile);
        externalRegisterUserParam.setLoginTenantId(loginTenantId);
        externalRegisterUserParam.setLoginPlatformId(loginPlatformId);
        Long systemUserId;
        // 校验手机号是否已经使用,并且已经激活
        SystemUserInfoVo systemUserInfoByMobile = this.getSystemUserInfoByAccountName(mobile);
        if(systemUserInfoByMobile == null){
            SystemUserInfo systemUserInfo = new SystemUserInfo();
            systemUserInfo.setId(SnowflakeIdWorker.getUuid());
            systemUserInfo.setUsername(mobile);
            String aesMobile = DesensitizeUtil.aesEncrypt(mobile);
            systemUserInfo.setMobile(aesMobile);
            systemUserInfo.setUserType(Constants.USER_TYPE_VALUE_02);
            systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
            systemUserInfo.setActiveStatus(true);
            systemUserInfo.setLockStatus(false);
            systemUserInfo.setSealFlag(false);
            systemUserInfo.setRegisterFrom(Constants.USER_TYPE_VALUE_09);
            systemUserInfo.setCreateTime(new Date());
            systemUserInfo.setCreateUser(systemUserInfo.getId().toString());
            systemUserInfo.setLoginTime(new Date());
            systemUserInfoMapper.insert(systemUserInfo);
            systemUserId = systemUserInfo.getId();
        }else{
            systemUserId = systemUserInfoByMobile.getId();
        }
        SystemTenantUser systemTenantUserResult = systemTenantUserService.getSystemTenantUserByUserId(systemUserId.toString(), loginTenantId, loginPlatformId);
        if(systemTenantUserResult == null){
            SystemTenantUser systemTenantUser = new SystemTenantUser();
            systemTenantUser.setId(SnowflakeIdWorker.getUuid());
            systemTenantUser.setUserId(systemUserId);
            systemTenantUser.setCreateTime(new Date());
            systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
            systemTenantUser.setActiveStatus(true);
            systemTenantUser.setSystemAccount(false);
            systemTenantUser.setCompanyOwnerUser(false);
            systemTenantUser.setDataFrom(loginSource);
            systemTenantUser.setTenantId(loginTenantId);
            systemTenantUser.setPlatformId(loginPlatformId);
            systemTenantUserService.insertSystemTenantUser(systemTenantUser);
        }
        // 同步系统默认角色
        List<SystemUserRole> userRoleList = new ArrayList<>();
        List<SystemRole> systemRoles = systemRoleService.selectRoleByUserId(systemUserId);
        if(CollectionUtil.isEmpty(systemRoles)){
            SystemUserRole systemUserRole = new SystemUserRole();
            systemUserRole.setUserId(systemUserId);
            systemUserRole.setRoleId(Constants.SYSTEM_USER_DEFAULT_ROLE_ID);
            systemUserRole.setTenantId(loginTenantId);
            systemUserRole.setPlatformId(loginPlatformId);
            userRoleList.add(systemUserRole);
            systemRoleService.batchSaveUserRole(userRoleList);
        }
        return CommonResult.success(externalRegisterUserParam);
    }
    
    @Override
    public CustomResult saveBatchSystemUser(MultipartFile file, String enterprise, boolean projectAuthority) throws Exception {
        CustomResult customResult = new CustomResult();
        String loginTenantId = getTenantIdFromLoginSource();
        String loginPlatformId = getPlatformIdFromLoginSource(Constants.USER_LOGIN_RESOURCE_1);
        ExcelUtil<SystemUserExcelParam> excelUtil = new ExcelUtil<>(SystemUserExcelParam.class);
        List<SystemUserExcelParam> systemUserExcelParamList = excelUtil.importExcel(file.getInputStream());
        if(CollectionUtil.isEmpty(systemUserExcelParamList)){
            customResult.setCode(ResultCode.BUSINESS_PROJECT_RESEARCHERS_IMPORT_MESSAGE_01.getCode());
            customResult.setMessage(ResultCode.BUSINESS_PROJECT_RESEARCHERS_IMPORT_MESSAGE_01.getMessage());
            return customResult;
        }
        String batchCode  = DateUtil.getCurrentdate();
        customResult.setData(systemUserExcelParamList);
        for (SystemUserExcelParam systemUserExcelParam : systemUserExcelParamList) {
            if(StringUtils.isBlank(systemUserExcelParam.getTelPhone())){continue;}
            systemUserExcelParam.setTelPhone(StrUtil.cleanBlank(systemUserExcelParam.getTelPhone()));
            Long systemUserId;
            SystemUserInfoVo systemUserInfoByMobile = this.getSystemUserInfoByAccountName(systemUserExcelParam.getTelPhone());
            if(systemUserInfoByMobile == null){
                SystemUserInfo systemUserInfo = new SystemUserInfo();
                systemUserInfo.setId(SnowflakeIdWorker.getUuid());
                systemUserInfo.setUsername(systemUserExcelParam.getTelPhone());
                systemUserInfo.setRealName(systemUserExcelParam.getName());
                String aesMobile = DesensitizeUtil.aesEncrypt(systemUserExcelParam.getTelPhone());
                systemUserInfo.setMobile(aesMobile);
                systemUserInfo.setEmail(null);
                //String encodePassword = passwordEncoder.encode(Constants.BORUI_EDC_DEFAULT_PASSWORD);
                //systemUserInfo.setPassword(encodePassword);
                systemUserInfo.setUserType(Constants.USER_TYPE_VALUE_01);
                systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
                systemUserInfo.setEnterprise(enterprise);
                Dictionary dictionaryPositional = dictionaryService.getDictInfoByNameAndCode("职称", "LEVEL");
                dictionaryPositional = dictionaryService.getDictByName(systemUserExcelParam.getPositional(), dictionaryPositional.getId());
                if(dictionaryPositional != null){systemUserInfo.setPositional(dictionaryPositional.getId().toString());}
                systemUserInfo.setActiveStatus(false);
                systemUserInfo.setLockStatus(false);
                systemUserInfo.setSealFlag(false);
                systemUserInfo.setRegisterFrom(Constants.USER_TYPE_VALUE_10);
                systemUserInfo.setCreateTime(new Date());
                systemUserInfo.setCreateUser(systemUserInfo.getId().toString());
                systemUserInfo.setNote("<" + batchCode + ">");
                
                String areaId = "";
                String groupInfoId = "";
                Dictionary dictionaryArea = dictionaryService.getDictInfoByNameAndCode("区域", "QY");
                dictionaryArea = dictionaryService.getDictByName(systemUserExcelParam.getArea(), dictionaryArea.getId());
                if(dictionaryArea != null){areaId = dictionaryArea.getId().toString();}
                Dictionary dictionaryGroupInfo = dictionaryService.getDictInfoByNameAndCode("组", "GROUP");
                dictionaryGroupInfo = dictionaryService.getDictByName(systemUserExcelParam.getGroupInfo(), dictionaryGroupInfo.getId());
                if(dictionaryGroupInfo != null){groupInfoId = dictionaryGroupInfo.getId().toString();}
                systemUserInfo.setRoleDesc(groupInfoId);
                systemUserInfoMapper.insert(systemUserInfo);
                systemUserId = systemUserInfo.getId();
                
                // 设置研究者信息
                ProjectResearchersInfo projectResearchersInfo = new ProjectResearchersInfo();
                BeanUtils.copyProperties(systemUserExcelParam, projectResearchersInfo);
                projectResearchersInfo.setName(systemUserExcelParam.getName());
                projectResearchersInfo.setArea(areaId);
                projectResearchersInfo.setGroupInfo(groupInfoId);
                projectResearchersInfo.setCreateUser(systemUserId.toString());
                projectResearchersInfo.setCertificate(null);
                projectResearchersInfoService.saveOrUpdate(projectResearchersInfo);
            }else{
                systemUserId = systemUserInfoByMobile.getId();
            }
            SystemTenantUser systemTenantUserResult = systemTenantUserService.getSystemTenantUserByUserId(systemUserId.toString(), loginTenantId, loginPlatformId);
            if(systemTenantUserResult == null){
                SystemTenantUser systemTenantUser = new SystemTenantUser();
                systemTenantUser.setId(SnowflakeIdWorker.getUuid());
                systemTenantUser.setUserId(systemUserId);
                systemTenantUser.setEnterprise(enterprise);
                Dictionary dictionaryPositional = dictionaryService.getDictInfoByNameAndCode("职称", "LEVEL");
                dictionaryPositional = dictionaryService.getDictByName(systemUserExcelParam.getPositional(), dictionaryPositional.getId());
                if(dictionaryPositional != null){systemTenantUser.setPositional(dictionaryPositional.getId().toString());}
                systemTenantUser.setCreateTime(new Date());
                systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
                systemTenantUser.setActiveStatus(false);
                systemTenantUser.setSystemAccount(false);
                systemTenantUser.setCompanyOwnerUser(false);
                systemTenantUser.setDataFrom(Constants.USER_LOGIN_RESOURCE_1);
                systemTenantUser.setTenantId(loginTenantId);
                systemTenantUser.setPlatformId(loginPlatformId);
                log.error("systemTenantUser insert mobile: {}", systemUserExcelParam.getTelPhone());
                systemTenantUserService.insertSystemTenantUser(systemTenantUser);
            }
            // 同步系统默认角色
            List<SystemUserRole> userRoleList = new ArrayList<>();
            List<SystemRole> systemRoles = systemRoleService.selectRoleByUserId(systemUserId);
            if(CollectionUtil.isEmpty(systemRoles)){
                SystemUserRole systemUserRole = new SystemUserRole();
                systemUserRole.setUserId(systemUserId);
                systemUserRole.setRoleId(Constants.SYSTEM_USER_DEFAULT_ROLE_ID);
                systemUserRole.setTenantId(loginTenantId);
                systemUserRole.setPlatformId(loginPlatformId);
                userRoleList.add(systemUserRole);
                systemRoleService.batchSaveUserRole(userRoleList);
            }
            Project project = projectBaseManageService.getEnableProjectBaseInfo(loginTenantId, loginPlatformId);
            ProjectUserInfo projectUserInfo = projectUserService.getProjectUserInfoByUserId(project.getId().toString(), systemUserId.toString());
            if(projectUserInfo == null){
                ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrganizationInfo(project.getId().toString());
                projectUserService.saveAuthorizeProjectUser(project.getId().toString(), systemUserId.toString(), projectOrgInfo.getId().toString(), loginTenantId, loginPlatformId);
            }
        }
        return customResult;
    }
    
}
