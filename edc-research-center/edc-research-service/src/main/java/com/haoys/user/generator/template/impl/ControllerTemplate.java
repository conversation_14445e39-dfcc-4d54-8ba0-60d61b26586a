package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import java.util.Map;

/**
 * Controller模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class ControllerTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String controllerPackage = (String) context.get("controllerPackage");
        String servicePackage = (String) context.get("servicePackage");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String serviceName = (String) context.get("serviceName");
        String controllerName = (String) context.get("controllerName");
        String entityNameLowerCase = (String) context.get("entityNameLowerCase");
        String primaryKeyType = (String) context.get("primaryKeyType");
        String primaryKeyProperty = (String) context.get("primaryKeyProperty");
        String currentDate = (String) context.get("currentDate");
        boolean enableSwagger = (Boolean) context.get("enableSwagger");
        
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(controllerPackage).append(";\n\n");
        
        // 导入语句
        sb.append("import ").append(modelPackage).append(".").append(entityName).append(";\n");
        sb.append("import ").append(servicePackage).append(".").append(serviceName).append(";\n");
        sb.append("import com.haoys.user.common.api.CommonPage;\n");
        sb.append("import com.haoys.user.common.api.CommonResult;\n");
        sb.append("import com.haoys.user.common.core.base.BaseController;\n");
        if (enableSwagger) {
            sb.append("import io.swagger.annotations.Api;\n");
            sb.append("import io.swagger.annotations.ApiOperation;\n");
            sb.append("import io.swagger.annotations.ApiParam;\n");
        }
        sb.append("import lombok.extern.slf4j.Slf4j;\n");
        sb.append("import org.springframework.beans.factory.annotation.Autowired;\n");
        sb.append("import org.springframework.web.bind.annotation.*;\n");
        sb.append("import java.util.List;\n");
        sb.append("import java.util.Map;\n\n");
        
        // 类注释
        sb.append("/**\n");
        sb.append(" * ").append(tableInfo.getTableDescription()).append("Controller\n");
        sb.append(" * 提供RESTful API接口\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" * @version 2.0.0\n");
        sb.append(" * @since 1.0.0\n");
        sb.append(" * @date ").append(currentDate).append("\n");
        sb.append(" */\n");
        
        // 类注解
        sb.append("@Slf4j\n");
        sb.append("@RestController\n");
        sb.append("@RequestMapping(\"/").append(entityNameLowerCase).append("\")\n");
        if (enableSwagger) {
            sb.append("@Api(tags = \"").append(tableInfo.getTableDescription()).append("管理\")\n");
        }
        
        // 类声明
        sb.append("public class ").append(controllerName).append(" extends BaseController {\n\n");
        
        // 注入Service
        sb.append("    @Autowired\n");
        sb.append("    private ").append(serviceName).append(" ").append(entityNameLowerCase).append("Service;\n\n");
        
        // 生成API方法
        generateApiMethods(sb, entityName, entityNameLowerCase, serviceName, primaryKeyType, primaryKeyProperty, enableSwagger, tableInfo);
        
        sb.append("}\n");
        
        return sb.toString();
    }
    
    private static void generateApiMethods(StringBuilder sb, String entityName, String entityNameLowerCase, 
                                         String serviceName, String primaryKeyType, String primaryKeyProperty, 
                                         boolean enableSwagger, TableInfo tableInfo) {
        
        // 创建接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"创建").append(tableInfo.getTableDescription()).append("\", notes = \"创建新的").append(tableInfo.getTableDescription()).append("记录\")\n");
        }
        sb.append("    @PostMapping\n");
        sb.append("    public CommonResult<").append(entityName).append("> create(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"").append(tableInfo.getTableDescription()).append("信息\", required = true) ");
        }
        sb.append("@RequestBody ").append(entityName).append(" ").append(entityNameLowerCase).append(") {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.create(").append(entityNameLowerCase).append(");\n");
        sb.append("    }\n\n");
        
        // 删除接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"删除").append(tableInfo.getTableDescription()).append("\", notes = \"根据ID删除").append(tableInfo.getTableDescription()).append("\")\n");
        }
        sb.append("    @DeleteMapping(\"/{").append(primaryKeyProperty).append("}\")\n");
        sb.append("    public CommonResult<Void> delete(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"").append(tableInfo.getTableDescription()).append("ID\", required = true) ");
        }
        sb.append("@PathVariable ").append(primaryKeyType).append(" ").append(primaryKeyProperty).append(") {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.deleteById(").append(primaryKeyProperty).append(");\n");
        sb.append("    }\n\n");
        
        // 更新接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"更新").append(tableInfo.getTableDescription()).append("\", notes = \"更新").append(tableInfo.getTableDescription()).append("信息\")\n");
        }
        sb.append("    @PutMapping\n");
        sb.append("    public CommonResult<").append(entityName).append("> update(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"").append(tableInfo.getTableDescription()).append("信息\", required = true) ");
        }
        sb.append("@RequestBody ").append(entityName).append(" ").append(entityNameLowerCase).append(") {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.update(").append(entityNameLowerCase).append(");\n");
        sb.append("    }\n\n");
        
        // 查询接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"查询").append(tableInfo.getTableDescription()).append("\", notes = \"根据ID查询").append(tableInfo.getTableDescription()).append("\")\n");
        }
        sb.append("    @GetMapping(\"/{").append(primaryKeyProperty).append("}\")\n");
        sb.append("    public CommonResult<").append(entityName).append("> getById(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"").append(tableInfo.getTableDescription()).append("ID\", required = true) ");
        }
        sb.append("@PathVariable ").append(primaryKeyType).append(" ").append(primaryKeyProperty).append(") {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.getById(").append(primaryKeyProperty).append(");\n");
        sb.append("    }\n\n");
        
        // 列表查询接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"查询").append(tableInfo.getTableDescription()).append("列表\", notes = \"根据条件查询").append(tableInfo.getTableDescription()).append("列表\")\n");
        }
        sb.append("    @GetMapping\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> list(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"查询参数\") ");
        }
        sb.append("@RequestParam Map<String, Object> params) {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.listByCondition(params);\n");
        sb.append("    }\n\n");
        
        // 分页查询接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"分页查询").append(tableInfo.getTableDescription()).append("\", notes = \"分页查询").append(tableInfo.getTableDescription()).append("列表\")\n");
        }
        sb.append("    @GetMapping(\"/page\")\n");
        sb.append("    public CommonResult<CommonPage<").append(entityName).append(">> listByPage(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"查询参数\") ");
        }
        sb.append("@RequestParam Map<String, Object> params,\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"页码\", defaultValue = \"1\") ");
        }
        sb.append("@RequestParam(defaultValue = \"1\") int pageNum,\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"页大小\", defaultValue = \"10\") ");
        }
        sb.append("@RequestParam(defaultValue = \"10\") int pageSize) {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.pageByCondition(params, pageNum, pageSize);\n");
        sb.append("    }\n\n");
        
        // 批量删除接口
        if (enableSwagger) {
            sb.append("    @ApiOperation(value = \"批量删除").append(tableInfo.getTableDescription()).append("\", notes = \"根据ID列表批量删除").append(tableInfo.getTableDescription()).append("\")\n");
        }
        sb.append("    @DeleteMapping(\"/batch\")\n");
        sb.append("    public CommonResult<Void> batchDelete(\n");
        if (enableSwagger) {
            sb.append("            @ApiParam(value = \"ID列表\", required = true) ");
        }
        sb.append("@RequestBody List<").append(primaryKeyType).append("> ids) {\n");
        sb.append("        return ").append(entityNameLowerCase).append("Service.batchDeleteByIds(ids);\n");
        sb.append("    }\n\n");
    }
}
