package com.haoys.user.manager.factory;

import cn.hutool.http.useragent.UserAgentUtil;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.ServletUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectOrgParam;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.model.SystemExceptionLog;
import com.haoys.user.model.SystemLoginLog;
import com.haoys.user.model.SystemPointLog;
import com.haoys.user.model.SystemRequestLog;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.SendMessageService;
import com.haoys.user.service.SystemExceptionLogService;
import com.haoys.user.service.SystemLoginLogService;
import com.haoys.user.service.SystemPointLogService;
import com.haoys.user.service.SystemRequestLogService;
import com.haoys.user.service.SystemUserInfoService;
import eu.bitwalker.useragentutils.UserAgent;
import net.dreamlu.mica.ip2region.core.IpInfo;
import net.dreamlu.mica.ip2region.impl.Ip2regionSearcherImpl;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.TimerTask;

public class AsyncTaskFactory {

    public static TimerTask recordSystemloginLogInfo(HttpServletRequest request, final String username, final String status, final String message, final String operateType, final Object... args) {
        UserAgent userAgentOld;
        String userAgentValue = "";
        if(request == null){
            userAgentValue = ServletUtils.getRequest().getHeader("User-Agent");
            userAgentOld = UserAgent.parseUserAgentString(userAgentValue);
            request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            
        }else {
            userAgentValue = request.getHeader("User-Agent");
            userAgentOld = UserAgent.parseUserAgentString(userAgentValue);
        }
        
        String ipAddress = RequestIpUtils.getIpAddress(request);
        //String ipAdrress = RequestIpUtils.getIp(request);
        final UserAgent userAgent = userAgentOld;
        String finalUserAgentValue = userAgentValue;
        return new TimerTask() {
            @Override
            public void run() {
                SystemUserInfoVo systemUserInfoVo = SpringUtils.getBean(SystemUserInfoService.class).getSystemUserInfoByAccountName(username);
                Ip2regionSearcherImpl ip2regionSearcher  = SpringUtils.getBean(Ip2regionSearcherImpl.class);
                IpInfo ipInfo = ip2regionSearcher.memorySearch(ipAddress);
                String address = ipInfo.getAddressAndIsp();
                String browser = userAgent.getBrowser().getName();
                SystemLoginLog systemLoginLog = new SystemLoginLog();
                systemLoginLog.setUserName(username);
                systemLoginLog.setRequestIp(ipAddress);
                systemLoginLog.setLocation(address);
                systemLoginLog.setBrowser(browser);
                systemLoginLog.setOs(userAgent.getOperatingSystem().getName());
                systemLoginLog.setMessage(message);
                systemLoginLog.setLoginTime(new Date());
                systemLoginLog.setOperateType(operateType);
                if (StringUtils.equalsAny(status, Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER))
                {
                    systemLoginLog.setStatus(Constants.SUCCESS);
                }
                else if (Constants.LOGIN_FAIL.equals(status))
                {
                    systemLoginLog.setStatus(Constants.FAIL);
                }
                if(systemUserInfoVo != null){systemLoginLog.setRealName(systemUserInfoVo.getRealName());}
                SpringUtils.getBean(SystemLoginLogService.class).insertSystemUserLoginLog(systemLoginLog);
                if(systemUserInfoVo != null){
                    SystemUserInfo systemUserInfo = new SystemUserInfo();
                    BeanUtils.copyProperties(systemUserInfoVo, systemUserInfo);
                    systemUserInfo.setLoginTime(new Date());
                    cn.hutool.http.useragent.UserAgent userAgentInfo = UserAgentUtil.parse(finalUserAgentValue);
                    String platform = userAgentInfo.getPlatform().toString();
                    String browserValue = userAgentInfo.getBrowser().toString();//Chrome
                    String version = userAgentInfo.getVersion();//14.0.835.163
                    String engine = userAgentInfo.getEngine().toString();//Webkit
                    String engineVsersion = userAgentInfo.getEngineVersion();//535.1
                    String os = userAgentInfo.getOs().toString();//Windows 7
                    systemUserInfo.setLoginDevice(platform.concat("/").concat(os).concat("/").concat(browserValue)
                            .concat("/").concat(version).concat("/").concat(engine)
                            .concat("/").concat(engineVsersion));
                    SpringUtils.getBean(SystemUserInfoService.class).updateByPrimaryKeySelective(systemUserInfo);
                }
            }
        };
    }

    public static TimerTask saveSystemRequestLog(final SystemRequestLog operLog) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(SystemRequestLogService.class).insertOperlog(operLog);
            }
        };
    }

    public static TimerTask insertSystemExceptionLog(SystemExceptionLog systemExceptionLog) {
        return new TimerTask() {
            @Override
            public void run() {
                systemExceptionLog.setId(SnowflakeIdWorker.getUuid());
                SpringUtils.getBean(SystemExceptionLogService.class).insertSystemExceptionlog(systemExceptionLog);
            }
        };
    }

    public static TimerTask insertSystemPointLog(String userIdValue, String username, String className, String methodName, String params, String result) {
        return new TimerTask() {
            @Override
            public void run() {
                SystemPointLog systemPointLog = new SystemPointLog();
                systemPointLog.setId(SnowflakeIdWorker.getUuid());
                systemPointLog.setClassName(className);
                systemPointLog.setMethodName(methodName);
                systemPointLog.setParams(params);
                systemPointLog.setResult(result);
                systemPointLog.setUserId(userIdValue);
                systemPointLog.setUserName(username);
                systemPointLog.setCreateTime(new Date());
                SpringUtils.getBean(SystemPointLogService.class).insertSystemPointlog(systemPointLog);
            }
        };

    }
    
    public static TimerTask updateProjectTesteeBaseInfoVariableInputValues(String projectId, String testeeId, String operator, String systemTenantId, String systemPlatformId) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(ProjectTesteeInfoService.class).updateProjectTesteeBaseInfoVariableInputValues(projectId, testeeId, operator, systemTenantId, systemPlatformId);
            }
        };
    }

    public static TimerTask updateProjectTesteeFormProcess(String projectId, String projectOrgId, String planId, String visitId, String formId, String formExpandId, String testeeId, String status, String operator, String complateStatus, String tenantId, String platformId) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(ProjectTesteeInfoService.class).updateProjectTesteeFormProcess(projectId, projectOrgId, planId, visitId, formId, formExpandId, testeeId, status, complateStatus,operator, tenantId, platformId);
                SpringUtils.getBean(ProjectTesteeInfoService.class).updateProjectTesteeTableProcess(projectId, projectOrgId, planId, visitId, testeeId, status, operator);
            }
        };
    }

    public static TimerTask initProjectOrgRole(ProjectOrgParam projectOrgParam, Long projectOrgId, String message, String systemLogLogin) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(OrganizationService.class).initProjectOrgRole(projectOrgParam, projectOrgId);
            }
        };
    }
    
    public static TimerTask insertflowFormSetExpandForTemplate(String projectId, String planId, String visitId, String formId, String testeeId, String systemTenantId, String systemPlatformId) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(FlowFormSetService.class).insertflowFormSetExpandForTemplate(projectId, planId, visitId, formId, testeeId, systemTenantId, systemPlatformId);
            }
        };
    }
    
    public static TimerTask insertSendMessageRecord(String messageAccount, String code, String requestId, String responseText) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(SendMessageService.class).insertSendMessageRecord(messageAccount, code, requestId, responseText);
            }
        };
    }
    
    public static TimerTask updateSystemUserValue(SystemUserInfo systemUserInfo) {
        return new TimerTask() {
            @Override
            public void run() {
                SpringUtils.getBean(SystemUserInfoService.class).updateSystemUser(systemUserInfo);
            }
        };
    }
}
