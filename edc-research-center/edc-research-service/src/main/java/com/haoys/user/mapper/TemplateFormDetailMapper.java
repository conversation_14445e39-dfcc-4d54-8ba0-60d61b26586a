package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.TemplateFormDetailExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormTableExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableExportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo;
import com.haoys.user.domain.vo.ecrf.TemplateVariableVo;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormDetailExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TemplateFormDetailMapper {
    long countByExample(TemplateFormDetailExample example);

    int deleteByExample(TemplateFormDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormDetail record);

    int insertSelective(TemplateFormDetail record);

    List<TemplateFormDetail> selectByExample(TemplateFormDetailExample example);

    TemplateFormDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormDetail record, @Param("example") TemplateFormDetailExample example);

    int updateByExample(@Param("record") TemplateFormDetail record, @Param("example") TemplateFormDetailExample example);

    int updateByPrimaryKeySelective(TemplateFormDetail record);

    int updateByPrimaryKey(TemplateFormDetail record);

    /**
     * 通过projectId和visitId查询表单变量名称集合
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param formTableIds
     * @return
     */
    List<TemplateFormVariableExportVo> getProjectFormTableLabelListByProjectIdAndVisitId(@Param("projectId") String projectId, @Param("visitId") String visitId, @Param("formId") String formId,@Param("formDetailId")  String formDetailId,@Param("formTableIds")  String formTableIds);

    TemplateFormDetailVo getTemplateFormVariableIdByName(String projectId, String visitId, String formId, String variableName);

    TemplateFormDetailExcelImportVo getFormDetailByFormNameAndVariableName(String projectId, String visitName, String formName, String variableName);

    /**
     * 根据访视表单名称和variable查询表格变量详情
     * @param projectId
     * @param visitName
     * @param formName
     * @param variableName
     * @return
     */
    TemplateFormTableExcelImportVo getFormTableInfoByFormNameAndVariableName(String projectId, String visitName, String formName, String variableName);

    TemplateFormDetail getTemplateFormCopyVariableByGroupId(Long id);

    List<TemplateFormVariableViewVo> getTemplateCurrentFormVariableList(String projectId, String formId);

    List<TemplateVariableVo> getTemplateVariableDetailConfig();

    List<TemplateFormVariableViewVo> getFormVariableAndRuleList(@Param("projectId") String projectId,@Param("formId")  String formId);
}