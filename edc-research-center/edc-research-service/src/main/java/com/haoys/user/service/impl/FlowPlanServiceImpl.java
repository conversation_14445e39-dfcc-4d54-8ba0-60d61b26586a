package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.enums.ProjectFlowReturnEnums;
import com.haoys.user.mapper.FlowPlanMapper;
import com.haoys.user.mapper.ProjectVisitConfigMapper;
import com.haoys.user.model.*;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectVisitConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 流程配置-创建计划服务实现类
 */
@Service
public class FlowPlanServiceImpl extends BaseService implements FlowPlanService {

    @Autowired
    private FlowPlanMapper flowPlanMapper;
    @Autowired
    private ProjectVisitConfigService visitConfigService;

    @Autowired
    private ProjectVisitConfigMapper projectVisitConfigMapper;

    @Autowired
    private FlowFormSetService formSetService;
    /**
     * 创建计划
     * @param plan 计划信息
     * @return
     */
    @Override
    public CommonResult create(FlowPlan plan) {
        // 根据名称和项目id查询是否已经存在
        FlowPlan plan0 = getFlowPlans(plan.getProjectId(),plan.getPlanName());
        if (plan0==null){
            // 查询最大序号
            plan.setStatus(Integer.parseInt(BusinessConfig.VALID_STATUS));
            plan.setPublishStatus(BusinessConfig.NOT_PUBLISH_STATUS);
            plan.setTenantId(SecurityUtils.getSystemTenantId());
            plan.setPlatformId(SecurityUtils.getSystemPlatformId());
            plan.setId(SnowflakeIdWorker.getUuid());
            int insert = flowPlanMapper.insert(plan);
            if (insert>0){
                return CommonResult.success(plan.getId().toString());
            }else {
                return CommonResult.failed();
            }
        }else {
            return CommonResult.failed(ProjectFlowReturnEnums.E60000);
        }
    }


    @Override
    public CommonResult update(FlowPlan plan) {
        // 根据名称和项目id查询是否已经存在
        FlowPlan plan0 = getFlowPlans(plan.getProjectId(),plan.getPlanName());
        if (plan0!=null && !plan0.getId().equals(plan.getId())){
            return CommonResult.failed(ProjectFlowReturnEnums.E60000);
        }
        FlowPlan flowPlan = flowPlanMapper.selectByPrimaryKey(plan.getId());
        flowPlan.setUpdateUser(plan.getUpdateUser());
        flowPlan.setPlanName(plan.getPlanName());
        flowPlan.setVersion(plan.getVersion());
        int update = flowPlanMapper.updateByPrimaryKey(flowPlan);
        if (update>0){
            return CommonResult.success(null);
        }else {
            return CommonResult.failed();
        }
    }

    @Override
    public CommonResult delete(String planId) {
        FlowPlan flowPlan = flowPlanMapper.selectByPrimaryKey(Long.parseLong(planId));
        if (flowPlan!=null){
            //检查是否存在研究流程
            List<ProjectVisitConfig> list = visitConfigService.getByPlanIdOrName(Long.parseLong(planId), null);
            if (CollectionUtil.isNotEmpty(list)){
                return CommonResult.failed(ProjectFlowReturnEnums.E60001);
            }
            Integer publishStatus = flowPlan.getPublishStatus();
            if (BusinessConfig.ENABLED_STATUS.equals(publishStatus)){
                // 删除提示
                return CommonResult.failed();
            }else {
                flowPlanMapper.deleteByPrimaryKey(flowPlan.getId());
                return CommonResult.success(null);
            }
        }
        return CommonResult.failed();
    }

    /**
     * 获取列表
     * @param projectId 分页和查询参数
     * @return 列表结果
     */
    @Override
    @NoRepeatSubmit
    public List<FlowPlan> getProjectFlowPlanList(Long projectId) {
        FlowPlanExample example = new FlowPlanExample();
        example.setOrderByClause("status asc,update_time desc");
        FlowPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        return flowPlanMapper.selectByExample(example);

    }

    /**
     * 发布计划
     * @param plan 计划id
     * @return 发布结果
     */
    @Override
    public CommonResult publish(FlowPlan plan) {

        // 发布流程
        FlowPlan flowPlan = flowPlanMapper.selectByPrimaryKey(plan.getId());
        if (flowPlan!=null){
            // 校验是否已经存在一个已经发布的计划了
            int  count = flowPlanMapper.countPublish(flowPlan.getProjectId());
            if (count>0){
                return CommonResult.failed(ProjectFlowReturnEnums.E60005);
            }
            // 校验计划下是否有流程

            ProjectVisitConfigExample example0 = new ProjectVisitConfigExample();
            example0.setOrderByClause(" sort asc ");
            ProjectVisitConfigExample.Criteria criteria0 = example0.createCriteria();
            criteria0.andPlanIdEqualTo(flowPlan.getId());
            criteria0.andStatusEqualTo(BusinessConfig.VALID_STATUS);
            List<ProjectVisitConfig> visitConfigs = projectVisitConfigMapper.selectByExample(example0);

            if (CollectionUtil.isEmpty(visitConfigs)){
                return CommonResult.failed(ProjectFlowReturnEnums.E60002);
            }
            for (ProjectVisitConfig config : visitConfigs) {
                // 校验流程下是否已经包含表单
                FlowFormSetExample example = new FlowFormSetExample();
                FlowFormSetExample.Criteria criteria = example.createCriteria();
                criteria.andProjectIdEqualTo(config.getProjectId());
                criteria.andVisitIdEqualTo(config.getId());
                Long aLong = formSetService.countByExample(example);
                if (aLong==0){
                    return CommonResult.failed(ProjectFlowReturnEnums.E60006);
                }
            }

            flowPlan.setPublishStatus(BusinessConfig.PUBLISH_STATUS);
            flowPlan.setVersion(plan.getVersion());
            flowPlan.setOrgId(plan.getOrgId());
            flowPlan.setPublishDate(new Date());
            flowPlanMapper.updateByPrimaryKey(flowPlan);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    @Override
    public CommonResult<Object> unPublish(Long id) {
        // 撤销流程
        FlowPlan flowPlan = flowPlanMapper.selectByPrimaryKey(id);
        if (flowPlan!=null){
            flowPlan.setPublishStatus(BusinessConfig.NOT_PUBLISH_STATUS);
            flowPlan.setUpdateTime(new Date());
            flowPlanMapper.updateByPrimaryKey(flowPlan);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    /**
     * 初始化一条计划数据
     * @return
     */
    @Override
    public int initPlan(Long projectId) {
        FlowPlanExample example = new FlowPlanExample();
        FlowPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        long count = flowPlanMapper.countByExample(example);
        if (count==0){
            FlowPlan flowPlan = new FlowPlan();
            flowPlan.setPlanName("方案1.0");
            flowPlan.setProjectId(projectId);
            flowPlan.setOrgId(0L);
            flowPlan.setId(SnowflakeIdWorker.getUuid());
            flowPlan.setStatus(Integer.parseInt(BusinessConfig.VALID_STATUS));
            flowPlan.setPublishStatus(BusinessConfig.NOT_PUBLISH_STATUS);
            flowPlan.setTenantId(SecurityUtils.getSystemTenantId());
            flowPlan.setPlatformId(SecurityUtils.getSystemPlatformId());
            flowPlan.setXhNum(1);
            flowPlan.setCreateTime(new Date());
            return flowPlanMapper.insert(flowPlan);
        }
        return 1;
    }

    /**
     * 根据项目id 获取已经发布的计划
     * @param projectId 项目id
     * @return 计划信息
     */
    @Override
    public FlowPlan getPlanByProjectId(String projectId) {
        FlowPlanExample example = new FlowPlanExample();
        example.setOrderByClause("xh_num asc");
        FlowPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPublishStatusEqualTo(BusinessConfig.PUBLISH_STATUS);
        criteria.andStatusEqualTo(BusinessConfig.ENABLED_STATUS);
        List<FlowPlan> flowPlans = flowPlanMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(flowPlans)){
            return null;
        }
        return flowPlans.get(0);
    }

    @Override
    public FlowPlan getFlowPlanInfoByPlanId(String planId) {
        return flowPlanMapper.selectByPrimaryKey(Long.parseLong(planId));
    }

    @Override
    public CommonResult<Object> isHavPlan(Long projectId) {
        FlowPlan flowPlan = this.getPlanByProjectId(projectId + "");
        if (flowPlan!=null){
            return CommonResult.success(null);
        }
        return CommonResult.failed(ProjectFlowReturnEnums.E30003);
    }


    /**
     * 根据
     * @param projectId 项目id
     * @param planName 计划名称
     * @return
     */
    private FlowPlan getFlowPlans(Long projectId ,String planName) {
        FlowPlanExample example = new FlowPlanExample();
        FlowPlanExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andPlanNameEqualTo(planName);
        List<FlowPlan> flowPlans = flowPlanMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(flowPlans)){
            return null;
        }
        return flowPlans.get(0);
    }
}
