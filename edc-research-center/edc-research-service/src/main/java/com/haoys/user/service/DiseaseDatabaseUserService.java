package com.haoys.user.service;

import com.haoys.user.model.DiseaseDatabaseAuth;
import com.haoys.user.model.DiseaseDatabaseUser;

import java.util.Date;
import java.util.List;

public interface DiseaseDatabaseUserService {

    DiseaseDatabaseUser getDiseaseDatabaseUser(String dataBaseId, String userId);

    void saveDiseaseDatabaseUser(String databaseId, String databaseName, Date validateStartDate, Date validateEndDate, String systemUserId);

    void deleteDiseaseDatabaseUser(String id);

    void deleteDiseaseDatabaseUserByUserId(String systemUserId);

    /**
     * 查询系统用户数据库授权列表
     * @param systemUserId
     * @return
     */
    List<DiseaseDatabaseUser> getDiseaseDatabaseListByUserId(String systemUserId);


    DiseaseDatabaseAuth getDiseaseDatabaseAuth(String userId);

    void saveDiseaseDatabaseAuth(String userId, Boolean ownerTotalAuth);
}
