package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableCountVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo;
import com.haoys.user.model.ProjectTesteeTable;
import com.haoys.user.model.ProjectTesteeTableExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeTableMapper {
    long countByExample(ProjectTesteeTableExample example);

    int deleteByExample(ProjectTesteeTableExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeTable record);

    int insertSelective(ProjectTesteeTable record);

    List<ProjectTesteeTable> selectByExample(ProjectTesteeTableExample example);

    ProjectTesteeTable selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeTable record, @Param("example") ProjectTesteeTableExample example);

    int updateByExample(@Param("record") ProjectTesteeTable record, @Param("example") ProjectTesteeTableExample example);

    int updateByPrimaryKeySelective(ProjectTesteeTable record);

    int updateByPrimaryKey(ProjectTesteeTable record);

    List<ProjectTesteeTableWrapperVo> getProjectTesteeTableListForPage(Map<String, Object> params);

    List<ProjectTesteeTableWrapperVo> getProjectTesteeGroupTableListForPage(Map<String, Object> params);


    /**
     * 查询表格行记录总数
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param formDetailId
     * @param testeeId
     * @param queryFormGroup
     * @param taskDate
     * @param tableSort
     * @param groupId
     * @return
     */
    List<Long> getProjectTesteeTableRowNumber(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId, String queryFormGroup, String taskDate, String tableSort, String groupId);

    List<ProjectTesteeTableVo> getProjectTesteeTableRowFieldRecord(@Param("projectId") String projectId, @Param("tableId") String tableId);

    /**
     * 查询表格行记录
     * @param params
     * @return
     */
    List<ProjectTesteeTableVo> getProjectTesteeTableRowRecord(Map<String, Object> params);

    /**
     * 查询字段组表格行记录
     * @param params
     * @return
     */
    List<ProjectTesteeTableVo> getProjectTesteeGroupTableRowRecord(Map<String, Object> params);

    /**
     * 批量插入表格记录
     * @param tableHeadRowInfoList
     */
    int saveBatchProjectTesteeTableRowRecord(List<ProjectTesteeTable> tableHeadRowInfoList);

    /**
     * 导出参与者表格提交数据
     *
     * @param projectId
     * @param projectOrgId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param formTableId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeTableResultExportVo> getProjectTesteeTableRecordsWithVariableIds(@Param("projectId") String projectId,
                                                                                       @Param("orgId") String projectOrgId, @Param("visitId") String visitId,
                                                                                       @Param("formId") String formId,
                                                                                       @Param("formDetailId") String formDetailId,
                                                                                       @Param("formTableId") String formTableId,
                                                                                       @Param("testeeId") String testeeId);

    /**
     * 查询参与者访视表单和表格提交数据
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param formTableId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeFormAndTableResultExportVo> getProjectTesteeFormAndTableResultByVariableIds(String projectId, String visitId, String formId, String formDetailId, String formTableId, String testeeId, String orgId);

    /**
     * 查询参与者当前访视下所有表格汇总
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeFormAndTableCountVo> getProjectTesteeFormAndTableCountByVariableIds(String projectId, String visitId, String formId, String formDetailId, String testeeId, String orgId);


    /**
     * 获取题组的表单变量值和表格变量值信息
     * @param projectId
     * @param visitId
     * @return
     */
    List<ProjectTesteeTableResultExportVo> getProjectTesteeGroupVariableAndTable(
            @Param("projectId") String projectId
            , @Param("visitId")String visitId
            , @Param("testeeId")String testeeId
            , @Param("fromId")String fromId
            , @Param("orgId")String orgId
    );

    /**
     * 根据题组的分组id进行删除
     * @param groupId 分组id
     * @param status 状态
     * @param userId 更新人的id
     * @return 删除结果
     */
    int deleteByGroupId(@Param("groupId") String groupId,@Param("status") String status,@Param("userId") String userId);

    List<ProjectTesteeTableResultExportVo> getProjectTesteeGroupTableResult(@Param("projectId") String projectId,
                                                                            @Param("visitId") String visitId,
                                                                            @Param("fromId") String fromId,
                                                                            @Param("formTableId") String formTableId,
                                                                            @Param("testeeId") String testeeId,
                                                                            @Param("orgId") String orgId);






}