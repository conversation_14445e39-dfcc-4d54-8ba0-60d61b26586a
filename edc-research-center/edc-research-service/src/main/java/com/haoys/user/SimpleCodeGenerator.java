package com.haoys.user;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.InputStream;
import java.sql.*;
import java.util.*;

/**
 * 简化的代码生成器
 * 避免SLF4J冲突，直接生成ProjectConfigModule相关代码
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class SimpleCodeGenerator {

    private static final String GENERATOR_PROPERTIES = "src/main/resources/generator.properties";
    private static final String GENERATOR_CONFIG_XML = "src/main/resources/generatorMysqlConfig.xml";

    public static void main(String[] args) throws Exception {
        System.out.println("=== 简化代码生成器启动 ===");

        try {
            // 从XML配置文件读取要生成的表名列表
            List<String> tableNames = readTableNamesFromConfig();
            
            if (tableNames.isEmpty()) {
                System.out.println("未在配置文件中找到要生成的表，使用默认表名: project_config_module");
                tableNames.add("project_config_module");
            }

            // 加载数据库配置
            Properties dbProps = loadDatabaseProperties();

            // 为每个表生成代码
            for (String tableName : tableNames) {
                System.out.println("开始生成表 " + tableName + " 的代码...");
                generateCodeForTable(tableName, dbProps);
                System.out.println("表 " + tableName + " 代码生成完成");
            }

            System.out.println("=== 所有代码生成完成 ===");

        } catch (Exception e) {
            System.err.println("代码生成失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 从XML配置文件读取要生成的表名列表
     */
    private static List<String> readTableNamesFromConfig() {
        List<String> tableNames = new ArrayList<>();
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(GENERATOR_CONFIG_XML);
            
            NodeList tableNodes = document.getElementsByTagName("table");
            for (int i = 0; i < tableNodes.getLength(); i++) {
                Element tableElement = (Element) tableNodes.item(i);
                String tableName = tableElement.getAttribute("tableName");
                if (tableName != null && !tableName.trim().isEmpty() && !tableName.contains("%")) {
                    tableNames.add(tableName.trim());
                    System.out.println("从配置文件读取到表名: " + tableName);
                }
            }
        } catch (Exception e) {
            System.err.println("读取XML配置文件失败: " + e.getMessage());
        }
        return tableNames;
    }

    /**
     * 从properties文件读取数据库配置
     */
    private static Properties loadDatabaseProperties() {
        Properties props = new Properties();
        try (InputStream input = new FileInputStream(GENERATOR_PROPERTIES)) {
            props.load(input);
            System.out.println("成功加载数据库配置文件");
        } catch (Exception e) {
            System.err.println("加载数据库配置文件失败: " + e.getMessage());
            // 使用默认配置
            props.setProperty("jdbc.driverClass", "com.mysql.cj.jdbc.Driver");
            props.setProperty("jdbc.connectionURL", "********************************************************************************************************************");
            props.setProperty("jdbc.userId", "root");
            props.setProperty("jdbc.password", "Asd123456##");
        }
        return props;
    }

    /**
     * 为指定表生成代码
     */
    private static void generateCodeForTable(String tableName, Properties dbProps) throws Exception {
        // 获取表信息
        TableMetadata tableMetadata = getTableMetadata(tableName, dbProps);
        
        // 生成各种文件
        generateEntity(tableMetadata);
        generateMapper(tableMetadata);
        generateMapperXml(tableMetadata);
        generateService(tableMetadata);
        generateServiceImpl(tableMetadata);
        generateTest(tableMetadata);
    }

    /**
     * 获取表元数据
     */
    private static TableMetadata getTableMetadata(String tableName, Properties dbProps) throws Exception {
        TableMetadata metadata = new TableMetadata();
        metadata.tableName = tableName;
        metadata.entityName = convertToEntityName(tableName);
        metadata.columns = new ArrayList<>();

        String url = dbProps.getProperty("jdbc.connectionURL");
        String username = dbProps.getProperty("jdbc.userId");
        String password = dbProps.getProperty("jdbc.password");

        try (Connection conn = DriverManager.getConnection(url, username, password)) {
            DatabaseMetaData dbMetaData = conn.getMetaData();
            
            // 获取列信息
            try (ResultSet rs = dbMetaData.getColumns(null, null, tableName, null)) {
                while (rs.next()) {
                    ColumnMetadata column = new ColumnMetadata();
                    column.columnName = rs.getString("COLUMN_NAME");
                    column.dataType = rs.getInt("DATA_TYPE");
                    column.typeName = rs.getString("TYPE_NAME");
                    column.columnSize = rs.getInt("COLUMN_SIZE");
                    column.nullable = "YES".equals(rs.getString("IS_NULLABLE"));
                    column.remarks = rs.getString("REMARKS");
                    column.javaProperty = convertToJavaProperty(column.columnName);
                    column.javaType = getJavaType(column.dataType, column.typeName);
                    
                    metadata.columns.add(column);
                }
            }
            
            // 获取主键信息
            try (ResultSet rs = dbMetaData.getPrimaryKeys(null, null, tableName)) {
                if (rs.next()) {
                    metadata.primaryKey = rs.getString("COLUMN_NAME");
                }
            }
        }

        return metadata;
    }

    /**
     * 将表名转换为实体类名
     */
    private static String convertToEntityName(String tableName) {
        if (tableName == null || tableName.isEmpty()) {
            return tableName;
        }

        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (char c : tableName.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }

        return result.toString();
    }

    /**
     * 将列名转换为Java属性名
     */
    private static String convertToJavaProperty(String columnName) {
        if (columnName == null || columnName.isEmpty()) {
            return columnName;
        }

        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;

        for (char c : columnName.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }

        return result.toString();
    }

    /**
     * 获取Java类型
     */
    private static String getJavaType(int sqlType, String typeName) {
        switch (sqlType) {
            case Types.TINYINT:
            case Types.SMALLINT:
            case Types.INTEGER:
                return "Integer";
            case Types.BIGINT:
                return "Long";
            case Types.FLOAT:
                return "Float";
            case Types.DOUBLE:
            case Types.DECIMAL:
            case Types.NUMERIC:
                return "Double";
            case Types.BOOLEAN:
            case Types.BIT:
                return "Boolean";
            case Types.CHAR:
            case Types.VARCHAR:
            case Types.LONGVARCHAR:
                return "String";
            case Types.DATE:
            case Types.TIME:
            case Types.TIMESTAMP:
                return "Date";
            case Types.BLOB:
            case Types.CLOB:
                return "String";
            default:
                if ("TEXT".equalsIgnoreCase(typeName) || 
                    "LONGTEXT".equalsIgnoreCase(typeName) ||
                    "MEDIUMTEXT".equalsIgnoreCase(typeName)) {
                    return "String";
                }
                return "String";
        }
    }

    // 内部类定义
    static class TableMetadata {
        String tableName;
        String entityName;
        String primaryKey;
        List<ColumnMetadata> columns;
    }

    static class ColumnMetadata {
        String columnName;
        int dataType;
        String typeName;
        int columnSize;
        boolean nullable;
        String remarks;
        String javaProperty;
        String javaType;
    }

    // 生成方法将在下一部分实现
    private static void generateEntity(TableMetadata metadata) throws Exception {
        // 实现将在后续添加
        System.out.println("生成实体类: " + metadata.entityName);
    }

    private static void generateMapper(TableMetadata metadata) throws Exception {
        System.out.println("生成Mapper接口: " + metadata.entityName + "Mapper");
    }

    private static void generateMapperXml(TableMetadata metadata) throws Exception {
        System.out.println("生成Mapper XML: " + metadata.entityName + "Mapper.xml");
    }

    private static void generateService(TableMetadata metadata) throws Exception {
        System.out.println("生成Service接口: " + metadata.entityName + "Service");
    }

    private static void generateServiceImpl(TableMetadata metadata) throws Exception {
        System.out.println("生成Service实现: " + metadata.entityName + "ServiceImpl");
    }

    private static void generateTest(TableMetadata metadata) throws Exception {
        System.out.println("生成测试类: " + metadata.entityName + "ServiceTest");
    }
}
