package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.PatientTaskSubmitParam;
import com.haoys.user.domain.vo.patient.PatientTaskCalendarDetailVo;
import com.haoys.user.domain.vo.patient.PatientTaskFormDetailVo;
import com.haoys.user.domain.vo.patient.PatientTaskFormValueVo;
import com.haoys.user.domain.vo.patient.PatientTaskSummaryVo;
import com.haoys.user.domain.vo.patient.ProjectPatientBindResultVo;

public interface ProjectPatientService {


    /**
     * 患者提交方案结果
     * @param patientTaskSubmitParam
     * @return
     */
    CustomResult savePatientTaskData(PatientTaskSubmitParam patientTaskSubmitParam);

    /**
     * 查询患者方案
     * @param projectId
     * @param testeeId
     * @return
     */
    PatientTaskFormValueVo getPatientTaskDataList(String projectId, String testeeId);

    /**
     * 患者端方案详情
     * @param projectId
     * @param testeeId
     * @param planId
     * @param taskId
     * @return
     */
    PatientTaskFormDetailVo getPatientTaskFormDetail(String projectId, String testeeId, String planId, String taskId, String taskDate);


    /**
     * 查询H5页面患者任务日历详情
     * @param projectId         项目id
     * @param testeeId          患者id
     * @param planId            方案id
     * @param taskDateTime      任务查询时间
     * @return
     */
    PatientTaskCalendarDetailVo getPatientTaskCalendarDetail(String projectId, String testeeId, String planId, String taskDateTime);

    /**
     * 查询H5页面患者任务汇总
     * @param projectId
     * @param testeeId
     * @param planId
     * @return
     */
    PatientTaskSummaryVo getPatientTaskSummary(String projectId, String testeeId, String planId);

    /**
     * 查询患者端指定日期的任务日历详情
     * @param projectId
     * @param testeeId
     * @param planId
     * @param taskDate
     * @return
     */
    PatientTaskFormValueVo getPatientTaskCalendarDetailByTaskDate(String projectId, String testeeId, String planId, String taskDate);

    /**
     * 查询患者绑定结果
     * @param projectId
     * @param mobile
     * @return
     */
    ProjectPatientBindResultVo getProjectPatientBindResult(String projectId, String mobile);

    /**
     * 通过手机号查询绑定项目id
     * @param mobile
     * @return
     */
    String getPatientTaskProjectId(String mobile);
}
