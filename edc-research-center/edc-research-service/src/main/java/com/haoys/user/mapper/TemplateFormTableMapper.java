package com.haoys.user.mapper;

import com.haoys.user.model.TemplateFormTable;
import com.haoys.user.model.TemplateFormTableExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateFormTableMapper {
    long countByExample(TemplateFormTableExample example);

    int deleteByExample(TemplateFormTableExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormTable record);

    int insertSelective(TemplateFormTable record);

    List<TemplateFormTable> selectByExample(TemplateFormTableExample example);

    TemplateFormTable selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormTable record, @Param("example") TemplateFormTableExample example);

    int updateByExample(@Param("record") TemplateFormTable record, @Param("example") TemplateFormTableExample example);

    int updateByPrimaryKeySelective(TemplateFormTable record);

    int updateByPrimaryKey(TemplateFormTable record);
}