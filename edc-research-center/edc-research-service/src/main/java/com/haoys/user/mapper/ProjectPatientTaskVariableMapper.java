package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPatientTaskVariable;
import com.haoys.user.model.ProjectPatientTaskVariableExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPatientTaskVariableMapper {
    long countByExample(ProjectPatientTaskVariableExample example);

    int deleteByExample(ProjectPatientTaskVariableExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPatientTaskVariable record);

    int insertSelective(ProjectPatientTaskVariable record);

    List<ProjectPatientTaskVariable> selectByExample(ProjectPatientTaskVariableExample example);

    ProjectPatientTaskVariable selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPatientTaskVariable record, @Param("example") ProjectPatientTaskVariableExample example);

    int updateByExample(@Param("record") ProjectPatientTaskVariable record, @Param("example") ProjectPatientTaskVariableExample example);

    int updateByPrimaryKeySelective(ProjectPatientTaskVariable record);

    int updateByPrimaryKey(ProjectPatientTaskVariable record);
}