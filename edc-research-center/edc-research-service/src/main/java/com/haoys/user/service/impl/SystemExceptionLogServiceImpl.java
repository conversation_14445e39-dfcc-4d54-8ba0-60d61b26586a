package com.haoys.user.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.domain.vo.system.SystemExceptionLogVo;
import com.haoys.user.mapper.SystemExceptionLogMapper;
import com.haoys.user.model.SystemExceptionLog;
import com.haoys.user.service.SystemExceptionLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemExceptionLogServiceImpl extends BaseService implements SystemExceptionLogService {

    @Autowired
    private SystemExceptionLogMapper systemExceptionLogMapper;

    @Override
    public void insertSystemExceptionlog(SystemExceptionLog systemExceptionLog) {
        systemExceptionLogMapper.insert(systemExceptionLog);
    }
    
    @Override
    public CommonPage<SystemExceptionLogVo> getSystemExceptionLogList(String userName, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<SystemExceptionLogVo> systemExceptionLogList = systemExceptionLogMapper.getSystemExceptionLogList(userName, pageNum, pageSize);
        return commonPageListWrapper(pageNum, pageSize, page, systemExceptionLogList);
    }
    
    @Override
    public SystemExceptionLog getSystemExceptionLog(String id) {
        return systemExceptionLogMapper.selectByPrimaryKey(NumberUtil.parseLong(id));
    }
}
