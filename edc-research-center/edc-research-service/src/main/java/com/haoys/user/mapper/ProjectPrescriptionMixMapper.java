package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPrescriptionMix;
import com.haoys.user.model.ProjectPrescriptionMixExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPrescriptionMixMapper {
    long countByExample(ProjectPrescriptionMixExample example);

    int deleteByExample(ProjectPrescriptionMixExample example);

    int insert(ProjectPrescriptionMix record);

    int insertSelective(ProjectPrescriptionMix record);

    List<ProjectPrescriptionMix> selectByExampleWithBLOBs(ProjectPrescriptionMixExample example);

    List<ProjectPrescriptionMix> selectByExample(ProjectPrescriptionMixExample example);

    int updateByExampleSelective(@Param("record") ProjectPrescriptionMix record, @Param("example") ProjectPrescriptionMixExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectPrescriptionMix record, @Param("example") ProjectPrescriptionMixExample example);

    int updateByExample(@Param("record") ProjectPrescriptionMix record, @Param("example") ProjectPrescriptionMixExample example);
}