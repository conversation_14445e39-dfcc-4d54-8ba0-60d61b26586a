package com.haoys.user.service.impl;

import com.haoys.user.domain.param.testee.ProjectTesteeStatistVo;
import com.haoys.user.mapper.ProjectVisitUserMapper;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.ProjectVisitUserExample;
import com.haoys.user.service.ProjectVisitUserService;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectVisitUserServiceImpl implements ProjectVisitUserService {

    private final ProjectVisitUserMapper projectVisitUserMapper;

    @Override
    public void insert(ProjectVisitUser projectVisitUser) {
        projectVisitUserMapper.insert(projectVisitUser);
    }

    @Override
    public void updateProjectVisitUserById(ProjectVisitUser projectVisitUserInfo) {
        ProjectVisitUserExample example = new ProjectVisitUserExample();
        ProjectVisitUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectVisitUserInfo.getProjectId());
        criteria.andTesteeIdEqualTo(projectVisitUserInfo.getTesteeId());
        projectVisitUserMapper.updateByExampleSelective(projectVisitUserInfo, example);
    }

    @Override
    public void updateByExample(ProjectVisitUser projectVisitUser, ProjectVisitUserExample example) {
        projectVisitUserMapper.updateByExample(projectVisitUser, example);
    }

    @Override
    public void updateByExampleSelective(ProjectVisitUser projectVisitUser, ProjectVisitUserExample example) {
        projectVisitUserMapper.updateByExampleSelective(projectVisitUser, example);
    }

    @Override
    public long countByExample(ProjectVisitUserExample example) {
        return projectVisitUserMapper.countByExample(example);
    }

    @Override
    public List<ProjectVisitUser> selectByExample(ProjectVisitUserExample example) {
        return projectVisitUserMapper.selectByExample(example);
    }

    @Override
    public void deleteByExample(ProjectVisitUserExample example) {
        projectVisitUserMapper.deleteByExample(example);
    }


    /**
     * 统计筛选中，筛选未通过，研究中，研究结束。
     * @param projectId 项目id
     * @param orgId 研究中心id
     * @return 统计结果
     */
    @Override
    public ProjectTesteeStatistVo testeeStatist(Long projectId, Long orgId) {
        return projectVisitUserMapper.testeeStatist(projectId,orgId);
    }
    /**
     * 查询参与者某一日的数量
     * @param projectId 项目id
     * @param orgId 研究中心id
     * @return 统计结果
     */
    @Override
    public Integer testeeStatistByDate(Long projectId, Long orgId,String createTime) {
        return projectVisitUserMapper.testeeStatistByDate(projectId,orgId,createTime);

    }

    @Override
    public ProjectVisitUser getProjectVisitUserInfo(String projectId, String ownerOrgId, String testeeId) {
        return projectVisitUserMapper.getProjectVisitUserInfo(projectId, ownerOrgId, testeeId);
    }

    @Override
    public  String getMaxtesteeCode(@Param("projectId") Long projectId, @Param("orgId") Long orgId, @Param("prefix") String prefix){
        return projectVisitUserMapper.getMaxtesteeCode(projectId, orgId, prefix);

    }

    @Override
    public ProjectVisitUser getMobileProjectVisitUser(String projectId, String ownerOrgId, String testeeId) {
        return projectVisitUserMapper.getMobileProjectVisitUser(projectId, ownerOrgId, testeeId);
    }
    
    @Override
    public ProjectVisitUser getProjectTesteeUserByUserId(String projectId, String userId, String tenantId, String platformId) {
        return projectVisitUserMapper.getProjectTesteeUserByUserId(projectId, userId, tenantId, platformId);
    }
    
    @Override
    public ProjectVisitUser getProjectDeleteVisitUserInfo(String projectId, String ownerOrgId, String testeeId) {
        return projectVisitUserMapper.getProjectDeleteVisitUserInfo(projectId, ownerOrgId, testeeId);
    }
    
    @Override
    public String getMaxTesteeCodeForBoRui(Long projectId) {
        return projectVisitUserMapper.getMaxTesteeCodeForBoRui(projectId);
    }
    
}
