package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.vo.auth.ProjectMenuVo;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.domain.vo.auth.ProjectUserRoleVo;
import com.haoys.user.model.*;

import java.util.List;

/**
 * 项目角色业务层
 */
public interface ProjectRoleService {

    List<ProjectRole> selectByExample(ProjectRoleExample example);

    int insertSelective(ProjectRole record);

    int updateByPrimaryKeySelective(ProjectRole record);

    int deleteByExample(ProjectRoleExample example);

    ProjectRole selectByPrimaryKey(Long id);

    /**
     * 根据条件分页查询项目角色数据
     * @param projectRoleQuery 项目角色信息
     * @return 项目角色数据集合信息
     */
    List<ProjectRoleQuery> selectProjectRoleList(ProjectRoleQuery projectRoleQuery);


    List<ProjectRoleVo> getProjectRoleListForCombobox(ProjectRoleQuery projectRoleQuery);

    /**
     * 初始化项目角色
     *
     * @param projectId
     * @param userId
     * @param roleId
     * @param systemTenantId
     * @param systemPlatformId
     */
    void insertProjectUserRole(String projectId, String userId, String roleId, String systemTenantId, String systemPlatformId);

    /**
     * 初始化项目角色权限
     * @param roleId
     */
    void insertProjectRoleMenu(Long roleId);


    /**
     * 新增保存项目角色信息
     * @param projectRoleQuery 项目角色信息
     * @return 结果
     */
    CommonResult insertProjectRole(ProjectRoleQuery projectRoleQuery);

    /**
     * 修改保存项目角色信息
     * @param projectRoleQuery 项目角色信息
     * @return 结果
     */
    CommonResult updateProjectRole(ProjectRoleQuery projectRoleQuery);

    /**
     * 通过项目角色ID删除角色
     * @param roleId 项目角色ID
     * @return 结果
     */
    CommonResult deleteProjectRoleById(Long roleId);


    /**
     * 校验项目角色名称是否唯一
     * @param role 角色信息
     * @return 结果
     */
    ProjectRole checkProjectRoleNameUnique(ProjectRoleQuery role);

    /**
     * 通过项目角色ID查询角色使用数量
     * @param roleId 角色ID
     * @return 结果
     */
    int countProjectRoleByRoleId(Long roleId);


    /**
     * 根据项目用户角色
     * @param projectId
     * @param userId
     * @return
     */
    List<ProjectRoleVo> getProjectRoleListByUserId(String projectId, String userId);


    /**
     * 查询项目用户研究中心角色
     * @param projectId
     * @param orgId
     * @param userId
     * @return
     */
    List<ProjectRoleVo> getProjectOrgRoleListByUserId(String projectId, String orgId, String userId);


    /**
     * 初始化中心角色信息
     * @param projectId      项目ID
     * @param templateStatus
     * @param createUserId
     * @return
     */
    CustomResult initSystemProjectRoleInfo(String projectId, String templateStatus, String createUserId);

    /**
     * 查询项目初始化角色信息
     * @param projectId
     * @return
     */
    ProjectRoleVo getProjectLeaderRoleInfo(Long projectId);

    /**
     * 停用/启用角色
     * @param projectRoleQuery 角色id和状态
     * @return
     */
    CommonResult updateProjectRoleStatus(ProjectRoleQuery projectRoleQuery);

    /**
     * 根据id进行获取
     * @param roleId 角色id
     * @return
     */
    ProjectRoleQuery getProjectRoleByRoleId(Long roleId);

    /**
     * 查询项目管理员角色信息
     * @param projectId
     * @param systemUserId
     * @param roleId
     * @return
     */
    ProjectUserRoleVo getProjectRoleListByProjectIdAndUserId(String projectId, String systemUserId, String roleId);

    /**
     * 删除用户项目角色
     * @param projectId
     * @param userId
     */
    void deleteProjectUserRole(String projectId, String userId);

    /**
     * 查询模版菜单
     * @param systemProjectMenuTemplateId
     * @param ename
     * @return
     */
    List<ProjectMenuVo> selectProjectTemplateMenuInfo(String systemProjectMenuTemplateId, String ename);

    ProjectRole getProjectTemplateRoleInfoByRoleCode(String projectRoleCode);
    
    ProjectRole getProjectManageRoleInfoByProjectRoleCode(String projectId, String code, String tenantId);
}
