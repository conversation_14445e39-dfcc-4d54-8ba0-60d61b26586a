package com.haoys.user.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectTesteeTableExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectTesteeTableExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdIsNull() {
            addCriterion("form_expand_id is null");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdIsNotNull() {
            addCriterion("form_expand_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdEqualTo(Long value) {
            addCriterion("form_expand_id =", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdNotEqualTo(Long value) {
            addCriterion("form_expand_id <>", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdGreaterThan(Long value) {
            addCriterion("form_expand_id >", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_expand_id >=", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdLessThan(Long value) {
            addCriterion("form_expand_id <", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdLessThanOrEqualTo(Long value) {
            addCriterion("form_expand_id <=", value, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdIn(List<Long> values) {
            addCriterion("form_expand_id in", values, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdNotIn(List<Long> values) {
            addCriterion("form_expand_id not in", values, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdBetween(Long value1, Long value2) {
            addCriterion("form_expand_id between", value1, value2, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormExpandIdNotBetween(Long value1, Long value2) {
            addCriterion("form_expand_id not between", value1, value2, "formExpandId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNull() {
            addCriterion("form_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNotNull() {
            addCriterion("form_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdEqualTo(Long value) {
            addCriterion("form_detail_id =", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotEqualTo(Long value) {
            addCriterion("form_detail_id <>", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThan(Long value) {
            addCriterion("form_detail_id >", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_detail_id >=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThan(Long value) {
            addCriterion("form_detail_id <", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("form_detail_id <=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIn(List<Long> values) {
            addCriterion("form_detail_id in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotIn(List<Long> values) {
            addCriterion("form_detail_id not in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdBetween(Long value1, Long value2) {
            addCriterion("form_detail_id between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("form_detail_id not between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNull() {
            addCriterion("form_table_id is null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNotNull() {
            addCriterion("form_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdEqualTo(Long value) {
            addCriterion("form_table_id =", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotEqualTo(Long value) {
            addCriterion("form_table_id <>", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThan(Long value) {
            addCriterion("form_table_id >", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_table_id >=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThan(Long value) {
            addCriterion("form_table_id <", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThanOrEqualTo(Long value) {
            addCriterion("form_table_id <=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIn(List<Long> values) {
            addCriterion("form_table_id in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotIn(List<Long> values) {
            addCriterion("form_table_id not in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdBetween(Long value1, Long value2) {
            addCriterion("form_table_id between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotBetween(Long value1, Long value2) {
            addCriterion("form_table_id not between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNull() {
            addCriterion("testee_id is null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNotNull() {
            addCriterion("testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdEqualTo(Long value) {
            addCriterion("testee_id =", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotEqualTo(Long value) {
            addCriterion("testee_id <>", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThan(Long value) {
            addCriterion("testee_id >", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testee_id >=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThan(Long value) {
            addCriterion("testee_id <", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThanOrEqualTo(Long value) {
            addCriterion("testee_id <=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIn(List<Long> values) {
            addCriterion("testee_id in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotIn(List<Long> values) {
            addCriterion("testee_id not in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdBetween(Long value1, Long value2) {
            addCriterion("testee_id between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotBetween(Long value1, Long value2) {
            addCriterion("testee_id not between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdIsNull() {
            addCriterion("resource_variable_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdIsNotNull() {
            addCriterion("resource_variable_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdEqualTo(Long value) {
            addCriterion("resource_variable_id =", value, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdNotEqualTo(Long value) {
            addCriterion("resource_variable_id <>", value, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdGreaterThan(Long value) {
            addCriterion("resource_variable_id >", value, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_variable_id >=", value, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdLessThan(Long value) {
            addCriterion("resource_variable_id <", value, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_variable_id <=", value, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdIn(List<Long> values) {
            addCriterion("resource_variable_id in", values, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdNotIn(List<Long> values) {
            addCriterion("resource_variable_id not in", values, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdBetween(Long value1, Long value2) {
            addCriterion("resource_variable_id between", value1, value2, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceVariableIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_variable_id not between", value1, value2, "resourceVariableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdIsNull() {
            addCriterion("resource_table_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdIsNotNull() {
            addCriterion("resource_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdEqualTo(Long value) {
            addCriterion("resource_table_id =", value, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdNotEqualTo(Long value) {
            addCriterion("resource_table_id <>", value, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdGreaterThan(Long value) {
            addCriterion("resource_table_id >", value, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_table_id >=", value, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdLessThan(Long value) {
            addCriterion("resource_table_id <", value, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_table_id <=", value, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdIn(List<Long> values) {
            addCriterion("resource_table_id in", values, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdNotIn(List<Long> values) {
            addCriterion("resource_table_id not in", values, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdBetween(Long value1, Long value2) {
            addCriterion("resource_table_id between", value1, value2, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andResourceTableIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_table_id not between", value1, value2, "resourceTableId");
            return (Criteria) this;
        }

        public Criteria andRowNoIsNull() {
            addCriterion("row_no is null");
            return (Criteria) this;
        }

        public Criteria andRowNoIsNotNull() {
            addCriterion("row_no is not null");
            return (Criteria) this;
        }

        public Criteria andRowNoEqualTo(Long value) {
            addCriterion("row_no =", value, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoNotEqualTo(Long value) {
            addCriterion("row_no <>", value, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoGreaterThan(Long value) {
            addCriterion("row_no >", value, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoGreaterThanOrEqualTo(Long value) {
            addCriterion("row_no >=", value, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoLessThan(Long value) {
            addCriterion("row_no <", value, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoLessThanOrEqualTo(Long value) {
            addCriterion("row_no <=", value, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoIn(List<Long> values) {
            addCriterion("row_no in", values, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoNotIn(List<Long> values) {
            addCriterion("row_no not in", values, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoBetween(Long value1, Long value2) {
            addCriterion("row_no between", value1, value2, "rowNo");
            return (Criteria) this;
        }

        public Criteria andRowNoNotBetween(Long value1, Long value2) {
            addCriterion("row_no not between", value1, value2, "rowNo");
            return (Criteria) this;
        }

        public Criteria andLabelIsNull() {
            addCriterion("label is null");
            return (Criteria) this;
        }

        public Criteria andLabelIsNotNull() {
            addCriterion("label is not null");
            return (Criteria) this;
        }

        public Criteria andLabelEqualTo(String value) {
            addCriterion("label =", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotEqualTo(String value) {
            addCriterion("label <>", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThan(String value) {
            addCriterion("label >", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelGreaterThanOrEqualTo(String value) {
            addCriterion("label >=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThan(String value) {
            addCriterion("label <", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLessThanOrEqualTo(String value) {
            addCriterion("label <=", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelLike(String value) {
            addCriterion("label like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotLike(String value) {
            addCriterion("label not like", value, "label");
            return (Criteria) this;
        }

        public Criteria andLabelIn(List<String> values) {
            addCriterion("label in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotIn(List<String> values) {
            addCriterion("label not in", values, "label");
            return (Criteria) this;
        }

        public Criteria andLabelBetween(String value1, String value2) {
            addCriterion("label between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andLabelNotBetween(String value1, String value2) {
            addCriterion("label not between", value1, value2, "label");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNull() {
            addCriterion("field_name is null");
            return (Criteria) this;
        }

        public Criteria andFieldNameIsNotNull() {
            addCriterion("field_name is not null");
            return (Criteria) this;
        }

        public Criteria andFieldNameEqualTo(String value) {
            addCriterion("field_name =", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotEqualTo(String value) {
            addCriterion("field_name <>", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThan(String value) {
            addCriterion("field_name >", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameGreaterThanOrEqualTo(String value) {
            addCriterion("field_name >=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThan(String value) {
            addCriterion("field_name <", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLessThanOrEqualTo(String value) {
            addCriterion("field_name <=", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameLike(String value) {
            addCriterion("field_name like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotLike(String value) {
            addCriterion("field_name not like", value, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameIn(List<String> values) {
            addCriterion("field_name in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotIn(List<String> values) {
            addCriterion("field_name not in", values, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameBetween(String value1, String value2) {
            addCriterion("field_name between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldNameNotBetween(String value1, String value2) {
            addCriterion("field_name not between", value1, value2, "fieldName");
            return (Criteria) this;
        }

        public Criteria andFieldValueIsNull() {
            addCriterion("field_value is null");
            return (Criteria) this;
        }

        public Criteria andFieldValueIsNotNull() {
            addCriterion("field_value is not null");
            return (Criteria) this;
        }

        public Criteria andFieldValueEqualTo(String value) {
            addCriterion("field_value =", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueNotEqualTo(String value) {
            addCriterion("field_value <>", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueGreaterThan(String value) {
            addCriterion("field_value >", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueGreaterThanOrEqualTo(String value) {
            addCriterion("field_value >=", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueLessThan(String value) {
            addCriterion("field_value <", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueLessThanOrEqualTo(String value) {
            addCriterion("field_value <=", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueLike(String value) {
            addCriterion("field_value like", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueNotLike(String value) {
            addCriterion("field_value not like", value, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueIn(List<String> values) {
            addCriterion("field_value in", values, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueNotIn(List<String> values) {
            addCriterion("field_value not in", values, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueBetween(String value1, String value2) {
            addCriterion("field_value between", value1, value2, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldValueNotBetween(String value1, String value2) {
            addCriterion("field_value not between", value1, value2, "fieldValue");
            return (Criteria) this;
        }

        public Criteria andFieldTextIsNull() {
            addCriterion("field_text is null");
            return (Criteria) this;
        }

        public Criteria andFieldTextIsNotNull() {
            addCriterion("field_text is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTextEqualTo(String value) {
            addCriterion("field_text =", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotEqualTo(String value) {
            addCriterion("field_text <>", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextGreaterThan(String value) {
            addCriterion("field_text >", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextGreaterThanOrEqualTo(String value) {
            addCriterion("field_text >=", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextLessThan(String value) {
            addCriterion("field_text <", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextLessThanOrEqualTo(String value) {
            addCriterion("field_text <=", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextLike(String value) {
            addCriterion("field_text like", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotLike(String value) {
            addCriterion("field_text not like", value, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextIn(List<String> values) {
            addCriterion("field_text in", values, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotIn(List<String> values) {
            addCriterion("field_text not in", values, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextBetween(String value1, String value2) {
            addCriterion("field_text between", value1, value2, "fieldText");
            return (Criteria) this;
        }

        public Criteria andFieldTextNotBetween(String value1, String value2) {
            addCriterion("field_text not between", value1, value2, "fieldText");
            return (Criteria) this;
        }

        public Criteria andUnitValueIsNull() {
            addCriterion("unit_value is null");
            return (Criteria) this;
        }

        public Criteria andUnitValueIsNotNull() {
            addCriterion("unit_value is not null");
            return (Criteria) this;
        }

        public Criteria andUnitValueEqualTo(String value) {
            addCriterion("unit_value =", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotEqualTo(String value) {
            addCriterion("unit_value <>", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueGreaterThan(String value) {
            addCriterion("unit_value >", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueGreaterThanOrEqualTo(String value) {
            addCriterion("unit_value >=", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLessThan(String value) {
            addCriterion("unit_value <", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLessThanOrEqualTo(String value) {
            addCriterion("unit_value <=", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueLike(String value) {
            addCriterion("unit_value like", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotLike(String value) {
            addCriterion("unit_value not like", value, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueIn(List<String> values) {
            addCriterion("unit_value in", values, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotIn(List<String> values) {
            addCriterion("unit_value not in", values, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueBetween(String value1, String value2) {
            addCriterion("unit_value between", value1, value2, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitValueNotBetween(String value1, String value2) {
            addCriterion("unit_value not between", value1, value2, "unitValue");
            return (Criteria) this;
        }

        public Criteria andUnitTextIsNull() {
            addCriterion("unit_text is null");
            return (Criteria) this;
        }

        public Criteria andUnitTextIsNotNull() {
            addCriterion("unit_text is not null");
            return (Criteria) this;
        }

        public Criteria andUnitTextEqualTo(String value) {
            addCriterion("unit_text =", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotEqualTo(String value) {
            addCriterion("unit_text <>", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextGreaterThan(String value) {
            addCriterion("unit_text >", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextGreaterThanOrEqualTo(String value) {
            addCriterion("unit_text >=", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextLessThan(String value) {
            addCriterion("unit_text <", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextLessThanOrEqualTo(String value) {
            addCriterion("unit_text <=", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextLike(String value) {
            addCriterion("unit_text like", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotLike(String value) {
            addCriterion("unit_text not like", value, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextIn(List<String> values) {
            addCriterion("unit_text in", values, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotIn(List<String> values) {
            addCriterion("unit_text not in", values, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextBetween(String value1, String value2) {
            addCriterion("unit_text between", value1, value2, "unitText");
            return (Criteria) this;
        }

        public Criteria andUnitTextNotBetween(String value1, String value2) {
            addCriterion("unit_text not between", value1, value2, "unitText");
            return (Criteria) this;
        }

        public Criteria andScoreValueIsNull() {
            addCriterion("score_value is null");
            return (Criteria) this;
        }

        public Criteria andScoreValueIsNotNull() {
            addCriterion("score_value is not null");
            return (Criteria) this;
        }

        public Criteria andScoreValueEqualTo(BigDecimal value) {
            addCriterion("score_value =", value, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueNotEqualTo(BigDecimal value) {
            addCriterion("score_value <>", value, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueGreaterThan(BigDecimal value) {
            addCriterion("score_value >", value, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("score_value >=", value, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueLessThan(BigDecimal value) {
            addCriterion("score_value <", value, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("score_value <=", value, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueIn(List<BigDecimal> values) {
            addCriterion("score_value in", values, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueNotIn(List<BigDecimal> values) {
            addCriterion("score_value not in", values, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("score_value between", value1, value2, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andScoreValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("score_value not between", value1, value2, "scoreValue");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenIsNull() {
            addCriterion("option_hidden is null");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenIsNotNull() {
            addCriterion("option_hidden is not null");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenEqualTo(Boolean value) {
            addCriterion("option_hidden =", value, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenNotEqualTo(Boolean value) {
            addCriterion("option_hidden <>", value, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenGreaterThan(Boolean value) {
            addCriterion("option_hidden >", value, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenGreaterThanOrEqualTo(Boolean value) {
            addCriterion("option_hidden >=", value, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenLessThan(Boolean value) {
            addCriterion("option_hidden <", value, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenLessThanOrEqualTo(Boolean value) {
            addCriterion("option_hidden <=", value, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenIn(List<Boolean> values) {
            addCriterion("option_hidden in", values, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenNotIn(List<Boolean> values) {
            addCriterion("option_hidden not in", values, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenBetween(Boolean value1, Boolean value2) {
            addCriterion("option_hidden between", value1, value2, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andOptionHiddenNotBetween(Boolean value1, Boolean value2) {
            addCriterion("option_hidden not between", value1, value2, "optionHidden");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("sort is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("sort is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("sort =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("sort <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("sort >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("sort <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("sort <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("sort in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("sort not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("sort between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("sort not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andComplateStatusIsNull() {
            addCriterion("complate_status is null");
            return (Criteria) this;
        }

        public Criteria andComplateStatusIsNotNull() {
            addCriterion("complate_status is not null");
            return (Criteria) this;
        }

        public Criteria andComplateStatusEqualTo(String value) {
            addCriterion("complate_status =", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotEqualTo(String value) {
            addCriterion("complate_status <>", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusGreaterThan(String value) {
            addCriterion("complate_status >", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusGreaterThanOrEqualTo(String value) {
            addCriterion("complate_status >=", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusLessThan(String value) {
            addCriterion("complate_status <", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusLessThanOrEqualTo(String value) {
            addCriterion("complate_status <=", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusLike(String value) {
            addCriterion("complate_status like", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotLike(String value) {
            addCriterion("complate_status not like", value, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusIn(List<String> values) {
            addCriterion("complate_status in", values, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotIn(List<String> values) {
            addCriterion("complate_status not in", values, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusBetween(String value1, String value2) {
            addCriterion("complate_status between", value1, value2, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andComplateStatusNotBetween(String value1, String value2) {
            addCriterion("complate_status not between", value1, value2, "complateStatus");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNull() {
            addCriterion("data_from is null");
            return (Criteria) this;
        }

        public Criteria andDataFromIsNotNull() {
            addCriterion("data_from is not null");
            return (Criteria) this;
        }

        public Criteria andDataFromEqualTo(String value) {
            addCriterion("data_from =", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotEqualTo(String value) {
            addCriterion("data_from <>", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThan(String value) {
            addCriterion("data_from >", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromGreaterThanOrEqualTo(String value) {
            addCriterion("data_from >=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThan(String value) {
            addCriterion("data_from <", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLessThanOrEqualTo(String value) {
            addCriterion("data_from <=", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromLike(String value) {
            addCriterion("data_from like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotLike(String value) {
            addCriterion("data_from not like", value, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromIn(List<String> values) {
            addCriterion("data_from in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotIn(List<String> values) {
            addCriterion("data_from not in", values, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromBetween(String value1, String value2) {
            addCriterion("data_from between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andDataFromNotBetween(String value1, String value2) {
            addCriterion("data_from not between", value1, value2, "dataFrom");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}