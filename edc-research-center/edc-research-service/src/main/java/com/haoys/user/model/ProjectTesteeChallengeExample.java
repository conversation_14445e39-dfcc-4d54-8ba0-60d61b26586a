package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProjectTesteeChallengeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ProjectTesteeChallengeExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdIsNull() {
            addCriterion("dvp_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdIsNotNull() {
            addCriterion("dvp_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdEqualTo(Long value) {
            addCriterion("dvp_rule_id =", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdNotEqualTo(Long value) {
            addCriterion("dvp_rule_id <>", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdGreaterThan(Long value) {
            addCriterion("dvp_rule_id >", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("dvp_rule_id >=", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdLessThan(Long value) {
            addCriterion("dvp_rule_id <", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdLessThanOrEqualTo(Long value) {
            addCriterion("dvp_rule_id <=", value, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdIn(List<Long> values) {
            addCriterion("dvp_rule_id in", values, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdNotIn(List<Long> values) {
            addCriterion("dvp_rule_id not in", values, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdBetween(Long value1, Long value2) {
            addCriterion("dvp_rule_id between", value1, value2, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andDvpRuleIdNotBetween(Long value1, Long value2) {
            addCriterion("dvp_rule_id not between", value1, value2, "dvpRuleId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNull() {
            addCriterion("testee_id is null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIsNotNull() {
            addCriterion("testee_id is not null");
            return (Criteria) this;
        }

        public Criteria andTesteeIdEqualTo(Long value) {
            addCriterion("testee_id =", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotEqualTo(Long value) {
            addCriterion("testee_id <>", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThan(Long value) {
            addCriterion("testee_id >", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("testee_id >=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThan(Long value) {
            addCriterion("testee_id <", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdLessThanOrEqualTo(Long value) {
            addCriterion("testee_id <=", value, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdIn(List<Long> values) {
            addCriterion("testee_id in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotIn(List<Long> values) {
            addCriterion("testee_id not in", values, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdBetween(Long value1, Long value2) {
            addCriterion("testee_id between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andTesteeIdNotBetween(Long value1, Long value2) {
            addCriterion("testee_id not between", value1, value2, "testeeId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormNameIsNull() {
            addCriterion("form_name is null");
            return (Criteria) this;
        }

        public Criteria andFormNameIsNotNull() {
            addCriterion("form_name is not null");
            return (Criteria) this;
        }

        public Criteria andFormNameEqualTo(String value) {
            addCriterion("form_name =", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotEqualTo(String value) {
            addCriterion("form_name <>", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameGreaterThan(String value) {
            addCriterion("form_name >", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameGreaterThanOrEqualTo(String value) {
            addCriterion("form_name >=", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameLessThan(String value) {
            addCriterion("form_name <", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameLessThanOrEqualTo(String value) {
            addCriterion("form_name <=", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameLike(String value) {
            addCriterion("form_name like", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotLike(String value) {
            addCriterion("form_name not like", value, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameIn(List<String> values) {
            addCriterion("form_name in", values, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotIn(List<String> values) {
            addCriterion("form_name not in", values, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameBetween(String value1, String value2) {
            addCriterion("form_name between", value1, value2, "formName");
            return (Criteria) this;
        }

        public Criteria andFormNameNotBetween(String value1, String value2) {
            addCriterion("form_name not between", value1, value2, "formName");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNull() {
            addCriterion("form_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNotNull() {
            addCriterion("form_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdEqualTo(Long value) {
            addCriterion("form_detail_id =", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotEqualTo(Long value) {
            addCriterion("form_detail_id <>", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThan(Long value) {
            addCriterion("form_detail_id >", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_detail_id >=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThan(Long value) {
            addCriterion("form_detail_id <", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("form_detail_id <=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIn(List<Long> values) {
            addCriterion("form_detail_id in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotIn(List<Long> values) {
            addCriterion("form_detail_id not in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdBetween(Long value1, Long value2) {
            addCriterion("form_detail_id between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("form_detail_id not between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNull() {
            addCriterion("form_table_id is null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNotNull() {
            addCriterion("form_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdEqualTo(Long value) {
            addCriterion("form_table_id =", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotEqualTo(Long value) {
            addCriterion("form_table_id <>", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThan(Long value) {
            addCriterion("form_table_id >", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_table_id >=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThan(Long value) {
            addCriterion("form_table_id <", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThanOrEqualTo(Long value) {
            addCriterion("form_table_id <=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIn(List<Long> values) {
            addCriterion("form_table_id in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotIn(List<Long> values) {
            addCriterion("form_table_id not in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdBetween(Long value1, Long value2) {
            addCriterion("form_table_id between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotBetween(Long value1, Long value2) {
            addCriterion("form_table_id not between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdIsNull() {
            addCriterion("form_result_id is null");
            return (Criteria) this;
        }

        public Criteria andFormResultIdIsNotNull() {
            addCriterion("form_result_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultIdEqualTo(Long value) {
            addCriterion("form_result_id =", value, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdNotEqualTo(Long value) {
            addCriterion("form_result_id <>", value, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdGreaterThan(Long value) {
            addCriterion("form_result_id >", value, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_result_id >=", value, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdLessThan(Long value) {
            addCriterion("form_result_id <", value, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdLessThanOrEqualTo(Long value) {
            addCriterion("form_result_id <=", value, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdIn(List<Long> values) {
            addCriterion("form_result_id in", values, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdNotIn(List<Long> values) {
            addCriterion("form_result_id not in", values, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdBetween(Long value1, Long value2) {
            addCriterion("form_result_id between", value1, value2, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultIdNotBetween(Long value1, Long value2) {
            addCriterion("form_result_id not between", value1, value2, "formResultId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoIsNull() {
            addCriterion("form_result_table_rowno is null");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoIsNotNull() {
            addCriterion("form_result_table_rowno is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoEqualTo(Long value) {
            addCriterion("form_result_table_rowno =", value, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoNotEqualTo(Long value) {
            addCriterion("form_result_table_rowno <>", value, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoGreaterThan(Long value) {
            addCriterion("form_result_table_rowno >", value, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoGreaterThanOrEqualTo(Long value) {
            addCriterion("form_result_table_rowno >=", value, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoLessThan(Long value) {
            addCriterion("form_result_table_rowno <", value, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoLessThanOrEqualTo(Long value) {
            addCriterion("form_result_table_rowno <=", value, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoIn(List<Long> values) {
            addCriterion("form_result_table_rowno in", values, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoNotIn(List<Long> values) {
            addCriterion("form_result_table_rowno not in", values, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoBetween(Long value1, Long value2) {
            addCriterion("form_result_table_rowno between", value1, value2, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableRownoNotBetween(Long value1, Long value2) {
            addCriterion("form_result_table_rowno not between", value1, value2, "formResultTableRowno");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdIsNull() {
            addCriterion("form_result_table_id is null");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdIsNotNull() {
            addCriterion("form_result_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdEqualTo(Long value) {
            addCriterion("form_result_table_id =", value, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdNotEqualTo(Long value) {
            addCriterion("form_result_table_id <>", value, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdGreaterThan(Long value) {
            addCriterion("form_result_table_id >", value, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_result_table_id >=", value, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdLessThan(Long value) {
            addCriterion("form_result_table_id <", value, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdLessThanOrEqualTo(Long value) {
            addCriterion("form_result_table_id <=", value, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdIn(List<Long> values) {
            addCriterion("form_result_table_id in", values, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdNotIn(List<Long> values) {
            addCriterion("form_result_table_id not in", values, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdBetween(Long value1, Long value2) {
            addCriterion("form_result_table_id between", value1, value2, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultTableIdNotBetween(Long value1, Long value2) {
            addCriterion("form_result_table_id not between", value1, value2, "formResultTableId");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelIsNull() {
            addCriterion("form_result_label is null");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelIsNotNull() {
            addCriterion("form_result_label is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelEqualTo(String value) {
            addCriterion("form_result_label =", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelNotEqualTo(String value) {
            addCriterion("form_result_label <>", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelGreaterThan(String value) {
            addCriterion("form_result_label >", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelGreaterThanOrEqualTo(String value) {
            addCriterion("form_result_label >=", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelLessThan(String value) {
            addCriterion("form_result_label <", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelLessThanOrEqualTo(String value) {
            addCriterion("form_result_label <=", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelLike(String value) {
            addCriterion("form_result_label like", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelNotLike(String value) {
            addCriterion("form_result_label not like", value, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelIn(List<String> values) {
            addCriterion("form_result_label in", values, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelNotIn(List<String> values) {
            addCriterion("form_result_label not in", values, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelBetween(String value1, String value2) {
            addCriterion("form_result_label between", value1, value2, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultLabelNotBetween(String value1, String value2) {
            addCriterion("form_result_label not between", value1, value2, "formResultLabel");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyIsNull() {
            addCriterion("form_result_key is null");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyIsNotNull() {
            addCriterion("form_result_key is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyEqualTo(String value) {
            addCriterion("form_result_key =", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyNotEqualTo(String value) {
            addCriterion("form_result_key <>", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyGreaterThan(String value) {
            addCriterion("form_result_key >", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyGreaterThanOrEqualTo(String value) {
            addCriterion("form_result_key >=", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyLessThan(String value) {
            addCriterion("form_result_key <", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyLessThanOrEqualTo(String value) {
            addCriterion("form_result_key <=", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyLike(String value) {
            addCriterion("form_result_key like", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyNotLike(String value) {
            addCriterion("form_result_key not like", value, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyIn(List<String> values) {
            addCriterion("form_result_key in", values, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyNotIn(List<String> values) {
            addCriterion("form_result_key not in", values, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyBetween(String value1, String value2) {
            addCriterion("form_result_key between", value1, value2, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultKeyNotBetween(String value1, String value2) {
            addCriterion("form_result_key not between", value1, value2, "formResultKey");
            return (Criteria) this;
        }

        public Criteria andFormResultValueIsNull() {
            addCriterion("form_result_value is null");
            return (Criteria) this;
        }

        public Criteria andFormResultValueIsNotNull() {
            addCriterion("form_result_value is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultValueEqualTo(String value) {
            addCriterion("form_result_value =", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueNotEqualTo(String value) {
            addCriterion("form_result_value <>", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueGreaterThan(String value) {
            addCriterion("form_result_value >", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueGreaterThanOrEqualTo(String value) {
            addCriterion("form_result_value >=", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueLessThan(String value) {
            addCriterion("form_result_value <", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueLessThanOrEqualTo(String value) {
            addCriterion("form_result_value <=", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueLike(String value) {
            addCriterion("form_result_value like", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueNotLike(String value) {
            addCriterion("form_result_value not like", value, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueIn(List<String> values) {
            addCriterion("form_result_value in", values, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueNotIn(List<String> values) {
            addCriterion("form_result_value not in", values, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueBetween(String value1, String value2) {
            addCriterion("form_result_value between", value1, value2, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultValueNotBetween(String value1, String value2) {
            addCriterion("form_result_value not between", value1, value2, "formResultValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueIsNull() {
            addCriterion("form_result_original_value is null");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueIsNotNull() {
            addCriterion("form_result_original_value is not null");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueEqualTo(String value) {
            addCriterion("form_result_original_value =", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueNotEqualTo(String value) {
            addCriterion("form_result_original_value <>", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueGreaterThan(String value) {
            addCriterion("form_result_original_value >", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueGreaterThanOrEqualTo(String value) {
            addCriterion("form_result_original_value >=", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueLessThan(String value) {
            addCriterion("form_result_original_value <", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueLessThanOrEqualTo(String value) {
            addCriterion("form_result_original_value <=", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueLike(String value) {
            addCriterion("form_result_original_value like", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueNotLike(String value) {
            addCriterion("form_result_original_value not like", value, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueIn(List<String> values) {
            addCriterion("form_result_original_value in", values, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueNotIn(List<String> values) {
            addCriterion("form_result_original_value not in", values, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueBetween(String value1, String value2) {
            addCriterion("form_result_original_value between", value1, value2, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andFormResultOriginalValueNotBetween(String value1, String value2) {
            addCriterion("form_result_original_value not between", value1, value2, "formResultOriginalValue");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(String value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(String value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(String value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(String value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(String value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(String value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLike(String value) {
            addCriterion("type like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotLike(String value) {
            addCriterion("type not like", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<String> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<String> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(String value1, String value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(String value1, String value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andQueryMethodIsNull() {
            addCriterion("query_method is null");
            return (Criteria) this;
        }

        public Criteria andQueryMethodIsNotNull() {
            addCriterion("query_method is not null");
            return (Criteria) this;
        }

        public Criteria andQueryMethodEqualTo(String value) {
            addCriterion("query_method =", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotEqualTo(String value) {
            addCriterion("query_method <>", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodGreaterThan(String value) {
            addCriterion("query_method >", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodGreaterThanOrEqualTo(String value) {
            addCriterion("query_method >=", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodLessThan(String value) {
            addCriterion("query_method <", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodLessThanOrEqualTo(String value) {
            addCriterion("query_method <=", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodLike(String value) {
            addCriterion("query_method like", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotLike(String value) {
            addCriterion("query_method not like", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodIn(List<String> values) {
            addCriterion("query_method in", values, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotIn(List<String> values) {
            addCriterion("query_method not in", values, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodBetween(String value1, String value2) {
            addCriterion("query_method between", value1, value2, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotBetween(String value1, String value2) {
            addCriterion("query_method not between", value1, value2, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andIfSystemIsNull() {
            addCriterion("if_system is null");
            return (Criteria) this;
        }

        public Criteria andIfSystemIsNotNull() {
            addCriterion("if_system is not null");
            return (Criteria) this;
        }

        public Criteria andIfSystemEqualTo(Boolean value) {
            addCriterion("if_system =", value, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemNotEqualTo(Boolean value) {
            addCriterion("if_system <>", value, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemGreaterThan(Boolean value) {
            addCriterion("if_system >", value, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemGreaterThanOrEqualTo(Boolean value) {
            addCriterion("if_system >=", value, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemLessThan(Boolean value) {
            addCriterion("if_system <", value, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemLessThanOrEqualTo(Boolean value) {
            addCriterion("if_system <=", value, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemIn(List<Boolean> values) {
            addCriterion("if_system in", values, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemNotIn(List<Boolean> values) {
            addCriterion("if_system not in", values, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemBetween(Boolean value1, Boolean value2) {
            addCriterion("if_system between", value1, value2, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andIfSystemNotBetween(Boolean value1, Boolean value2) {
            addCriterion("if_system not between", value1, value2, "ifSystem");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusIsNull() {
            addCriterion("reply_close_status is null");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusIsNotNull() {
            addCriterion("reply_close_status is not null");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusEqualTo(String value) {
            addCriterion("reply_close_status =", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusNotEqualTo(String value) {
            addCriterion("reply_close_status <>", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusGreaterThan(String value) {
            addCriterion("reply_close_status >", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusGreaterThanOrEqualTo(String value) {
            addCriterion("reply_close_status >=", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusLessThan(String value) {
            addCriterion("reply_close_status <", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusLessThanOrEqualTo(String value) {
            addCriterion("reply_close_status <=", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusLike(String value) {
            addCriterion("reply_close_status like", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusNotLike(String value) {
            addCriterion("reply_close_status not like", value, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusIn(List<String> values) {
            addCriterion("reply_close_status in", values, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusNotIn(List<String> values) {
            addCriterion("reply_close_status not in", values, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusBetween(String value1, String value2) {
            addCriterion("reply_close_status between", value1, value2, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andReplyCloseStatusNotBetween(String value1, String value2) {
            addCriterion("reply_close_status not between", value1, value2, "replyCloseStatus");
            return (Criteria) this;
        }

        public Criteria andCloseReasonIsNull() {
            addCriterion("close_reason is null");
            return (Criteria) this;
        }

        public Criteria andCloseReasonIsNotNull() {
            addCriterion("close_reason is not null");
            return (Criteria) this;
        }

        public Criteria andCloseReasonEqualTo(String value) {
            addCriterion("close_reason =", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonNotEqualTo(String value) {
            addCriterion("close_reason <>", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonGreaterThan(String value) {
            addCriterion("close_reason >", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonGreaterThanOrEqualTo(String value) {
            addCriterion("close_reason >=", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonLessThan(String value) {
            addCriterion("close_reason <", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonLessThanOrEqualTo(String value) {
            addCriterion("close_reason <=", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonLike(String value) {
            addCriterion("close_reason like", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonNotLike(String value) {
            addCriterion("close_reason not like", value, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonIn(List<String> values) {
            addCriterion("close_reason in", values, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonNotIn(List<String> values) {
            addCriterion("close_reason not in", values, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonBetween(String value1, String value2) {
            addCriterion("close_reason between", value1, value2, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseReasonNotBetween(String value1, String value2) {
            addCriterion("close_reason not between", value1, value2, "closeReason");
            return (Criteria) this;
        }

        public Criteria andCloseTimeIsNull() {
            addCriterion("close_time is null");
            return (Criteria) this;
        }

        public Criteria andCloseTimeIsNotNull() {
            addCriterion("close_time is not null");
            return (Criteria) this;
        }

        public Criteria andCloseTimeEqualTo(Date value) {
            addCriterion("close_time =", value, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeNotEqualTo(Date value) {
            addCriterion("close_time <>", value, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeGreaterThan(Date value) {
            addCriterion("close_time >", value, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("close_time >=", value, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeLessThan(Date value) {
            addCriterion("close_time <", value, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeLessThanOrEqualTo(Date value) {
            addCriterion("close_time <=", value, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeIn(List<Date> values) {
            addCriterion("close_time in", values, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeNotIn(List<Date> values) {
            addCriterion("close_time not in", values, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeBetween(Date value1, Date value2) {
            addCriterion("close_time between", value1, value2, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseTimeNotBetween(Date value1, Date value2) {
            addCriterion("close_time not between", value1, value2, "closeTime");
            return (Criteria) this;
        }

        public Criteria andCloseUserIsNull() {
            addCriterion("close_user is null");
            return (Criteria) this;
        }

        public Criteria andCloseUserIsNotNull() {
            addCriterion("close_user is not null");
            return (Criteria) this;
        }

        public Criteria andCloseUserEqualTo(String value) {
            addCriterion("close_user =", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserNotEqualTo(String value) {
            addCriterion("close_user <>", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserGreaterThan(String value) {
            addCriterion("close_user >", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserGreaterThanOrEqualTo(String value) {
            addCriterion("close_user >=", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserLessThan(String value) {
            addCriterion("close_user <", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserLessThanOrEqualTo(String value) {
            addCriterion("close_user <=", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserLike(String value) {
            addCriterion("close_user like", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserNotLike(String value) {
            addCriterion("close_user not like", value, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserIn(List<String> values) {
            addCriterion("close_user in", values, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserNotIn(List<String> values) {
            addCriterion("close_user not in", values, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserBetween(String value1, String value2) {
            addCriterion("close_user between", value1, value2, "closeUser");
            return (Criteria) this;
        }

        public Criteria andCloseUserNotBetween(String value1, String value2) {
            addCriterion("close_user not between", value1, value2, "closeUser");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeIsNull() {
            addCriterion("user_role_code is null");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeIsNotNull() {
            addCriterion("user_role_code is not null");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeEqualTo(String value) {
            addCriterion("user_role_code =", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeNotEqualTo(String value) {
            addCriterion("user_role_code <>", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeGreaterThan(String value) {
            addCriterion("user_role_code >", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("user_role_code >=", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeLessThan(String value) {
            addCriterion("user_role_code <", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeLessThanOrEqualTo(String value) {
            addCriterion("user_role_code <=", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeLike(String value) {
            addCriterion("user_role_code like", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeNotLike(String value) {
            addCriterion("user_role_code not like", value, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeIn(List<String> values) {
            addCriterion("user_role_code in", values, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeNotIn(List<String> values) {
            addCriterion("user_role_code not in", values, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeBetween(String value1, String value2) {
            addCriterion("user_role_code between", value1, value2, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andUserRoleCodeNotBetween(String value1, String value2) {
            addCriterion("user_role_code not between", value1, value2, "userRoleCode");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdIsNull() {
            addCriterion("receive_user_id is null");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdIsNotNull() {
            addCriterion("receive_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdEqualTo(String value) {
            addCriterion("receive_user_id =", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdNotEqualTo(String value) {
            addCriterion("receive_user_id <>", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdGreaterThan(String value) {
            addCriterion("receive_user_id >", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("receive_user_id >=", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdLessThan(String value) {
            addCriterion("receive_user_id <", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdLessThanOrEqualTo(String value) {
            addCriterion("receive_user_id <=", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdLike(String value) {
            addCriterion("receive_user_id like", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdNotLike(String value) {
            addCriterion("receive_user_id not like", value, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdIn(List<String> values) {
            addCriterion("receive_user_id in", values, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdNotIn(List<String> values) {
            addCriterion("receive_user_id not in", values, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdBetween(String value1, String value2) {
            addCriterion("receive_user_id between", value1, value2, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveUserIdNotBetween(String value1, String value2) {
            addCriterion("receive_user_id not between", value1, value2, "receiveUserId");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleIsNull() {
            addCriterion("receive_rule is null");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleIsNotNull() {
            addCriterion("receive_rule is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleEqualTo(String value) {
            addCriterion("receive_rule =", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleNotEqualTo(String value) {
            addCriterion("receive_rule <>", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleGreaterThan(String value) {
            addCriterion("receive_rule >", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleGreaterThanOrEqualTo(String value) {
            addCriterion("receive_rule >=", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleLessThan(String value) {
            addCriterion("receive_rule <", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleLessThanOrEqualTo(String value) {
            addCriterion("receive_rule <=", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleLike(String value) {
            addCriterion("receive_rule like", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleNotLike(String value) {
            addCriterion("receive_rule not like", value, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleIn(List<String> values) {
            addCriterion("receive_rule in", values, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleNotIn(List<String> values) {
            addCriterion("receive_rule not in", values, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleBetween(String value1, String value2) {
            addCriterion("receive_rule between", value1, value2, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andReceiveRuleNotBetween(String value1, String value2) {
            addCriterion("receive_rule not between", value1, value2, "receiveRule");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNull() {
            addCriterion("create_user is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIsNotNull() {
            addCriterion("create_user is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserEqualTo(String value) {
            addCriterion("create_user =", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotEqualTo(String value) {
            addCriterion("create_user <>", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThan(String value) {
            addCriterion("create_user >", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserGreaterThanOrEqualTo(String value) {
            addCriterion("create_user >=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThan(String value) {
            addCriterion("create_user <", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLessThanOrEqualTo(String value) {
            addCriterion("create_user <=", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserLike(String value) {
            addCriterion("create_user like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotLike(String value) {
            addCriterion("create_user not like", value, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserIn(List<String> values) {
            addCriterion("create_user in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotIn(List<String> values) {
            addCriterion("create_user not in", values, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserBetween(String value1, String value2) {
            addCriterion("create_user between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateUserNotBetween(String value1, String value2) {
            addCriterion("create_user not between", value1, value2, "createUser");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNull() {
            addCriterion("update_user is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIsNotNull() {
            addCriterion("update_user is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserEqualTo(String value) {
            addCriterion("update_user =", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotEqualTo(String value) {
            addCriterion("update_user <>", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThan(String value) {
            addCriterion("update_user >", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserGreaterThanOrEqualTo(String value) {
            addCriterion("update_user >=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThan(String value) {
            addCriterion("update_user <", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLessThanOrEqualTo(String value) {
            addCriterion("update_user <=", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserLike(String value) {
            addCriterion("update_user like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotLike(String value) {
            addCriterion("update_user not like", value, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIn(List<String> values) {
            addCriterion("update_user in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotIn(List<String> values) {
            addCriterion("update_user not in", values, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserBetween(String value1, String value2) {
            addCriterion("update_user between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNotBetween(String value1, String value2) {
            addCriterion("update_user not between", value1, value2, "updateUser");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(String value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(String value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(String value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(String value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(String value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLike(String value) {
            addCriterion("group_id like", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotLike(String value) {
            addCriterion("group_id not like", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<String> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<String> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(String value1, String value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(String value1, String value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdIsNull() {
            addCriterion("project_org_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdIsNotNull() {
            addCriterion("project_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdEqualTo(String value) {
            addCriterion("project_org_id =", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotEqualTo(String value) {
            addCriterion("project_org_id <>", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdGreaterThan(String value) {
            addCriterion("project_org_id >", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("project_org_id >=", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdLessThan(String value) {
            addCriterion("project_org_id <", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdLessThanOrEqualTo(String value) {
            addCriterion("project_org_id <=", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdLike(String value) {
            addCriterion("project_org_id like", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotLike(String value) {
            addCriterion("project_org_id not like", value, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdIn(List<String> values) {
            addCriterion("project_org_id in", values, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotIn(List<String> values) {
            addCriterion("project_org_id not in", values, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdBetween(String value1, String value2) {
            addCriterion("project_org_id between", value1, value2, "projectOrgId");
            return (Criteria) this;
        }

        public Criteria andProjectOrgIdNotBetween(String value1, String value2) {
            addCriterion("project_org_id not between", value1, value2, "projectOrgId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}