package com.haoys.user.mapper;

import com.haoys.user.model.FlowBaseInfo;
import com.haoys.user.model.FlowBaseInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FlowBaseInfoMapper {
    long countByExample(FlowBaseInfoExample example);

    int deleteByExample(FlowBaseInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlowBaseInfo record);

    int insertSelective(FlowBaseInfo record);

    List<FlowBaseInfo> selectByExample(FlowBaseInfoExample example);

    FlowBaseInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FlowBaseInfo record, @Param("example") FlowBaseInfoExample example);

    int updateByExample(@Param("record") FlowBaseInfo record, @Param("example") FlowBaseInfoExample example);

    int updateByPrimaryKeySelective(FlowBaseInfo record);

    int updateByPrimaryKey(FlowBaseInfo record);
}