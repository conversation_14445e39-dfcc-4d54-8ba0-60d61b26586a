package com.haoys.user.service.impl;


import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.*;
import com.haoys.user.domain.param.rcts.ModifyRandomizedParam;
import com.haoys.user.domain.param.rcts.ModifyRandomizedParamsConfigParam;
import com.haoys.user.domain.param.rcts.SaveRandomizedParam;
import com.haoys.user.domain.param.rcts.SaveRandomizedParamsConfigParam;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.domain.vo.rcts.RandomizedBlindRecordVo;
import com.haoys.user.domain.vo.rcts.RandomizedParamsVo;
import com.haoys.user.domain.vo.rcts.RandomizedVo;
import com.haoys.user.domain.vo.rcts.TesteeInfoVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo;
import com.haoys.user.enums.ProjectTesteeEnum;
import com.haoys.user.enums.TesteeSortFieldEnum;
import com.haoys.user.mapper.*;
import com.haoys.user.model.RctsRandomizedBlindRecord;
import com.haoys.user.model.RctsRandomizedConfig;
import com.haoys.user.model.RctsRandomizedParamsConfig;
import com.haoys.user.model.RctsTesteeInfo;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.RandomizedControlledTrialsService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class RandomizedControlledTrialsServiceImpl extends BaseService implements RandomizedControlledTrialsService {
    
    private final RctsTesteeInfoMapper rctsTesteeInfoMapper;
    private final ProjectTesteeInfoMapper projectTesteeInfoMapper;
    private final RctsRandomizedConfigMapper rctsRandomizedConfigMapper;
    private final RctsRandomizedParamsConfigMapper rctsRandomizedParamsConfigMapper;
    private final RctsRandomizedBlindRecordMapper rctsRandomizedBlindRecordMapper;
    private final PythonApiService pythonApiService;
    private final ProjectTesteeInfoService projectTesteeInfoService;

    private final OrganizationService organizationService;
    
    @Override
    public int saveRandomizedConfig(SaveRandomizedParam param) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        if (StringUtils.isNotEmpty(String.valueOf(param.getId()))&&param.getId() != null){
            RctsRandomizedConfig rc = rctsRandomizedConfigMapper.selectByPrimaryKey(param.getId());
            BeanUtils.copyProperties(param, rc);
            rc.setUpdateTime(new Date());
            rc.setUpdateUserId(userId);
            return rctsRandomizedConfigMapper.updateByPrimaryKey(rc);
        }
        RctsRandomizedConfig rc = new RctsRandomizedConfig();
        BeanUtils.copyProperties(param, rc);
        rc.setId(SnowflakeIdWorker.getUuid());
        rc.setCreateTime(new Date());
        rc.setCreateUserId(userId);
        rc.setTenantId(SecurityUtils.getSystemTenantId());
        rc.setPlatformId(SecurityUtils.getSystemPlatformId());
        rc.setEnableGroup(true);
        rc.setEnabled(true);
        rc.setStatus(BusinessConfig.VALID_STATUS);
        return rctsRandomizedConfigMapper.insert(rc);
    }


    @Override
    public int getRandomizedConfigCountCheck(Long projectId, Long visitId, Long formId, Long variableId) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        if (StringUtils.isNotEmpty(String.valueOf(visitId))){
            params.put("visitId", visitId);
        }
        if (StringUtils.isNotEmpty(String.valueOf(formId))){
            params.put("formId", formId);
        }
        if (StringUtils.isNotEmpty(String.valueOf(variableId))){
            params.put("variableId", variableId);
        }
        int res = rctsRandomizedConfigMapper.getRandomizedConfigCountCheck(params);
        return res;
    }

    @Override
    public List<RandomizedVo> getRandomizedConfig(String projectId) {
        List<RandomizedVo> res = rctsRandomizedConfigMapper.getRandomizedConfig(projectId, BusinessConfig.VALID_STATUS);
        for (RandomizedVo vo : res ){
            if (vo.getExpand() != null){
                Object expandObj = JsonUtils.jsonToMap(vo.getExpand());
                if (expandObj != null){
                    vo.setExpandObject(expandObj);
                }
            }
        }
        return res;
    }

    @Override
    public int modifyRandomizedConfig(ModifyRandomizedParam param) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsRandomizedConfig rc = rctsRandomizedConfigMapper.selectByPrimaryKey(param.getId());
        //RctsRandomizedConfig rc = new RctsRandomizedConfig();
        BeanUtils.copyProperties(param, rc);
        rc.setUpdateTime(new Date());
        rc.setUpdateUserId(userId);
        return rctsRandomizedConfigMapper.updateByPrimaryKey(rc);
    }

    @Override
    public int removeRandomizedConfig(Long id) {
        RctsRandomizedConfig rc = rctsRandomizedConfigMapper.selectByPrimaryKey(id);
        rc.setId(id);
        rc.setStatus(BusinessConfig.NO_VALID_STATUS);
        return rctsRandomizedConfigMapper.updateByPrimaryKey(rc);
    }


    @Override
    public int saveRandomizedParamsConfig(SaveRandomizedParamsConfigParam param) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsRandomizedParamsConfig pc = new RctsRandomizedParamsConfig();
        BeanUtils.copyProperties(param, pc);
        pc.setId(SnowflakeIdWorker.getUuid());
        pc.setCreateTime(new Date());
        pc.setCreateUserId(userId);
        pc.setTenantId(SecurityUtils.getSystemTenantId());
        pc.setPlatformId(SecurityUtils.getSystemPlatformId());
        pc.setStatus(BusinessConfig.VALID_STATUS);
        pc.setTenantId(SecurityUtils.getSystemTenantId());
        pc.setPlatformId(SecurityUtils.getSystemPlatformId());
        return rctsRandomizedParamsConfigMapper.insert(pc);
    }

    @Override
    public int countByProjectId(Long projectId) {
        return rctsRandomizedParamsConfigMapper.countByProjectId(projectId);
    }

    @Override
    public RandomizedParamsVo getRandomizedParamsConfig(Long projectId) {
        RandomizedParamsVo res = rctsRandomizedParamsConfigMapper.getRandomizedParamsConfig(BusinessConfig.VALID_STATUS, projectId);
        return res;
    }

    public String checkRandomizedParamsConfig(Long projectId){
        int queryBlindRecordTesteeCount = rctsRandomizedBlindRecordMapper.queryBlindRecordTesteeCount(projectId);
        if (queryBlindRecordTesteeCount>0){
            return "盲底已绑定参与者，不能更新。";
        }
        return "ok";
    }


    @Override
    public String modifyRandomizedParamsConfig(ModifyRandomizedParamsConfigParam param) {
        String checkRandomizedParamsConfig = checkRandomizedParamsConfig(param.getProjectId());
        if (!"ok".equals(checkRandomizedParamsConfig)){
            return checkRandomizedParamsConfig;
        }
        int queryBlindRecordCount = rctsRandomizedBlindRecordMapper.queryBlindRecordCount(param.getProjectId());
        if (queryBlindRecordCount > 0){
            rctsRandomizedBlindRecordMapper.deleteBlindRecordCount(param.getProjectId());
        }

        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsRandomizedParamsConfig pc = rctsRandomizedParamsConfigMapper.selectByPrimaryKey(param.getId());
        BeanUtils.copyProperties(param, pc);
        pc.setUpdateTime(new Date());
        pc.setUpdateUserId(userId);
        rctsRandomizedParamsConfigMapper.updateByPrimaryKey(pc);
        return "ok";
    }

    @Override
    public String modifyRandomizedGroupConfig(String param, Long id) {

        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsRandomizedParamsConfig pc = rctsRandomizedParamsConfigMapper.selectByPrimaryKey(id);

        String checkRandomizedParamsConfig = checkRandomizedParamsConfig(pc.getProjectId());
        if (!"ok".equals(checkRandomizedParamsConfig)){
            return checkRandomizedParamsConfig;
        }
        int queryBlindRecordCount = rctsRandomizedBlindRecordMapper.queryBlindRecordCount(pc.getProjectId());
        if (queryBlindRecordCount > 0){
            rctsRandomizedBlindRecordMapper.deleteBlindRecordCount(pc.getProjectId());
        }

        //RctsRandomizedParamsConfig pc = new RctsRandomizedParamsConfig();
        pc.setRandomizedGroupConfig(param);
        pc.setUpdateTime(new Date());
        pc.setUpdateUserId(userId);
        rctsRandomizedParamsConfigMapper.updateByPrimaryKey(pc);
        return "ok";
    }

    @Override
    public String modifyRandomizedLayerConfig(String param, Long id) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsRandomizedParamsConfig pc = rctsRandomizedParamsConfigMapper.selectByPrimaryKey(id);
        //RctsRandomizedParamsConfig pc = new RctsRandomizedParamsConfig();
        String checkRandomizedParamsConfig = checkRandomizedParamsConfig(pc.getProjectId());
        if (!"ok".equals(checkRandomizedParamsConfig)){
            return checkRandomizedParamsConfig;
        }
        int queryBlindRecordCount = rctsRandomizedBlindRecordMapper.queryBlindRecordCount(pc.getProjectId());
        if (queryBlindRecordCount > 0){
            rctsRandomizedBlindRecordMapper.deleteBlindRecordCount(pc.getProjectId());
        }

        pc.setRandomizedLayerConfig(param);
        pc.setUpdateTime(new Date());
        pc.setUpdateUserId(userId);
        rctsRandomizedParamsConfigMapper.updateByPrimaryKey(pc);
        return "ok";
    }


    @Override
    public String saveTesteeInfo(Long projectId, Long orgId) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("orgId", getQueryWrapperParams(orgId.toString()));
        String sortField = TesteeSortFieldEnum.TESTEE_SORT_FIELD_05.getValue();
        String sortType = "desc";
        params.put("sortField", sortField);
        params.put("sortType", sortType);

        List<ProjectTesteeWrapperVo> testeeList = projectTesteeInfoMapper.getProjectTesteeListForPage(params);
        if (testeeList.size() > 0){
            for (ProjectTesteeWrapperVo vo : testeeList){
                int checkTestee = rctsTesteeInfoMapper.getTesteeCountByTesteeId(projectId, vo.getId());
                if (checkTestee > 0){
                    RctsTesteeInfo uInfo = rctsTesteeInfoMapper.getTesteeInfo(projectId, vo.getId());
                    uInfo.setTesteeCode(vo.getTesteeCode());
                    uInfo.setRealName(vo.getRealName());
                    uInfo.setAcronym(vo.getAcronym());
                    uInfo.setAge(vo.getAge());
                    uInfo.setGender(vo.getGender());
                    rctsTesteeInfoMapper.updateByPrimaryKey(uInfo);
                } else {
                    RctsTesteeInfo info = new RctsTesteeInfo();
                    BeanUtils.copyProperties(vo, info);
                    info.setTesteeCode(vo.getTesteeCode());
                    info.setId(SnowflakeIdWorker.getUuid());
                    info.setTesteeId(vo.getId());
                    info.setProjectId(projectId);
                    info.setOwnerOrgId(vo.getOwnerOrgId());
                    info.setStatus(BusinessConfig.VALID_STATUS);
                    info.setCreateTime(new Date());
                    info.setCreateUserId(userId);
                    info.setTenantId(SecurityUtils.getSystemTenantId());
                    info.setPlatformId(SecurityUtils.getSystemPlatformId());
                    rctsTesteeInfoMapper.insert(info);
                }
            }
        }
        return "ok";
    }


    @Override
    public CommonPage<TesteeInfoVo> getTesteeInfoPage(Long projectId, Long orgId, String researchStatus, String queryParam1, int pageNum, int pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        params.put("queryParam1", queryParam1);
        if (StringUtils.isNotEmpty(String.valueOf(projectId))){
            params.put("projectId", projectId);
        }
        if (StringUtils.isNotEmpty(String.valueOf(orgId))){
            params.put("orgId", orgId);
        }
        if (StringUtils.isNotEmpty(researchStatus)){
            params.put("researchStatus", researchStatus);
        }
        if (StringUtils.isNotEmpty(queryParam1)){
            params.put("testeeCode", queryParam1);
            params.put("acronym", queryParam1);
        }
        List<TesteeInfoVo> res = rctsTesteeInfoMapper.getTesteeInfoPage(params);
        return commonPageListWrapper(pageNum, pageSize, page, res);
    }

    @Override
    public RctsTesteeInfo getRctsTesteeInfo(Long projectId, Long testeeId) {
        return rctsTesteeInfoMapper.getRctsTesteeInfo(projectId, null, testeeId);
    }

    @Override
    public List<Map<String, String>> getTesteeSelection(Long projectId, String testeeCode) {
        if (testeeCode == null){
            testeeCode = "";
        }
        List<Map<String, String>> res = rctsTesteeInfoMapper.getTesteeSelection(projectId, testeeCode);
        return res;
    }

    @Override
    public boolean getShowRandomizedBlindButton(Long projectId) {
        int res = rctsRandomizedBlindRecordMapper.queryBlindRecordTesteeCount(projectId);
        if (res > 0){
            return false;
        } else {
            RandomizedParamsVo vo = rctsRandomizedParamsConfigMapper.getRandomizedParamsConfig(BusinessConfig.VALID_STATUS, projectId);
            if (vo == null || StringUtils.isEmpty(vo.getRandomizedMethod()) || "dynamicRandomized".equals(vo.getRandomizedMethod())){
                return false;
            }
            return true;
        }
    }

    @Override
    public String randomizedBlind(Long projectId) {
        int getBlindRecordTesteeCount = rctsRandomizedBlindRecordMapper.queryBlindRecordTesteeCount(projectId);
        if (getBlindRecordTesteeCount > 0){
            return "盲底已绑定参与者，不能重复生成盲底！";
        }
        int queryBlindRecordCount = rctsRandomizedBlindRecordMapper.queryBlindRecordCount(projectId);
        if (queryBlindRecordCount > 0){
            rctsRandomizedBlindRecordMapper.deleteBlindRecordCount(projectId);
        }

        // 随机参数
        RandomizedParamsVo randomizedParamsVo = rctsRandomizedParamsConfigMapper.getRandomizedParamsConfig(BusinessConfig.VALID_STATUS, projectId);
        if (randomizedParamsVo.getId() == null || !randomizedParamsVo.getEnabled()){
            return "没有配置随机参数或者随机参数未启用！";
        }
        if (randomizedParamsVo.getJoinGroupLimit() <=0){
            return "入组上限应大于0！";
        }
        if (randomizedParamsVo.getRandomizedGroupConfig() == null || randomizedParamsVo.getRandomizedLayerConfig()== null){
            return "随机分组或分层因素未配置！";
        }
        String RandomizedGroupConfig = randomizedParamsVo.getRandomizedGroupConfig();
        String RandomizedLayerConfig = randomizedParamsVo.getRandomizedLayerConfig();
        Map<String, Object> groupMap = JsonUtils.jsonToObj(RandomizedGroupConfig, Map.class);
        boolean groupEnable = (boolean) groupMap.get("enabled");
        Map<String, Object> layerMap = JsonUtils.jsonToObj(RandomizedLayerConfig, Map.class);
        boolean layerEnable = (boolean) layerMap.get("enabled");
        if ("staticRandomized-simple".equals(randomizedParamsVo.getRandomizedRule())){
            if (!groupEnable ){
                return "随机分组未开启！";
            }
        }
        if ("staticRandomized-layered".equals(randomizedParamsVo.getRandomizedRule())){
            if (!groupEnable || !layerEnable){
                return "随机分组或分层因素未开启！";
            }
        }
        if ("staticRandomized-grouped".equals(randomizedParamsVo.getRandomizedRule())){
            if (!groupEnable ){
                return "随机分组未开启！";
            }
        }

        List<Map<String, String>> layerConfigList = (List<Map<String, String>>) layerMap.get("list");
        //Map<String, List<Map<String, String>>> ma = new HashMap<>();
        Map<String, Map<String, String>> ma = new HashMap<>();
        Map<String, String> ma2 = new HashMap<>();
        for (Map<String, String> map : layerConfigList){
            String name = map.get("layeredId");         // 分层名称 的id
            String cnName = map.get("layeredNames");         // 分层名称 的mc
            String layer = map.get("factorLevelId");    // 因素水平 的id
            String layerName = map.get("factorLevel");  // 因素水平 的汉字
            String[] layerLis = layer.split("#");
            String[] layerNameLis = layerName.split("#");
            //List<Map<String, String>> mapList = new ArrayList<>();
            Map<String, String> m = new HashMap<>();
            for (int i = 0; i < layerLis.length; i++){
//                Map<String, String> m = new HashMap<>();
                m.put(layerLis[i], layerNameLis[i]);
                //mapList.add(m);
            }

            //ma.put(name, mapList);
            ma.put(name, m);
            ma2.put("cn_"+name, cnName);
        }

        String res1 = pythonApiService.randomizedBlind(randomizedParamsVo);
        ObjectMapper mapper = new ObjectMapper();
        try {
            //Map map = mapper.readValue(Files.newInputStream(Paths.get("C:\\Users\\<USER>\\Desktop\\我得\\1up\\edc\\938297372402913280_RandomizedPythonScript.json")), Map.class);
            Map map = mapper.readValue(Files.newInputStream(Paths.get(res1)), Map.class);
            Map<String, Object> saveMongo = new HashMap<>();
            saveMongo.put("projectId", projectId);
            // "pid - 随机号","Gender","AgeGroup","batch_n - 区组号","batch_item_n - 区组内编号","group - 组别名称"
            saveMongo.putAll(map);
            //MongoTemplateUtil.saveOne("randomized_blind_record ", saveMongo);
            Object columnsStr = map.get("columns");

            List<String> columns = (List<String>) map.get("columns");
            List<List<Object>> datas = (List<List<Object>>) map.get("data");
            int columnSize = columns.size();
            Date nowTime = new Date();

            List<ProjectOrgVo> orgVos = organizationService.getProjectOrgListByCondition(String.valueOf(randomizedParamsVo.getProjectId()), "","");
            List<Long> orgList = orgVos.stream().map(ProjectOrgVo :: getId).collect(Collectors.toList());
            for (Long orgId : orgList){
                for (List<Object> strings : datas){
                    RctsRandomizedBlindRecord record = new RctsRandomizedBlindRecord();
                    record.setId(SnowflakeIdWorker.getUuid());
                    record.setProjectId(projectId);
                    record.setProjectOrgId(orgId);
                    record.setRandomizedTime(nowTime);
                    record.setCreateTime(nowTime);
                    record.setStatus(BusinessConfig.VALID_STATUS);
                    Map<String, String> expand = new HashMap<>();
                    for (int i = 0; i < columnSize; i++){
                        if ("pid".equals(columns.get(i))){
                            Object pid = strings.get(i);
                            record.setRandomizedNumber(String.valueOf(pid));
                        } else if ("batch_n".equals(columns.get(i))){
                            Object batchNo = strings.get(i);
                            record.setBatchNo(Integer.parseInt(String.valueOf(batchNo)));
                        } else if ("batch_item_n".equals(columns.get(i))){
                            Object batchItemNo = strings.get(i);
                            record.setBatchItemNo(Integer.parseInt(String.valueOf(batchItemNo)));
                        } else if ("group".equals(columns.get(i))){
                            Object group = strings.get(i);
                            record.setJoinGroupName(String.valueOf(group));
                        } else {
                            String columnName = columns.get(i);
                            Map<String, String> m2 = ma.get(columnName);
                            String res = m2.get(String.valueOf(strings.get(i)));
                            String res2 = ma2.get("cn_"+columnName);
                            expand.put(res2, res);
                        }
                    }
                    record.setExpand(JsonUtils.objToJson(expand));
                    rctsRandomizedBlindRecordMapper.insert(record);
                }
            }
            System.out.println("fdsfdsa ");
        } catch (IOException e) {
            e.printStackTrace();
            return "error";
        }

        return "ok";
    }



    @Override
    public CommonPage<RandomizedBlindRecordVo> getRandomizedBlindPage(Long projectId, Long orgId, String batchNo, String randomizedNumber, int pageNum, int pageSize) {

        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(String.valueOf(projectId))){
            params.put("projectId", projectId);
        }
        if (StringUtils.isNotEmpty(String.valueOf(orgId))){
            params.put("orgId", orgId);
        }
        if (StringUtils.isNotEmpty(batchNo)){
            params.put("batchNo", batchNo);
        }
        if (StringUtils.isNotEmpty(randomizedNumber)){
            params.put("randomizedNumber", randomizedNumber);
        }
        List<RandomizedBlindRecordVo> res = rctsRandomizedBlindRecordMapper.getRandomizedBlindPage(params);
        for (RandomizedBlindRecordVo vo : res){
            vo.setExpandObject(JsonUtils.jsonToMap(vo.getExpand()));
        }
        return commonPageListWrapper(pageNum, pageSize, page, res);
    }
    
    @Override
    public RctsRandomizedBlindRecord getProjectRandomizedConfigNumber(String projectId, String projectOrgId, String testeeId) {
        return rctsRandomizedBlindRecordMapper.getProjectRandomizedConfigNumber(projectId, projectOrgId, testeeId);
    }
    
    @Override
    public void updateRandomizedBlindRecordBindTetsee(String projectId, String projectOrgId, String blindId, String testeeId, String randomizedNumber) {
        RctsRandomizedBlindRecord rctsRandomizedBlindRecord = getRandodmBlindRecordByBlindId(blindId);
        rctsRandomizedBlindRecord.setProjectOrgId(NumberUtil.parseLong(projectOrgId));
        rctsRandomizedBlindRecord.setBindTesteeId(testeeId);
        rctsRandomizedBlindRecord.setRandomizedNumber(randomizedNumber);
        rctsRandomizedBlindRecord.setBindRandomizedTime(new Date());
        rctsRandomizedBlindRecordMapper.updateByPrimaryKeySelective(rctsRandomizedBlindRecord);
        
        // 更新记录
        RctsTesteeInfo rctsTesteeInfo = rctsTesteeInfoMapper.getRctsTesteeInfo(NumberUtil.parseLong(projectId), NumberUtil.parseLong(projectOrgId), NumberUtil.parseLong(testeeId));
        rctsTesteeInfo.setRandomizedTime(new Date());
        rctsTesteeInfo.setRandomizedNumber(randomizedNumber);
        rctsTesteeInfo.setJoinGroupName(rctsRandomizedBlindRecord.getJoinGroupName());
        rctsTesteeInfo.setResearchStatus(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_03.getName());
        rctsTesteeInfo.setUpdateTime(new Date());
        rctsTesteeInfoMapper.updateByPrimaryKeySelective(rctsTesteeInfo);
        
        // 更新参与者基本信息
        // ProjectTesteeVo projectTesteeBaseInfo = projectTesteeInfoService.getProjectTesteeBaseInfo(projectId, testeeId);
        projectTesteeInfoService.updateProjectTesteeStatus(projectId, "", testeeId, ProjectTesteeEnum.PROJECT_TESTEE_STATUS_03.getName());
        
    }

    @Override
    public String getBlindId(Long projectId, List<String> expand) {
        return rctsRandomizedBlindRecordMapper.getBlindId(projectId, expand);
    }
    
    @Override
    public RctsRandomizedBlindRecord getRandodmBlindRecordByBlindId(String blindId) {
        return rctsRandomizedBlindRecordMapper.selectByPrimaryKey(NumberUtil.parseLong(blindId));
    }
}
