package com.haoys.user.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.mapper.ProjectTesteeSignMapper;
import com.haoys.user.model.ProjectTesteeSign;
import com.haoys.user.service.ProjectTesteeSignService;
import com.haoys.user.service.ProjectVisitUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class ProjectTesteeSignServiceImpl implements ProjectTesteeSignService {

    @Autowired
    private ProjectTesteeSignMapper projectTesteeSignMapper;
    @Autowired
    private ProjectVisitUserService projectVisitUserService;

    @Override
    public ProjectTesteeSign create(ProjectTesteeSign projectTesteeSign) {
        projectTesteeSign.setId(SnowflakeIdWorker.getUuid());
        projectTesteeSign.setCreateTime(new Date());
        projectTesteeSign.setCreateUser(SecurityUtils.getUserIdValue());
        projectTesteeSign.setTenantId(SecurityUtils.getSystemTenantId());
        projectTesteeSign.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectTesteeSign.setStatus(BusinessConfig.VALID_STATUS);
        projectTesteeSign.setTesteeCode(getNextTesteeCode(projectTesteeSign.getProjectId()));
        projectTesteeSignMapper.insertSelective(projectTesteeSign);
        return projectTesteeSign;
    }

    @Override
    public int update(ProjectTesteeSign projectTesteeSign) {
        ProjectTesteeSign projectTesteeSignValue = projectTesteeSignMapper.selectByPrimaryKey(projectTesteeSign.getId());
        BeanUtil.copyProperties(projectTesteeSign, projectTesteeSignValue);
        projectTesteeSignValue.setUpdateUser(SecurityUtils.getUserIdValue());
        projectTesteeSignValue.setUpdateTime(new Date());
        projectTesteeSignMapper.updateByPrimaryKeyWithBLOBs(projectTesteeSignValue);
        return 0;
    }

    @Override
    public int delete(Long signId) {
        return projectTesteeSignMapper.deleteByPrimaryKey(signId);
    }

    @Override
    public ProjectTesteeSign getProjectTesteeSignInfo(String projectId, String testeeCode) {
        return projectTesteeSignMapper.getProjectTesteeSign(NumberUtil.parseLong(projectId), testeeCode);
    }


    public String getNextTesteeCode(Long projectId){
        StringBuilder formattedNumber = new StringBuilder();
        String maxtesteeCode = projectVisitUserService.getMaxTesteeCodeForBoRui(projectId);
        //String maxtesteeCode = projectTesteeSignMapper.getMaxTesteeCode(projectId);
        maxtesteeCode = maxtesteeCode == null ? "0" : maxtesteeCode;
        int codeInt = Integer.valueOf(maxtesteeCode) + 1;
        formattedNumber.append(String.format("%04d", codeInt));
        return formattedNumber.toString();
    }
}
