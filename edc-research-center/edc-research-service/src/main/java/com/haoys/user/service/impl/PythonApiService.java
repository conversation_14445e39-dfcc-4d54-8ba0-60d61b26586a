package com.haoys.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.haoys.user.common.util.JsonUtils;
import com.haoys.user.config.RctsPythonScriptConfig;
import com.haoys.user.domain.vo.rcts.RandomizedParamsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PythonApiService {
    
    @Autowired
    private RctsPythonScriptConfig rctsPythonScriptConfig;
    
    public CompletableFuture<String> callStaticRandomizedPythonScriptForApi() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建执行 Python 脚本的命令
                ProcessBuilder processBuilder = new ProcessBuilder("/webserver/weilong/miniconda3/bin/python3", "/webserver/weilong/script/static_randomize.py");
                processBuilder.command("--sample_size 100");
                processBuilder.command("--batch_size 5");
                processBuilder.command("--group_ratios '{\"group_1\": 0.2,\"group_2\": 0.3,\"group_3\": 0.5}'");
                processBuilder.command("--strata_definitions '{\"Gender\": [\"Male\", \"Female\"], \"AgeGroup\": [\"Young\", \"Adult\", \"Senior\"]}'");
                processBuilder.command("--random_seed 7");
                processBuilder.command("--output_path /webserver/weilong/script/output.json");
                
                List<String> command = processBuilder.command();
                log.info("Python script command: " + String.join(" ", command));
                processBuilder.redirectErrorStream(true);

                // 启动进程并执行命令
                Process process = processBuilder.start();
                
                // 读取进程的输出流
                InputStream inputStream = process.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                
                // 读取 Python 脚本的输出
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                
                // 等待进程执行完毕
                int exitCode = process.waitFor();
                log.info("Python script exit with code: " + exitCode);
                // 返回 Python 脚本的输出作为 API 接口的返回结果
                return output.toString();
            } catch (IOException | InterruptedException e) {
                e.printStackTrace();
                return null;
            }
        });
    }
    
    public String callStaticRandomizedPythonScript() {
        StringBuilder output;
        try {
            // 构建执行 Python 脚本的命令
            // /webserver/weilong/miniconda3/bin/python3 /webserver/weilong/script/static_randomize.py
            // --sample_size 100
            // --group_ratios '{"group_1": 0.2,"group_2": 0.3,"group_3": 0.5}'
            // --strata_definitions '{"Gender": ["Male", "Female"], "AgeGroup": ["Young", "Adult", "Senior"]}'
            // --batch_size 5 --random_seed 7
            // --output_path /webserver/weilong/script/output.json
            ProcessBuilder processBuilder = new ProcessBuilder();
            
//            processBuilder.command(" --sample_size 100");
//            processBuilder.command(" --batch_size 5");
//            processBuilder.command(" --group_ratios '{\"group_1\": 0.2,\"group_2\": 0.3,\"group_3\": 0.5}'");
//            processBuilder.command(" --strata_definitions '{\"Gender\": [\"Male\", \"Female\"], \"AgeGroup\": [\"Young\", \"Adult\", \"Senior\"]}'");
//            processBuilder.command(" --random_seed 7");
//            processBuilder.command(" --output_path /webserver/weilong/script/output.json");
//            List<String> command = processBuilder.command();
//            log.info("StaticRandomized Python script command: " + JSON.toJSON(command));
//            processBuilder.redirectErrorStream(true);

            // sh tongji-c.sh 100 '{"group_1":0.2,"group_2":0.3,"group_3":0.5}' '{"Gender":["Male","Female"],"AgeGroup":["Young","Adult","Senior"]}' 5 7 /webserver/weilong/script/output.json
            List<String> list = new ArrayList<>();
            list.add("/webserver/weilong/tongji-c.sh");
            list.add("100");
            list.add("{\"group_1\":0.2,\"group_2\":0.3,\"group_3\":0.5}");
            list.add("{\"Gender\":[\"Male\",\"Female\"],\"AgeGroup\":[\"Young\",\"Adult\",\"Senior\"]}");
            list.add("5");
            list.add("7");
            list.add("/webserver/weilong/script/RandomizedPythonScript.json");
//            list.add("/webserver/weilong/miniconda3/bin/python3");
//            list.add("/webserver/weilong/script/static_randomize.py");
//            list.add("--sample_size 100");
//            list.add("--batch_size 5");
//            list.add("--group_ratios '{\"group_1\": 0.2,\"group_2\": 0.3,\"group_3\": 0.5}'");
//            list.add("--strata_definitions '{\"Gender\": [\"Male\", \"Female\"], \"AgeGroup\": [\"Young\", \"Adult\", \"Senior\"]}'");
//            list.add("--random_seed 7");
//            list.add("--output_path /webserver/weilong/script/output.json");
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.command(list).start();
            log.info("StaticRandomized Python script command: " + String.join(" ", processBuilder.command(list).command()));
            
            // 启动进程并执行命令
            //Process process = processBuilder.start();
            
            // 读取进程的输出流
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            
            // 读取 Python 脚本的输出
            output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            // 等待进程执行完毕
            Boolean exitCode = process.waitFor(3000, TimeUnit.MILLISECONDS);
            log.info("StaticRandomized Python script exit with code: " + exitCode);
            process.destroy();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 返回 Python 脚本的输出作为 API 接口的返回结果
        return output.toString();
    }



    public String callDynamicRandomizedPythonScript() {
        /*
        String[] args1=new String[]{"/home/<USER>/anaconda2/bin/python","/home/<USER>/myfile/pythonfile/helloword.py"};
        Process pr=Runtime.getRuntime().exec(args1);
        String数组里的那一行很重要
        首先一定要设置好你所使用的python的位置，切记不要直接使用python，因为系统会默认使用自带的python，所以一定要设置好你所使用的python的位置，否则可能会出现意想不到的问题（比如说我使用的是anaconda中的python，而ubuntu系统会默认调用自带的python，而我自带的python中并没有numpy库，所以会造成相应的代码不会执行的问题，所以设置好python的位置是很重要的）。还有就是要设置好py文件的位置，使用绝对路径。在这里插入代码片
        还有就是可以看出，此方法可以满足我们python代码中调用第三方库的情况，简单实用。
        */
        StringBuilder output = new StringBuilder();
        String command = "--sample_size 100 --batch_size 5 --random_seed 7 --group_ratios '{\"group_1\": 0.2,\"group_2\": 0.3,\"group_3\": 0.5}' --strata_definitions '{\"Gender\": [\"Male\", \"Female\"], \"AgeGroup\": [\"Young\", \"Adult\", \"Senior\"]}' --output_path /webserver/weilong/script/output.json";
        String[] args = new String[]{"/webserver/weilong/miniconda3/bin/python3", "/webserver/weilong/script/static_randomize.py --sample_size", command};
        try {
            log.info("DynamicRandomized Python script command: " + JSON.toJSON(args));
            Process process = Runtime.getRuntime().exec(args);
            BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = null;
            while ((line = in.readLine()) != null) {
                System.out.println(line);
                output.append(line).append("\n");
            }
            in.close();

            InputStream processErrorStream = process.getErrorStream();
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(processErrorStream));
            String errorLine = null;
            while ((errorLine = errorReader.readLine()) != null) {
                System.out.println(errorLine);
                output.append(errorLine).append("\n");
            }
            errorReader.close();
            boolean exitCode = process.waitFor(3, TimeUnit.SECONDS);
            log.info("DynamicRandomized Python script exit with code: " + exitCode);
            process.destroy();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return output.toString();
    }


    public String randomizedBlind(RandomizedParamsVo paramsVo) {
        String RandomizedGroupConfig = paramsVo.getRandomizedGroupConfig();
        String RandomizedLayerConfig = paramsVo.getRandomizedLayerConfig();
        Map<String, Object> groupMap = JsonUtils.jsonToObj(RandomizedGroupConfig, Map.class);
        List<Map<String, String>> groupConfigList = (List<Map<String, String>>) groupMap.get("list");
        Map<String, Object> groupConfigMap = new HashMap<>();
        for (Map<String, String> map : groupConfigList){
            String group = map.get("studyGrouping");
            float bl = Float.valueOf(map.get("proportion"));
            groupConfigMap.put(group, bl);
        }
        String groupConfigValue = JsonUtils.objToJson(groupConfigMap);

        Map<String, Object> layerMap = JsonUtils.jsonToObj(RandomizedLayerConfig, Map.class);
        boolean layerEnable = (boolean) layerMap.get("enabled");
        Map<String, String> m1 = new HashMap<>();
        m1.put("","");
        String layerConfigValue =JsonUtils.objToJson(m1);
        if (layerEnable){
            List<Map<String, String>> layerConfigList = (List<Map<String, String>>) layerMap.get("list");
            Map<String, Object> layerConfigMap = new HashMap<>();
            for (Map<String, String> map : layerConfigList){
                String name = map.get("layeredId");         // 分层名称 的id
                String layer = map.get("factorLevelId");    // 因素水平 的id
                String[] lis = layer.split("#");
                layerConfigMap.put(name, lis);
            }
            /*String[] lis = new String[]{"1","2","3"};
            String[] lis2 = new String[]{"4","5","6"};
            layerConfigMap.put("tes", lis);
            layerConfigMap.put("tes2", lis2);*/
            layerConfigValue = JsonUtils.objToJson(layerConfigMap);
        }
            // 构建执行 Python 脚本的命令
            /*ProcessBuilder processBuilder = new ProcessBuilder("/webserver/weilong/miniconda3/bin/python3", "/webserver/weilong/script/static_randomize.py");
            processBuilder.command(" --sample_size " + paramsVo.getJoinGroupLimit());  //
            processBuilder.command(" --group_ratios '{\"group_1\": 0.2,\"group_2\": 0.3,\"group_3\": 0.5}'");
            processBuilder.command(" --strata_definitions '{\"Gender\": [\"Male\", \"Female\"], \"AgeGroup\": [\"Young\", \"Adult\", \"Senior\"]}'");
            processBuilder.command(" --batch_size " + paramsVo.getGroupSize());   // 区组长度
            processBuilder.command(" --random_seed " + paramsVo.getRandomizedSeed());  // 随机种子
            processBuilder.command(" --pid_prefix " + paramsVo.getRandomizedFrefix());    // 随机号前缀
            processBuilder.command(" --pid_length "+ paramsVo.getRandomizedDigit());    // 随机号位数
            processBuilder.command(" --output_path /webserver/weilong/script/output.json");*/

        try {
            List<String> list = new ArrayList<>();
            ProcessBuilder processBuilder = new ProcessBuilder(rctsPythonScriptConfig.getRandom_static_script_path());
            list.add(rctsPythonScriptConfig.getRandom_static_script_path());
            list.add(String.valueOf(paramsVo.getJoinGroupLimit()));
            list.add(groupConfigValue);
            list.add(layerConfigValue);
            list.add(String.valueOf(paramsVo.getGroupSize()));
            list.add(String.valueOf(paramsVo.getRandomizedSeed()));
            list.add(rctsPythonScriptConfig.getRandom_bind_record_path() + "/" + paramsVo.getProjectId() + "_RandomizedPythonScript.json");
            list.add(paramsVo.getRandomizedFrefix());
            list.add(String.valueOf(paramsVo.getRandomizedDigit()));
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.command(list).start();
            log.info("StaticRandomized Python script command: " + String.join(" ", processBuilder.command(list).command()));
            // 读取进程的输出流
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));

            // 读取 Python 脚本的输出
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            // 等待进程执行完毕
            Boolean exitCode = process.waitFor(3000, TimeUnit.MILLISECONDS);
            log.info("StaticRandomized Python script exit with code: " + exitCode);
            process.destroy();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 返回 Python 脚本的输出作为 API 接口的返回结果
        return rctsPythonScriptConfig.getRandom_bind_record_path() + "/" + paramsVo.getProjectId() + "_RandomizedPythonScript.json";
    }
    
    public String randomizedDynamicConfigBlind(RandomizedParamsVo randomizedParamsVo) {
        StringBuilder output;
        String RandomizedGroupConfig = randomizedParamsVo.getRandomizedGroupConfig();
        String RandomizedLayerConfig = randomizedParamsVo.getRandomizedLayerConfig();
        Map<String, Object> groupMap = JsonUtils.jsonToObj(RandomizedGroupConfig, Map.class);
        List<Map<String, String>> groupConfigList = (List<Map<String, String>>) groupMap.get("list");
        Map<String, Object> groupConfigMap = new HashMap<>();
        for (Map<String, String> map : groupConfigList){
            String group = map.get("studyGrouping");
            float bl = Float.valueOf(map.get("proportion"));
            groupConfigMap.put(group, bl);
        }
        String groupConfigStr = JsonUtils.objToJson(groupConfigMap);
        
        Map<String, Object> layerMap = JsonUtils.jsonToObj(RandomizedLayerConfig, Map.class);
        boolean layerEnable = (boolean) layerMap.get("enabled");
        String layerConfigStr ="{}";
        if (layerEnable){
            List<Map<String, String>> layerConfigList = (List<Map<String, String>>) layerMap.get("list");
            Map<String, Object> layerConfigMap = new HashMap<>();
            for (Map<String, String> map : layerConfigList){
                String name = map.get("layeredNames");
                String layer = map.get("factorLevel");
                String[] lis = layer.split(",");
                layerConfigMap.put(name, lis);
            }
            layerConfigStr = JsonUtils.objToJson(layerConfigMap);
        }
        // 构建执行 Python 脚本的命令
        try {
            ProcessBuilder processBuilder = new ProcessBuilder();
            List<String> paramList = new ArrayList<>();
            paramList.add(rctsPythonScriptConfig.getRandom_dynamic_script_path());
            paramList.add(String.valueOf(randomizedParamsVo.getJoinGroupLimit()));
            paramList.add(groupConfigStr);
            paramList.add(layerConfigStr);
            paramList.add(String.valueOf(randomizedParamsVo.getGroupSize()));  // batch_size 区组长度
            paramList.add(String.valueOf(randomizedParamsVo.getRandomizedSeed()));  // random_seed  随机种子
            paramList.add(rctsPythonScriptConfig.getRandom_bind_record_path().concat("/") + randomizedParamsVo.getProjectId() + "_RandomizedDynamicPythonScript.json");
            
            processBuilder.redirectErrorStream(true);
            Process process = processBuilder.command(paramList).start();
            log.info("DynamicRandomized Python script command: " + String.join(" ", processBuilder.command(paramList).command()));
            // 读取进程的输出流
            InputStream inputStream = process.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            
            // 读取 Python 脚本的输出
            output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            // 等待进程执行完毕
            Boolean exitCode = process.waitFor(3000, TimeUnit.MILLISECONDS);
            log.info("DynamicRandomized Python script exit with code: " + exitCode);
            process.destroy();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 返回 Python 脚本的输出作为 API 接口的返回结果
        return output.toString();
    }

}

