package com.haoys.user.mapper;

import com.haoys.user.model.SystemChatRecord;
import com.haoys.user.model.SystemChatRecordExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SystemChatRecordMapper {
    long countByExample(SystemChatRecordExample example);

    int deleteByExample(SystemChatRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemChatRecord record);

    int insertSelective(SystemChatRecord record);

    List<SystemChatRecord> selectByExample(SystemChatRecordExample example);

    SystemChatRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemChatRecord record, @Param("example") SystemChatRecordExample example);

    int updateByExample(@Param("record") SystemChatRecord record, @Param("example") SystemChatRecordExample example);

    int updateByPrimaryKeySelective(SystemChatRecord record);

    int updateByPrimaryKey(SystemChatRecord record);

    SystemChatRecord getSystemChatRecordByTopic(String topic, String createUserId);

    List<SystemChatRecord> getChatContentList(String topic, String modelType, String createUserId);

}