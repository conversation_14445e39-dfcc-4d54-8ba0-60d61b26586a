package com.haoys.user.domain.wrapper;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectUserInfoWrapper {

    private String projectId;
    private String projectOrgId;
    private String systemUserId;
    private String userName;

    private List<ProjectOrgRoleInfo> projectOrgRoleList = new ArrayList<>();

    @Data
    public static class ProjectOrgRoleInfo {
        private String roleId;
        private String roleName;
        private String roleCode;
    }

}
