package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.dto.ProjectUserExportDto;
import com.haoys.user.domain.dto.ProjectUserParam;
import com.haoys.user.domain.param.auth.ProjectUserAuthParam;
import com.haoys.user.domain.param.project.ProjectUserQueryParam;
import com.haoys.user.domain.vo.project.ProjectUserOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectUserInfo;

import java.util.List;
import java.util.Map;

public interface ProjectUserService {

    /**
     * 查询项目成员分页列表
     * @param projectUserQueryParam
     * @return
     */
    List<ProjectUserVo> selectProjectUserListForPage(ProjectUserQueryParam projectUserQueryParam);

    /**
     * 按照条件查询项目成员列表
     * @param projectId
     * @param orgId
     * @param departmentId
     * @param realName
     * @param roleIds
     * @param projectLeader
     * @return
     */
    List<ProjectUserVo> selectProjectUserList(String projectId, String orgId, String departmentId, String realName, String mobile, String roleIds, String projectLeader);

    /**
     * 添加项目成员-项目研究中心授权
     * @param projectUserAuthParam
     * @return
     */
    CustomResult saveProjectUserInfo(ProjectUserAuthParam projectUserAuthParam);

    /**
     * 删除项目用户信息
     * @param projectId 项目ID
     * @param userId    用户ID
     * @param forceDelete
     * @return 结果
     */
    String deleteProjectUser(String projectId, String userId, boolean forceDelete);

    /**
     * 修改项目成员管理信息
     * @param projectUserParam
     * @return 结果
     */
    CustomResult updateProjectUserInfo(ProjectUserParam projectUserParam);

    /**
     * 修改项目成员管理信息
     * @param projectUserInfo
     * @return 结果
     */
    Integer updateProjectUserInfoById(ProjectUserInfo projectUserInfo);

    /**
     * 导入项目成员
     * @param projectUserParam
     * @param projectId
     * @param errorList 错误信息
     * @return
     */
    String saveBatchProjectUser(List<ProjectUserParam> projectUserParam, Long projectId, List<ProjectUserExportDto> errorList);

    /**
     * 设置项目负责人
     *
     * @param projectId  项目id
     * @param userId     用户id
     * @param tenantId
     * @param platformId
     * @return
     */
    String saveProjectManageUserInfo(String projectId, String userId, String tenantId, String platformId);

    /**
     * 根据用户id查询项目角色
     * @param projectId
     * @param orgId
     * @param userId
     * @return
     */
    ProjectUserInfoWrapper getProjectUserRoleInfoByUserId(String projectId, String orgId, String userId);

    /**
     * 删除项目成员研究者等
     * @param projectId
     * @param userId
     * @param roleCode
     * @return
     */
    String deleteProjectUserByRoleCode(String projectId, String userId, String roleCode);

    /**
     * 查询用户参与项目
     * @param userId
     * @return
     */
    List<ProjectApplyUser> getProjectUserResultByUserId(String userId);

    /**
     * 项目用户查询详情
     * @param projectId 项目id
     * @param userId 项目用户id
     * @return 用户详情
     */
    ProjectUserVo getProjectUserInfo(String projectId, String userId);

    /**
     * 查询项目用户基本信息
     * @param projectId
     * @param userId
     * @return
     */
    ProjectUserInfo getProjectUserInfoByUserId(String projectId, String userId);

    /**
     * 根据项目id和用户id更新激活状态和数据状态
     * @return
     */
    int updateStatus(String projectId,String userId);


    /**
     * 加入项目（被邀请人加入项目）
     * @param projectId
     * @return
     */
    CommonResult joinProject(String projectId);
    /**
     * 拒绝加入项目（拒绝被邀请人加入项目）
     * @param projectId
     * @return
     */
    CommonResult refuseJoinProject(String projectId);

    /**
     * 被邀请列表。
     * @param projectUserQueryParam
     * @return
     */
    CommonPage<ProjectUserVo> list(ProjectUserQueryParam projectUserQueryParam);

    /**
     * 查询系统用户项目列表
     * @param systemUserId
     * @return
     */
    List<ProjectUserInfo> getProjectUserListByUserId(String systemUserId);

    /**
     * 查询项目角色和研究中心列表
     * @param projectIds
     * @return
     */
    Map<String, Object> getProjectRoleListAndOrgList(List<Long> projectIds);
    
    
    List<ProjectUserOrgVo> getProjectUserJoinOrgListByUserId(String projectId, String userId);
    
    void saveAuthorizeProjectUser(String projectId, String userId, String projectOrgId, String loginTenantId, String loginPlatformId);
}
