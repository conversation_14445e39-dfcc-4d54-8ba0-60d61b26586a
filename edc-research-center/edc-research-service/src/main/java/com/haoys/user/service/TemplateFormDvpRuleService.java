package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.model.TemplateFormDvpRule;

import java.util.List;
import java.util.Map;

/**
 *逻辑配置service
 */
public interface TemplateFormDvpRuleService {

    /**
     * 新增
     * @param dvpRule 逻辑配置信息
     * @return
     */
    CommonResult<Object> add(TemplateFormDvpRule dvpRule);

    /**
     * 编辑
     * @param dvpRule 逻辑配置信息
     * @return
     */
    CommonResult<Object> modify(TemplateFormDvpRule dvpRule);


    /**
     * 启用和停用
     * @param ruleId 规则ID
     * @return
     */
    CommonResult<Object> enable(Long ruleId);

    /**
     * 列表
     *
     * @param pageNum   页数
     * @param pageSize  每页数量
     * @param ruleType 触发类型
     * @param checkName 逻辑名称
     * @param projectId
     * @return
     */
    CommonPage<Object> list(Integer pageNum, Integer pageSize, String ruleType, String checkName, String projectId);

    /**
     * 删除
     * @param ruleId 规则ID
     * @return
     */
    CommonResult<Object> remove(Long ruleId);

    /**
     * 表单字段规则查询
     * @param dvpRule
     * @return
     */
    List<TemplateFormDvpRule> searchRules(TemplateFormDvpRule dvpRule);
    /**
     * 公式计算
     * @param rule 规则
     * @param data 参与计算的数据
     * @return
     */
    Object compute(String rule, Map<String, Object> data);

}
