package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.testee.ProjectTesteeFillInfoParam;
import com.haoys.user.domain.param.testee.ProjectTesteeStatistVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeImageInfoVo;

public interface ProjectTesteeInfoFillService {
    /**
     * 获取参与者管理-图形信息
     * @param param 搜索参数
     * @return 参与者图形信息列表
     */
    CommonResult<ProjectTesteeImageInfoVo> getFillInformationList(ProjectTesteeFillInfoParam param);

    /**
     * 获取参与者管理-状态统计
     *
     * @param projectId 项目id
     * @param orgId 研究中心的id
     * @return
     */
    CommonResult<ProjectTesteeStatistVo> testeeStatist(Long projectId, Long orgId);

}
