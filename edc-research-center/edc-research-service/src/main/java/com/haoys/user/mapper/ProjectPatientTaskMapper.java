package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPatientTask;
import com.haoys.user.model.ProjectPatientTaskExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPatientTaskMapper {
    long countByExample(ProjectPatientTaskExample example);

    int deleteByExample(ProjectPatientTaskExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPatientTask record);

    int insertSelective(ProjectPatientTask record);

    List<ProjectPatientTask> selectByExample(ProjectPatientTaskExample example);

    ProjectPatientTask selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPatientTask record, @Param("example") ProjectPatientTaskExample example);

    int updateByExample(@Param("record") ProjectPatientTask record, @Param("example") ProjectPatientTaskExample example);

    int updateByPrimaryKeySelective(ProjectPatientTask record);

    int updateByPrimaryKey(ProjectPatientTask record);
}