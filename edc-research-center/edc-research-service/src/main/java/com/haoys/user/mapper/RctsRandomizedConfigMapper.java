package com.haoys.user.mapper;

import com.haoys.user.domain.vo.rcts.RandomizedVo;
import com.haoys.user.model.RctsRandomizedConfig;
import com.haoys.user.model.RctsRandomizedConfigExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface RctsRandomizedConfigMapper {
    long countByExample(RctsRandomizedConfigExample example);

    int deleteByExample(RctsRandomizedConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RctsRandomizedConfig record);

    int insertSelective(RctsRandomizedConfig record);

    List<RctsRandomizedConfig> selectByExample(RctsRandomizedConfigExample example);

    RctsRandomizedConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RctsRandomizedConfig record, @Param("example") RctsRandomizedConfigExample example);

    int updateByExample(@Param("record") RctsRandomizedConfig record, @Param("example") RctsRandomizedConfigExample example);

    int updateByPrimaryKeySelective(RctsRandomizedConfig record);

    int updateByPrimaryKey(RctsRandomizedConfig record);

    int getRandomizedConfigCountCheck(Map<String, Object> params);

    List<RandomizedVo> getRandomizedConfig(@Param("projectId")String projectId, @Param("status") String status);
}