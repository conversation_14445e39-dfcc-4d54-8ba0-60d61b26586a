package com.haoys.user.mapper;

import com.haoys.user.domain.vo.rcts.DrugDistributeVo;
import com.haoys.user.model.RctsDrugDistributeManage;
import com.haoys.user.model.RctsDrugDistributeManageExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface RctsDrugDistributeManageMapper {
    long countByExample(RctsDrugDistributeManageExample example);

    int deleteByExample(RctsDrugDistributeManageExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RctsDrugDistributeManage record);

    int insertSelective(RctsDrugDistributeManage record);

    List<RctsDrugDistributeManage> selectByExample(RctsDrugDistributeManageExample example);

    RctsDrugDistributeManage selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RctsDrugDistributeManage record, @Param("example") RctsDrugDistributeManageExample example);

    int updateByExample(@Param("record") RctsDrugDistributeManage record, @Param("example") RctsDrugDistributeManageExample example);

    int updateByPrimaryKeySelective(RctsDrugDistributeManage record);

    int updateByPrimaryKey(RctsDrugDistributeManage record);

    List<DrugDistributeVo> getDrugDistributePage(Map<String, Object> params);

}