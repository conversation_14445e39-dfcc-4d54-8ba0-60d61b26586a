package com.haoys.user.mapper;

import com.haoys.user.model.SystemPointLog;
import com.haoys.user.model.SystemPointLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SystemPointLogMapper {
    long countByExample(SystemPointLogExample example);

    int deleteByExample(SystemPointLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SystemPointLog record);

    int insertSelective(SystemPointLog record);

    List<SystemPointLog> selectByExample(SystemPointLogExample example);

    SystemPointLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SystemPointLog record, @Param("example") SystemPointLogExample example);

    int updateByExample(@Param("record") SystemPointLog record, @Param("example") SystemPointLogExample example);

    int updateByPrimaryKeySelective(SystemPointLog record);

    int updateByPrimaryKey(SystemPointLog record);
}