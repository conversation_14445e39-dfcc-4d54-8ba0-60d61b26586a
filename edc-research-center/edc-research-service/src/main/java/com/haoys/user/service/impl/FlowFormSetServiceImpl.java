package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.expand.FlowFormSetValueExpand;
import com.haoys.user.domain.param.flow.FlowEditPerParam;
import com.haoys.user.domain.param.flow.FlowFormExpandParam;
import com.haoys.user.domain.param.flow.FlowFormSetParam;
import com.haoys.user.domain.vo.FlowFormSetVo;
import com.haoys.user.domain.vo.FlowFormVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.flow.FlowOrderFormVo;
import com.haoys.user.domain.vo.flow.ProjectFlowFormVo;
import com.haoys.user.mapper.FlowFormSetExpandMapper;
import com.haoys.user.mapper.FlowFormSetMapper;
import com.haoys.user.mapper.ProjectVisitConfigMapper;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowFormSetExample;
import com.haoys.user.model.FlowFormSetExpand;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitConfigExample;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.TemplateConfigService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class FlowFormSetServiceImpl extends BaseService implements FlowFormSetService {

    @Autowired
    private FlowFormSetMapper flowFormSetMapper;
    @Autowired
    private FlowFormSetExpandMapper flowFormSetExpandMapper;
    @Autowired
    private ProjectVisitConfigMapper projectVisitConfigMapper;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private FlowPlanService flowPlanService;

    @Override
    public CommonResult<Object> save(List<FlowFormSetParam> params) {
        if (CollectionUtil.isNotEmpty(params)) {
            FlowFormSetParam set0 = params.get(0);
            // 根据计划id删除该计划的流程和表单的配置。
            FlowFormSetExample example = new FlowFormSetExample();
            FlowFormSetExample.Criteria criteria = example.createCriteria();
            criteria.andPlanIdEqualTo(set0.getPlanId());
            flowFormSetMapper.deleteByExample(example);
            List<FlowFormSet> insertList = new ArrayList<>(params.size());
            params.forEach(formSet -> {
                if (CollectionUtil.isNotEmpty(formSet.getFlowIds())){
                    FlowFormSet set = getFlowFormSet(formSet);
                    insertList.add(set);
                }
            });
            if (insertList.size()>0){
                int i = flowFormSetMapper.batchSave(insertList);
                if (i>0){
                    return CommonResult.success(null);
                }
            }
        }
        return CommonResult.failed();
    }

    /**
     * 单个保存
     * @param param 表单和流程的关联参数
     * @return
     */
    @Override
    public CommonResult<Object> saveOne(FlowFormSetParam param) {
        FlowFormSet set = getFlowFormSet(param);
        TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(param.getFormId());
        set.setXh(templateFormConfig.getSort());
        // 根据项目id，流程id，计划id，表单id 获取绑定信息
        FlowFormSetExample example = new FlowFormSetExample();
        FlowFormSetExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(param.getProjectId());
        criteria.andPlanIdEqualTo(param.getPlanId());
        criteria.andVisitIdEqualTo(param.getFlowId());
        criteria.andFormIdEqualTo(param.getFormId());
        flowFormSetMapper.deleteByExample(example);
        int insert = flowFormSetMapper.insert(set);
        return insert>0?CommonResult.success(null):CommonResult.failed();
    }

    /**
     * 取消
     * @param param 表单和流程的关联参数
     * @return 取消结果
     */

    @Override
    public CommonResult<Object> delete(FlowFormSetParam param) {
        FlowFormSetExample example = new FlowFormSetExample();
        FlowFormSetExample.Criteria criteria = example.createCriteria();
        criteria.andPlanIdEqualTo(param.getPlanId());
        criteria.andFormIdEqualTo(param.getFormId());
        criteria.andVisitIdEqualTo(param.getFlowId());
        int del = flowFormSetMapper.deleteByExample(example);
        return del>0?CommonResult.success(null):CommonResult.failed();
    }

    /**
     * 修改表单pc和移动端的权限
     * @param formSet 参数
     * @return 结果
     */
    @Override
    public CommonResult<Object> updateFlowFormAuth(FlowFormSet formSet) {

        FlowFormSet flowFormSet = flowFormSetMapper.selectByPrimaryKey(formSet.getId());
        if (flowFormSet!=null){
            flowFormSet.setMoRoAuth(formSet.getMoRoAuth());
            flowFormSet.setMoRwAuth(formSet.getMoRwAuth());
            flowFormSet.setPcRoAuth(formSet.getPcRoAuth());
            flowFormSet.setPcRwAuth(formSet.getPcRwAuth());
            int update = flowFormSetMapper.updateByPrimaryKey(flowFormSet);
            return update>0?CommonResult.success(null):CommonResult.failed();
        }
        return CommonResult.failed();
    }

    /**
     * 表单排序-保存
     * @return 保存结果
     */
    @Override
    public CommonResult<Object> saveOrderForm(List<String> formSetIds) {
        if (CollectionUtil.isNotEmpty(formSetIds)) {
            for (int i = 0; i <formSetIds.size(); i++) {
                FlowFormSet formSet = flowFormSetMapper.selectByPrimaryKey(Long.parseLong(formSetIds.get(i)));
                formSet.setXh(i);
                flowFormSetMapper.updateByPrimaryKey(formSet);
            }
        }
        return CommonResult.success(null);
    }

    /**
     * 获取配置研究流程的列表
     * @param projectId 项目id
     * @param planId 流程id
     * @return 列表信息
     */
    @Override
    public CommonResult<List<FlowFormSetVo>> list(Long projectId, Long planId) {
        // 获取表单信息
        List<FlowFormSetVo> list = new ArrayList<>();
        List<TemplateFormConfigVo> forms =
                templateConfigService.getTemplateFormConfigListByPlanId(null, projectId.toString(), planId.toString(), null, null,true, false);
        if (CollectionUtil.isNotEmpty(forms)){

            // 获取所有的流程信息
            ProjectVisitConfigExample example0 = new ProjectVisitConfigExample();
            example0.setOrderByClause("sort asc");
            ProjectVisitConfigExample.Criteria criteria0 = example0.createCriteria();
            criteria0.andProjectIdEqualTo(projectId);
            criteria0.andPlanIdEqualTo(planId);
            List<ProjectVisitConfig> flowList = projectVisitConfigMapper.selectByExample(example0);
            if (CollectionUtil.isNotEmpty(flowList)){
                // 根据项目id和计划id 获取所有已经绑定的流程信息
                FlowFormSetExample example = new FlowFormSetExample();
                FlowFormSetExample.Criteria criteria = example.createCriteria();
                criteria.andProjectIdEqualTo(projectId);
                criteria.andPlanIdEqualTo(planId);
                List<FlowFormSet> flowFormSets = flowFormSetMapper.selectByExample(example);
                // 流程信息根据表单id进行分组
                Map<Long, List<FlowFormSet>> collect = flowFormSets.stream().collect(Collectors.groupingBy(FlowFormSet::getFormId));
                for (TemplateFormConfigVo form : forms) {
//                    if(f)
                    FlowFormSetVo setVo = new FlowFormSetVo();
                    setVo.setFormId(Long.parseLong(form.getFormId()));
                    setVo.setFormName(form.getFormName());
                    List<FlowFormSet> formSets = collect.get(Long.parseLong(form.getFormId()));
                    // 根据流程id进行分组
                    Map<Long, List<FlowFormSet>> listMap =  new HashMap<>();
                    if (CollectionUtil.isNotEmpty(formSets)){
                        listMap = formSets.stream().collect(Collectors.groupingBy(FlowFormSet::getVisitId));
                    }

                    List<FlowFormVo> formVoList = new ArrayList<>();
                    for (ProjectVisitConfig config : flowList) {
                        // 构建FlowFormVo 对象，如果listMap中没有对应的流程id说明没有选中
                        FlowFormVo flowFormVo = new FlowFormVo();
                        flowFormVo.setFlowId(config.getId());
                        flowFormVo.setFlowName(config.getVisitName());
                        flowFormVo.setCheck(listMap.get(config.getId())!=null);
                        formVoList.add(flowFormVo);
                    }
                    setVo.setFlowFormVos(formVoList);
                    list.add(setVo);
                }
            }
        }
        return CommonResult.success(list);
    }
    /**
     * 获取配置研究流程的列表
     * @param flowId 流程id
     * @return
     */
    @Override
    public CommonResult<FlowOrderFormVo> listByFlowId(Long flowId) {
        FlowOrderFormVo vo = new FlowOrderFormVo();
        // 获取列表
        List<FlowFormSetValueExpand> flowFormSets = flowFormSetMapper.getFlowFormSetValueExpandByFlowId(flowId);
        vo.setList(flowFormSets);
        if (CollectionUtil.isNotEmpty(flowFormSets)){
            FlowFormSet formSet = flowFormSets.get(0);
            // 获取是否选中 pc可编辑可查看移动端可查看可编辑
            // pc可查看默认全部选中，所以无需查询
            // pc可编辑
           long pcRwCount = flowFormSets.stream().filter(FlowFormSet::getPcRwAuth).count();
            vo.setAllPcRoCheck(true);
            vo.setAllPcRwCheck(flowFormSets.size()==pcRwCount);
            // 移动端可查看
            long moRoCount = flowFormSets.stream().filter(FlowFormSet::getMoRoAuth).count();
            vo.setAllMoRoCheck(flowFormSets.size()==moRoCount);
            // 移动端可编辑
            long moRwCount = flowFormSets.stream().filter(FlowFormSet::getMoRwAuth).count();
            vo.setAllMoRwCheck(flowFormSets.size()==moRwCount);
        }
        return CommonResult.success(vo);
    }

    @Override
    public CommonResult<Object> editAllPer(FlowEditPerParam param) {
        // 根据项目id和流程id获取绑定的表单数据。
        FlowFormSetExample example = new FlowFormSetExample();
        FlowFormSetExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(param.getProjectId());
        criteria.andVisitIdEqualTo(param.getFlowId());
        long count = flowFormSetMapper.countByExample(example);
        if (count>0){
            FlowFormSet formSet = new FlowFormSet();
            formSet.setProjectId(param.getProjectId());
            formSet.setVisitId(param.getFlowId());
            if (Objects.equals(param.getType(), "pcShowPer")){
                formSet.setPcRoAuth(param.getPcShowPer());
            }else if (Objects.equals(param.getType(), "pcEditPer")){
                formSet.setPcRwAuth(param.getPcEditPer());
            }else if (Objects.equals(param.getType(), "mobShowPer")){
                formSet.setMoRoAuth(param.getMobShowPer());
            }else if (Objects.equals(param.getType(), "mobEditPer")){
                formSet.setMoRwAuth(param.getMobEditPer());
            }
            flowFormSetMapper.UpdatePer(formSet);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    @Override
    public List<ProjectFlowFormVo> listByFlowIdAndFilterPer(FlowFormSet formSet) {
        return flowFormSetMapper.listByFlowIdAndFilterPer(formSet);
    }

    @Override
    public List<FlowFormSet> selectByExample(FlowFormSetExample example) {
        return flowFormSetMapper.selectByExample(example);
    }

    @Override
    public CommonResult<List<FlowFormSet>> getFormByFlowId(Long flowId) {
        return CommonResult.success(flowFormSetMapper.listByFlowId(flowId));
    }
    
    /**
     * @param flowId
     * @return
     */
    @Override
    public List<FlowFormSet> getProjectVisitListByVisitId(String flowId) {
        return flowFormSetMapper.listByFlowId(NumberUtil.parseLong(flowId));
    }
    
    @Override
    public Long countByExample(FlowFormSetExample example) {
        return flowFormSetMapper.countByExample(example);
    }

    @Override
    public FlowFormSetValueExpand getFormConfigListByProjectIdAndFormId(String projectId, String planId, String visitId, String formId) {
        return flowFormSetMapper.getFormConfigListByProjectIdAndFormId(projectId, planId, visitId, formId);
    }

    @Override
    public void updateFlowFormBaseInfo(FlowFormSet flowFormSet) {
        flowFormSetMapper.updateByPrimaryKey(flowFormSet);
    }

    @Override
    public CommonResult<Object> saveFormExpand(FlowFormExpandParam flowFormExpandParam) {
        FlowFormSetExpand flowFormSetExpand = new FlowFormSetExpand();
        BeanUtils.copyProperties(flowFormExpandParam, flowFormSetExpand);
        if(flowFormExpandParam.getPlanId() == null){
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(flowFormExpandParam.getProjectId().toString());
            if(flowPlanInfo != null){
                flowFormSetExpand.setPlanId(flowPlanInfo.getId());
            }
        }
        flowFormSetExpand.setId(SnowflakeIdWorker.getUuid());
        flowFormSetExpand.setTemplateCopy(false);
        flowFormSetExpand.setStatus(BusinessConfig.VALID_STATUS);
        flowFormSetExpand.setCreateTime(new Date());
        flowFormSetExpand.setCreateUser(SecurityUtils.getUserIdValue());
        flowFormSetExpand.setTenantId(SecurityUtils.getSystemTenantId());
        flowFormSetExpand.setPlatformId(SecurityUtils.getSystemPlatformId());
        flowFormSetExpandMapper.insert(flowFormSetExpand);
        return CommonResult.success(flowFormSetExpand);
    }

    @Override
    public CommonResult<Object> deleteFormExpand(FlowFormExpandParam flowFormExpandParam) {
        return null;
    }

    @Override
    public List<FlowFormSetExpand> getFormSetExpandList(String projectId, String planId, String visitId, String formId, String testeeId) {
        return flowFormSetExpandMapper.getFormSetExpandList(projectId, planId, visitId, formId, testeeId);
    }

    @Override
    public void saveFormExpandObject(FlowFormSetExpand flowFormSetExpand) {
        flowFormSetExpandMapper.insert(flowFormSetExpand);
    }

    @Override
    public FlowFormSetExpand getFormSetExpandTemplateCopy(String projectId, String planId, String visitId, String formId, String testeeId) {
        return flowFormSetExpandMapper.getFormSetExpandTemplateCopy(projectId, planId, visitId, formId, testeeId);
    }

    @Override
    public FlowFormSetExpand getFormExpandByFormSetId(String projectId, String formSetId, String testeeId) {
        return flowFormSetExpandMapper.getFormExpandByFormSetId(projectId, formSetId, testeeId);
    }

    @Override
    public void insertflowFormSetExpandForTemplate(String projectId, String planId, String visitId, String formId, String testeeId, String systemTenantId, String systemPlatformId) {
        FlowFormSetValueExpand flowFormSet = this.getFormConfigListByProjectIdAndFormId(projectId, planId, visitId, formId);
        TemplateFormConfigVo templateFormConfigVo = templateConfigService.getTemplateFormConfigBaseInfoByFormId(formId);
        if(BusinessConfig.TEMPLATE_CONFIG_INPUT_TYPE_2.equals(templateFormConfigVo.getFormType())){
            FlowFormSetExpand flowFormSetExpandObject = this.getFormSetExpandTemplateCopy(projectId, planId, visitId, formId, testeeId);
            if(flowFormSetExpandObject == null){
                FlowFormSetExpand flowFormSetExpand = new FlowFormSetExpand();
                BeanUtils.copyProperties(flowFormSet,flowFormSetExpand);
                flowFormSetExpand.setId(SnowflakeIdWorker.getUuid());
                flowFormSetExpand.setOwnerBaseId(flowFormSet.getId());
                flowFormSetExpand.setTemplateCopy(true);
                flowFormSetExpand.setCreateTime(new Date());
                flowFormSetExpand.setCreateUser(SecurityUtils.getUserIdValue());
                flowFormSetExpand.setUpdateTime(null);
                flowFormSetExpand.setUpdateUser(null);
                flowFormSetExpand.setTenantId(systemTenantId);
                flowFormSetExpand.setPlatformId(systemPlatformId);
                this.saveFormExpandObject(flowFormSetExpand);
            }
        }
    }

    /**
     * 构建一个对象
     * @param formSet
     * @return
     */
    @NotNull
    private static FlowFormSet getFlowFormSet(FlowFormSetParam formSet) {
        FlowFormSet set = new FlowFormSet();
        set.setId(SnowflakeIdWorker.getUuid());
        set.setProjectId(formSet.getProjectId());
        set.setPlanId(formSet.getPlanId());
        set.setFormId(formSet.getFormId());
        set.setVisitId(formSet.getFlowId());
        set.setCreateUser(SecurityUtils.getUserIdValue());
        set.setUpdateUser(SecurityUtils.getUserIdValue());
        set.setUpdateTime(new Date());
        set.setCreateTime(new Date());
        set.setMoRoAuth(false);
        set.setMoRwAuth(false);
        set.setPcRoAuth(true);
        set.setPcRwAuth(false);
        set.setTenantId(SecurityUtils.getSystemTenantId());
        set.setPlatformId(SecurityUtils.getSystemPlatformId());
        set.setStatus(BusinessConfig.VALID_STATUS);
        return set;
    }
}
