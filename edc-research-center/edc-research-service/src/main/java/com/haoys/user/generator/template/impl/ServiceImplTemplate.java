package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import java.util.Map;

/**
 * Service实现类模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class ServiceImplTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String serviceImplPackage = (String) context.get("serviceImplPackage");
        String servicePackage = (String) context.get("servicePackage");
        String mapperPackage = (String) context.get("mapperPackage");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String serviceName = (String) context.get("serviceName");
        String serviceImplName = (String) context.get("serviceImplName");
        String mapperName = (String) context.get("mapperName");
        String entityNameLowerCase = (String) context.get("entityNameLowerCase");
        String primaryKeyType = (String) context.get("primaryKeyType");
        String primaryKeyProperty = (String) context.get("primaryKeyProperty");
        String currentDate = (String) context.get("currentDate");
        boolean enableCache = (Boolean) context.get("enableCache");
        
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(serviceImplPackage).append(";\n\n");
        
        // 导入语句
        sb.append("import ").append(modelPackage).append(".").append(entityName).append(";\n");
        sb.append("import ").append(servicePackage).append(".").append(serviceName).append(";\n");
        sb.append("import ").append(mapperPackage).append(".").append(mapperName).append(";\n");
        sb.append("import com.haoys.user.common.api.CommonPage;\n");
        sb.append("import com.haoys.user.common.api.CommonResult;\n");
        sb.append("import com.haoys.user.common.util.SecurityUtils;\n");
        sb.append("import lombok.extern.slf4j.Slf4j;\n");
        sb.append("import org.springframework.beans.factory.annotation.Autowired;\n");
        sb.append("import org.springframework.stereotype.Service;\n");
        sb.append("import org.springframework.transaction.annotation.Transactional;\n");
        if (enableCache) {
            sb.append("import org.springframework.cache.annotation.CacheEvict;\n");
            sb.append("import org.springframework.cache.annotation.Cacheable;\n");
            sb.append("import org.springframework.cache.annotation.CachePut;\n");
        }
        // 根据表字段动态添加导包
        if (tableInfo.hasDateColumns()) {
            sb.append("import java.util.Date;\n");
        }
        sb.append("import java.util.ArrayList;\n");
        sb.append("import java.util.List;\n");
        sb.append("import java.util.Map;\n");
        sb.append("import java.util.HashMap;\n");
        sb.append("import com.github.pagehelper.PageHelper;\n\n");
        
        // 类注释
        sb.append("/**\n");
        sb.append(" * ").append(tableInfo.getTableDescription()).append("Service实现类\n");
        sb.append(" * 提供完整的CRUD操作、批量操作、复杂查询、缓存管理等功能\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" * @version 2.0.0\n");
        sb.append(" * @since 1.0.0\n");
        sb.append(" * @date ").append(currentDate).append("\n");
        sb.append(" */\n");
        
        // 类注解
        sb.append("@Slf4j\n");
        sb.append("@Service\n");
        sb.append("@Transactional(rollbackFor = Exception.class)\n");
        
        // 类声明
        sb.append("public class ").append(serviceImplName).append(" implements ").append(serviceName).append(" {\n\n");
        
        // 缓存名称常量
        if (enableCache) {
            sb.append("    private static final String CACHE_NAME = \"").append(entityNameLowerCase).append("Cache\";\n");
            sb.append("    private static final String CACHE_KEY = \"'").append(entityNameLowerCase).append(":' + #id\";\n");
            sb.append("    private static final String CACHE_KEY_ALL = \"'").append(entityNameLowerCase).append(":all'\";\n\n");
        }
        
        // 注入Mapper
        sb.append("    @Autowired\n");
        sb.append("    private ").append(mapperName).append(" ").append(entityNameLowerCase).append("Mapper;\n\n");
        
        // 基础CRUD操作
        generateBasicCrudMethods(sb, entityName, entityNameLowerCase, primaryKeyType, primaryKeyProperty, enableCache, tableInfo);

        // 批量操作方法
        generateBatchMethods(sb, entityName, entityNameLowerCase, primaryKeyType, enableCache, tableInfo);

        // 条件查询方法
        generateQueryMethods(sb, entityName, entityNameLowerCase, enableCache);

        // 数据校验方法
        generateValidationMethods(sb, entityName, entityNameLowerCase, primaryKeyType, primaryKeyProperty, tableInfo);

        sb.append("}\n");
        
        return sb.toString();
    }
    
    /**
     * 检查表中是否存在指定字段
     */
    private static boolean hasColumn(TableInfo tableInfo, String columnName) {
        return tableInfo.getColumns().stream()
                .anyMatch(column -> columnName.equals(column.getColumnName()));
    }

    /**
     * 检查表中是否存在指定Java属性
     */
    private static boolean hasProperty(TableInfo tableInfo, String propertyName) {
        return tableInfo.getColumns().stream()
                .anyMatch(column -> propertyName.equals(column.getJavaProperty()));
    }

    private static void generateBasicCrudMethods(StringBuilder sb, String entityName, String entityNameLowerCase,
                                               String primaryKeyType, String primaryKeyProperty, boolean enableCache, TableInfo tableInfo) {
        // 创建方法
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @CacheEvict(value = CACHE_NAME, key = CACHE_KEY_ALL)\n");
        }
        sb.append("    public CommonResult<").append(entityName).append("> create(").append(entityName).append(" record) {\n");
        sb.append("        try {\n");
        sb.append("            // 设置创建信息\n");
        if (hasProperty(tableInfo, "createTime")) {
            sb.append("            record.setCreateTime(new Date());\n");
        }
        if (hasProperty(tableInfo, "updateTime")) {
            sb.append("            record.setUpdateTime(new Date());\n");
        }
        sb.append("            String userId = SecurityUtils.getUserIdValue();\n");
        // 支持两种用户ID字段命名方式
        if (hasProperty(tableInfo, "createUserId")) {
            sb.append("            record.setCreateUserId(userId);\n");
        } else if (hasProperty(tableInfo, "createUser")) {
            sb.append("            record.setCreateUser(userId);\n");
        }
        if (hasProperty(tableInfo, "updateUserId")) {
            sb.append("            record.setUpdateUserId(userId);\n");
        } else if (hasProperty(tableInfo, "updateUser")) {
            sb.append("            record.setUpdateUser(userId);\n");
        }
        if (hasProperty(tableInfo, "tenantId")) {
            sb.append("            record.setTenantId(SecurityUtils.getSystemTenantId());\n");
        }
        if (hasProperty(tableInfo, "platformId")) {
            sb.append("            record.setPlatformId(SecurityUtils.getSystemPlatformId());\n");
        }
        sb.append("\n");
        sb.append("            int result = ").append(entityNameLowerCase).append("Mapper.insertSelective(record);\n");
        sb.append("            if (result > 0) {\n");
        sb.append("                log.info(\"创建").append(entityName).append("成功: {}\", record.get").append(capitalize(primaryKeyProperty)).append("());\n");
        sb.append("                return CommonResult.success(record);\n");
        sb.append("            } else {\n");
        sb.append("                log.warn(\"创建").append(entityName).append("失败\");\n");
        sb.append("                return CommonResult.failed(\"创建失败\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"创建").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"创建异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
        
        // 删除方法
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @CacheEvict(value = CACHE_NAME, allEntries = true)\n");
        }
        sb.append("    public CommonResult<Void> deleteById(").append(primaryKeyType).append(" id) {\n");
        sb.append("        try {\n");
        sb.append("            int result = ").append(entityNameLowerCase).append("Mapper.deleteByPrimaryKey(id);\n");
        sb.append("            if (result > 0) {\n");
        sb.append("                log.info(\"删除").append(entityName).append("成功: {}\", id);\n");
        sb.append("                return CommonResult.success(null);\n");
        sb.append("            } else {\n");
        sb.append("                log.warn(\"删除").append(entityName).append("失败，记录不存在: {}\", id);\n");
        sb.append("                return CommonResult.failed(\"记录不存在\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"删除").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"删除异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
        
        // 更新方法
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @CachePut(value = CACHE_NAME, key = CACHE_KEY)\n");
        }
        sb.append("    public CommonResult<").append(entityName).append("> update(").append(entityName).append(" record) {\n");
        sb.append("        try {\n");
        sb.append("            // 设置更新信息\n");
        if (hasProperty(tableInfo, "updateTime")) {
            sb.append("            record.setUpdateTime(new Date());\n");
        }
        // 支持两种用户ID字段命名方式
        if (hasProperty(tableInfo, "updateUserId")) {
            sb.append("            record.setUpdateUserId(SecurityUtils.getUserIdValue());\n");
        } else if (hasProperty(tableInfo, "updateUser")) {
            sb.append("            record.setUpdateUser(SecurityUtils.getUserIdValue());\n");
        }
        sb.append("\n");
        sb.append("            int result = ").append(entityNameLowerCase).append("Mapper.updateByPrimaryKeySelective(record);\n");
        sb.append("            if (result > 0) {\n");
        sb.append("                log.info(\"更新").append(entityName).append("成功: {}\", record.get").append(capitalize(primaryKeyProperty)).append("());\n");
        sb.append("                return CommonResult.success(record);\n");
        sb.append("            } else {\n");
        sb.append("                log.warn(\"更新").append(entityName).append("失败，记录不存在: {}\", record.get").append(capitalize(primaryKeyProperty)).append("());\n");
        sb.append("                return CommonResult.failed(\"记录不存在\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"更新").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"更新异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
        
        // 查询方法
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @Cacheable(value = CACHE_NAME, key = CACHE_KEY)\n");
        }
        sb.append("    public CommonResult<").append(entityName).append("> getById(").append(primaryKeyType).append(" id) {\n");
        sb.append("        try {\n");
        sb.append("            ").append(entityName).append(" record = ").append(entityNameLowerCase).append("Mapper.selectByPrimaryKey(id);\n");
        sb.append("            if (record != null) {\n");
        sb.append("                return CommonResult.success(record);\n");
        sb.append("            } else {\n");
        sb.append("                return CommonResult.failed(\"记录不存在\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
    }
    
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 生成批量操作方法
     */
    private static void generateBatchMethods(StringBuilder sb, String entityName, String entityNameLowerCase,
                                           String primaryKeyType, boolean enableCache, TableInfo tableInfo) {
        // 批量创建
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @CacheEvict(value = CACHE_NAME, allEntries = true)\n");
        }
        sb.append("    public CommonResult<List<").append(entityName).append(">> batchCreate(List<").append(entityName).append("> records) {\n");
        sb.append("        try {\n");
        sb.append("            if (records == null || records.isEmpty()) {\n");
        sb.append("                return CommonResult.failed(\"批量创建数据不能为空\");\n");
        sb.append("            }\n\n");
        sb.append("            String userId = SecurityUtils.getUserIdValue();\n");
        sb.append("            String tenantId = SecurityUtils.getSystemTenantId();\n");
        sb.append("            String platformId = SecurityUtils.getSystemPlatformId();\n");
        sb.append("            Date now = new Date();\n\n");
        sb.append("            // 设置公共字段\n");
        sb.append("            for (").append(entityName).append(" record : records) {\n");
        if (hasProperty(tableInfo, "createTime")) {
            sb.append("                record.setCreateTime(now);\n");
        }
        if (hasProperty(tableInfo, "updateTime")) {
            sb.append("                record.setUpdateTime(now);\n");
        }
        if (hasProperty(tableInfo, "createUserId")) {
            sb.append("                record.setCreateUserId(userId);\n");
        } else if (hasProperty(tableInfo, "createUser")) {
            sb.append("                record.setCreateUser(userId);\n");
        }
        if (hasProperty(tableInfo, "updateUserId")) {
            sb.append("                record.setUpdateUserId(userId);\n");
        } else if (hasProperty(tableInfo, "updateUser")) {
            sb.append("                record.setUpdateUser(userId);\n");
        }
        if (hasProperty(tableInfo, "tenantId")) {
            sb.append("                record.setTenantId(tenantId);\n");
        }
        if (hasProperty(tableInfo, "platformId")) {
            sb.append("                record.setPlatformId(platformId);\n");
        }
        sb.append("            }\n\n");
        sb.append("            int result = ").append(entityNameLowerCase).append("Mapper.batchInsert(records);\n");
        sb.append("            if (result > 0) {\n");
        sb.append("                log.info(\"批量创建").append(entityName).append("成功，共{}条记录\", records.size());\n");
        sb.append("                return CommonResult.success(records);\n");
        sb.append("            } else {\n");
        sb.append("                log.warn(\"批量创建").append(entityName).append("失败\");\n");
        sb.append("                return CommonResult.failed(\"批量创建失败\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"批量创建").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"批量创建异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 批量删除
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @CacheEvict(value = CACHE_NAME, allEntries = true)\n");
        }
        sb.append("    public CommonResult<Void> batchDeleteByIds(List<").append(primaryKeyType).append("> ids) {\n");
        sb.append("        try {\n");
        sb.append("            if (ids == null || ids.isEmpty()) {\n");
        sb.append("                return CommonResult.failed(\"批量删除ID列表不能为空\");\n");
        sb.append("            }\n\n");
        sb.append("            int result = ").append(entityNameLowerCase).append("Mapper.batchDeleteByIds(ids);\n");
        sb.append("            if (result > 0) {\n");
        sb.append("                log.info(\"批量删除").append(entityName).append("成功，共{}条记录\", result);\n");
        sb.append("                return CommonResult.success(null);\n");
        sb.append("            } else {\n");
        sb.append("                log.warn(\"批量删除").append(entityName).append("失败，没有找到匹配的记录\");\n");
        sb.append("                return CommonResult.failed(\"没有找到匹配的记录\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"批量删除").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"批量删除异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 批量更新
        sb.append("    @Override\n");
        if (enableCache) {
            sb.append("    @CacheEvict(value = CACHE_NAME, allEntries = true)\n");
        }
        sb.append("    public CommonResult<List<").append(entityName).append(">> batchUpdate(List<").append(entityName).append("> records) {\n");
        sb.append("        try {\n");
        sb.append("            if (records == null || records.isEmpty()) {\n");
        sb.append("                return CommonResult.failed(\"批量更新数据不能为空\");\n");
        sb.append("            }\n\n");
        sb.append("            String userId = SecurityUtils.getUserIdValue();\n");
        sb.append("            Date now = new Date();\n\n");
        sb.append("            // 设置更新字段\n");
        sb.append("            for (").append(entityName).append(" record : records) {\n");
        if (hasProperty(tableInfo, "updateTime")) {
            sb.append("                record.setUpdateTime(now);\n");
        }
        if (hasProperty(tableInfo, "updateUserId")) {
            sb.append("                record.setUpdateUserId(userId);\n");
        } else if (hasProperty(tableInfo, "updateUser")) {
            sb.append("                record.setUpdateUser(userId);\n");
        }
        sb.append("            }\n\n");
        sb.append("            int result = ").append(entityNameLowerCase).append("Mapper.batchUpdate(records);\n");
        sb.append("            if (result > 0) {\n");
        sb.append("                log.info(\"批量更新").append(entityName).append("成功，共{}条记录\", result);\n");
        sb.append("                return CommonResult.success(records);\n");
        sb.append("            } else {\n");
        sb.append("                log.warn(\"批量更新").append(entityName).append("失败\");\n");
        sb.append("                return CommonResult.failed(\"批量更新失败\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"批量更新").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"批量更新异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
    }

    /**
     * 生成复杂查询方法
     */
    private static void generateQueryMethods(StringBuilder sb, String entityName, String entityNameLowerCase, boolean enableCache) {
        // 条件查询列表
        sb.append("    @Override\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> listByCondition(Map<String, Object> params) {\n");
        sb.append("        try {\n");
        sb.append("            List<").append(entityName).append("> records = ").append(entityNameLowerCase).append("Mapper.selectByCondition(params);\n");
        sb.append("            return CommonResult.success(records);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"条件查询").append(entityName).append("列表异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 单个对象条件查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<").append(entityName).append("> getOneByCondition(Map<String, Object> params) {\n");
        sb.append("        try {\n");
        sb.append("            ").append(entityName).append(" record = ").append(entityNameLowerCase).append("Mapper.selectOneByCondition(params);\n");
        sb.append("            if (record != null) {\n");
        sb.append("                return CommonResult.success(record);\n");
        sb.append("            } else {\n");
        sb.append("                return CommonResult.failed(\"未找到符合条件的记录\");\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"条件查询单个").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 分页查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<CommonPage<").append(entityName).append(">> pageByCondition(Map<String, Object> params, Integer pageNum, Integer pageSize) {\n");
        sb.append("        try {\n");
        sb.append("            // 设置分页参数\n");
        sb.append("            if (pageNum == null || pageNum < 1) pageNum = 1;\n");
        sb.append("            if (pageSize == null || pageSize < 1) pageSize = 10;\n");
        sb.append("            \n");
        sb.append("            // 这里需要集成分页插件，如PageHelper\n");
        sb.append("            // PageHelper.startPage(pageNum, pageSize);\n");
        sb.append("            List<").append(entityName).append("> records = ").append(entityNameLowerCase).append("Mapper.selectByCondition(params);\n");
        sb.append("            \n");
        sb.append("            // 构建分页结果\n");
        sb.append("            CommonPage<").append(entityName).append("> page = CommonPage.restPage(records);\n");
        sb.append("            return CommonResult.success(page);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"分页查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"分页查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 条件统计
        sb.append("    @Override\n");
        sb.append("    public CommonResult<Long> countByCondition(Map<String, Object> params) {\n");
        sb.append("        try {\n");
        sb.append("            long count = ").append(entityNameLowerCase).append("Mapper.countByCondition(params);\n");
        sb.append("            return CommonResult.success(count);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"条件统计").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"统计异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 多条件AND查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> listByMultipleConditions(Map<String, Object> conditions) {\n");
        sb.append("        try {\n");
        sb.append("            List<").append(entityName).append("> records = ").append(entityNameLowerCase).append("Mapper.selectByMultipleConditions(conditions);\n");
        sb.append("            return CommonResult.success(records);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"多条件查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 范围查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> listByRange(String field, Object startValue, Object endValue) {\n");
        sb.append("        try {\n");
        sb.append("            List<").append(entityName).append("> records = ").append(entityNameLowerCase).append("Mapper.selectByRange(field, startValue, endValue);\n");
        sb.append("            return CommonResult.success(records);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"范围查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 模糊查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> listByLike(String field, String keyword) {\n");
        sb.append("        try {\n");
        sb.append("            List<").append(entityName).append("> records = ").append(entityNameLowerCase).append("Mapper.selectByLike(field, keyword);\n");
        sb.append("            return CommonResult.success(records);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"模糊查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // IN查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> listByIn(String field, List<Object> values) {\n");
        sb.append("        try {\n");
        sb.append("            List<").append(entityName).append("> records = ").append(entityNameLowerCase).append("Mapper.selectByIn(field, values);\n");
        sb.append("            return CommonResult.success(records);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"IN查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 总数统计
        sb.append("    @Override\n");
        sb.append("    public CommonResult<Long> countTotal(Map<String, Object> params) {\n");
        sb.append("        try {\n");
        sb.append("            long count = ").append(entityNameLowerCase).append("Mapper.countTotal(params);\n");
        sb.append("            return CommonResult.success(count);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"统计").append(entityName).append("总数异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"统计异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 复杂组合查询 - 支持多种条件组合
        sb.append("    @Override\n");
        sb.append("    public CommonResult<List<").append(entityName).append(">> complexQuery(Map<String, Object> queryParams) {\n");
        sb.append("        try {\n");
        sb.append("            // 支持多种查询条件的组合\n");
        sb.append("            List<").append(entityName).append("> records = new ArrayList<>();\n");
        sb.append("            \n");
        sb.append("            // 如果有精确匹配条件\n");
        sb.append("            if (queryParams.containsKey(\"exactConditions\")) {\n");
        sb.append("                @SuppressWarnings(\"unchecked\")\n");
        sb.append("                Map<String, Object> exactConditions = (Map<String, Object>) queryParams.get(\"exactConditions\");\n");
        sb.append("                records = ").append(entityNameLowerCase).append("Mapper.selectByMultipleConditions(exactConditions);\n");
        sb.append("            }\n");
        sb.append("            // 如果有范围查询条件\n");
        sb.append("            else if (queryParams.containsKey(\"rangeField\") && queryParams.containsKey(\"startValue\") && queryParams.containsKey(\"endValue\")) {\n");
        sb.append("                String field = (String) queryParams.get(\"rangeField\");\n");
        sb.append("                Object startValue = queryParams.get(\"startValue\");\n");
        sb.append("                Object endValue = queryParams.get(\"endValue\");\n");
        sb.append("                records = ").append(entityNameLowerCase).append("Mapper.selectByRange(field, startValue, endValue);\n");
        sb.append("            }\n");
        sb.append("            // 如果有模糊查询条件\n");
        sb.append("            else if (queryParams.containsKey(\"likeField\") && queryParams.containsKey(\"keyword\")) {\n");
        sb.append("                String field = (String) queryParams.get(\"likeField\");\n");
        sb.append("                String keyword = (String) queryParams.get(\"keyword\");\n");
        sb.append("                records = ").append(entityNameLowerCase).append("Mapper.selectByLike(field, keyword);\n");
        sb.append("            }\n");
        sb.append("            // 如果有IN查询条件\n");
        sb.append("            else if (queryParams.containsKey(\"inField\") && queryParams.containsKey(\"values\")) {\n");
        sb.append("                String field = (String) queryParams.get(\"inField\");\n");
        sb.append("                @SuppressWarnings(\"unchecked\")\n");
        sb.append("                List<Object> values = (List<Object>) queryParams.get(\"values\");\n");
        sb.append("                records = ").append(entityNameLowerCase).append("Mapper.selectByIn(field, values);\n");
        sb.append("            }\n");
        sb.append("            // 默认条件查询\n");
        sb.append("            else {\n");
        sb.append("                records = ").append(entityNameLowerCase).append("Mapper.selectByCondition(queryParams);\n");
        sb.append("            }\n");
        sb.append("            \n");
        sb.append("            return CommonResult.success(records);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"复杂查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 分页复杂查询
        sb.append("    @Override\n");
        sb.append("    public CommonResult<CommonPage<").append(entityName).append(">> complexPageQuery(Map<String, Object> queryParams, Integer pageNum, Integer pageSize) {\n");
        sb.append("        try {\n");
        sb.append("            PageHelper.startPage(pageNum, pageSize);\n");
        sb.append("            CommonResult<List<").append(entityName).append(">> result = complexQuery(queryParams);\n");
        sb.append("            \n");
        sb.append("            if (result.isSuccess()) {\n");
        sb.append("                List<").append(entityName).append("> list = result.getData();\n");
        sb.append("                return CommonResult.success(CommonPage.restPage(list));\n");
        sb.append("            } else {\n");
        sb.append("                return CommonResult.failed(result.getMessage());\n");
        sb.append("            }\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"分页复杂查询").append(entityName).append("异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"查询异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
    }

    /**
     * 生成数据校验方法
     */
    private static void generateValidationMethods(StringBuilder sb, String entityName, String entityNameLowerCase,
                                                String primaryKeyType, String primaryKeyProperty, TableInfo tableInfo) {
        // 数据校验
        sb.append("    @Override\n");
        sb.append("    public CommonResult<Void> validateData(").append(entityName).append(" record) {\n");
        sb.append("        try {\n");
        sb.append("            if (record == null) {\n");
        sb.append("                return CommonResult.failed(\"数据不能为空\");\n");
        sb.append("            }\n\n");
        sb.append("            // 基础字段校验\n");

        // 根据表字段生成校验逻辑
        tableInfo.getColumns().forEach(column -> {
            boolean isPrimaryKey = tableInfo.getPrimaryKey() != null &&
                                 tableInfo.getPrimaryKey().equals(column.getColumnName());
            if (!column.isNullable() && !isPrimaryKey) {
                String property = column.getJavaProperty();
                String comment = column.getColumnComment();
                if ("String".equals(column.getJavaType())) {
                    sb.append("            if (record.get").append(capitalize(property)).append("() == null || record.get")
                      .append(capitalize(property)).append("().trim().isEmpty()) {\n");
                    sb.append("                return CommonResult.failed(\"").append(comment != null ? comment : property).append("不能为空\");\n");
                    sb.append("            }\n");
                } else {
                    sb.append("            if (record.get").append(capitalize(property)).append("() == null) {\n");
                    sb.append("                return CommonResult.failed(\"").append(comment != null ? comment : property).append("不能为空\");\n");
                    sb.append("            }\n");
                }
            }
        });

        sb.append("\n");
        sb.append("            // 业务逻辑校验\n");
        sb.append("            // 可以在这里添加具体的业务校验逻辑\n");
        sb.append("            \n");
        sb.append("            return CommonResult.success(null);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"数据校验异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"数据校验异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");

        // 存在性检查
        sb.append("    @Override\n");
        sb.append("    public CommonResult<Boolean> existsById(").append(primaryKeyType).append(" id) {\n");
        sb.append("        try {\n");
        sb.append("            if (id == null) {\n");
        sb.append("                return CommonResult.success(false);\n");
        sb.append("            }\n");
        sb.append("            ").append(entityName).append(" record = ").append(entityNameLowerCase).append("Mapper.selectByPrimaryKey(id);\n");
        sb.append("            return CommonResult.success(record != null);\n");
        sb.append("        } catch (Exception e) {\n");
        sb.append("            log.error(\"检查").append(entityName).append("存在性异常: {}\", e.getMessage(), e);\n");
        sb.append("            return CommonResult.failed(\"检查异常: \" + e.getMessage());\n");
        sb.append("        }\n");
        sb.append("    }\n\n");
    }
}
