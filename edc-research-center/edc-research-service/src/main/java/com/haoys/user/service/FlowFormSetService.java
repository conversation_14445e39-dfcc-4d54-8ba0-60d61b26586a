package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.expand.FlowFormSetValueExpand;
import com.haoys.user.domain.param.flow.FlowEditPerParam;
import com.haoys.user.domain.param.flow.FlowFormExpandParam;
import com.haoys.user.domain.param.flow.FlowFormSetParam;
import com.haoys.user.domain.vo.FlowFormSetVo;
import com.haoys.user.domain.vo.flow.FlowOrderFormVo;
import com.haoys.user.domain.vo.flow.ProjectFlowFormVo;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowFormSetExample;
import com.haoys.user.model.FlowFormSetExpand;

import java.util.List;

public interface FlowFormSetService {

    /**
     * 配置研究流程 -保存
     * @param params 表单和流程的关联参数
     * @return
     */
    CommonResult<Object> save(List<FlowFormSetParam> params);

    /**
     * 单个保存
     * @param param 表单和流程的关联参数
     * @return
     */
    CommonResult<Object> saveOne(FlowFormSetParam param);

    /**
     * 取消
     * @param param 表单和流程的关联参数
     * @return 取消结果
     */
    CommonResult<Object> delete(FlowFormSetParam param);

    /**
     * 修改表单pc和移动端的权限
     * @param formSet 参数
     * @return 结果
     */
    CommonResult<Object> updateFlowFormAuth(FlowFormSet formSet);

    /**
     * 表单排序-保存
     * @param param 表单信息
     * @return 保存结果
     */
    CommonResult<Object> saveOrderForm( List<String> ids);

    /**
     * 获取配置研究流程的列表
     * @param projectId
     * @param planId
     * @return
     */
    CommonResult<List<FlowFormSetVo>> list(Long projectId, Long planId);
    /**
     * 获取配置研究流程的列表
     * @param flowId 流程id
     * @return
     */
    CommonResult<FlowOrderFormVo> listByFlowId(Long flowId);

    /**
     * 全部选中/取消
     * @return
     */
    CommonResult<Object> editAllPer(FlowEditPerParam param);

    /**
     * 获取配置研究流程的列表同时根据权限进行过滤
     * @param formSet 流程id
     * @return
     */
    List<ProjectFlowFormVo> listByFlowIdAndFilterPer(FlowFormSet formSet);


    /**
     * 获取流程设置信息
     * @return
     */
    List<FlowFormSet> selectByExample(FlowFormSetExample example);


    CommonResult<List<FlowFormSet>> getFormByFlowId(Long flowId);
    
    
    List<FlowFormSet> getProjectVisitListByVisitId(String flowId);


    /**
     * 统计流程设置信息s
     * @return
     */
    Long countByExample(FlowFormSetExample example);
    
    FlowFormSetValueExpand getFormConfigListByProjectIdAndFormId(String projectId, String planId, String visitId, String formId);
    
    void updateFlowFormBaseInfo(FlowFormSet flowFormSet);
    
    CommonResult<Object> saveFormExpand(FlowFormExpandParam flowFormExpandParam);
    
    CommonResult<Object> deleteFormExpand(FlowFormExpandParam flowFormExpandParam);
    
    /**
     * 查询扩展表单列表
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    List<FlowFormSetExpand> getFormSetExpandList(String projectId, String planId, String visitId, String formId, String testeeId);
    
    void saveFormExpandObject(FlowFormSetExpand flowFormSetExpand);
    
    /**
     * 获取扩展表单信息
     * @return
     */
    FlowFormSetExpand getFormSetExpandTemplateCopy(String projectId, String planId, String visitId, String formId, String testeeId);
    
    FlowFormSetExpand getFormExpandByFormSetId(String projectId, String formSetId, String testeeId);
    
    void insertflowFormSetExpandForTemplate(String projectId, String planId, String visitId, String formId, String testeeId, String systemTenantId, String systemPlatformId);
}
