package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.param.ProjectTesteeVariableMappingParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVariableMappingVo;
import com.haoys.user.mapper.FlowFormSetMapper;
import com.haoys.user.mapper.ProjectTesteeVariableMappingMapper;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.ProjectTesteeVariableMapping;
import com.haoys.user.model.ProjectTesteeVariableMappingExample;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.service.ProjectTesteeVariableMappingService;
import com.haoys.user.service.ProjectTesteeVariableSyncService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自定义标题-配置映射
 */
@Service
public class ProjectTesteeVariableMappingServiceImpl implements ProjectTesteeVariableMappingService {

    @Autowired
    private ProjectTesteeVariableMappingMapper projectTesteeVariableMappingMapper;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private FlowFormSetMapper flowFormSetMapper;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ProjectTesteeVariableSyncService projectTesteeVariableSyncService;

    @Override
    public CommonResult<Object> create(ProjectTesteeVariableMappingParam variableMappingParam) {
        // 先删除
        ProjectTesteeVariableMappingExample example = new ProjectTesteeVariableMappingExample();
        ProjectTesteeVariableMappingExample.Criteria criteria = example.createCriteria();
        criteria.andBaseVariableIdEqualTo(variableMappingParam.getBaseVariableId());
        projectTesteeVariableMappingMapper.deleteByExample(example);

        //查询sourceVariableId
        //ProjectTesteeVariableSync projectTesteeVariableSync = projectTesteeVariableSyncService.getVariableSyncByBaseVariableId(variableMappingParam.getBaseVariableId());

        // 再新增
        if (CollectionUtil.isNotEmpty(variableMappingParam.getList())){
            variableMappingParam.getList().forEach(projectTesteeVariableMapping -> {
                String userId = SecurityUtils.getUserIdValue();
                projectTesteeVariableMapping.setId(SnowflakeIdWorker.getUuid());
                projectTesteeVariableMapping.setTenantId(SecurityUtils.getSystemTenantId());
                projectTesteeVariableMapping.setPlatformId(SecurityUtils.getSystemPlatformId());
                projectTesteeVariableMapping.setCreateUserId(userId);
                projectTesteeVariableMapping.setCreateTime(new Date());
                projectTesteeVariableMapping.setStatus(BusinessConfig.ENABLED_STATUS.toString());
                projectTesteeVariableMappingMapper.insert(projectTesteeVariableMapping);

                //if(projectTesteeVariableSync != null && projectTesteeVariableSync.getId() != null){
                    //String baseVariableId = projectTesteeVariableSync.getId().toString();
                    String sourceVariableId = projectTesteeVariableMapping.getTargetVariableId() == null ? null : projectTesteeVariableMapping.getTargetVariableId().toString();
                    String baseVariableId = variableMappingParam.getBaseVariableId() == null ? null : variableMappingParam.getBaseVariableId().toString();
                    templateConfigService.updateTemplateFormDetailConfigByVaribaleId(sourceVariableId,baseVariableId,userId);
                //}

            });
        }
        return CommonResult.success(null);
    }

    @Override
    public CommonResult<List<ProjectTesteeVariableMappingVo>> getByBaseVariableId(Long baseVariableId) {
        ProjectTesteeVariableMappingExample example = new ProjectTesteeVariableMappingExample();
        ProjectTesteeVariableMappingExample.Criteria criteria = example.createCriteria();
        criteria.andBaseVariableIdEqualTo(baseVariableId);
        List<ProjectTesteeVariableMapping> mappingList = projectTesteeVariableMappingMapper.selectByExample(example);
        List<ProjectTesteeVariableMappingVo> voList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(mappingList)){
            List<ProjectVisitConfig> flowList = projectVisitConfigService.listByProjectId(mappingList.get(0).getProjectId());
            mappingList.forEach(mapping->{
                ProjectTesteeVariableMappingVo vo = new ProjectTesteeVariableMappingVo();
                BeanUtils.copyProperties(mapping,vo);
                // 获取流程信息
                vo.setFlowList(flowList);
                // 获取表单信息
                List<FlowFormSet> formList = flowFormSetMapper.listByFlowId(mapping.getTargetVisitId());
                vo.setFormList(formList);
                // 获取字段信息
                List<TemplateFormDetailVo> filedList = templateConfigService.getTemplateFormDetailConfigListByFormId(null, mapping.getTargetFormId().toString(), "", "1", "1", "");
                vo.setFiledList(filedList);
                voList.add(vo);
            });
        }
        return CommonResult.success(voList);
    }
}
