package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.rcts.ModifyRandomizedParam;
import com.haoys.user.domain.param.rcts.ModifyRandomizedParamsConfigParam;
import com.haoys.user.domain.param.rcts.SaveRandomizedParam;
import com.haoys.user.domain.param.rcts.SaveRandomizedParamsConfigParam;
import com.haoys.user.domain.vo.rcts.RandomizedBlindRecordVo;
import com.haoys.user.domain.vo.rcts.RandomizedParamsVo;
import com.haoys.user.domain.vo.rcts.RandomizedVo;
import com.haoys.user.domain.vo.rcts.TesteeInfoVo;
import com.haoys.user.model.RctsRandomizedBlindRecord;
import com.haoys.user.model.RctsTesteeInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/3 13:49
 */
public interface RandomizedControlledTrialsService {

    // 随机配置

    int saveRandomizedConfig(SaveRandomizedParam param);

    int getRandomizedConfigCountCheck(Long projectId, Long visitId, Long formId, Long variableId);

    List<RandomizedVo> getRandomizedConfig(String projectId);

    int modifyRandomizedConfig(ModifyRandomizedParam param);

    int removeRandomizedConfig(Long id);


    // 随机参数

    int saveRandomizedParamsConfig(SaveRandomizedParamsConfigParam param);

    int countByProjectId(Long projectId);

    RandomizedParamsVo getRandomizedParamsConfig(Long projectId);

    String modifyRandomizedParamsConfig(ModifyRandomizedParamsConfigParam param);

    // randomized_group_config
    String modifyRandomizedGroupConfig(String param, Long id);

    // randomized_layer_config
    String modifyRandomizedLayerConfig(String param, Long id);

    // 数据列表
    String saveTesteeInfo(Long projectId, Long orgId);

    CommonPage<TesteeInfoVo> getTesteeInfoPage(Long projectId, Long orgId, String researchStatus, String queryParam1, int pageNum, int pageSize);

    /**
     * 查询随机系统受试者基本信息
     * @param projectId
     * @param testeeId
     * @return
     */
    RctsTesteeInfo getRctsTesteeInfo(Long projectId, Long testeeId);

    /**
     * 受试者下拉选，返回受试者 编号/姓名缩写
     * @param projectId
     * @return
     */
    List<Map<String, String>> getTesteeSelection(Long projectId, String testeeCode);

    // 盲底管理

    boolean getShowRandomizedBlindButton(Long projectId);

    String randomizedBlind(Long projectId);

    CommonPage<RandomizedBlindRecordVo> getRandomizedBlindPage(Long projectId, Long orgId, String batchNo, String randomizedNumber, int pageNum, int pageSize);
    
    RctsRandomizedBlindRecord getProjectRandomizedConfigNumber(String projectId, String projectOrgId, String testeeId);
    
    void updateRandomizedBlindRecordBindTetsee(String projectId, String projectOrgId, String blindId, String testeeId, String randomizedNumber);

    String getBlindId(Long projectId, List<String> expand);
    
    RctsRandomizedBlindRecord getRandodmBlindRecordByBlindId(String blindId);
}
