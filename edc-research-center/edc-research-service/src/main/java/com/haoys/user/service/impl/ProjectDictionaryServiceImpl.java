package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.DictExportDto;
import com.haoys.user.domain.dto.DictItemExportDto;
import com.haoys.user.domain.dto.ProjectDictionaryDto;
import com.haoys.user.domain.enums.SystemDictEnums;
import com.haoys.user.domain.param.dict.ProjectDictParam;
import com.haoys.user.exception.CustomerException;
import com.haoys.user.expand.DictionaryCacheUtils;
import com.haoys.user.mapper.ProjectDictionaryMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.ProjectDictionary;
import com.haoys.user.model.ProjectDictionaryExample;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.ProjectDictionaryService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ProjectDictionaryServiceImpl extends BaseService implements ProjectDictionaryService {

    @Autowired
    private ProjectDictionaryMapper projectDictionaryMapper;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private DictionaryService dictionaryService;

    private String DEFAULT_PARENT_CODE = "0";

    @Override
    public CommonPage<ProjectDictionary> getDictionaryListForPage(ProjectDictParam projectDictParam) {
        List<ProjectDictionary> dataList  = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(projectDictParam.getPageNum(), projectDictParam.getPageSize());
        if (StringUtils.isBlank(projectDictParam.getParentId())) {
            projectDictParam.setParentId(DEFAULT_PARENT_CODE);
        }
        if ("-1".equals(projectDictParam.getStatus())) {
            projectDictParam.setStatus(null);
        }
        List<ProjectDictionary> dictionaryList = projectDictionaryMapper.selectProjectDictionaryList(projectDictParam);
        for (ProjectDictionary projectDictionary : dictionaryList) {
            ProjectDictionary projectDictionary1 = new ProjectDictionary();
            BeanUtils.copyProperties(projectDictionary, projectDictionary1);
            projectDictionary1.setValue(projectDictionary.getOptionValue());
            dataList.add(projectDictionary1);
        }
        return commonPageListWrapper(projectDictParam.getPageNum(), projectDictParam.getPageSize(), page, dataList);
    }
    @Override
    public List<ProjectDictionary> getDictionaryList(String projectId, String dicType, String parentId, String name) {
        List<ProjectDictionary> dataList  = new ArrayList<>();
        ProjectDictParam projectDictParam = new ProjectDictParam();
        projectDictParam.setProjectId(NumberUtil.parseLong(projectId));
        projectDictParam.setDicType(dicType);
        if (StringUtils.isBlank(parentId)) {
            projectDictParam.setParentId(DEFAULT_PARENT_CODE);
        }
        if ("-1".equals(projectDictParam.getStatus())) {
            projectDictParam.setStatus(null);
        }
        if(StringUtils.isNotBlank(name)){
            projectDictParam.setName(name);
        }
        List<ProjectDictionary> dictionaryList = projectDictionaryMapper.selectProjectDictionaryList(projectDictParam);
        for (ProjectDictionary projectDictionary : dictionaryList) {
            ProjectDictionary projectDictionary1 = new ProjectDictionary();
            BeanUtils.copyProperties(projectDictionary, projectDictionary1);
            projectDictionary1.setValue(projectDictionary.getOptionValue());
            dataList.add(projectDictionary1);
        }
        return dataList;
    }

    @Override
    public CommonResult<Object> saveDictionary(ProjectDictionaryDto dto) {
        // 创建字典对象并保存
        // 校验名称是否重复
        if (dto.getParentId() == null) {
            dto.setParentId(DEFAULT_PARENT_CODE);
            extractedNew(dto, dto.getParentId(), false);
        } else {
            extractedNew(dto, dto.getParentId(), true);
        }
        String userId = SecurityUtils.getUserId().toString();
        ProjectDictionary dictionary = new ProjectDictionary();
        dictionary.setProjectId(StringUtils.isNotEmpty(dto.getProjectId())?Long.parseLong(dto.getProjectId()):0L);
        dictionary.setParentId(dto.getParentId());
        dictionary.setName(dto.getName());
        dictionary.setSort(dto.getSort());
        dictionary.setEnName(dto.getEnName());
        dictionary.setScoreValue(dto.getScoreValue());
        dictionary.setUnitValue(dto.getUnitValue());
        dictionary.setOptionValue(dto.getOptionValue());
        dictionary.setDicType(dto.getDicType());
        dictionary.setDescription(dto.getDescription());
        if(dto.getParentId().equals(DEFAULT_PARENT_CODE)){
            dictionary.setCode(dto.getCode());
        }
        dictionary.setId(SnowflakeIdWorker.getUuid().toString());
        dictionary.setCreateTime(new Date());
        dictionary.setCreateUserId(userId);
        dictionary.setUpdateTime(new Date());
        dictionary.setUpdateUserId(userId);
        dictionary.setTenantId(SecurityUtils.getSystemTenantId());
        dictionary.setPlatformId(SecurityUtils.getSystemPlatformId());
        dictionary.setStatus(BusinessConfig.ENABLED_STATUS.toString());
        dictionary.setDataFrom(BusinessConfig.PROJECT_DICTIONARY_PROJECT_ADD);
        projectDictionaryMapper.insert(dictionary);
        String key = dictionary.getProjectId() + ":" + dictionary.getDicType() + ":" + dictionary.getParentId();
        DictionaryCacheUtils.removeDictCache(key);

        String key2 = "DICT:" + ":" + dto.getDicType() + ":" + dictionary.getId();
        DictionaryCacheUtils.removeDictCache(key2);
        return CommonResult.success(null);
    }


    @Override
    public ProjectDictionary getDictionaryInfo(String id) {
        return projectDictionaryMapper.selectByPrimaryKey(id);
    }

    @Override
    public CommonResult<Object> updateDictionary(ProjectDictionaryDto dto) {
        // 创建字典对象并保存
        // 校验名称是否重复
        if (dto.getParentId() == null) {
            dto.setParentId(DEFAULT_PARENT_CODE);
            extractedNew(dto, dto.getParentId(), false);
        } else {
            extractedNew(dto, dto.getParentId(), true);
        }
        String userId = SecurityUtils.getUserId().toString();
        ProjectDictionary dictionary = projectDictionaryMapper.selectByPrimaryKey(dto.getId());
        dictionary.setProjectId(Long.parseLong(dto.getProjectId()));
        dictionary.setParentId(dto.getParentId());
        dictionary.setName(dto.getName());
        dictionary.setEnName(dto.getEnName());
        dictionary.setSort(dto.getSort());
        dictionary.setScoreValue(dto.getScoreValue());
        dictionary.setUnitValue(dto.getUnitValue());
        dictionary.setOptionValue(dto.getOptionValue());
        dictionary.setDicType(dto.getDicType());
        dictionary.setDescription(dto.getDescription());
        if(dto.getParentId().equals(DEFAULT_PARENT_CODE)){
            dictionary.setCode(dto.getCode());
        }
        dictionary.setUpdateTime(new Date());
        dictionary.setUpdateUserId(userId);
        dictionary.setTenantId(SecurityUtils.getSystemTenantId());
        dictionary.setPlatformId(SecurityUtils.getSystemPlatformId());
        dictionary.setDataFrom(BusinessConfig.PROJECT_DICTIONARY_PROJECT_ADD);
        projectDictionaryMapper.updateByPrimaryKey(dictionary);
        String key = dictionary.getProjectId() + ":" + dictionary.getDicType() + ":" + dictionary.getParentId();
        DictionaryCacheUtils.removeDictCache(key);
        String key2 = "DICT:" + ":" + dto.getDicType() + ":" + dictionary.getId();
        DictionaryCacheUtils.removeDictCache(key2);
        return CommonResult.success(null);
    }


    /**
     * 新增和更新字典时候的校验
     *
     * @param param
     * @param isCheckEnName 是否校验英文名称
     */
    private void extracted(ProjectDictionaryDto param, String parentId, boolean isCheckEnName) {
        // 校验名称是否重复
        ProjectDictionary dictByName0 = getDictByName("", param.getName(), parentId);
        if (dictByName0 != null && !dictByName0.getId().equals(param.getId())) {
            if (DEFAULT_PARENT_CODE.equals(parentId)) {
                throw new CustomerException(SystemDictEnums.E51000);
            } else {
                throw new CustomerException(SystemDictEnums.E51001);
            }
        }

        if (isCheckEnName) {
            // 校验英文名称是否重复
            if (StringUtils.isNotEmpty(param.getEnName())) {
                ProjectDictionary dictItemByEnName = getDictByEnName(param.getEnName(), parentId);
                if (dictItemByEnName != null && !dictByName0.getId().equals(param.getId())) {
                    if (DEFAULT_PARENT_CODE.equals(parentId)) {
                        throw new CustomerException(SystemDictEnums.E51000);
                    } else {
                        throw new CustomerException(SystemDictEnums.E51001);
                    }
                }
            }
        }
        // 校验字典编码是否重复
        ProjectDictionary dictByCode0 = getDictByCode(param.getCode(), parentId);
        if (dictByCode0 != null && !dictByCode0.getId().equals(param.getId())) {
            if (DEFAULT_PARENT_CODE.equals(parentId)) {
                throw new CustomerException(SystemDictEnums.E51000);
            } else {
                throw new CustomerException(SystemDictEnums.E51001);
            }
        }
    }


    @Override
    public ProjectDictionary getDictByCode(String code, String parentId) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        ProjectDictionaryExample example = new ProjectDictionaryExample();
        ProjectDictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andCodeEqualTo(code);
        criteria.andParentIdEqualTo(parentId);
        List<ProjectDictionary> dictItems = projectDictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)) {
            return dictItems.get(0);
        }
        return null;
    }

    @Override
    public List<Dictionary> getDictionaryListByParentId(String projectId, String parentId, String name, String dicType) {
        String key = projectId + ":" + dicType + ":" + parentId;
        List<Dictionary> dictCache = DictionaryCacheUtils.getDictCache(key);
        if (dictCache == null) {
            dictCache = projectDictionaryMapper.getDictList(projectId,parentId,dicType,BusinessConfig.ENABLED_STATUS.toString());
            DictionaryCacheUtils.setDictCache(key, dictCache);
        }
        if (StringUtils.isNotEmpty(name)) {
            return dictCache.stream().filter(dictionary -> dictionary.getName().contains(name)).collect(Collectors.toList());
        }
        return dictCache;
    }

    @Override
    public CommonResult<Object> removeById(String dictId) {
        ProjectDictionary dictionary = projectDictionaryMapper.selectByPrimaryKey(dictId);
        if (Objects.equals(dictionary.getParentId(), DEFAULT_PARENT_CODE)) {
            // 字典类型
            Boolean bl = templateConfigService.checkProjectDictionaryTypeReference(dictionary.getProjectId().toString(), dictId);
            if (bl) {
                throw new CustomerException(SystemDictEnums.E51002);
            }
        } else {
            Boolean bl = templateConfigService.checkProjectDictionaryOptionReference(dictionary.getProjectId().toString(),dictId);
            if (bl) {
                throw new CustomerException(SystemDictEnums.E51004);
            }
        }
        String key = dictionary.getProjectId() + ":" + dictionary.getDicType() + ":" + dictionary.getParentId();
        DictionaryCacheUtils.removeDictCache(key);
        int delete = projectDictionaryMapper.deleteByPrimaryKey(dictId);
        return delete > 0 ? CommonResult.success(null) : CommonResult.failed();
    }

    @Override
    public int enableOrUnable(String id, String status) {
        ProjectDictionary projectDictionary = projectDictionaryMapper.selectByPrimaryKey(id);
        if (BusinessConfig.DISABLED_STATUS.toString().equals(status)) {
            // 字典类型
            Boolean bl = templateConfigService.checkProjectDictionaryTypeReference(projectDictionary.getProjectId().toString(), id);
            if (bl) {
                throw new CustomerException(SystemDictEnums.E51003);
            }
            String key = projectDictionary.getProjectId() + ":" + projectDictionary.getDicType() + ":" + projectDictionary.getParentId();
            DictionaryCacheUtils.removeDictCache(key);
        }
        projectDictionary.setStatus(status);
        return projectDictionaryMapper.updateByPrimaryKey(projectDictionary);
    }

    @Override
    public List<DictExportDto> getList(ProjectDictParam param) {
        if (StringUtils.isBlank(param.getParentId())) {
            param.setParentId(DEFAULT_PARENT_CODE);
        }
        if ("-1".equals(param.getStatus())) {
            param.setStatus(null);
        }
        List<DictExportDto> dictionaryList = projectDictionaryMapper.selectExportList(param);
        return dictionaryList;
    }

    @Override
    public List<DictItemExportDto> exportDictItem(ProjectDictParam param) {
        List<DictItemExportDto> dictItems = projectDictionaryMapper.exportDictItem(param);
        return dictItems;
    }

    @Override
    public CommonResult<Object> initProjectDictionary(Long projectId) {
        ProjectDictParam param = new ProjectDictParam();
        param.setParentId(DEFAULT_PARENT_CODE);
        param.setProjectId(0L);
        param.setPageNum(0);
        param.setPageSize(Integer.MAX_VALUE);
        List<ProjectDictionary> dictionaryList = projectDictionaryMapper.selectProjectDictionaryList(param);
        if (CollectionUtil.isNotEmpty(dictionaryList)) {
            for (ProjectDictionary dictionary : dictionaryList) {
                param.setParentId(dictionary.getId());
                List<ProjectDictionary> dictItemList = projectDictionaryMapper.selectProjectDictionaryList(param);
                dictionary.setId(SnowflakeIdWorker.getUuid().toString());
                dictionary.setProjectId(projectId);
                dictionary.setDataFrom(BusinessConfig.PROJECT_DICTIONARY_SYSTEM_INIT);
                projectDictionaryMapper.insert(dictionary);
                if (CollectionUtil.isNotEmpty(dictItemList)) {
                    for (ProjectDictionary item : dictItemList) {
                        item.setId(SnowflakeIdWorker.getUuid().toString());
                        item.setProjectId(projectId);
                        item.setParentId(dictionary.getId());
                        item.setDataFrom(BusinessConfig.PROJECT_DICTIONARY_SYSTEM_INIT);
                        projectDictionaryMapper.insert(item);
                    }
                }
            }
        }
        return CommonResult.success(null);
    }

    /**
     * 根据
     *
     * @param dictId   字典id
     * @param dictType 字典类型 1-系统字典2-表单字典 3-数据单位
     * @return
     */
    @Override
    public Dictionary getDict(Long dictId, String dictType) {

        String key = "DICT:" + ":" + dictType + ":" + dictId;
        Dictionary dictCache = DictionaryCacheUtils.getDict(key);
        Dictionary dictionary = null;
        if ((dictCache != null)) {
            return dictCache;
        } else {
            if (BusinessConfig.SYSTEM_DICT.equals(dictType)) {
                // 系统字典
                dictionary = dictionaryService.getDictionaryInfo(dictId.toString());
                if (dictionary != null) {
                    DictionaryCacheUtils.setDict(key, dictionary);
                }
            } else {
                ProjectDictionary dict = projectDictionaryMapper.selectByPrimaryKey(dictId.toString());
                if (dict != null) {
                    dictionary = new Dictionary();
                    dictionary.setValue(dict.getUnitValue());
                    dictionary.setName(dict.getName());
                    dictionary.setId(Long.parseLong(dict.getId()));
                    dictionary.setCode(dict.getCode());
                    dictionary.setDescription(dict.getDescription());
                    DictionaryCacheUtils.setDict(key, dictionary);
                }
            }
        }
        return dictionary;
    }
    
    @Override
    public ProjectDictionary getProjectDictionaryByParentIdAndCode(String projectId, String parentId, String code) {
        return projectDictionaryMapper.getProjectDictionaryByParentIdAndCode(projectId, parentId, code);
    }
    
    @Override
    public ProjectDictionary getDictByName(String projectId, String dictName, String parentId) {
        if (StringUtils.isBlank(dictName)) {
            return null;
        }
        ProjectDictionaryExample example = new ProjectDictionaryExample();
        ProjectDictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(dictName);
        criteria.andParentIdEqualTo(parentId);
        if(StringUtils.isNotEmpty(projectId)){
            criteria.andProjectIdEqualTo(NumberUtil.parseLong(projectId));
        }
        List<ProjectDictionary> dictItems = projectDictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)) {
            return dictItems.get(0);
        }
        return null;
    }


    @Override
    public ProjectDictionary getDictByEnName(String dictEnName, String parentId) {
        ProjectDictionaryExample example = new ProjectDictionaryExample();
        ProjectDictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andEnNameEqualTo(dictEnName);
        criteria.andParentIdEqualTo(parentId);
        List<ProjectDictionary> dictItems = projectDictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)) {
            return dictItems.get(0);
        }
        return null;
    }

    private void extractedNew(ProjectDictionaryDto param, String parentId, boolean isCheckEnName) {
        // 校验名称是否重复
        ProjectDictionary dictByName0 = getDictByNameNew(param, parentId);
        if (dictByName0 != null && !dictByName0.getId().equals(param.getId())) {
            if (DEFAULT_PARENT_CODE.equals(parentId)) {
                throw new CustomerException(SystemDictEnums.E51000);
            } else {
                throw new CustomerException(SystemDictEnums.E51001);
            }
        }

        if (isCheckEnName) {
            // 校验英文名称是否重复
            if (StringUtils.isNotEmpty(param.getEnName())) {
                ProjectDictionary dictItemByEnName = getDictByEnNameNew(param, parentId);
                if (dictItemByEnName != null && !dictItemByEnName.getId().equals(param.getId())) {
                    if (DEFAULT_PARENT_CODE.equals(parentId)) {
                        throw new CustomerException(SystemDictEnums.E51000);
                    } else {
                        throw new CustomerException(SystemDictEnums.E51001);
                    }
                }
            }
        }
        // 校验字典编码是否重复
        ProjectDictionary dictByCode0 = getDictByCodeNew(param, parentId);
        if (dictByCode0 != null && !dictByCode0.getId().equals(param.getId())) {
            if (DEFAULT_PARENT_CODE.equals(parentId)) {
                throw new CustomerException(SystemDictEnums.E51000);
            } else {
                throw new CustomerException(SystemDictEnums.E51001);
            }
        }
    }

    public ProjectDictionary getDictByNameNew(ProjectDictionaryDto param, String parentId) {
        if (StringUtils.isBlank(param.getName())) {
            return null;
        }
        ProjectDictionaryExample example = new ProjectDictionaryExample();
        ProjectDictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(param.getName());
        criteria.andParentIdEqualTo(parentId);
        criteria.andProjectIdEqualTo(StringUtils.isNotEmpty(param.getProjectId())?Long.parseLong(param.getProjectId()):0L);
        List<ProjectDictionary> dictItems = projectDictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)) {
            return dictItems.get(0);
        }
        return null;
    }


    public ProjectDictionary getDictByEnNameNew(ProjectDictionaryDto param, String parentId) {
        ProjectDictionaryExample example = new ProjectDictionaryExample();
        ProjectDictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andEnNameEqualTo(param.getEnName());
        criteria.andParentIdEqualTo(parentId);
        criteria.andProjectIdEqualTo(StringUtils.isNotEmpty(param.getProjectId())?Long.parseLong(param.getProjectId()):0L);
        List<ProjectDictionary> dictItems = projectDictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)) {
            return dictItems.get(0);
        }
        return null;
    }

    public ProjectDictionary getDictByCodeNew(ProjectDictionaryDto param, String parentId) {
        if (StringUtils.isBlank(param.getCode())) {
            return null;
        }
        ProjectDictionaryExample example = new ProjectDictionaryExample();
        ProjectDictionaryExample.Criteria criteria = example.createCriteria();
        criteria.andCodeEqualTo(param.getCode());
        criteria.andParentIdEqualTo(parentId);
        criteria.andProjectIdEqualTo(StringUtils.isNotEmpty(param.getProjectId())?Long.parseLong(param.getProjectId()):0L);
        List<ProjectDictionary> dictItems = projectDictionaryMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(dictItems)) {
            return dictItems.get(0);
        }
        return null;
    }
}
