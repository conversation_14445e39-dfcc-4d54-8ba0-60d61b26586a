package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.crf.TemplateFormDvpRuleParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormDvpRuleVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.mapper.TemplateFormDvpRuleMapper;
import com.haoys.user.model.TemplateFormDvpRule;
import com.haoys.user.model.TemplateFormDvpRuleExample;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.service.ProjectTesteeDvpService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ProjectTesteeDvpServiceImpl extends BaseService implements ProjectTesteeDvpService {

    @Autowired
    private TemplateFormDvpRuleMapper templateFormDvpRuleMapper;


    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private TemplateConfigService templateConfigService;


    @Override
    public CommonPage<TemplateFormDvpRuleVo> getProjectTemplateDVPRuleList(String projectId, String visitId, String formId, String formDetailId, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String,Object> params = new HashMap<>();
        params.put("projectId", projectId);
        if(StringUtils.isNotBlank(visitId)){
            params.put("visitId", visitId);
        }
        if(StringUtils.isNotBlank(formId)){
            params.put("formId", formId);
        }
        if(StringUtils.isNotBlank(formDetailId)){
            params.put("formDetailId", formDetailId);
        }
        List<TemplateFormDvpRuleVo> dataList = templateFormDvpRuleMapper.getProjectTemplateDVPRuleListForPage(params);
        for (TemplateFormDvpRuleVo templateFormDVPRuleVo : dataList) {
            SystemUserInfoExtendVo userInfo = systemUserInfoService.getSystemUserInfoByUserId(templateFormDVPRuleVo.getCreateUserId());
            if(userInfo != null){
                templateFormDVPRuleVo.setCreateUserName(userInfo.getRealName());
            }
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public List<TemplateFormDvpRuleVo> getFormDetailDvpContent(String projectId, String visitId, String formId, String formDetailId) {
        List<TemplateFormDvpRuleVo> dataList = new ArrayList<>();
        TemplateFormDvpRuleExample example = new TemplateFormDvpRuleExample();
        TemplateFormDvpRuleExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        criteria.andFormTableIdIsNull();
        criteria.andStatusEqualTo("0");
        List<TemplateFormDvpRule> templateFormDvpRuleList = templateFormDvpRuleMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(templateFormDvpRuleList)){
            for (TemplateFormDvpRule templateFormDVPRule : templateFormDvpRuleList) {
                TemplateFormDvpRuleVo templateFormDVPRuleVo = new TemplateFormDvpRuleVo();
                BeanUtils.copyProperties(templateFormDVPRule, templateFormDVPRuleVo);
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
                if(templateFormDetailConfig != null){
                    templateFormDVPRuleVo.setKey(templateFormDetailConfig.getFieldName());
                }
                dataList.add(templateFormDVPRuleVo);
            }
        }
        return dataList;
    }

    @Override
    public List<TemplateFormDvpRuleVo> getTableDetailDvpContent(String projectId, String visitId, String formId, String formDetailId, String tableId) {
        List<TemplateFormDvpRuleVo> dataList = new ArrayList<>();
        TemplateFormDvpRuleExample example = new TemplateFormDvpRuleExample();
        TemplateFormDvpRuleExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        criteria.andFormTableIdEqualTo(Long.parseLong(tableId));
        criteria.andStatusEqualTo("0");
        List<TemplateFormDvpRule> templateFormDvpRuleList = templateFormDvpRuleMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(templateFormDvpRuleList)){
            for (TemplateFormDvpRule templateFormDVPRule : templateFormDvpRuleList) {
                TemplateFormDvpRuleVo templateFormDVPRuleVo = new TemplateFormDvpRuleVo();
                BeanUtils.copyProperties(templateFormDVPRule, templateFormDVPRuleVo);
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(formDetailId);
                if(templateFormDetailConfig != null){
                    templateFormDVPRuleVo.setKey(templateFormDetailConfig.getFieldName());
                }
                dataList.add(templateFormDVPRuleVo);
            }
        }
        return dataList;
    }

    @Override
    public CustomResult saveProjectTemplateFormDetailDvpRule(TemplateFormDvpRuleParam templateFormDVPRuleParam) {
        CustomResult customResult = new CustomResult();
        if(templateFormDVPRuleParam.getId() == null){
            List<TemplateFormDvpRuleVo> templateFormDvpRuleVoList = getFormDetailDvpContent(templateFormDVPRuleParam.getProjectId().toString(), templateFormDVPRuleParam.getVisitId().toString(), templateFormDVPRuleParam.getFormId().toString(), templateFormDVPRuleParam.getFormDetailId().toString());
            for (TemplateFormDvpRuleVo templateFormDVPRuleVo : templateFormDvpRuleVoList) {
                if(templateFormDVPRuleVo.getFormTableId() == null && templateFormDVPRuleVo.getFormDetailId().equals(templateFormDVPRuleParam.getFormDetailId()) && templateFormDVPRuleVo.getQueryMethod().equals(templateFormDVPRuleParam.getQueryMethod())){
                    customResult.setMessage(BusinessConfig.PROJECT_TESTEE_DVP_FOUND);
                    return customResult;
                }else{
                    //如果是定制表格 验证tableRowIndex
                    if(templateFormDVPRuleParam.getCustomTable()){
                        if(ObjectUtil.equal(templateFormDVPRuleVo.getFormTableId(), templateFormDVPRuleParam.getFormTableId()) & ObjectUtil.equal(templateFormDVPRuleVo.getTableRowIndex(), templateFormDVPRuleParam.getTableRowIndex()) & ObjectUtil.equal(templateFormDVPRuleVo.getQueryMethod(), templateFormDVPRuleParam.getQueryMethod())){
                            customResult.setMessage(BusinessConfig.PROJECT_TESTEE_DVP_FOUND);
                            return customResult;
                        }
                    }else{
                        if(ObjectUtil.equal(templateFormDVPRuleVo.getFormTableId(), templateFormDVPRuleParam.getFormTableId()) & ObjectUtil.equal(templateFormDVPRuleVo.getQueryMethod(), templateFormDVPRuleParam.getQueryMethod())){
                            customResult.setMessage(BusinessConfig.PROJECT_TESTEE_DVP_FOUND);
                            return customResult;
                        }
                    }
                }
            }
            TemplateFormDvpRule templateFormDVPRule = new TemplateFormDvpRule();
            BeanUtils.copyProperties(templateFormDVPRuleParam, templateFormDVPRule);
            templateFormDVPRule.setId(SnowflakeIdWorker.getUuid());
            templateFormDVPRule.setCreateUserId(templateFormDVPRuleParam.getCreateUserId());
            templateFormDVPRule.setCreateTime(new Date());
            templateFormDVPRule.setStatus("0");
            templateFormDvpRuleMapper.insertSelective(templateFormDVPRule);
        }else{
            TemplateFormDvpRule templateFormDVPRule = templateFormDvpRuleMapper.selectByPrimaryKey(templateFormDVPRuleParam.getId());
            if(templateFormDVPRule == null){
                customResult.setMessage(BusinessConfig.PROJECT_TESTEE_DVP_NOT_FOUND);
                return customResult;
            }
            BeanUtils.copyProperties(templateFormDVPRuleParam, templateFormDVPRule);
            templateFormDVPRule.setUpdateUserId(templateFormDVPRuleParam.getCreateUserId());
            templateFormDVPRule.setUpdateTime(new Date());
            templateFormDvpRuleMapper.updateByPrimaryKeySelective(templateFormDVPRule);
        }
        return customResult;
    }

    @Override
    public CustomResult updateProjectTemplateFormDetailDvpRule(String id, String enabled, String createUserId) {
        CustomResult customResult = new CustomResult();
        TemplateFormDvpRule templateFormDVPRule = templateFormDvpRuleMapper.selectByPrimaryKey(Long.parseLong(id));
        if(templateFormDVPRule != null){
            templateFormDVPRule.setEnabled(true);
            templateFormDVPRule.setUpdateUserId(createUserId);
            templateFormDVPRule.setUpdateTime(new Date());
            templateFormDvpRuleMapper.updateByPrimaryKeySelective(templateFormDVPRule);
        }
        return customResult;
    }

    @Override
    public TemplateFormDvpRuleVo getTemplateFormDvpRuleRecord(String projectId, String id) {
        TemplateFormDvpRule templateFormDVPRule = templateFormDvpRuleMapper.selectByPrimaryKey(Long.parseLong(id));
        if(templateFormDVPRule != null){
            TemplateFormDvpRuleVo templateFormDVPRuleVo = new TemplateFormDvpRuleVo();
            BeanUtils.copyProperties(templateFormDVPRule, templateFormDVPRuleVo);
            return templateFormDVPRuleVo;
        }
        return null;
    }

    @Override
    public CustomResult saveBatchProjectTemplateFormDetailDvpRule(List<TemplateFormDvpRuleParam> params, String createUserId) {
        CustomResult customResult = new CustomResult();
        for (TemplateFormDvpRuleParam templateFormDVPRuleParam : params) {
            //查询是否存在非空变量规则
            List<TemplateFormDvpRuleVo> templateFormDvpRuleVoList = getFormDetailDvpContent(templateFormDVPRuleParam.getProjectId().toString(), templateFormDVPRuleParam.getVisitId().toString(), templateFormDVPRuleParam.getFormId().toString(), templateFormDVPRuleParam.getFormDetailId().toString());
            for (TemplateFormDvpRuleVo templateFormDVPRuleVo : templateFormDvpRuleVoList) {
                if(templateFormDVPRuleVo.getFormDetailId().equals(templateFormDVPRuleParam.getFormDetailId()) && templateFormDVPRuleVo.getQueryMethod().equals(templateFormDVPRuleParam.getQueryMethod())){
                    continue;
                }
            }
            templateFormDVPRuleParam.setCreateUserId(createUserId);
            customResult = saveProjectTemplateFormDetailDvpRule(templateFormDVPRuleParam);
        }
        return customResult;
    }
}
