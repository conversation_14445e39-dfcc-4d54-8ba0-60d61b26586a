package com.haoys.user.service.impl;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.project.ProjectTesteeConfigParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeConfigVo;
import com.haoys.user.mapper.ProjectTesteeConfigMapper;
import com.haoys.user.model.ProjectTesteeConfig;
import com.haoys.user.model.ProjectTesteeConfigExample;
import com.haoys.user.service.ProjectTesteeConfigService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ProjectTesteeConfigServiceImpl implements ProjectTesteeConfigService {

    @Resource
    private ProjectTesteeConfigMapper projectTesteeConfigMapper;

    @Override
    public CustomResult saveProjectTesteeConfig(ProjectTesteeConfigParam projectTesteeConfigParam) {
        CustomResult customResult = new CustomResult();
        if(projectTesteeConfigParam.getId() == null){
            ProjectTesteeConfigVo projectTesteeConfigVo = getProjectTesteeConfig(projectTesteeConfigParam.getProjectId().toString());
            if(projectTesteeConfigVo != null){
                customResult.setMessage(BusinessConfig.PROJECT_TESTEE_CONFIG_FOUND);
                return customResult;
            }
            ProjectTesteeConfig projectTesteeConfig = new ProjectTesteeConfig();
            BeanUtils.copyProperties(projectTesteeConfigParam, projectTesteeConfig);
            if(projectTesteeConfigParam.getConfigData() != null){
                projectTesteeConfig.setConfigData(projectTesteeConfigParam.getConfigData().toString());
            }
            if(projectTesteeConfigParam.getBindConfig() != null){
                projectTesteeConfig.setBindConfig(projectTesteeConfigParam.getBindConfig().toString());
            }
            if(projectTesteeConfigParam.getBindBaseConfig() != null){
                projectTesteeConfig.setBindBaseConfig(projectTesteeConfigParam.getBindBaseConfig().toString());
            }
            projectTesteeConfig.setId(SnowflakeIdWorker.getUuid());
            projectTesteeConfig.setCreateTime(new Date());
            projectTesteeConfig.setStatus("0");
            projectTesteeConfigMapper.insertSelective(projectTesteeConfig);
        }else{
            ProjectTesteeConfig projectTesteeConfig = projectTesteeConfigMapper.selectByPrimaryKey(projectTesteeConfigParam.getId());
            if(projectTesteeConfig == null){
                customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                return customResult;
            }
            BeanUtils.copyProperties(projectTesteeConfigParam, projectTesteeConfig);
            if(projectTesteeConfigParam.getConfigData() != null){
                projectTesteeConfig.setConfigData(projectTesteeConfigParam.getConfigData().toString());
            }
            if(projectTesteeConfigParam.getBindConfig() != null){
                projectTesteeConfig.setBindConfig(projectTesteeConfigParam.getBindConfig().toString());
            }
            if(projectTesteeConfigParam.getBindBaseConfig() != null){
                projectTesteeConfig.setBindBaseConfig(projectTesteeConfigParam.getBindBaseConfig().toString());
            }
            projectTesteeConfig.setUpdateTime(new Date());
            projectTesteeConfig.setUpdateUserId(projectTesteeConfigParam.getCreateUserId());
            if(projectTesteeConfigParam.getBindConfig() != null && !projectTesteeConfigParam.getBindConfig().equals(projectTesteeConfig.getBindConfig())){
                projectTesteeConfig.setModifyFlag(true);
                projectTesteeConfig.setModifyTime(new Date());
            }
            projectTesteeConfigMapper.updateByPrimaryKey(projectTesteeConfig);
        }
        return customResult;
    }

    @Override
    public ProjectTesteeConfigVo getProjectTesteeConfig(String projectId) {
        if(StringUtils.isEmpty(projectId)){return null;}
        ProjectTesteeConfigVo projectTesteeConfigVo = new ProjectTesteeConfigVo();
        ProjectTesteeConfigExample example = new ProjectTesteeConfigExample();
        ProjectTesteeConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        List<ProjectTesteeConfig> projectTesteeConfigList = projectTesteeConfigMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(projectTesteeConfigList)){
            ProjectTesteeConfig projectTesteeConfig = projectTesteeConfigList.get(0);
            if(projectTesteeConfig != null){
                BeanUtils.copyProperties(projectTesteeConfig, projectTesteeConfigVo);
                return projectTesteeConfigVo;
            }
        }
        return null;
    }
}
