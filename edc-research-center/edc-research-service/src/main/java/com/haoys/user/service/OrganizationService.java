package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.dto.ProjectOrgParam;
import com.haoys.user.domain.vo.overview.ProjectComprehensiveVo;
import com.haoys.user.domain.vo.project.ProjectOrgIdentCodeVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.project.ProjectOrgExportVo;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.model.Organization;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectUserOrg;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


public interface OrganizationService {


    /**
     * 项目中心分页列表
     * @param projectId
     * @param orgId
     * @param name
     * @param officer
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<ProjectOrgVo> getProjectOrgListForPage(String projectId, String orgId, String name, String officer, Integer pageNum, Integer pageSize);

    List<ProjectOrgVo> getProjectOrgListByCondition(String projectId, String orgId, String name);

    /**
     * 添加项目中心
     * @param projectOrgParam
     * @return
     */
    CustomResult saveProjectOrgInfo(ProjectOrgParam projectOrgParam);


    /**
     * 编辑项目研究中心
     * @param projectId
     * @param orgId
     * @param code
     * @param officer
     * @param expands
     * @param userId
     * @return
     */
    CustomResult updateProjectOrgInfo(String projectId, String orgId, String code, String officer, String expands, String userId);

    /**
     * 查询系统用户所属中心
     * @param userId
     * @return
     */
    List<OrganizationVo> getUserOrgList(String userId);

    /**
     * 查询系统用户所属中心
     * @param userId
     * @return
     */
    OrganizationVo getSystemUserOrgInfo(String userId);

    /**
     * 查询项目用户所属中心
     * @param projectId 项目id
     * @param userId    用户id
     * @return
     */
    List<OrganizationVo> getProjectUserOrgList(String projectId, String userId);

    /**
     * 查询项目用户所属研究中心
     * @param projectId  项目id
     * @param userId     用户id
     * @param projectOrgId
     * @param projectOrgCode
     * @return
     */
    OrganizationVo getProjectUserOrgInfo(String projectId, String userId, String projectOrgId, String projectOrgCode);

    /**
     * 项目中心下拉框 - CRC显示当前加入的中心 项目负责人显示所有中心
     * @param projectId
     * @param createUserId
     * @param showProjectOrgCode
     * @param showSelectAllOption
     * @return
     */
    List<OrganizationVo> getOrganizationListForCombobox(String projectId, String orgName, String createUserId, String showProjectOrgCode, String showSelectAllOption);

    /**
     * 查询企业中心基本信息
     * @param orgId
     * @return
     */
    Organization getSystemOrganizationInfo(String orgId);

    /**
     * 保存系统用户所属中心
     * @param userId
     * @param orgId
     * @return
     */
    String saveSystemUserOrgInfo(String userId, String orgId);

    /**
     * 保存项目用户研究中心
     * @param projectId
     * @param userId
     * @param systemOrgId
     * @param projectOrgCode
     * @return
     */
    String saveProjectUserOrgInfo(String projectId, String userId, String systemOrgId, String projectOrgCode);


    /**
     * 删除项目研究中心
     * @param projectId
     * @param projectOrgId
     * @return
     */
    CustomResult deleteProjectOrgByOrgId(String projectId, String projectOrgId);

    ProjectUserOrg getProjectUserOrgInfo(String projectId, String userId, String orgId);


    String saveBatchProjectOrg(MultipartFile file, String projectId, List<ProjectOrgExportVo> errorList, String createUserId) throws Exception;

    /**
     * 删除用户所属系统中心
     * @param userId
     * @return
     */
    String deleteSystemUserOrgInfo(String userId);

    /**
     * 删除项目用户所属研究中心
     * @param projectId
     * @param userId
     * @param orgId
     */
    void deleteProjectUserOrgInfo(String projectId, String userId, String orgId);

    /**
     * 中心概览数据
     * @param projectId
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectComprehensiveVo> getProjectComprehensiveList(String projectId, String userId, Integer pageNum, Integer pageSize);

    /**
     * 查询项目下所有研究中心列表
     * @param projectId
     * @return
     */
    List<String> getProjectUserOrgListByProjectId(String projectId);


    ProjectOrgInfo getProjectOrgInfo(String projectOrgId);

    /**
     * 通过项目唯一机构识别码获取项目信息和机构信息
     * @param projectId 项目id
     * @param identCode 唯一机构识别码
     * @return ProjectVo
     */
    ProjectVo getProjectByOrgIdentCode(String projectId, String identCode);

    /**
     * 通过项目中心id获取中心识别码
     * @param orgId 项目中心id
     * @return
     */
    ProjectOrgIdentCodeVo getProjectOrgInfoByOrgIdentCode(String orgId);

    /**
     * 通过项目识别码查询项目
     * @param identCode
     * @return
     */
    List<ProjectOrgInfo> selectProjectByIdentCode(String identCode);

    /**
     * 查询平台默认研究中心
     * @param systemTenantId
     * @param systemPlatformId
     * @return
     */
    Long getDefaultProjectOrgId(String systemTenantId, String systemPlatformId);

    void initProjectOrgRole(ProjectOrgParam projectOrgParam, Long projectOrgId);
    
    ProjectOrgInfo getProjectOrganizationInfo(String projectId);
}
