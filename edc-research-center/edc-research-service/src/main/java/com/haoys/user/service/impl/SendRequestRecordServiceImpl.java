package com.haoys.user.service.impl;

import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.thirdservice.PlatformBussinessConfig;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.mapper.SendRequestRecordMapper;
import com.haoys.user.model.SendRequestRecord;
import com.haoys.user.service.SendRequestRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class SendRequestRecordServiceImpl extends BaseService implements SendRequestRecordService {

    @Autowired
    private SendRequestRecordMapper sendRequestRecordMapper;

    @Override
    public void saveSendRequestRecord(String url, String params, String data, String status, String bussinessId, String userId) {
        SendRequestRecord sendRequestRecord = new SendRequestRecord();
        sendRequestRecord.setId(SnowflakeIdWorker.getUuid());
        sendRequestRecord.setPlatformName(PlatformBussinessConfig.RESEACHER_PLATFORM_NAME);
        sendRequestRecord.setUrl(url);
        sendRequestRecord.setParams(params);
        sendRequestRecord.setReturnData(data);
        sendRequestRecord.setStatus(status);
        sendRequestRecord.setCreateTime(new Date());
        sendRequestRecord.setBussinessId(bussinessId);
        sendRequestRecord.setOperator(userId);
        sendRequestRecord.setResendFlag("0");
        sendRequestRecordMapper.insertSelective(sendRequestRecord);
    }
}
