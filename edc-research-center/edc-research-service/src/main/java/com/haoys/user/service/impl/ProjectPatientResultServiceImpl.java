package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.enums.PatientTaskComplateEnum;
import com.haoys.user.enums.PatientTaskRateEnum;
import com.haoys.user.enums.PatientTaskTypeEnum;
import com.haoys.user.enums.PatientTaskViewResultEnum;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.vo.patient.PatientTaskFormValueVo;
import com.haoys.user.domain.vo.project.ProjectPlanVo;
import com.haoys.user.domain.vo.project.ProjectTaskVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.mapper.ProjectPatientResultMapper;
import com.haoys.user.model.ProjectPatientCalendar;
import com.haoys.user.model.ProjectPatientPlan;
import com.haoys.user.model.ProjectPatientResult;
import com.haoys.user.model.ProjectPatientResultExample;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectVisitTesteeRecord;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.service.ProjectPatientCalendarService;
import com.haoys.user.service.ProjectPatientResultService;
import com.haoys.user.service.ProjectPlanManageService;
import com.haoys.user.service.ProjectTaskManageService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ProjectPatientResultServiceImpl implements ProjectPatientResultService {

    @Resource
    private ProjectPatientResultMapper projectPatientResultMapper;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private ProjectTesteeResultService projectTesteeResultService;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private ProjectPlanManageService projectPlanManageService;
    @Autowired
    private ProjectTaskManageService projectTaskManageService;
    @Autowired
    private ProjectPatientCalendarService projectPatientCalendarService;
    @Autowired
    private TemplateConfigService templateConfigService;

    @Override
    public CustomResult savePatientResult(ProjectPatientResult projectPatientResult) {
        CustomResult customResult = new CustomResult();
        if(projectPatientResult.getId() == null){
            projectPatientResult.setId(SnowflakeIdWorker.getUuid());
            projectPatientResult.setCreateTime(new Date());
            projectPatientResultMapper.insert(projectPatientResult);
        }else{
            ProjectPatientResult projectPatientResultVo = projectPatientResultMapper.selectByPrimaryKey(projectPatientResult.getId());
            BeanUtils.copyProperties(projectPatientResult, projectPatientResultVo);
            projectPatientResultMapper.updateByPrimaryKeySelective(projectPatientResultVo);
        }
        return customResult;
    }

    @Override
    public int getPatientCurrentDateTaskCount(Long projectId, Long planId, Long taskId, Long testeeId) {
        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andPlanIdEqualTo(planId);
        criteria.andTaskIdEqualTo(taskId);
        criteria.andTesteeIdEqualTo(testeeId);
        criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        return (int)projectPatientResultMapper.countByExample(example);
    }

    @Override
    public int getPatientCurrentDateTableTaskCount(Long projectId, Long planId, Long taskId, Long testeeId) {
        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andPlanIdEqualTo(planId);
        criteria.andTaskIdEqualTo(taskId);
        criteria.andTesteeIdEqualTo(testeeId);
        criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        List<ProjectPatientResult> projectPatientResultList = projectPatientResultMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectPatientResultList)){
            return projectPatientResultList.get(0).getComplateCount();
        }
        return 0;
    }

    @Override
    public int getPatientTaskCountByTaskDateAndSendRate(Long projectId, Long planId, Long taskId, Long testeeId, Date taskDate, Date nextTaskDate, String sendRate) {
        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andPlanIdEqualTo(planId);
        criteria.andTaskIdEqualTo(taskId);
        criteria.andTesteeIdEqualTo(testeeId);
        if(StringUtils.isNotEmpty(sendRate)){
            criteria.andTaskRateEqualTo(sendRate);
        }

        if(taskDate != null && nextTaskDate != null){
            criteria.andTaskDateBetween(taskDate, nextTaskDate);
        }
        return (int)projectPatientResultMapper.countByExample(example);
    }

    @Override
    public ProjectPatientResult getRecentPatientTask(Long projectId, Long planId, Long taskId, Long testeeId) {
        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andPlanIdEqualTo(planId);
        criteria.andTaskIdEqualTo(taskId);
        criteria.andTesteeIdEqualTo(testeeId);
        example.setOrderByClause("create_time desc");
        List<ProjectPatientResult> projectPatientResults = projectPatientResultMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectPatientResults)){
            return projectPatientResults.get(0);
        }
        return null;
    }

    @Override
    public boolean getPatientTaskComplateResult(String projectId, String planId, String testeeId, String taskDate) {
        List<String> countList = new ArrayList<>();
        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        if(StringUtils.isNotEmpty(taskDate)){
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(taskDate, DateUtil.DEFAULTFORMAT));
        }else{
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        }
        List<ProjectPatientResult> projectPatientResults = projectPatientResultMapper.selectByExample(example);
        for (ProjectPatientResult projectPatientResult : projectPatientResults) {
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectPatientResult.getComplateStatus())){
                countList.add(projectPatientResult.getComplateStatus());
            }
        }
        return countList.size() != 0 && countList.size() == projectPatientResults.size();
    }

    @Override
    public PatientTaskFormValueVo getPatientTaskCalendarDetailByTaskDate(String projectId, String planId, String testeeId, String taskDate) {
        PatientTaskFormValueVo patientTaskFormValueVo = new PatientTaskFormValueVo();

        patientTaskFormValueVo.setProjectId(projectId);
        patientTaskFormValueVo.setPlanId(planId);
        ProjectPlanVo projectPlanVo = projectPlanManageService.getProjectPlanInfo(planId);
        if (projectPlanVo == null) {
            patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_01.getCode());
            return patientTaskFormValueVo;
        }
        if (projectPlanVo.getIfOpen()) {
            ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", projectPlanVo.getVisitId().toString(), projectPlanVo.getFormId().toString(), "", projectPlanVo.getFormDetailId().toString(), testeeId);
            if (projectTesteeResult != null) {
                if(StringUtils.isNotEmpty(projectTesteeResult.getFieldValue())){
                    List<String> dataList = new ArrayList<>();
                    String formDetailValue = projectPlanVo.getFormDetailValue();
                    String[] array = formDetailValue.split(",");
                    for (String value : array) {
                        System.out.println("getPatientTaskCalendarDetailByTaskDate getFieldValue: " + projectTesteeResult.getFieldValue());
                        System.out.println("getPatientTaskCalendarDetailByTaskDate value: " + value);
                        if (projectTesteeResult.getFieldValue().contains(value)) {
                            dataList.add(value);
                        }
                    }
                    if(dataList.size() != array.length){
                        String userIdValue = "";
                        Long userId = SecurityUtils.getUserId();
                        if(userId != null){
                            userIdValue = userId.toString();
                        }
                        String username = SecurityUtils.getUserName();
                        String params = "projectId=" + projectId + ",planId=" + planId + ",testeeId=" + testeeId + ",taskDate=" + taskDate;
                        String result = PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_03.getValue() +" dataList >>>>>"+ JSON.toJSONString(dataList) +",array>>>>>>>>>"+ JSON.toJSONString(array);
                        String className = "com.haoys.mis.service.impl.ProjectPatientResultServiceImpl";
                        String methodName = "getPatientTaskCalendarDetailByTaskDate()";
                        AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertSystemPointLog(userIdValue, username, className, methodName, params, result));
                        //匹配成功 显示方案详情 按照任务task明细展示
                        patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_03.getCode());
                        //return patientTaskFormValueVo;
                    }
                }
            }
        }

        patientTaskFormValueVo.setPlanName(projectPlanVo.getPlanName());
        boolean complateState = this.getPatientTaskComplateResult(projectId, patientTaskFormValueVo.getPlanId(), testeeId, taskDate);
        patientTaskFormValueVo.setFinishAllFormState(complateState);

        List<PatientTaskFormValueVo.PatientTaskFormVo> taskFormVariableList = new ArrayList<>();

        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(taskDate, DateUtil.DEFAULTFORMAT));
        List<ProjectPatientResult> projectPatientResultList = projectPatientResultMapper.selectByExample(example);
        for (ProjectPatientResult projectPatientResult : projectPatientResultList) {
            PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo = new PatientTaskFormValueVo.PatientTaskFormVo();
            ProjectTaskVo projectTaskVo = projectTaskManageService.getProjectTaskInfo(projectPatientResult.getTaskId().toString());
            patientTaskFormVo.setTaskId(projectPatientResult.getTaskId());
            patientTaskFormVo.setTaskName(projectTaskVo.getTaskName());
            patientTaskFormVo.setRemark(projectTaskVo.getDescription());
            patientTaskFormVo.setTaskTypeId(projectTaskVo.getType());
            patientTaskFormVo.setTaskTypeViewName(PatientTaskTypeEnum.getValue(projectTaskVo.getType()));
            patientTaskFormVo.setVisitId(projectTaskVo.getVisitId());
            patientTaskFormVo.setFormId(projectTaskVo.getFormId());
            patientTaskFormVo.setComplateStatus(projectPatientResult.getComplateStatus());

            List<ProjectPlanVo.ProjectPatientPlanVariableVo> projectPatientPlanVariableVoList = projectPlanVo.getDataList();
            for (ProjectPlanVo.ProjectPatientPlanVariableVo patientPlanVariableVo : projectPatientPlanVariableVoList) {
                if(patientPlanVariableVo.getTaskId().equals(projectPatientResult.getTaskId()) && PatientTaskRateEnum.PROJECT_TASK_04.getName().equals(patientPlanVariableVo.getSendRate())){
                    patientTaskFormVo.setIdentification("月");
                }
            }
            patientTaskFormVo.setFinishFormState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectPatientResult.getComplateStatus()));
            taskFormVariableList.add(patientTaskFormVo);
        }
        patientTaskFormValueVo.setDataList(taskFormVariableList);
        return patientTaskFormValueVo;
    }

    @Override
    public void initProjectPatientTask(String projectId, String planId) {
        //每天写入患者端待完成任务
        List<ProjectVisitUser> projectVisitUserList = projectTesteeInfoService.getProjectVisitUserList(projectId, "");
        for (ProjectVisitUser projectVisitUser : projectVisitUserList) {
            List<PatientTaskFormValueVo.PatientTaskFormVo> taskFormValueVoDataList = initProjectPatientTaskList();
            for (PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo : taskFormValueVoDataList) {
                ProjectPatientCalendar projectPatientCalendar = projectPatientCalendarService.getCurrentCalendarPatientTask(projectId, planId, projectVisitUser.getTesteeId().toString());
                if(projectPatientCalendar == null){
                    projectPatientCalendarService.saveCurrentDateCalendarPatientTask(projectId, planId, projectVisitUser.getTesteeId().toString(), true);
                }
                ProjectPatientResult projectPatientResult = this.getCurrentDatePatientTask(projectId, planId, patientTaskFormVo.getTaskId().toString(), null, projectVisitUser.getTesteeId().toString());
                if(projectPatientResult == null){
                    this.saveCurrentDatePatientTask(projectId, planId, patientTaskFormVo.getTaskId().toString(), projectVisitUser.getTesteeId().toString());
                }
            }
            if(CollectionUtil.isEmpty(taskFormValueVoDataList)){
                projectPatientCalendarService.saveCurrentDateCalendarPatientTask(projectId, planId, projectVisitUser.getTesteeId().toString(), false);
            }
        }
    }

    public void saveProjectPatientScheduleTask(String projectId, String planId, String testeeId, List<PatientTaskFormValueVo.PatientTaskFormVo> taskFormValueVoDataList){
        for (PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo : taskFormValueVoDataList) {
            ProjectPatientCalendar projectPatientCalendar = projectPatientCalendarService.getCurrentCalendarPatientTask(projectId, planId, testeeId);
            if(projectPatientCalendar == null){
                projectPatientCalendarService.saveCurrentDateCalendarPatientTask(projectId, planId, testeeId, true);
            }
            ProjectPatientResult projectPatientResult = this.getCurrentDatePatientTask(projectId, planId, patientTaskFormVo.getTaskId().toString(), null, testeeId);
            if(projectPatientResult == null){
                this.saveCurrentDatePatientTask(projectId, planId, patientTaskFormVo.getTaskId().toString(), testeeId);
            }
        }
        if(CollectionUtil.isEmpty(taskFormValueVoDataList)){
            projectPatientCalendarService.saveCurrentDateCalendarPatientTask(projectId, planId, testeeId, false);
        }

    }


    public void saveCurrentDatePatientTask(String projectId, String planId, String taskId, String testeeId) {
        ProjectPatientResult currentDatePatientTask = getCurrentDatePatientTask(projectId, planId, taskId, null, testeeId);
        if(currentDatePatientTask == null){
            ProjectPatientResult projectPatientResult = new ProjectPatientResult();
            projectPatientResult.setId(SnowflakeIdWorker.getUuid());
            projectPatientResult.setProjectId(Long.parseLong(projectId));
            projectPatientResult.setPlanId(Long.parseLong(planId));
            projectPatientResult.setTaskId(Long.parseLong(taskId));
            ProjectTaskVo projectTaskInfo = projectTaskManageService.getProjectTaskInfo(taskId);
            if(projectTaskInfo != null){
                projectPatientResult.setTaskName(projectTaskInfo.getTaskName());
                projectPatientResult.setTaskType(projectTaskInfo.getType());
            }
            projectPatientResult.setTesteeId(Long.parseLong(testeeId));
            projectPatientResult.setTaskDate(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
            projectPatientResult.setCreateTime(new Date());
            projectPatientResult.setComplateCount(0);
            projectPatientResult.setComplateStatus(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_01.getCode());
            projectPatientResultMapper.insert(projectPatientResult);
        }
    }

    @Override
    public void updateCheckProjectPatientTask(String projectId) {
        //按项目查询任务日历的数据
        List<ProjectPatientCalendar> projectPatientCalendarList = projectPatientCalendarService.getAllCalendarPatientTaskList(projectId);
        for (ProjectPatientCalendar projectPatientCalendar : projectPatientCalendarList) {
            boolean patientTaskComplateResult = this.getPatientTaskComplateResult(projectId, projectPatientCalendar.getPlanId().toString(), projectPatientCalendar.getTesteeId().toString(), DateUtil.formatDate2String(projectPatientCalendar.getTaskDate(),DateUtil.DEFAULTFORMAT));
            if(patientTaskComplateResult){
                projectPatientCalendar.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
            }else{
                projectPatientCalendar.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_01.getCode());
            }
            projectPatientCalendarService.updatePatientCalendarTaskById(projectPatientCalendar);
        }
    }

    public ProjectPatientResult getCurrentDatePatientTask(String projectId, String planId, String taskId, Date taskDate, String testeeId) {
        ProjectPatientResultExample example = new ProjectPatientResultExample();
        ProjectPatientResultExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andTaskIdEqualTo(Long.parseLong(taskId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        if(taskDate == null){
            criteria.andTaskDateEqualTo(DateUtil.getAutoParseDate(DateUtil.getCurrentDate(), DateUtil.DEFAULTFORMAT));
        }else{
            criteria.andTaskDateEqualTo(taskDate);
        }
        List<ProjectPatientResult> projectPatientResults = projectPatientResultMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectPatientResults)){
            return projectPatientResults.get(0);
        }
        return null;
    }


    //初始化患者端任务数据
    @Override
    public List<PatientTaskFormValueVo.PatientTaskFormVo> initProjectPatientTaskList() {
        List<PatientTaskFormValueVo.PatientTaskFormVo> taskFormVariableList = new ArrayList<>();
        //查询绑定项目的患者列表
        List<ProjectVisitUser> projectPatientUserList = projectTesteeInfoService.getProjectPatientUserList();
        for (ProjectVisitUser projectVisitUser : projectPatientUserList) {
            String testeeId = projectVisitUser.getTesteeId().toString();
            String projectId = projectVisitUser.getProjectId().toString();
            List<ProjectPatientPlan> projectPatientPlanList = projectPlanManageService.getProjectPlanListByProjectId(Long.parseLong(projectId));
            for (ProjectPatientPlan projectPatientPlan : projectPatientPlanList) {
                PatientTaskFormValueVo patientTaskFormValueVo = getPatientComputeTaskList(projectId, projectPatientPlan.getId().toString(), testeeId);
                if(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_01.getCode().equals(patientTaskFormValueVo.getViewTaskResult())){
                    String params = "projectId=" + projectId + ",planId=" + projectPatientPlan.getId().toString() + ",testeeId=" + testeeId;
                    String result = PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_01.getValue();
                    String className = "com.haoys.mis.service.impl.ProjectPatientResultServiceImpl";
                    String methodName = "initProjectPatientTaskList()";
                    AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertSystemPointLog(Constants.SYSTEM_ADMIN_ID, Constants.SYSTEM_ADMIN, className, methodName, params, result));
                    continue;
                }
                if(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_03.getCode().equals(patientTaskFormValueVo.getViewTaskResult())){
                    String params = "projectId=" + projectId + ",planId=" + projectPatientPlan.getId().toString() + ",testeeId=" + testeeId;
                    String result = PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_03.getValue();
                    String className = "com.haoys.mis.service.impl.ProjectPatientResultServiceImpl";
                    String methodName = "initProjectPatientTaskList()";
                    AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertSystemPointLog(Constants.SYSTEM_ADMIN_ID, Constants.SYSTEM_ADMIN, className, methodName, params, result));
                    continue;
                }
                if(CollectionUtil.isNotEmpty(patientTaskFormValueVo.getDataList())){
                    taskFormVariableList.addAll(patientTaskFormValueVo.getDataList());
                    saveProjectPatientScheduleTask(projectId, projectPatientPlan.getId().toString(), testeeId, patientTaskFormValueVo.getDataList());
                }
            }
        }
        return taskFormVariableList;
    }

    public PatientTaskFormValueVo getPatientComputeTaskList(String projectId, String planId, String testeeId) {
        //展示个性化方案  查询符合条件的方案
        PatientTaskFormValueVo patientTaskFormValueVo = new PatientTaskFormValueVo();
        //查询是否开启了前置条件
        ProjectPlanVo projectPlanVo = projectPlanManageService.getProjectPlanInfo(planId);
        if (projectPlanVo == null) {
            patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_01.getCode());
            return patientTaskFormValueVo;
        }
        patientTaskFormValueVo.setProjectId(projectId);
        patientTaskFormValueVo.setPlanId(planId);
        patientTaskFormValueVo.setPlanName(projectPlanVo.getPlanName());
        List<PatientTaskFormValueVo.PatientTaskFormVo> taskFormVariableList = new ArrayList<>();
        if (projectPlanVo.getIfOpen()) {
            ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", projectPlanVo.getVisitId().toString(), projectPlanVo.getFormId().toString(), "", projectPlanVo.getFormDetailId().toString(), testeeId);
            if (projectTesteeResult != null) {
                String fieldValue = projectTesteeResult.getFieldValue();
                if(StringUtils.isNotEmpty(fieldValue)){
                    List<String> dataList = new ArrayList<>();
                    String formDetailValue = projectPlanVo.getFormDetailValue();
                    String[] array = formDetailValue.split(",");
                    for (String value : array) {
                        if (fieldValue.contains(value)) {
                            dataList.add(value);
                        }
                    }
                    if(dataList.size() != array.length){
                        String params = "projectId=" + projectId + ",planId=" + planId + ",testeeId=" + testeeId;
                        String result = PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_03.getValue() +" dataList >>>>>"+ JSON.toJSONString(dataList) +",array>>>>>>>>>"+ JSON.toJSONString(array);
                        String className = "com.haoys.mis.service.impl.ProjectPatientResultServiceImpl";
                        String methodName = "getPatientComputeTaskList()";
                        AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertSystemPointLog(Constants.SYSTEM_ADMIN_ID, Constants.SYSTEM_ADMIN, className, methodName, params, result));
                        //匹配成功 显示方案详情 按照任务task明细展示
                        patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_03.getCode());
                        return patientTaskFormValueVo;
                    }
                }
            }
        }

        List<ProjectPlanVo.ProjectPatientPlanVariableVo> dataList = projectPlanVo.getDataList();
        for (ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo : dataList) {
            PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo = new PatientTaskFormValueVo.PatientTaskFormVo();
            patientTaskFormVo.setTaskId(projectPatientPlanVariableVo.getTaskId());
            ProjectTaskVo projectTaskVo = projectTaskManageService.getProjectTaskInfo(projectPatientPlanVariableVo.getTaskId().toString());
            patientTaskFormVo.setTaskName(projectTaskVo.getTaskName());
            patientTaskFormVo.setRemark(projectTaskVo.getDescription());
            patientTaskFormVo.setTaskTypeId(projectTaskVo.getType());
            patientTaskFormVo.setTaskTypeViewName(PatientTaskTypeEnum.getValue(projectTaskVo.getType()));
            patientTaskFormVo.setVisitId(projectTaskVo.getVisitId());
            patientTaskFormVo.setFormId(projectTaskVo.getFormId());

            if(PatientTaskRateEnum.PROJECT_TASK_04.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                patientTaskFormVo.setIdentification("月");
            }

            boolean complateState = getPatientTaskComplateResult(projectId, patientTaskFormValueVo.getPlanId(), testeeId, null);
            patientTaskFormVo.setFinishFormState(complateState);

            ProjectPatientResult recentPatientTask = this.getRecentPatientTask(Long.parseLong(projectId), Long.parseLong(planId), projectPatientPlanVariableVo.getTaskId(), Long.parseLong(testeeId));
            //普通表单任务与问卷表单任务
            if(PatientTaskTypeEnum.PROJECT_TASK_01.getName().equals(projectTaskVo.getType()) ||
                    PatientTaskTypeEnum.PROJECT_TASK_02.getName().equals(projectTaskVo.getType()) ||
                    PatientTaskTypeEnum.PROJECT_TASK_03.getName().equals(projectTaskVo.getType())){
                Boolean enableVisitWindow = projectPatientPlanVariableVo.getIfVisitWindow();
                //是否开启访视窗口期
                if (enableVisitWindow) {
                    Integer startVisitValue = projectPatientPlanVariableVo.getStartVisitValue();
                    Integer endVisitValue = projectPatientPlanVariableVo.getEndVisitValue();
                    //获取最新的访视信息
                    ProjectVisitVo projectVisitConfig = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(projectTaskVo.getVisitId().toString());
                    String preVisitId = projectVisitConfig.getPreVisitId() == null ? "0" : projectVisitConfig.getPreVisitId().toString();

                    //查询访视时间表单id
                    TemplateFormConfig templateFormConfig = templateConfigService.getFormIdByCoustomVisitName(projectId, preVisitId, BusinessConfig.PROJECT_LABEL_VISIT_NAME);
                    if(templateFormConfig == null){
                        continue;
                    }
                    ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getLastTesteeVisitFollowTime(projectId, preVisitId, templateFormConfig.getId().toString(), BusinessConfig.PROJECT_LABEL_VISIT_REG_NAME, testeeId);
                    if(projectTesteeResult == null){
                        //taskFormVariableList.add(patientTaskFormVo);
                    }else{
                        String fieldValue = projectTesteeResult.getFieldValue();
                        if(StringUtils.isNotBlank(fieldValue)){
                            fieldValue = fieldValue.replace("\"", "");
                        }
                        Date followUpRealTime = DateUtil.getAutoParseDate(fieldValue, DateUtil.DEFAULTFORMAT);
                        ProjectVisitTesteeRecord projectVisitTesteeRecord = projectVisitConfigService.getProjectVisitTesteeRecord(projectId, preVisitId, testeeId);
                        if(projectVisitTesteeRecord != null){
                            Date followUpNextTime = projectVisitTesteeRecord.getFollowUpNextTime();
                            if(followUpNextTime != null){
                                Date followEndDateTime = DateUtil.getDateAfter(followUpNextTime, endVisitValue);
                                Date preFollowDateTime = DateUtil.getDateAfter(followUpRealTime, startVisitValue);
                                if(DateUtil.belongCalendar(new Date(), preFollowDateTime, followEndDateTime)){
                                    if(recentPatientTask == null){
                                        taskFormVariableList.add(patientTaskFormVo);
                                    }
                                }
                            }
                        }
                    }
                }else{
                    //只推送一次
                    if(recentPatientTask == null || PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_01.getCode().equals(recentPatientTask.getComplateStatus())){
                        taskFormVariableList.add(patientTaskFormVo);
                    }
                }
            }

            //不良事件和合并用药
            if(PatientTaskTypeEnum.PROJECT_TASK_04.getName().equals(projectTaskVo.getType()) ||
                    PatientTaskTypeEnum.PROJECT_TASK_05.getName().equals(projectTaskVo.getType())){

                if(PatientTaskRateEnum.PROJECT_TASK_01.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                    String baseVisitId = projectPatientPlanVariableVo.getBaseVisitId().toString();
                    String baseFormId = projectPatientPlanVariableVo.getBaseFormId().toString();
                    String baseVariableId = projectPatientPlanVariableVo.getBaseVariableId().toString();
                    Integer baseVisitInterval = projectPatientPlanVariableVo.getBaseVisitInterval();
                    ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", baseVisitId, baseFormId, "", baseVariableId, testeeId);
                    if(projectTesteeResult != null){
                        Date testeeResultDate = DateUtil.getAutoParseDate(projectTesteeResult.getFieldValue(), DateUtil.DEFAULTFORMAT);
                        if(testeeResultDate != null){
                            Date testeeRateDate = DateUtil.getDateAfter(testeeResultDate, baseVisitInterval);
                            if(DateUtil.getCurrentdate().equals(testeeRateDate)){
                                taskFormVariableList.add(patientTaskFormVo);
                            }
                        }
                    }
                }else if(PatientTaskRateEnum.PROJECT_TASK_02.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                    //单日频次超出上限不再推送
                    int patientCurrentDateTaskCount = getPatientCurrentDateTableTaskCount(projectPlanVo.getProjectId(), projectPlanVo.getId(), projectTaskVo.getId(), Long.parseLong(testeeId));
                    if(patientCurrentDateTaskCount < projectPatientPlanVariableVo.getDaySendRate()){
                        taskFormVariableList.add(patientTaskFormVo);
                    }
                }else if(PatientTaskRateEnum.PROJECT_TASK_03.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                    //查询一周内任务是否完成 单日频次
                    boolean patientMatchTaskResult = getPatientMatchTaskResult(projectPlanVo.getProjectId(), projectPlanVo.getId(), projectTaskVo.getId(), testeeId, projectPatientPlanVariableVo.getSendRate(), projectPatientPlanVariableVo.getDaySendRate(), 7);
                    if(patientMatchTaskResult){
                        taskFormVariableList.add(patientTaskFormVo);
                    }
                }else if(PatientTaskRateEnum.PROJECT_TASK_04.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                    //30个工作日内完成任务不再推送
                    ProjectPatientResult projectPatientResult = getRecentPatientTask(projectPlanVo.getProjectId(), projectPlanVo.getId(), projectTaskVo.getId(), Long.parseLong(testeeId));
                    if(projectPatientResult == null){
                        taskFormVariableList.add(patientTaskFormVo);
                    }else{
                        Date taskDate = projectPatientResult.getTaskDate();
                        Date preTaskDate = DateUtil.getDateBefore(projectPatientResult.getTaskDate(), 30);
                        int count = getPatientTaskCountByTaskDateAndSendRate(projectPlanVo.getProjectId(), projectPlanVo.getId(), projectTaskVo.getId(), Long.parseLong(testeeId), preTaskDate, taskDate, null);
                        if(count < projectPatientPlanVariableVo.getDaySendRate()){
                            taskFormVariableList.add(patientTaskFormVo);
                        }
                    }
                }else if(PatientTaskRateEnum.PROJECT_TASK_05.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                    ProjectPatientResult projectPatientResult = getRecentPatientTask(projectPlanVo.getProjectId(), projectPlanVo.getId(), projectTaskVo.getId(), Long.parseLong(testeeId));
                    if(projectPatientResult == null){
                        taskFormVariableList.add(patientTaskFormVo);
                    }else{
                        Date taskDate = projectPatientResult.getTaskDate();
                        Date nextTaskDate = DateUtil.getDateBefore(projectPatientResult.getTaskDate(), 365);
                        int count = getPatientTaskCountByTaskDateAndSendRate(projectPlanVo.getProjectId(), projectPlanVo.getId(), projectTaskVo.getId(), Long.parseLong(testeeId), nextTaskDate, taskDate, null);
                        if(count < projectPatientPlanVariableVo.getDaySendRate()){
                            taskFormVariableList.add(patientTaskFormVo);
                        }
                    }
                }
            }
        }
        patientTaskFormValueVo.setDataList(taskFormVariableList);
        return patientTaskFormValueVo;
    }


    private boolean getPatientMatchTaskResult(Long projectId, Long planId, Long taskId, String testeeId, String sendRate, int daySendRate, int unit){
        ProjectPatientResult projectPatientResult = getRecentPatientTask(projectId, planId, taskId, Long.parseLong(testeeId));
        if(projectPatientResult == null){
            return true;
        }else{
            Date taskDate = projectPatientResult.getTaskDate();
            Date preTaskDate = DateUtil.getDateBefore(projectPatientResult.getTaskDate(), unit);
            int count = getPatientTaskCountByTaskDateAndSendRate(projectId, planId, taskId, Long.parseLong(testeeId), preTaskDate, taskDate, null);
            return count < daySendRate;
        }
    }


}
