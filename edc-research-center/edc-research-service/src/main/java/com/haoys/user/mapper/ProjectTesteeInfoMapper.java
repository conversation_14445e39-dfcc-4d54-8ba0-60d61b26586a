package com.haoys.user.mapper;

import com.haoys.user.domain.expand.ProjectVisitUserExpand;
import com.haoys.user.domain.param.project.ProjectTesteeStatisticsParam;
import com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo;
import com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo;
import com.haoys.user.domain.vo.participant.ProjectParticipantViewConfigVo;
import com.haoys.user.domain.vo.project.ProjectTesteeOrgVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeBaseVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeWrapperVo;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectTesteeInfoExample;
import com.haoys.user.model.ProjectVisitUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface ProjectTesteeInfoMapper {

    long countByExample(ProjectTesteeInfoExample example);

    int deleteByExample(ProjectTesteeInfoExample example);

    int deleteByPrimaryKey(Long id);
    
    ProjectTesteeInfo selectByPrimaryKey(Long id);

    int insert(ProjectTesteeInfo record);

    int insertSelective(ProjectTesteeInfo record);

    List<ProjectTesteeInfo> selectByExample(ProjectTesteeInfoExample example);

    int updateByExampleSelective(@Param("record") ProjectTesteeInfo record, @Param("example") ProjectTesteeInfoExample example);

    int updateByExample(@Param("record") ProjectTesteeInfo record, @Param("example") ProjectTesteeInfoExample example);

    int updateByPrimaryKeySelective(ProjectTesteeInfo record);

    int updateByPrimaryKey(ProjectTesteeInfo record);

    List<ProjectTesteeWrapperVo> getProjectTesteeListForPage(Map<String, Object> params);

    List<ProjectTesteeVo> getProjectTesteeAnalysisListByProjectId(String projectId, String planId, String orgId);

    List<ProjectParticipantViewConfigVo> getProjectParticipantListForPage(String projectId, String projectOrgId, String realName, String testeeCode,
                                                                          String ownerDoctorId, String researchStatus, String inheritor, String Instructor, String Batchnumber, String REDATE, @Param("sortField") String sortField, @Param("sortType") String sortType, String status);

    List<ProjectTesteeInfo> getTesteeCodeByProjectId(String projectId, String testeeCode, String ownerOrgId);

    ProjectTesteeInfo getTesteeInfoByMobile(String mobile);

    List<Map<Integer, Object>> getProjectGenderData(String projectId, String userId, @Param("orgIds") String orgIds);

    List<Map<Integer, Object>> getProjectAgeDistrbuteData(String projectId, String userId, @Param("orgIds") String orgIds);

    Long getProjectTesteeCount(@Param("projectId")String projectId, @Param("orgIds") String orgIds, @Param("customOrgId")  String customOrgId);

    List<ProjectTesteeStatisticsVo> list(ProjectTesteeStatisticsParam param);

    ProjectTesteeVo getTesteeBaseInfoByTesteeCode(String projectId, String testeeCode);

    List<ProjectTesteeInfo> getOrgIdsByTesteeIds(@Param("testeeIds") List<String> testeeIds);

    List<ProjectTesteeInfo> getProjectTesteeListByIds(@Param("projectId")String projectId,@Param("projectOrgId")String projectOrgId,@Param("testeeIds") List<String> testeeIdList);

    ProjectTesteeVo getProjectTesteeBaseInfoForVersion2(@Param("projectId")String projectId, @Param("testeeId")String testeeId);
    
    ProjectTesteeBaseVo selectProjectTesteeInfoByUserId(String userId, String tenantId, String platformId);

    List<ProjectTesteeOrgVo> getProjectTesteeBaseInfoByUserId(String projectId, String userId);

    @Select("")
    int getProjectTesteeCountByTCode(Long projectId, String testeeCode);
    
    ProjectTesteeProcessCountVo getProjectTesteeProcessCount(String projectId, String visitId, String createUserId);
    
    ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditCount(String projectId, String visitId, String createUserId);
    
    int getProjectTesteeInputCount(String projectId, String createUserId);
    
    ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditValue(String projectId, String visitId, String testeeId);
    
    ProjectTesteeProcessCountVo getProjectTesteeProcessValue(String projectId, String visitId, String testeeId);
    
    List<ProjectVisitUserExpand> getProjectTesteeUserListForBoRui(String projectId, String testeeCode, String realName);
    
    List<ProjectVisitUser> getProjectUncheckTesteeUserList(String projectId, String testeeCode);
}
