package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.flow.FlowParam;
import com.haoys.user.domain.param.project.ProjectVisitConfigParam;
import com.haoys.user.domain.vo.flow.ProjectFlowVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.ProjectVisitTesteeRecord;

import java.util.List;

public interface ProjectVisitConfigService {

    /**
     * 分页查询访视列表
     * @param visitName 访视名称
     * @param templateId 模版名称
     * @param projectId 项目id
     * @param pageSize  每页显示条数
     * @param pageNum   当前页码
     * @return
     */
    List<ProjectVisitConfig> getProjectVisitListForPage(String visitName, String templateId, String projectId, Integer pageSize, Integer pageNum);

    /**
     * 保存编辑CRF访视配置信息
     * @param projectVisitConfigParam
     * @return
     */
    CustomResult saveProjectVisitConfig(ProjectVisitConfigParam projectVisitConfigParam);

    /**
     * 查询项目访视列表
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @return
     */
    List<ProjectVisitConfig> getProjectVisitListByPlanId(String projectId, String planId, String visitId);


    /**
     * 根据templateId查询访视列表
     * @param templateId
     * @return
     */
    List<ProjectVisitConfig> getProjectVisitListByTemplateId(String templateId);

    /**
     * 查询访视基本信息
     * @param visitId
     * @return
     */
    ProjectVisitVo getProjectVisitBaseConfigByVisitId(String visitId);

    /**
     * 删除访视配置信息（含访视表单）
     * @param visitId
     * @param forceDelete
     * @param operator
     * @return
     */
    CustomResult deleteProjectVisitConfig(String visitId, String forceDelete, String operator);


    /**
     * 查询参与者预期和实际录入访视时间
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    ProjectVisitTesteeRecord getProjectVisitTesteeRecord(String projectId, String visitId, String testeeId);

    /**
     * 新增访视窗口时间记录
     * @param projectId
     * @param testeeId
     * @param visitId
     */
    void insertProjectVisitTesteeRecord(String projectId, String testeeId, String visitId);

    /**
     * 指定条件新增访视窗口数据
     * @param projectVisitTesteeRecordVo
     */
    void insertProjectVisitTesteeRecordSelective(ProjectVisitTesteeRecord projectVisitTesteeRecordVo);

    /**
     * 修改访视窗口数据
     * @param projectVisitTesteeRecord
     */
    void updateProjectVisitTesteeRecord(ProjectVisitTesteeRecord projectVisitTesteeRecord);

    /**
     * 代办任务访视数量
     * @param projectId
     * @param orgIds
     * @return
     */
    List<ProjectVisitTesteeRecord> getProjectVisitFollowRealTimeCount(String projectId, String orgIds);

    /**
     * 逾期待访视总量
     * @param projectId
     * @param orgIds
     * @return
     */
    int getProjectOverdueVisitFollowRealTimeNullCount(String projectId, String orgIds, String nextFollowRealNullValue);


    /**
     * 根据访视id获取下一次的访视信息
     * @param preVisitId
     * @return
     */
    ProjectVisitConfig getVisitConfigByPreVisitId(String preVisitId);

    /**
     * 查询当月计划访视总量
     * @param projectId
     * @param orgIds
     * @return
     */
    int getProjectPlannedVisitCount(String projectId, String orgIds);

    /**
     * 逾期已访视总量
     * @param projectId
     * @param orgIds
     * @return
     */
    List<ProjectVisitTesteeRecord> getProjectOverdueVisitFollowRealTimeNotNullCount(String projectId, String orgIds, String nextFollowRealNotNullValue);

    /**
     * 根据访视名称查询访视id
     * @param projectId
     * @param visitName
     * @return
     */
    ProjectVisitConfig getProjectVisitConfigByVisitName(String projectId, String visitName);

    /**
     * 获取研究流程列表
     * @param planId 计划id
     * @return
     */
    List<ProjectVisitConfig> list(Long planId);
    /**
     * 创建研究流程
     * @param param 创建研究流程数据
     * @return
     */
    CommonResult<Object> create(FlowParam param);
    /**
     * 根据访视名称查询访视id
     * @param param 创建研究流程
     * @return
     */
    CommonResult<Object> update(FlowParam param);

    /**
     * 删除研究流程
     * @param  id
     * @return
     */
    CommonResult<Object> delete(Long id);

    /**
     *
     * @param templateId
     * @param visitName
     * @return
     */
    List<ProjectVisitConfig> getByPlanIdOrName(Long templateId,String visitName);

    /**
     * 根据项目id获取当前绑定的访视和表单。
     * @param projectId 项目id
     * @param isH5 是否是h5调用
     * @return
     */
    List<ProjectFlowVo> getProjectVisitConfigByProjectId(String projectId,Boolean isH5);

    /**
     * 创建研究流程-移动
     * @param ids 流程id集合
     * @return
     */
    CommonResult<Object> move(List<Long> ids);

    /**
     * 根据项目id获取访视流程
     * @param projectId 项目id
     * @return  流程列表
     */
    List<ProjectVisitConfig> listByProjectId(Long projectId);
    
    List<ProjectVisitConfig> getProjectVisitConfigListByProjectId(String projectId);
}
