package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectChallengeParam;
import com.haoys.user.domain.param.project.ProjectChallengeQueryParam;
import com.haoys.user.domain.param.project.ProjectCloseChallengeParam;
import com.haoys.user.domain.param.project.ProjectCloseVariableChallengeParam;
import com.haoys.user.domain.param.project.ProjectReplyChallengeParam;
import com.haoys.user.domain.param.project.ProjectSystemChallengeParam;
import com.haoys.user.domain.vo.project.ProjectChallengeApplyVo;
import com.haoys.user.domain.vo.project.ProjectChallengeVo;
import com.haoys.user.model.ProjectTesteeChallenge;

import java.util.List;

public interface ProjectTesteeChallengeService {

    /**
     * 质疑列表
     * @param projectChallengeQueryParam
     * @return
     */
    CommonPage<ProjectChallengeVo> getProjectChallengeListForPage(ProjectChallengeQueryParam projectChallengeQueryParam);

    /**
     * 个人质疑
     * @param projectChallengeQueryParam
     * @return
     */
    CommonPage<ProjectChallengeVo> getUserChallengeListForPage(ProjectChallengeQueryParam projectChallengeQueryParam);


    /**
     * 创建项目质疑
     * @param projectChallengeParam
     * @return
     */
    CustomResult saveProjectChallenge(ProjectChallengeParam projectChallengeParam);

    /**
     * 回复质疑
     * @param projectReplyChallengeParam
     * @return
     */
    CustomResult saveReplyProjectChallenge(ProjectReplyChallengeParam projectReplyChallengeParam);

    /**
     * 关闭质疑
     * @param projectCloseChallengeParam
     * @return
     */
    CustomResult updateProjectChallengeStatus(ProjectCloseChallengeParam projectCloseChallengeParam);


    /**
     * 根据访视id查询质疑列表
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    List<ProjectChallengeVo> getProjectChallengeListByVisitId(String projectId, String visitId, String testeeId);

    /**
     * 根据formId查询质疑列表
     * @param projectId
     * @param visitId
     * @param formId
     * @return
     */
    List<ProjectChallengeVo> getProjectChallengeListByFormId(String projectId, String visitId, String formId, String testeeId);

    /**
     * 根据formIdDetalId查询质疑列表
     * @param projectId
     * @param visitId
     * @param formId
     * @return
     */
    List<ProjectChallengeVo> getProjectChallengeListByDetailId(String projectId, String visitId, String formId, String detailId, String testeeId);


    /**
     * 根据tableRowNo查询质疑列表
     * @param projectId
     * @param visitId
     * @param formId
     * @return
     */
    List<ProjectTesteeChallenge> getProjectChallengeListByTableRowNo(String projectId, String visitId, String formId, String tableRowNo, String testeeId);


    /**
     * 根据tableResultId查询质疑列表
     * @param projectId
     * @param visitId
     * @param formId
     * @param tableResultId
     * @param testeeId
     * @return
     */
    List<ProjectChallengeVo> getProjectChallengeListByTableId(String projectId, String visitId, String formId, String tableResultId, String testeeId);

    /**
     * 查询质疑回复列表
     * @param challengeId 质疑id
     * @return
     */
    List<ProjectChallengeApplyVo> getProjectChallengeApplyList(String challengeId);


    /**
     * 查询表单变量质疑列表
     * @param projectId
     * @param visitId
     * @param formId
     * @param testeeId
     * @param formResultId
     * @param formTableId
     * @return
     */
    List<ProjectChallengeVo> getChallengeList(String projectId, String visitId, String formId, String testeeId, String formResultId, String formTableId);

    /**
     * 设置质疑备注信息
     * @param projectChallengeParam
     * @return
     */
    CustomResult updateProjectChallenge(ProjectChallengeParam projectChallengeParam);

    /**
     * 查询未关闭的质疑总量
     * @param projectId
     * @param visitId
     * @param testeeId
     * @param orgIds
     * @return
     */
    int getProjectChallengeUnclosedCount(String projectId, String visitId, String testeeId, String orgIds);

    /**
     * 创建系统质疑-----逻辑核查触发
     * @param projectSystemChallengeParam
     * @return
     */
    CustomResult saveProjectSystemChallenge(ProjectSystemChallengeParam projectSystemChallengeParam);

    /**
     * 根据变量id查询系统质疑详情
     * @param projectId
     * @param visitId
     * @param formId
     * @param testeeId
     * @param formDetailId
     * @param formTableId
     * @param rowNumber
     * @param queryMethod
     * @return
     */
    List<ProjectChallengeVo> getProjectSystemChallageData(String projectId, String visitId, String formId, String testeeId, String formDetailId, String formTableId, String rowNumber, String queryMethod, String closeStatus);

    /**
     * 批量创建变量系统质疑
     * @param projectSystemChallengeParams
     * @param userId
     * @return
     */
    CustomResult saveBatchProjectSystemChallenge(List<ProjectSystemChallengeParam> projectSystemChallengeParams, String userId);

    /**
     * 批量关闭系统质疑
     * @param dataList
     * @param userId
     * @return
     */
    CustomResult updateBatchProjectChallengeStatus(List<ProjectCloseChallengeParam> dataList, String userId);

    /**
     * 根据变量id批量关闭系统质疑
     * @param variableList
     * @param userId
     * @return
     */
    CustomResult updateBatchProjectChallengeStatusByVariableId(List<ProjectCloseVariableChallengeParam> variableList, String userId);


    /**
     * 手动质疑发送消息
     * @param challenge
     * @return
     */
    CommonResult<Object> SaveCustomerChallenge(ProjectTesteeChallenge challenge);

    /**
     * 获取手动质疑发送的消息
     * @param challengeId
     * @return
     */
    CommonResult<ProjectTesteeChallenge> selectCustomerChallenge(Long challengeId);

    /**
     * 关闭质疑
     * @param challengeId 质疑ID
     * @return
     */
    CommonResult<Object> closeChallenge(String challengeId);

    /**
     * 个人质疑消息数量
     * @param projectChallengeQueryParam
     * @return
     */
    CommonResult<Object> getUserChallengeNum(ProjectChallengeQueryParam projectChallengeQueryParam);

}
