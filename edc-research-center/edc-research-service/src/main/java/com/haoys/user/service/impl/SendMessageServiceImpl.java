package com.haoys.user.service.impl;

import cn.hutool.core.lang.Validator;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.vo.system.SendMessageRecordVo;
import com.haoys.user.mapper.SendMessageRecordMapper;
import com.haoys.user.model.SendMessageRecord;
import com.haoys.user.service.SendMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class SendMessageServiceImpl implements SendMessageService {
    
    private final SendMessageRecordMapper sendMessageRecordMapper;
    
    
    @Override
    public void insertSendMessageRecord(String messageAccount, String code, String requestId, String responseText) {
        SendMessageRecord sendMessageRecord = new SendMessageRecord();
        sendMessageRecord.setId(SnowflakeIdWorker.getUuid());
        if(Validator.isMobile(messageAccount)){
            sendMessageRecord.setMobile(messageAccount);
        }else{
            sendMessageRecord.setEmail(messageAccount);
        }
        sendMessageRecord.setSendCode(code);
        sendMessageRecord.setRequestId(requestId);
        sendMessageRecord.setReturnData(responseText);
        sendMessageRecord.setStatus(BusinessConfig.VALID_STATUS);
        sendMessageRecord.setCreateTime(new Date());
        sendMessageRecord.setOperator("system");
        sendMessageRecordMapper.insertSelective(sendMessageRecord);
    }
    
    @Override
    public List<SendMessageRecord> getSendMessageList(Integer currentPage, Integer pageSize) {
        PageHelper.startPage(currentPage, pageSize);
        List<SendMessageRecord> messageList = sendMessageRecordMapper.selectByExample(null);
        return messageList;
    }
    
    @Override
    public List<SendMessageRecordVo> getSendMessageListForPage(String mobile, Integer currentPage, Integer pageSize) {
        PageHelper.startPage(currentPage, pageSize);
        mobile = "+86".concat(mobile);
        return sendMessageRecordMapper.getSendMessageListForPage(mobile);
    }
}
