package com.haoys.user.mapper;

import com.haoys.user.domain.param.project.ProjectChallengeQueryParam;
import com.haoys.user.domain.param.project.ProjectChallengeStatisticsParam;
import com.haoys.user.domain.vo.project.ProjectChallengeVo;
import com.haoys.user.domain.vo.testee.ProjectChallengeStatisticsVo;
import com.haoys.user.model.ProjectTesteeChallenge;
import com.haoys.user.model.ProjectTesteeChallengeExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface ProjectTesteeChallengeMapper {
    long countByExample(ProjectTesteeChallengeExample example);

    int deleteByExample(ProjectTesteeChallengeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeChallenge record);

    int insertSelective(ProjectTesteeChallenge record);

    List<ProjectTesteeChallenge> selectByExampleWithBLOBs(ProjectTesteeChallengeExample example);

    List<ProjectTesteeChallenge> selectByExample(ProjectTesteeChallengeExample example);

    ProjectTesteeChallenge selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeChallenge record, @Param("example") ProjectTesteeChallengeExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectTesteeChallenge record, @Param("example") ProjectTesteeChallengeExample example);

    int updateByExample(@Param("record") ProjectTesteeChallenge record, @Param("example") ProjectTesteeChallengeExample example);

    int updateByPrimaryKeySelective(ProjectTesteeChallenge record);

    int updateByPrimaryKeyWithBLOBs(ProjectTesteeChallenge record);

    int updateByPrimaryKey(ProjectTesteeChallenge record);

    List<ProjectChallengeVo> getProjectChallengeListForPage(Map<String, Object> params);

    List<ProjectChallengeVo> getProjectChallengeList(ProjectChallengeQueryParam param);

    /**
     * 获取质疑列表
     * @param param 搜索参数
     * @return List<ProjectChallengeStatisticsVo>
     */
    List<ProjectChallengeStatisticsVo> list(ProjectChallengeStatisticsParam param);

    /**
     * 获取质疑信息
     * @param ids 所属机构id集合
     * @param projectId 项目id
     * @return List<ProjectChallengeStatisticsVo>
     */
    List<ProjectChallengeStatisticsVo> getChallengeListByOrgIds(ProjectChallengeStatisticsParam param);
    /**
     * 质疑统计数量
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    List<ProjectChallengeStatisticsVo> challengeChart(ProjectChallengeStatisticsParam param);

    /**
     * 系统质疑统计数量
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    int getSystemChallenge(ProjectChallengeStatisticsParam param);
    /**
     * cra质疑统计数量
     * @param param 搜索条件（比如所属中心）
     * @return 柱状图的数据
     */
    int getCraChallenge(ProjectChallengeStatisticsParam param);

    /**
     * 获取手动质疑信息（获取当前用户有权限的信息）
     *
     * @param projectId 项目ID
     * @param visitId   访视ID
     * @param formId    表单ID
     * @param testeeId  受试者ID
     * @param roleId    角色集合
     * @param userId    用户ID
     * @param isSP
     * @return
     */
    List<ProjectTesteeChallenge> selectCustomChallengeList(String projectId, String projectOrgId, String visitId, String formId, String testeeId, List<Long> roleId, Long userId, boolean isSP);

    /**
     * 个人提醒消息质疑列表
     * @param param
     * @return
     */
    List<ProjectChallengeVo> getUserChallengeListForPage(ProjectChallengeQueryParam param);

    /**
     * 个人质疑消息数量
     * @param param
     * @return
     */
    Long getUserChallengeNum(ProjectChallengeQueryParam param);

}
