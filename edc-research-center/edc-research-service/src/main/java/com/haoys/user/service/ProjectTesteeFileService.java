package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.file.UploadProjectFileParam;
import com.haoys.user.domain.param.file.SaveUploadProjectFileParam;
import com.haoys.user.domain.vo.project.ProjectTesteeFileVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.model.ProjectTesteeFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

public interface ProjectTesteeFileService {


    /**
     * 上传项目资料文件
     * @param file
     * @param createUserId      操作人
     * @param projectId         项目id
     * @param visitId           访视id
     * @param formId            表单id
     * @param resourceId        变量id
     * @param tableId           表格单元格列id
     * @param rowNumber         行记录编号
     * @param testeeId          参与者id
     * @param collect           收藏标识
     * @param taskDate          患者端任务日期
     * @param openOCR           是否启用OCR识别 0/1
     * @param batchUpload       是否批量上传 0/1
     * @param medicalType       ocr类型
     * @param templateId        定制模版id
     * @param prePageNo         上一张单据编号
     * @param groupId           参与者字段组id
     * @param groupName         分组名称
     * @param imageType         图片类型
     * @param extendStruct      表单+表格
     * @param generalAccurate
     * @param mergeImage
     * @param mergeMethod
     * @param ifMontage
     * @param needMergeFileParam
     * @param batchOpenOcr
     * @return
     * @throws IOException
     */
    List<UploadFileResultVo> saveUploadProjectFile(MultipartFile[] file, String createUserId, String projectId, String visitId, String formId, String resourceId, String tableId,
                                                   String rowNumber, String testeeId, String collect, String taskDate, String openOCR, String batchUpload, String medicalType,
                                                   String templateId, String prePageNo, String groupId, String groupName, String imageType, String extendStruct, String generalAccurate,
                                                   String mergeImage, String mergeMethod, String ifMontage, String needMergeFileParam, String batchOpenOcr) throws IOException;

    /**
     * 重构后的文件上传方法，使用参数对象
     *
     * @param multipartFiles 上传的文件数组
     * @param param 封装的参数对象
     * @return 上传结果列表
     * @throws IOException IO异常
     */
    List<UploadFileResultVo> saveUploadProjectFileWithParam(MultipartFile[] multipartFiles, SaveUploadProjectFileParam param) throws IOException;

    /**
     * 查询项目表单图片
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeId
     * @param taskDate
     * @param resourceId
     * @param tableId
     * @param rowNumber
     * @param medicalType
     * @param openOCR
     * @param batchUpload
     * @param groupName
     * @param queryIgnoreMontage
     * @param batchOpenOcr
     * @return
     */
    List<ProjectTesteeFormImageVo> getProjectTesteeFormImageList(String projectId, String planId, String visitId, String formId, String testeeId, String taskDate, String resourceId, String tableId, String rowNumber, String medicalType, String openOCR, String batchUpload, String groupName, Boolean queryIgnoreMontage, String batchOpenOcr);

    /**
     * 根据文件id删除文件
     * @param fileId
     * @param operatorUserId
     * @return
     */
    CustomResult deleteProjectTesteeFileById(String fileId, String operatorUserId);

    /**
     * 标记表单与OCR图片关联
     * @param fileId
     * @param visitId
     * @param formId
     * @param userId
     * @param collect
     * @return
     */
    CustomResult updateProjectTesteeOCRFile(String fileId, String visitId, String formId, String userId, String collect);

    /**
     * 更新图片查询OCR结果
     * @param fileId
     * @param updateGroupResource
     * @param ocrMedicalResult
     * @param extendStruct
     * @param generalAccurate
     * @param operator
     * @param groupId
     * @param resourceId
     */
    void updateOcrMedicalReportDetection(String fileId, String updateGroupResource, String ocrMedicalResult, String extendStruct, String generalAccurate, String openOCR, String operator, String groupId, String resourceId);

    /**
     * 通过fileId查询图片信息
     * @param fileId
     * @return
     */
    ProjectTesteeFormImageVo getProjectTesteeFormImageByFileId(String fileId);

    /**
     * 通过fileId查询下一张图片信息
     * @param fileId
     * @return
     */
    ProjectTesteeFormImageVo getProjectTesteeNextImageByFileId(String fileId);

    /**
     * 裁剪图片合并
     * @param projectId
     * @param visitId
     * @param formId
     * @param resourceId
     * @param groupId
     * @param testeeId
     * @param fileIds
     * @param mergeImage
     * @param mergeMethod
     * @param openOCR
     * @param medicalType
     * @param extendStruct
     * @param generalAccurate
     * @param createUserId
     * @return
     */
    String updateMergeImages(String projectId, String visitId, String formId, String resourceId, String groupId, String testeeId, String fileIds, String mergeImage, String mergeMethod,
                             String openOCR, String medicalType, String extendStruct, String generalAccurate, String createUserId) throws FileNotFoundException;

    /**
     * 查询合成图片对应的缩略图列表
     * @param projectId
     * @param fileId
     * @return
     */
    List<ProjectTesteeFormImageVo> getProjectTesteeThumbnailImageByFileId(String projectId, String fileId);


    /**
     * 查询项目参与者待合成图片列表
     * @param projectId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeFormImageVo> getProjectTesteeUplaodThumbnailImages(String projectId, String visitId, String formId, String testeeId);

    List<UploadFileResultVo> saveUploadSystemFileResource(MultipartFile multipartFiles, String folder, String tenantId) throws IOException;

    ProjectTesteeFile getProjectTesteeFileByFileId(Long fileId);

    void updateProjectTesteeFileByFileId(ProjectTesteeFile projectTesteeFile);
    
    List<UploadFileResultVo> saveUploadCrfFile(MultipartFile[] multipartFiles, String projectId) throws IOException;
    
    List<UploadFileResultVo> uploadProjectFile(MultipartFile[] file, UploadProjectFileParam uploadProjectFileParam) throws IOException;
    
    CommonPage<ProjectTesteeFileVo> getProjectFileListForPage(String projectId, String resourceType, String searchValue, String sortField, String sortType, Integer pageNum, Integer pageSize);
    
    ProjectTesteeFile checkFileRecordRepeat(String projectId, String fileName, String fileNumber, String version, String createUserId);
    
    List<UploadFileResultVo> uploadSystemFile(MultipartFile file, String folderName) throws IOException;

    /**
     * 批量查询参与者的所有表单图片，用于导出优化
     *
     * @param projectId 项目ID
     * @param testeeId 参与者ID
     * @return 该参与者的所有表单图片
     */
    List<ProjectTesteeFormImageVo> getTesteeAllFormImages(String projectId, String testeeId);
}
