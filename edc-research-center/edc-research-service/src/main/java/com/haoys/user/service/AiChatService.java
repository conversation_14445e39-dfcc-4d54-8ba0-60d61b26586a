package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.dto.AiChatRequestDto;
import com.haoys.user.domain.entity.AiChatSession;
import com.haoys.user.domain.entity.AiModelConfig;
import com.haoys.user.domain.vo.AiChatResponseVo;
import com.haoys.user.domain.vo.AiChatSessionVo;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * AI聊天服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
public interface AiChatService {
    
    /**
     * 发送聊天消息(一次性响应)
     */
    CommonResult<AiChatResponseVo> sendMessage(AiChatRequestDto request);
    
    /**
     * 发送聊天消息(流式响应)
     */
    SseEmitter sendMessageStream(AiChatRequestDto request);
    
    /**
     * 上传文件并解析
     */
    CommonResult<AiChatResponseVo> uploadAndParseFile(String sessionId, MultipartFile file, String question);
    
    /**
     * 创建新会话
     */
    CommonResult<AiChatSession> createSession(String title, String modelType, String modelName);
    
    /**
     * 获取会话详情
     */
    CommonResult<AiChatSessionVo> getSession(String sessionId);
    
    /**
     * 获取用户会话列表
     */
    CommonResult<CommonPage<AiChatSessionVo>> getUserSessions(String userId, Integer pageNum, Integer pageSize);
    
    /**
     * 获取会话消息历史
     */
    CommonResult<List<AiChatResponseVo.MessageInfo>> getSessionMessages(String sessionId, Integer pageNum, Integer pageSize);
    
    /**
     * 结束会话
     */
    CommonResult<Void> endSession(String sessionId);
    
    /**
     * 删除会话
     */
    CommonResult<Void> deleteSession(String sessionId);
    
    /**
     * 获取可用模型列表
     */
    CommonResult<List<AiModelConfig>> getAvailableModels();
    
    /**
     * 获取用户Token使用统计
     */
    CommonResult<Map<String, Object>> getUserTokenUsage(String userId, String period);
    
    /**
     * 获取系统使用统计
     */
    CommonResult<Map<String, Object>> getSystemUsageStats(String period);
    
    /**
     * 清理超时会话
     */
    void cleanupTimeoutSessions();
}
