package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.core.wrapper.TesteeVisitFormDataWrapper;
import com.haoys.user.domain.param.TesteeExportHistoryParam;
import com.haoys.user.domain.param.export.ProjectSearchCollectParam;
import com.haoys.user.domain.template.TesteeImportResultVo;
import com.haoys.user.domain.param.ExportTesteeDataParam;
import com.haoys.user.domain.vo.export.ProjectSearchCollectVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVisitFormExceptionVo;
import com.haoys.user.domain.vo.testee.TesteeExportVo;

import java.util.List;

public interface TesteeExportService {

    /**
     * 导出参与者列表
     * @param param
     * @return
     */
    CommonPage<TesteeExportVo> getTesteeExportListForPage(TesteeExportHistoryParam param);

    /**
     * 导出参与者任务
     * @param param
     * @return
     */
    CustomResult saveTesteeExportTask(ExportTesteeDataParam param);
    /**
     * 导出参与者任务
     * @param taskName
     * @param projectId
     * @param orgId
     * @param visitId
     * @param formId
     * @param formDetailIds
     * @param formTableIds
     * @param operator
     * @return
     */
    CustomResult saveTesteeExportTask2(String taskName, String projectId, String orgId, String visitId, String formId, String formDetailIds, String formTableIds, String operator);
    /**
     * 下载导入参与者访视数据模板
     * @param projectId
     * @param userId
     * @return
     */
    CustomResult downloadTesteeImportTemplate(String projectId, String userId);


    /**
     * 批量导入参与者访视表单数据
     * @param dataList
     * @param projectId
     * @param userId
     * @param errorList
     * @return
     */
    String saveBatchProjectTesteeVisitFormData(List<TesteeVisitFormDataWrapper> dataList, String projectId, String userId, List<ProjectTesteeVisitFormExceptionVo> errorList);

    /**
     * 批量导入参与者访视表单数据 版本2
     * @param visitFormDataWrapperList
     * @param projectId
     * @param userId
     * @param errorList
     * @return
     */
    TesteeImportResultVo saveBatchProjectTesteeVisitFormDataVersion2(List<TesteeVisitFormDataWrapper> visitFormDataWrapperList, String projectId, String userId, List<ProjectTesteeVisitFormExceptionVo> errorList);

    /**
     * 批量导入参与者访视表单后更新访视进度 //TODO 待优化
     *
     * @param projectId
     * @param projectOrgId
     * @param operator
     * @return
     */
    CustomResult updateProjectTesteeOwnerFormsProcess(String projectId, String projectOrgId, String planId, String operator);

    /**
     * 删除导出记录
     * @param id
     * @return
     */
    CommonResult deleteProjectTesteeExportRecordById(String id);

    /**
     * 保存收藏条件
     * @param projectSearchCollectParam
     * @return
     */
    CustomResult saveProjectExportCollectRecord(ProjectSearchCollectParam projectSearchCollectParam);

    /**
     * 查询收藏参与者搜索条件分页列表
     * @param projectId
     * @param userId
     * @return
     */
    CommonPage<ProjectSearchCollectVo> getProjectTesteeExportCollectListForPage(String projectId, String userId, Integer pageNum, Integer pageSize);

    /**
     * 删除收藏参与者搜索条件
     * @param id
     * @return
     */
    CustomResult deleteProjectExportCollectRecord(String id);
}
