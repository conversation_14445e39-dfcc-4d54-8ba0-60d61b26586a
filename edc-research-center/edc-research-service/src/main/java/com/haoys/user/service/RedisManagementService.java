package com.haoys.user.service;

import com.haoys.user.domain.vo.RedisManagementVo;

import java.util.List;

/**
 * Redis数据管理服务接口
 * 提供Redis数据的查询、删除等管理功能
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
public interface RedisManagementService {

    /**
     * 验证配置秘钥
     * 
     * @param secretKey 配置秘钥
     * @return 验证结果
     */
    RedisManagementVo.SecretVerifyResponse verifySecret(String secretKey);

    /**
     * 通过code和refreshCode获取AccessToken
     * 
     * @param code 验证码
     * @param refreshCode 刷新码
     * @return AccessToken响应
     */
    RedisManagementVo.AccessTokenResponse getAccessToken(String code, String refreshCode);

    /**
     * 验证AccessToken
     * 
     * @param accessToken 访问令牌
     * @return 验证结果
     */
    RedisManagementVo.TokenValidateResponse validateToken(String accessToken);

    /**
     * 查询指定key的数据
     * 
     * @param key Redis键
     * @return 查询结果
     */
    RedisManagementVo.KeyQueryResponse queryKey(String key);

    /**
     * 查询模糊匹配的keys
     * 
     * @param pattern 匹配模式
     * @param limit 限制数量
     * @return 查询结果
     */
    RedisManagementVo.PatternQueryResponse queryPattern(String pattern, Integer limit);

    /**
     * 查询前缀匹配的keys
     * 
     * @param prefix 前缀
     * @param limit 限制数量
     * @return 查询结果
     */
    RedisManagementVo.PrefixQueryResponse queryPrefix(String prefix, Integer limit);

    /**
     * 删除指定key
     * 
     * @param key Redis键
     * @return 删除结果
     */
    RedisManagementVo.DeleteResponse deleteKey(String key);

    /**
     * 批量删除keys
     * 
     * @param keys Redis键列表
     * @return 删除结果
     */
    RedisManagementVo.BatchDeleteResponse deleteBatch(List<String> keys);

    /**
     * 删除模糊匹配的keys
     * 
     * @param pattern 匹配模式
     * @return 删除结果
     */
    RedisManagementVo.PatternDeleteResponse deletePattern(String pattern);

    /**
     * 删除前缀匹配的keys
     *
     * @param prefix 前缀
     * @return 删除结果
     */
    RedisManagementVo.PrefixDeleteResponse deletePrefix(String prefix);

    /**
     * 生成AuthCode
     *
     * @return AuthCode生成结果
     */
    RedisManagementVo.AuthCodeGenerateResponse generateAuthCode();

    /**
     * 通过AuthCode查询所有keys（分页）
     *
     * @param authCode 授权码
     * @param page 页码
     * @param size 每页大小
     * @return 查询结果
     */
    RedisManagementVo.AuthCodeQueryResponse queryAllKeysByAuthCode(String authCode, Integer page, Integer size);

    /**
     * 分页查询keys
     *
     * @param queryType 查询类型
     * @param queryCondition 查询条件
     * @param page 页码
     * @param size 每页大小
     * @return 查询结果
     */
    RedisManagementVo.PageQueryResponse queryKeysWithPagination(String queryType, String queryCondition, Integer page, Integer size);
}
