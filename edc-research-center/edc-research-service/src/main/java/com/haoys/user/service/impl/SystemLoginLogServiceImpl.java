package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.excel.ExcelUtils;
import com.haoys.user.common.excel.Template.SystemLoginLogExcel;
import com.haoys.user.common.excel.Template.SystemRequestLogExcel;
import com.haoys.user.domain.entity.SystemLoginLogQuery;
import com.haoys.user.domain.vo.system.SystemLoginLogVo;
import com.haoys.user.mapper.SystemLoginLogMapper;
import com.haoys.user.model.SystemLoginLog;
import com.haoys.user.service.SystemLoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


/**
 * 系统访问日志情况信息 服务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SystemLoginLogServiceImpl extends BaseService implements SystemLoginLogService {

    @Autowired
    private SystemLoginLogMapper systemLoginLogMapper;

    @Override
    public void insertSystemUserLoginLog(SystemLoginLog systemLoginLog)
    {
        systemLoginLogMapper.insert(systemLoginLog);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param systemLoginLogQuery 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public CommonPage<SystemLoginLogVo> selectLoginRecordList(SystemLoginLogQuery systemLoginLogQuery, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        return commonPageListWrapper(pageNum, pageSize, page, systemLoginLogMapper.selectList(systemLoginLogQuery));
    }

    /**
     * 批量删除系统登录日志
     * 
     * @param infoIds 需要删除的登录日志ID
     * @return
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return systemLoginLogMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor()
    {
        systemLoginLogMapper.cleanLogininfor();
    }

    @Override
    public void importSystemLoginLog(HttpServletResponse response, SystemLoginLogQuery systemLoginLogQuery) {
        List<SystemLoginLog> systemLoginLogList = systemLoginLogMapper.selectSystemLoginLogList(systemLoginLogQuery);
        List<SystemLoginLogExcel> list = new ArrayList<>();

        for(SystemLoginLog sl :systemLoginLogList){
            SystemLoginLogExcel systemLoginLogExcel = new SystemLoginLogExcel();
            BeanUtils.copyProperties(sl, systemLoginLogExcel);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateString = sdf.format(sl.getLoginTime());
            systemLoginLogExcel.setLoginTime(dateString);
            if("1".equals(sl.getOperateType())){
                systemLoginLogExcel.setOperateType("系统登录");
            }else if("2".equals(sl.getOperateType())){
                systemLoginLogExcel.setOperateType("超时或者更换登录设备");
            }else if("3".equals(sl.getOperateType())){
                systemLoginLogExcel.setOperateType("系统推出");
            }
            if("0".equals(sl.getStatus())){
                systemLoginLogExcel.setStatus("是");
            }else if("1".equals(sl.getStatus())){
                systemLoginLogExcel.setStatus("否");
            }
            list.add(systemLoginLogExcel);
        }

        try {
            ExcelUtils.export(response, "登录日志", list, SystemRequestLogExcel.class);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
