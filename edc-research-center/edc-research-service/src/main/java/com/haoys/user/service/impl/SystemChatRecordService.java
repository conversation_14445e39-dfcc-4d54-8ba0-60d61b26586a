package com.haoys.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.mapper.SystemChatRecordMapper;
import com.haoys.user.model.SystemChatRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class SystemChatRecordService {

    private final SystemChatRecordMapper systemChatRecordMapper;

    public String saveSystemChatRecord(String topic, String promptValue, String modelType, String conversationParam) {
        SystemChatRecord systemChatRecord = new SystemChatRecord();
        // 查询是否存在会话内容
        SystemChatRecord systemChatRecordQuery = systemChatRecordMapper.getSystemChatRecordByTopic(topic, SecurityUtils.getUserIdValue());
        if(systemChatRecordQuery == null){
            systemChatRecord.setId(SnowflakeIdWorker.getUuid());
            systemChatRecord.setTopic(topic);
            systemChatRecord.setContent(conversationParam);
            systemChatRecord.setPromptValue(promptValue);
            systemChatRecord.setModelType(modelType);
            systemChatRecord.setCreateUserId(SecurityUtils.getUserIdValue());
            systemChatRecord.setCreateTime(new Date());
            systemChatRecord.setStatus(BusinessConfig.VALID_STATUS);
            systemChatRecordMapper.insertSelective(systemChatRecord);
            return systemChatRecord.getId().toString();
        }else{
            systemChatRecordQuery.setContent(conversationParam);
            if(StringUtils.isNotEmpty(promptValue)){
                systemChatRecord.setPromptValue(promptValue);
            }
            systemChatRecordMapper.updateByPrimaryKeySelective(systemChatRecordQuery);
            return systemChatRecordQuery.getId().toString();
        }
    }

    public List<SystemChatRecord> getChatContentList(String topic, String modelType, String createUserId) {
        return systemChatRecordMapper.getChatContentList(topic, modelType, createUserId);
    }

    public SystemChatRecord getSystemChatContentByTopic(String topic, String createUserId) {
        SystemChatRecord systemChatRecord = systemChatRecordMapper.getSystemChatRecordByTopic(topic, createUserId);
        if(StringUtils.isNotBlank(systemChatRecord.getPromptValue())){
            Map<String, Object> dataMap = JSON.parseObject(systemChatRecord.getPromptValue(), new TypeReference<HashMap<String, Object>>(){}.getType());
            systemChatRecord.setPromptContent(dataMap);
        }
        Map<String, Object> dataMap = JSON.parseObject(systemChatRecord.getContent(), new TypeReference<HashMap<String, Object>>(){}.getType());
        systemChatRecord.setContentValue(dataMap);
        return systemChatRecord;
    }

    public void systemChatRecordService(String conversationIds) {
        String[] conversationIdArray = conversationIds.split(",");
        for (String conversationId : conversationIdArray) {
            SystemChatRecord systemChatRecord = systemChatRecordMapper.selectByPrimaryKey(Long.parseLong(conversationId));
            systemChatRecord.setStatus(BusinessConfig.NO_VALID_STATUS);
            systemChatRecordMapper.updateByPrimaryKey(systemChatRecord);
        }
    }
}
