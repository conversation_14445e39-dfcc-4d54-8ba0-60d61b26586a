package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ProjectAnalysisRecordVo;
import com.haoys.user.model.ProjectAnalysisRecord;
import com.haoys.user.model.ProjectAnalysisRecordExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface ProjectAnalysisRecordMapper {
    long countByExample(ProjectAnalysisRecordExample example);

    int deleteByExample(ProjectAnalysisRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectAnalysisRecord record);

    int insertSelective(ProjectAnalysisRecord record);

    List<ProjectAnalysisRecord> selectByExample(ProjectAnalysisRecordExample example);

    ProjectAnalysisRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectAnalysisRecord record, @Param("example") ProjectAnalysisRecordExample example);

    int updateByExample(@Param("record") ProjectAnalysisRecord record, @Param("example") ProjectAnalysisRecordExample example);

    int updateByPrimaryKeySelective(ProjectAnalysisRecord record);

    int updateByPrimaryKey(ProjectAnalysisRecord record);

    List<ProjectAnalysisRecordVo> getProjectAnalysisRecordForPage(String batchCode);

    List<Map<Integer,Object>> queryAgeLineAnalysis(String batchCode);
}