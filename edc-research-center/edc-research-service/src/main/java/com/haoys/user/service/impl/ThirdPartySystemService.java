package com.haoys.user.service.impl;


import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.ocr.TencentAIService;
import com.haoys.user.common.util.HttpUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.BdpUserInfoConfig;
import com.haoys.user.config.NCBIApiConfig;
import com.haoys.user.config.ReportApiConfig;
import com.haoys.user.domain.ncbi.NCBIRequestResult;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ThirdPartySystemService {
    
    @Autowired
    private ReportApiConfig reportApiConfig;
    @Autowired
    private BdpUserInfoConfig bdpUserInfoConfig;
    @Autowired
    private NCBIApiConfig ncbiApiConfig;
    @Autowired
    private TencentAIService tencentAIService;
    
    public CustomResult getAjreportLoginToken() {
        CustomResult customResult = new CustomResult();
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("loginName", "admin");
        dataMap.put("password", "d51e052f621c0e6e6a8a59ff2e0dd7b5");
        dataMap.put("verifyCode", "");
        String params = JSON.toJSONString(dataMap);
        String result = HttpUtil.post(reportApiConfig.getAj_report_url(), params);
        Map<String, String> map = JSON.parseObject(result, new TypeReference<HashMap<String, String>>(){}.getType());
        Object data = map.get("data");
        Map<String, String> tokenMap = JSON.parseObject(data.toString(), new TypeReference<HashMap<String, String>>(){}.getType());
        String token = tokenMap.get("token");
        customResult.setData(token);
        return customResult;
    }
    
    public CustomResult getUserLoginInfo(String token) {
        CustomResult customResult = new CustomResult();
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("token", token);
        String params = JSON.toJSONString(dataMap);
        String result = HttpUtil.post(reportApiConfig.getAj_report_login_user_url(), params);
        customResult.setData(result);
        return customResult;
    }
    
    public CustomResult getNCBISearchResultList(String searchValue, String year, String reldate, String mindate, String maxdate) {
        CustomResult customResult = new CustomResult();
        // negative breast cancer
        StringBuilder stringBuilder = new StringBuilder();
        if(StringUtils.isNotEmpty(searchValue)) {
            String[] searchValueArray = searchValue.split(" ");
            for (int i = 0; i < searchValueArray.length; i++) {
                if(i == 0){
                    stringBuilder.append("+AND").append("+").append(searchValueArray[i]);
                }else {
                    stringBuilder.append("+").append(searchValueArray[i]);
                }
            }
        }
        if(StringUtils.isNotEmpty(year)) {
            stringBuilder.append("+AND+").append(year).append("[pdat]");
        }
        if(StringUtils.isNotEmpty(reldate)) {
            stringBuilder.append("=").append(reldate);
        }
        if(StringUtils.isNotEmpty(mindate)) {
            stringBuilder.append("").append(mindate);
        }
        if(StringUtils.isNotEmpty(maxdate)) {
            stringBuilder.append("=").append(maxdate);
        }
        // url = 'https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=science[journal]+AND+breast+cancer+AND+2008[pdat]'
        String ncbiBaseUrl = ncbiApiConfig.getNcbi_base_url()+stringBuilder;
        String body = HttpUtils.sendGet(ncbiBaseUrl, null,"utf-8");
        body = body.replace("<!DOCTYPE eSearchResult PUBLIC \"-//NLM//DTD esearch 20060628//EN\" \"https://eutils.ncbi.nlm.nih.gov/eutils/dtd/20060628/esearch.dtd\">", "");
        log.info("body: {}", XmlUtil.format(body));
        org.w3c.dom.Document document = XmlUtil.parseXml(body);
        NCBIRequestResult ncbiRequestResult = XmlUtil.xmlToBean(document, NCBIRequestResult.class);
        customResult.setData(ncbiRequestResult);
        return customResult;
    }
    
    public CustomResult getBCBISearchSummaryResult(String id) {
        CustomResult customResult = new CustomResult();
        String ncbiBaseUrlSearch = ncbiApiConfig.getNcbi_base_url_search();
        // 19008416,18927361,18787170,18487186,18239126,18239125
        ncbiBaseUrlSearch = ncbiBaseUrlSearch.concat(id);
        String body = HttpUtils.sendGet(ncbiBaseUrlSearch, null,"utf-8");
        body = body.replace("<!DOCTYPE eSummaryResult PUBLIC \"-//NLM//DTD esummary v1 20041029//EN\" \"https://eutils.ncbi.nlm.nih.gov/eutils/dtd/20041029/esummary-v1.dtd\">", "");
        List<LinkedHashMap<String, Object>> xmlBody = parseXml(body);
        customResult.setData(xmlBody);
        return customResult;
    }
    
    public List<LinkedHashMap<String, Object>> parseXml(String xml) {
        List<LinkedHashMap<String, Object>> dataMapList = new ArrayList<>();
        try {
            Document document = DocumentHelper.parseText(xml);
            Element rootElement = document.getRootElement();
            //遍历根节点的子节点,用Element对象的elementIterator获取子节点的迭代器
            Iterator<Element> iterator = rootElement.elementIterator();
            //遍历迭代器，获取根节点的信息
            while(iterator.hasNext()) {
                LinkedHashMap<String,Object> dataMap = new LinkedHashMap();
                log.info("------开始遍历xml---------");
                Element book = iterator.next();
                List<Attribute> attributeList = book.attributes();
                //遍历book节点子节点，elementIterator得到迭代器
                Iterator it2 = book.elementIterator();
                while(it2.hasNext()) {
                    Element bookChild = (Element)it2.next();
                    //log.info("bookChild节点名 name= " + bookChild.getName() + "--bookChild节点值：" + bookChild.getStringValue());
                    if("Id".equals(bookChild.getName())){
                        dataMap.put(bookChild.getName(),bookChild.getStringValue());
                    }
                    List<String> elementNames = new ArrayList<>();
                    // xml 子节点的属性
                    List attr = bookChild.attributes();
                    for (int i = 0; i <attr.size() ; i++) {
                        Attribute attribute = (Attribute) attr.get(i);
                        Iterator elementIterator = bookChild.elementIterator();
                        while(elementIterator.hasNext()) {
                            Element element = (Element)elementIterator.next();
                            //String translateText = tencentAIService.translateText(element.getStringValue());
                            elementNames.add(element.getText());
                            //log.info("element节点名 name= " + element.getName() + "--节点值：" + element.getStringValue());
                        }
                        String bookChildValue = bookChild.getStringValue();
                        Attribute attribute1 = bookChild.attribute("Type");
                        if(attribute1 != null){
                            String value = attribute1.getValue();
                            if("List".equals(value)){
                                bookChildValue = elementNames.stream().collect(Collectors.joining(",")).trim();
                            }
                        }
                        dataMap.put(attribute.getValue(),bookChildValue);
                        //log.info("节点名 name= " + name + "--节点值：" + bookChild.getStringValue());
                    }
                }
                log.info("------结束遍历xml子节点---------");
                dataMapList.add(dataMap);
            }
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return dataMapList;
    }
    
    public CustomResult getBCBISearchSummaryDownloadUrl(String eLocationID) {
        CustomResult customResult = new CustomResult();
        String ncbiBaseUrlDownload = ncbiApiConfig.getNcbi_summary_url();
        ncbiBaseUrlDownload = ncbiBaseUrlDownload.concat(eLocationID);
        customResult.setData(ncbiBaseUrlDownload);
        return customResult;
    }
    
    public CustomResult getBCBISearchSuggestions(String searchValue) {
        CustomResult customResult = new CustomResult();
        String ncbiSearchSuggestionsUrl = ncbiApiConfig.getNcbi_search_suggestions_url();
        ncbiSearchSuggestionsUrl = ncbiSearchSuggestionsUrl.concat(searchValue);
        String body = HttpUtils.sendGet(ncbiSearchSuggestionsUrl, null,"utf-8");
        HashMap<String, Object> bodyMap = JSON.parseObject(body, new TypeReference<HashMap<String, Object>>(){}.getType());
        List<String> suggestions = (List<String>) bodyMap.get("suggestions");
        customResult.setData(suggestions);
        return customResult;
    }
    
    public CustomResult getUserInfoByUserId(String ticket) {
        CustomResult customResult = new CustomResult();
        Map<String,Object> dataMap = new HashMap<>();
        /*
        loginId	是	要查询的账号 id
        timestamp	是	当前时间戳，13位
        nonce	是	随机字符串（不可重复使用）
        sign	是	签名，生成算法：md5( loginId={账号id}&nonce={随机字符串}&timestamp={13位时间戳}&key={secretkey秘钥} )
        */
        // RandomStringUtils.randomAlphanumeric(6)
        long currentTimeMillisValue = System.currentTimeMillis();
        String timestamp = Long.toString(currentTimeMillisValue);
        //dataMap.put("loginId", userId);
        //dataMap.put("timestamp", timestamp);
        //dataMap.put("nonce", MD5.create().md5(userId + "kQwIOrYvnXmSDkwEiFngrKidMcdrgKor" + timestamp));
        //dataMap.put("sign", "kQwIOrYvnXmSDkwEiFngrKidMcdrgKor");
        //String body = HttpUtil.get(bdpUserInfoConfig.getBdp_get_user_url(), dataMap);
        
        dataMap.put("ticket", ticket);
        dataMap.put("siteId", "103");
        String data = HttpUtil.post(bdpUserInfoConfig.getBdp_check_ticket_url(), dataMap);
        log.info("data:{}", data);
        
        HashMap<String, Object> dataMapValue = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>(){}.getType());
        Object loginId = dataMapValue.get("data");
        String body = HttpUtil.get(bdpUserInfoConfig.getBdp_get_user_url().concat("/").concat(loginId.toString()));
        log.info("getUserInfoByUserId url:{}, paramMap:{}, body:{}" , bdpUserInfoConfig.getBdp_get_user_url().concat("/" + loginId), dataMap, body);
        HashMap<String, Object> bodyMap = JSON.parseObject(body, new TypeReference<HashMap<String, Object>>(){}.getType());
        customResult.setData(bodyMap);
        return customResult;
    }
}
