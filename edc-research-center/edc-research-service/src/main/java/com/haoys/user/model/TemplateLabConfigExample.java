package com.haoys.user.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TemplateLabConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TemplateLabConfigExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNull() {
            addCriterion("form_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNotNull() {
            addCriterion("form_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdEqualTo(Long value) {
            addCriterion("form_detail_id =", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotEqualTo(Long value) {
            addCriterion("form_detail_id <>", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThan(Long value) {
            addCriterion("form_detail_id >", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_detail_id >=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThan(Long value) {
            addCriterion("form_detail_id <", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("form_detail_id <=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIn(List<Long> values) {
            addCriterion("form_detail_id in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotIn(List<Long> values) {
            addCriterion("form_detail_id not in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdBetween(Long value1, Long value2) {
            addCriterion("form_detail_id between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("form_detail_id not between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNull() {
            addCriterion("form_table_id is null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNotNull() {
            addCriterion("form_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdEqualTo(Long value) {
            addCriterion("form_table_id =", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotEqualTo(Long value) {
            addCriterion("form_table_id <>", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThan(Long value) {
            addCriterion("form_table_id >", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_table_id >=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThan(Long value) {
            addCriterion("form_table_id <", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThanOrEqualTo(Long value) {
            addCriterion("form_table_id <=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIn(List<Long> values) {
            addCriterion("form_table_id in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotIn(List<Long> values) {
            addCriterion("form_table_id not in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdBetween(Long value1, Long value2) {
            addCriterion("form_table_id between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotBetween(Long value1, Long value2) {
            addCriterion("form_table_id not between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andVariableNameIsNull() {
            addCriterion("variable_name is null");
            return (Criteria) this;
        }

        public Criteria andVariableNameIsNotNull() {
            addCriterion("variable_name is not null");
            return (Criteria) this;
        }

        public Criteria andVariableNameEqualTo(String value) {
            addCriterion("variable_name =", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotEqualTo(String value) {
            addCriterion("variable_name <>", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameGreaterThan(String value) {
            addCriterion("variable_name >", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameGreaterThanOrEqualTo(String value) {
            addCriterion("variable_name >=", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLessThan(String value) {
            addCriterion("variable_name <", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLessThanOrEqualTo(String value) {
            addCriterion("variable_name <=", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLike(String value) {
            addCriterion("variable_name like", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotLike(String value) {
            addCriterion("variable_name not like", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameIn(List<String> values) {
            addCriterion("variable_name in", values, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotIn(List<String> values) {
            addCriterion("variable_name not in", values, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameBetween(String value1, String value2) {
            addCriterion("variable_name between", value1, value2, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotBetween(String value1, String value2) {
            addCriterion("variable_name not between", value1, value2, "variableName");
            return (Criteria) this;
        }

        public Criteria andScopeIsNull() {
            addCriterion("scope is null");
            return (Criteria) this;
        }

        public Criteria andScopeIsNotNull() {
            addCriterion("scope is not null");
            return (Criteria) this;
        }

        public Criteria andScopeEqualTo(String value) {
            addCriterion("scope =", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotEqualTo(String value) {
            addCriterion("scope <>", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeGreaterThan(String value) {
            addCriterion("scope >", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeGreaterThanOrEqualTo(String value) {
            addCriterion("scope >=", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLessThan(String value) {
            addCriterion("scope <", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLessThanOrEqualTo(String value) {
            addCriterion("scope <=", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeLike(String value) {
            addCriterion("scope like", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotLike(String value) {
            addCriterion("scope not like", value, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeIn(List<String> values) {
            addCriterion("scope in", values, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotIn(List<String> values) {
            addCriterion("scope not in", values, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeBetween(String value1, String value2) {
            addCriterion("scope between", value1, value2, "scope");
            return (Criteria) this;
        }

        public Criteria andScopeNotBetween(String value1, String value2) {
            addCriterion("scope not between", value1, value2, "scope");
            return (Criteria) this;
        }

        public Criteria andLabTypeIsNull() {
            addCriterion("lab_type is null");
            return (Criteria) this;
        }

        public Criteria andLabTypeIsNotNull() {
            addCriterion("lab_type is not null");
            return (Criteria) this;
        }

        public Criteria andLabTypeEqualTo(String value) {
            addCriterion("lab_type =", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeNotEqualTo(String value) {
            addCriterion("lab_type <>", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeGreaterThan(String value) {
            addCriterion("lab_type >", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeGreaterThanOrEqualTo(String value) {
            addCriterion("lab_type >=", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeLessThan(String value) {
            addCriterion("lab_type <", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeLessThanOrEqualTo(String value) {
            addCriterion("lab_type <=", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeLike(String value) {
            addCriterion("lab_type like", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeNotLike(String value) {
            addCriterion("lab_type not like", value, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeIn(List<String> values) {
            addCriterion("lab_type in", values, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeNotIn(List<String> values) {
            addCriterion("lab_type not in", values, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeBetween(String value1, String value2) {
            addCriterion("lab_type between", value1, value2, "labType");
            return (Criteria) this;
        }

        public Criteria andLabTypeNotBetween(String value1, String value2) {
            addCriterion("lab_type not between", value1, value2, "labType");
            return (Criteria) this;
        }

        public Criteria andGenderValueIsNull() {
            addCriterion("gender_value is null");
            return (Criteria) this;
        }

        public Criteria andGenderValueIsNotNull() {
            addCriterion("gender_value is not null");
            return (Criteria) this;
        }

        public Criteria andGenderValueEqualTo(String value) {
            addCriterion("gender_value =", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueNotEqualTo(String value) {
            addCriterion("gender_value <>", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueGreaterThan(String value) {
            addCriterion("gender_value >", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueGreaterThanOrEqualTo(String value) {
            addCriterion("gender_value >=", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueLessThan(String value) {
            addCriterion("gender_value <", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueLessThanOrEqualTo(String value) {
            addCriterion("gender_value <=", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueLike(String value) {
            addCriterion("gender_value like", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueNotLike(String value) {
            addCriterion("gender_value not like", value, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueIn(List<String> values) {
            addCriterion("gender_value in", values, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueNotIn(List<String> values) {
            addCriterion("gender_value not in", values, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueBetween(String value1, String value2) {
            addCriterion("gender_value between", value1, value2, "genderValue");
            return (Criteria) this;
        }

        public Criteria andGenderValueNotBetween(String value1, String value2) {
            addCriterion("gender_value not between", value1, value2, "genderValue");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsIsNull() {
            addCriterion("if_upper_contains is null");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsIsNotNull() {
            addCriterion("if_upper_contains is not null");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsEqualTo(Boolean value) {
            addCriterion("if_upper_contains =", value, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsNotEqualTo(Boolean value) {
            addCriterion("if_upper_contains <>", value, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsGreaterThan(Boolean value) {
            addCriterion("if_upper_contains >", value, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("if_upper_contains >=", value, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsLessThan(Boolean value) {
            addCriterion("if_upper_contains <", value, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsLessThanOrEqualTo(Boolean value) {
            addCriterion("if_upper_contains <=", value, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsIn(List<Boolean> values) {
            addCriterion("if_upper_contains in", values, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsNotIn(List<Boolean> values) {
            addCriterion("if_upper_contains not in", values, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsBetween(Boolean value1, Boolean value2) {
            addCriterion("if_upper_contains between", value1, value2, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andIfUpperContainsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("if_upper_contains not between", value1, value2, "ifUpperContains");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueIsNull() {
            addCriterion("upper_limit_value is null");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueIsNotNull() {
            addCriterion("upper_limit_value is not null");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueEqualTo(BigDecimal value) {
            addCriterion("upper_limit_value =", value, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueNotEqualTo(BigDecimal value) {
            addCriterion("upper_limit_value <>", value, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueGreaterThan(BigDecimal value) {
            addCriterion("upper_limit_value >", value, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("upper_limit_value >=", value, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueLessThan(BigDecimal value) {
            addCriterion("upper_limit_value <", value, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("upper_limit_value <=", value, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueIn(List<BigDecimal> values) {
            addCriterion("upper_limit_value in", values, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueNotIn(List<BigDecimal> values) {
            addCriterion("upper_limit_value not in", values, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("upper_limit_value between", value1, value2, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andUpperLimitValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("upper_limit_value not between", value1, value2, "upperLimitValue");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsIsNull() {
            addCriterion("if_lower_contains is null");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsIsNotNull() {
            addCriterion("if_lower_contains is not null");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsEqualTo(Boolean value) {
            addCriterion("if_lower_contains =", value, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsNotEqualTo(Boolean value) {
            addCriterion("if_lower_contains <>", value, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsGreaterThan(Boolean value) {
            addCriterion("if_lower_contains >", value, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("if_lower_contains >=", value, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsLessThan(Boolean value) {
            addCriterion("if_lower_contains <", value, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsLessThanOrEqualTo(Boolean value) {
            addCriterion("if_lower_contains <=", value, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsIn(List<Boolean> values) {
            addCriterion("if_lower_contains in", values, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsNotIn(List<Boolean> values) {
            addCriterion("if_lower_contains not in", values, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsBetween(Boolean value1, Boolean value2) {
            addCriterion("if_lower_contains between", value1, value2, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andIfLowerContainsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("if_lower_contains not between", value1, value2, "ifLowerContains");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueIsNull() {
            addCriterion("lower_limit_value is null");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueIsNotNull() {
            addCriterion("lower_limit_value is not null");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueEqualTo(BigDecimal value) {
            addCriterion("lower_limit_value =", value, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueNotEqualTo(BigDecimal value) {
            addCriterion("lower_limit_value <>", value, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueGreaterThan(BigDecimal value) {
            addCriterion("lower_limit_value >", value, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("lower_limit_value >=", value, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueLessThan(BigDecimal value) {
            addCriterion("lower_limit_value <", value, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueLessThanOrEqualTo(BigDecimal value) {
            addCriterion("lower_limit_value <=", value, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueIn(List<BigDecimal> values) {
            addCriterion("lower_limit_value in", values, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueNotIn(List<BigDecimal> values) {
            addCriterion("lower_limit_value not in", values, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lower_limit_value between", value1, value2, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andLowerLimitValueNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("lower_limit_value not between", value1, value2, "lowerLimitValue");
            return (Criteria) this;
        }

        public Criteria andDicResourceIsNull() {
            addCriterion("dic_resource is null");
            return (Criteria) this;
        }

        public Criteria andDicResourceIsNotNull() {
            addCriterion("dic_resource is not null");
            return (Criteria) this;
        }

        public Criteria andDicResourceEqualTo(String value) {
            addCriterion("dic_resource =", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotEqualTo(String value) {
            addCriterion("dic_resource <>", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceGreaterThan(String value) {
            addCriterion("dic_resource >", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceGreaterThanOrEqualTo(String value) {
            addCriterion("dic_resource >=", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLessThan(String value) {
            addCriterion("dic_resource <", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLessThanOrEqualTo(String value) {
            addCriterion("dic_resource <=", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceLike(String value) {
            addCriterion("dic_resource like", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotLike(String value) {
            addCriterion("dic_resource not like", value, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceIn(List<String> values) {
            addCriterion("dic_resource in", values, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotIn(List<String> values) {
            addCriterion("dic_resource not in", values, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceBetween(String value1, String value2) {
            addCriterion("dic_resource between", value1, value2, "dicResource");
            return (Criteria) this;
        }

        public Criteria andDicResourceNotBetween(String value1, String value2) {
            addCriterion("dic_resource not between", value1, value2, "dicResource");
            return (Criteria) this;
        }

        public Criteria andRefDicIdIsNull() {
            addCriterion("ref_dic_id is null");
            return (Criteria) this;
        }

        public Criteria andRefDicIdIsNotNull() {
            addCriterion("ref_dic_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefDicIdEqualTo(Long value) {
            addCriterion("ref_dic_id =", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotEqualTo(Long value) {
            addCriterion("ref_dic_id <>", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdGreaterThan(Long value) {
            addCriterion("ref_dic_id >", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ref_dic_id >=", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdLessThan(Long value) {
            addCriterion("ref_dic_id <", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdLessThanOrEqualTo(Long value) {
            addCriterion("ref_dic_id <=", value, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdIn(List<Long> values) {
            addCriterion("ref_dic_id in", values, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotIn(List<Long> values) {
            addCriterion("ref_dic_id not in", values, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdBetween(Long value1, Long value2) {
            addCriterion("ref_dic_id between", value1, value2, "refDicId");
            return (Criteria) this;
        }

        public Criteria andRefDicIdNotBetween(Long value1, Long value2) {
            addCriterion("ref_dic_id not between", value1, value2, "refDicId");
            return (Criteria) this;
        }

        public Criteria andNormalTextIsNull() {
            addCriterion("normal_text is null");
            return (Criteria) this;
        }

        public Criteria andNormalTextIsNotNull() {
            addCriterion("normal_text is not null");
            return (Criteria) this;
        }

        public Criteria andNormalTextEqualTo(String value) {
            addCriterion("normal_text =", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextNotEqualTo(String value) {
            addCriterion("normal_text <>", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextGreaterThan(String value) {
            addCriterion("normal_text >", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextGreaterThanOrEqualTo(String value) {
            addCriterion("normal_text >=", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextLessThan(String value) {
            addCriterion("normal_text <", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextLessThanOrEqualTo(String value) {
            addCriterion("normal_text <=", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextLike(String value) {
            addCriterion("normal_text like", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextNotLike(String value) {
            addCriterion("normal_text not like", value, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextIn(List<String> values) {
            addCriterion("normal_text in", values, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextNotIn(List<String> values) {
            addCriterion("normal_text not in", values, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextBetween(String value1, String value2) {
            addCriterion("normal_text between", value1, value2, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalTextNotBetween(String value1, String value2) {
            addCriterion("normal_text not between", value1, value2, "normalText");
            return (Criteria) this;
        }

        public Criteria andNormalValueIsNull() {
            addCriterion("normal_value is null");
            return (Criteria) this;
        }

        public Criteria andNormalValueIsNotNull() {
            addCriterion("normal_value is not null");
            return (Criteria) this;
        }

        public Criteria andNormalValueEqualTo(String value) {
            addCriterion("normal_value =", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueNotEqualTo(String value) {
            addCriterion("normal_value <>", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueGreaterThan(String value) {
            addCriterion("normal_value >", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueGreaterThanOrEqualTo(String value) {
            addCriterion("normal_value >=", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueLessThan(String value) {
            addCriterion("normal_value <", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueLessThanOrEqualTo(String value) {
            addCriterion("normal_value <=", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueLike(String value) {
            addCriterion("normal_value like", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueNotLike(String value) {
            addCriterion("normal_value not like", value, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueIn(List<String> values) {
            addCriterion("normal_value in", values, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueNotIn(List<String> values) {
            addCriterion("normal_value not in", values, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueBetween(String value1, String value2) {
            addCriterion("normal_value between", value1, value2, "normalValue");
            return (Criteria) this;
        }

        public Criteria andNormalValueNotBetween(String value1, String value2) {
            addCriterion("normal_value not between", value1, value2, "normalValue");
            return (Criteria) this;
        }

        public Criteria andLabResourceIsNull() {
            addCriterion("lab_resource is null");
            return (Criteria) this;
        }

        public Criteria andLabResourceIsNotNull() {
            addCriterion("lab_resource is not null");
            return (Criteria) this;
        }

        public Criteria andLabResourceEqualTo(Long value) {
            addCriterion("lab_resource =", value, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceNotEqualTo(Long value) {
            addCriterion("lab_resource <>", value, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceGreaterThan(Long value) {
            addCriterion("lab_resource >", value, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceGreaterThanOrEqualTo(Long value) {
            addCriterion("lab_resource >=", value, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceLessThan(Long value) {
            addCriterion("lab_resource <", value, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceLessThanOrEqualTo(Long value) {
            addCriterion("lab_resource <=", value, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceIn(List<Long> values) {
            addCriterion("lab_resource in", values, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceNotIn(List<Long> values) {
            addCriterion("lab_resource not in", values, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceBetween(Long value1, Long value2) {
            addCriterion("lab_resource between", value1, value2, "labResource");
            return (Criteria) this;
        }

        public Criteria andLabResourceNotBetween(Long value1, Long value2) {
            addCriterion("lab_resource not between", value1, value2, "labResource");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgIsNull() {
            addCriterion("apply_total_org is null");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgIsNotNull() {
            addCriterion("apply_total_org is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgEqualTo(Boolean value) {
            addCriterion("apply_total_org =", value, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgNotEqualTo(Boolean value) {
            addCriterion("apply_total_org <>", value, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgGreaterThan(Boolean value) {
            addCriterion("apply_total_org >", value, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgGreaterThanOrEqualTo(Boolean value) {
            addCriterion("apply_total_org >=", value, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgLessThan(Boolean value) {
            addCriterion("apply_total_org <", value, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgLessThanOrEqualTo(Boolean value) {
            addCriterion("apply_total_org <=", value, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgIn(List<Boolean> values) {
            addCriterion("apply_total_org in", values, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgNotIn(List<Boolean> values) {
            addCriterion("apply_total_org not in", values, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgBetween(Boolean value1, Boolean value2) {
            addCriterion("apply_total_org between", value1, value2, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andApplyTotalOrgNotBetween(Boolean value1, Boolean value2) {
            addCriterion("apply_total_org not between", value1, value2, "applyTotalOrg");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNull() {
            addCriterion("enabled is null");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNotNull() {
            addCriterion("enabled is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledEqualTo(Boolean value) {
            addCriterion("enabled =", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotEqualTo(Boolean value) {
            addCriterion("enabled <>", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThan(Boolean value) {
            addCriterion("enabled >", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enabled >=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThan(Boolean value) {
            addCriterion("enabled <", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("enabled <=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledIn(List<Boolean> values) {
            addCriterion("enabled in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotIn(List<Boolean> values) {
            addCriterion("enabled not in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("enabled between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enabled not between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsIsNull() {
            addCriterion("selected_options is null");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsIsNotNull() {
            addCriterion("selected_options is not null");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsEqualTo(String value) {
            addCriterion("selected_options =", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsNotEqualTo(String value) {
            addCriterion("selected_options <>", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsGreaterThan(String value) {
            addCriterion("selected_options >", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsGreaterThanOrEqualTo(String value) {
            addCriterion("selected_options >=", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsLessThan(String value) {
            addCriterion("selected_options <", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsLessThanOrEqualTo(String value) {
            addCriterion("selected_options <=", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsLike(String value) {
            addCriterion("selected_options like", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsNotLike(String value) {
            addCriterion("selected_options not like", value, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsIn(List<String> values) {
            addCriterion("selected_options in", values, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsNotIn(List<String> values) {
            addCriterion("selected_options not in", values, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsBetween(String value1, String value2) {
            addCriterion("selected_options between", value1, value2, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andSelectedOptionsNotBetween(String value1, String value2) {
            addCriterion("selected_options not between", value1, value2, "selectedOptions");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(String value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(String value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(String value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(String value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLike(String value) {
            addCriterion("update_user_id like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotLike(String value) {
            addCriterion("update_user_id not like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<String> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<String> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(String value1, String value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}