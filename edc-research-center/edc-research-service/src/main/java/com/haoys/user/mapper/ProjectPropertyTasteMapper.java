package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPropertyTaste;
import com.haoys.user.model.ProjectPropertyTasteExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPropertyTasteMapper {
    long countByExample(ProjectPropertyTasteExample example);

    int deleteByExample(ProjectPropertyTasteExample example);

    int insert(ProjectPropertyTaste record);

    int insertSelective(ProjectPropertyTaste record);

    List<ProjectPropertyTaste> selectByExampleWithBLOBs(ProjectPropertyTasteExample example);

    List<ProjectPropertyTaste> selectByExample(ProjectPropertyTasteExample example);

    int updateByExampleSelective(@Param("record") ProjectPropertyTaste record, @Param("example") ProjectPropertyTasteExample example);

    int updateByExampleWithBLOBs(@Param("record") ProjectPropertyTaste record, @Param("example") ProjectPropertyTasteExample example);

    int updateByExample(@Param("record") ProjectPropertyTaste record, @Param("example") ProjectPropertyTasteExample example);
}