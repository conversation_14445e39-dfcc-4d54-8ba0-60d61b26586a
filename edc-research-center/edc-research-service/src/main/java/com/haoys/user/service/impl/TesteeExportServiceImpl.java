package com.haoys.user.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.core.wrapper.FormVariableExcelWrapper;
import com.haoys.user.common.core.wrapper.TesteeVisitFormDataWrapper;
import com.haoys.user.enums.FormVariableComplateStatus;
import com.haoys.user.common.excel.DictExcelHandler;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.common.util.Validation;
import com.haoys.user.domain.param.ExportTesteeDataParam;
import com.haoys.user.domain.param.TesteeExportHistoryParam;
import com.haoys.user.domain.param.export.ProjectSearchCollectParam;
import com.haoys.user.domain.param.project.ProjectTesteeResultParam;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.template.FormVarOptionVo;
import com.haoys.user.domain.template.TesteeImportResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailTableVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableRowDataVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormTableExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableExportVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.export.ProjectSearchCollectVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableCountVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormAndTableResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeTableResultExportVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVisitFormExceptionVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.domain.vo.testee.TesteeExportVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.mapper.ProjectTesteeExportMapper;
import com.haoys.user.mapper.ProjectTesteeInfoMapper;
import com.haoys.user.model.Organization;
import com.haoys.user.model.ProjectTesteeExport;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TesteeExportService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TesteeExportServiceImpl extends BaseService implements TesteeExportService {

    @Autowired
    private OssStorageConfig storageConfig;
    @Autowired
    private ProjectTesteeInfoMapper projectTesteeInfoMapper;
    @Autowired
    private ProjectTesteeExportMapper projectTesteeExportMapper;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ProjectVisitConfigService projectVisitConfigService;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private ProjectTesteeResultService projectTesteeResultService;
    @Autowired
    private ProjectTesteeTableService projectTesteeTableService;


    @Override
    public CommonPage<TesteeExportVo> getTesteeExportListForPage(TesteeExportHistoryParam param) {
        List<TesteeExportVo> dataList = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        CommonPage<TesteeExportVo> commonPage = new CommonPage<>();
        if (param.getExportType()==null){
            param.setExportType(1);
        }
        // 处理多个所属中心的搜索条件
        String orgIds = param.getOrgIds();
        if (StringUtils.isNotEmpty(orgIds)){
            String[] orgIdArr = orgIds.split(",");
            String orgSearch = "" ;
            for (int i = 0; i <orgIdArr.length; i++) {
                if (i==orgIdArr.length-1){
                    orgSearch="instr(org_id,"+orgIdArr[i]+")>0 ";
                }else {
                    orgSearch="instr(org_id,"+orgIdArr[i]+")>0 or";
                }
            }
            param.setOrgIds(orgSearch);
        }

        List<ProjectTesteeExport> projectTesteeExports = projectTesteeExportMapper.selectList(param);
        commonPage.setTotal(page.getTotal());
        commonPage.setTotalPage(page.getPages());
        commonPage.setPageNum(param.getPageNum());
        commonPage.setPageSize(param.getPageSize());
        for (ProjectTesteeExport projectTesteeExport : projectTesteeExports) {
            TesteeExportVo testeeExportVo = new TesteeExportVo();
            BeanUtils.copyProperties(projectTesteeExport, testeeExportVo);
            SystemUserInfoExtendVo userInfo = systemUserInfoService.getSystemUserInfoByUserId(projectTesteeExport.getOperator().toString());
            if (userInfo != null) {
                testeeExportVo.setOperatorName(userInfo.getRealName());
            }
            dataList.add(testeeExportVo);
        }
        commonPage.setList(dataList);
        return commonPage;
    }

    /**
     * 新建导出参与者任务
     * @param taskName
     * @param projectId
     * @param orgId
     * @param visitId
     * @param formId
     * @param formDetailIds
     * @param formTableIds
     * @param operator
     * @return
     */
    @Override
    public CustomResult saveTesteeExportTask2(String taskName, String projectId, String orgId, String visitId, String formId, String formDetailIds, String formTableIds, String operator) {
        //按照访视展示每个参与者的表单明细
        CustomResult customResult = new CustomResult();
        ProjectTesteeExport projectTesteeExport = new ProjectTesteeExport();
        String targetPath = storageConfig.getUploadFolder() + taskName + Constants.EXPORT_FILE_SUFFIX;
        OutputStream outputSream = null;
        try {
            if (!new File(storageConfig.getUploadFolder()).exists()) {
                new File(storageConfig.getUploadFolder()).mkdirs();
            }
            File file = new File(targetPath);
            outputSream = new FileOutputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        List<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeAnalysisListByProjectId(projectId, "", orgId);
        //查询项目访视列表
        List<ProjectVisitConfig> visitList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
        for (ProjectVisitConfig projectVisitConfig : visitList) {
            Map<String, Object> dataMap = new HashMap<>();
            List<ExcelExportEntity> entityList = new LinkedList<>();
            List<Map<String, Object>> dataList = new ArrayList<>();

            ExcelExportEntity excelExportEntity1 = new ExcelExportEntity("姓名", "realName", 20);
            ExcelExportEntity excelExportEntity2 = new ExcelExportEntity("编号", "code", 20);
            entityList.add(excelExportEntity1);
            entityList.add(excelExportEntity2);

            ExportParams params = new ExportParams(projectVisitConfig.getVisitName(), projectVisitConfig.getVisitName(), ExcelType.XSSF);
            params.setCreateHeadRows(true);
            params.setSheetName(projectVisitConfig.getVisitName());
            List<TemplateFormVariableExportVo> TemplateFormDetailLabelList = templateConfigService.getProjectFormAndTableLabelByProjectIdAndFormVariableIds(projectId, projectVisitConfig.getId().toString(), "", "", "");
            for (TemplateFormVariableExportVo templateFormDetail : TemplateFormDetailLabelList) {
                ExcelExportEntity excelExportEntity = new ExcelExportEntity(templateFormDetail.getLabel(), templateFormDetail.getKey(), 20);
                excelExportEntity.setOrderNum(templateFormDetail.getRownum());
                excelExportEntity.setGroupName(templateFormDetail.getFormName());
                excelExportEntity.setNeedMerge(true);
                entityList.add(excelExportEntity);
            }

            for (ProjectTesteeVo testeeMap : projectTesteeList) {
                String testeeId = testeeMap.getId().toString();
                // 查询普通表单提交记录
                List<ProjectTesteeResultExportVo> projectTesteeResultData = projectTesteeResultService.getProjectTesteeFormResultByVariableIds(projectId, projectVisitConfig.getId().toString(), formId, formDetailIds, testeeId, orgId);
                List<LinkedHashMap<String, List<ProjectTesteeFormAndTableResultExportVo>>> exportTableList = new ArrayList<>();
                // 查询参与者当前所有表格信息
                List<ProjectTesteeFormAndTableCountVo> testeeTableCountList = projectTesteeTableService.getProjectTesteeFormAndTableCountByVariableIds(projectId, projectVisitConfig.getId().toString(), formId, formDetailIds, testeeId, orgId);
                for (int i = 0; i < testeeTableCountList.size(); i++) {
                    String queryFormDetailId = testeeTableCountList.get(i).getFormDetaiId();
                    List<ProjectTesteeFormDetailTableVo> projectTesteeFormDetailTableRow = projectTesteeTableService.getProjectTesteeFormDetailTableRow(projectId, projectVisitConfig.getId().toString(), formId, queryFormDetailId, testeeId, "", "4");
                    log.info("projectTesteeFormDetailTableRow:{}", JSON.toJSONString(projectTesteeFormDetailTableRow));
                }

                List<ProjectTesteeFormAndTableResultExportVo> projectTesteeFormAndTableResultData = projectTesteeTableService.getProjectTesteeFormAndTableResultByVariableIds(projectId, projectVisitConfig.getId().toString(), formId, formDetailIds, formTableIds, testeeId, orgId);
                /*projectTesteeFormAndTableResultData.forEach(data -> {
                    Map<String, Object> testeeTableRowDataMap = new HashMap<>();
                    if (StringUtils.isNotEmpty(data.getFieldValue())) {
                        testeeTableRowDataMap.put(data.getFieldName(), data.getFieldValue());
                    }
                    dataList.add(testeeTableRowDataMap);
                });*/

                /*List<ProjectTesteeTableResultExportVo> projectTesteeTableResultData = projectTesteeTableService.getProjectTesteeTableResultByVariableIds(projectId, projectVisitConfig.getId().toString(), formId, formDetailId, formTableId, testeeId, orgId);
                LinkedHashMap<String, List<ProjectTesteeTableResultExportVo>> projectTesteeTableDataMap = projectTesteeTableResultData.stream().collect(Collectors.groupingBy(data ->data.getFormDetailId(), LinkedHashMap::new, Collectors.toList()));
                projectTesteeTableDataMap.forEach((tableId,formTableData)->{
                    TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfig(tableId);
                    List<Long> projectTesteeTableRowNumber = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, projectVisitConfig.getId().toString(), templateFormDetailConfig.getFormId().toString(), tableId, testeeId, "", "", "", "");
                    if(projectTesteeTableRowNumber.size() > count.get()){
                        count.set(projectTesteeTableRowNumber.size());
                    }
                    countMap.put(tableId, projectTesteeTableRowNumber.size());
                    Map<String, Object> testeeTableDataMap = new HashMap<>();
                    log.info("formDetailId:{}, projectTesteeTableDataMap: {}", tableId, JSON.toJSONString(projectTesteeTableDataMap));
                    List<ProjectTesteeTableResultExportVo> projectTesteeTableResultExportVoList = formTableData.stream().collect(Collectors.toList());
                    LinkedHashMap<String, List<ProjectTesteeTableResultExportVo>> projectTesteeTableMap = projectTesteeTableResultExportVoList.stream().collect(Collectors.groupingBy(data ->data.getFormDetailId()+"-"+data.getRowNumber(), LinkedHashMap::new, Collectors.toList()));
                    totalTableList.add(projectTesteeTableMap);
                    projectTesteeTableMap.forEach((rowNumber,value)->{
                        Map<String, Object> testeeTableRowDataMap = new HashMap<>();
                        for (ProjectTesteeTableResultExportVo projectTesteeTableResultExportVo : value) {
                            if(StringUtils.isNotEmpty(projectTesteeTableResultExportVo.getFieldValue())){
                                //testeeTableRowDataMap.put(projectTesteeTableResultExportVo.getFieldName(), projectTesteeTableResultExportVo.getFieldValue());
                            }
                        }
                        log.info("rowNumber:{}, testeeTableRowDataMap: {}", rowNumber, JSON.toJSONString(testeeTableRowDataMap));
                        if(!testeeTableRowDataMap.isEmpty()){
                            testeeTableRowDataMap.put("realName", testeeName);
                            testeeTableRowDataMap.put("code", code);
                            //dataList.add(testeeTableRowDataMap);
                            //testeeTableDataMap.putAll(testeeTableRowDataMap);
                        }
                    });
                    //dataList.addAll(totalList);
                });*/



                /*String targetTableId = String.valueOf(count.get());
                Map<String, Integer> sortMap = sortMap(countMap);
                Set<Map.Entry<String, Integer>> entries = sortMap.entrySet();
                for (Map.Entry<String, Integer> entry : entries) {
                    targetTableId = entry.getKey();
                    break;
                }

                List<ProjectTesteeTableResultExportVo> projectTesteeTableData = projectTesteeTableService.getProjectTesteeTableResultByVariableIds(projectId, projectVisitConfig.getId().toString(), formId, "", formTableId, testeeId, orgId);
                projectTesteeTableData.forEach(data ->{
                    Map<String, Object> testeeTableRowDataMap = new HashMap<>();
                    if(StringUtils.isNotEmpty(data.getFieldValue())){
                        testeeTableRowDataMap.put(data.getFieldName(), data.getFieldValue());
                    }
                    dataList.add(testeeTableRowDataMap);
                });*/


                /*for (LinkedHashMap<String, List<ProjectTesteeTableResultExportVo>> tableRowInfoDataMap : totalTableList) {
                    tableRowInfoDataMap.forEach((tableInfo, tableRowInfo) -> {
                        List<ProjectTesteeTableResultExportVo> projectTesteeTableResultExportList = tableRowInfo.stream().collect(Collectors.toList());
                        Map<String, Object> testeeTableRowDataMap = new HashMap<>();
                        projectTesteeTableResultExportList.forEach(data -> {
                            if(StringUtils.isNotEmpty(data.getFieldValue())){
                                testeeTableRowDataMap.put(data.getFieldName(), data.getFieldValue());
                            }
                        });
                        dataList.add(testeeTableRowDataMap);
                    });
                }*/

                LinkedHashMap<String, List<ProjectTesteeFormAndTableResultExportVo>> projectTesteeFormAndTableDataMap = projectTesteeFormAndTableResultData.stream().collect(Collectors.groupingBy(data ->data.getTableSort(), LinkedHashMap::new, Collectors.toList()));
                projectTesteeFormAndTableDataMap.forEach((tableInfo, tableRowInfo) -> {
                    List<ProjectTesteeFormAndTableResultExportVo> projectTesteeTableResultExportList = tableRowInfo.stream().collect(Collectors.toList());
                    Map<String, Object> testeeTableRowDataMap = new HashMap<>();
                    projectTesteeTableResultExportList.forEach(data -> {
                        if(StringUtils.isNotEmpty(data.getFieldValue())){
                            testeeTableRowDataMap.put(data.getFieldName(), data.getFieldValue());
                            testeeTableRowDataMap.put("realName", data.getRealName());
                            testeeTableRowDataMap.put("code", data.getCode());
                        }
                    });
                    dataList.add(testeeTableRowDataMap);
                });
            }
            dataMap.put("title", params);
            dataMap.put("entityList", entityList);
            dataMap.put("data", dataList);
            sheetsList.add(dataMap);
        }

        Workbook workbook = exportMultiSheetExcel(sheetsList);
        try {
            workbook.write(outputSream);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        projectTesteeExport.setId(SnowflakeIdWorker.getUuid());
        projectTesteeExport.setProjectId(Long.parseLong(projectId));
        projectTesteeExport.setTaskName(taskName);
        projectTesteeExport.setCreateTime(new Date());
        projectTesteeExport.setOperator(Long.parseLong(operator));
        projectTesteeExport.setOrgId(orgId);
        Organization organizationInfo = organizationService.getSystemOrganizationInfo(orgId);
        if (organizationInfo != null) {
            projectTesteeExport.setOrgName(organizationInfo.getName());
        }
        Long count = projectTesteeExportMapper.getPorjectTesteeCount(projectId, orgId);
        projectTesteeExport.setTesteeCount(count);
        projectTesteeExport.setStatus(BusinessConfig.VALID_STATUS);
        projectTesteeExport.setDownloadUrl(storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + taskName + Constants.EXPORT_FILE_SUFFIX);
        projectTesteeExportMapper.insert(projectTesteeExport);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }



    /**
     * 新建导出参与者任务
     * @param param
     * @return
     */
    @Override
    public CustomResult saveTesteeExportTask(ExportTesteeDataParam param) {
        List<String> rowIdList = param.getRowIdList();
        Map<String,Object> rowMap = new HashMap<>();
        if (rowIdList.size()>0){
            rowIdList.forEach(row->rowMap.put(row,true));
        }
        //按照访视展示每个参与者的表单明细
        CustomResult customResult = new CustomResult();
        ProjectTesteeExport projectTesteeExport = new ProjectTesteeExport();
        String targetPath = storageConfig.getUploadFolder() + param.getTaskName() + Constants.EXPORT_FILE_SUFFIX;
        OutputStream outputSream = null;
        try {
            if (!new File(storageConfig.getUploadFolder()).exists()) {
                new File(storageConfig.getUploadFolder()).mkdirs();
            }
            File file = new File(targetPath);
            outputSream = new FileOutputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        // 所有sheet页的数据
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        // 导出参与者的id
        List<String> tids = param.getTesteeIdList();

        // 根据参与者id 查询并根据code 进行排序
        List<String> testeeIdList = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(tids)){
            List<ProjectTesteeInfo> projectTesteeList = projectTesteeInfoMapper.getProjectTesteeListByIds(param.getProjectId(),param.getOrgId(),tids);
            if (CollectionUtil.isNotEmpty(projectTesteeList)){
                projectTesteeList.forEach(t->testeeIdList.add(t.getId().toString()));
            }
        }
        /*if (CollectionUtil.isEmpty(testeeIdList)){
            List<Map<String, Object>> testees = projectTesteeService.getProjectTesteeList(param.getProjectId(), "", param.getOrgId());
            if (CollectionUtil.isNotEmpty(testees)){
                testees.forEach(t->testeeIdList.add(t.get("id").toString()));
            }
        }*/

        // 根据项目id和机构id获取参与者列表
//        List<Map<String, Object>> testees = projectTesteeService.getProjectTesteeList(param.getProjectId(), "", param.getOrgId());
        // 根据项目id获取表单
        List<TemplateFormConfig> forms = templateConfigService.getProjectTemplateFormListByProjectId(param.getProjectId(), "0");
        forms.forEach(form->{
            // 创建这个集合是为了保证表单在前面，明细在后面的sheet 中。
            List<Map<String, Object>> sList = new ArrayList<>();
            // 创建excel导出配置
            ExportParams params = new ExportParams(form.getFormName(), null, ExcelType.XSSF);
            params.setCreateHeadRows(true);
            params.setSheetName(form.getFormName());

            // 每个表单存放的数据
            List<Map<String, Object>> dataList = new ArrayList<>();
            // 每个表单表头集合
            List<ExcelExportEntity> excelEntityList = createExcelEntityList();
            // 添加一个标志，如果有普通变量则为true ,防止如果没有普通变量也生成一个sheet 页
            boolean flg= false;
            // 根据表单id获取变量，
            // 循环变量创建表头集合，如果变量中有table 变量需要另外存放一个sheet页
            List<TemplateFormDetailVo> templateFormDetailConfigByFormId = templateConfigService.getTemplateFormDetailConfigListByFormId(null, form.getId().toString(), "", "1", "1", "0");
            for (TemplateFormDetailVo templateFormDetail : templateFormDetailConfigByFormId) {
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetail.getType())) {
                    // 题组templateFormDetailConfigByFormId = {ArrayList@17795}  size = 5
                    ExportParams paramsRow = new ExportParams(templateFormDetail.getLabel(), templateFormDetail.getLabel(), ExcelType.XSSF);
                    paramsRow.setCreateHeadRows(true);
                    paramsRow.setSheetName(form.getFormName()+"-"+templateFormDetail.getLabel());
                    // 是否组合数据，如果前端没有选择导出列表，只导出普通变量，就没有必要组合表数据了。
                    Map<String, Object> groupMap = getGroupTitle(rowMap, form.getId().toString(), templateFormDetail.getId().toString());
                    List<ExcelExportEntity>  titleEntry = (List<ExcelExportEntity>) groupMap.get("entityList");
                    Boolean  gflg = (Boolean) groupMap.get("flg");
                    // 因为默认添加参与者名称和编号，所以size 需要大于2
                    if (titleEntry.size()>2){
                        // 题组的数据。
                        List<Map<String,Object>> groupData = new ArrayList<>();

                        if (CollectionUtil.isNotEmpty(testeeIdList)){
                            testeeIdList.forEach(testeeId->{
                                getGroupData(gflg,groupData,param.getProjectId(),param.getOrgId(),null,testeeId,form.getId().toString());
                            });
                        }
                        // 创建sheet页，并存放到集合中
                        HashMap<String,Object> sheetRowMap = new HashMap<>(3);
                        sheetRowMap.put("title", paramsRow);
                        sheetRowMap.put("entityList", titleEntry);
                        sheetRowMap.put("data", groupData);
                        sList.add(sheetRowMap);
                    }
                }else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetail.getType())) {
                    // 如果变量是表格变量
                    ExportParams paramsRow = new ExportParams(templateFormDetail.getLabel(), templateFormDetail.getLabel(), ExcelType.XSSF);
                    paramsRow.setCreateHeadRows(true);
                    paramsRow.setSheetName(form.getFormName()+"-"+templateFormDetail.getLabel());
                    // 表格的表头集合
                    List<ExcelExportEntity> rowEntry = createExcelEntityList();

                    List<Map<String,Object>> rowDataList = new ArrayList<>();
                    // 如果变量中有table 变量需要另外存放一个sheet页
                    // 根据表格获取表格的列
                    List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(form.getId().toString(), templateFormDetail.getId().toString(), false);
                    if(CollectionUtil.isNotEmpty(rowList)){
                        for (TemplateTableVo templateTableVo : rowList) {
                            if (!isExport(rowMap, templateTableVo.getId().toString())) {
                                continue;
                            }
                            rowEntry.add(new ExcelExportEntity(templateTableVo.getLabel(), templateTableVo.getId().toString(), 20));
                        }

                        if (rowEntry.size()>2 && CollectionUtil.isNotEmpty(testeeIdList)){
                            // 循环每一个参与者进行查询数据。
                            testeeIdList.forEach(testeeId->{
                                List<ProjectTesteeTableResultExportVo> tableData = projectTesteeTableService.getProjectTesteeTableRecordsWithVariableIds(param.getProjectId(), param.getOrgId(), null, form.getId().toString(), templateFormDetail.getId().toString(), null, testeeId);
                                if (CollectionUtil.isNotEmpty(tableData)){
                                    // 表格数据根据tableSort 进行分组。分组成每一行
                                    ProjectTesteeTableResultExportVo tableResultExportVo = tableData.get(0);
                                    Map<String, List<ProjectTesteeTableResultExportVo>> tableDateMap = tableData.stream().collect(Collectors.groupingBy(ProjectTesteeTableResultExportVo::getTableSort,LinkedHashMap::new,Collectors.toList()));
                                    // 获取列的数据拼接表格数据
                                    tableDateMap.forEach((k,v)->{
                                        HashMap<String,Object> map = new HashMap<>();
                                        map.put("realName",tableResultExportVo.getRealName());
                                        map.put("code",tableResultExportVo.getCode());
                                        v.forEach(vo->map.put(vo.getFormTableId(),vo.getFieldValue()));
                                        rowDataList.add(map);
                                    });
                                }
                            });
                        }
                        if (rowEntry.size()>2){
                            // 创建sheet页，并存放到集合中
                            HashMap<String,Object> sheetRowMap = new HashMap<>(3);
                            sheetRowMap.put("title", paramsRow);
                            sheetRowMap.put("entityList", rowEntry);
                            sheetRowMap.put("data", rowDataList);
                            sList.add(sheetRowMap);
                        }
                    }
                } else {
                    // 如果变量是普通变量
                    if (!isExport(rowMap,templateFormDetail.getId().toString())){
                        continue;
                    }
                    excelEntityList.add(new ExcelExportEntity(templateFormDetail.getLabel(), templateFormDetail.getId().toString(), 20));
                    flg=true;
                }

            }
            if (flg){
                // 普通变量值

                if (CollectionUtil.isNotEmpty(testeeIdList)){
                    testeeIdList.forEach(testeeId->{
                        ProjectTesteeVo vo = projectTesteeInfoService.getProjectTesteeBaseInfo(param.getProjectId(), testeeId);
                        List<ProjectTesteeResult> detail = projectTesteeResultService.getTesteeVisitFormResultDetail(param.getProjectId(), "", null, form.getId().toString(), testeeId);
                        if (CollectionUtil.isNotEmpty(detail)){
                            Map<String, Object> map = new HashMap<>();
                            map.put("realName",vo.getRealName());
                            map.put("code",vo.getCode());
                            detail.forEach(re->map.put(re.getFormDetailId().toString(),re.getFieldValue()));
                            dataList.add(map);
                        }
                    });
                }
                // 创建sheet页，并存放到集合中
                HashMap<String,Object> sheetMap = new HashMap<>(3);
                sheetMap.put("title", params);
                sheetMap.put("entityList", excelEntityList);
                sheetMap.put("data", dataList);
                sList.add(0,sheetMap);
            }
            if (sList.size()>0){
                for (Map<String, Object> map : sList) {
                    sheetsList.add(map);
                }
            }
        });

        Workbook workbook = exportMultiSheetExcel(sheetsList);
        try {
            workbook.write(outputSream);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        // 根据参与者ids 获取他们所属的中心。
        List<String> testeeIds = param.getTesteeIdList();
        if (CollectionUtil.isNotEmpty(testeeIds)){
            List<ProjectTesteeInfo> testees = projectTesteeInfoMapper.getOrgIdsByTesteeIds(testeeIds);
            if (CollectionUtil.isNotEmpty(testees)){
                String orgNames = "";
                String orgIds = "";
                // 代码重构 暂时注释
                /*for (ProjectTesteeInfo testee : testees) {
                    orgIds+=testee.getOwnerOrgId()+",";
                    orgNames+=testee.getOwnerOrgName()+",";
                }
                if (StringUtils.isNotEmpty(orgNames) && StringUtils.isNotEmpty(orgIds)){
                    orgIds=orgIds.substring(0,orgIds.length()-1);
                    orgNames=orgNames.substring(0,orgNames.length()-1);
                }*/
                projectTesteeExport.setOrgId(orgIds);
                projectTesteeExport.setOrgName(orgNames);
            }
        }
        projectTesteeExport.setId(SnowflakeIdWorker.getUuid());
        projectTesteeExport.setProjectId(Long.parseLong(param.getProjectId()));
        projectTesteeExport.setTaskName(param.getTaskName());
        projectTesteeExport.setCreateTime(new Date());
        projectTesteeExport.setOperator(Long.parseLong(param.getOperator()));
        projectTesteeExport.setExportFileType(param.getExportType());
        projectTesteeExport.setDescription(param.getExportDesc());
        Long count = projectTesteeExportMapper.getPorjectTesteeCount(param.getProjectId(), param.getOrgId());
        projectTesteeExport.setTesteeCount(count);
        projectTesteeExport.setStatus(BusinessConfig.VALID_STATUS);
        projectTesteeExport.setDownloadUrl(storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + param.getTaskName() + Constants.EXPORT_FILE_SUFFIX);
        projectTesteeExportMapper.insert(projectTesteeExport);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    private static boolean isExport(Map<String,Object> rowMap,String rowId) {
        // 需要导出的字段id做成一个map 方便查询
        if (rowMap.size()>0){
            return rowMap.get(rowId)!=null;
        }else {
            return true;
        }
    }

    /**
     * 每个表单表头集合
     * @return
     */
    private static List<ExcelExportEntity> createExcelEntityList() {
        List<ExcelExportEntity> entityList = new LinkedList<>();
        entityList.add(new ExcelExportEntity("姓名", "realName", 20));
        entityList.add(new ExcelExportEntity("编号", "code", 20));
        return entityList;
    }

    @Override
    public CustomResult downloadTesteeImportTemplate(String projectId, String userId) {
        CustomResult customResult = new CustomResult();
        String targetPath = storageConfig.getUploadFolder() + Constants.EXCEL_SHEET_FORM_IMPORT_TESTEE_NAME + Constants.EXPORT_FILE_SUFFIX;
        OutputStream outputSream = null;
        try {
            if (!new File(storageConfig.getUploadFolder()).exists()) {
                new File(storageConfig.getUploadFolder()).mkdirs();
            }
            File file = new File(targetPath);
            outputSream = new FileOutputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        List<Map<String, Object>> sheetsList = new ArrayList<>();

        //查询项目访视列表
        List<ProjectVisitConfig> visitList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
        if(CollectionUtil.isEmpty(visitList)){
            customResult.setMessage("未设置有效的访视表单变量");
            return customResult;
        }
        for (ProjectVisitConfig projectVisitConfig : visitList) {
            List<ExcelExportEntity> entityList = new LinkedList<>();
            List<Map<String, Object>> dataList = new ArrayList<>();
            ExcelExportEntity excelExportEntity1 = new ExcelExportEntity("参与者姓名", "realName", 20);
            ExcelExportEntity excelExportEntity2 = new ExcelExportEntity("参与者编号", "code", 20);
            excelExportEntity1.setOrderNum(-Integer.MAX_VALUE);
            excelExportEntity2.setOrderNum(-Integer.MAX_VALUE+1);
            excelExportEntity1.setNeedMerge(true);
            excelExportEntity2.setNeedMerge(true);
            excelExportEntity1.setGroupName(Constants.EXCEL_SHEET_VISIT_TESTEE_NAME);
            excelExportEntity2.setGroupName(Constants.EXCEL_SHEET_VISIT_TESTEE_NAME);
            entityList.add(excelExportEntity1);
            entityList.add(excelExportEntity2);

            Map<String, Object> dataMap = new HashMap<>();

            ExportParams params = new ExportParams("参与者访视表单录入数据", "", ExcelType.XSSF);
            params.setCreateHeadRows(true);
            params.setSheetName(projectVisitConfig.getVisitName());
            params.setDynamicData(true);
            params.setDictHandler(new DictExcelHandler());

            List<TemplateFormVariableExportVo> templateFormVariableExportVoList = templateConfigService.getProjectFormAndTableLabelByProjectIdAndFormVariableIds(projectId, projectVisitConfig.getId().toString(), "", "", "");
            for (TemplateFormVariableExportVo templateVariableExport : templateFormVariableExportVoList) {
                String requiredTip = "";
                Boolean required = templateVariableExport.getRequired() == null ? false : templateVariableExport.getRequired();
                if(required){
                    requiredTip = "必填项* ";
                }
                ExcelExportEntity excelExportEntity = new ExcelExportEntity(requiredTip + templateVariableExport.getLabel().concat("[").concat(templateVariableExport.getKey().concat("]")), templateVariableExport.getKey(), 20);
                excelExportEntity.setOrderNum(templateVariableExport.getRownum());
                excelExportEntity.setGroupName(templateVariableExport.getFormName());
                excelExportEntity.setNeedMerge(true);
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateVariableExport.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateVariableExport.getType())) {
                    String options = templateVariableExport.getOptions();
                    List<FormVarOptionVo> formVarOptionVoList = JSON.parseArray(options, FormVarOptionVo.class);
                    String[] array = new String[formVarOptionVoList.size()];
                    for (int i = 0; i < formVarOptionVoList.size(); i++) {
                        array[i] = formVarOptionVoList.get(i).getLabel() + "_" + formVarOptionVoList.get(i).getLabel();
                    }

                    List<String> strings = new ArrayList<>();
                    strings.add("_null");
                    formVarOptionVoList.forEach(item -> {
                        String dictName = item.getLabel();
                        String dictVal = item.getOption();
                        strings.add(dictName + "_" + dictVal);
                    });
                    String[] selectData = strings.toArray(new String[0]);
                    excelExportEntity.setAddressList(true);
                    excelExportEntity.setReplace(selectData);
                }
                entityList.add(excelExportEntity);
            }
            Map<String, Object> testeeDataMap = new HashMap<>();
            dataList.add(testeeDataMap);


            dataMap.put("title", params);
            dataMap.put("entityList", entityList);
            dataMap.put("data", dataList);
            sheetsList.add(dataMap);
        }

        Workbook workbook = exportMultiSheetExcel(sheetsList);
        try {
            workbook.write(outputSream);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        customResult.setData(storageConfig.getViewUrl() + Constants.SYSTEM_VIEW_PATH + Constants.EXCEL_SHEET_FORM_IMPORT_TESTEE_NAME + Constants.EXPORT_FILE_SUFFIX);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    /**
     * 获取题组的表头信息
     * @return 题组表头列表
     */
    public  Map<String,Object>  getGroupTitle(Map<String,Object> rowMap,String formId,String formDetailId){
        // excel 表头构建
        // 是否组合数据，如果前端没有选择导出列表，只导出普通变量，就没有必要组合表数据了。
        boolean flg = false;

        List<ExcelExportEntity> entityList = createExcelEntityList();
        // 获取题组的变量
        List<TemplateFormDetailVo> details = templateConfigService.getTemplateFormDetailByFormIdAndVariableType(null, formId, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
        if (CollectionUtil.isNotEmpty(details)){
            for (TemplateFormDetailVo detail : details) {
                // 获取每个题组的变量
                List<TemplateFormDetailVo> detailVoList = templateConfigService.getTemplateFormDetailByGroupId(formId, detail.getId()+"", "", false);
                if (CollectionUtil.isNotEmpty(detailVoList)){
                    for (TemplateFormDetailVo templateFormDetailVo : detailVoList) {

                        if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                            // 普通变量添加到表头
                            if (!isExport(rowMap, templateFormDetailVo.getId().toString())) {
                                continue;
                            }
                            entityList.add(new ExcelExportEntity(templateFormDetailVo.getLabel(), templateFormDetailVo.getId().toString(), 20));
                        } else {
                            // 获取表格变量的表头
                            // 查询表格Head数据
                            List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailVo.getId().toString(), false);
                            if (CollectionUtil.isNotEmpty(rowList)){
                                for (TemplateTableVo vo : rowList) {
                                    if (!isExport(rowMap, vo.getId().toString())) {
                                        continue;
                                    }
                                    flg=true;
                                    entityList.add(new ExcelExportEntity(vo.getLabel(), vo.getId().toString(), 20));
                                }

                            }
                        }
                    }
                }
            }
        }
        Map<String,Object> data  = new HashMap<>(2);
        data.put("flg",flg);
        data.put("entityList",entityList);
        return data;
    }


    /**
     * 获取题组的数据
     * @param projectId
     * @param orgId
     * @param visitId
     * @return 题组表头列表
     */
    public void getGroupData(Boolean flg, List<Map<String,Object>> data ,String projectId, String orgId, String visitId, String testeeId,String fromId){

        // 获取当前参与者获取参与者当前题组的数据的普通变量的数据
        List<ProjectTesteeTableResultExportVo> variableAndTable =
                projectTesteeTableService.getProjectTesteeGroupVariableAndTable(projectId, orgId, visitId, testeeId,fromId);
        if (CollectionUtil.isNotEmpty(variableAndTable)){
            // 对题组中的数据进行分组 根据组id进行分组，可以求出每个组的数据
            Map<String, List<ProjectTesteeTableResultExportVo>> groupList
                    = variableAndTable.stream().collect(Collectors.groupingBy(ProjectTesteeTableResultExportVo::getGroupId));
            if (groupList.size()>0){
                groupList.forEach((k,v)->{
                    // 根据rowNumber 进行分组，区分出来
                    LinkedHashMap<String, List<ProjectTesteeTableResultExportVo>> valuesList = v.stream().collect(Collectors.groupingBy(ProjectTesteeTableResultExportVo::getRowNumber, LinkedHashMap::new, Collectors.toList()));
                    if (valuesList.size()>0){
                        // 获取普通变量的数据
                        List<ProjectTesteeTableResultExportVo> variableList = valuesList.get("1");
                        if (CollectionUtil.isNotEmpty(variableList)){
                            // 普通变量封装成的map
                            Map<String, Object> map = variableList.stream()
                                    .collect(Collectors.toMap(ProjectTesteeTableResultExportVo::getVariableId, ProjectTesteeTableResultExportVo::getFieldValue));
                            if (flg){
                                // 循环获取table 变量的数据
                                valuesList.forEach((key,value)->{
                                    if (!key.equals("1")){
                                        // table 变量的数据 封装层一个map ,同时把普通变量的数据也存放到里面。
                                        // 普通变量和表格变量组合到一起，形成一个大对象。行数已表格的数据为准。
                                        Map<String, Object> row = new HashMap<>();
                                        value.forEach(vo->{
                                            row.put("realName",vo.getRealName());
                                            row.put("code",vo.getCode());
                                            row.put(vo.getVariableId(),vo.getFieldValue());
                                        });
                                        row.putAll(map);
                                        data.add(row);
                                    }
                                });
                            }else {
                                ProjectTesteeTableResultExportVo vo = variableList.get(0);
                                map.put("realName",vo.getRealName());
                                map.put("code",vo.getCode());
                                data.add(map);
                            }
                        }else {
                            //没有普通变量的数据，只有表格变量
                            // 循环获取table 变量的数据
                            valuesList.forEach((key,value)->{
                                    // table 变量的数据 封装层一个map ,同时把普通变量的数据也存放到里面。
                                    // 普通变量和表格变量组合到一起，形成一个大对象。行数已表格的数据为准。
                                    Map<String, Object> row = value.stream()
                                            .collect(Collectors.toMap(ProjectTesteeTableResultExportVo::getVariableId, ProjectTesteeTableResultExportVo::getFieldValue));
                                    data.add(row);
                            });
                        }
                    }
                });
            }
        }
    }


    @Override
    public String saveBatchProjectTesteeVisitFormData(List<TesteeVisitFormDataWrapper> visitFormDataWrapperList, String projectId, String userId, List<ProjectTesteeVisitFormExceptionVo> errorList) {
        if (CollectionUtil.isEmpty(visitFormDataWrapperList)) {
            throw new ServiceException("导入参与者访视表单数据不能为空");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder msg = new StringBuilder();
        for (TesteeVisitFormDataWrapper testeeVisitFormDataWrapper : visitFormDataWrapperList) {
            String visitName = testeeVisitFormDataWrapper.getVisitName();
            //根据访视名称查询访视id
            ProjectVisitConfig projectVisitConfig = projectVisitConfigService.getProjectVisitConfigByVisitName(projectId, visitName);
            if (projectVisitConfig == null) {
                throw new ServiceException("导入参与者访视信息不存在");
            }
            List<ProjectTesteeTableParam.TableRowData> tableRowDataList = new ArrayList<>();
            List<FormVariableExcelWrapper> excelFormVariableDataList = testeeVisitFormDataWrapper.getExcelFormVariableDataList();
            log.info("excelFormVariableDataList: {}", JSON.toJSONString(excelFormVariableDataList));
            for (int i = 2; i < excelFormVariableDataList.size(); i++) {
                FormVariableExcelWrapper formVariableExcelWrapper = excelFormVariableDataList.get(i);
                ProjectTesteeVisitFormExceptionVo projectTesteeVisitFormExceptionVo = new ProjectTesteeVisitFormExceptionVo();

                ProjectTesteeVisitFormExceptionVo projectTesteeVisitTesteeFormExceptionVo = new ProjectTesteeVisitFormExceptionVo();
                String realName = excelFormVariableDataList.get(0).getFieldValue();
                if (StringUtils.isEmpty(realName)) {
                    failureNum++;
                    projectTesteeVisitTesteeFormExceptionVo.setMessage(visitName + "参与者姓名不能为空");
                    projectTesteeVisitTesteeFormExceptionVo.setFieldName("realName");
                    errorList.add(projectTesteeVisitTesteeFormExceptionVo);
                    break;
                }
                projectTesteeVisitTesteeFormExceptionVo.setRealName(realName);
                String testeeCode = excelFormVariableDataList.get(1).getFieldValue();
                if (StringUtils.isEmpty(testeeCode)) {
                    failureNum++;
                    projectTesteeVisitTesteeFormExceptionVo.setMessage(visitName + "参与者编号不能为空");
                    projectTesteeVisitTesteeFormExceptionVo.setFieldName("testeeCode");
                    errorList.add(projectTesteeVisitTesteeFormExceptionVo);
                    break;
                }

                projectTesteeVisitTesteeFormExceptionVo.setTesteeCode(testeeCode);
                ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getTesteeBaseInfoByTesteeCode(projectId, testeeCode);
                if (projectTesteeVo == null) {
                    failureNum++;
                    projectTesteeVisitTesteeFormExceptionVo.setMessage(visitName + "参与者信息不存在");
                    errorList.add(projectTesteeVisitTesteeFormExceptionVo);
                    continue;
                }

                BeanUtils.copyProperties(formVariableExcelWrapper, projectTesteeVisitFormExceptionVo);
                CustomResult customResult = new CustomResult();
                ProjectTesteeResultParam projectTesteeResultParam = new ProjectTesteeResultParam();
                projectTesteeResultParam.setOperator(userId);
                List<ProjectTesteeResultParam.TesteeFormResultValue> formResultValueList = new ArrayList<>();

                String key = formVariableExcelWrapper.getFieldName();
                //根据key获取对应的变量
                String variable = StringUtils.substringBetween(key, "[", "]");
                String value = formVariableExcelWrapper.getFieldValue();
                //如果是单选或者多选 判断是否符合options
                projectTesteeVisitFormExceptionVo.setLabel(key);
                projectTesteeVisitFormExceptionVo.setFieldName(variable);

                //区分普通表单和表格变量
                if(!variable.startsWith("td_")){
                    TemplateFormDetailExcelImportVo templateFormDetailExcelImportVo = templateConfigService.getFormDetailByFormNameAndVariableName(projectId, visitName, formVariableExcelWrapper.getFormName(), variable);
                    if (templateFormDetailExcelImportVo == null) {
                        failureNum++;
                        projectTesteeVisitFormExceptionVo.setMessage("字段信息" + variable + "不存在");
                        errorList.add(projectTesteeVisitFormExceptionVo);
                        continue;
                    }
                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormDetailExcelImportVo.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailExcelImportVo.getType())) {
                        String options = templateFormDetailExcelImportVo.getOptions() == null ? "" : templateFormDetailExcelImportVo.getOptions();
                        if (!options.contains(value)) {
                            failureNum++;
                            projectTesteeVisitFormExceptionVo.setMessage("字段录入数据" + value + "不符合CRF配置选项内容");
                            errorList.add(projectTesteeVisitFormExceptionVo);
                            continue;
                        }
                    }

                    projectTesteeResultParam.setProjectId(templateFormDetailExcelImportVo.getProjectId());
                    projectTesteeResultParam.setVisitId(templateFormDetailExcelImportVo.getVisitId());
                    projectTesteeResultParam.setFormId(templateFormDetailExcelImportVo.getFormId());
                    projectTesteeResultParam.setTesteeId(projectTesteeVo.getId());

                    String visitId = templateFormDetailExcelImportVo.getVisitId().toString();
                    String formId = templateFormDetailExcelImportVo.getFormId().toString();
                    String formDetailId = templateFormDetailExcelImportVo.getFormDetailId().toString();

                    ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", visitId, formId, "", formDetailId, projectTesteeVo.getId().toString());
                    if (projectTesteeResult != null) {
                        projectTesteeResult.setFieldValue(value);
                        projectTesteeResult.setUpdateTime(new Date());
                        projectTesteeResult.setUpdateUser(userId);
                        projectTesteeResultService.updateByPrimaryKeySelective(projectTesteeResult);
                    } else {
                        ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue = new ProjectTesteeResultParam.TesteeFormResultValue();
                        testeeFormResultValue.setFieldName(templateFormDetailExcelImportVo.getKey());
                        testeeFormResultValue.setFieldValue(value);
                        testeeFormResultValue.setLabel(templateFormDetailExcelImportVo.getLabel());
                        testeeFormResultValue.setFormDetailId(templateFormDetailExcelImportVo.getFormDetailId());
                        testeeFormResultValue.setStatus("0");
                        formResultValueList.add(testeeFormResultValue);
                        projectTesteeResultParam.setDataList(formResultValueList);
                        projectTesteeInfoService.saveTesteeVisitFormDetail(projectTesteeResultParam);
                    }
                    if (BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult.getMessage())) {
                        successNum++;
                    } else {
                        failureNum++;
                        projectTesteeVisitFormExceptionVo.setMessage(customResult.getMessage());
                        errorList.add(projectTesteeVisitFormExceptionVo);
                    }
                }else{
                    List<Long> formDetailVariableList = new ArrayList<>();
                    TemplateFormTableExcelImportVo templateFormTableExcelImportVo = templateConfigService.getFormTableInfoByFormNameAndVariableName(projectId, visitName, formVariableExcelWrapper.getFormName(), variable);
                    if (templateFormTableExcelImportVo == null) {
                        failureNum++;
                        projectTesteeVisitFormExceptionVo.setMessage("字段信息" + variable + "不存在");
                        errorList.add(projectTesteeVisitFormExceptionVo);
                        continue;
                    }
                    //log.info("formTableId: {}", templateFormTableExcelImportVo.getFormTableId());
                    if(CollectionUtil.isEmpty(formDetailVariableList) || formDetailVariableList.get(0).equals(templateFormTableExcelImportVo.getFormDetailId())){
                        formDetailVariableList.add(templateFormTableExcelImportVo.getFormDetailId());
                    }

                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormTableExcelImportVo.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormTableExcelImportVo.getType())) {
                        String options = templateFormTableExcelImportVo.getExpand() == null ? "" : templateFormTableExcelImportVo.getExpand();
                        if (!options.contains(value)) {
                            failureNum++;
                            projectTesteeVisitFormExceptionVo.setMessage("表格字段录入数据" + value + "不符合CRF配置选项内容");
                            errorList.add(projectTesteeVisitFormExceptionVo);
                            continue;
                        }
                    }

                    List<TemplateTableVo> projectTesteeTableRowHead = projectTesteeTableService.getProjectTesteeTableRowHead(templateFormTableExcelImportVo.getFormId().toString(), templateFormTableExcelImportVo.getFormDetailId().toString(), false);

                    //查询参与者表格行记录是否存在
                    ProjectTesteeTableParam projectTesteeTableParam = new ProjectTesteeTableParam();

                    projectTesteeTableParam.setCreateUserId(userId);
                    projectTesteeTableParam.setProjectId(Long.parseLong(projectId));
                    projectTesteeTableParam.setVisitId(projectVisitConfig.getId());
                    projectTesteeTableParam.setFormId(templateFormTableExcelImportVo.getFormId());
                    projectTesteeTableParam.setFormDetailId(templateFormTableExcelImportVo.getFormTableId());
                    projectTesteeTableParam.setTesteeId(projectTesteeVo.getId());

                    //将同一个表格的单元格组装成一条记录
                    if(formDetailVariableList.contains(templateFormTableExcelImportVo.getFormDetailId())){
                        ProjectTesteeTableParam.TableRowData tableRowData = new ProjectTesteeTableParam.TableRowData();
                        tableRowData.setFormTableId(templateFormTableExcelImportVo.getFormTableId());
                        tableRowData.setLabel(templateFormTableExcelImportVo.getLabel());
                        tableRowData.setFieldName(templateFormTableExcelImportVo.getKey());
                        tableRowData.setStatus("0");
                        tableRowData.setFieldValue(value);
                        tableRowDataList.add(tableRowData);
                    }

                    if(projectTesteeTableRowHead.size() == tableRowDataList.size()){
                        projectTesteeTableParam.setTableRowDataList(tableRowDataList);
                        customResult = projectTesteeInfoService.saveProjectTesteeTableRowRecord(projectTesteeTableParam);
                        tableRowDataList.clear();
                        log.info("saveProjectTesteeTableRowRecord: {}" , JSON.toJSONString(tableRowDataList));
                    }

                    if (BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult.getMessage())) {
                        successNum++;
                    } else {
                        failureNum++;
                        projectTesteeVisitFormExceptionVo.setMessage(customResult.getMessage());
                        errorList.add(projectTesteeVisitFormExceptionVo);
                    }
                }
            }
        }
        /*if (failureNum > 0) {
            msg.insert(0, "成功导入字段 " + successNum + " 条数据，解析失败字段 " + failureNum + " 条数据，请核对后重新导入");
        } else {
            msg.insert(0, "成功导入字段 " + successNum + " 条数据");
        }*/
        if (failureNum > 0) {
            msg.insert(0, "参与者部分字段导入异常，请下载查看异常文件说明");
        }
        return msg.toString();
    }


    @Override
    public TesteeImportResultVo saveBatchProjectTesteeVisitFormDataVersion2(List<TesteeVisitFormDataWrapper> visitFormDataWrapperList, String projectId, String userId, List<ProjectTesteeVisitFormExceptionVo> errorList) {
        if (CollectionUtil.isEmpty(visitFormDataWrapperList)) {
            throw new ServiceException("导入参与者访视表单数据不能为空");
        }
        TesteeImportResultVo testeeImportResultVo = new TesteeImportResultVo();
        Set<String> testeeCount = new HashSet<>();
        Set<String> visitCount = new HashSet<>();
        Set<String> formCount = new HashSet<>();
        for (TesteeVisitFormDataWrapper testeeVisitFormDataWrapper : visitFormDataWrapperList) {
            String visitName = testeeVisitFormDataWrapper.getVisitName();
            ProjectVisitConfig projectVisitConfig = projectVisitConfigService.getProjectVisitConfigByVisitName(projectId, visitName);
            if (projectVisitConfig == null) {
                throw new ServiceException("导入参与者访视信息不存在");
            }

            List<String> testeeCodeList = new ArrayList<>();
            List<Map<String, String>> formVariableDataList = testeeVisitFormDataWrapper.getFormDataList();
            for (int i = 0; i < formVariableDataList.size(); i++) {
                /**
                 * 对应每个访视 每个参与者所有表单变量数据
                 */
                Map<String, String> testeeVariableMap = formVariableDataList.get(i);

                ProjectTesteeVisitFormExceptionVo projectTesteeVisitFormException = new ProjectTesteeVisitFormExceptionVo();
                projectTesteeVisitFormException.setVisitName(projectVisitConfig.getVisitName());
                String realName = testeeVariableMap.get("参与者姓名");
                if (StringUtils.isEmpty(realName)) {
                    projectTesteeVisitFormException.setMessage("【" + visitName + "】" + "参与者姓名不能为空");
                    projectTesteeVisitFormException.setFieldName("realName");
                    errorList.add(projectTesteeVisitFormException);
                    continue;
                }
                projectTesteeVisitFormException.setRealName(realName);
                String testeeCode = testeeVariableMap.get("参与者编号");
                if (StringUtils.isEmpty(testeeCode)) {
                    projectTesteeVisitFormException.setMessage("【" + visitName + "】"  + "参与者编号不能为空");
                    projectTesteeVisitFormException.setFieldName("testeeCode");
                    projectTesteeVisitFormException.setTesteeCode(testeeCode);
                    errorList.add(projectTesteeVisitFormException);
                    continue;
                }
                if(testeeCodeList.contains(testeeCode)){
                    projectTesteeVisitFormException.setMessage("【" + visitName + "】"  + "当前工作簿参与者编号重复，请核对参与者基本信息");
                    projectTesteeVisitFormException.setFieldName("testeeCode");
                    projectTesteeVisitFormException.setTesteeCode(testeeCode);
                    errorList.add(projectTesteeVisitFormException);
                    continue;
                }
                projectTesteeVisitFormException.setTesteeCode(testeeCode);
                testeeCodeList.add(testeeCode);
                ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getTesteeBaseInfoByTesteeCode(projectId, testeeCode);
                if (projectTesteeVo == null) {
                    projectTesteeVisitFormException.setMessage("【" + visitName + "】" + "参与者信息不存在");
                    errorList.add(projectTesteeVisitFormException);
                    continue;
                }

                /**
                 * 计数器
                 */
                List<Long> formTableVariableList = new ArrayList<>();
                List<ProjectTesteeTableParam.TableRowData> tableRowDataList = new ArrayList<>();

                Set<Map.Entry<String, String>> entrySet = testeeVariableMap.entrySet();
                for (Map.Entry<String, String> entry : entrySet) {
                    String key = entry.getKey();
                    if("参与者姓名".equals(key) || "参与者编号".equals(key)){
                        continue;
                    }
                    ProjectTesteeVisitFormExceptionVo projectTesteeVisitFormExceptionVo = new ProjectTesteeVisitFormExceptionVo();
                    projectTesteeVisitFormExceptionVo.setVisitName(projectVisitConfig.getVisitName());
                    projectTesteeVisitFormExceptionVo.setTesteeCode(projectTesteeVisitFormException.getTesteeCode());
                    projectTesteeVisitFormExceptionVo.setRealName(projectTesteeVisitFormException.getRealName());
                    //根据key获取对应的变量
                    String variableKey = StringUtils.substringBetween(key, "[", "]");
                    String value = entry.getValue();
                    //如果是单选或者多选 判断是否符合options
                    int subLength = key.length() - variableKey.length();
                    projectTesteeVisitFormExceptionVo.setLabel(key.substring(0, subLength-2));
                    projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                    //区分普通表单和表格变量
                    if(!variableKey.startsWith("td_")){
                        ProjectTesteeResultParam projectTesteeResultParam = new ProjectTesteeResultParam();
                        projectTesteeResultParam.setOperator(userId);
                        TemplateFormDetailExcelImportVo templateFormDetailExcelImportVo = templateConfigService.getFormDetailByFormNameAndVariableName(projectId, visitName, "", variableKey);
                        if(templateFormDetailExcelImportVo == null) {
                            projectTesteeVisitFormExceptionVo.setMessage("字段信息" + variableKey + "不存在");
                            errorList.add(projectTesteeVisitFormExceptionVo);
                            testeeCount.add(projectTesteeVo.getId().toString());
                            continue;
                        }
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormDetailExcelImportVo.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailExcelImportVo.getType())) {
                            String options = templateFormDetailExcelImportVo.getOptions() == null ? "" : templateFormDetailExcelImportVo.getOptions();
                            if (!options.contains(value)) {
                                projectTesteeVisitFormExceptionVo.setMessage("字段录入数据" + value + "不符合CRF配置选项内容");
                                errorList.add(projectTesteeVisitFormExceptionVo);
                                testeeCount.add(projectTesteeVo.getId().toString());
                                continue;
                            }
                        }
                        boolean required = templateFormDetailExcelImportVo.getRequired() == null ? false : templateFormDetailExcelImportVo.getRequired();
                        //是否必填项
                        if(required && StringUtils.isEmpty(value)){
                            projectTesteeVisitFormExceptionVo.setMessage("当前字段规则为必填项");
                            projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                            projectTesteeVisitFormExceptionVo.setFieldValue(value);
                            errorList.add(projectTesteeVisitFormExceptionVo);
                            testeeCount.add(projectTesteeVo.getId().toString());
                            //continue;
                        }
                        //文本输入 验证字符长度
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT.equals(templateFormDetailExcelImportVo.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_TEXTAREA.equals(templateFormDetailExcelImportVo.getType())){
                            String expand = templateFormDetailExcelImportVo.getExpand();
                            if(StringUtils.isNotBlank(expand)){
                                Map<String, String> dataMap = JSON.parseObject(expand, new TypeReference<HashMap<String, String>>(){}.getType());
                                String textSize = dataMap.get("textSize");
                                if(StringUtils.isNotEmpty(textSize)){
                                    if(value.length() > Integer.parseInt(textSize)){
                                        projectTesteeVisitFormExceptionVo.setMessage("当前字段长度超出设置属性规则");
                                        projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                        projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                        errorList.add(projectTesteeVisitFormExceptionVo);
                                        testeeCount.add(projectTesteeVo.getId().toString());
                                        continue;
                                    }
                                }
                            }
                        }

                        //数值类型验证
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(templateFormDetailExcelImportVo.getType())){
                            if(StringUtils.isNotEmpty(value)){
                                if(!Validation.isNumeric(value)){
                                    projectTesteeVisitFormExceptionVo.setMessage("导入该字段类型规则：必须为数值格式");
                                    projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                    projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                    errorList.add(projectTesteeVisitFormExceptionVo);
                                    testeeCount.add(projectTesteeVo.getId().toString());
                                    continue;
                                }
                                if(StringUtils.isNotEmpty(templateFormDetailExcelImportVo.getExpand())){
                                    // {"region":"4","number":{"min":"2.98","max":"7.65"}}
                                    Map<String, String> dataMap = JSON.parseObject(templateFormDetailExcelImportVo.getExpand(), new TypeReference<HashMap<String, String>>(){}.getType());
                                    String number = dataMap.get("number");
                                    Map<String, String> numberValue = JSON.parseObject(number, new TypeReference<HashMap<String, String>>(){}.getType());
                                    String min = numberValue.get("min");
                                    String max = numberValue.get("max");
                                    if(StringUtils.isNotEmpty(min) && StringUtils.isNotEmpty(max)){
                                        if(Double.parseDouble(value) > Double.parseDouble(max) || Double.parseDouble(value) < Double.parseDouble(min)){
                                            projectTesteeVisitFormExceptionVo.setMessage("该数值格式不在设置的数值区间范围内");
                                            projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                            projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                            errorList.add(projectTesteeVisitFormExceptionVo);
                                            testeeCount.add(projectTesteeVo.getId().toString());
                                            continue;
                                        }
                                    }
                                }

                            }
                        }
                        //日期类型验证
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_DATE.equals(templateFormDetailExcelImportVo.getType())){
                            if(StringUtils.isNotEmpty(value)){
                                boolean validata1 = Validation.isValidDatePattern(value, DateUtil.DEFAULTFORMAT_01, "/");
                                boolean validata2 = Validation.isValidDatePattern(value, DateUtil.DEFAULTFORMAT, "-");
                                if(!validata1 && !validata2){
                                    projectTesteeVisitFormExceptionVo.setMessage("日期类型格式不符合规则，尝试将数据格式调整为yyyy-MM-dd格式进行导入");
                                    projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                    projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                    errorList.add(projectTesteeVisitFormExceptionVo);
                                    testeeCount.add(projectTesteeVo.getId().toString());
                                    continue;
                                }
                            }
                        }
                        // 标尺验证区间范围 {"max":"100","min":"1","stepSize":"1"}
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_SLIDER.equals(templateFormDetailExcelImportVo.getType())){
                            if(StringUtils.isNotEmpty(value)){
                                if(StringUtils.isNotEmpty(templateFormDetailExcelImportVo.getExpand())){
                                    Map<String, String> dataMap = JSON.parseObject(templateFormDetailExcelImportVo.getExpand(), new TypeReference<HashMap<String, String>>(){}.getType());
                                    String min = StringUtils.isEmpty(dataMap.get("min")) ? "0" : dataMap.get("min");
                                    String max = StringUtils.isEmpty(dataMap.get("max")) ? "0" : dataMap.get("max");
                                    if(Double.parseDouble(value) > Double.parseDouble(max) || Double.parseDouble(value) < Double.parseDouble(min)){
                                        projectTesteeVisitFormExceptionVo.setMessage("该标尺输入值不在设置的数值区间范围内");
                                        projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                        projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                        errorList.add(projectTesteeVisitFormExceptionVo);
                                        testeeCount.add(projectTesteeVo.getId().toString());
                                        continue;
                                    }
                                }
                            }
                        }

                        projectTesteeResultParam.setProjectId(templateFormDetailExcelImportVo.getProjectId());
                        projectTesteeResultParam.setVisitId(templateFormDetailExcelImportVo.getVisitId());
                        projectTesteeResultParam.setFormId(templateFormDetailExcelImportVo.getFormId());
                        projectTesteeResultParam.setTesteeId(projectTesteeVo.getId());

                        String visitId = templateFormDetailExcelImportVo.getVisitId().toString();
                        String formId = templateFormDetailExcelImportVo.getFormId().toString();
                        String formDetailId = templateFormDetailExcelImportVo.getFormDetailId().toString();
                        ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", visitId, formId, "", formDetailId, projectTesteeVo.getId().toString());
                        List<ProjectTesteeResultParam.TesteeFormResultValue> formResultValueList = new ArrayList<>();
                        ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue = new ProjectTesteeResultParam.TesteeFormResultValue();
                        if (projectTesteeResult != null) {
                            testeeFormResultValue.setTesteeResultId(projectTesteeResult.getId());
                        }
                        testeeFormResultValue.setFieldName(templateFormDetailExcelImportVo.getKey());
                        testeeFormResultValue.setFieldValue(value);
                        // testeeFormResultValue.setComplateStatus(StringUtils.isEmpty(value) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
                        testeeFormResultValue.setLabel(templateFormDetailExcelImportVo.getLabel());
                        testeeFormResultValue.setFormDetailId(templateFormDetailExcelImportVo.getFormDetailId());
                        testeeFormResultValue.setStatus(BusinessConfig.VALID_STATUS);
                        formResultValueList.add(testeeFormResultValue);

                        /**
                         * 保存普通表单信息
                         */
                        projectTesteeResultParam.setDataList(formResultValueList);
                        projectTesteeInfoService.saveTesteeVisitFormDetail(projectTesteeResultParam);
                        visitCount.add(projectTesteeResultParam.getVisitId().toString()+projectTesteeResultParam.getTesteeId().toString());
                        formCount.add(projectTesteeResultParam.getFormId().toString()+projectTesteeResultParam.getTesteeId().toString());
                    }else{
                        /************************************************ 表格处理逻辑 *********************************************************/
                        TemplateFormTableExcelImportVo templateFormTableExcelImportVo = templateConfigService.getFormTableInfoByFormNameAndVariableName(projectId, visitName, "", variableKey);
                        if(templateFormTableExcelImportVo == null) {
                            projectTesteeVisitFormExceptionVo.setMessage("字段信息" + variableKey + "不存在");
                            errorList.add(projectTesteeVisitFormExceptionVo);
                            testeeCount.add(projectTesteeVo.getId().toString());
                            continue;
                        }
                        if(CollectionUtil.isEmpty(formTableVariableList) || formTableVariableList.contains(templateFormTableExcelImportVo.getFormDetailId())){
                            formTableVariableList.add(templateFormTableExcelImportVo.getFormDetailId());
                        }
                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormTableExcelImportVo.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormTableExcelImportVo.getType())) {
                            String options = templateFormTableExcelImportVo.getExpand() == null ? "" : templateFormTableExcelImportVo.getExpand();
                            if (!options.contains(value)) {
                                projectTesteeVisitFormExceptionVo.setMessage("表格字段录入数据" + value + "不符合CRF配置选项内容");
                                errorList.add(projectTesteeVisitFormExceptionVo);
                                testeeCount.add(projectTesteeVo.getId().toString());
                                continue;
                            }
                        }
                        //表格数据验证
                        boolean required = templateFormTableExcelImportVo.getRequired() != null && templateFormTableExcelImportVo.getRequired();
                        //是否必填项
                        if(required && StringUtils.isEmpty(value)){
                            projectTesteeVisitFormExceptionVo.setMessage("当前字段规则为必填项");
                            projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                            projectTesteeVisitFormExceptionVo.setFieldValue(value);
                            errorList.add(projectTesteeVisitFormExceptionVo);
                            testeeCount.add(projectTesteeVo.getId().toString());
                            //continue;
                        }
                        //文本输入 验证字符长度
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_INPUT.equals(templateFormTableExcelImportVo.getType()) || BusinessConfig.PROJECT_VISIT_CRF_FORM_TEXTAREA.equals(templateFormTableExcelImportVo.getType())){
                            String expand = templateFormTableExcelImportVo.getExtData2();
                            if(StringUtils.isNotBlank(expand)){
                                Map<String, String> dataMap = JSON.parseObject(expand, new TypeReference<HashMap<String, String>>(){}.getType());
                                String textSize = dataMap.get("textSize");
                                if(StringUtils.isNotEmpty(textSize)){
                                    if(value.length() > Integer.parseInt(textSize)){
                                        projectTesteeVisitFormExceptionVo.setMessage("当前字段长度超出设置属性规则");
                                        projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                        projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                        errorList.add(projectTesteeVisitFormExceptionVo);
                                        testeeCount.add(projectTesteeVo.getId().toString());
                                        continue;
                                    }
                                }
                            }
                        }
                        //数值类型验证
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(templateFormTableExcelImportVo.getType())){
                            if(StringUtils.isNotEmpty(value)){
                                if(!Validation.isNumeric(value)){
                                    projectTesteeVisitFormExceptionVo.setMessage("导入该字段类型规则：必须为数值格式");
                                    projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                    projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                    errorList.add(projectTesteeVisitFormExceptionVo);
                                    testeeCount.add(projectTesteeVo.getId().toString());
                                    continue;
                                }
                                if(StringUtils.isNotEmpty(templateFormTableExcelImportVo.getExtData2())){
                                    // {"region":"4","number":{"min":"2.98","max":"7.65"}}
                                    Map<String, String> dataMap = JSON.parseObject(templateFormTableExcelImportVo.getExtData2(), new TypeReference<HashMap<String, String>>(){}.getType());
                                    String number = dataMap.get("number");
                                    Map<String, String> numberValue = JSON.parseObject(number, new TypeReference<HashMap<String, String>>(){}.getType());
                                    String min = numberValue.get("min");
                                    String max = numberValue.get("max");
                                    if(StringUtils.isNotEmpty(min) && StringUtils.isNotEmpty(max)){
                                        if(Double.parseDouble(value) > Double.parseDouble(max) || Double.parseDouble(value) < Double.parseDouble(min)){
                                            projectTesteeVisitFormExceptionVo.setMessage("该数值格式不在设置的数值区间范围内");
                                            projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                            projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                            errorList.add(projectTesteeVisitFormExceptionVo);
                                            testeeCount.add(projectTesteeVo.getId().toString());
                                            continue;
                                        }
                                    }
                                }

                            }
                        }
                        //日期类型验证
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_DATE.equals(templateFormTableExcelImportVo.getType())){
                            //if(StringUtils.isNotEmpty(value)){
                                boolean validata1 = Validation.isValidDatePattern(value, DateUtil.DEFAULTFORMAT_01, "/");
                                boolean validata2 = Validation.isValidDatePattern(value, DateUtil.DEFAULTFORMAT, "-");
                                if(!validata1 && !validata2){
                                    projectTesteeVisitFormExceptionVo.setMessage("日期类型格式不符合，尝试将数据格式调整为yyyy-MM-dd格式进行导入");
                                    projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                    projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                    errorList.add(projectTesteeVisitFormExceptionVo);
                                    testeeCount.add(projectTesteeVo.getId().toString());
                                    continue;
                                }
                            //}
                        }
                        // 标尺验证区间范围 {"max":"100","min":"1","stepSize":"1"}
                        if(BusinessConfig.PROJECT_VISIT_CRF_FORM_SLIDER.equals(templateFormTableExcelImportVo.getType())){
                            if(StringUtils.isNotEmpty(value)){
                                if(StringUtils.isNotEmpty(templateFormTableExcelImportVo.getExtData2())){
                                    Map<String, String> dataMap = JSON.parseObject(templateFormTableExcelImportVo.getExtData2(), new TypeReference<HashMap<String, String>>(){}.getType());
                                    String min = StringUtils.isEmpty(dataMap.get("min")) ? "0" : dataMap.get("min");
                                    String max = StringUtils.isEmpty(dataMap.get("max")) ? "0" : dataMap.get("max");
                                    if(Double.parseDouble(value) > Double.parseDouble(max) || Double.parseDouble(value) < Double.parseDouble(min)){
                                        projectTesteeVisitFormExceptionVo.setMessage("该标尺输入值不在设置的数值区间范围内");
                                        projectTesteeVisitFormExceptionVo.setFieldName(variableKey);
                                        projectTesteeVisitFormExceptionVo.setFieldValue(value);
                                        errorList.add(projectTesteeVisitFormExceptionVo);
                                        testeeCount.add(projectTesteeVo.getId().toString());
                                        continue;
                                    }
                                }
                            }
                        }

                        //查询参与者表格行记录是否存在
                        ProjectTesteeTableParam projectTesteeTableParam = new ProjectTesteeTableParam();
                        projectTesteeTableParam.setCreateUserId(userId);
                        projectTesteeTableParam.setProjectId(Long.parseLong(projectId));
                        projectTesteeTableParam.setVisitId(projectVisitConfig.getId());
                        projectTesteeTableParam.setFormId(templateFormTableExcelImportVo.getFormId());
                        projectTesteeTableParam.setFormDetailId(templateFormTableExcelImportVo.getFormDetailId());
                        projectTesteeTableParam.setTesteeId(projectTesteeVo.getId());

                        //将同一个表格的单元格组装成一条记录
                        if(formTableVariableList.contains(templateFormTableExcelImportVo.getFormDetailId())){
                            ProjectTesteeTableParam.TableRowData tableRowData = new ProjectTesteeTableParam.TableRowData();
                            tableRowData.setFormTableId(templateFormTableExcelImportVo.getFormTableId());
                            tableRowData.setLabel(templateFormTableExcelImportVo.getLabel());
                            tableRowData.setFieldName(templateFormTableExcelImportVo.getKey());
                            tableRowData.setStatus(BusinessConfig.VALID_STATUS);
                            tableRowData.setFieldValue(value);
                            tableRowData.setComplateStatus(StringUtils.isEmpty(value) ? FormVariableComplateStatus.FORM_VAR_NO_FILL.getCode() : FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
                            tableRowDataList.add(tableRowData);
                        }

                        List<TemplateTableVo> projectTesteeTableRowHead = projectTesteeTableService.getProjectTesteeTableRowHead(templateFormTableExcelImportVo.getFormId().toString(), templateFormTableExcelImportVo.getFormDetailId().toString(), false);
                        if(projectTesteeTableRowHead.size() == formTableVariableList.size()){
                            boolean nullValue = checkProjectTesteeTableResult(projectTesteeTableParam, tableRowDataList);
                            if(!nullValue){
                                if(projectTesteeTableParam.getComplateCount() == projectTesteeTableParam.getRowcount()){
                                    projectTesteeTableParam.setTableComplateStatus(FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode());
                                }else{
                                    projectTesteeTableParam.setTableComplateStatus(FormVariableComplateStatus.FORM_VAR_FILL.getCode());
                                }
                                projectTesteeTableParam.setTableRowDataList(tableRowDataList);
                                projectTesteeInfoService.saveProjectTesteeTableRowRecord(projectTesteeTableParam);
                                testeeCount.add(projectTesteeTableParam.getTesteeId().toString());
                                visitCount.add(projectTesteeTableParam.getVisitId().toString()+projectTesteeTableParam.getTesteeId().toString());
                                formCount.add(projectTesteeTableParam.getFormId().toString()+projectTesteeTableParam.getTesteeId().toString());
                            }
                            tableRowDataList.clear();
                            formTableVariableList.clear();
                        }
                    }
                }
                //启用线程计算普通表单录入情况
                //AsyncManager.me().execute(AsyncFactory.updateProjectTesteeFormProcess(projectId, projectVisitConfig.getId().toString(), projectTesteeVo.getId().toString(), userId));
            }
        }

        testeeImportResultVo.setTesteeErrorRowCount(testeeCount.size());
        testeeImportResultVo.setVisitCount(visitCount.size());
        testeeImportResultVo.setFormCount(formCount.size());
        if (errorList.size() > 0) {
            testeeImportResultVo.setMessage(BusinessConfig.PROJECT_TESTEE_VISIT_DATA_EXCEPTION_MESSAGE);
        }
        return testeeImportResultVo;
    }

    @Override
    public CustomResult updateProjectTesteeOwnerFormsProcess(String projectId, String projectOrgId, String planId, String operator) {
        CustomResult customResult = new CustomResult();
        List<ProjectVisitConfig> projectVisitList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, planId, "");
        for (ProjectVisitConfig projectVisitConfig : projectVisitList) {
            List<ProjectTesteeVo> projectTesteeList = projectTesteeInfoService.getProjectTesteeAnalysisListByProjectId(projectId, planId, "");
            for (ProjectTesteeVo projectTesteeVo: projectTesteeList) {
                String testeeId = projectTesteeVo.getId().toString();
                projectTesteeInfoService.updateProjectTesteeFormProcess(projectId, projectOrgId, planId, projectVisitConfig.getId().toString(), "", "", testeeId, "", "", operator, "", "");
            }
        }
        return customResult;
    }


    @Override
    public CommonResult deleteProjectTesteeExportRecordById(String id) {
        int i= projectTesteeExportMapper.rmById(id);
        if (i>0){
            return CommonResult.success("删除成功");
        }
        return CommonResult.failed("删除失败");
    }

    @Override
    public CustomResult saveProjectExportCollectRecord(ProjectSearchCollectParam projectSearchCollectParam) {
        CustomResult customResult = new CustomResult();
        return customResult;
    }

    @Override
    public CommonPage<ProjectSearchCollectVo> getProjectTesteeExportCollectListForPage(String projectId, String userId, Integer pageNum, Integer pageSize) {
        return null;
    }

    @Override
    public CustomResult deleteProjectExportCollectRecord(String id) {
        CustomResult customResult = new CustomResult();
        return customResult;
    }

    private boolean checkProjectTesteeTableResult(ProjectTesteeTableParam projectTesteeTableParam, List<ProjectTesteeTableParam.TableRowData> tableRowDataList) {
        // 1-如果录入表格数据都为空，则不进行导入
        // 2-如果录入的行记录单元格数据和之前的数据行记录相同，则认为是重复数据，不进行导入
        String projectId = projectTesteeTableParam.getProjectId().toString();
        String visitId = projectTesteeTableParam.getVisitId().toString();
        String formId = projectTesteeTableParam.getFormId().toString();
        String formDetailId = projectTesteeTableParam.getFormDetailId().toString();
        String testeeId = projectTesteeTableParam.getTesteeId().toString();
        List<String> complateCountList = new ArrayList<>();
        List<ProjectTesteeTableRowDataVo> projectTesteeTableRowDataList = projectTesteeTableService.getProjectTesteeTableRowDataList(projectId, "", visitId, formId, formDetailId, testeeId);
        projectTesteeTableParam.setRowcount(projectTesteeTableRowDataList.size());
        for (ProjectTesteeTableRowDataVo projectTesteeTableRowDataVo : projectTesteeTableRowDataList) {
            if(projectTesteeTableRowDataVo.getComplateStatus().equals(FormVariableComplateStatus.FORM_VAR_FILL_SUCCESS.getCode())){
                complateCountList.add(projectTesteeTableRowDataVo.getComplateStatus());
            }
            String content = "";
            StringBuilder contentRow = new StringBuilder();
            List<ProjectTesteeTableRowDataVo.TesteeTableRow> rowList = projectTesteeTableRowDataVo.getRowList();
            for (ProjectTesteeTableRowDataVo.TesteeTableRow testeeTableRow : rowList) {
                contentRow.append(StringUtils.isEmpty(testeeTableRow.getValue()) ? "" : testeeTableRow.getValue());
            }
            content = contentRow.toString();

            StringBuilder targetcontentRow = new StringBuilder();
            for (ProjectTesteeTableParam.TableRowData tableRowData : tableRowDataList) {
                String value = tableRowData.getFieldValue() == null ? "" : tableRowData.getFieldValue();
                targetcontentRow.append(value);
            }
            projectTesteeTableParam.setComplateCount(complateCountList.size());
            if(targetcontentRow.toString().equals(content)){
                return true;
            }
        }

        List<String> contentRowData = new ArrayList<>();
        StringBuilder dataList = new StringBuilder();
        if(CollectionUtil.isNotEmpty(tableRowDataList)){
            for (ProjectTesteeTableParam.TableRowData tableRowData : tableRowDataList) {
                String value = tableRowData.getFieldValue() == null ? "" : tableRowData.getFieldValue();
                if(StringUtils.isNotEmpty(value)){
                    contentRowData.add(value);
                }
                dataList.append(value);
            }
        }
        projectTesteeTableParam.setTableRowCellCount(contentRowData.size());
        return StringUtils.isEmpty(dataList.toString());
    }
}

