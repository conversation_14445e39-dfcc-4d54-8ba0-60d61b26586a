package com.haoys.user.service.impl;

import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.system.SystemTenantParam;
import com.haoys.user.model.SystemFileInfo;
import com.haoys.user.model.SystemTenant;
import com.haoys.user.service.SystemFileInfoService;
import com.haoys.user.service.SystemTenantService;
import com.haoys.user.service.TenantInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Service
public class TenantInformationServiceImpl implements TenantInformationService {

    @Autowired
    private SystemTenantService systemTenantService;
    @Autowired
    private SystemFileInfoService systemFileInfoService;

    @Override
    public Map<String, Object> getTenantInfoById(String systemTenantId) {
        Map<String, Object> dataMap = new HashMap<>();
        SystemTenant systemTenant = systemTenantService.selectByPrimaryKey(Long.valueOf(systemTenantId));
        dataMap.put("systemFileInfo", systemFileInfoService.selectSystemFileInfoById(systemTenant.getFileId() == null ? 0L : systemTenant.getFileId()));
        dataMap.put("systemTenant", systemTenant);
        return dataMap;
    }

    @Override
    public void updateSystemTenantInfo(String userId, SystemTenantParam systemTenantParam) {
        SystemTenant systemTenant = systemTenantService.selectByPrimaryKey(systemTenantParam.getId());
        if(systemTenant == null){return;}
        BeanUtils.copyProperties(systemTenantParam, systemTenant);
        if(StringUtils.isNotEmpty(systemTenantParam.getFileNameKey())){
            SystemFileInfo systemFileInfo = new SystemFileInfo();
            systemFileInfo.setId(SnowflakeIdWorker.getUuid());
            systemFileInfo.setFileName(systemTenantParam.getNewFileName());
            systemFileInfo.setOriginalName(systemTenantParam.getOriginalFilename());
            systemFileInfo.setUploadPath(systemTenantParam.getFileName());
            systemFileInfo.setFileUrl(systemTenantParam.getUrl());
            systemFileInfo.setResourceType("logo");
            systemFileInfo.setStatus(BusinessConfig.VALID_STATUS);
            systemFileInfo.setCreateTime(new Date());
            systemFileInfo.setCreateUserId(userId);
            systemFileInfo.setTenantId(String.valueOf(SecurityUtils.getSystemTenantId()));
            systemFileInfo.setPlatformId(String.valueOf(SecurityUtils.getSystemTenantId()));
            systemFileInfoService.insert(systemFileInfo);
            systemTenant.setFileId(systemFileInfo.getId());
        }
        //修改企业相关信息
        systemTenant.setUpdateUserId(userId);
        systemTenant.setUpdateTime(new Date());
        systemTenantService.updateByPrimaryKeySelective(systemTenant);
    }

}
