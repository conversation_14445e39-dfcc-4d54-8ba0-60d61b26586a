package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectTesteeConfigParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeConfigVo;

public interface ProjectTesteeConfigService {
    
    CustomResult saveProjectTesteeConfig(ProjectTesteeConfigParam projectTesteeConfigParam);

    ProjectTesteeConfigVo getProjectTesteeConfig(String projectId);
}
