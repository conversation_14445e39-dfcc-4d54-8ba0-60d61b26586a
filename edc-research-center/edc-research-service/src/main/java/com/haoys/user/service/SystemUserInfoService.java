package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.core.domain.entity.BaseSystemUser;
import com.haoys.user.domain.param.system.ActiveUserParam;
import com.haoys.user.domain.param.system.PatientUserParam;
import com.haoys.user.domain.param.system.RegisterUserParam;
import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.param.system.UpdateSystemUserPasswordParam;
import com.haoys.user.domain.vo.project.SystemUserExtendVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.SystemUserInfoExample;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 后台用户管理Service
 */
public interface SystemUserInfoService {
    /**
     * 根据用户账号获取后台管理员 accountName 账号名称 用户名、手机号、邮箱
     * @return
     */
    SystemUserInfoVo getSystemUserInfoByAccountName(String username);

    /**
     * 根据用户名查询用户基本信息
     * @param userName
     * @return
     */
    SystemUserInfo getUserBaseInfoByUserName(String userName);

    /**
     * 添加用户
     */
    CustomResult saveSystemUserInfo(SystemUserInfoParam systemUserInfoParam);

    /**
     * 根据用户id获取用户基本信息
     * @return
     */
    SystemUserInfo getUserBaseInfo(Long userId);

    /**
     * 根据用户名或昵称分页查询用户
     * @return
     */
    List<SystemUserInfo> getSysUserList(String keyword, Integer pageSize, Integer pageNum);

    /**
     * 修改系统用户信息
     */
    CustomResult updateSystemUserInfo(SystemUserInfoParam systemUserInfoParam);

    /**
     * 删除指定用户
     */
    CustomResult deleteSystemUserInfo(String userId);

    /**
     * 根据手机号查询用户信息
     * @param mobile
     * @return
     */
    SystemUserInfoExtendVo getUserInfoByMobile(String mobile);
    /**
     * 根据用户名查询用户信息
     * @param username
     * @return
     */
    SystemUserInfoExtendVo getUserInfoByUserName(String username);

    /**
     * 根据用户id查询用户信息
     * @param userId
     * @return
     */
    SystemUserInfoExtendVo getSystemUserInfoByUserId(String userId);

    /**
     * 查询用户列表
     * @param projectId
     * @param ifPrimary
     * @param username
     * @param realName
     * @param orgId
     * @param mobile
     * @param roleCode
     * @return
     */
    List<SystemUserExtendVo> getProjectUserList(String projectId, String ifPrimary, String username, String realName, String orgId, String mobile, String roleCode);


    /**
     * 查询系统用户分页列表
     * @param username      用户名
     * @param realName      姓名
     * @param mobile        手机号
     * @param orgId         中心id
     * @param pageNum       当前页
     * @param pageSize      每页显示条数
     * @return
     */
    CommonPage<SystemUserExtendVo> getSystemUserListForPage(String username, String realName, String mobile, String orgId, Integer pageNum, Integer pageSize);


    /**
     * 校验手机号码是否唯一
     * @param user
     * @return 结果
     */
    String checkPhoneUnique(BaseSystemUser user);

    /**
     * 修改用户信息
     * @param systemUserInfo
     * @return
     */
    int updateSystemUser(SystemUserInfo systemUserInfo);

    /**
     * 添加用户信息
     * @param user
     * @return
     */
    int insertSystemUser(SystemUserInfo user);

    /**
     * 修改用户状态
     * @param puId
     * @param status
     * @param projectId
     * @return
     */
    CommonResult updateUserStatus(String puId,String projectId, Integer status);

    /**
     * 修改用户密码
     * @param updatePasswordParam
     * @return
     */
    String updateUserPassword(UpdateSystemUserPasswordParam updatePasswordParam);

    /**
     * 根据手机号查询
     * @param mobile
     * @return
     */
    SystemUserInfo getSystemUserInfoByMobile(String mobile);

    /**
     * 患者端注册
     * @param patientUserParam
     * @return
     */
    CustomResult savePatientUser(PatientUserParam patientUserParam);

    /**
     * 邀请用户-激活用户
     * @param activeUserParam 用户信息
     * @return 激活结果
     */
    CommonResult updateActiveSystemUserInfo(ActiveUserParam activeUserParam);

    /**
     * 用户注册
     * @param registerUserParam
     * @return
     */
    CommonResult registerSystemUserInfo(RegisterUserParam registerUserParam);
    
    CommonResult registerExternalSystemUserInfo(String ticket, String loginSource);
    
    /**
     * 根据邮箱和
     * @param username
     * @return
     */
    SystemUserInfo getSystemUserByAccountNameOrEmail(String username);

    /**
     * 条件查询
     * @param example
     * @return
     */
    List<SystemUserInfo> selectByExample(SystemUserInfoExample example);

    int selectCountUser(Long departmentId, String platformId, String tenantId);

    SystemUserInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemUserInfo record);

    /**
     * 统计外部部门人数
     * @return
     */
    int selectCountOutUser(String tenantId, String platformId, String userType);

    List<SystemUserInfo> getSystemUserListForPage(SystemUserInfoParam systemUserInfoParam);

    List<SystemUserInfo> selectJoinSystemTenantUser(SystemUserInfoParam param);
    
    CommonResult registerResearchProjectUserUserInfo(String mobile, String loginSource);
    
    CustomResult saveBatchSystemUser(MultipartFile file, String enterprise, boolean projectAuthority) throws Exception;
    
}
