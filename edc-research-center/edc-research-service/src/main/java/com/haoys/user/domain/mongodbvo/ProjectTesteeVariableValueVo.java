package com.haoys.user.domain.mongodbvo;


import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Document(collection = "ProjectTesteeVariableValueVo")
public class ProjectTesteeVariableValueVo implements Serializable {

    private String testee_id;
    private String testee_code;
    private String real_name;
    private String gender;
    private Date birthday;
    private String owner_org_id;
    private String owner_org_name;
    private String owner_doctor_id;
    private String owner_doctor_name;
    private String mobile;
    private Date join_group_time;

    private String project_id;
    private String visit_id;
    private String form_id;
    private String group_id;

    private List<FormVariableResultInfo> variable_info = null;

    @Data
    public static class FormVariableResultInfo {

        private String variable_id;
        private String variable_name;
        private String variable_record_id;
        private Date variable_record_time;

        private String field_name;
        private String field_value;

    }

}
