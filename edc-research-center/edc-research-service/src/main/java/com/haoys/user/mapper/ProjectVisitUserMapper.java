package com.haoys.user.mapper;

import com.haoys.user.domain.param.testee.ProjectTesteeStatistVo;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.ProjectVisitUserExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectVisitUserMapper {
    long countByExample(ProjectVisitUserExample example);

    int deleteByExample(ProjectVisitUserExample example);

    int insert(ProjectVisitUser record);

    int insertSelective(ProjectVisitUser record);

    List<ProjectVisitUser> selectByExample(ProjectVisitUserExample example);

    int updateByExampleSelective(@Param("record") ProjectVisitUser record, @Param("example") ProjectVisitUserExample example);

    int updateByExample(@Param("record") ProjectVisitUser record, @Param("example") ProjectVisitUserExample example);

    ProjectTesteeStatistVo testeeStatist(@Param("projectId") Long projectId, @Param("orgId") Long orgId);
    /**
     * 查询参与者某一日的数量
     * @param projectId 项目id
     * @param orgId 研究中心id
     * @return 统计结果
     */
    int testeeStatistByDate(@Param("projectId") Long projectId, @Param("orgId") Long orgId, @Param("createTime") String createTime);

    ProjectVisitUser getProjectVisitUserInfo(String projectId, String ownerOrgId, String testeeId);

    String getMaxtesteeCode(@Param("projectId") Long projectId, @Param("orgId") Long orgId, @Param("prefix") String prefix);

    ProjectVisitUser getMobileProjectVisitUser(String projectId, String ownerOrgId, String testeeId);
    
    ProjectVisitUser getProjectTesteeUserByUserId(String projectId, String userId, String tenantId, String platformId);
    
    ProjectVisitUser getProjectDeleteVisitUserInfo(String projectId, String ownerOrgId, String testeeId);
    
    String getMaxTesteeCodeForBoRui(Long projectId);
}
