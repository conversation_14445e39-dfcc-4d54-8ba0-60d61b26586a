package com.haoys.user.mapper;

import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectResearchersInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectResearchersInfoMapper {
    long countByExample(ProjectResearchersInfoExample example);

    int deleteByExample(ProjectResearchersInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectResearchersInfo record);

    int insertSelective(ProjectResearchersInfo record);

    List<ProjectResearchersInfo> selectByExample(ProjectResearchersInfoExample example);

    ProjectResearchersInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectResearchersInfo record, @Param("example") ProjectResearchersInfoExample example);

    int updateByExample(@Param("record") ProjectResearchersInfo record, @Param("example") ProjectResearchersInfoExample example);

    int updateByPrimaryKeySelective(ProjectResearchersInfo record);

    int updateByPrimaryKey(ProjectResearchersInfo record);
    
    ProjectResearchersInfo getProjectResearchersInfoByCreateUser(String projectId, String userId);
}