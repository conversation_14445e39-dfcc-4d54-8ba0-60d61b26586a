package com.haoys.user.mapper;

import com.haoys.user.domain.dto.DictExportDto;
import com.haoys.user.domain.dto.DictItemExportDto;
import com.haoys.user.domain.param.dict.ProjectDictParam;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.ProjectDictionary;
import com.haoys.user.model.ProjectDictionaryExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectDictionaryMapper {
    long countByExample(ProjectDictionaryExample example);

    int deleteByExample(ProjectDictionaryExample example);

    int deleteByPrimaryKey(String id);

    int insert(ProjectDictionary record);

    int insertSelective(ProjectDictionary record);

    List<ProjectDictionary> selectByExample(ProjectDictionaryExample example);

    ProjectDictionary selectByPrimaryKey(String id);

    int updateByExampleSelective(@Param("record") ProjectDictionary record, @Param("example") ProjectDictionaryExample example);

    int updateByExample(@Param("record") ProjectDictionary record, @Param("example") ProjectDictionaryExample example);

    int updateByPrimaryKeySelective(ProjectDictionary record);

    int updateByPrimaryKey(ProjectDictionary record);

    /**
     * 获取字典列表
     * @param param
     * @return
     */
    List<ProjectDictionary> selectProjectDictionaryList(ProjectDictParam param);

    /**
     * 获取字典列表 不分页
     * @return
     */
    List<Dictionary> getDictList(@Param("projectId") String projectId,@Param("parentId")String parentId,@Param("dicType")String dicType,@Param("status")String status);

    /**
     * 导出字典查询方法
     * @param param 参数
     * @return
     */
    List<DictExportDto> selectExportList(ProjectDictParam param);

    /**
     * 导出字典明细查询方法
     * @param projectDictParam 参数
     * @return
     */
    List<DictItemExportDto> exportDictItem(ProjectDictParam projectDictParam);
    
    ProjectDictionary getProjectDictionaryByParentIdAndCode(String projectId, String parentId, String code);
}
