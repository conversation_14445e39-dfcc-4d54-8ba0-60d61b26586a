package com.haoys.user.service.impl;

import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.mapper.ProjectTesteeExportMapper;
import com.haoys.user.model.ProjectTesteeExport;
import com.haoys.user.service.ProjectTesteeExportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProjectTesteeExportServiceImpl implements ProjectTesteeExportService {
    @Autowired
    private ProjectTesteeExportMapper projectTesteeExportMapper;


    @Autowired
    private  RedisTemplateService redisTemplateService;
    @Override
    public ProjectTesteeExport getById(String id) {
        return projectTesteeExportMapper.selectByPrimaryKey(Long.parseLong(id));
    }

    @Override
    public int removeFile(String exId) {
        ProjectTesteeExport export = projectTesteeExportMapper.selectByPrimaryKey(Long.parseLong(exId));
        redisTemplateService.lRemove(RedisKeyContants.USER_DOWN+export.getOperator(), 0, exId);
        export.setStatus(BusinessConfig.NO_VALID_STATUS);
        return projectTesteeExportMapper.rmById(exId);
    }
}
