package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.model.ProjectFormAudit;

import java.util.List;

/**
 * 流程配置-创建计划服务类
 */
public interface ProjectFormAuditService {

    int create(ProjectFormAudit audit);

    int update(ProjectFormAudit audit);

    int delete(Long auditId);

    List<ProjectFormAudit> getProjectFormAuditList(Long projectId,Long visitId, Long formId, Long testeeId);

    Boolean isHavAudit(Long projectId);

    ProjectFormAudit getProjectFormAuditInfo(Long projectId,Long visitId, Long formId, Long testeeId);

    /**
     * 修改审核状态
     * @param auditId  审核记录id
     * @param auditStatus 审核状态
     * @return
     */
    CustomResult updateAuditForm(String auditId, String auditStatus);
}
