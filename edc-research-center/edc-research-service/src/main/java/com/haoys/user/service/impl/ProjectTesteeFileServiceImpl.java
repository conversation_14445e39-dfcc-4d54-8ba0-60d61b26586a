package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.file.FileUtils;
import com.haoys.user.common.file.ImageCompressionUtils;
import com.haoys.user.common.ocr.BaiduAIService;
import com.haoys.user.common.ocr.ImageUtil;
import com.haoys.user.common.ocr.TencentAIService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.NeedMergeFileParam;
import com.haoys.user.domain.param.file.UploadProjectFileParam;
import com.haoys.user.domain.param.file.SaveUploadProjectFileParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.domain.vo.project.ProjectTesteeFileVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.enums.MedicalTypeEnum;
import com.haoys.user.enums.system.OssTypeEnum;
import com.haoys.user.mapper.ProjectTesteeFileMapper;
import com.haoys.user.mapper.ProjectTesteeOcrMapper;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.ProjectTesteeFile;
import com.haoys.user.model.ProjectTesteeFileExample;
import com.haoys.user.model.ProjectTesteeOcr;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.service.AsyncFileUploadService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.user.storge.cloud.OssStorageFactory;
import com.haoys.user.storge.cloud.localStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectTesteeFileServiceImpl extends BaseService implements ProjectTesteeFileService {

    private final OssStorageConfig storageConfig;
    private final localStorageService torageService;
    private final AsyncFileUploadService asyncFileUploadService;
    private final BaiduAIService baiduAIService;
    private final TencentAIService tencentAIService;
    private final TemplateConfigService templateConfigService;
    private final ProjectTesteeFileMapper projectTesteeFileMapper;
    private final ProjectTesteeOcrMapper projectTesteeOcrMapper;
    private final FlowPlanService flowPlanService;

    private static final String TEMPLATE_FORM_CONFIG_PATH = "crf-form";
    private static final String PROJECT_FILE_PATH = "project-file";

    @Override
    public List<UploadFileResultVo> saveUploadProjectFile(MultipartFile[] multipartFiles, String createUserId, String projectId, String visitId, String formId, String resourceId,
                                                          String tableId, String rowNumber, String testeeId, String collect, String taskDate, String openOCR, String batchUpload,
                                                          String medicalType, String templateId, String prePageNo, String groupId, String groupName, String imageType,
                                                          String extendStruct, String generalAccurate, String mergeImage, String mergeMethod, String ifMontage,
                                                          String needMergeFileParam, String batchOpenOcr)
                                                    throws IOException {
        // 构建参数对象
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId(createUserId)
                .projectId(projectId)
                .visitId(visitId)
                .formId(formId)
                .resourceId(resourceId)
                .tableId(tableId)
                .rowNumber(rowNumber)
                .testeeId(testeeId)
                .collect(collect)
                .taskDate(taskDate)
                .openOCR(openOCR)
                .batchUpload(batchUpload)
                .medicalType(medicalType)
                .templateId(templateId)
                .prePageNo(prePageNo)
                .groupId(groupId)
                .groupName(groupName)
                .imageType(imageType)
                .extendStruct(extendStruct)
                .generalAccurate(generalAccurate)
                .mergeImage(mergeImage)
                .mergeMethod(mergeMethod)
                .ifMontage(ifMontage)
                .needMergeFileParam(needMergeFileParam)
                .batchOpenOcr(batchOpenOcr);

        return saveUploadProjectFileWithParam(multipartFiles, param);
    }

    /**
     * 重构后的文件上传方法，使用参数对象
     *
     * @param multipartFiles 上传的文件数组
     * @param param 封装的参数对象
     * @return 上传结果列表
     * @throws IOException IO异常
     */
    public List<UploadFileResultVo> saveUploadProjectFileWithParam(MultipartFile[] multipartFiles, SaveUploadProjectFileParam param) throws IOException {

        log.info("needMergeFileParam:{} ", JSON.toJSONString(param.getNeedMergeFileParam()));
        List<UploadFileResultVo> dataList = new ArrayList<>();

        if (multipartFiles == null || multipartFiles.length == 0) {
            return dataList;
        }

        // 准备异步上传的数据
        String[] paths = new String[multipartFiles.length];

        for (int i = 0; i < multipartFiles.length; i++) {
            MultipartFile file = multipartFiles[i];

            // 生成文件存储路径
            String path = generateFilePath(param.getProjectId(), param.getMedicalType(), file.getOriginalFilename());
            paths[i] = path;

            // 上传到主存储（云存储或本地存储）
            String url = uploadToMainStorage(file, path);

            // 创建上传结果对象
            UploadFileResultVo uploadFileResultVo = createUploadFileResult(file, path, url);

            // 保存文件记录到数据库
            ProjectTesteeFile record = createProjectTesteeFileRecord(file, path, url, param);
            projectTesteeFileMapper.insert(record);

            uploadFileResultVo.setFileId(record.getId().toString());

            // 处理文件合并参数
            handleMergeFileParam(param.getNeedMergeFileParam(), record);

            // 处理OCR识别
            if (record.getOcrFlag()) {
                handleOcrProcessing(record, param, uploadFileResultVo);
            }

            dataList.add(uploadFileResultVo);
        }

        // 异步上传到本地存储（仅在使用云存储时）
        asyncFileUploadService.batchUploadToLocalStorageAsync(multipartFiles, paths);

        return dataList;
    }

    /**
     * 生成文件存储路径
     *
     * @param projectId 项目ID
     * @param medicalType 医疗类型
     * @param originalFilename 原始文件名
     * @return 存储路径
     */
    private String generateFilePath(String projectId, String medicalType, String originalFilename) {
        return storageConfig.getRootPath() + "/" + projectId +
                CharUtil.SLASH +
                medicalType +
                CharUtil.SLASH +
                IdUtil.simpleUUID() +
                CharUtil.DOT +
                FileUtil.extName(originalFilename);
    }

    /**
     * 上传文件到主存储
     *
     * @param file 文件
     * @param path 存储路径
     * @return 访问URL
     * @throws IOException IO异常
     */
    private String uploadToMainStorage(MultipartFile file, String path) throws IOException {
        InputStream uploadInputStream;
        try {
            if (ImageCompressionUtils.isImageFile(file)) {
                log.info("检测到图片文件，开始压缩: {}", file.getOriginalFilename());
                uploadInputStream = ImageCompressionUtils.createCompressedInputStream(file);
            } else {
                uploadInputStream = file.getInputStream();
            }
        } catch (Exception e) {
            log.error("图片压缩失败，使用原文件上传: {}", file.getOriginalFilename(), e);
            uploadInputStream = file.getInputStream();
        }

        return Objects.requireNonNull(OssStorageFactory.build()).upload(uploadInputStream, path);
    }

    /**
     * 创建上传文件结果对象
     *
     * @param file 原始文件
     * @param path 存储路径
     * @param url 访问URL
     * @return 上传结果对象
     */
    private UploadFileResultVo createUploadFileResult(MultipartFile file, String path, String url) {
        UploadFileResultVo uploadFileResultVo = new UploadFileResultVo();
        uploadFileResultVo.setFileName(storageConfig.getUploadFolder() + path);
        uploadFileResultVo.setNewFileName(FileUtils.getName(path));
        uploadFileResultVo.setOriginalFilename(file.getOriginalFilename());
        uploadFileResultVo.setFileNameKey(FileUtils.getNameNotSuffix(path));
        uploadFileResultVo.setUrl(url);
        return uploadFileResultVo;
    }

    /**
     * 创建项目测试文件记录
     *
     * @param file 原始文件
     * @param path 存储路径
     * @param url 访问URL
     * @param param 参数对象
     * @return 文件记录
     */
    private ProjectTesteeFile createProjectTesteeFileRecord(MultipartFile file, String path, String url, SaveUploadProjectFileParam param) {
        ProjectTesteeFile record = new ProjectTesteeFile();
        record.setId(SnowflakeIdWorker.getUuid());
        record.setProjectId(Long.parseLong(param.getProjectId()));
        record.setCreateUser(param.getCreateUserId());
        record.setCreateTime(new Date());

        if (StringUtils.isNotEmpty(param.getTaskDate())) {
            record.setTaskDate(DateUtil.getAutoParseDate(param.getTaskDate()));
        }
        record.setStatus(BusinessConfig.VALID_STATUS);

        String medicalType = param.getMedicalType();
        if (StringUtils.isEmpty(medicalType)) {
            medicalType = BusinessConfig.PROJECT_TESTEE_FILE_PATH_05;
        }

        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(param.getProjectId());
        if (flowPlanInfo != null) {
            record.setPlanId(flowPlanInfo.getId());
        }

        // 设置各种ID字段
        setRecordIds(record, param);

        // 设置文件相关字段
        record.setTesteeId(Long.parseLong(param.getTesteeId()));
        record.setOriginalName(file.getOriginalFilename());
        record.setFileName(FileUtils.getName(path));
        record.setFileUrl(url);
        record.setUploadPath(storageConfig.getUploadFolder() + path);
        record.setFileExt(FileUtil.getSuffix(FileUtils.getName(path)));
        record.setOcrFlag("1".equals(param.getOpenOCR()));
        record.setBatchUpload("1".equals(param.getBatchUpload()));
        record.setIfMontage("1".equals(param.getIfMontage()));
        record.setBatchOpenOcr("1".equals(param.getBatchOpenOcr()));
        record.setResourceType(medicalType);
        record.setTenantId(SecurityUtils.getSystemTenantId());
        record.setPlatformId(SecurityUtils.getSystemPlatformId());

        return record;
    }

    /**
     * 设置记录的各种ID字段
     *
     * @param record 文件记录
     * @param param 参数对象
     */
    private void setRecordIds(ProjectTesteeFile record, SaveUploadProjectFileParam param) {
        if (StringUtils.isNotEmpty(param.getVisitId())) {
            record.setVisitId(Long.parseLong(param.getVisitId()));
        }
        if (StringUtils.isNotEmpty(param.getFormId())) {
            record.setFormId(Long.parseLong(param.getFormId()));
        }
        if (StringUtils.isNotEmpty(param.getResourceId())) {
            record.setResourceId(Long.parseLong(param.getResourceId()));
        }
        if (StringUtils.isNotEmpty(param.getTableId())) {
            record.setExtData1(param.getTableId());
        }
        if (StringUtils.isNotEmpty(param.getCollect())) {
            record.setExtData2(param.getCollect());
        }
        if (StringUtils.isNotEmpty(param.getRowNumber())) {
            record.setTableNumber(Long.parseLong(param.getRowNumber()));
        }
        if (StringUtils.isNotEmpty(param.getPrePageNo())) {
            record.setPrePageNo(Long.parseLong(param.getPrePageNo()));
        }
        if (StringUtils.isNotEmpty(param.getGroupName())) {
            record.setGroupName(param.getGroupName());
        }
        if (StringUtils.isNotEmpty(param.getGroupId())) {
            record.setGroupId(Long.parseLong(param.getGroupId()));
        }
        if (StringUtils.isNotEmpty(param.getImageType())) {
            record.setImageType(param.getImageType());
        }
    }

    /**
     * 处理文件合并参数
     *
     * @param needMergeFileParam 合并文件参数JSON字符串
     * @param record 当前文件记录
     */
    private void handleMergeFileParam(String needMergeFileParam, ProjectTesteeFile record) {
        if (StringUtils.isNotEmpty(needMergeFileParam)) {
            List<NeedMergeFileParam> needMergeFileParams = JSON.parseArray(needMergeFileParam, NeedMergeFileParam.class);
            needMergeFileParams.forEach(data -> {
                String fileId = data.getFileId();
                ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
                if (projectTesteeFile != null) {
                    projectTesteeFile.setFileNumber(Integer.parseInt(data.getPageNumber()));
                    projectTesteeFile.setTargetFileId(record.getId());
                    projectTesteeFileMapper.updateByPrimaryKeySelective(projectTesteeFile);
                }
            });
        }
    }

    /**
     * 处理OCR识别
     *
     * @param record 文件记录
     * @param param 参数对象
     * @param uploadFileResultVo 上传结果对象
     */
    private void handleOcrProcessing(ProjectTesteeFile record, SaveUploadProjectFileParam param, UploadFileResultVo uploadFileResultVo) {
        // 写入OCR记录
        ProjectTesteeOcr projectTesteeOcr = new ProjectTesteeOcr();
        projectTesteeOcr.setProjectId(Long.parseLong(param.getProjectId()));
        projectTesteeOcr.setId(SnowflakeIdWorker.getUuid());
        projectTesteeOcr.setFileId(record.getId());
        projectTesteeOcr.setOcrKey(FileUtils.getNameNotSuffix(record.getUploadPath()));

        String medicalType = param.getMedicalType();
        if (MedicalTypeEnum.MEDICAL_TYPE_12.getCode().equals(medicalType)) {
            handleMedicalType12Ocr(record, param, projectTesteeOcr, uploadFileResultVo);
        } else if (MedicalTypeEnum.MEDICAL_TYPE_13.getCode().equals(medicalType)) {
            handleMedicalType13Ocr(record, projectTesteeOcr, uploadFileResultVo);
        } else if (MedicalTypeEnum.MEDICAL_TYPE_14.getCode().equals(medicalType)) {
            handleMedicalType14Ocr(record, projectTesteeOcr, uploadFileResultVo);
        } else {
            handleDefaultOcr(record, param, projectTesteeOcr, uploadFileResultVo);
        }

        projectTesteeOcr.setCreateTime(new Date());
        projectTesteeOcr.setCreateUserId(param.getCreateUserId());
        projectTesteeOcr.setStatus(BusinessConfig.VALID_STATUS);
        projectTesteeOcr.setTargetFormId(null);
        projectTesteeOcr.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectTesteeOcr.setTenantId(SecurityUtils.getSystemTenantId());
        projectTesteeOcrMapper.insert(projectTesteeOcr);
    }

    /**
     * 处理医疗类型12的OCR识别
     */
    private void handleMedicalType12Ocr(ProjectTesteeFile record, SaveUploadProjectFileParam param,
                                       ProjectTesteeOcr projectTesteeOcr, UploadFileResultVo uploadFileResultVo) {
        String recognizeTableAccurateOCR = tencentAIService.recognizeTableAccurateOCR(record.getFileUrl(), record.getUploadPath());
        projectTesteeOcr.setWordsResult(recognizeTableAccurateOCR);
        uploadFileResultVo.setWordsResult(recognizeTableAccurateOCR);

        if ("1".equals(param.getExtendStruct())) {
            String smartStructuralOCR = tencentAIService.smartStructuralOCR(record.getFileUrl(), record.getUploadPath());
            projectTesteeOcr.setFormWordsResult(smartStructuralOCR);
            uploadFileResultVo.setFormWordsResult(smartStructuralOCR);
            try {
                Thread.sleep(100L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("OCR处理线程被中断", e);
            }
        }

        if ("1".equals(param.getGeneralAccurate())) {
            String generalAccurateResult = tencentAIService.generalAccurateOCR(record.getFileUrl(), record.getUploadPath());
            projectTesteeOcr.setGeneralAccurateResult(generalAccurateResult);
            uploadFileResultVo.setGeneralAccurateResult(generalAccurateResult);
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("OCR处理线程被中断", e);
            }
        }
    }

    /**
     * 处理医疗类型13的OCR识别
     */
    private void handleMedicalType13Ocr(ProjectTesteeFile record, ProjectTesteeOcr projectTesteeOcr, UploadFileResultVo uploadFileResultVo) {
        String smartStructuralOCR = tencentAIService.smartStructuralOCR(record.getFileUrl(), record.getUploadPath());
        projectTesteeOcr.setWordsResult(smartStructuralOCR);
        uploadFileResultVo.setWordsResult(smartStructuralOCR);
    }

    /**
     * 处理医疗类型14的OCR识别
     */
    private void handleMedicalType14Ocr(ProjectTesteeFile record, ProjectTesteeOcr projectTesteeOcr, UploadFileResultVo uploadFileResultVo) {
        String smartStructuralProOCR = tencentAIService.smartStructuralProOCR(record.getFileUrl(), record.getUploadPath());
        projectTesteeOcr.setWordsResult(smartStructuralProOCR);
        uploadFileResultVo.setWordsResult(smartStructuralProOCR);
    }

    /**
     * 处理默认的OCR识别
     */
    private void handleDefaultOcr(ProjectTesteeFile record, SaveUploadProjectFileParam param,
                                 ProjectTesteeOcr projectTesteeOcr, UploadFileResultVo uploadFileResultVo) {
        String wordsResult = baiduAIService.getOcrMedicalReportDetection("", param.getMedicalType(), "",
                record.getUploadPath(), "", param.getTemplateId());
        projectTesteeOcr.setWordsResult(wordsResult);
        uploadFileResultVo.setWordsResult(wordsResult);
    }

    private boolean isValidURL(String url) {
        try {
            new URI(url).parseServerAuthority();
            return true;
        } catch (URISyntaxException e) {
            return false;
        }
    }

    @Override
    public List<ProjectTesteeFormImageVo> getProjectTesteeFormImageList(String projectId, String planId, String visitId, String formId, String testeeId,
                                                                        String taskDate, String resourceId, String tableId, String rowNumber,
                                                                        String medicalType, String openOCR, String batchUpload, String groupName,
                                                                        Boolean queryIgnoreMontage, String batchOpenOcr) {
        List<ProjectTesteeFormImageVo> dataList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("testeeId", testeeId);
        params.put("status", BusinessConfig.VALID_STATUS);
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo != null){
            planId = flowPlanInfo.getId().toString();
        }
        if(StringUtils.isNotEmpty(planId)){params.put("planId", planId);}
        if(StringUtils.isNotEmpty(visitId)){params.put("visitId", visitId);}
        if(StringUtils.isNotEmpty(formId)){params.put("formId", formId);}
        if(StringUtils.isNotEmpty(openOCR)){params.put("ocrFlag", "1".equals(openOCR));}
        if(StringUtils.isNotEmpty(batchUpload)){ params.put("batchUpload", "1".equals(batchUpload));}
        if(StringUtils.isNotEmpty(batchOpenOcr)){params.put("batchOpenOcr", "1".equals(batchOpenOcr));}
        if(StringUtils.isNotEmpty(taskDate)){params.put("taskDate", DateUtil.getAutoParseDate(taskDate));}
        if(StringUtils.isNotEmpty(resourceId)){params.put("resourceId", resourceId);}
        if(StringUtils.isNotEmpty(tableId)){params.put("tableId", tableId);}
        if(StringUtils.isNotEmpty(rowNumber)){params.put("rowNumber", rowNumber);}
        if(StringUtils.isNotEmpty(medicalType)){params.put("medicalType", medicalType);}
        if(StringUtils.isNotEmpty(groupName)){params.put("groupName", groupName);}
        if(queryIgnoreMontage){params.put("queryIgnoreMontage", !queryIgnoreMontage);}
        List<ProjectTesteeFile> projectTesteeFileList = projectTesteeFileMapper.getProjectTesteeFormImageList(params);
        for (ProjectTesteeFile projectTesteeFile : projectTesteeFileList) {
            ProjectTesteeFormImageVo projectTesteeFormImageVo = new ProjectTesteeFormImageVo();
            BeanUtils.copyProperties(projectTesteeFile, projectTesteeFormImageVo);
            projectTesteeFormImageVo.setName(projectTesteeFile.getOriginalName());
            projectTesteeFormImageVo.setUrl(projectTesteeFile.getFileUrl());
            if(projectTesteeFile.getGroupId() != null){
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(projectTesteeFile.getGroupId().toString());
                if(templateFormDetailConfig != null){
                    projectTesteeFormImageVo.setGroupName(templateFormDetailConfig.getLabel());
                }
            }
            if(projectTesteeFile.getResourceId() != null){
                TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(projectTesteeFile.getResourceId().toString());
                if(templateFormDetailConfig != null){
                    projectTesteeFormImageVo.setResourceName(templateFormDetailConfig.getLabel());
                }
            }
            boolean OCRValue = projectTesteeFile.getOcrFlag() == null ? false : projectTesteeFile.getOcrFlag();
            if(OCRValue){
                ProjectTesteeOcr projectTesteeOcr = projectTesteeOcrMapper.selectByFileId(projectTesteeFile.getId().toString());
                if(projectTesteeOcr != null){
                    projectTesteeFormImageVo.setProjectTesteeOcr(projectTesteeOcr);
                }
            }
            dataList.add(projectTesteeFormImageVo);
        }
        return dataList;
    }

    @Override
    public CustomResult deleteProjectTesteeFileById(String id, String operatorUserId) {
        ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(id));
        if(projectTesteeFile != null){
            projectTesteeFile.setUpdateTime(new Date());
            projectTesteeFile.setStatus(BusinessConfig.NO_VALID_STATUS);
            projectTesteeFile.setUpdateUser(operatorUserId);
            projectTesteeFileMapper.updateByPrimaryKeySelective(projectTesteeFile);
        }
        return null;
    }

    @Override
    public CustomResult updateProjectTesteeOCRFile(String fileId, String visitId, String formId, String userId, String collect) {
        ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
        if(projectTesteeFile != null){
            if(StringUtils.isNotEmpty(visitId)){
                projectTesteeFile.setVisitId(Long.parseLong(visitId));
            }
            if(StringUtils.isNotEmpty(formId)){
                projectTesteeFile.setFormId(Long.parseLong(formId));
            }
            if(StringUtils.isEmpty(visitId) && StringUtils.isEmpty(formId)){
                if("0".equals(collect)){
                    projectTesteeFile.setVisitId(null);
                    projectTesteeFile.setFormId(null);
                }
            }
            projectTesteeFile.setExtData2(collect);
            projectTesteeFile.setUpdateUser(userId);
            projectTesteeFile.setUpdateTime(new Date());
            projectTesteeFileMapper.updateByPrimaryKey(projectTesteeFile);
        }
        return null;
    }

    @Override
    public void updateOcrMedicalReportDetection(String fileId, String updateGroupResource, String ocrMedicalResult, String extendStruct, String generalAccurate, String openOCR, String operator, String groupId, String resourceId) {
        if(StringUtils.isNotEmpty(fileId)){
            if("1".equals(updateGroupResource)){
                ProjectTesteeFile projectTesteeFileVo = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
                if(projectTesteeFileVo != null){
                    if(StringUtils.isNotEmpty(groupId)){
                        projectTesteeFileVo.setGroupId(Long.parseLong(groupId));
                    }
                    if(StringUtils.isNotEmpty(resourceId)){
                        projectTesteeFileVo.setResourceId(Long.parseLong(resourceId));
                    }
                    projectTesteeFileVo.setUpdateTime(new Date());
                    projectTesteeFileMapper.updateByPrimaryKeySelective(projectTesteeFileVo);
                }
            }
            ProjectTesteeOcr projectTesteeOcr = projectTesteeOcrMapper.selectByFileId(fileId);
            if(projectTesteeOcr != null){
                projectTesteeOcr.setUpdateUserId(operator);
                projectTesteeOcr.setUpdateTime(new Date());
                if(StringUtils.isNotEmpty(ocrMedicalResult)){
                    projectTesteeOcr.setWordsResult(ocrMedicalResult);
                }
                ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
                if(StringUtils.isNotEmpty(openOCR)){
                    projectTesteeFile.setOcrFlag("1".equals(openOCR));
                    projectTesteeFileMapper.updateByPrimaryKeySelective(projectTesteeFile);
                }
                if("1".equals(extendStruct)){
                    String smartStructuralResult = tencentAIService.smartStructuralOCR(projectTesteeFile.getFileUrl(), projectTesteeFile.getUploadPath());
                    projectTesteeOcr.setFormWordsResult(smartStructuralResult);
                }
                if("1".equals(generalAccurate)){
                    String generalAccurateResult = tencentAIService.generalAccurateOCR(projectTesteeFile.getFileUrl(), projectTesteeFile.getUploadPath());
                    projectTesteeOcr.setGeneralAccurateResult(generalAccurateResult);
                }
                projectTesteeOcrMapper.updateByPrimaryKeySelective(projectTesteeOcr);
            }
        }
    }

    @Override
    public ProjectTesteeFormImageVo getProjectTesteeFormImageByFileId(String fileId) {
        ProjectTesteeOcr projectTesteeOcr = projectTesteeOcrMapper.selectByFileId(fileId);
        if(projectTesteeOcr != null){
            ProjectTesteeFormImageVo projectTesteeFormImageVo = new ProjectTesteeFormImageVo();
            ProjectTesteeFile projectTesteeFileVo = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
            BeanUtils.copyProperties(projectTesteeFileVo, projectTesteeFormImageVo);
            projectTesteeFormImageVo.setProjectTesteeOcr(projectTesteeOcr);
            return projectTesteeFormImageVo;
        }
        return null;
    }

    @Override
    public ProjectTesteeFormImageVo getProjectTesteeNextImageByFileId(String fileId) {
        ProjectTesteeOcr projectTesteeOcr =projectTesteeOcrMapper.getProjectTesteeNextImageByFileId(fileId);
        if(projectTesteeOcr != null){
            ProjectTesteeFormImageVo projectTesteeFormImageVo = new ProjectTesteeFormImageVo();
            ProjectTesteeFile projectTesteeFileVo = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
            BeanUtils.copyProperties(projectTesteeFileVo, projectTesteeFormImageVo);
            projectTesteeFormImageVo.setProjectTesteeOcr(projectTesteeOcr);
            return projectTesteeFormImageVo;
        }
        return null;
    }

    @Override
    public String updateMergeImages(String projectId, String visitId, String formId, String resourceId, String groupId, String testeeId, String fileIds, String mergeImage, String mergeMethod,
                                    String openOCR, String medicalType, String extendStruct, String generalAccurate, String createUserId) throws FileNotFoundException {
        
        
        String uploadPath = storageConfig.getRootPath() + "/" + projectId + CharUtil.SLASH + medicalType + CharUtil.SLASH + IdUtil.simpleUUID() + CharUtil.DOT + "png";
        String targetPath = storageConfig.getUploadFolder() + uploadPath;
        File file = new File(targetPath);
        if (FileUtil.exist(file.getParent())) {
            FileUtil.mkdir(file.getParent());
        }
        List<String> imageList = new LinkedList<>();
        if(StringUtils.isNotEmpty(fileIds)){
            String[] fileIdArrar = fileIds.split(",");
            for (String fileId : fileIdArrar) {
                ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(Long.parseLong(fileId));
                String uploadImagePath = projectTesteeFile.getUploadPath();
                imageList.add(uploadImagePath);
            }
            String[] images = imageList.stream().collect(Collectors.joining(",")).split(",");
            boolean imageListHorizontal = ImageUtil.merge(images, "png", targetPath);
            if(imageListHorizontal){
                File targetFile = new File(targetPath);
                FileInputStream inputStream = new FileInputStream(targetFile);
                String url = OssStorageFactory.build().upload(inputStream, uploadPath);
                ProjectTesteeFile record = new ProjectTesteeFile();
                record.setId(SnowflakeIdWorker.getUuid());
                record.setProjectId(Long.parseLong(projectId));
                if(StringUtils.isNotEmpty(visitId)){
                    record.setVisitId(Long.parseLong(visitId));
                }
                if(StringUtils.isNotEmpty(formId)){
                    record.setFormId(Long.parseLong(formId));
                }
                if(StringUtils.isNotEmpty(resourceId)){
                    record.setResourceId(Long.parseLong(resourceId));
                }
                if(StringUtils.isNotEmpty(groupId)){
                    record.setGroupId(Long.parseLong(groupId));
                }
                record.setImageType(BusinessConfig.PROJECT_TESTEE_IMAGE_03);
                record.setTesteeId(Long.parseLong(testeeId));
                record.setOriginalName("合成图片.png");
                record.setFileName(FileUtils.getName(targetPath));
                record.setFileUrl(url);
                record.setUploadPath(targetPath);
                record.setFileExt(FileUtil.getSuffix(FileUtils.getName(targetPath)));
                record.setOcrFlag(true);
                record.setBatchUpload(false);
                record.setIfMontage(false);
                record.setResourceType(medicalType);
                record.setCreateUser(createUserId);
                record.setCreateTime(new Date());
                record.setStatus(BusinessConfig.VALID_STATUS);
                projectTesteeFileMapper.insert(record);
            }
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public List<ProjectTesteeFormImageVo> getProjectTesteeThumbnailImageByFileId(String projectId, String fileId) {
        // 查询合成图片对应的缩略图列表
        List<ProjectTesteeFormImageVo> dataList = new ArrayList<>();
        List<ProjectTesteeFile> projectTesteeFileList = projectTesteeFileMapper.getProjectTesteeThumbnailImageByFileId(projectId, fileId);
        projectTesteeFileList.forEach(data->{
            ProjectTesteeFormImageVo projectTesteeFormImageVo = new ProjectTesteeFormImageVo();
            BeanUtils.copyProperties(data, projectTesteeFormImageVo);
            dataList.add(projectTesteeFormImageVo);
        });
        return dataList;
    }

    @Override
    public List<ProjectTesteeFormImageVo> getProjectTesteeUplaodThumbnailImages(String projectId, String visitId, String formId, String testeeId) {
        List<ProjectTesteeFormImageVo> dataList = new ArrayList<>();
        ProjectTesteeFileExample example = new ProjectTesteeFileExample();
        ProjectTesteeFileExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andIfMontageEqualTo(true);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        example.setOrderByClause("file_number asc");
        List<ProjectTesteeFile> projectTesteeFileList = projectTesteeFileMapper.selectByExample(example);
        projectTesteeFileList.forEach(data->{
            ProjectTesteeFormImageVo projectTesteeFormImageVo = new ProjectTesteeFormImageVo();
            BeanUtils.copyProperties(data, projectTesteeFormImageVo);
            dataList.add(projectTesteeFormImageVo);
        });
        return dataList;
    }

    @Override
    public List<UploadFileResultVo> saveUploadSystemFileResource(MultipartFile file, String folder, String tenantId) throws IOException {
        if(StringUtils.isEmpty(tenantId)){tenantId = SecurityUtils.getSystemTenantId();}
        if(StringUtils.isEmpty(folder)){folder = "logo";}
        List<UploadFileResultVo> dataList = new ArrayList();
        if(file != null){
            String path = new StringBuffer(storageConfig.getRootPath() + "/"+ folder +"/" + tenantId)
                    .append(CharUtil.SLASH)
                    .append(IdUtil.simpleUUID())
                    .append(CharUtil.DOT)
                    .append(FileUtil.extName(file.getOriginalFilename())).toString();

            // 图片压缩处理
            InputStream uploadInputStream;
            try {
                if (ImageCompressionUtils.isImageFile(file)) {
                    log.info("检测到图片文件，开始压缩: {}", file.getOriginalFilename());
                    uploadInputStream = ImageCompressionUtils.createCompressedInputStream(file);
                } else {
                    uploadInputStream = file.getInputStream();
                }
            } catch (Exception e) {
                log.error("图片压缩失败，使用原文件上传: {}", file.getOriginalFilename(), e);
                uploadInputStream = file.getInputStream();
            }

            String url = OssStorageFactory.build().upload(uploadInputStream, path);

            // 为本地存储重新创建输入流
            InputStream localInputStream;
            try {
                if (ImageCompressionUtils.isImageFile(file)) {
                    localInputStream = ImageCompressionUtils.createCompressedInputStream(file);
                } else {
                    localInputStream = file.getInputStream();
                }
            } catch (Exception e) {
                log.error("为本地存储创建压缩流失败，使用原文件: {}", file.getOriginalFilename(), e);
                localInputStream = file.getInputStream();
            }
            torageService.upload(localInputStream, path);

            UploadFileResultVo uploadFileResultVo = new UploadFileResultVo();
            uploadFileResultVo.setFileName(storageConfig.getUploadFolder() + path);
            uploadFileResultVo.setNewFileName(FileUtils.getName(path));
            uploadFileResultVo.setOriginalFilename(file.getOriginalFilename());
            uploadFileResultVo.setFileNameKey(FileUtils.getNameNotSuffix(path));
            uploadFileResultVo.setUrl(url);
            dataList.add(uploadFileResultVo);

        }
        return dataList;
    }

    @Override
    public ProjectTesteeFile getProjectTesteeFileByFileId(Long fileId) {
        return projectTesteeFileMapper.selectByPrimaryKey(fileId);
    }

    @Override
    public void updateProjectTesteeFileByFileId(ProjectTesteeFile projectTesteeFile) {
        projectTesteeFileMapper.updateByPrimaryKey(projectTesteeFile);
    }
    
    public List<UploadFileResultVo> saveUploadCrfFile(MultipartFile[] multipartFiles, String projectId) throws IOException {
        return saveUploadFile(multipartFiles, projectId, TEMPLATE_FORM_CONFIG_PATH);
    }
    
    @Override
    public List<UploadFileResultVo> uploadProjectFile(MultipartFile[] file, UploadProjectFileParam uploadProjectFileParam) throws IOException {
        List<UploadFileResultVo> uploadFileResultList = saveUploadFile(file, uploadProjectFileParam.getProjectId(), PROJECT_FILE_PATH);
        if(CollectionUtil.isEmpty(uploadFileResultList)){
            ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(NumberUtil.parseLong(uploadProjectFileParam.getFileId()));
            projectTesteeFile.setFileName(uploadProjectFileParam.getFileName());
            projectTesteeFile.setFileNumber(NumberUtil.parseInt(uploadProjectFileParam.getFileNumber()));
            projectTesteeFile.setVersion(uploadProjectFileParam.getVersion());
            projectTesteeFile.setUpdateUser(SecurityUtils.getUserIdValue());
            projectTesteeFile.setUpdateTime(new Date());
            projectTesteeFileMapper.updateByPrimaryKey(projectTesteeFile);
            return uploadFileResultList;
        }
        uploadFileResultList.forEach(uploadFileResultVo -> {
            if(StringUtils.isEmpty(uploadProjectFileParam.getFileId())){
                ProjectTesteeFile record = new ProjectTesteeFile();
                record.setId(SnowflakeIdWorker.getUuid());
                record.setProjectId(Long.parseLong(uploadProjectFileParam.getProjectId()));
                record.setCreateUser(SecurityUtils.getUserIdValue());
                record.setCreateTime(new Date());
                record.setStatus(BusinessConfig.VALID_STATUS);
                record.setOriginalName(uploadFileResultVo.getOriginalFilename());
                record.setFileName(uploadProjectFileParam.getFileName());
                record.setFileUrl(uploadFileResultVo.getUrl());
                record.setUploadPath(uploadFileResultVo.getFileName());
                record.setFileExt(FileUtil.getSuffix(FileUtils.getName(uploadFileResultVo.getFileName())));
                record.setFileNumber(NumberUtil.parseInt(uploadProjectFileParam.getFileNumber()));
                record.setVersion(uploadProjectFileParam.getVersion());
                record.setOcrFlag(false);
                record.setBatchUpload(uploadFileResultList.size() > 1);
                record.setIfMontage(false);
                record.setBatchOpenOcr(false);
                record.setResourceType("project_file");
                if(StringUtils.isNotEmpty(uploadProjectFileParam.getResourceType())){
                    record.setResourceType(uploadProjectFileParam.getResourceType());
                }
                record.setTenantId(SecurityUtils.getSystemTenantId());
                record.setPlatformId(SecurityUtils.getSystemPlatformId());
                projectTesteeFileMapper.insert(record);
            }else{
                ProjectTesteeFile projectTesteeFile = projectTesteeFileMapper.selectByPrimaryKey(NumberUtil.parseLong(uploadProjectFileParam.getFileId()));
                projectTesteeFile.setFileName(uploadProjectFileParam.getFileName());
                projectTesteeFile.setOriginalName(uploadFileResultVo.getOriginalFilename());
                projectTesteeFile.setFileUrl(uploadFileResultVo.getUrl());
                projectTesteeFile.setUploadPath(uploadFileResultVo.getFileName());
                projectTesteeFile.setFileExt(FileUtil.getSuffix(FileUtils.getName(uploadFileResultVo.getFileName())));
                projectTesteeFile.setFileNumber(NumberUtil.parseInt(uploadProjectFileParam.getFileNumber()));
                projectTesteeFile.setVersion(uploadProjectFileParam.getVersion());
                projectTesteeFile.setUpdateUser(SecurityUtils.getUserIdValue());
                projectTesteeFile.setUpdateTime(new Date());
                projectTesteeFileMapper.updateByPrimaryKey(projectTesteeFile);
            }
            
        });
        return uploadFileResultList;
    }
    
    @Override
    public CommonPage<ProjectTesteeFileVo> getProjectFileListForPage(String projectId, String resourceType, String searchValue, String sortField, String sortType, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        if (StringUtils.isBlank(sortField)) {sortField = "version";}
        if (StringUtils.isBlank(sortType)) {sortType = "asc";}
        if (StringUtils.isBlank(resourceType)) {resourceType = "project_file";}
        List<ProjectTesteeFileVo> dataList = projectTesteeFileMapper.getProjectFileListForPage(projectId, resourceType, searchValue, sortField, sortType, pageNum, pageSize);
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }
    
    @Override
    public ProjectTesteeFile checkFileRecordRepeat(String projectId, String fileName, String fileNumber, String version, String createUserId) {
        return projectTesteeFileMapper.checkFileRecordRepeat(projectId, fileName, fileNumber, version, createUserId);
    }
    
    @Override
    public List<UploadFileResultVo> uploadSystemFile(MultipartFile file, String folderName) throws IOException {
        return saveUploadSystemFileResource(file, folderName, null);
    }
    
    public List<UploadFileResultVo> saveUploadFile(MultipartFile[] multipartFiles, String projectId, String resource) throws IOException {
        List<UploadFileResultVo> dataList = new ArrayList();
        if(multipartFiles != null){
            for (MultipartFile file : multipartFiles) {
                String path = new StringBuffer(storageConfig.getRootPath() + "/" + projectId)
                        .append(CharUtil.SLASH)
                        .append(resource)
                        .append(CharUtil.SLASH)
                        .append(IdUtil.simpleUUID())
                        .append(CharUtil.DOT)
                        .append(FileUtil.extName(file.getOriginalFilename())).toString();

                // 图片压缩处理
                InputStream uploadInputStream;
                try {
                    if (ImageCompressionUtils.isImageFile(file)) {
                        log.info("检测到图片文件，开始压缩: {}", file.getOriginalFilename());
                        uploadInputStream = ImageCompressionUtils.createCompressedInputStream(file);
                    } else {
                        uploadInputStream = file.getInputStream();
                    }
                } catch (Exception e) {
                    log.error("图片压缩失败，使用原文件上传: {}", file.getOriginalFilename(), e);
                    uploadInputStream = file.getInputStream();
                }

                String url = OssStorageFactory.build().upload(uploadInputStream, path);

                // 为本地存储重新创建输入流
                InputStream localInputStream;
                try {
                    if (ImageCompressionUtils.isImageFile(file)) {
                        localInputStream = ImageCompressionUtils.createCompressedInputStream(file);
                    } else {
                        localInputStream = file.getInputStream();
                    }
                } catch (Exception e) {
                    log.error("为本地存储创建压缩流失败，使用原文件: {}", file.getOriginalFilename(), e);
                    localInputStream = file.getInputStream();
                }
                torageService.upload(localInputStream, path);
                UploadFileResultVo uploadFileResultVo = new UploadFileResultVo();
                uploadFileResultVo.setFileName(storageConfig.getUploadFolder() + path);
                uploadFileResultVo.setNewFileName(FileUtils.getName(path));
                uploadFileResultVo.setOriginalFilename(file.getOriginalFilename());
                uploadFileResultVo.setFileNameKey(FileUtils.getNameNotSuffix(path));
                uploadFileResultVo.setFileSuffix(FileUtil.getSuffix(FileUtils.getName(uploadFileResultVo.getFileName())));
                uploadFileResultVo.setFileSize(FileUtil.readableFileSize(file.getSize()));
                uploadFileResultVo.setUrl(url);
                dataList.add(uploadFileResultVo);
            }
        }
        return dataList;
    }

    @Override
    public List<ProjectTesteeFormImageVo> getTesteeAllFormImages(String projectId, String testeeId) {
        List<ProjectTesteeFormImageVo> dataList = new ArrayList<>();

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("projectId", projectId);
            params.put("testeeId", testeeId);
            params.put("status", BusinessConfig.VALID_STATUS);

            List<ProjectTesteeFile> projectTesteeFileList = projectTesteeFileMapper.getProjectTesteeFormImageList(params);

            for (ProjectTesteeFile projectTesteeFile : projectTesteeFileList) {
                ProjectTesteeFormImageVo projectTesteeFormImageVo = new ProjectTesteeFormImageVo();
                BeanUtils.copyProperties(projectTesteeFile, projectTesteeFormImageVo);
                projectTesteeFormImageVo.setName(projectTesteeFile.getOriginalName());

                // 设置文件访问URL
                if (StringUtils.isNotEmpty(projectTesteeFile.getFileUrl())) {
                    String fileUrl = projectTesteeFile.getFileUrl();
                    if (!fileUrl.startsWith("http")) {
                        fileUrl = storageConfig.getDomain() + "/" + fileUrl;
                    }
                    projectTesteeFormImageVo.setUrl(fileUrl);
                }

                dataList.add(projectTesteeFormImageVo);
            }
        } catch (Exception e) {
            log.error("批量查询参与者图片失败，projectId: {}, testeeId: {}", projectId, testeeId, e);
        }

        return dataList;
    }

}
