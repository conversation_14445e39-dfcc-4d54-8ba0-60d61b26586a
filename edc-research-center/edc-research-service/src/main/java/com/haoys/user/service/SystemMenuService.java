package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.entity.TreeSelect;
import com.haoys.user.domain.dto.ProjectMenuDto;
import com.haoys.user.domain.vo.auth.SystemMenuVo;
import com.haoys.user.model.SystemMenu;

import java.util.List;
import java.util.Set;

public interface SystemMenuService {
    /**
     * 创建后台菜单
     */
    CommonResult create(SystemMenu systemMenu);

    /**
     * 修改后台菜单
     */
    CommonResult update(Long id, SystemMenu systemMenu);

    /**
     * 根据ID获取菜单详情
     */
    SystemMenu getSystemMenuInfoByMenuId(Long id);

    /**
     * 根据ID删除菜单
     */
    int delete(Long id);


    /**
     * 查询系统菜单列表-全部
     * @param systemMenu
     * @return
     */
    List<SystemMenu> getSystemMenuList(SystemMenu systemMenu);

    /**
     * 查询系统平台菜单-顶级菜单
     *
     * @param userId
     * @param groupName
     * @param tenantId
     * @return
     */
    List<SystemMenuVo> getSystemPlatformMenuNameList(String userId, String groupName, String tenantId);

    /**
     * 树形结构返回所有菜单列表
     * @param menuType
     */
    List<SystemMenu> treeList(String menuType);

    /**
     * 根据用户ID查询权限
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据用户ID查询所属项目权限
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByProject(Long userId);


    /**
     * 根据项目ID获取所属项目权限
     * @param projectId 项目ID
     * @return
     */
    List<TreeSelect> getProjectMenuTreeListByProjectId(Long projectId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(Long roleId);


    /**
     * 根据项目角色ID查询菜单树信息
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByProjectRoleId(Long roleId);


    /**
     * 根据当前登录用户获取项目权限菜单
     * @param projectId           项目ID
     * @param roleId
     * @param userId              用户ID
     * @param parentId            父级节点id
     * @param groupName           分组名称
     * @param projectManagerOwner
     * @param HideResearchMethod
     * @return
     */
    List<SystemMenu> selectProjectUserMenuListByUserId(Long projectId, String roleId, Long userId, Long parentId, String groupName, Boolean projectManagerOwner, Boolean HideResearchMethod);

    /**
     * 查询项目路由列表
     * @param projectId
     * @param userId
     * @return
     */
    List<SystemMenu> getProjectRouterListByProjectIAndUserId(Long projectId, Long userId);


    /**
     * 查询系统用户权限列表
     * @param userId
     * @param menuType
     * @param groupName
     * @param showChildrenStructure 是否展示为树形结构
     * @return
     */
    List<SystemMenu> getSystemUserMenuList(String userId, String menuType, String groupName, boolean showChildrenStructure);


    /**
     * 批量新增项目菜单信息
     *
     * @param projectMenuDto 项目菜单
     */
    void batchSaveProjectMenuListByProjectId(ProjectMenuDto projectMenuDto);

    /**
     * 查询项目所展示的菜单列表
     * @return
     * @param menuType
     * @param status
     * @param hidden
     */
    List<TreeSelect> selectSystemMenuListByMenuType(String menuType, String status, Integer hidden);

    /**
     * 根据项目Id查看菜单
     * @param projectId
     * @return
     */
    List<Long> selectMenuListByProjectId(Long projectId);

    /**
     * 批量修改项目菜单信息
     * @param projectMenuDto 项目菜单
     */
    void updateProjectMenuListByProjectId(ProjectMenuDto projectMenuDto);

    /**
     * 根据用户查询系统菜单列表
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SystemMenu> selectMenuListByUserId(Long userId);


    /**
     * 构建前端所需要下拉树结构
     * @param systemMenuList 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelect> buildMenuTreeSelect(List<SystemMenu> systemMenuList);
    
    List<SystemMenu> getSystemMenuTreeList(String menuType, boolean enableViewValidData);
}
