package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.mapper.ProjectResearchersInfoMapper;
import com.haoys.user.mapper.ProjectTesteeSignMapper;
import com.haoys.user.model.ProjectResearchersInfo;
import com.haoys.user.model.ProjectResearchersInfoExample;
import com.haoys.user.model.ProjectTesteeSign;
import com.haoys.user.service.ProjectResearchersInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class ProjectResearchersInfoServiceImpl extends BaseService implements ProjectResearchersInfoService {

    @Autowired
    private  ProjectResearchersInfoMapper projectResearchersInfoMapper;
    @Autowired
    private ProjectTesteeSignMapper projectTesteeSignMapper;

    @Override
    public CommonResult<Object> create(ProjectResearchersInfo projectResearchersInfo) {
        projectResearchersInfo.setId(SnowflakeIdWorker.getUuid());
        projectResearchersInfo.setCreateTime(new Date());
        projectResearchersInfo.setCreateUser(SecurityUtils.getUserIdValue());
        projectResearchersInfo.setTenantId(SecurityUtils.getSystemTenantId());
        projectResearchersInfo.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectResearchersInfo.setStatus(Integer.valueOf(BusinessConfig.VALID_STATUS));
        projectResearchersInfoMapper.insertSelective(projectResearchersInfo);
        return CommonResult.success(null);
    }

    @Override
    public CommonResult<Object> update(ProjectResearchersInfo projectResearchersParams) {
        ProjectResearchersInfo projectResearchersInfo = projectResearchersInfoMapper.selectByPrimaryKey(projectResearchersParams.getId());
        if(projectResearchersInfo == null){
            return CommonResult.failed("未找到对应的研究者信息");
        }
        projectResearchersInfo.setName(projectResearchersParams.getName());
        projectResearchersInfo.setArea(projectResearchersParams.getArea());
        projectResearchersInfo.setGroupInfo(projectResearchersParams.getGroupInfo());
        projectResearchersInfo.setHospital(projectResearchersParams.getHospital());
        projectResearchersInfo.setDept(projectResearchersParams.getDept());
        projectResearchersInfo.setOpenBank(projectResearchersParams.getOpenBank());
        projectResearchersInfo.setIdentityCard(projectResearchersParams.getIdentityCard());
        projectResearchersInfo.setBankNumber(projectResearchersParams.getBankNumber());
        projectResearchersInfo.setTelPhone(projectResearchersParams.getTelPhone());
        projectResearchersInfo.setCertificate(projectResearchersParams.getCertificate());
        projectResearchersInfo.setIdCardBackPhoto(projectResearchersParams.getIdCardBackPhoto());
        projectResearchersInfo.setIdCardFacePhoto(projectResearchersParams.getIdCardFacePhoto());
        projectResearchersInfo.setUpdateTime(new Date());
        projectResearchersInfo.setUpdateUser(SecurityUtils.getUserIdValue());
        projectResearchersInfoMapper.updateByPrimaryKey(projectResearchersInfo);
        return CommonResult.success(null);
    }


    @Override
    public int saveOrUpdate(ProjectResearchersInfo projectResearchersInfo) {
        if (projectResearchersInfo.getId() != null){
            ProjectResearchersInfo info = projectResearchersInfoMapper.selectByPrimaryKey(projectResearchersInfo.getId());
            info.setName(projectResearchersInfo.getName());
            info.setArea(projectResearchersInfo.getArea());
            info.setGroupInfo(projectResearchersInfo.getGroupInfo());
            info.setHospital(projectResearchersInfo.getHospital());
            info.setDept(projectResearchersInfo.getDept());
            info.setProjectVolunteer(projectResearchersInfo.getProjectVolunteer());
            info.setProjectVolunteerPhone(projectResearchersInfo.getProjectVolunteerPhone());
            info.setUpdateTime(new Date());
            info.setUpdateUser(SecurityUtils.getUserIdValue());
            return projectResearchersInfoMapper.updateByPrimaryKey(info);
        } else {
            ProjectResearchersInfo info = new ProjectResearchersInfo();
            info.setId(SnowflakeIdWorker.getUuid());
            info.setName(projectResearchersInfo.getName());
            info.setArea(projectResearchersInfo.getArea());
            info.setGroupInfo(projectResearchersInfo.getGroupInfo());
            info.setHospital(projectResearchersInfo.getHospital());
            info.setDept(projectResearchersInfo.getDept());
            info.setOpenBank(projectResearchersInfo.getOpenBank());
            info.setIdentityCard(projectResearchersInfo.getIdentityCard());
            info.setBankNumber(projectResearchersInfo.getBankNumber());
            info.setTelPhone(projectResearchersInfo.getTelPhone());
            info.setCertificate(projectResearchersInfo.getCertificate());
            info.setIdCardBackPhoto(projectResearchersInfo.getIdCardBackPhoto());
            info.setIdCardFacePhoto(projectResearchersInfo.getIdCardFacePhoto());
            info.setStatus(Integer.valueOf(BusinessConfig.VALID_STATUS));
            info.setProjectVolunteer(projectResearchersInfo.getProjectVolunteer());
            info.setProjectVolunteerPhone(projectResearchersInfo.getProjectVolunteerPhone());
            info.setCreateTime(new Date());
            info.setCreateUser(projectResearchersInfo.getCreateUser());
            info.setTenantId(SecurityUtils.getSystemTenantId());
            info.setPlatformId(SecurityUtils.getSystemPlatformId());
            return projectResearchersInfoMapper.insertSelective(info);
        }
    }
    
    @Override
    public void deleteResearchersInfoByUserId(String userId) {
        ProjectResearchersInfoExample example = new ProjectResearchersInfoExample();
        ProjectResearchersInfoExample.Criteria criteria = example.createCriteria();
        criteria.andCreateUserEqualTo(userId);
        projectResearchersInfoMapper.deleteByExample(example);
    }
    
    @Override
    public ProjectResearchersInfo getProjectResearchersInfoByTesteeCode(String projectId, String testeeCode) {
        ProjectTesteeSign projectTesteeSign = projectTesteeSignMapper.getProjectTesteeSign(NumberUtil.parseLong(projectId), testeeCode);
        if(projectTesteeSign != null){
            return projectResearchersInfoMapper.getProjectResearchersInfoByCreateUser(projectId, projectTesteeSign.getCreateUser());
        }
        return null;
    }
    
    @Override
    public CommonResult<Object>  delete(Long signId) {
        int deleted = projectResearchersInfoMapper.deleteByPrimaryKey(signId);
        return CommonResult.success(deleted);
    }

    @Override
    public ProjectResearchersInfo getProjectResearcherInfo(String userId) {
        ProjectResearchersInfoExample example = new ProjectResearchersInfoExample();
        example.createCriteria().andCreateUserEqualTo(userId);
        example.setOrderByClause("create_time desc");
        List<ProjectResearchersInfo> projectResearchersInfos = projectResearchersInfoMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(projectResearchersInfos)){
            return projectResearchersInfos.get(0);
        }
        return null;
    }
}
