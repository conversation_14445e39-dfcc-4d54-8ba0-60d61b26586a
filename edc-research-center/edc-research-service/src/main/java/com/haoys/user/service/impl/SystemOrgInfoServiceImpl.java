package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.ThirdServiceConstants;
import com.haoys.user.common.core.domain.entity.BaseSystemArea;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.excel.ExcelUtil;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.entity.SystemOrgInfoQuery;
import com.haoys.user.domain.enums.SystemDictEnums;
import com.haoys.user.domain.vo.project.SysOrgExcelVo;
import com.haoys.user.domain.vo.project.SysOrgExportVo;
import com.haoys.user.domain.vo.project.SysOrgResult;
import com.haoys.user.domain.vo.system.SystemOrgInfoVo;
import com.haoys.user.mapper.ProjectOrgRoleMapper;
import com.haoys.user.mapper.SystemAreaMapper;
import com.haoys.user.mapper.SystemOrgInfoMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.ProjectOrgRole;
import com.haoys.user.model.ProjectOrgRoleExample;
import com.haoys.user.model.SystemOrgInfo;
import com.haoys.user.model.SystemOrgInfoExample;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.SystemOrgInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SystemOrgInfoServiceImpl extends BaseService implements SystemOrgInfoService {

    @Autowired
    private SystemOrgInfoMapper systemOrgInfoMapper;

    @Autowired
    private SystemAreaMapper systemAreaMapper;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private ProjectOrgRoleMapper projectOrgRoleMapper;

    @Override
    public CommonPage<SystemOrgInfoVo> selectSystemOrgInfoList(SystemOrgInfoQuery systemOrgInfoQuery) {
        Page<Object> page = PageHelper.startPage(systemOrgInfoQuery.getPageNum(), systemOrgInfoQuery.getPageSize());
        List<SystemOrgInfoVo> dataList = systemOrgInfoMapper.selectSystemOrgListForPage(systemOrgInfoQuery);
        return commonPageListWrapper(systemOrgInfoQuery.getPageNum(), systemOrgInfoQuery.getPageSize(), page, dataList);

    }

    @Override
    public CustomResult saveSystemOrgInfo(SystemOrgInfoQuery systemOrgInfoQuery) {
        CustomResult customResult = new CustomResult();
        //验证系统中心名称是否存在
        SystemOrgInfo systemOrgInfo = systemOrgInfoMapper.getSysOrgInfoByAreaOrgName(systemOrgInfoQuery.getProvinceCode(), systemOrgInfoQuery.getCityCode(), systemOrgInfoQuery.getCountyCode(), systemOrgInfoQuery.getOrgName());
        if(systemOrgInfo != null){
            customResult.setMessage(BusinessConfig.SYSTEM_ORG_RECORD_FOUND);
            return customResult;
        }
        //查询code最大值
        /*String orgCode = systemOrgInfoMapper.getMaxOrgCode();
        if(StringUtils.isNotEmpty(orgCode)){
            int value = NumberUtil.add(orgCode, "1").intValue();
            String formatOrgCode = NumberUtil.decimalFormat("0000000", value);
            systemOrgInfoQuery.setOrgCode(String.valueOf(formatOrgCode));
        }*/
        systemOrgInfoQuery.setCreateTime(new Date());
        SystemOrgInfo systemOrgInfoParams = new SystemOrgInfo();
        BeanUtils.copyProperties(systemOrgInfoQuery, systemOrgInfoParams);
        systemOrgInfoParams.setOrgId(SnowflakeIdWorker.getUuid());
        systemOrgInfoParams.setStatus(BusinessConfig.VALID_STATUS);
        systemOrgInfoMapper.insertSelective(systemOrgInfoParams);
        customResult.setData(systemOrgInfoParams.getOrgId().toString());
        return customResult;
    }

    @Override
    public CustomResult updateSystemOrgAreaInfoByOrgId(SystemOrgInfoQuery systemOrgInfoQuery) {
        CustomResult customResult = new CustomResult();
        SystemOrgInfo systemOrgInfo = new SystemOrgInfo();
        BeanUtils.copyProperties(systemOrgInfoQuery, systemOrgInfo);
        systemOrgInfoMapper.updateByPrimaryKeySelective(systemOrgInfo);
        return customResult;
    }
    
    @Override
    public CustomResult updateSystemOrgAreaInfoByOrgId(SystemOrgInfo systemOrgInfo) {
        CustomResult customResult = new CustomResult();
        systemOrgInfoMapper.updateByPrimaryKeySelective(systemOrgInfo);
        return customResult;
    }

    @Override
    public CustomResult updateSystemOrgInfo(SystemOrgInfoQuery systemOrgInfoQuery) {
        CustomResult customResult = new CustomResult();
        SystemOrgInfo systemOrgInfo = systemOrgInfoMapper.getSysOrgInfoByAreaOrgName(systemOrgInfoQuery.getProvinceCode() , systemOrgInfoQuery.getCityCode(), systemOrgInfoQuery.getCountyCode(), systemOrgInfoQuery.getOrgName());
        if (systemOrgInfo!=null){
            if (!systemOrgInfoQuery.getOrgId().equals(systemOrgInfo.getOrgId())){
                customResult.setMessage(ResultCode.BUSINESS_USER_SYSTEM_ORG_EXIST.getMessage());
                return customResult;
            }
        }
        SystemOrgInfo systemOrgParams = new SystemOrgInfo();
        BeanUtils.copyProperties(systemOrgInfoQuery, systemOrgParams, new String[]{"pageSize","pageNum"});
        systemOrgParams.setUpdateTime(new Date());
        systemOrgInfoMapper.updateByPrimaryKeySelective(systemOrgParams);
        return customResult;
    }

    @Override
    public CustomResult deleteSystemOrgInfoByOrgId(Long orgId) {
        CustomResult customResult = new CustomResult();
        ProjectOrgRoleExample example = new ProjectOrgRoleExample();
        ProjectOrgRoleExample.Criteria criteria = example.createCriteria();
        criteria.andOrgIdEqualTo(orgId);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<ProjectOrgRole> projectOrgRoles = projectOrgRoleMapper.selectByExample(example);
        if(projectOrgRoles.size() > 0){
            customResult.setMessage(SystemDictEnums.E70002.getMessage());
        }else {
            systemOrgInfoMapper.deleteByPrimaryKey(orgId);
        }
        return customResult;
    }

    @Override
    public SystemOrgInfo selectSystemOrgInfoByOrgId(String orgId) {
        SystemOrgInfo systemOrgInfo = systemOrgInfoMapper.selectByPrimaryKey(NumberUtil.parseLong(orgId));
        return systemOrgInfo;
    }

    @Override
    public SystemOrgInfoVo selectSystemOrgInfoByOrgName(String orgName) {
        SystemOrgInfoQuery systemOrgInfoQuery = new SystemOrgInfoQuery();
        systemOrgInfoQuery.setOrgName(orgName);
        return systemOrgInfoMapper.getSysOrgInfoByOrgName(systemOrgInfoQuery.getOrgName());
    }


    @Override
    public CustomResult<SysOrgResult> saveBatchSystemOrgInfo(MultipartFile file, List<SysOrgExportVo> errorList) throws Exception {
        CustomResult customResult = new CustomResult();
        List<SystemOrgInfoQuery> sysOrgList = new ArrayList<>();
        ExcelUtil<SysOrgExcelVo> excelUtil = new ExcelUtil<>(SysOrgExcelVo.class);
        boolean baseAdd = true;
        int value = 0;
        List<SysOrgExcelVo> sysOrgVoList = excelUtil.importExcel(file.getInputStream());
        for (SysOrgExcelVo sysOrgExcelVo : sysOrgVoList) {


            SysOrgExportVo sysOrgExportVo = new SysOrgExportVo();
            String orgName = sysOrgExcelVo.getOrgName();

            sysOrgExportVo.setOrgName(orgName);
            String result = validateAreaResult(sysOrgExcelVo);
            if(StringUtils.isNotEmpty(result)){
                sysOrgExportVo.setErrorMessage(result);
                errorList.add(sysOrgExportVo);
                continue;
            }
            // 根据省区市的code和所属中心的名称查询是否已经存在该所属中心
            SystemOrgInfo systemOrgInfo = systemOrgInfoMapper.getSysOrgInfoByAreaOrgName(sysOrgExcelVo.getProvinceCode() ,sysOrgExcelVo.getCityCode(),sysOrgExcelVo.getCountyCode(),sysOrgExcelVo.getOrgName());
            if(systemOrgInfo!=null){
                // 如果所属中心不存在，需要进行提示。
                sysOrgExportVo.setErrorMessage(BusinessConfig.SYSTEM_ORG_RECORD_FOUND);
                errorList.add(sysOrgExportVo);
                continue;
            }
            if(StringUtils.isEmpty(orgName)){
                sysOrgExportVo.setErrorMessage(BusinessConfig.SYSTEM_ORG_RECORD_EMPTY);
                errorList.add(sysOrgExportVo);
                continue;
            }
            SystemOrgInfoVo systemOrgInfoQuery = this.selectSystemOrgInfoByOrgName(orgName);
            if(systemOrgInfoQuery != null){
                sysOrgExportVo.setErrorMessage(BusinessConfig.SYSTEM_ORG_RECORD_FOUND);
                errorList.add(sysOrgExportVo);
                continue;
            }
            SystemOrgInfoQuery systemOrgInfoQueryModel = new SystemOrgInfoQuery();
            systemOrgInfoQueryModel.setOrgName(orgName);
            String orgCode = systemOrgInfoMapper.getMaxOrgCode();
            if(StringUtils.isNotEmpty(orgCode)){
                if(baseAdd){
                    value = NumberUtil.add(orgCode, "1").intValue();
                }else{
                    value = NumberUtil.add(String.valueOf(value), "1").intValue();
                }
                String formatOrgCode = NumberUtil.decimalFormat("0000000", value);
                systemOrgInfoQueryModel.setOrgCode(String.valueOf(formatOrgCode));
            }
            systemOrgInfoQueryModel.setProvinceCode(sysOrgExcelVo.getProvinceCode());
            systemOrgInfoQueryModel.setCityCode(sysOrgExcelVo.getCityCode());
            systemOrgInfoQueryModel.setCountyCode(sysOrgExcelVo.getCountyCode());


            //医院登记设置
            String orgType = sysOrgExcelVo.getOrgType();
            if(StringUtils.isEmpty(orgType)){
                sysOrgExportVo.setErrorMessage(BusinessConfig.SYSTEM_ORG_TYPE_EMPTY);
                errorList.add(sysOrgExportVo);
                continue;
            }
            String dictinaryCode = dictionaryService.getDictNameById(Long.parseLong(orgType));
            if(StringUtils.isBlank(dictinaryCode)){
                sysOrgExportVo.setErrorMessage(BusinessConfig.SYSTEM_ORG_TYP_DICE_EMPTY);
                errorList.add(sysOrgExportVo);
                continue;
            }
            systemOrgInfoQueryModel.setOrgType(orgType);
            systemOrgInfoQueryModel.setCreateTime(new Date());
            sysOrgList.add(systemOrgInfoQueryModel);
            baseAdd = false;
        }
        if(CollectionUtil.isNotEmpty(sysOrgList)){
            systemOrgInfoMapper.insertBatchSysOrgInfo(sysOrgList);
        }

        if(sysOrgList.size() >=0 && errorList.size() >0){
            customResult.setData(sysOrgList.size());
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_FAIL);
        }
        if(errorList.size() ==0){
            customResult.setData(errorList.size());
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        }
        if(sysOrgList.size() ==0 && errorList.size() ==0){
            customResult.setData(sysOrgList.size());
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_FAIL);
        }
        return customResult;
    }

    @Override
    public Map<String, Object> getThirdSystemOrgInfoByName(String orgName) {
        Map<String, Object> dataMap = new HashMap<>();
        SystemOrgInfoQuery systemOrgInfoQuery = new SystemOrgInfoQuery();
        systemOrgInfoQuery.setOrgName(orgName);
        //List<SystemOrgInfo> systemOrgInfoList = systemOrgInfoMapper.selectSystemOrgListForPage(systemOrgInfoQuery);
        List<SystemOrgInfoVo> dataList = systemOrgInfoMapper.selectSystemOrgListForPage(systemOrgInfoQuery);
        dataMap.put("systemOrgInfoList", dataList);
        // https://public.creditchina.gov.cn/private-api/catalogSearchHome?keyword=%E5%81%A5%E5%BA%B7%E5%9C%A8%E7%BA%BF&scenes=defaultScenario&tableName=credit_xyzx_tyshxydm&searchState=2&entityType=1%2C2%2C4%2C5%2C6%2C7%2C8&templateId=&page=1&pageSize=10
        String params = "?keyword="+ orgName + "&templateId=&scenes=defaultscenario&tableName=credit_xyzx_tyshxydm&searchState=2&entityType=1%2C2%2C4%2C5%2C6%2C7%2C8&page=1&pageSize=10&_="+new Date().getTime();
        String result = HttpUtil.get(ThirdServiceConstants.RESOURCE_SYSTEM_ORG_SYNS_URL + params);
         if(StringUtils.isNotEmpty(result)){
            Map<String, Object> dataInfo = JSON.parseObject(result, new TypeReference<HashMap<String, Object>>(){}.getType());
            dataMap.put("thirdSystemOrgInfoList", dataInfo);
        }
        return dataMap;
    }

    private String validateAreaResult(SysOrgExcelVo sysOrgExcelVo) {
        if(sysOrgExcelVo == null){return "";}
        StringBuilder failureMsg = new StringBuilder();
        String provinceName = sysOrgExcelVo.getProvinceName();
        String cityName = sysOrgExcelVo.getCityName();
        String countyName = sysOrgExcelVo.getCountyName();
        String orgType = sysOrgExcelVo.getOrgType();
        Dictionary dictionary = null;
        if(dictionary != null){
            sysOrgExcelVo.setOrgType(dictionary.getCode());
        }
        BaseSystemArea baseSystemArea = new BaseSystemArea();
        baseSystemArea.setCityName(provinceName);
        BaseSystemArea provinceArea = systemAreaMapper.selectOne(baseSystemArea);
        if(StringUtils.isNull(provinceArea)){
            failureMsg.append(BusinessConfig.SYS_ORG_PROVINCE_FOUND);
        }else{
            sysOrgExcelVo.setProvinceCode(provinceArea.getId());
            baseSystemArea.setParentId(provinceArea.getId());
            baseSystemArea.setCityName(cityName);
            BaseSystemArea cityArea = systemAreaMapper.selectOne(baseSystemArea);
            if(StringUtils.isNull(cityArea)){
                failureMsg.append(BusinessConfig.SYS_ORG_CITY_FOUND);
            }else{
                sysOrgExcelVo.setCityCode(cityArea.getId());
                baseSystemArea.setParentId(cityArea.getId());
                baseSystemArea.setCityName(countyName);
                BaseSystemArea countyArea = systemAreaMapper.selectOne(baseSystemArea);
                if(StringUtils.isNull(countyArea)){
                    failureMsg.append(BusinessConfig.SYS_ORG_AREA_FOUND);
                }else{
                    sysOrgExcelVo.setCountyCode(countyArea.getId());
                }
            }
        }
        return failureMsg.toString();
    }

    @Override
    public List<SystemOrgInfo> selectByExample(SystemOrgInfoExample example){
        return systemOrgInfoMapper.selectByExample(example);
    }

    @Override
    public int enableOrUnable(String id){
        SystemOrgInfo systemOrgInfo = systemOrgInfoMapper.selectByPrimaryKey(Long.valueOf(id));
        if(systemOrgInfo.getStatus().equals(BusinessConfig.VALID_STATUS)){
            //校验是否被使用
            ProjectOrgRoleExample example = new ProjectOrgRoleExample();
            ProjectOrgRoleExample.Criteria criteria = example.createCriteria();
            criteria.andOrgIdEqualTo(Long.valueOf(id));
            List<ProjectOrgRole> projectOrgRoles = projectOrgRoleMapper.selectByExample(example);
            if(projectOrgRoles.size() > 0){
               return 0;

            }
            systemOrgInfo.setStatus(BusinessConfig.NO_VALID_STATUS);
        }else if(systemOrgInfo.getStatus().equals(BusinessConfig.NO_VALID_STATUS)){
            systemOrgInfo.setStatus(BusinessConfig.VALID_STATUS);
        }
        return  systemOrgInfoMapper.updateByPrimaryKey(systemOrgInfo);
    }

}
