package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.model.ProjectResearchersInfo;

/**
 * 流程配置-创建计划服务类
 */
public interface ProjectResearchersInfoService {

    CommonResult<Object> create(ProjectResearchersInfo sign);
    
    CommonResult<Object> update(ProjectResearchersInfo sign);
    
    CommonResult<Object> delete(Long signId);

    ProjectResearchersInfo getProjectResearcherInfo(String userId);

    int saveOrUpdate(ProjectResearchersInfo projectResearchersInfo);
    
    void deleteResearchersInfoByUserId(String userId);
    
    ProjectResearchersInfo getProjectResearchersInfoByTesteeCode(String projectId, String testeeCode);
}
