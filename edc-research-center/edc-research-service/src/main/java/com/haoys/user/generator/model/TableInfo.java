package com.haoys.user.generator.model;

import lombok.Data;
import java.util.List;

/**
 * 表信息模型
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Data
public class TableInfo {
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 表注释
     */
    private String tableComment;
    
    /**
     * 实体类名
     */
    private String entityName;
    
    /**
     * 主键列名
     */
    private String primaryKey;
    
    /**
     * 列信息列表
     */
    private List<ColumnInfo> columns;
    
    /**
     * 获取主键列信息
     */
    public ColumnInfo getPrimaryKeyColumn() {
        if (primaryKey == null || columns == null) {
            return null;
        }
        
        return columns.stream()
                .filter(column -> primaryKey.equals(column.getColumnName()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取非主键列
     */
    public List<ColumnInfo> getNonPrimaryKeyColumns() {
        if (primaryKey == null || columns == null) {
            return columns;
        }
        
        return columns.stream()
                .filter(column -> !primaryKey.equals(column.getColumnName()))
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取BLOB类型列
     */
    public List<ColumnInfo> getBlobColumns() {
        if (columns == null) {
            return java.util.Collections.emptyList();
        }
        
        return columns.stream()
                .filter(ColumnInfo::isBlobType)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取非BLOB类型列
     */
    public List<ColumnInfo> getNonBlobColumns() {
        if (columns == null) {
            return java.util.Collections.emptyList();
        }
        
        return columns.stream()
                .filter(column -> !column.isBlobType())
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 是否包含BLOB字段
     */
    public boolean hasBlobColumns() {
        return columns != null && columns.stream().anyMatch(ColumnInfo::isBlobType);
    }
    
    /**
     * 是否包含日期字段
     */
    public boolean hasDateColumns() {
        return columns != null && columns.stream()
                .anyMatch(column -> "Date".equals(column.getJavaType()) || "Time".equals(column.getJavaType()));
    }
    
    /**
     * 获取实体类的小写名称（用于变量名）
     */
    public String getEntityNameLowerCase() {
        if (entityName == null || entityName.isEmpty()) {
            return entityName;
        }
        return Character.toLowerCase(entityName.charAt(0)) + entityName.substring(1);
    }
    
    /**
     * 获取表的描述信息
     */
    public String getTableDescription() {
        if (tableComment != null && !tableComment.trim().isEmpty()) {
            return tableComment;
        }
        return entityName + "表";
    }
}
