package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.project.ProjectPrescriptionParam;
import com.haoys.user.domain.vo.ProjectPrescriptionVo;
import com.haoys.user.mapper.ProjectPrescriptionMapper;
import com.haoys.user.model.ProjectPrescription;
import com.haoys.user.model.ProjectPrescriptionExample;
import com.haoys.user.service.ProjectPrescriptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class ProjectPrescriptionServiceImpl extends BaseService implements ProjectPrescriptionService {


    @Autowired
    private ProjectPrescriptionMapper projectPrescriptionMapper;

    @Override
    public CommonPage<ProjectPrescriptionVo> getProjectPrescriptionListForPage(String projectId, String name, String ageStart, String ageEnd, String diagnosticDesc, String userId, Integer pageSize, Integer pageNum) {
        List<ProjectPrescriptionVo> dataList = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectPrescriptionExample example = new ProjectPrescriptionExample();
        ProjectPrescriptionExample.Criteria criteria = example.createCriteria();
        criteria.andPlatformIdEqualTo(projectId);
        if(StringUtils.isNotEmpty(name)){
            criteria.andContentLike("%"+name+"%");
        }
        if(StringUtils.isNotEmpty(diagnosticDesc)){
            criteria.andDiagnosticDescLike("%"+diagnosticDesc+"%");
        }
        if(StringUtils.isNotEmpty(ageStart)){
            criteria.andAgeGreaterThanOrEqualTo(Integer.parseInt(ageStart));
        }
        if(StringUtils.isNotEmpty(ageEnd)){
            criteria.andAgeLessThanOrEqualTo(Integer.parseInt(ageEnd));
        }
        criteria.andStatusEqualTo("0");
        example.setOrderByClause("create_time desc");
        List<ProjectPrescription> projectPatientTaskList = projectPrescriptionMapper.selectByExample(example);
        for (ProjectPrescription projectPrescription : projectPatientTaskList) {
            ProjectPrescriptionVo projectPrescriptionVo = new ProjectPrescriptionVo();
            BeanUtils.copyProperties(projectPrescription, projectPrescriptionVo);
            dataList.add(projectPrescriptionVo);
        }
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public CustomResult saveProjectPrescription(ProjectPrescriptionParam projectPrescriptionParam) {
        CustomResult customResult = new CustomResult();
        if(projectPrescriptionParam.getId() == null){
            ProjectPrescription record = new ProjectPrescription();
            BeanUtils.copyProperties(projectPrescriptionParam, record);
            record.setId(SnowflakeIdWorker.getUuid());
            record.setCreateTime(new Date());
            record.setStatus("0");
            projectPrescriptionMapper.insertSelective(record);
        }else{
            ProjectPrescription projectPrescription = getProjectPrescriptionBySampleIdAndPlatformId(projectPrescriptionParam.getPlatformId(), projectPrescriptionParam.getSampleId().toString());
            if(projectPrescription != null){
                BeanUtils.copyProperties(projectPrescriptionParam, projectPrescription);
                projectPrescriptionMapper.updateByPrimaryKeySelective(projectPrescription);
            }
        }
        return customResult;
    }

    public ProjectPrescription getProjectPrescriptionBySampleIdAndPlatformId(String projectId, String sampleId){
        ProjectPrescriptionExample example = new ProjectPrescriptionExample();
        ProjectPrescriptionExample.Criteria criteria = example.createCriteria();
        criteria.andPlatformIdEqualTo(projectId);
        criteria.andSampleIdEqualTo(Long.parseLong(sampleId));
        List<ProjectPrescription> projectPrescriptionList = projectPrescriptionMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectPrescriptionList)){
            return projectPrescriptionList.get(0);
        }
        return null;
    }

}
