package com.haoys.user.model;

import java.io.Serializable;

public class ProjectPrescriptionMix implements Serializable {
    private Long index;

    private String batchCode;

    private Long nameCount;

    private String nameList;

    private static final long serialVersionUID = 1L;

    public Long getIndex() {
        return index;
    }

    public void setIndex(Long index) {
        this.index = index;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public Long getNameCount() {
        return nameCount;
    }

    public void setNameCount(Long nameCount) {
        this.nameCount = nameCount;
    }

    public String getNameList() {
        return nameList;
    }

    public void setNameList(String nameList) {
        this.nameList = nameList;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", index=").append(index);
        sb.append(", batchCode=").append(batchCode);
        sb.append(", nameCount=").append(nameCount);
        sb.append(", nameList=").append(nameList);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}