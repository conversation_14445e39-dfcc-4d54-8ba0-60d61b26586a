package com.haoys.user.mapper;

import com.haoys.user.model.ProjectTesteeVariableSync;
import com.haoys.user.model.ProjectTesteeVariableSyncExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProjectTesteeVariableSyncMapper {
    long countByExample(ProjectTesteeVariableSyncExample example);

    int deleteByExample(ProjectTesteeVariableSyncExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectTesteeVariableSync record);

    int insertSelective(ProjectTesteeVariableSync record);

    List<ProjectTesteeVariableSync> selectByExample(ProjectTesteeVariableSyncExample example);

    ProjectTesteeVariableSync selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectTesteeVariableSync record, @Param("example") ProjectTesteeVariableSyncExample example);

    int updateByExample(@Param("record") ProjectTesteeVariableSync record, @Param("example") ProjectTesteeVariableSyncExample example);

    int updateByPrimaryKeySelective(ProjectTesteeVariableSync record);

    int updateByPrimaryKey(ProjectTesteeVariableSync record);
}
