package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import java.util.Map;

/**
 * Service接口模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class ServiceTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String servicePackage = (String) context.get("servicePackage");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String serviceName = (String) context.get("serviceName");
        String primaryKeyType = (String) context.get("primaryKeyType");
        String currentDate = (String) context.get("currentDate");
        
        StringBuilder sb = new StringBuilder();
        
        // 包声明
        sb.append("package ").append(servicePackage).append(";\n\n");
        
        // 导入语句
        sb.append("import ").append(modelPackage).append(".").append(entityName).append(";\n");
        sb.append("import com.haoys.user.common.api.CommonResult;\n");
        sb.append("import com.haoys.user.common.api.CommonPage;\n");
        sb.append("import java.util.List;\n");
        sb.append("import java.util.Map;\n\n");

        // 接口注释
        sb.append("/**\n");
        sb.append(" * ").append(tableInfo.getTableDescription()).append("Service接口\n");
        sb.append(" * 提供完整的CRUD操作、批量操作、复杂查询、缓存管理等功能\n");
        sb.append(" * \n");
        sb.append(" * <AUTHOR>
        sb.append(" * @version 2.0.0\n");
        sb.append(" * @since 1.0.0\n");
        sb.append(" * @date ").append(currentDate).append("\n");
        sb.append(" */\n");

        // 接口声明
        sb.append("public interface ").append(serviceName).append(" {\n\n");

        // 基础CRUD方法
        sb.append("    // ========== 基础CRUD操作 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 创建记录\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 创建结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<").append(entityName).append("> create(").append(entityName).append(" record);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据主键删除\n");
        sb.append("     * @param id 主键\n");
        sb.append("     * @return 删除结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<Void> deleteById(").append(primaryKeyType).append(" id);\n\n");

        sb.append("    /**\n");
        sb.append("     * 更新记录\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 更新结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<").append(entityName).append("> update(").append(entityName).append(" record);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据主键查询\n");
        sb.append("     * @param id 主键\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<").append(entityName).append("> getById(").append(primaryKeyType).append(" id);\n\n");

        // 批量操作方法
        sb.append("    // ========== 批量操作 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 批量创建\n");
        sb.append("     * @param records 实体对象列表\n");
        sb.append("     * @return 创建结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> batchCreate(List<").append(entityName).append("> records);\n\n");

        sb.append("    /**\n");
        sb.append("     * 批量删除\n");
        sb.append("     * @param ids 主键列表\n");
        sb.append("     * @return 删除结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<Void> batchDeleteByIds(List<").append(primaryKeyType).append("> ids);\n\n");

        sb.append("    /**\n");
        sb.append("     * 批量更新\n");
        sb.append("     * @param records 实体对象列表\n");
        sb.append("     * @return 更新结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> batchUpdate(List<").append(entityName).append("> records);\n\n");

        // 复杂查询方法
        sb.append("    // ========== 复杂查询操作 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件查询列表\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> listByCondition(Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件查询单个对象\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<").append(entityName).append("> getOneByCondition(Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件分页查询\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @param pageNum 页码\n");
        sb.append("     * @param pageSize 页大小\n");
        sb.append("     * @return 分页查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<CommonPage<").append(entityName).append(">> pageByCondition(Map<String, Object> params, Integer pageNum, Integer pageSize);\n\n");

        sb.append("    /**\n");
        sb.append("     * 根据条件统计数量\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 统计结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<Long> countByCondition(Map<String, Object> params);\n\n");

        sb.append("    /**\n");
        sb.append("     * 多条件AND查询\n");
        sb.append("     * @param conditions 条件映射 (字段名 -> 值)\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> listByMultipleConditions(Map<String, Object> conditions);\n\n");

        sb.append("    /**\n");
        sb.append("     * 范围查询\n");
        sb.append("     * @param field 字段名\n");
        sb.append("     * @param startValue 起始值\n");
        sb.append("     * @param endValue 结束值\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> listByRange(String field, Object startValue, Object endValue);\n\n");

        sb.append("    /**\n");
        sb.append("     * 模糊查询\n");
        sb.append("     * @param field 字段名\n");
        sb.append("     * @param keyword 关键词\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> listByLike(String field, String keyword);\n\n");

        sb.append("    /**\n");
        sb.append("     * IN查询\n");
        sb.append("     * @param field 字段名\n");
        sb.append("     * @param values 值列表\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> listByIn(String field, List<Object> values);\n\n");

        sb.append("    /**\n");
        sb.append("     * 总数统计\n");
        sb.append("     * @param params 查询参数\n");
        sb.append("     * @return 统计结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<Long> countTotal(Map<String, Object> params);\n\n");

        // 复杂查询方法
        sb.append("    // ========== 复杂查询操作 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 复杂组合查询\n");
        sb.append("     * 支持多种查询条件的组合：精确匹配、范围查询、模糊查询、IN查询等\n");
        sb.append("     * @param queryParams 查询参数映射\n");
        sb.append("     *        - exactConditions: Map<String, Object> 精确匹配条件\n");
        sb.append("     *        - rangeField: String, startValue: Object, endValue: Object 范围查询\n");
        sb.append("     *        - likeField: String, keyword: String 模糊查询\n");
        sb.append("     *        - inField: String, values: List<Object> IN查询\n");
        sb.append("     * @return 查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<List<").append(entityName).append(">> complexQuery(Map<String, Object> queryParams);\n\n");

        sb.append("    /**\n");
        sb.append("     * 分页复杂查询\n");
        sb.append("     * @param queryParams 查询参数映射（同complexQuery）\n");
        sb.append("     * @param pageNum 页码\n");
        sb.append("     * @param pageSize 页大小\n");
        sb.append("     * @return 分页查询结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<CommonPage<").append(entityName).append(">> complexPageQuery(Map<String, Object> queryParams, Integer pageNum, Integer pageSize);\n\n");

        // 数据校验方法
        sb.append("    // ========== 数据校验 ==========\n\n");

        sb.append("    /**\n");
        sb.append("     * 校验数据有效性\n");
        sb.append("     * @param record 实体对象\n");
        sb.append("     * @return 校验结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<Void> validateData(").append(entityName).append(" record);\n\n");

        sb.append("    /**\n");
        sb.append("     * 检查记录是否存在\n");
        sb.append("     * @param id 主键\n");
        sb.append("     * @return 检查结果\n");
        sb.append("     */\n");
        sb.append("    CommonResult<Boolean> existsById(").append(primaryKeyType).append(" id);\n\n");

        // 自定义方法示例
        sb.append("    // ========== 自定义业务方法 ==========\n");
        sb.append("    // 可以在这里添加特定的业务方法\n");
        sb.append("    // 例如：复杂的业务逻辑、数据校验、事务处理等\n\n");

        sb.append("}\n");
        
        return sb.toString();
    }
}
