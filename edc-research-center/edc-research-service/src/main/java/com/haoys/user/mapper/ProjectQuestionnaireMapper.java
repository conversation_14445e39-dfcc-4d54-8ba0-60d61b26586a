package com.haoys.user.mapper;

import com.haoys.user.model.ProjectQuestionnaire;
import com.haoys.user.model.ProjectQuestionnaireExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectQuestionnaireMapper {
    long countByExample(ProjectQuestionnaireExample example);

    int deleteByExample(ProjectQuestionnaireExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectQuestionnaire record);

    int insertSelective(ProjectQuestionnaire record);

    List<ProjectQuestionnaire> selectByExample(ProjectQuestionnaireExample example);

    ProjectQuestionnaire selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectQuestionnaire record, @Param("example") ProjectQuestionnaireExample example);

    int updateByExample(@Param("record") ProjectQuestionnaire record, @Param("example") ProjectQuestionnaireExample example);

    int updateByPrimaryKeySelective(ProjectQuestionnaire record);

    int updateByPrimaryKey(ProjectQuestionnaire record);
}