package com.haoys.user.mapper;

import com.haoys.user.model.TemplateGroupLable;
import com.haoys.user.model.TemplateGroupLableExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateGroupLableMapper {
    long countByExample(TemplateGroupLableExample example);

    int deleteByExample(TemplateGroupLableExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateGroupLable record);

    int insertSelective(TemplateGroupLable record);

    List<TemplateGroupLable> selectByExample(TemplateGroupLableExample example);

    TemplateGroupLable selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateGroupLable record, @Param("example") TemplateGroupLableExample example);

    int updateByExample(@Param("record") TemplateGroupLable record, @Param("example") TemplateGroupLableExample example);

    int updateByPrimaryKeySelective(TemplateGroupLable record);

    int updateByPrimaryKey(TemplateGroupLable record);
}