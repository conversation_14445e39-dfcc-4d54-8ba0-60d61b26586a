package com.haoys.user.mapper;

import com.haoys.user.domain.vo.rcts.RandomizedBlindRecordVo;
import com.haoys.user.model.RctsRandomizedBlindRecord;
import com.haoys.user.model.RctsRandomizedBlindRecordExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface RctsRandomizedBlindRecordMapper {
    long countByExample(RctsRandomizedBlindRecordExample example);

    int deleteByExample(RctsRandomizedBlindRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(RctsRandomizedBlindRecord record);

    int insertSelective(RctsRandomizedBlindRecord record);

    List<RctsRandomizedBlindRecord> selectByExample(RctsRandomizedBlindRecordExample example);

    RctsRandomizedBlindRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") RctsRandomizedBlindRecord record, @Param("example") RctsRandomizedBlindRecordExample example);

    int updateByExample(@Param("record") RctsRandomizedBlindRecord record, @Param("example") RctsRandomizedBlindRecordExample example);

    int updateByPrimaryKeySelective(RctsRandomizedBlindRecord record);

    int updateByPrimaryKey(RctsRandomizedBlindRecord record);

    List<RandomizedBlindRecordVo> getRandomizedBlindPage(Map<String, Object> params);

    @Select("SELECT COUNT(*) FROM rcts_randomized_blind_record br WHERE br.status = '0' AND br.project_id = #{projectId} AND bind_testee_id IS NOT NULL AND bind_testee_id !=''")
    int queryBlindRecordTesteeCount(@Param("projectId") Long projectId);

    @Select("SELECT COUNT(*) FROM rcts_randomized_blind_record br WHERE br.status = '0' AND br.project_id = #{projectId} ")
    int queryBlindRecordCount(@Param("projectId") Long projectId);

    @Update(" update rcts_randomized_blind_record br set br.status = '1' where br.project_id = #{projectId} ")
    int deleteBlindRecordCount(@Param("projectId") Long projectId);
    
    RctsRandomizedBlindRecord getProjectRandomizedConfigNumber(String projectId, String projectOrgId, String bindTesteeId);


    String getBlindId(@Param("projectId") Long projectId, @Param("expands") List<String> expand);

}