package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.rcts.ModifyDrugParam;
import com.haoys.user.domain.param.rcts.SaveDrugParam;
import com.haoys.user.domain.param.rcts.saveDrugDistributeParam;
import com.haoys.user.domain.vo.rcts.DrugDistributeVo;
import com.haoys.user.domain.vo.rcts.DrugRegisterVo;
import com.haoys.user.mapper.RctsDrugDistributeManageMapper;
import com.haoys.user.mapper.RctsDrugRegisterManageMapper;
import com.haoys.user.model.RctsDrugDistributeManage;
import com.haoys.user.model.RctsDrugRegisterManage;
import com.haoys.user.service.RctsDrugRegisterManageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class RctsDrugRegisterManageServiceImpl extends BaseService implements RctsDrugRegisterManageService {


    @Resource
    private RctsDrugRegisterManageMapper rctsDrugRegisterManageMapper;

    @Resource
    private RctsDrugDistributeManageMapper rctsDrugDistributeManageMapper;


    @Override
    public int saveDrugRegister(SaveDrugParam param) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsDrugRegisterManage manage = new RctsDrugRegisterManage();
        BeanUtils.copyProperties(param, manage);
        manage.setId(SnowflakeIdWorker.getUuid());

        manage.setCreateTime(new Date());
        manage.setCreateUserId(userId);
        manage.setTenantId(SecurityUtils.getSystemTenantId());
        manage.setPlatformId(SecurityUtils.getSystemPlatformId());
        manage.setStatus(BusinessConfig.VALID_STATUS);

        int res = rctsDrugRegisterManageMapper.insert(manage);
        return res;
    }

    @Override
    public CommonPage<DrugRegisterVo> getDrugRegisterPage(String projectId, String warningStatus, String materialStatus,
                                                          String materialCode, String materialName,
                                                          int pageNum, int pageSize) {

        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(warningStatus)){
            params.put("warningStatus", warningStatus);
        }
        if (StringUtils.isNotEmpty(materialStatus)){
            params.put("materialStatus", materialStatus);
        }
        if (StringUtils.isNotEmpty(materialCode)){
            params.put("materialCode", materialCode);
        }
        if (StringUtils.isNotEmpty(materialName)){
            params.put("materialName", materialName);
        }


        List<DrugRegisterVo> res = rctsDrugRegisterManageMapper.getDrugRegisterPage(params);
        return commonPageListWrapper(pageNum, pageSize, page, res);
    }


    @Override
    public DrugRegisterVo getDrugRegister(Long id) {
        RctsDrugRegisterManage drugRegisterManage = rctsDrugRegisterManageMapper.selectByPrimaryKey(id);
        DrugRegisterVo drugRegisterVo = new DrugRegisterVo();
        BeanUtils.copyProperties(drugRegisterManage, drugRegisterVo);
        return drugRegisterVo;
    }


    @Override
    public int modifyDrugRegister(ModifyDrugParam param) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsDrugRegisterManage manage = rctsDrugRegisterManageMapper.selectByPrimaryKey(param.getId());
        BeanUtils.copyProperties(param, manage);
        manage.setUpdateTime(new Date());
        manage.setUpdateUserId(userId);
        return rctsDrugRegisterManageMapper.updateByPrimaryKey(manage);
    }

    @Override
    public int removeDrugRegister(Long id) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsDrugRegisterManage manage = rctsDrugRegisterManageMapper.selectByPrimaryKey(id);
        manage.setStatus(BusinessConfig.NO_VALID_STATUS);
        manage.setUpdateUserId(userId);
        manage.setUpdateTime(new Date());
        return rctsDrugRegisterManageMapper.updateByPrimaryKey(manage);
    }

    @Override
    public List<Map<String, String>> getMaterialCode(String projectId) {

        List<Map<String, String>> res = rctsDrugRegisterManageMapper.getMaterialCode(projectId);
        return res;
    }

    @Override
    public void checkExpirationDrug() {
        rctsDrugRegisterManageMapper.updateExpirationDrug("w02");
    }

    @Override
    public String saveDrugDistribute(saveDrugDistributeParam param) {
        String userId = Objects.requireNonNull(SecurityUtils.getUserId()).toString();
        RctsDrugRegisterManage drugRegisterManage = rctsDrugRegisterManageMapper.selectByPrimaryKey(param.getDrugId());
        if (drugRegisterManage.getMinPackageUnit() != null){
            int minPackageUnit = drugRegisterManage.getMinPackageUnit();
            int distributeCount = param.getDistributeCount();
            if (minPackageUnit < distributeCount) {
                return "库存不足!";
            }
            int nowMinPackageUnit = minPackageUnit-distributeCount;
            drugRegisterManage.setMinPackageUnit(nowMinPackageUnit);
            rctsDrugRegisterManageMapper.updateDrugMinPackageUnit(nowMinPackageUnit, param.getDrugId());
            if (drugRegisterManage.getWarningValue() != null){
                if (nowMinPackageUnit <= drugRegisterManage.getWarningValue()){
                    rctsDrugRegisterManageMapper.updateExpirationDrug("w01");
                }
            }
        }
        RctsDrugDistributeManage manage = new RctsDrugDistributeManage();
        BeanUtils.copyProperties(param, manage);
        manage.setId(SnowflakeIdWorker.getUuid());
        manage.setCreateTime(new Date());
        manage.setCreateUserId(userId);
        manage.setTenantId(SecurityUtils.getSystemTenantId());
        manage.setPlatformId(SecurityUtils.getSystemPlatformId());
        manage.setStatus(BusinessConfig.VALID_STATUS);
        rctsDrugDistributeManageMapper.insert(manage);
        return "ok";
    }


    @Override
    public CommonPage<DrugDistributeVo> getDrugDistributePage(String projectId, String testeeCode, String acronym,
                                                              String queryMaterial, int pageNum, int pageSize ) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(testeeCode)){
            params.put("testeeCode", testeeCode);
        }
        if (StringUtils.isNotEmpty(acronym)){
            params.put("acronym", acronym);
        }
        if (StringUtils.isNotEmpty(queryMaterial)){
            params.put("queryMaterial", queryMaterial);
        }

        List<DrugDistributeVo> res = rctsDrugDistributeManageMapper.getDrugDistributePage(params);
        return commonPageListWrapper(pageNum, pageSize, page, res);
    }

    @Override
    public DrugDistributeVo getDrugDistribute(Long id) {
        RctsDrugDistributeManage res = rctsDrugDistributeManageMapper.selectByPrimaryKey(id);
        DrugDistributeVo dd = new DrugDistributeVo();
        BeanUtils.copyProperties(res, dd);
        return dd;
    }
}
