package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPatientPlanDetail;
import com.haoys.user.model.ProjectPatientPlanDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPatientPlanDetailMapper {
    long countByExample(ProjectPatientPlanDetailExample example);

    int deleteByExample(ProjectPatientPlanDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPatientPlanDetail record);

    int insertSelective(ProjectPatientPlanDetail record);

    List<ProjectPatientPlanDetail> selectByExample(ProjectPatientPlanDetailExample example);

    ProjectPatientPlanDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPatientPlanDetail record, @Param("example") ProjectPatientPlanDetailExample example);

    int updateByExample(@Param("record") ProjectPatientPlanDetail record, @Param("example") ProjectPatientPlanDetailExample example);

    int updateByPrimaryKeySelective(ProjectPatientPlanDetail record);

    int updateByPrimaryKey(ProjectPatientPlanDetail record);
}