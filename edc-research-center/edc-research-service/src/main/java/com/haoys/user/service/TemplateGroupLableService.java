package com.haoys.user.service;


import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.crf.TemplateGroupLableParam;
import com.haoys.user.domain.template.ProjectVisitSearchTreeVo;
import com.haoys.user.domain.vo.ecrf.ProjectFormVariableTreeVo;
import com.haoys.user.domain.vo.ecrf.ProjectTemplateFormVariableTreeVo;
import com.haoys.user.domain.vo.ecrf.ProjectVisitTreeVo;
import com.haoys.user.domain.vo.ecrf.TemplateGroupLableVo;

import java.util.List;

public interface TemplateGroupLableService {

    /**
     * 根据访视id查询表单项列表-前台使用
     * @param projectId
     * @param lableId
     * @param visitId
     * @param testeeId
     * @return
     */
    List<TemplateGroupLableVo> getTemplateGroupLableList(String projectId, String lableId, String visitId, String testeeId);

    /**
     * 保存编辑表单项分类
     * @param templateGroupLableParam
     * @return
     */
    CustomResult saveTemplateFormLabel(TemplateGroupLableParam templateGroupLableParam);

    /**
     * 删除表单项分类
     * @param labelId 分类id
     * @return
     */
    String deleteTemplateFormLabel(String projectId, String labelId);

    /**
     * 新版本访视列表
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeId
     * @param hiddenReadForm
     * @param complateStatus
     * @param enableVisitPercent
     * @return
     */
    List<ProjectVisitTreeVo> getProjectFrontVisitTreeList(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId, String hiddenReadForm, String complateStatus, String enableVisitPercent);

    /**
     * 查询模版访视列表
     * @param templateId
     * @return
     */
    List<ProjectVisitTreeVo> getTemplateVisitConfigList(String templateId);

    /**
     * 查询项目访视表单项分类列表
     * @param projectId
     * @return
     */
    List<ProjectVisitTreeVo> getProjectBackVisitTreeList(String projectId);

    /**
     * 访视树状结构筛查条件
     * @param projectId
     * @return
     */
    List<ProjectVisitSearchTreeVo> getProjectVisitTreeData(String projectId);

    /**
     * 查询导出参与者数据条件
     * @param projectId
     * @param planId
     * @return
     */
    List<ProjectFormVariableTreeVo> getProjectFormVariableTreeVo(String projectId, String planId);
    
    List<ProjectTemplateFormVariableTreeVo> getTemplateFormVariableTreeVo(String projectId, String labConfigType);
    
    List<ProjectTemplateFormVariableTreeVo> getTemplateVisitFormVariableTreeVo(String projectId, String planId, String labConfigType, String queryGroup, String queryTable);
}
