package com.haoys.user.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectTesteeTableParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormParam;
import com.haoys.user.domain.param.testee.QueryTesteeGroupParam;
import com.haoys.user.domain.param.testee.QueryTesteeFormAndTableParam;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailAndTableResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeVariableResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultVo;
import com.haoys.user.domain.vo.participant.ProjectTesteeResultWrapperVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeExportViewVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormVariableComplateResultVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeResultExportVo;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeResultExample;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface ProjectTesteeResultService {

    ProjectTesteeResult selectByPrimaryKey(Long id);

    void insert(ProjectTesteeResult projectTesteeResult);

    void updateByPrimaryKeySelective(ProjectTesteeResult projectTesteeResultVo);

    /**
     * 查询导出参与者列表表单结果
     * @param projectId
     * @param planId
     * @param projectOrgId
     * @param variableId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeResultWrapperVo> getProjectTesteeFormAndTableResultList(String projectId, String planId, String projectOrgId, String variableId, String testeeId);

    /**
     * 保存表格行记录(多个单元格数据)
     * @param projectTesteeTableParam
     * @return
     */
    CustomResult saveProjectTesteeTableRowRecord(ProjectTesteeTableParam projectTesteeTableParam);

    /**
     * 查询表单提交结果列表
     * @param projectId
     * @param visitId
     * @param testeeId
     * @return
     */
    List<Map<String, Object>> getTesteeFormResultList(String projectId, String visitId, String testeeId);

    ProjectTesteeResult getProjectTesteeFormOneResult(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId);

    List<Map<String, Object>> getResearchTesteeResultList(String projectId, String testeeId);

    List<ProjectTesteeResult> getProjectTesteeVisitTimeResult(String projectId, String visitId);

    /**
     * 计算表单变量完成度
     *
     * @param projectId
     * @param projectOrgId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    ProjectTesteeFormVariableComplateResultVo getProjectTesteeFormVariableIdComplateCount(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId);


    ProjectTesteeFormVariableComplateResultVo getProjectTesteeTableVariableIdComplateCount(String projectId, String projectOrgId, String planId, String visitId, String formId, String testeeId);


    /**
     * 查询下次访视的计划访视时间 TODO 需求诡异，无法理解
     */
    ProjectTesteeResult getLastTesteeVisitFollowTime(String projectId, String visitId, String formId, String followVisitViewName, String testeeId);

    /**
     * 记录表单中表格变量
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param groupId
     * @param testeeId
     * @param complateStatus
     * @param tenantId
     * @param platformId
     * @return
     */
    String saveProjectTesteeFormTableResult(String projectId, String planId, String visitId, String formId, String formDetailId, String groupId, String testeeId, String complateStatus, String tenantId, String platformId);

    /**
     * 查询表单变量录入情况
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param tableId
     * @return
     */
    List<ProjectTesteeFormDetailAndTableResultVo> getTemplateFormDetailAndTableResult(String projectId, String planId, String visitId, String formId, String formDetailId, String tableId);

    void saveProjectTesteeBaseFormVariableResult(ProjectTesteeResult projectTesteeResult);
    
    /**
     * 保存患者表单
     * @param projectId
     * @param visitId
     * @param formId
     * @param variableId
     * @param label
     * @param fieldName
     * @param fieldText
     * @param fieldValue
     * @param testeeId
     * @param createUserId
     * @param dataFrom
     * @param tenantId
     * @param platformId
     */
    void saveProjectTesteeResult(String projectId, String visitId, String formId, String variableId, String label,
                                 String fieldName, String fieldText, String fieldValue, String testeeId,
                                 String createUserId, String dataFrom, String tenantId, String platformId);

    /**
     * 查询参与者普通表单详情
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param testeeId
     * @return
     */
    List<ProjectTesteeResult> getTesteeVisitFormResultDetail(String projectId, String planId, String visitId, String formId, String testeeId);

    /**
     * 查询普通表单变量录入结果
     * @param testeeResultId
     * @return
     */
    ProjectTesteeResult getTesteeFormDetailResult(Long testeeResultId);

    /**
     * 查询参与者分组字段录入结果
     * @param projectId
     * @param visitId
     * @param formId
     * @param groupVariableId
     * @param groupId
     * @param testeeId
     * @return
     */
    ProjectTesteeResult getProjectTesteeGroupResult(String projectId, String visitId, String formId, String groupVariableId, String groupId, String testeeId);

    /**
     * 查询参与者访视表单提交记录
     * @param projectId
     * @param visitId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @param orgId
     * @return
     */
    List<ProjectTesteeResultExportVo> getProjectTesteeFormResultByVariableIds(String projectId, String visitId, String formId, String formDetailId, String testeeId, String orgId);

    /**
     * 导出参与者查询列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgId
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param conditionValue
     * @param conditionTableValue
     * @return
     */
    List<ProjectTesteeExportViewVo> getExportProjectTesteeListForPage(String projectId, String code, String realName, String orgId, String ownerDoctor, String status, String sortField, String sortType, String conditionValue, String conditionTableValue);

    /**
     * 查询导出患者列表
     * @param projectId
     * @param code
     * @param realName
     * @param orgIds
     * @param ownerDoctor
     * @param status
     * @param sortField
     * @param sortType
     * @param queryTesteeFormParamList
     * @param queryTesteeFormAndTableParamList
     * @param queryTesteeGroupParamList
     * @return
     */
    List<Map<String,Object>> getExportProjectTesteeListForPageVersion2(String projectId, String code, String realName, String orgIds, String ownerDoctor, String status, String sortField, String sortType, List<QueryTesteeFormParam> queryTesteeFormParamList, List<QueryTesteeFormAndTableParam> queryTesteeFormAndTableParamList, List<QueryTesteeGroupParam> queryTesteeGroupParamList);


    List<ProjectTesteeExportViewVo.FormVariableVo> getExportProjectTesteeVariableValue(String projectId, String testeeId, String conditionValue, String conditionTableValue);

    List<ProjectTesteeVariableResultVo> getProjectTesteeVariableResultFromMysqlDataSource(String projectId, String testeeId);

    /**
     * 删除参与者指定变量记录
     * @param testeeResultId
     */
    void deleteTesteeFormResultDetailById(long testeeResultId);

    /**
     * 查询参与者表单提交结果
     *
     * @param projectId
     * @param planId
     * @param visitId
     * @param formId
     * @param formExpandId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    ProjectTesteeResult getTesteeFormResultValueByFormDetailId(String projectId, String planId, String visitId, String formId, String formExpandId, String formDetailId, String testeeId);

    /**
     * 批量查询参与者的所有表单结果，用于导出优化
     *
     * @param projectId 项目ID
     * @param testeeId 参与者ID
     * @return 该参与者的所有表单结果
     */
    List<ProjectTesteeResult> getTesteeAllFormResults(String projectId, String testeeId);

    List<ProjectTesteeResult> selectProjectTesteeFormVariableValues(ProjectTesteeResultExample example);

    void initProjectUserFormVariableInfo();

    Boolean getSystemDictionaryOptionReference(String dictionaryId);

    Boolean getProjectDictionaryOptionReference(String projectId, String dictionaryId);

    /**
     * 查询参与者基本信息表单变量记录
     * @param projectId
     * @param formId
     * @param formDetailId
     * @param testeeId
     * @return
     */
    ProjectTesteeResultWrapperVo getTesteeFormBaseInfoByFormVariableId(String projectId, String formId, String formDetailId, String testeeId);

    /**
     * 保存参与者基本信息和扩展配置
     *
     * @param projectId
     * @param formId
     * @param variableId
     * @param label
     * @param fieldName
     * @param fieldText
     * @param value
     * @param testeeId
     * @param createUserId
     * @param dataFrom
     */
    ProjectTesteeResultWrapperVo saveTesteeBaseFormVariableInfo(String projectId, String formId, String variableId, String label, String fieldName, String fieldText, String value, String testeeId, String createUserId, String dataFrom);

    void saveTesteeVariableRecord() throws IOException;

    List<ProjectTesteeResultVo> getProjectTesteeGroupResultList(Long projectId, Long visitId, Long formId, Long formDetailId, Long testeeId);

}
