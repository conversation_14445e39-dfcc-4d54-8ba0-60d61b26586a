package com.haoys.user.generator.template.impl;

import com.haoys.user.generator.model.TableInfo;
import com.haoys.user.generator.model.ColumnInfo;
import java.util.List;
import java.util.Map;

/**
 * Mapper XML模板
 * 
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
public class MapperXmlTemplate {
    
    @SuppressWarnings("unchecked")
    public static String generate(Map<String, Object> context) {
        TableInfo tableInfo = (TableInfo) context.get("tableInfo");
        String mapperPackage = (String) context.get("mapperPackage");
        String modelPackage = (String) context.get("modelPackage");
        String entityName = (String) context.get("entityName");
        String mapperName = (String) context.get("mapperName");
        String primaryKeyProperty = (String) context.get("primaryKeyProperty");
        String primaryKeyColumn = (String) context.get("primaryKeyColumn");
        String tableName = (String) context.get("tableName");
        List<ColumnInfo> columns = (List<ColumnInfo>) context.get("columns");
        List<ColumnInfo> nonPrimaryKeyColumns = (List<ColumnInfo>) context.get("nonPrimaryKeyColumns");
        List<ColumnInfo> nonBlobColumns = (List<ColumnInfo>) context.get("nonBlobColumns");
        boolean hasBlobColumns = (Boolean) context.get("hasBlobColumns");
        
        StringBuilder sb = new StringBuilder();
        
        // XML声明
        sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        sb.append("<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n");
        sb.append("<mapper namespace=\"").append(mapperPackage).append(".").append(mapperName).append("\">\n\n");
        
        // ResultMap
        generateResultMap(sb, entityName, modelPackage, primaryKeyProperty, primaryKeyColumn, columns);
        
        // Base_Column_List
        generateBaseColumnList(sb, nonBlobColumns);
        
        // Blob_Column_List (如果有BLOB字段)
        if (hasBlobColumns) {
            generateBlobColumnList(sb, tableInfo.getBlobColumns());
        }
        
        // 基础CRUD操作
        generateBasicCrud(sb, tableName, entityName, primaryKeyProperty, primaryKeyColumn, columns, nonPrimaryKeyColumns, hasBlobColumns, modelPackage);
        
        // 批量操作
        generateBatchOperations(sb, tableName, entityName, primaryKeyProperty, primaryKeyColumn, columns, nonPrimaryKeyColumns);
        
        // 条件查询
        generateConditionalQueries(sb, tableName, entityName, hasBlobColumns);
        
        // 聚合查询
        generateAggregateQueries(sb, tableName);
        
        // 动态查询
        generateDynamicQueries(sb);
        
        sb.append("</mapper>\n");
        
        return sb.toString();
    }
    
    private static void generateResultMap(StringBuilder sb, String entityName, String modelPackage, 
                                        String primaryKeyProperty, String primaryKeyColumn, List<ColumnInfo> columns) {
        sb.append("    <!-- 基础结果映射 -->\n");
        sb.append("    <resultMap id=\"BaseResultMap\" type=\"").append(modelPackage).append(".").append(entityName).append("\">\n");
        
        // 主键映射
        sb.append("        <id column=\"").append(primaryKeyColumn).append("\" jdbcType=\"")
          .append(getPrimaryKeyJdbcType(columns, primaryKeyColumn)).append("\" property=\"")
          .append(primaryKeyProperty).append("\" />\n");
        
        // 其他字段映射
        for (ColumnInfo column : columns) {
            if (!column.getColumnName().equals(primaryKeyColumn)) {
                sb.append("        <result column=\"").append(column.getColumnName())
                  .append("\" jdbcType=\"").append(column.getMybatisJdbcType())
                  .append("\" property=\"").append(column.getJavaProperty()).append("\" />\n");
            }
        }
        
        sb.append("    </resultMap>\n\n");
    }
    
    private static void generateBaseColumnList(StringBuilder sb, List<ColumnInfo> nonBlobColumns) {
        sb.append("    <!-- 基础列列表 -->\n");
        sb.append("    <sql id=\"Base_Column_List\">\n");
        sb.append("        ");
        
        for (int i = 0; i < nonBlobColumns.size(); i++) {
            if (i > 0) sb.append(", ");
            if (i > 0 && i % 5 == 0) sb.append("\n        ");
            sb.append(nonBlobColumns.get(i).getColumnName());
        }
        
        sb.append("\n    </sql>\n\n");
    }
    
    private static void generateBlobColumnList(StringBuilder sb, List<ColumnInfo> blobColumns) {
        sb.append("    <!-- BLOB列列表 -->\n");
        sb.append("    <sql id=\"Blob_Column_List\">\n");
        sb.append("        ");
        
        for (int i = 0; i < blobColumns.size(); i++) {
            if (i > 0) sb.append(", ");
            sb.append(blobColumns.get(i).getColumnName());
        }
        
        sb.append("\n    </sql>\n\n");
    }
    
    private static void generateBasicCrud(StringBuilder sb, String tableName, String entityName,
                                        String primaryKeyProperty, String primaryKeyColumn,
                                        List<ColumnInfo> columns, List<ColumnInfo> nonPrimaryKeyColumns,
                                        boolean hasBlobColumns, String modelPackage) {
        // 插入
        sb.append("    <!-- 插入记录 -->\n");
        sb.append("    <insert id=\"insert\" parameterType=\"").append(modelPackage).append(".").append(entityName).append("\">\n");
        sb.append("        INSERT INTO ").append(tableName).append(" (\n");
        sb.append("            ");
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) sb.append(", ");
            if (i > 0 && i % 5 == 0) sb.append("\n            ");
            sb.append(columns.get(i).getColumnName());
        }
        sb.append("\n        ) VALUES (\n");
        sb.append("            ");
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) sb.append(", ");
            if (i > 0 && i % 5 == 0) sb.append("\n            ");
            ColumnInfo column = columns.get(i);
            sb.append("#{").append(column.getJavaProperty()).append(",jdbcType=")
              .append(column.getMybatisJdbcType()).append("}");
        }
        sb.append("\n        )\n");
        sb.append("    </insert>\n\n");

        // 选择性插入
        generateInsertSelective(sb, tableName, columns, modelPackage, entityName);

        // 根据主键删除
        sb.append("    <!-- 根据主键删除 -->\n");
        sb.append("    <delete id=\"deleteByPrimaryKey\" parameterType=\"").append(getPrimaryKeyJavaType(columns, primaryKeyColumn)).append("\">\n");
        sb.append("        DELETE FROM ").append(tableName).append("\n");
        sb.append("        WHERE ").append(primaryKeyColumn).append(" = #{").append(primaryKeyProperty).append("}\n");
        sb.append("    </delete>\n\n");

        // 根据主键更新
        generateUpdateByPrimaryKey(sb, tableName, primaryKeyProperty, primaryKeyColumn, nonPrimaryKeyColumns, modelPackage, entityName);

        // 根据主键选择性更新
        generateUpdateByPrimaryKeySelective(sb, tableName, primaryKeyProperty, primaryKeyColumn, nonPrimaryKeyColumns, modelPackage, entityName);

        // 根据主键查询
        sb.append("    <!-- 根据主键查询 -->\n");
        sb.append("    <select id=\"selectByPrimaryKey\" parameterType=\"").append(getPrimaryKeyJavaType(columns, primaryKeyColumn)).append("\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        WHERE ").append(primaryKeyColumn).append(" = #{").append(primaryKeyProperty).append("}\n");
        sb.append("    </select>\n\n");
    }
    
    private static void generateInsertSelective(StringBuilder sb, String tableName, List<ColumnInfo> columns, String modelPackage, String entityName) {
        sb.append("    <!-- 选择性插入记录 -->\n");
        sb.append("    <insert id=\"insertSelective\" parameterType=\"").append(modelPackage).append(".").append(entityName).append("\">\n");
        sb.append("        INSERT INTO ").append(tableName).append("\n");
        sb.append("        <trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\">\n");
        for (ColumnInfo column : columns) {
            sb.append("            <if test=\"").append(column.getJavaProperty()).append(" != null\">\n");
            sb.append("                ").append(column.getColumnName()).append(",\n");
            sb.append("            </if>\n");
        }
        sb.append("        </trim>\n");
        sb.append("        <trim prefix=\"VALUES (\" suffix=\")\" suffixOverrides=\",\">\n");
        for (ColumnInfo column : columns) {
            sb.append("            <if test=\"").append(column.getJavaProperty()).append(" != null\">\n");
            sb.append("                #{").append(column.getJavaProperty()).append(",jdbcType=")
              .append(column.getMybatisJdbcType()).append("},\n");
            sb.append("            </if>\n");
        }
        sb.append("        </trim>\n");
        sb.append("    </insert>\n\n");
    }
    
    private static void generateUpdateByPrimaryKey(StringBuilder sb, String tableName, String primaryKeyProperty,
                                                 String primaryKeyColumn, List<ColumnInfo> nonPrimaryKeyColumns, String modelPackage, String entityName) {
        sb.append("    <!-- 根据主键更新 -->\n");
        sb.append("    <update id=\"updateByPrimaryKey\" parameterType=\"").append(modelPackage).append(".").append(entityName).append("\">\n");
        sb.append("        UPDATE ").append(tableName).append(" SET\n");
        for (int i = 0; i < nonPrimaryKeyColumns.size(); i++) {
            ColumnInfo column = nonPrimaryKeyColumns.get(i);
            sb.append("            ").append(column.getColumnName()).append(" = #{")
              .append(column.getJavaProperty()).append(",jdbcType=")
              .append(column.getMybatisJdbcType()).append("}");
            if (i < nonPrimaryKeyColumns.size() - 1) sb.append(",");
            sb.append("\n");
        }
        sb.append("        WHERE ").append(primaryKeyColumn).append(" = #{").append(primaryKeyProperty).append("}\n");
        sb.append("    </update>\n\n");
    }
    
    private static void generateUpdateByPrimaryKeySelective(StringBuilder sb, String tableName, String primaryKeyProperty,
                                                          String primaryKeyColumn, List<ColumnInfo> nonPrimaryKeyColumns, String modelPackage, String entityName) {
        sb.append("    <!-- 根据主键选择性更新 -->\n");
        sb.append("    <update id=\"updateByPrimaryKeySelective\" parameterType=\"").append(modelPackage).append(".").append(entityName).append("\">\n");
        sb.append("        UPDATE ").append(tableName).append("\n");
        sb.append("        <set>\n");
        for (ColumnInfo column : nonPrimaryKeyColumns) {
            sb.append("            <if test=\"").append(column.getJavaProperty()).append(" != null\">\n");
            sb.append("                ").append(column.getColumnName()).append(" = #{")
              .append(column.getJavaProperty()).append(",jdbcType=")
              .append(column.getMybatisJdbcType()).append("},\n");
            sb.append("            </if>\n");
        }
        sb.append("        </set>\n");
        sb.append("        WHERE ").append(primaryKeyColumn).append(" = #{").append(primaryKeyProperty).append("}\n");
        sb.append("    </update>\n\n");
    }
    
    private static String getPrimaryKeyJdbcType(List<ColumnInfo> columns, String primaryKeyColumn) {
        return columns.stream()
                .filter(col -> col.getColumnName().equals(primaryKeyColumn))
                .findFirst()
                .map(ColumnInfo::getMybatisJdbcType)
                .orElse("BIGINT");
    }
    
    private static String getPrimaryKeyJavaType(List<ColumnInfo> columns, String primaryKeyColumn) {
        String javaType = columns.stream()
                .filter(col -> col.getColumnName().equals(primaryKeyColumn))
                .findFirst()
                .map(ColumnInfo::getJavaType)
                .orElse("Long");

        // 返回完整的Java类型名称
        switch (javaType) {
            case "Long":
                return "java.lang.Long";
            case "Integer":
                return "java.lang.Integer";
            case "String":
                return "java.lang.String";
            case "Date":
                return "java.util.Date";
            case "BigDecimal":
                return "java.math.BigDecimal";
            default:
                return "java.lang." + javaType;
        }
    }
    
    private static void generateBatchOperations(StringBuilder sb, String tableName, String entityName,
                                               String primaryKeyProperty, String primaryKeyColumn,
                                               List<ColumnInfo> columns, List<ColumnInfo> nonPrimaryKeyColumns) {
        // 批量插入
        sb.append("    <!-- 批量插入 -->\n");
        sb.append("    <insert id=\"batchInsert\" parameterType=\"java.util.List\">\n");
        sb.append("        INSERT INTO ").append(tableName).append(" (\n");
        sb.append("            ");
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) sb.append(", ");
            if (i > 0 && i % 5 == 0) sb.append("\n            ");
            sb.append(columns.get(i).getColumnName());
        }
        sb.append("\n        ) VALUES\n");
        sb.append("        <foreach collection=\"records\" item=\"item\" separator=\",\">\n");
        sb.append("            (\n");
        sb.append("                ");
        for (int i = 0; i < columns.size(); i++) {
            if (i > 0) sb.append(", ");
            if (i > 0 && i % 5 == 0) sb.append("\n                ");
            ColumnInfo column = columns.get(i);
            sb.append("#{item.").append(column.getJavaProperty()).append(",jdbcType=")
              .append(column.getMybatisJdbcType()).append("}");
        }
        sb.append("\n            )\n");
        sb.append("        </foreach>\n");
        sb.append("    </insert>\n\n");

        // 批量删除
        sb.append("    <!-- 批量删除 -->\n");
        sb.append("    <delete id=\"batchDeleteByIds\" parameterType=\"java.util.List\">\n");
        sb.append("        DELETE FROM ").append(tableName).append("\n");
        sb.append("        WHERE ").append(primaryKeyColumn).append(" IN\n");
        sb.append("        <foreach collection=\"ids\" item=\"id\" open=\"(\" close=\")\" separator=\",\">\n");
        sb.append("            #{id}\n");
        sb.append("        </foreach>\n");
        sb.append("    </delete>\n\n");

        // 批量更新
        sb.append("    <!-- 批量更新 -->\n");
        sb.append("    <update id=\"batchUpdate\" parameterType=\"java.util.List\">\n");
        sb.append("        <foreach collection=\"records\" item=\"item\" separator=\";\">\n");
        sb.append("            UPDATE ").append(tableName).append(" SET\n");
        for (int i = 0; i < nonPrimaryKeyColumns.size(); i++) {
            ColumnInfo column = nonPrimaryKeyColumns.get(i);
            sb.append("                ").append(column.getColumnName()).append(" = #{item.")
              .append(column.getJavaProperty()).append(",jdbcType=")
              .append(column.getMybatisJdbcType()).append("}");
            if (i < nonPrimaryKeyColumns.size() - 1) sb.append(",");
            sb.append("\n");
        }
        sb.append("            WHERE ").append(primaryKeyColumn).append(" = #{item.").append(primaryKeyProperty).append("}\n");
        sb.append("        </foreach>\n");
        sb.append("    </update>\n\n");
    }

    private static void generateConditionalQueries(StringBuilder sb, String tableName, String entityName, boolean hasBlobColumns) {
        // 条件查询
        sb.append("    <!-- 根据条件查询列表 -->\n");
        sb.append("    <select id=\"selectByCondition\" parameterType=\"java.util.Map\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 条件查询单个对象
        sb.append("    <!-- 根据条件查询单个对象 -->\n");
        sb.append("    <select id=\"selectOneByCondition\" parameterType=\"java.util.Map\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("        LIMIT 1\n");
        sb.append("    </select>\n\n");

        // 条件统计
        sb.append("    <!-- 根据条件统计数量 -->\n");
        sb.append("    <select id=\"countByCondition\" parameterType=\"java.util.Map\" resultType=\"long\">\n");
        sb.append("        SELECT COUNT(*) FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 多条件AND查询
        sb.append("    <!-- 多条件AND查询 -->\n");
        sb.append("    <select id=\"selectByMultipleConditions\" parameterType=\"java.util.Map\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"conditions != null\">\n");
        sb.append("                <foreach collection=\"conditions\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null and value != ''\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 范围查询
        sb.append("    <!-- 范围查询 -->\n");
        sb.append("    <select id=\"selectByRange\" parameterType=\"java.util.Map\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"field != null and field != ''\">\n");
        sb.append("                <if test=\"startValue != null\">\n");
        sb.append("                    AND ${field} &gt;= #{startValue}\n");
        sb.append("                </if>\n");
        sb.append("                <if test=\"endValue != null\">\n");
        sb.append("                    AND ${field} &lt;= #{endValue}\n");
        sb.append("                </if>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 模糊查询
        sb.append("    <!-- 模糊查询 -->\n");
        sb.append("    <select id=\"selectByLike\" parameterType=\"java.util.Map\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"field != null and field != '' and keyword != null and keyword != ''\">\n");
        sb.append("                ${field} LIKE CONCAT('%', #{keyword}, '%')\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // IN查询
        sb.append("    <!-- IN查询 -->\n");
        sb.append("    <select id=\"selectByIn\" parameterType=\"java.util.Map\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />");
        if (hasBlobColumns) {
            sb.append(",\n        <include refid=\"Blob_Column_List\" />");
        }
        sb.append("\n        FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"field != null and field != '' and values != null and values.size() > 0\">\n");
        sb.append("                ${field} IN\n");
        sb.append("                <foreach collection=\"values\" item=\"value\" open=\"(\" separator=\",\" close=\")\">\n");
        sb.append("                    #{value}\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 总数统计
        sb.append("    <!-- 总数统计 -->\n");
        sb.append("    <select id=\"countTotal\" parameterType=\"java.util.Map\" resultType=\"long\">\n");
        sb.append("        SELECT COUNT(*) FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null and value != ''\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");
    }

    private static void generateAggregateQueries(StringBuilder sb, String tableName) {
        // 求和查询
        sb.append("    <!-- 求和查询 -->\n");
        sb.append("    <select id=\"sumByColumn\" parameterType=\"java.util.Map\" resultType=\"java.lang.Double\">\n");
        sb.append("        SELECT SUM(${column}) FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 平均值查询
        sb.append("    <!-- 平均值查询 -->\n");
        sb.append("    <select id=\"avgByColumn\" parameterType=\"java.util.Map\" resultType=\"java.lang.Double\">\n");
        sb.append("        SELECT AVG(${column}) FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 最大值查询
        sb.append("    <!-- 最大值查询 -->\n");
        sb.append("    <select id=\"maxByColumn\" parameterType=\"java.util.Map\" resultType=\"java.lang.Object\">\n");
        sb.append("        SELECT MAX(${column}) FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 最小值查询
        sb.append("    <!-- 最小值查询 -->\n");
        sb.append("    <select id=\"minByColumn\" parameterType=\"java.util.Map\" resultType=\"java.lang.Object\">\n");
        sb.append("        SELECT MIN(${column}) FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("    </select>\n\n");

        // 分组统计
        sb.append("    <!-- 分组统计 -->\n");
        sb.append("    <select id=\"groupByColumn\" parameterType=\"java.util.Map\" resultType=\"java.util.Map\">\n");
        sb.append("        SELECT ${groupColumn}, COUNT(*) as count FROM ").append(tableName).append("\n");
        sb.append("        <where>\n");
        sb.append("            <if test=\"params != null\">\n");
        sb.append("                <foreach collection=\"params\" index=\"key\" item=\"value\">\n");
        sb.append("                    <if test=\"value != null\">\n");
        sb.append("                        AND ${key} = #{value}\n");
        sb.append("                    </if>\n");
        sb.append("                </foreach>\n");
        sb.append("            </if>\n");
        sb.append("        </where>\n");
        sb.append("        GROUP BY ${groupColumn}\n");
        sb.append("    </select>\n\n");
    }

    private static void generateDynamicQueries(StringBuilder sb) {
        // 动态查询
        sb.append("    <!-- 执行自定义SQL查询 -->\n");
        sb.append("    <select id=\"executeCustomQuery\" parameterType=\"java.util.Map\" resultType=\"java.util.Map\">\n");
        sb.append("        ${sql}\n");
        sb.append("    </select>\n\n");

        // 动态更新
        sb.append("    <!-- 执行自定义SQL更新 -->\n");
        sb.append("    <update id=\"executeCustomUpdate\" parameterType=\"java.util.Map\">\n");
        sb.append("        ${sql}\n");
        sb.append("    </update>\n\n");

        // 查询所有记录
        sb.append("    <!-- 查询所有记录 -->\n");
        sb.append("    <select id=\"selectAll\" resultMap=\"BaseResultMap\">\n");
        sb.append("        SELECT\n");
        sb.append("        <include refid=\"Base_Column_List\" />\n");
        sb.append("        FROM ${tableName}\n");
        sb.append("    </select>\n\n");

        // 统计总记录数
        sb.append("    <!-- 统计总记录数 -->\n");
        sb.append("    <select id=\"countAll\" resultType=\"long\">\n");
        sb.append("        SELECT COUNT(*) FROM ${tableName}\n");
        sb.append("    </select>\n\n");
    }
}
