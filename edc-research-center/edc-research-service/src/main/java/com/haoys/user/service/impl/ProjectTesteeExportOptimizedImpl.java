package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.file.ImageCompressionService;
import com.haoys.user.common.pdf.PDFTemplateUtil;
import com.haoys.user.common.pdf.ZipUtils;
import com.haoys.user.common.service.ImageCompressionCacheService;
import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.storge.cloud.OssStorageConfig;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.testee.ProjectExportFiledParam;
import com.haoys.user.domain.param.testee.ProjectExportFlowParam;
import com.haoys.user.domain.param.testee.ProjectExportFlowParam2;
import com.haoys.user.domain.param.testee.ProjectTesteeExportParam;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableBody;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDictionaryVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.mapper.ProjectTesteeExportMapper;
import com.haoys.user.mapper.ProjectVisitConfigMapper;
import com.haoys.user.model.Organization;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectTesteeExport;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectTesteeFileService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.HtmlUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * PDF导出服务 - 与原始方法完全一致的实现
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectTesteeExportOptimizedImpl {

    // PDF导出图片URL配置常量
    /**
     * PDF导出是否使用原始URL（true：使用原始URL，false：使用压缩URL）
     * 默认使用原始URL以确保图片质量
     */
    private static final boolean USE_ORIGINAL_IMAGE_URL = true;

    /**
     * PDF导出是否启用图片缓存（true：启用缓存，false：不缓存）
     * 默认不缓存图片，避免缓存一致性问题
     */
    private static final boolean ENABLE_IMAGE_CACHE = false;

    private final ProjectTesteeInfoService projectTesteeInfoService;
    private final ProjectTesteeResultService projectTesteeResultService;
    private final ProjectTesteeFileService projectTesteeFileService;
    private final ProjectBaseManageService projectBaseManageService;
    private final OrganizationService organizationService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final ProjectTesteeExportMapper projectTesteeExportMapper;
    private final ImageCompressionService imageCompressionService;
    private final ImageCompressionCacheService imageCompressionCacheService;
    private final ProjectVisitConfigMapper projectVisitConfigMapper;
    private final OssStorageConfig storageConfig;
    private final TemplateConfigService templateConfigService;


    /**
     * 直接从数据库获取访视信息，避免缓存导致的数据不一致
     * <AUTHOR>
     */
    private ProjectVisitVo getProjectVisitConfigDirectly(String visitId) {
        try {
            // 直接从数据库查询，绕过Redis缓存，确保数据一致性
            ProjectVisitConfig projectVisitConfig = projectVisitConfigMapper.selectByPrimaryKey(Long.parseLong(visitId));

            if (projectVisitConfig == null) {
                log.warn("访视信息不存在，visitId: {}", visitId);
                // 返回默认值，避免程序中断
                ProjectVisitVo defaultVo = new ProjectVisitVo();
                defaultVo.setVisitName("访视信息不存在");
                return defaultVo;
            }

            ProjectVisitVo projectVisitVo = new ProjectVisitVo();
            BeanUtils.copyProperties(projectVisitConfig, projectVisitVo);
            return projectVisitVo;
        } catch (Exception e) {
            log.error("获取访视信息失败，visitId: {}", visitId, e);
            // 返回默认值，避免程序中断
            ProjectVisitVo defaultVo = new ProjectVisitVo();
            defaultVo.setVisitName("访视信息获取失败");
            return defaultVo;
        }
    }

    /**
     * 增强的图片处理方法 - 支持配置化的URL选择和缓存控制
     * @param imageVo 图片对象，包含fileUrl和uploadPath
     * @param testeeCode 参与者编码，用于错误提示
     * @param testeeRealName 参与者姓名，用于错误提示
     * @return 根据配置返回原始URL或压缩URL
     */
    private String processImageWithCache(ProjectTesteeFormImageVo imageVo, String testeeCode, String testeeRealName) {
        if (imageVo == null) {
            log.warn("参与者 {}({}) 的图片对象为空", testeeRealName, testeeCode);
            return null;
        }

        if (imageVo.getFileUrl() == null && imageVo.getUploadPath() == null) {
            log.warn("参与者 {}({}) 的图片对象缺少URL信息，fileUrl和uploadPath都为空，图片ID: {}",
                    testeeRealName, testeeCode, imageVo.getId());
            return null;
        }

        // 优先使用uploadPath（上传路径），如果没有则使用fileUrl
        String originalImagePath = imageVo.getUploadPath();
        String originalImageUrl = imageVo.getFileUrl();

        // 确定实际使用的图片路径，优先使用uploadPath
        String actualImagePath = originalImagePath;
        if (actualImagePath == null || actualImagePath.trim().isEmpty()) {
            actualImagePath = originalImageUrl;
            log.debug("参与者 {}({}) uploadPath为空，使用fileUrl: {}", testeeRealName, testeeCode, originalImageUrl);
        }

        if (actualImagePath == null || actualImagePath.trim().isEmpty()) {
            log.warn("参与者 {}({}) 的图片路径和URL都为空", testeeRealName, testeeCode);
            return null;
        }

        // 根据配置决定是否使用原始URL
        if (USE_ORIGINAL_IMAGE_URL) {
            log.debug("参与者 {}({}) 使用原始图片URL: {}", testeeRealName, testeeCode, originalImageUrl);
            return originalImageUrl != null ? originalImageUrl : convertPathToUrl(actualImagePath);
        }

        // 使用压缩URL的逻辑
        return processImageCompression(imageVo, testeeCode, testeeRealName, actualImagePath, originalImageUrl);
    }

    /**
     * 处理图片压缩逻辑
     * @param imageVo 图片对象
     * @param testeeCode 参与者编码
     * @param testeeRealName 参与者姓名
     * @param actualImagePath 实际图片路径
     * @param originalImageUrl 原始图片URL
     * @return 压缩后的图片URL
     */
    private String processImageCompression(ProjectTesteeFormImageVo imageVo, String testeeCode, String testeeRealName,
                                         String actualImagePath, String originalImageUrl) {
        try {
            // 使用fileId作为缓存key，提高缓存效率和准确性
            String fileId = imageVo.getId() != null ? imageVo.getId().toString() : null;
            if (fileId == null) {
                log.warn("参与者 {}({}) 的图片文件ID为空，使用URL作为缓存key", testeeRealName, testeeCode);
                fileId = originalImageUrl != null ? originalImageUrl : actualImagePath;
            }

            // 根据配置决定是否使用缓存
            if (ENABLE_IMAGE_CACHE) {
                // 1. 先从缓存获取压缩后的图片URL（使用fileId作为缓存键）
                String cachedUrl = imageCompressionCacheService.getCompressedImageUrl(fileId);
                if (cachedUrl != null) {
                    log.debug("参与者 {}({}) 从缓存获取压缩图片URL: {}", testeeRealName, testeeCode, cachedUrl);
                    return cachedUrl;
                }
            }

            // 2. 进行压缩（优先使用uploadPath）
            String compressedFileUrl = imageCompressionService.compressExistingImage(actualImagePath, originalImageUrl);

            // 3. 如果启用缓存且压缩成功，缓存结果
            if (ENABLE_IMAGE_CACHE && compressedFileUrl != null && !compressedFileUrl.equals(originalImageUrl)) {
                imageCompressionCacheService.cacheCompressedImageUrl(fileId, compressedFileUrl);
                log.debug("参与者 {}({}) 压缩图片URL已缓存: {}", testeeRealName, testeeCode, compressedFileUrl);
                return compressedFileUrl;
            }

            // 4. 返回压缩URL或原始URL
            String finalUrl = compressedFileUrl != null ? compressedFileUrl :
                             (originalImageUrl != null ? originalImageUrl : convertPathToUrl(actualImagePath));
            log.debug("参与者 {}({}) 最终使用图片URL: {}", testeeRealName, testeeCode, finalUrl);
            return finalUrl;

        } catch (Exception e) {
            // 5. 压缩过程中出现异常，记录详细信息但不中断PDF生成
            log.error("参与者 {}({}) 的图片处理异常，路径: {}，使用原图片继续PDF生成",
                     testeeRealName, testeeCode, actualImagePath, e);
            return originalImageUrl != null ? originalImageUrl : actualImagePath;
        }
    }

    /**
     * 兼容旧版本的方法，使用URL进行处理 - 支持配置化的URL选择和缓存控制
     * @param originalImageUrl 原始图片URL
     * @param testeeCode 参与者编码，用于错误提示
     * @param testeeRealName 参与者姓名，用于错误提示
     * @return 根据配置返回原始URL或压缩URL
     */
    private String processImageWithCache(String originalImageUrl, String testeeCode, String testeeRealName) {
        if (originalImageUrl == null || originalImageUrl.trim().isEmpty()) {
            return originalImageUrl;
        }

        // 根据配置决定是否使用原始URL
        if (USE_ORIGINAL_IMAGE_URL) {
            log.debug("参与者 {}({}) 使用原始图片URL: {}", testeeRealName, testeeCode, originalImageUrl);
            return originalImageUrl;
        }

        // 使用压缩URL的逻辑
        try {
            // 根据配置决定是否使用缓存
            if (ENABLE_IMAGE_CACHE) {
                // 1. 先从缓存获取压缩后的图片URL
                String cachedUrl = imageCompressionCacheService.getCompressedImageUrl(originalImageUrl);
                if (cachedUrl != null) {
                    log.debug("参与者 {}({}) 从缓存获取压缩图片URL: {}", testeeRealName, testeeCode, cachedUrl);
                    return cachedUrl;
                }
            }

            // 2. 进行压缩
            String compressedUrl = imageCompressionService.compressExistingImage(originalImageUrl);

            // 3. 如果启用缓存且压缩成功，缓存结果
            if (ENABLE_IMAGE_CACHE && compressedUrl != null && !compressedUrl.equals(originalImageUrl)) {
                imageCompressionCacheService.cacheCompressedImageUrl(originalImageUrl, compressedUrl);
                log.debug("参与者 {}({}) 压缩图片URL已缓存: {}", testeeRealName, testeeCode, compressedUrl);
            }

            String finalUrl = compressedUrl != null ? compressedUrl : originalImageUrl;
            log.debug("参与者 {}({}) 最终使用图片URL: {}", testeeRealName, testeeCode, finalUrl);
            return finalUrl;

        } catch (Exception e) {
            // 4. 压缩过程中出现异常，记录详细信息但不中断PDF生成
            log.error("参与者 {}({}) 的图片处理异常，使用原图片继续PDF生成: {}", testeeRealName, testeeCode, originalImageUrl, e);
            return originalImageUrl;
        }
    }

    /**
     * 批量处理参与者图片压缩 - 使用缓存优化性能
     * @param testeeCode 参与者编码
     * @param testeeRealName 参与者姓名
     * @param imageUrls 图片URL列表
     * @return 压缩后的图片URL映射
     */
    private Map<String, String> batchProcessImagesWithCache(String testeeCode, String testeeRealName, List<String> imageUrls) {
        Map<String, String> result = new HashMap<>();

        if (imageUrls == null || imageUrls.isEmpty()) {
            return result;
        }

        try {
            // 1. 先从缓存批量获取
            Map<String, String> cachedImages = imageCompressionCacheService.batchGetCompressedImageUrls(imageUrls);
            result.putAll(cachedImages);

            // 2. 处理缓存中没有的图片
            List<String> uncachedUrls = imageUrls.stream()
                .filter(url -> !cachedImages.containsKey(url))
                .collect(Collectors.toList());

            if (!uncachedUrls.isEmpty()) {
                Map<String, String> newCompressed = new HashMap<>();

                for (String url : uncachedUrls) {
                    String compressedUrl = processImageWithCache(url, testeeCode, testeeRealName);
                    result.put(url, compressedUrl);

                    // 如果压缩成功，加入批量缓存
                    if (!compressedUrl.equals(url)) {
                        newCompressed.put(url, compressedUrl);
                    }
                }

                // 3. 批量缓存新压缩的图片
                if (!newCompressed.isEmpty()) {
                    imageCompressionCacheService.batchCacheCompressedImageUrls(newCompressed);
                }
            }

            log.debug("参与者 {}({}) 图片处理完成，总数: {}, 缓存命中: {}",
                     testeeRealName, testeeCode, imageUrls.size(), cachedImages.size());

        } catch (Exception e) {
            log.error("参与者 {}({}) 批量图片处理异常，使用原图片继续PDF生成",
                     testeeRealName, testeeCode, e);
            // 异常时返回原URL映射
            for (String url : imageUrls) {
                result.put(url, url);
            }
        }

        return result;
    }

    /**
     * 将本地文件路径转换为可访问的URL
     * @param localPath 本地文件路径
     * @return 可访问的URL
     */
    private String convertPathToUrl(String localPath) {
        if (localPath == null || localPath.trim().isEmpty()) {
            return null;
        }

        try {
            // 获取存储配置
            OssStorageConfig storageConfig = getStorageConfig();
            if (storageConfig == null) {
                log.warn("convertPathToUrl: 无法获取存储配置，返回原路径: {}", localPath);
                return localPath;
            }

            // 如果已经是HTTP URL，直接返回
            if (localPath.startsWith("http://") || localPath.startsWith("https://")) {
                return localPath;
            }

            // 如果是绝对路径，需要转换为相对路径
            String relativePath = localPath;
            String uploadFolder = storageConfig.getUploadFolder();

            if (uploadFolder != null && localPath.startsWith(uploadFolder)) {
                relativePath = localPath.substring(uploadFolder.length());
                if (relativePath.startsWith("/")) {
                    relativePath = relativePath.substring(1);
                }
            }

            // 构建访问URL
            String domain = storageConfig.getDomain();
            if (domain != null && !domain.trim().isEmpty()) {
                if (!domain.endsWith("/")) {
                    domain += "/";
                }
                return domain + relativePath;
            } else {
                // 如果没有配置域名，返回相对路径
                log.warn("convertPathToUrl: 域名配置为空，返回相对路径: {}", relativePath);
                return relativePath;
            }

        } catch (Exception e) {
            log.error("convertPathToUrl: 转换路径为URL失败，输入路径: {}", localPath, e);
            return localPath;
        }
    }

    /**
     * 获取存储配置
     * @return 存储配置对象
     */
    private OssStorageConfig getStorageConfig() {
        try {
            return SpringUtils.getBean(OssStorageConfig.class);
        } catch (Exception e) {
            log.warn("无法获取存储配置: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 智能PDF导出方法 - 默认使用高性能版本，提供标准版本作为备选
     *
     * 重构说明（2025-07-25）：
     * - 经过分析，高性能版本在数据加载、错误处理、性能优化等方面都优于标准版本
     * - 标准版本存在N+1查询问题，容易导致导出卡住（如参与者0069的问题）
     * - 默认使用高性能版本，确保导出的稳定性和性能
     *
     * <AUTHOR>
     */
    public void exportPdfOptimized(ProjectTesteeExportParam projectTesteeExportParam, Long exportFileId) {
        int testeeCount = projectTesteeExportParam.getTesteeIds().size();
        log.info("开始PDF导出，参与者数量: {}", testeeCount);

        // 默认使用高性能版本，除非明确指定使用标准版本
        if (shouldUseStandardVersion(projectTesteeExportParam)) {
            log.warn("使用标准版本处理 {} 个参与者的导出（不推荐，可能存在性能问题）", testeeCount);
            exportPdfStandard(projectTesteeExportParam, exportFileId);
            return;
        }

        log.info("使用高性能版本处理 {} 个参与者的导出（推荐）", testeeCount);
        exportPdfHighPerformance(projectTesteeExportParam, exportFileId);
    }

    /**
     * 判断是否应该使用标准版本（不推荐）
     * 默认返回false，即默认使用高性能版本
     */
    private boolean shouldUseStandardVersion(ProjectTesteeExportParam projectTesteeExportParam) {
        // 可以通过系统属性或配置文件控制是否强制使用标准版本
        String forceStandard = System.getProperty("pdf.export.force.standard", "false");
        if ("true".equalsIgnoreCase(forceStandard)) {
            log.warn("检测到强制使用标准版本的系统属性，将使用标准版本（不推荐）");
            return true;
        }

        // 默认不使用标准版本
        return false;
    }

    /**
     * 标准PDF导出方法（原始逻辑）- 增强错误处理和进度更新
     *
     * 注意：此方法存在N+1查询问题，可能导致性能问题和导出卡住
     * 推荐使用高性能版本 exportPdfHighPerformance
     *
     * <AUTHOR>
     */
    private void exportPdfStandard(ProjectTesteeExportParam projectTesteeExportParam, Long exportFileId) {
        log.info("执行标准PDF导出，参与者数量: {}", projectTesteeExportParam.getTesteeIds().size());
        log.warn("标准版本存在N+1查询问题，建议使用高性能版本");
        log.info("PDF导出配置 - 使用原始图片URL: {}, 启用图片缓存: {}", USE_ORIGINAL_IMAGE_URL, ENABLE_IMAGE_CACHE);

        // 导出的流程
        List<ProjectExportFlowParam> exportList = extracted(projectTesteeExportParam);
        
        // 获取项目信息
        ProjectVo projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());
        
        // 获取机构信息
        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectTesteeExportParam.getOrgId());
        Organization systemOrgInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
        
        // 获取参与者列表
        List<ProjectTesteeInfo> testeeList = projectTesteeInfoService.getProjectTesteeListByIds(
            projectTesteeExportParam.getProjectId(), 
            projectTesteeExportParam.getOrgId(), 
            projectTesteeExportParam.getTesteeIds()
        );
        
        Map<String, ByteArrayOutputStream> byteFileMap = new HashMap<>();
        
        // 样式定义 - 与原始方法完全一致
        String vsStyle = "margin-top:10px;margin-bottom:10px;font-size:14px;font-weight:bold;";
        String tableStyle = "width:100%;border-collapse:collapse;margin-top:10px;margin-bottom:10px;";
        String tableTheadAndTbodyStyle = "border:1px solid #000;";
        String tableTdStyle = "border:1px solid #000;padding:5px;text-align:center;font-size:12px;";
        String tableTrStyle = "border:1px solid #000;";
        
        int processedCount = 0;
        int totalCount = testeeList.size();

        // 初始化进度状态
        updateProgress(exportFileId, 0, totalCount, "开始标准PDF导出...");

        for (ProjectTesteeInfo testeeInfo : testeeList) {
            try {
                log.debug("开始处理参与者 {}({})", testeeInfo.getCode(), testeeInfo.getRealName());
                String statusMessage = String.format("正在处理参与者 %s (%d/%d)", testeeInfo.getCode(), processedCount + 1, totalCount);
                updateProgress(exportFileId, processedCount, totalCount, statusMessage);

                StringBuilder html = new StringBuilder(8192); // 预分配容量
                html.append("<div style='width:100%'>");

                // 处理每个访视
                for (int x = 0; x < exportList.size(); x++) {
                    ProjectExportFlowParam flow = exportList.get(x);
                    String visitId = flow.getVisitId();

                    // 获取访视信息 - 直接从数据库获取，避免缓存导致的数据不一致问题
                    ProjectVisitVo projectVisitVo = null;
                    try {
                        projectVisitVo = getProjectVisitConfigDirectly(visitId);
                        if (projectVisitVo == null) {
                            log.warn("参与者 {}({}) 的访视配置为空，visitId: {}", testeeInfo.getCode(), testeeInfo.getRealName(), visitId);
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("参与者 {}({}) 获取访视配置失败，visitId: {}，错误: {}",
                                 testeeInfo.getCode(), testeeInfo.getRealName(), visitId, e.getMessage());
                        continue;
                    }

                    html.append("<div style='width:100%'>");
                    html.append("<div style='margin-top:10px;margin-bottom:10px;font-size:16px;font-weight:bold;text-align:center;'>")
                        .append(projectVisitVo.getVisitName()).append("</div>");
                    
                    List<ProjectExportFiledParam> checkFormList = flow.getForms();
                    
                    // 处理每个表单
                    for (int f = 0; f < checkFormList.size(); f++) {
                        ProjectExportFiledParam form = checkFormList.get(f);
                        String line = "<div style='page-break-inside:avoid;'>";
                        html.append(line);
                        
                        // 设置表单变量信息 - 增强错误处理
                        List<String> variableIds = form.getVariableIds();
                        List<TemplateFormDetailVo> testeeVisitFormDetailRecordList = null;
                        try {
                            testeeVisitFormDetailRecordList = projectTesteeInfoService.getTesteeVisitFormDetail(
                                projectTesteeExportParam.getProjectId(), "", visitId, form.getFormId(), "", null,
                                projectOrgInfo.getId().toString(), testeeInfo.getId().toString(), "4", "0");
                        } catch (Exception e) {
                            log.error("参与者 {}({}) 获取表单详情失败，visitId: {}, formId: {}，错误: {}",
                                     testeeInfo.getCode(), testeeInfo.getRealName(), visitId, form.getFormId(), e.getMessage());
                            continue; // 跳过这个表单，继续处理下一个
                        }

                        if (CollectionUtil.isNotEmpty(testeeVisitFormDetailRecordList)) {
                            for (int i = 0; i < testeeVisitFormDetailRecordList.size(); i++) {
                                TemplateFormDetailVo templateFormDetailVo = testeeVisitFormDetailRecordList.get(i);
                                
                                // 字段值处理 - 与原始方法完全一致
                                if (StringUtils.isNotBlank(templateFormDetailVo.getFieldValue())) {
                                    String fileValue = HtmlUtils.htmlUnescape(templateFormDetailVo.getFieldValue());
                                    templateFormDetailVo.setFieldValue(fileValue);
                                }
                                
                                if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                                    // 普通变量处理 - 增强版本，支持图片压缩缓存和错误处理
                                    if (projectTesteeExportParam.getExportAll() || variableIds.contains(templateFormDetailVo.getId().toString())) {
                                        processNormalField(html, templateFormDetailVo, vsStyle, testeeInfo.getCode(), testeeInfo.getRealName());
                                        html.append(line);
                                    }
                                } else {
                                    // 表格变量处理 - 与原始方法完全一致
                                    processTableField(html, templateFormDetailVo, variableIds, projectTesteeExportParam, 
                                        vsStyle, tableStyle, tableTheadAndTbodyStyle, tableTdStyle, tableTrStyle);
                                }
                            }
                        }
                        html.append("</div>");
                        
                        // 只在不是最后一个表单时添加分页符
                        if (f < checkFormList.size() - 1) {
                            html.append("<div style='page-break-after: always;'></div>");
                        }
                    }

                    // 只在不是最后一个访视时添加分页符
                    if (x < exportList.size() - 1) {
                        html.append("<div style='page-break-after: always;'></div>");
                    }
                }
                html.append("</div>");
                
                // 生成PDF - 增强错误处理
                try {
                    Map<String, Object> data = new HashMap<>();
                    data.put("content", html.toString());
                    data.put("version", "1.0"); // 添加version字段
                    data.put("projectName", projectVo.getName()); // 添加项目名称
                    data.put("testeeCode", testeeInfo.getCode()); // 添加参与者编号
                    data.put("orgName", systemOrgInfo.getName()); // 添加机构名称

                    String header = projectVo.getName() + " - " + testeeInfo.getRealName() + "(" + testeeInfo.getCode() + ")";
                    ByteArrayOutputStream byteArrayOutputStream = PDFTemplateUtil.createPDF(header, data, "testee.ftl");

                    if (byteArrayOutputStream != null) {
                        byteFileMap.put(testeeInfo.getCode() + ".pdf", byteArrayOutputStream);
                        log.debug("参与者 {}({}) PDF生成成功", testeeInfo.getCode(), testeeInfo.getRealName());
                    } else {
                        log.error("参与者 {}({}) PDF生成失败：PDFTemplateUtil.createPDF返回null", testeeInfo.getCode(), testeeInfo.getRealName());
                    }
                } catch (Exception pdfException) {
                    log.error("参与者 {}({}) PDF生成异常：{}", testeeInfo.getCode(), testeeInfo.getRealName(), pdfException.getMessage(), pdfException);
                }

                // 更新进度计数
                processedCount++;

            } catch (Exception e) {
                log.error("处理参与者 {}({}) 时发生错误：{}", testeeInfo.getCode(), testeeInfo.getRealName(), e.getMessage(), e);
                // 即使失败也要更新进度，确保进度条正常推进
                processedCount++;
                String statusMessage = String.format("处理参与者 %s 失败，继续下一个 (%d/%d)", testeeInfo.getCode(), processedCount, totalCount);
                updateProgress(exportFileId, processedCount, totalCount, statusMessage);
            }
        }
        
        // 保存ZIP文件 - 增强错误处理
        try {
            updateProgress(exportFileId, totalCount, totalCount, "打包ZIP文件中...");
            if (byteFileMap.isEmpty()) {
                log.warn("没有成功生成任何PDF文件，导出失败");
                updateExportInfo(exportFileId, null, BusinessConfig.TESTEE_EXPORT_STATUS_2);
                return;
            }

            log.info("标准版本生成了 {} 个PDF文件，开始打包ZIP", byteFileMap.size());
            saveZipFile(projectTesteeExportParam, byteFileMap, exportFileId);
            updateProgress(exportFileId, totalCount, totalCount, "导出完成");
            log.info("标准PDF导出完成，生成文件数量: {}", byteFileMap.size());
        } catch (Exception e) {
            log.error("标准PDF导出保存ZIP文件失败", e);
            updateExportInfo(exportFileId, null, BusinessConfig.TESTEE_EXPORT_STATUS_2);
            throw new RuntimeException("保存ZIP文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理普通字段 - 增强版本，支持图片压缩缓存和错误处理
     */
    private void processNormalField(StringBuilder html, TemplateFormDetailVo templateFormDetailVo, String vsStyle,
                                  String testeeCode, String testeeRealName) {
        // 变量是单行文本，多行文本，日期，文字展示
        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormDetailVo.getType())
                || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailVo.getType())
                || BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(templateFormDetailVo.getType())) {
            // 变量是单选，多选，下拉单选
            List<TemplateFormDictionaryVo> dictionaryList = templateFormDetailVo.getTemplateFormDictionaryList();
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":");
            if (CollectionUtil.isNotEmpty(dictionaryList)) {
                html.append("<span style='margin-left:20px'></span>");
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailVo.getType())) {
                    String fieldValue = templateFormDetailVo.getFieldValue();
                    if (StringUtils.isNotBlank(fieldValue)) {
                        JSONArray objects = JSON.parseArray(fieldValue);
                        for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                            if (objects.contains(dictionaryVo.getId())) {
                                html.append("<input type='checkbox' checked='true' />").append(dictionaryVo.getName());
                            } else {
                                html.append("<input type='checkbox' />").append(dictionaryVo.getName());
                            }
                        }
                    }
                } else {
                    String fieldValue = templateFormDetailVo.getFieldValue();
                    for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                        html.append("<span style='margin-left:20px'></span>");
                        if (fieldValue != null && dictionaryVo.getId().equals(fieldValue)) {
                            html.append("<input type='checkbox' checked='true' />").append(dictionaryVo.getName());
                        } else {
                            html.append("<input type='checkbox' />").append(dictionaryVo.getName());
                        }
                    }
                }
            }
            html.append("</div>");
        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(templateFormDetailVo.getType())) {
            String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");
        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(templateFormDetailVo.getType())) {
            html.append("<div style='").append(vsStyle).append("' >").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getLabel()).append(":</span>");
            List<ProjectTesteeFormImageVo> variableImageList = templateFormDetailVo.getVariableImageList();
            if (CollectionUtil.isNotEmpty(variableImageList)) {
                html.append("<div style='width:100%'>");
                for (ProjectTesteeFormImageVo imageVo : variableImageList) {
                    // 使用增强的图片压缩服务，支持缓存和错误处理，优先使用上传路径
                    String compressedImageUrl = processImageWithCache(imageVo, testeeCode, testeeRealName);
                    if (compressedImageUrl != null && !compressedImageUrl.trim().isEmpty()) {
                        html.append("<img src='").append(compressedImageUrl).append("' style='width:150px;height:150px' ").append(" />");
                    } else {
                        log.warn("参与者 {}({}) 的图片上传字段图片URL为空，字段: {}，图片ID: {}",
                                testeeRealName, testeeCode, templateFormDetailVo.getLabel(),
                                imageVo != null ? imageVo.getId() : "null");
                    }
                }
                html.append("</div>");
            } else {
                log.debug("参与者 {}({}) 的图片上传字段variableImageList为空，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
            }
            html.append("</div>");
        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW.equals(templateFormDetailVo.getType())) {
            html.append("<div style='").append(vsStyle).append("' >").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getLabel()).append(":</span>");
            Object expandValue = templateFormDetailVo.getExpand();
            if (expandValue != null && !expandValue.toString().trim().isEmpty()) {
                try {
                    JSONObject object = JSON.parseObject(expandValue.toString());
                    if (object != null && object.containsKey("imgUrl")) {
                        html.append("<div style='width:100%'>");
                        // 使用增强的图片压缩服务，支持缓存和错误处理
                        String originalImageUrl = object.getString("imgUrl");
                        if (originalImageUrl != null && !originalImageUrl.trim().isEmpty()) {
                            String compressedImageUrl = processImageWithCache(originalImageUrl, testeeCode, testeeRealName);
                            html.append("<img src='").append(compressedImageUrl).append("' style='width:150px;height:150px' ").append(" />");
                        } else {
                            log.warn("参与者 {}({}) 的图片展示字段imgUrl为空，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
                        }
                        html.append("</div>");
                    } else {
                        log.warn("参与者 {}({}) 的图片展示字段JSON中缺少imgUrl属性，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
                    }
                } catch (Exception e) {
                    log.error("参与者 {}({}) 的图片展示字段JSON解析失败，字段: {}，原始数据: {}，错误: {}",
                             testeeRealName, testeeCode, templateFormDetailVo.getLabel(), expandValue, e.getMessage());
                }
            } else {
                log.debug("参与者 {}({}) 的图片展示字段expand为空，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
            }
            html.append("</div>");
        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_SLIDER.equals(templateFormDetailVo.getType())) {
            String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");
        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TEXT_VIEW.equals(templateFormDetailVo.getType())) {
            Object expandValue = templateFormDetailVo.getExpand();
            if (expandValue != null && expandValue != "") {
                JSONObject object = JSON.parseObject(expandValue.toString());
                String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
                html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append(object.get("textContent")).append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");
            }
        } else {
            String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");
        }
    }

    /**
     * 处理表格字段 - 与原始方法完全一致
     */
    private void processTableField(StringBuilder html, TemplateFormDetailVo templateFormDetailVo,
                                 List<String> variableIds, ProjectTesteeExportParam projectTesteeExportParam,
                                 String vsStyle, String tableStyle, String tableTheadAndTbodyStyle,
                                 String tableTdStyle, String tableTrStyle) {
        // 表格变量
        html.append("<div style='").append(vsStyle).append("'>").append(templateFormDetailVo.getLabel()).append("</div>");
        html.append("<table style='").append(tableStyle).append("' >");

        // 设置表头
        List<com.haoys.user.domain.vo.ecrf.TemplateTableVo> tableHeadRowList = templateFormDetailVo.getTableHeadRowList();
        if (CollectionUtil.isNotEmpty(tableHeadRowList)) {
            html.append("<thead style='").append(tableTheadAndTbodyStyle).append("'>");
            StringBuffer trS = new StringBuffer();
            for (com.haoys.user.domain.vo.ecrf.TemplateTableVo tableVo : tableHeadRowList) {
                if (projectTesteeExportParam.getExportAll() || variableIds.contains(tableVo.getId().toString())) {
                    trS.append("<th style='").append(tableTdStyle).append("'>").append(tableVo.getLabel()).append("</th>");
                }
            }
            if (trS.length() > 0) {
                html.append("<tr>");
                html.append(trS);
                html.append("</tr>");
            }
            html.append("</thead>");
        }

        List<ProjectTesteeTableBody.RowDataDesc> rows = templateFormDetailVo.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            html.append("<tbody style='").append(tableTheadAndTbodyStyle).append("'>");
            for (ProjectTesteeTableBody.RowDataDesc row : rows) {
                html.append("<tr style='").append(tableTrStyle).append("'>");
                List<ProjectTesteeTableBody.ProjectTesteeTableData> rowData = row.getRowData();
                if (CollectionUtil.isNotEmpty(rowData)) {
                    for (int i1 = 0; i1 < rowData.size() - 1; i1++) {
                        if (i1 == 0) {
                            // 第一行是编号 - 与原始方法保持一致的注释逻辑
                        } else {
                            String fieldValue = rowData.get(i1).getFieldValue() != null ? rowData.get(i1).getFieldValue() : "";
                            html.append("<td style='").append(tableTdStyle).append("'>").append(fieldValue).append("</td>");
                        }
                    }
                }
                html.append("</tr>");
            }
            html.append("</tbody>");
        }
        html.append("</table>");
    }

    /**
     * 提取导出配置 - 与原始方法完全一致
     */
    private List<ProjectExportFlowParam> extracted(ProjectTesteeExportParam param) {
        // 导出的流程
        List<ProjectExportFlowParam> exportList = new ArrayList<>();
        Map<Long, ProjectExportFlowParam> visitMap = new HashMap<>();
        Map<String, ProjectExportFiledParam> formMap = new HashMap<>();

        for (ProjectExportFlowParam2 projectExportFlowParam2 : param.getExportList()) {
            if (projectExportFlowParam2.getVisitId() == null) {
                continue;
            }

            ProjectExportFlowParam projectExportFlowParam = visitMap.get(projectExportFlowParam2.getVisitId());
            if (projectExportFlowParam == null) {
                projectExportFlowParam = new ProjectExportFlowParam();
                projectExportFlowParam.setVisitId(projectExportFlowParam2.getVisitId().toString());
                projectExportFlowParam.setForms(new ArrayList<>());
                visitMap.put(projectExportFlowParam2.getVisitId(), projectExportFlowParam);
                exportList.add(projectExportFlowParam);
            }

            String formKey = projectExportFlowParam2.getVisitId() + "_" + projectExportFlowParam2.getFormId();
            ProjectExportFiledParam projectExportFiledParam = formMap.get(formKey);
            if (projectExportFiledParam == null) {
                projectExportFiledParam = new ProjectExportFiledParam();
                projectExportFiledParam.setFormId(projectExportFlowParam2.getFormId().toString());
                projectExportFiledParam.setVariableIds(new ArrayList<>());
                formMap.put(formKey, projectExportFiledParam);
                projectExportFlowParam.getForms().add(projectExportFiledParam);
            }

            if (projectExportFlowParam2.getVariableId() != null) {
                projectExportFiledParam.getVariableIds().add(projectExportFlowParam2.getVariableId().toString());
            }
        }
        return exportList;
    }



    /**
     * 保存ZIP文件 - 优化版本，解决路径生成和资源管理问题
     */
    private void saveZipFile(ProjectTesteeExportParam projectTesteeExportParam,
                           Map<String, ByteArrayOutputStream> byteFileMap, Long exportFileId) {
        FileOutputStream fileOutputStream = null;
        try {
            // 1. 安全的路径构建 - 使用File.separator确保跨平台兼容性
            String uploadFolder = storageConfig.getUploadFolder();
            if (!uploadFolder.endsWith(File.separator)) {
                uploadFolder += File.separator;
            }

            String basePath = uploadFolder + projectTesteeExportParam.getProjectId();
            String fileName = projectTesteeExportParam.getFileUrlName() + Constants.EXPORT_FILE_SUFFIX_ZIP;
            String targetPath = basePath + File.separator + fileName;

            log.info("准备保存ZIP文件到路径: {}", targetPath);

            // 2. 安全的目录创建 - 使用Files.createDirectories确保原子性操作
            File baseDir = new File(basePath);
            if (!baseDir.exists()) {
                boolean created = baseDir.mkdirs();
                if (!created && !baseDir.exists()) {
                    throw new RuntimeException("无法创建目录: " + basePath);
                }
                log.info("成功创建目录: {}", basePath);
            }

            // 3. 验证目录权限
            if (!baseDir.canWrite()) {
                throw new RuntimeException("目录没有写权限: " + basePath);
            }

            // 4. 创建ZIP文件 - 改进资源管理
            ByteArrayOutputStream zipOutputStream = ZipUtils.downZipFile(byteFileMap);
            if (zipOutputStream == null) {
                throw new RuntimeException("ZIP文件生成失败");
            }

            // 5. 安全的文件写入 - 使用try-with-resources确保资源释放
            File targetFile = new File(targetPath);
            fileOutputStream = new FileOutputStream(targetFile);
            zipOutputStream.writeTo(fileOutputStream);
            fileOutputStream.flush();

            log.info("ZIP文件保存成功，文件大小: {} bytes", targetFile.length());

            // 6. 构建下载URL - 确保URL格式正确
            String downloadUrl = buildDownloadUrl(projectTesteeExportParam.getProjectId(), fileName);
            projectTesteeExportParam.setFileUrlName(downloadUrl);

            // 7. 更新数据库记录
            updateExportInfo(exportFileId, downloadUrl, BusinessConfig.TESTEE_EXPORT_STATUS_0);

            log.info("ZIP文件导出完成，下载URL: {}", downloadUrl);

        } catch (Exception e) {
            log.error("保存ZIP文件失败，项目ID: {}, 文件名: {}",
                     projectTesteeExportParam.getProjectId(),
                     projectTesteeExportParam.getFileUrlName(), e);
            updateExportInfo(exportFileId, null, BusinessConfig.TESTEE_EXPORT_STATUS_2);
            throw new RuntimeException("保存ZIP文件失败: " + e.getMessage(), e);
        } finally {
            // 8. 确保资源释放
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (Exception e) {
                    log.warn("关闭文件输出流失败", e);
                }
            }
        }
    }

    /**
     * 构建下载URL - 统一URL构建逻辑
     */
    private String buildDownloadUrl(String projectId, String fileName) {
        String viewUrl = storageConfig.getViewUrl();
        if (viewUrl == null || viewUrl.trim().isEmpty()) {
            log.warn("viewUrl配置为空，使用默认值");
            viewUrl = "http://localhost:8080/";
        }

        // 确保URL格式正确
        if (!viewUrl.endsWith("/")) {
            viewUrl += "/";
        }

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(viewUrl)
                  .append(Constants.SYSTEM_VIEW_PATH)
                  .append(projectId)
                  .append("/")
                  .append(fileName);

        return urlBuilder.toString();
    }

    /**
     * 更新参与者导出信息(已完成/导出失败) - 与原始方法完全一致
     */
    private void updateExportInfo(Long uuid, String fileUrl, Integer status) {
        ProjectTesteeExport exportInfo = getExportInfo(uuid);
        if (exportInfo != null) {
            if (BusinessConfig.TESTEE_EXPORT_STATUS_0.equals(status)) {
                exportInfo.setDownloadUrl(fileUrl);
            }
            exportInfo.setExportStatus(status);
            projectTesteeExportMapper.updateByPrimaryKey(exportInfo);
        }
    }

    /**
     * 获取导出信息 - 与原始方法完全一致
     */
    private ProjectTesteeExport getExportInfo(Long uuid) {
        return projectTesteeExportMapper.selectByPrimaryKey(uuid);
    }

    /**
     * 高性能PDF导出方法 - 解决N+1查询问题
     * <AUTHOR>
     */
    public void exportPdfHighPerformance(ProjectTesteeExportParam projectTesteeExportParam, Long exportFileId) {
        try {
            int totalTestees = projectTesteeExportParam.getTesteeIds().size();
            log.info("开始高性能PDF导出，参与者数量: {}", totalTestees);
            log.info("PDF导出配置 - 使用原始图片URL: {}, 启用图片缓存: {}", USE_ORIGINAL_IMAGE_URL, ENABLE_IMAGE_CACHE);

            // 初始化进度状态
            updateProgress(exportFileId, 0, totalTestees, "初始化导出任务...");

            // 1. 预加载所有基础数据，避免重复查询
            updateProgress(exportFileId, 0, totalTestees, "预加载基础数据中...");
            ExportDataCache dataCache = preloadExportData(projectTesteeExportParam);

            // 2. 批量加载所有参与者的表单数据，解决N+1查询问题
            updateProgress(exportFileId, 0, totalTestees, "批量加载表单数据中...");
            Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData =
                batchLoadTesteeFormData(projectTesteeExportParam, dataCache);

            // 3. 串行生成PDF，避免并发问题
            updateProgress(exportFileId, 0, totalTestees, "开始生成PDF文件...");
            Map<String, ByteArrayOutputStream> byteFileMap =
                generatePdfsSequentially(projectTesteeExportParam, dataCache, allTesteeFormData, exportFileId);

            // 4. 保存ZIP文件
            updateProgress(exportFileId, totalTestees, totalTestees, "打包ZIP文件中...");
            saveZipFile(projectTesteeExportParam, byteFileMap, exportFileId, dataCache);

            // 5. 完成导出
            updateProgress(exportFileId, totalTestees, totalTestees, "导出完成");
            log.info("高性能PDF导出完成，生成文件数量: {}", byteFileMap.size());

        } catch (Exception e) {
            log.error("高性能PDF导出失败", e);
            // 更新失败状态
            updateProgress(exportFileId, 0, projectTesteeExportParam.getTesteeIds().size(), "导出失败: " + e.getMessage());
            updateExportInfo(exportFileId, null, BusinessConfig.TESTEE_EXPORT_STATUS_2);
            throw new RuntimeException("PDF导出失败", e);
        }
    }

    /**
     * 数据缓存类 - 存储预加载的数据
     */
    private static class ExportDataCache {
        List<ProjectExportFlowParam> exportList;
        ProjectVo projectVo;
        ProjectOrgInfo projectOrgInfo;
        Organization systemOrgInfo;
        List<ProjectTesteeInfo> testeeList;
        Map<String, ProjectVisitVo> visitConfigMap;
        Map<String, TemplateFormConfigVo> formConfigMap; // 添加表单配置缓存
    }

    /**
     * 预加载所有基础数据，避免重复查询
     */
    private ExportDataCache preloadExportData(ProjectTesteeExportParam projectTesteeExportParam) {
        ExportDataCache cache = new ExportDataCache();

        // 一次性加载所有基础数据
        cache.exportList = extracted(projectTesteeExportParam);
        cache.projectVo = projectBaseManageService.getProjectViewInfo(projectTesteeExportParam.getProjectId());
        cache.projectOrgInfo = organizationService.getProjectOrgInfo(projectTesteeExportParam.getOrgId());
        cache.systemOrgInfo = organizationService.getSystemOrganizationInfo(cache.projectOrgInfo.getOrgId().toString());
        cache.testeeList = projectTesteeInfoService.getProjectTesteeListByIds(
            projectTesteeExportParam.getProjectId(), projectTesteeExportParam.getOrgId(), projectTesteeExportParam.getTesteeIds());

        // 预加载访视配置信息
        cache.visitConfigMap = new HashMap<>();
        cache.formConfigMap = new HashMap<>();

        for (ProjectExportFlowParam flow : cache.exportList) {
            String visitId = flow.getVisitId();
            if (!cache.visitConfigMap.containsKey(visitId)) {
                ProjectVisitVo visitVo = getProjectVisitConfigDirectly(visitId);
                cache.visitConfigMap.put(visitId, visitVo);
            }

            // 预加载表单配置信息
            for (ProjectExportFiledParam form : flow.getForms()) {
                String formId = form.getFormId();
                if (!cache.formConfigMap.containsKey(formId)) {
                    TemplateFormConfigVo formConfigVo = templateConfigService.getTemplateFormConfigBaseInfoByFormId(formId);
                    cache.formConfigMap.put(formId, formConfigVo);
                }
            }
        }

        return cache;
    }

    /**
     * 批量加载所有参与者的表单数据，避免N+1查询 - 超级优化版本
     */
    private Map<String, Map<String, List<TemplateFormDetailVo>>> batchLoadTesteeFormData(
            ProjectTesteeExportParam projectTesteeExportParam, ExportDataCache dataCache) {

        log.info("开始批量加载表单数据，参与者数量: {}, 表单数量: {}",
                dataCache.testeeList.size(),
                dataCache.exportList.stream().mapToInt(flow -> flow.getForms().size()).sum());

        Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData = new HashMap<>();

        // 1. 批量预加载所有表单模板配置，避免重复查询
        Map<String, List<TemplateFormDetailVo>> formTemplateCache = batchLoadFormTemplates(dataCache);

        // 2. 批量预加载所有参与者的表单结果数据
        Map<String, Map<String, ProjectTesteeResult>> allTesteeResults = batchLoadTesteeResults(projectTesteeExportParam, dataCache);

        // 3. 批量预加载所有图片数据
        Map<String, Map<String, List<ProjectTesteeFormImageVo>>> allTesteeImages = batchLoadTesteeImages(projectTesteeExportParam, dataCache);

        // 4. 组装数据，避免循环查询
        for (ProjectTesteeInfo testeeInfo : dataCache.testeeList) {
            String testeeId = testeeInfo.getId().toString();
            Map<String, List<TemplateFormDetailVo>> testeeFormData = new HashMap<>();

            Map<String, ProjectTesteeResult> testeeResults = allTesteeResults.getOrDefault(testeeId, new HashMap<>());
            Map<String, List<ProjectTesteeFormImageVo>> testeeImages = allTesteeImages.getOrDefault(testeeId, new HashMap<>());

            for (ProjectExportFlowParam flow : dataCache.exportList) {
                String visitId = flow.getVisitId();

                for (ProjectExportFiledParam form : flow.getForms()) {
                    String formKey = visitId + "_" + form.getFormId();
                    String formId = form.getFormId();

                    // 从缓存获取表单模板
                    List<TemplateFormDetailVo> templateList = formTemplateCache.get(formId);
                    if (templateList == null) {
                        log.warn("表单模板不存在: {}", formId);
                        continue;
                    }

                    // 深拷贝模板并填充数据
                    List<TemplateFormDetailVo> formDetailList = new ArrayList<>();
                    for (TemplateFormDetailVo template : templateList) {
                        TemplateFormDetailVo formDetail = deepCopyTemplateFormDetail(template);

                        // 填充参与者数据
                        String resultKey = visitId + "_" + formId + "_" + template.getId();
                        ProjectTesteeResult result = testeeResults.get(resultKey);
                        if (result != null) {
                            // 处理null值，设置为空字符串以提升显示友好性
                            String fieldValue = result.getFieldValue() != null ? result.getFieldValue() : "";
                            formDetail.setFieldValue(HtmlUtils.htmlUnescape(fieldValue));
                            formDetail.setTesteeResultId(result.getId());
                            formDetail.setUnitResultValue(result.getUnitValue());
                        }

                        // 填充图片数据
                        if (isImageType(template.getType())) {
                            String imageKey = visitId + "_" + formId + "_" + template.getId();
                            List<ProjectTesteeFormImageVo> images = testeeImages.get(imageKey);
                            if (images != null) {
                                formDetail.setVariableImageList(images);
                            }
                        }

                        formDetailList.add(formDetail);
                    }

                    testeeFormData.put(formKey, formDetailList);
                }
            }

            allTesteeFormData.put(testeeId, testeeFormData);
        }

        log.info("批量加载表单数据完成，总数据量: {}", allTesteeFormData.size());
        return allTesteeFormData;
    }

    /**
     * 批量加载表单模板配置，避免重复查询
     */
    private Map<String, List<TemplateFormDetailVo>> batchLoadFormTemplates(ExportDataCache dataCache) {
        Map<String, List<TemplateFormDetailVo>> formTemplateCache = new HashMap<>();

        Set<String> formIds = new HashSet<>();
        for (ProjectExportFlowParam flow : dataCache.exportList) {
            for (ProjectExportFiledParam form : flow.getForms()) {
                formIds.add(form.getFormId());
            }
        }

        log.info("批量加载表单模板，表单数量: {}", formIds.size());

        for (String formId : formIds) {
            try {
                List<TemplateFormDetailVo> templateList = templateConfigService.getTemplateFormDetailConfigListByFormId(
                    null, formId, null, "1", "1", "0");
                formTemplateCache.put(formId, templateList);
            } catch (Exception e) {
                log.error("加载表单模板失败，formId: {}", formId, e);
                formTemplateCache.put(formId, new ArrayList<>());
            }
        }

        return formTemplateCache;
    }

    /**
     * 批量加载所有参与者的表单结果数据
     */
    private Map<String, Map<String, ProjectTesteeResult>> batchLoadTesteeResults(
            ProjectTesteeExportParam projectTesteeExportParam, ExportDataCache dataCache) {

        Map<String, Map<String, ProjectTesteeResult>> allTesteeResults = new HashMap<>();

        log.info("批量加载参与者表单结果数据");

        for (ProjectTesteeInfo testeeInfo : dataCache.testeeList) {
            String testeeId = testeeInfo.getId().toString();
            Map<String, ProjectTesteeResult> testeeResults = new HashMap<>();

            try {
                // 一次性查询该参与者的所有表单结果
                List<ProjectTesteeResult> resultList = projectTesteeResultService.getTesteeAllFormResults(
                    projectTesteeExportParam.getProjectId(), testeeId);

                for (ProjectTesteeResult result : resultList) {
                    String key = result.getVisitId() + "_" + result.getFormId() + "_" + result.getFormDetailId();
                    testeeResults.put(key, result);
                }
            } catch (Exception e) {
                log.error("加载参与者表单结果失败，testeeId: {}", testeeId, e);
            }

            allTesteeResults.put(testeeId, testeeResults);
        }

        return allTesteeResults;
    }

    /**
     * 批量加载所有参与者的图片数据
     */
    private Map<String, Map<String, List<ProjectTesteeFormImageVo>>> batchLoadTesteeImages(
            ProjectTesteeExportParam projectTesteeExportParam, ExportDataCache dataCache) {

        Map<String, Map<String, List<ProjectTesteeFormImageVo>>> allTesteeImages = new HashMap<>();

        log.info("批量加载参与者图片数据");

        for (ProjectTesteeInfo testeeInfo : dataCache.testeeList) {
            String testeeId = testeeInfo.getId().toString();
            Map<String, List<ProjectTesteeFormImageVo>> testeeImages = new HashMap<>();

            try {
                // 一次性查询该参与者的所有图片
                List<ProjectTesteeFormImageVo> imageList = projectTesteeFileService.getTesteeAllFormImages(
                    projectTesteeExportParam.getProjectId(), testeeId);

                for (ProjectTesteeFormImageVo image : imageList) {
                    String key = image.getVisitId() + "_" + image.getFormId() + "_" + image.getResourceId();
                    testeeImages.computeIfAbsent(key, k -> new ArrayList<>()).add(image);
                }
            } catch (Exception e) {
                log.error("加载参与者图片数据失败，testeeId: {}", testeeId, e);
            }

            allTesteeImages.put(testeeId, testeeImages);
        }

        return allTesteeImages;
    }

    /**
     * 深拷贝表单模板详情
     */
    private TemplateFormDetailVo deepCopyTemplateFormDetail(TemplateFormDetailVo source) {
        TemplateFormDetailVo copy = new TemplateFormDetailVo();
        BeanUtils.copyProperties(source, copy);

        // 重置数据字段
        copy.setFieldValue(null);
        copy.setTesteeResultId(null);
        copy.setUnitResultValue(null);
        copy.setVariableImageList(null);

        return copy;
    }

    /**
     * 判断是否为图片类型字段
     */
    private boolean isImageType(String type) {
        return BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(type) ||
               BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW.equals(type) ||
               BusinessConfig.PROJECT_VISIT_CRF_FORM_FILE.equals(type);
    }

    /**
     * 串行生成PDF，避免并发问题
     */
    private Map<String, ByteArrayOutputStream> generatePdfsSequentially(
            ProjectTesteeExportParam projectTesteeExportParam,
            ExportDataCache dataCache,
            Map<String, Map<String, List<TemplateFormDetailVo>>> allTesteeFormData,
            Long exportFileId) {

        Map<String, ByteArrayOutputStream> byteFileMap = new HashMap<>();
        int totalTestees = dataCache.testeeList.size();
        int processedCount = 0;

        // 串行处理每个参与者，使用预加载的数据
        for (ProjectTesteeInfo testeeInfo : dataCache.testeeList) {
            try {
                StringBuilder html = new StringBuilder(8192); // 预分配容量

                Map<String, List<TemplateFormDetailVo>> testeeFormData = allTesteeFormData.get(testeeInfo.getId().toString());

                // 构建HTML内容
                buildHtmlContent(html, projectTesteeExportParam, dataCache, testeeFormData, testeeInfo);

                // 生成PDF
                Map<String, Object> data = createPdfData(dataCache, testeeInfo, html.toString());
                // 使用与原始方法一致的header格式，支持编号和姓名两边对齐
                String header = "编号:" + testeeInfo.getCode() + " 姓名:" + testeeInfo.getRealName();

                try {
                    ByteArrayOutputStream byteArrayOutputStream = PDFTemplateUtil.createPDF(header, data, "testee.ftl");
                    if (byteArrayOutputStream != null) {
                        byteFileMap.put(testeeInfo.getCode() + ".pdf", byteArrayOutputStream);
                        log.debug("参与者 {}({}) PDF生成成功", testeeInfo.getCode(), testeeInfo.getRealName());
                    } else {
                        log.error("参与者 {}({}) PDF生成失败：PDFTemplateUtil.createPDF返回null", testeeInfo.getCode(), testeeInfo.getRealName());
                    }
                } catch (Exception pdfException) {
                    log.error("参与者 {}({}) PDF生成异常：{}", testeeInfo.getCode(), testeeInfo.getRealName(), pdfException.getMessage(), pdfException);
                    // 记录详细的错误信息但不中断整个导出过程
                }

                // 更新进度 - 使用完整的进度更新方法
                processedCount++;
                String statusMessage = String.format("正在处理参与者 %s (%d/%d)", testeeInfo.getCode(), processedCount, totalTestees);
                updateProgress(exportFileId, processedCount, totalTestees, statusMessage);

                log.debug("完成参与者 {} 的PDF生成，进度: {}/{}", testeeInfo.getCode(), processedCount, totalTestees);

            } catch (Exception e) {
                log.error("生成参与者 {}({}) 的PDF失败：{}", testeeInfo.getCode(), testeeInfo.getRealName(), e.getMessage(), e);
                // 即使失败也要更新进度计数，确保进度条正常推进
                processedCount++;
                String statusMessage = String.format("处理参与者 %s 失败，继续下一个 (%d/%d)", testeeInfo.getCode(), processedCount, totalTestees);
                updateProgress(exportFileId, processedCount, totalTestees, statusMessage);
                // 继续处理其他参与者，不中断整个流程
            }
        }

        return byteFileMap;
    }

    /**
     * 构建HTML内容 - 使用预加载的数据，与原始方法保持完全一致的结构
     */
    private void buildHtmlContent(StringBuilder html, ProjectTesteeExportParam projectTesteeExportParam,
                                ExportDataCache dataCache, Map<String, List<TemplateFormDetailVo>> testeeFormData,
                                ProjectTesteeInfo testeeInfo) {

        // 样式定义 - 与原始方法完全一致
        String vsStyle = "font-family:SimSun;margin-top:5px;width:100%;";
        String line = "<div style='height: 5px;border-bottom: 1px solid  #b4adad;'></div>";
        String tableStyle = "border: 1px solid #b4adad;width: 100%;margin-bottom: 20px;background-color: transparent;border-collapse: collapse;border-spacing: 0;display: table;";
        String tableTheadAndTbodyStyle = "display: table-header-group;vertical-align: middle;border-color: inherit;";
        String tableTrStyle = "display: table-row;ertical-align: inherit;border-color: inherit;";
        String tableTdStyle = "font-family:SimSun;border-top: 0;border-bottom-width: 2px;border: 1px solid  #b4adad;vertical-align: bottom;padding: 8px;line-height: 1.42857143;text-align: left;display: table-cell;";

        // 使用预加载的数据构建HTML，保持与原始方法完全一致的结构
        for (int x = 0; x < dataCache.exportList.size(); x++) {
            ProjectExportFlowParam flow = dataCache.exportList.get(x);
            String visitId = flow.getVisitId();
            ProjectVisitVo projectVisitVo = dataCache.visitConfigMap.get(visitId); // 从缓存获取

            html.append("<div class='content'>");
            // 访视名称标题 - 与原始方法完全一致
            html.append("<h3 style='font-family:SimSun;'>").append("<span style='margin-left:20px'>").append(projectVisitVo.getVisitName()).append("</span>").append("</h3>");

            List<ProjectExportFiledParam> checkFormList = flow.getForms();
            if (CollectionUtil.isNotEmpty(checkFormList)) {
                for (int f = 0; f < checkFormList.size(); f++) {
                    ProjectExportFiledParam form = checkFormList.get(f);

                    // 获取表单配置信息
                    TemplateFormConfigVo templateFormConfigVo = dataCache.formConfigMap.get(form.getFormId());

                    html.append("<div class='form'>");
                    // 表单名称标题 - 与原始方法完全一致
                    html.append("<h4 style='font-family:SimSun;'>").append("<span style='margin-left:20px'>").append(templateFormConfigVo.getFormName()).append("</span>").append("</h4>");
                    html.append(line);

                    String formKey = visitId + "_" + form.getFormId();
                    List<TemplateFormDetailVo> formDetailList = testeeFormData.get(formKey); // 从预加载数据获取

                    if (CollectionUtil.isNotEmpty(formDetailList)) {
                        // 处理表单数据，使用增强版本，支持图片压缩缓存和错误处理
                        processFormDataOriginalStyle(html, formDetailList, form.getVariableIds(), projectTesteeExportParam,
                                                   vsStyle, line, tableStyle, tableTheadAndTbodyStyle, tableTrStyle, tableTdStyle,
                                                   testeeInfo.getCode(), testeeInfo.getRealName());
                    }

                    html.append("</div>");

                    // 只在不是最后一个表单时添加分页符
                    if (f < checkFormList.size() - 1) {
                        html.append("<div style='page-break-after: always;'></div>");
                    }
                }
            }
            html.append("</div>");

            // 只在不是最后一个访视时添加分页符
            if (x < dataCache.exportList.size() - 1) {
                html.append("<div style='page-break-after: always;'></div>");
            }
        }
    }

    /**
     * 处理表单数据 - 增强版本，支持图片压缩缓存和错误处理
     */
    private void processFormDataOriginalStyle(StringBuilder html, List<TemplateFormDetailVo> formDetailList,
                                            List<String> variableIds, ProjectTesteeExportParam projectTesteeExportParam,
                                            String vsStyle, String line, String tableStyle, String tableTheadAndTbodyStyle,
                                            String tableTrStyle, String tableTdStyle, String testeeCode, String testeeRealName) {

        for (TemplateFormDetailVo templateFormDetailVo : formDetailList) {
            // HTML转义处理 - 与原始方法一致
            if (StringUtils.isNotBlank(templateFormDetailVo.getFieldValue())) {
                String fileValue = HtmlUtils.htmlUnescape(templateFormDetailVo.getFieldValue());
                templateFormDetailVo.setFieldValue(fileValue);
            }

            if (!BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                // 普通变量处理 - 增强版本，支持图片压缩缓存和错误处理
                if (projectTesteeExportParam.getExportAll() || variableIds.contains(templateFormDetailVo.getId().toString())) {
                    processNormalFieldOriginalStyle(html, templateFormDetailVo, vsStyle, line, testeeCode, testeeRealName);
                }
            } else {
                // 表格变量处理 - 与原始方法完全一致
                processTableFieldOriginalStyle(html, templateFormDetailVo, variableIds, projectTesteeExportParam,
                                              vsStyle, tableStyle, tableTheadAndTbodyStyle, tableTdStyle, tableTrStyle);
            }
        }
    }

    /**
     * 处理普通字段 - 增强版本，支持图片压缩缓存和错误处理
     */
    private void processNormalFieldOriginalStyle(StringBuilder html, TemplateFormDetailVo templateFormDetailVo,
                                                String vsStyle, String line, String testeeCode, String testeeRealName) {

        // 变量是单行文本，多行文本，日期，文字展示
        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO.equals(templateFormDetailVo.getType())
                || BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailVo.getType())
                || BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT.equals(templateFormDetailVo.getType())) {

            // 变量是单选，多选，下拉单选
            List<TemplateFormDictionaryVo> dictionaryList = templateFormDetailVo.getTemplateFormDictionaryList();
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":");

            if (CollectionUtil.isNotEmpty(dictionaryList)) {
                html.append("<span style='margin-left:20px'></span>");
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX.equals(templateFormDetailVo.getType())) {
                    String fieldValue = templateFormDetailVo.getFieldValue();
                    if (StringUtils.isNotBlank(fieldValue)) {
                        fieldValue = fieldValue.replace("\\","");
                        JSONArray objects = JSON.parseArray(fieldValue);
                        for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                            if (objects.contains(dictionaryVo.getId())) {
                                html.append("<input type='checkbox' checked='true' />").append(dictionaryVo.getName());
                            } else {
                                html.append("<input type='checkbox' />").append(dictionaryVo.getName());
                            }
                        }
                    }
                } else {
                    String fieldValue = templateFormDetailVo.getFieldValue();
                    for (TemplateFormDictionaryVo dictionaryVo : dictionaryList) {
                        html.append("<span style='margin-left:20px'></span>");
                        if (fieldValue != null && dictionaryVo.getId().equals(fieldValue)) {
                            html.append("<input type='checkbox' checked='true' />").append(dictionaryVo.getName());
                        } else {
                            html.append("<input type='checkbox' />").append(dictionaryVo.getName());
                        }
                    }
                }
            }
            html.append("</div>");

        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER.equals(templateFormDetailVo.getType())) {
            String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");

        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE.equals(templateFormDetailVo.getType())) {
            html.append("<div style='").append(vsStyle).append("' >").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getLabel()).append(":</span>");
            List<ProjectTesteeFormImageVo> variableImageList = templateFormDetailVo.getVariableImageList();
            if (CollectionUtil.isNotEmpty(variableImageList)) {
                html.append("<div style='width:100%'>");
                for (ProjectTesteeFormImageVo imageVo : variableImageList) {
                    // 使用增强的图片压缩服务，支持缓存和错误处理，优先使用上传路径
                    String compressedImageUrl = processImageWithCache(imageVo, testeeCode, testeeRealName);
                    if (compressedImageUrl != null && !compressedImageUrl.trim().isEmpty()) {
                        html.append("<img src='").append(compressedImageUrl).append("' style='width:150px;height:150px' ").append(" />");
                    } else {
                        log.warn("参与者 {}({}) 的图片上传字段图片URL为空，字段: {}，图片ID: {}",
                                testeeRealName, testeeCode, templateFormDetailVo.getLabel(),
                                imageVo != null ? imageVo.getId() : "null");
                    }
                }
                html.append("</div>");
            } else {
                log.debug("参与者 {}({}) 的图片上传字段variableImageList为空，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
            }
            html.append("</div>");

        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_IMAGE_VIEW.equals(templateFormDetailVo.getType())) {
            html.append("<div style='").append(vsStyle).append("' >").append("<span style='margin-left:20px'>").append(templateFormDetailVo.getLabel()).append(":</span>");
            Object expandValue = templateFormDetailVo.getExpand();
            if (expandValue != null && !expandValue.toString().trim().isEmpty()) {
                try {
                    JSONObject object = JSON.parseObject(expandValue.toString());
                    if (object != null && object.containsKey("imgUrl")) {
                        html.append("<div style='width:100%'>");
                        // 使用增强的图片压缩服务，支持缓存和错误处理
                        String originalImageUrl = object.getString("imgUrl");
                        if (originalImageUrl != null && !originalImageUrl.trim().isEmpty()) {
                            String compressedImageUrl = processImageWithCache(originalImageUrl, testeeCode, testeeRealName);
                            html.append("<img src='").append(compressedImageUrl).append("' style='width:150px;height:150px' ").append(" />");
                        } else {
                            log.warn("参与者 {}({}) 的图片展示字段imgUrl为空，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
                        }
                        html.append("</div>");
                    } else {
                        log.warn("参与者 {}({}) 的图片展示字段JSON中缺少imgUrl属性，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
                    }
                } catch (Exception e) {
                    log.error("参与者 {}({}) 的图片展示字段JSON解析失败，字段: {}，原始数据: {}，错误: {}",
                             testeeRealName, testeeCode, templateFormDetailVo.getLabel(), expandValue, e.getMessage());
                }
            } else {
                log.debug("参与者 {}({}) 的图片展示字段expand为空，字段: {}", testeeRealName, testeeCode, templateFormDetailVo.getLabel());
            }
            html.append("</div>");

        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_SLIDER.equals(templateFormDetailVo.getType())) {
            String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");

        } else if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TEXT_VIEW.equals(templateFormDetailVo.getType())) {
            Object expandValue = templateFormDetailVo.getExpand();
            if (expandValue != null && expandValue != "") {
                JSONObject object = JSON.parseObject(expandValue.toString());
                String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
                html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append(object.get("textContent")).append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");
            }
        } else {
            String fieldValue = templateFormDetailVo.getFieldValue() != null ? templateFormDetailVo.getFieldValue() : "";
            html.append("<div style='").append(vsStyle).append("' >").append(templateFormDetailVo.getLabel()).append(":").append("<span style='margin-left:20px'>").append(fieldValue).append("</span>").append("</div>");
        }
        html.append(line);
    }

    /**
     * 创建PDF数据对象
     */
    private Map<String, Object> createPdfData(ExportDataCache dataCache, ProjectTesteeInfo testeeInfo, String htmlContent) {
        Map<String, Object> data = new HashMap<>();
        data.put("projectName", dataCache.projectVo.getName());
        data.put("testeeCode", testeeInfo.getCode());
        data.put("orgName", dataCache.systemOrgInfo.getName());
        data.put("content", htmlContent);

        // 添加版本信息（与原始方法保持一致）
        try {
            data.put("version", "版本号：V1 版本日期：" + DateUtil.getCurrentDate());
        } catch (Exception e) {
            log.warn("获取当前日期失败，使用默认版本信息", e);
            data.put("version", "版本号：V1 版本日期：未知");
        }

        return data;
    }

    /**
     * 处理表格字段 - 与原始方法完全一致的实现
     */
    private void processTableFieldOriginalStyle(StringBuilder html, TemplateFormDetailVo templateFormDetailVo,
                                               List<String> variableIds, ProjectTesteeExportParam projectTesteeExportParam,
                                               String vsStyle, String tableStyle, String tableTheadAndTbodyStyle,
                                               String tableTdStyle, String tableTrStyle) {

        // 表格变量处理 - 与原始方法完全一致
        html.append("<div style='").append(vsStyle).append("'>").append(templateFormDetailVo.getLabel()).append("</div>");
        html.append("<table style='").append(tableStyle).append("' >");

        // 设置表头
        List<TemplateTableVo> tableHeadRowList = templateFormDetailVo.getTableHeadRowList();
        if (CollectionUtil.isNotEmpty(tableHeadRowList)) {
            html.append("<thead style='").append(tableTheadAndTbodyStyle).append("'>");
            StringBuffer trS = new StringBuffer();
            for (TemplateTableVo tableVo : tableHeadRowList) {
                if (projectTesteeExportParam.getExportAll() || variableIds.contains(tableVo.getId().toString())) {
                    trS.append("<th style='").append(tableTdStyle).append("'>").append(tableVo.getLabel()).append("</th>");
                }
            }
            if (trS.length() > 0) {
                html.append("<tr style='").append(tableTrStyle).append("'>").append(trS).append("</tr>");
            }
            html.append("</thead>");
        }

        // 设置表格数据
        List<ProjectTesteeTableBody.RowDataDesc> rows = templateFormDetailVo.getRows();
        if (CollectionUtil.isNotEmpty(rows)) {
            html.append("<tbody style='").append(tableTheadAndTbodyStyle).append("'>");
            for (ProjectTesteeTableBody.RowDataDesc row : rows) {
                html.append("<tr style='").append(tableTrStyle).append("'>");
                List<ProjectTesteeTableBody.ProjectTesteeTableData> rowData = row.getRowData();
                if (CollectionUtil.isNotEmpty(rowData)) {
                    for (int i1 = 0; i1 < rowData.size() - 1; i1++) {
                        if (i1 == 0) {
                            // 第一行是编号 - 跳过处理
                        } else {
                            // 与原始方法完全一致 - 直接输出表格数据，不进行变量ID过滤
                            ProjectTesteeTableBody.ProjectTesteeTableData tableData = rowData.get(i1);
                            String fieldValue = (tableData != null && tableData.getFieldValue() != null) ? tableData.getFieldValue() : "";
                            html.append("<td style='").append(tableTdStyle).append("'>").append(fieldValue).append("</td>");
                        }
                    }
                }
                html.append("</tr>");
            }
            html.append("</tbody>");
        }
        html.append("</table>");
    }

    /**
     * 更新导出进度 - 完整版本，同时更新所有进度相关字段
     * @param exportFileId 导出文件ID
     * @param processedCount 已处理数量
     * @param totalCount 总数量
     * @param currentStatus 当前状态描述
     * <AUTHOR>
     */
    private void updateProgress(Long exportFileId, int processedCount, int totalCount, String currentStatus) {
        try {
            // 计算进度百分比
            int progressPercent = totalCount > 0 ? (int) ((double) processedCount / totalCount * 100) : 0;

            ProjectTesteeExport exportRecord = new ProjectTesteeExport();
            exportRecord.setId(exportFileId);
            exportRecord.setProgressPercent(progressPercent);
            exportRecord.setProcessedCount(processedCount);
            exportRecord.setTotalCount(totalCount);
            exportRecord.setCurrentStatus(currentStatus);

            projectTesteeExportMapper.updateByPrimaryKeySelective(exportRecord);

            log.debug("更新导出进度: {}/{} ({}%) - {}", processedCount, totalCount, progressPercent, currentStatus);
        } catch (Exception e) {
            log.warn("更新导出进度失败: {}", e.getMessage());
        }
    }

    /**
     * 更新导出进度 - 简化版本，只更新进度百分比
     * @param exportFileId 导出文件ID
     * @param progressPercent 进度百分比
     * <AUTHOR>
     */
    private void updateProgressPercent(Long exportFileId, int progressPercent) {
        try {
            ProjectTesteeExport exportRecord = new ProjectTesteeExport();
            exportRecord.setId(exportFileId);
            exportRecord.setProgressPercent(progressPercent);
            projectTesteeExportMapper.updateByPrimaryKeySelective(exportRecord);
        } catch (Exception e) {
            log.warn("更新导出进度失败: {}", e.getMessage());
        }
    }

    /**
     * 保存ZIP文件 - 高性能优化版本，解决路径生成和资源管理问题
     */
    private void saveZipFile(ProjectTesteeExportParam projectTesteeExportParam,
                           Map<String, ByteArrayOutputStream> byteFileMap,
                           Long exportFileId, ExportDataCache dataCache) {
        FileOutputStream fileOutputStream = null;
        try {
            log.info("保存ZIP文件，包含 {} 个PDF文件", byteFileMap.size());

            // 1. 安全的路径构建
            String uploadFolder = storageConfig.getUploadFolder();
            if (!uploadFolder.endsWith(File.separator)) {
                uploadFolder += File.separator;
            }

            String targetDir = uploadFolder + storageConfig.getRootPath() + File.separator + projectTesteeExportParam.getProjectId() + Constants.SYSTEM_VIEW_PDF_PATH;
            log.info("准备保存ZIP文件到目录: {}", targetDir);

            // 2. 安全的目录创建
            File dir = new File(targetDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created && !dir.exists()) {
                    throw new RuntimeException("无法创建目录: " + targetDir);
                }
                log.info("成功创建目录: {}", targetDir);
            }

            // 3. 验证目录权限
            if (!dir.canWrite()) {
                throw new RuntimeException("目录没有写权限: " + targetDir);
            }

            // 4. 生成安全的文件名 - 避免特殊字符
            String projectName = dataCache.projectVo.getName();
            if (projectName == null || projectName.trim().isEmpty()) {
                projectName = "项目导出";
            }
            // 移除文件名中的特殊字符
            projectName = projectName.replaceAll("[\\\\/:*?\"<>|]", "_");

            String fileName = projectName + "_参与者导出_" + DateUtil.getCurrentdate() + Constants.EXPORT_FILE_SUFFIX_ZIP;
            String targetPath = targetDir + fileName;

            log.info("生成文件名: {}", fileName);

            // 5. 创建ZIP文件 - 改进资源管理
            ByteArrayOutputStream zipOutputStream = ZipUtils.downZipFile(byteFileMap);
            if (zipOutputStream == null) {
                throw new RuntimeException("ZIP文件生成失败");
            }

            // 6. 安全的文件写入
            File targetFile = new File(targetPath);
            fileOutputStream = new FileOutputStream(targetFile);
            zipOutputStream.writeTo(fileOutputStream);
            fileOutputStream.flush();

            log.info("ZIP文件保存成功，文件大小: {} bytes", targetFile.length());

            // 7. 构建下载URL - 改进URL构建逻辑
            String downloadUrl = buildHighPerformanceDownloadUrl(projectTesteeExportParam.getProjectId(), fileName, storageConfig.getRootPath());
            projectTesteeExportParam.setFileUrlName(downloadUrl);

            // 8. 更新数据库记录
            updateExportInfo(exportFileId, downloadUrl, BusinessConfig.TESTEE_EXPORT_STATUS_0);

            log.info("高性能ZIP文件导出完成，下载URL: {}", downloadUrl);

        } catch (Exception e) {
            log.error("保存ZIP文件失败，项目ID: {}, 项目名称: {}",
                     projectTesteeExportParam.getProjectId(),
                     dataCache != null && dataCache.projectVo != null ? dataCache.projectVo.getName() : "未知", e);
            updateExportInfo(exportFileId, null, BusinessConfig.TESTEE_EXPORT_STATUS_2);
            throw new RuntimeException("保存ZIP文件失败: " + e.getMessage(), e);
        } finally {
            // 9. 确保资源释放
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                } catch (Exception e) {
                    log.warn("关闭文件输出流失败", e);
                }
            }
        }
    }

    /**
     * 构建高性能版本的下载URL
     */
    private String buildHighPerformanceDownloadUrl(String projectId, String fileName, String rootPath) {
        String viewUrl = storageConfig.getViewUrl();
        if (viewUrl == null || viewUrl.trim().isEmpty()) {
            log.warn("viewUrl配置为空，使用默认值");
            viewUrl = "http://localhost:8080/";
        }

        // 确保URL格式正确
        if (!viewUrl.endsWith("/")) {
            viewUrl += "/";
        }

        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(viewUrl)
                  .append(Constants.SYSTEM_VIEW_PATH);

        // 处理rootPath
        if (rootPath != null && !rootPath.trim().isEmpty()) {
            if (rootPath.startsWith("/")) {
                rootPath = rootPath.substring(1);
            }
            if (!rootPath.endsWith("/")) {
                rootPath += "/";
            }
            urlBuilder.append(rootPath);
        }

        urlBuilder.append(projectId)
                  .append(Constants.SYSTEM_VIEW_PDF_PATH)
                  .append(fileName);

        return urlBuilder.toString();
    }

    /**
     * 获取PDF导出是否使用原始图片URL的配置
     * @return true：使用原始URL，false：使用压缩URL
     */
    public static boolean isUseOriginalImageUrl() {
        return USE_ORIGINAL_IMAGE_URL;
    }

    /**
     * 获取PDF导出是否启用图片缓存的配置
     * @return true：启用缓存，false：不缓存
     */
    public static boolean isEnableImageCache() {
        return ENABLE_IMAGE_CACHE;
    }

    /**
     * 获取当前PDF导出配置信息的描述
     * @return 配置信息描述字符串
     */
    public static String getExportConfigDescription() {
        return String.format("PDF导出配置 - 使用原始图片URL: %s, 启用图片缓存: %s",
                           USE_ORIGINAL_IMAGE_URL, ENABLE_IMAGE_CACHE);
    }
}
