package com.haoys.user.mapper;

import com.haoys.user.model.ProjectPatientResult;
import com.haoys.user.model.ProjectPatientResultExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectPatientResultMapper {
    long countByExample(ProjectPatientResultExample example);

    int deleteByExample(ProjectPatientResultExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectPatientResult record);

    int insertSelective(ProjectPatientResult record);

    List<ProjectPatientResult> selectByExample(ProjectPatientResultExample example);

    ProjectPatientResult selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectPatientResult record, @Param("example") ProjectPatientResultExample example);

    int updateByExample(@Param("record") ProjectPatientResult record, @Param("example") ProjectPatientResultExample example);

    int updateByPrimaryKeySelective(ProjectPatientResult record);

    int updateByPrimaryKey(ProjectPatientResult record);
}