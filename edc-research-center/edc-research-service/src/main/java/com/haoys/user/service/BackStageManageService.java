package com.haoys.user.service;

import java.util.List;
import java.util.Map;

public interface BackStageManageService {

    void updateProjectUserAuthForBackStage(Long userId);

    Map<String, Object> updateFormVariableFieldCode();
    
    Map<String, Object> getSystemQueryReturnRecord(String queryConditionValue);
    
    List<Map<String, Object>> getSystemQueryReturnTableRecord(String queryConditionValue);
}


