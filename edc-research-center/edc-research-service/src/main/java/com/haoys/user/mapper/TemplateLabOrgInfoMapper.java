package com.haoys.user.mapper;

import com.haoys.user.model.TemplateLabOrgInfo;
import com.haoys.user.model.TemplateLabOrgInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TemplateLabOrgInfoMapper {
    long countByExample(TemplateLabOrgInfoExample example);

    int deleteByExample(TemplateLabOrgInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateLabOrgInfo record);

    int insertSelective(TemplateLabOrgInfo record);

    List<TemplateLabOrgInfo> selectByExample(TemplateLabOrgInfoExample example);

    TemplateLabOrgInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateLabOrgInfo record, @Param("example") TemplateLabOrgInfoExample example);

    int updateByExample(@Param("record") TemplateLabOrgInfo record, @Param("example") TemplateLabOrgInfoExample example);

    int updateByPrimaryKeySelective(TemplateLabOrgInfo record);

    int updateByPrimaryKey(TemplateLabOrgInfo record);
    
    TemplateLabOrgInfo getTemplateLabOrgInfoByLabConfigIdAndOrgId(String labConfigId, String projectOrgId);
}