package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.crf.TemplateFormGroupFieldOptionParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormFieldOptionVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupFieldOptionVo;
import com.haoys.user.mapper.TemplateFormDetailOptionsMapper;
import com.haoys.user.model.TemplateFormDetailOptions;
import com.haoys.user.service.TemplateFormOptionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class TemplateFormOptionsServiceImpl extends BaseService implements TemplateFormOptionsService {


    @Autowired
    private TemplateFormDetailOptionsMapper templateFormDetailOptionsMapper;


    @Override
    public CommonPage<TemplateFormGroupFieldOptionVo> getTemplateFormGroupFieldOptionListForPage(String groupName, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<TemplateFormGroupFieldOptionVo> dataList = templateFormDetailOptionsMapper.getTemplateFormGroupFieldOptionListForPage(groupName);
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public CustomResult saveTemplateFormGroupFieldOption(TemplateFormGroupFieldOptionParam templateFormGroupFieldOptionParam) {
        CustomResult customResult = new CustomResult();
        if(templateFormGroupFieldOptionParam.getId() == null){
            TemplateFormDetailOptions templateFormOptions = new TemplateFormDetailOptions();
            BeanUtils.copyProperties(templateFormGroupFieldOptionParam, templateFormOptions);
            templateFormOptions.setId(SnowflakeIdWorker.getUuidValue());
            templateFormOptions.setCreateTime(new Date());
            templateFormOptions.setStatus("0");
            templateFormDetailOptionsMapper.insertSelective(templateFormOptions);
        }else{
            TemplateFormDetailOptions templateFormDetailOptions = templateFormDetailOptionsMapper.selectByPrimaryKey(templateFormGroupFieldOptionParam.getId());
            if(templateFormDetailOptions == null){
                customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                return customResult;
            }
            BeanUtils.copyProperties(templateFormGroupFieldOptionParam, templateFormDetailOptions);
            templateFormDetailOptions.setUpdateTime(new Date());
            templateFormDetailOptions.setUpdateUserId(templateFormGroupFieldOptionParam.getCreateUserId());
            templateFormDetailOptionsMapper.updateByPrimaryKey(templateFormDetailOptions);
        }
        return customResult;
    }

    @Override
    public CustomResult deleteFormGroupFieldOption(String parentId, String id, String updateUserId) {
        CustomResult customResult = new CustomResult();
        if(StringUtils.isNotEmpty(parentId) & StringUtils.isNotEmpty(id)){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_PARAMS_ERROR);
            return customResult;
        }

        if(StringUtils.isNotEmpty(id)){
            TemplateFormDetailOptions templateFormDetailOptions = templateFormDetailOptionsMapper.selectByPrimaryKey(id);
            if(templateFormDetailOptions == null){
                customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                return customResult;
            }
            templateFormDetailOptionsMapper.deleteByPrimaryKey(id);
        }

        if(StringUtils.isNotEmpty(parentId)){
            TemplateFormDetailOptions templateFormDetailOptions = templateFormDetailOptionsMapper.selectByPrimaryKey(parentId);
            if(templateFormDetailOptions == null){
                customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
                return customResult;
            }
            templateFormDetailOptionsMapper.deleteFormGroupFieldByParentId(parentId);
            templateFormDetailOptions.setStatus("1");
            templateFormDetailOptions.setUpdateUserId(updateUserId);
            templateFormDetailOptions.setUpdateTime(new Date());
            templateFormDetailOptionsMapper.updateByPrimaryKey(templateFormDetailOptions);
        }
        return customResult;
    }

    @Override
    public List<TemplateFormGroupFieldOptionVo> getFormGroupFieldOptionByParentId(String parentId) {
        return templateFormDetailOptionsMapper.getFormGroupFieldOptionByParentId(parentId);
    }

    @Override
    public TemplateFormFieldOptionVo getFormFieldOptionByParentId(String parentId) {
        TemplateFormFieldOptionVo templateFormFieldOptionVo = new TemplateFormFieldOptionVo();
        List<TemplateFormFieldOptionVo.FieldOption> optionList = new ArrayList<>();
        List<TemplateFormGroupFieldOptionVo> formGroupFieldOptionList = templateFormDetailOptionsMapper.getFormGroupFieldOptionByParentId(parentId);
        for (TemplateFormGroupFieldOptionVo templateFormGroupFieldOptionVo : formGroupFieldOptionList) {
            TemplateFormFieldOptionVo.FieldOption fieldOption = new TemplateFormFieldOptionVo.FieldOption();
            fieldOption.setId(templateFormGroupFieldOptionVo.getId());
            fieldOption.setParentId(templateFormGroupFieldOptionVo.getParentId());
            fieldOption.setLabel(templateFormGroupFieldOptionVo.getName());
            fieldOption.setValue(templateFormGroupFieldOptionVo.getName());
            fieldOption.setUnitValue(templateFormGroupFieldOptionVo.getUnitValue());
            fieldOption.setScoreValue(templateFormGroupFieldOptionVo.getScoreValue());
            optionList.add(fieldOption);
        }
        templateFormFieldOptionVo.setOptionList(optionList);
        return templateFormFieldOptionVo;
    }
}
