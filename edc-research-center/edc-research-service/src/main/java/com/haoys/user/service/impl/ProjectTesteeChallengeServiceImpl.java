package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.enums.ProjectChallengeEnum;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.project.ProjectChallengeParam;
import com.haoys.user.domain.param.project.ProjectChallengeQueryParam;
import com.haoys.user.domain.param.project.ProjectCloseChallengeParam;
import com.haoys.user.domain.param.project.ProjectCloseVariableChallengeParam;
import com.haoys.user.domain.param.project.ProjectReplyChallengeParam;
import com.haoys.user.domain.param.project.ProjectSystemChallengeParam;
import com.haoys.user.domain.vo.project.ProjectChallengeApplyVo;
import com.haoys.user.domain.vo.project.ProjectChallengeVo;
import com.haoys.user.domain.vo.project.SystemUserInfoExtendVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.mapper.ProjectTesteeChallengeMapper;
import com.haoys.user.mapper.ProjectTesteeChallengeReplyMapper;
import com.haoys.user.model.ProjectTesteeChallenge;
import com.haoys.user.model.ProjectTesteeChallengeExample;
import com.haoys.user.model.ProjectTesteeChallengeReply;
import com.haoys.user.model.ProjectTesteeChallengeReplyExample;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectTesteeChallengeService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProjectTesteeChallengeServiceImpl extends BaseService implements ProjectTesteeChallengeService {

    @Autowired
    private ProjectTesteeChallengeMapper projectTesteeChallengeMapper;
    @Autowired
    private ProjectTesteeChallengeReplyMapper projectTesteeChallengeReplyMapper;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ProjectUserService projectUserService;

    @Override
    public CommonPage<ProjectChallengeVo> getProjectChallengeListForPage(ProjectChallengeQueryParam param) {
        Page<Object> page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<ProjectChallengeVo> projectChallengeList = projectTesteeChallengeMapper.getProjectChallengeList(param);
        return commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, projectChallengeList);
    }

    /**
     * 个人质疑列表
     * @param param
     * @return
     */
    @Override
    public CommonPage<ProjectChallengeVo> getUserChallengeListForPage(ProjectChallengeQueryParam param) {
        Page<Object> page = PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<ProjectChallengeVo> projectChallengeList = projectTesteeChallengeMapper.getUserChallengeListForPage(param);
        return commonPageListWrapper(param.getPageNum(), param.getPageSize(), page, projectChallengeList);
    }

    /**
     * 个人质疑消息数量
     * @param param
     * @return
     */
    @Override
    public CommonResult<Object> getUserChallengeNum(ProjectChallengeQueryParam param) {
        Long l= projectTesteeChallengeMapper.getUserChallengeNum(param);
        return CommonResult.success(l);
    }


    @Override
    public CustomResult saveProjectChallenge(ProjectChallengeParam projectChallengeParam) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeChallenge projectTesteeChallenge = new ProjectTesteeChallenge();
        BeanUtils.copyProperties(projectChallengeParam, projectTesteeChallenge);
        projectTesteeChallenge.setCode(getProjectChallengeCode(projectChallengeParam.getProjectId().toString()));
        projectTesteeChallenge.setCreateTime(new Date());
        projectTesteeChallenge.setId(SnowflakeIdWorker.getUuid());
        projectTesteeChallenge.setStatus("0");
        projectTesteeChallenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
        projectTesteeChallengeMapper.insertSelective(projectTesteeChallenge);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    public String  getProjectChallengeCode(String projectId){
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        long count = projectTesteeChallengeMapper.countByExample(example);
        DecimalFormat df = new DecimalFormat("00000");
        String projectChallengeCode = df.format(count + 1);
        return projectChallengeCode;

    }

    @Override
    public CustomResult saveReplyProjectChallenge(ProjectReplyChallengeParam projectReplyChallengeParam) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeChallengeReply projectTesteeChallengeReply = new ProjectTesteeChallengeReply();
        BeanUtils.copyProperties(projectReplyChallengeParam, projectTesteeChallengeReply);
        projectTesteeChallengeReply.setId(SnowflakeIdWorker.getUuid());
        projectTesteeChallengeReply.setCreateTime(new Date());
        projectTesteeChallengeReply.setStatus("0");
        projectTesteeChallengeReplyMapper.insertSelective(projectTesteeChallengeReply);
        ProjectTesteeChallenge projectTesteeChallenge = projectTesteeChallengeMapper.selectByPrimaryKey(projectReplyChallengeParam.getChallengeId());
        projectTesteeChallenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
        projectTesteeChallengeMapper.updateByPrimaryKey(projectTesteeChallenge);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public CustomResult updateProjectChallengeStatus(ProjectCloseChallengeParam projectCloseChallengeParam) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeChallenge projectTesteeChallenge = projectTesteeChallengeMapper.selectByPrimaryKey(projectCloseChallengeParam.getId());
        if(projectTesteeChallenge == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        //需要验证是否可以关闭质疑；谁发起谁关闭 可以暂定同一个角色进行关闭
        projectTesteeChallenge.setCloseReason(projectCloseChallengeParam.getCloseReasonId());
        projectTesteeChallenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
        projectTesteeChallenge.setCloseTime(new Date());
        projectTesteeChallenge.setCloseUser(projectCloseChallengeParam.getCreateUser());
        projectTesteeChallenge.setUpdateTime(new Date());
        projectTesteeChallenge.setUpdateUser(projectCloseChallengeParam.getUpdateUser());
        projectTesteeChallengeMapper.updateByPrimaryKeySelective(projectTesteeChallenge);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public List<ProjectChallengeVo> getProjectChallengeListByVisitId(String projectId, String visitId, String testeeId) {
        List<ProjectChallengeVo> projectTesteeChallengeList = getProjectChallengeListByDetailId( projectId, visitId, null, null, testeeId);
        return projectTesteeChallengeList;
    }

    @Override
    public List<ProjectChallengeVo> getProjectChallengeListByFormId(String projectId, String visitId, String formId, String testeeId) {
        List<ProjectChallengeVo> projectTesteeChallengeList = getProjectChallengeListByDetailId( projectId, visitId, formId, null, testeeId);
        return projectTesteeChallengeList;
    }

    @Override
    public List<ProjectChallengeVo> getProjectChallengeListByDetailId(String projectId, String visitId, String formId, String formDetailId, String testeeId) {
        List<ProjectChallengeVo> dataList = new ArrayList();
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        if(StringUtils.isNotEmpty(formId)){
            criteria.andFormIdEqualTo(Long.parseLong(formId));
        }
        if(StringUtils.isNotEmpty(formDetailId)){
            criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        }
        if(StringUtils.isNotEmpty(testeeId)){
            criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        }
        List<ProjectTesteeChallenge> projectTesteeChallengeList = projectTesteeChallengeMapper.selectByExample(example);
        for (ProjectTesteeChallenge projectTesteeChallenge : projectTesteeChallengeList) {
            ProjectChallengeVo projectChallengeVo = new ProjectChallengeVo();
            BeanUtils.copyProperties(projectTesteeChallenge, projectChallengeVo);
            List<ProjectChallengeApplyVo> projectChallengeApplyList = getProjectChallengeApplyList(projectTesteeChallenge.getId().toString());
            projectChallengeVo.setChallengeApplyList(projectChallengeApplyList);
            dataList.add(projectChallengeVo);
        }
        return dataList;
    }

    @Override
    public List<ProjectTesteeChallenge> getProjectChallengeListByTableRowNo(String projectId, String visitId, String formId, String tableRowNo, String testeeId) {
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        if(StringUtils.isNotBlank(tableRowNo)){
            criteria.andFormResultTableRownoEqualTo(Long.parseLong(tableRowNo));
        }
        List<ProjectTesteeChallenge> projectTesteeChallengeList = projectTesteeChallengeMapper.selectByExample(example);
        return projectTesteeChallengeList;
    }

    @Override
    public List<ProjectChallengeVo> getProjectChallengeListByTableId(String projectId, String visitId, String formId, String testeeResultId, String testeeId) {
        List<ProjectChallengeVo> dataList = new ArrayList();
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        if(StringUtils.isNotBlank(testeeResultId)){
            criteria.andFormResultTableIdEqualTo(Long.parseLong(testeeResultId));
        }
        List<ProjectTesteeChallenge> projectTesteeChallengeList = projectTesteeChallengeMapper.selectByExample(example);
        for (ProjectTesteeChallenge projectTesteeChallenge : projectTesteeChallengeList) {
            ProjectChallengeVo projectChallengeVo = new ProjectChallengeVo();
            BeanUtils.copyProperties(projectTesteeChallenge, projectChallengeVo);
            List<ProjectChallengeApplyVo> projectChallengeApplyList = getProjectChallengeApplyList(projectTesteeChallenge.getId().toString());
            projectChallengeVo.setChallengeApplyList(projectChallengeApplyList);
            dataList.add(projectChallengeVo);
        }
        return dataList;
    }

    public List<ProjectChallengeVo> getProjectChallengeListByFormTableId(String projectId, String visitId, String formId, String formDetailId, String formTableId, String testeeId) {
        List<ProjectChallengeVo> dataList = new ArrayList();
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        //criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        criteria.andFormTableIdEqualTo(Long.parseLong(formTableId));
        List<ProjectTesteeChallenge> projectTesteeChallengeList = projectTesteeChallengeMapper.selectByExample(example);
        for (ProjectTesteeChallenge projectTesteeChallenge : projectTesteeChallengeList) {
            ProjectChallengeVo projectChallengeVo = new ProjectChallengeVo();
            BeanUtils.copyProperties(projectTesteeChallenge, projectChallengeVo);
            List<ProjectChallengeApplyVo> projectChallengeApplyList = getProjectChallengeApplyList(projectTesteeChallenge.getId().toString());
            projectChallengeVo.setChallengeApplyList(projectChallengeApplyList);
            dataList.add(projectChallengeVo);
        }
        return dataList;
    }

    //查询质疑回复列表
    public List<ProjectChallengeApplyVo> getProjectChallengeApplyList(String challengeId) {
        List<ProjectChallengeApplyVo> dataList = new ArrayList<>();
        ProjectTesteeChallengeReplyExample example = new ProjectTesteeChallengeReplyExample();
        ProjectTesteeChallengeReplyExample.Criteria criteria = example.createCriteria();
        criteria.andChallengeIdEqualTo(Long.parseLong(challengeId));
        List<ProjectTesteeChallengeReply> projectTesteeChallengeList = projectTesteeChallengeReplyMapper.selectByExampleWithBLOBs(example);
        for (ProjectTesteeChallengeReply projectTesteeChallengeReply : projectTesteeChallengeList) {
            ProjectChallengeApplyVo projectChallengeApplyVo = new ProjectChallengeApplyVo();
            BeanUtils.copyProperties(projectTesteeChallengeReply, projectChallengeApplyVo);
            SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectTesteeChallengeReply.getCreateUser());
            if(systemUserInfoExtendVo != null){
                projectChallengeApplyVo.setCreateUserName(systemUserInfoExtendVo.getRealName());
            }
            dataList.add(projectChallengeApplyVo);
        }
        return dataList;
    }

    @Override
    public List<ProjectChallengeVo> getChallengeList(String projectId, String visitId, String formId, String testeeId, String formResultId, String formTableId) {
        List<ProjectChallengeVo> dataList = new ArrayList();
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();

        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        if(StringUtils.isNotBlank(formResultId)){
            criteria.andFormResultIdEqualTo(Long.parseLong(formResultId));
        }
        if(StringUtils.isNotBlank(formTableId)){
            criteria.andFormResultTableIdEqualTo(Long.parseLong(formTableId));
        }
        example.setOrderByClause("create_time desc");
        List<ProjectTesteeChallenge> projectTesteeChallengeList = projectTesteeChallengeMapper.selectByExampleWithBLOBs(example);
        for (ProjectTesteeChallenge projectTesteeChallenge : projectTesteeChallengeList) {
            ProjectChallengeVo projectChallengeVo = new ProjectChallengeVo();
            BeanUtils.copyProperties(projectTesteeChallenge, projectChallengeVo);
            TemplateFormConfig templateFormConfig = templateConfigService.getTemplateFormConfigById(Long.parseLong(formId));
            if(templateFormConfig != null){
                projectTesteeChallenge.setFormName(templateFormConfig.getFormName());
            }
            SystemUserInfoExtendVo systemUserInfoExtendVo = systemUserInfoService.getSystemUserInfoByUserId(projectTesteeChallenge.getCreateUser());
            if(systemUserInfoExtendVo != null){
                projectChallengeVo.setCreateUserName(systemUserInfoExtendVo.getRealName());
                OrganizationVo projectUserPrimaryOrgInfo = organizationService.getProjectUserOrgInfo(projectId, projectTesteeChallenge.getCreateUser(), "", "");
                if(projectUserPrimaryOrgInfo != null){
                    projectChallengeVo.setUserOrgId(projectUserPrimaryOrgInfo.getId().toString());
                }
            }

            if(StringUtils.isNotEmpty(projectTesteeChallenge.getCloseUser())){
                ProjectUserInfoWrapper projectUserData = projectUserService.getProjectUserRoleInfoByUserId(projectId, "", projectTesteeChallenge.getCloseUser());
                if(projectUserData != null){
                    //projectChallengeVo.setCloseUserRoleCode(projectUserData.getRoleCode());
                }
                SystemUserInfoExtendVo umsAdminInfo = systemUserInfoService.getSystemUserInfoByUserId(projectTesteeChallenge.getCloseUser());
                if(umsAdminInfo != null){
                    projectChallengeVo.setCloseUser(umsAdminInfo.getRealName());
                }
            }
            projectChallengeVo.setCreateUserId(projectTesteeChallenge.getCreateUser());
            List<ProjectChallengeApplyVo> projectChallengeApplyList = getProjectChallengeApplyList(projectTesteeChallenge.getId().toString());
            projectChallengeVo.setChallengeApplyList(projectChallengeApplyList);
            dataList.add(projectChallengeVo);
        }
        return dataList;
    }

    @Override
    public CustomResult updateProjectChallenge(ProjectChallengeParam projectChallengeParam) {
        CustomResult customResult = new CustomResult();
        ProjectTesteeChallenge projectTesteeChallenge = projectTesteeChallengeMapper.selectByPrimaryKey(projectChallengeParam.getId());
        if(projectTesteeChallenge == null){
            customResult.setMessage(BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND);
            return customResult;
        }
        BeanUtils.copyProperties(projectChallengeParam, projectTesteeChallenge);
        projectTesteeChallengeMapper.updateByPrimaryKeyWithBLOBs(projectTesteeChallenge);
        return customResult;
    }

    @Override
    public int getProjectChallengeUnclosedCount(String projectId, String visitId, String testeeId, String orgIds) {
        Map<String,Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("replyCloseStatus", "006001,006002");
        if(StringUtils.isNotEmpty(visitId)){
            params.put("visitId", visitId);
        }
        if(StringUtils.isNotEmpty(orgIds)){
            params.put("orgId", orgIds);
        }
        if(StringUtils.isNotEmpty(testeeId)){
            params.put("testeeId", testeeId);
        }
        //不再查询当月质疑数据
        List<ProjectChallengeVo> projectChallengeList = projectTesteeChallengeMapper.getProjectChallengeListForPage(params);
        return projectChallengeList.size();
    }

    @Override
    public CustomResult saveProjectSystemChallenge(ProjectSystemChallengeParam projectSystemChallengeParam) {
        //查询系统质疑是否存在
        String projectId = projectSystemChallengeParam.getProjectId().toString();
        String visitId = projectSystemChallengeParam.getVisitId().toString();
        String formId = projectSystemChallengeParam.getFormId().toString();
        String formDetailId = projectSystemChallengeParam.getFormDetailId().toString();
        String testeeId = projectSystemChallengeParam.getTesteeId().toString();
        String queryMethod = projectSystemChallengeParam.getQueryMethod();
        String project_challenge_status = ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName();
        CustomResult customResult = new CustomResult();
        String formResultTableRowNumber = "";
        Long formResultTableRowno = projectSystemChallengeParam.getFormResultTableRowno();
        if(formResultTableRowno != null){
            formResultTableRowNumber = formResultTableRowno.toString();
        }
        List<ProjectChallengeVo> projectChallengeList = getProjectSystemChallageData(projectId, visitId, formId, testeeId, formDetailId, null, formResultTableRowNumber, queryMethod, project_challenge_status);
        if(CollectionUtil.isEmpty(projectChallengeList)){
            ProjectTesteeChallenge projectTesteeChallenge = new ProjectTesteeChallenge();
            BeanUtils.copyProperties(projectSystemChallengeParam, projectTesteeChallenge);
            projectTesteeChallenge.setCode(getProjectChallengeCode(projectSystemChallengeParam.getProjectId().toString()));
            projectTesteeChallenge.setCreateTime(new Date());
            projectTesteeChallenge.setId(SnowflakeIdWorker.getUuid());
            projectTesteeChallenge.setStatus("0");
            projectTesteeChallenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
            projectTesteeChallengeMapper.insertSelective(projectTesteeChallenge);
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public List<ProjectChallengeVo> getProjectSystemChallageData(String projectId, String visitId, String formId, String testeeId, String formDetailId, String formTableId, String rowNumber, String queryMethod, String closeStatus) {
        List<ProjectChallengeVo> dataList = new ArrayList();
        ProjectTesteeChallengeExample example = new ProjectTesteeChallengeExample();
        ProjectTesteeChallengeExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        if(StringUtils.isNotEmpty(testeeId)){
            criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        }
        criteria.andIfSystemEqualTo(true);
        if(StringUtils.isNotEmpty(formDetailId)){
            criteria.andFormDetailIdEqualTo(Long.parseLong(formDetailId));
        }
        if(StringUtils.isNotEmpty(formTableId)){
            criteria.andFormTableIdEqualTo(Long.parseLong(formTableId));
        }
        if(StringUtils.isNotEmpty(rowNumber)){
            criteria.andFormResultTableRownoEqualTo(Long.parseLong(rowNumber));
        }
        if(StringUtils.isNotEmpty(queryMethod)){
            criteria.andQueryMethodEqualTo(queryMethod);
        }
        if(StringUtils.isNotEmpty(closeStatus)){
            criteria.andReplyCloseStatusEqualTo(closeStatus);
        }
        List<ProjectTesteeChallenge> projectTesteeChallengeList = projectTesteeChallengeMapper.selectByExample(example);
        if(CollectionUtil.isNotEmpty(projectTesteeChallengeList)){
            ProjectTesteeChallenge projectTesteeChallenge = projectTesteeChallengeList.get(0);
            ProjectChallengeVo projectChallengeVo = new ProjectChallengeVo();
            BeanUtils.copyProperties(projectTesteeChallenge, projectChallengeVo);
            dataList.add(projectChallengeVo);
        }
        return dataList;
    }

    @Override
    public CustomResult saveBatchProjectSystemChallenge(List<ProjectSystemChallengeParam> projectSystemChallengeParams, String createUserId) {
        CustomResult customResult = new CustomResult();
        for (ProjectSystemChallengeParam projectSystemChallengeParam : projectSystemChallengeParams) {
            projectSystemChallengeParam.setCreateUser(createUserId);
            saveProjectSystemChallenge(projectSystemChallengeParam);
        }
        return customResult;
    }

    @Override
    public CustomResult updateBatchProjectChallengeStatus(List<ProjectCloseChallengeParam> dataList, String userId) {
        CustomResult customResult = new CustomResult();
        for (ProjectCloseChallengeParam projectCloseChallengeParam : dataList) {
            projectCloseChallengeParam.setCreateUser(userId);
            projectCloseChallengeParam.setUpdateUser(userId);
            updateProjectChallengeStatus(projectCloseChallengeParam);
        }
        return customResult;
    }

    @Override
    public CustomResult updateBatchProjectChallengeStatusByVariableId(List<ProjectCloseVariableChallengeParam> variableList, String userId) {
        CustomResult customResult = new CustomResult();
        if(CollectionUtil.isNotEmpty(variableList)){
            for (ProjectCloseVariableChallengeParam projectCloseVariableChallengeParam : variableList) {
                String projectId = projectCloseVariableChallengeParam.getProjectId();
                String visitId = projectCloseVariableChallengeParam.getVisitId();
                String formId = projectCloseVariableChallengeParam.getFormId();
                String formDetailId = projectCloseVariableChallengeParam.getFormDetailId();
                String formTableId = projectCloseVariableChallengeParam.getFormTableId();
                List<ProjectChallengeVo> projectChallengeListByDetailList = getProjectSystemChallageData(projectId, visitId, formId, null, formDetailId, formTableId, projectCloseVariableChallengeParam.getRowNumber(), projectCloseVariableChallengeParam.getQueryMethod(), ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_01.getName());
                for (ProjectChallengeVo projectChallengeVo : projectChallengeListByDetailList) {
                    ProjectTesteeChallenge projectTesteeChallenge = new ProjectTesteeChallenge();
                    BeanUtils.copyProperties(projectChallengeVo, projectTesteeChallenge);
                    projectTesteeChallenge.setCloseReason(projectCloseVariableChallengeParam.getCloseReasonId());
                    projectTesteeChallenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
                    projectTesteeChallenge.setUserRoleCode(projectCloseVariableChallengeParam.getUserRoleCode());
                    projectTesteeChallenge.setCloseTime(new Date());
                    projectTesteeChallenge.setCloseUser(userId);
                    projectTesteeChallenge.setUpdateTime(new Date());
                    projectTesteeChallenge.setUpdateUser(userId);
                    projectTesteeChallengeMapper.updateByPrimaryKeySelective(projectTesteeChallenge);
                }
            }
        }
        return customResult;
    }

    @Override
    public CommonResult<Object> SaveCustomerChallenge(ProjectTesteeChallenge challenge) {
        Long id = challenge.getId();
        if (id ==null){
           // 新增质疑信息
            // 生成质疑信息
            id=SnowflakeIdWorker.getUuid();
            challenge.setId(id);
            challenge.setIfSystem(false);
            challenge.setReceiveUserId(challenge.getReceiveUserId());
            challenge.setReceiveRule(challenge.getReceiveRule());
            challenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
            challenge.setStatus(BusinessConfig.VALID_STATUS);
            challenge.setCreateUser(SecurityUtils.getUserIdValue());
            challenge.setCreateTime(new Date());
            challenge.setPlatformId(SecurityUtils.getSystemPlatformId());
            challenge.setTenantId(SecurityUtils.getSystemTenantId());
            projectTesteeChallengeMapper.insert(challenge);
        }else {
            ProjectTesteeChallenge challenge0 = projectTesteeChallengeMapper.selectByPrimaryKey(id);
            if(!ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName().equals(challenge0.getReplyCloseStatus())){
                challenge0.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_02.getName());
                projectTesteeChallengeMapper.updateByPrimaryKey(challenge0);
            }
        }
        // 新增质疑回复的内容信息
        ProjectTesteeChallengeReply reply = new ProjectTesteeChallengeReply();
        reply.setId(SnowflakeIdWorker.getUuid());
        reply.setChallengeId(id);
        reply.setProjectId(challenge.getProjectId());
        reply.setContent(challenge.getContent());
        reply.setCreateTime(new Date());
        reply.setCreateUser(SecurityUtils.getUserIdValue());
        reply.setPlatformId(SecurityUtils.getSystemPlatformId());
        reply.setTenantId(SecurityUtils.getSystemTenantId());
        reply.setStatus(BusinessConfig.VALID_STATUS);
        int i = projectTesteeChallengeReplyMapper.insertSelective(reply);
        return i>0?CommonResult.success(id.toString()):CommonResult.failed();
    }

    /**
     * 获取手动质疑发送的消息
     * @param challengeId
     * @return
     */
    @Override
    public CommonResult<ProjectTesteeChallenge> selectCustomerChallenge(Long challengeId) {
        ProjectTesteeChallenge challenges = projectTesteeChallengeMapper.selectByPrimaryKey(challengeId);
        if (challenges!=null){
            List<ProjectChallengeApplyVo> list = projectTesteeChallengeReplyMapper.getApplyListByChallengeId(challenges.getId());
            if(CollectionUtil.isNotEmpty(list)){
                list.forEach(applyVo->{
                    if (applyVo.getCreateUser().equals(challenges.getCreateUser())){
                        applyVo.setIfChallengeUser(true);
                    }else {
                        applyVo.setIfChallengeUser(false);
                    }
                });
            }
            challenges.setReplyList(list);
            return CommonResult.success(challenges);

        }
        return CommonResult.success(new ProjectTesteeChallenge());
    }

    @Override
    public CommonResult<Object> closeChallenge(String challengeId) {
        ProjectTesteeChallenge challenge = projectTesteeChallengeMapper.selectByPrimaryKey(Long.parseLong(challengeId));
        if(challenge!=null){
            challenge.setReplyCloseStatus(ProjectChallengeEnum.PROJECT_CHALLENGE_STATUS_03.getName());
            projectTesteeChallengeMapper.updateByPrimaryKey(challenge);
            return CommonResult.success("");
        }
        return CommonResult.failed();
    }


}
