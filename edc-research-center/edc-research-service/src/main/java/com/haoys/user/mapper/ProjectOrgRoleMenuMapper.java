package com.haoys.user.mapper;

import com.haoys.user.model.ProjectOrgRoleMenu;
import com.haoys.user.model.ProjectOrgRoleMenuExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface ProjectOrgRoleMenuMapper {
    long countByExample(ProjectOrgRoleMenuExample example);

    int deleteByExample(ProjectOrgRoleMenuExample example);

    int insert(ProjectOrgRoleMenu record);

    int insertSelective(ProjectOrgRoleMenu record);

    List<ProjectOrgRoleMenu> selectByExample(ProjectOrgRoleMenuExample example);

    int updateByExampleSelective(@Param("record") ProjectOrgRoleMenu record, @Param("example") ProjectOrgRoleMenuExample example);

    int updateByExample(@Param("record") ProjectOrgRoleMenu record, @Param("example") ProjectOrgRoleMenuExample example);

    int deleteByRoleId(Long roleId);

    List<String> selectProjectOrgMenusListByRoleId(Long roleId);

    void batchSaveInsertProjectOrgRoleMenuList(List<ProjectOrgRoleMenu> projectOrgRoleMenuList);

    void batchSaveProjectOrgRoleMenuList(String projectId, String projectOrgRoleId, String projectOrgId, List<String> menuIds, String tenantId, String platformId);


}
