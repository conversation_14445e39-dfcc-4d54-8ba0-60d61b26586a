package com.haoys.user.mapper;

import com.haoys.user.model.ProjectConfigBase;
import com.haoys.user.model.ProjectConfigBaseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectConfigBaseMapper {
    long countByExample(ProjectConfigBaseExample example);

    int deleteByExample(ProjectConfigBaseExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ProjectConfigBase record);

    int insertSelective(ProjectConfigBase record);

    List<ProjectConfigBase> selectByExample(ProjectConfigBaseExample example);

    ProjectConfigBase selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ProjectConfigBase record, @Param("example") ProjectConfigBaseExample example);

    int updateByExample(@Param("record") ProjectConfigBase record, @Param("example") ProjectConfigBaseExample example);

    int updateByPrimaryKeySelective(ProjectConfigBase record);

    int updateByPrimaryKey(ProjectConfigBase record);
}