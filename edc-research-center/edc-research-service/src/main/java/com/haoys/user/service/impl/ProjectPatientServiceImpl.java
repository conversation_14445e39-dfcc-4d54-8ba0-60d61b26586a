package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.enums.PatientTaskComplateEnum;
import com.haoys.user.enums.PatientTaskRateEnum;
import com.haoys.user.enums.PatientTaskTypeEnum;
import com.haoys.user.enums.PatientTaskViewResultEnum;
import com.haoys.user.enums.ProjectTesteeReviewEnum;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DateUtil;
import com.haoys.user.common.util.PersistentDayUtil;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.PatientTaskSubmitParam;
import com.haoys.user.domain.param.project.ProjectTesteeResultParam;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableBody;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableWrapperVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableRowHeadVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.patient.PatientTaskCalendarDetailVo;
import com.haoys.user.domain.vo.patient.PatientTaskFormDetailVo;
import com.haoys.user.domain.vo.patient.PatientTaskFormValueVo;
import com.haoys.user.domain.vo.patient.PatientTaskFormVariableVo;
import com.haoys.user.domain.vo.patient.PatientTaskSummaryVo;
import com.haoys.user.domain.vo.patient.ProjectPatientBindResultVo;
import com.haoys.user.domain.vo.patient.ProjectPatientCalendarVo;
import com.haoys.user.domain.vo.project.ProjectPlanVo;
import com.haoys.user.domain.vo.project.ProjectTaskVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeConfigVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeVo;
import com.haoys.user.model.ProjectPatientCalendar;
import com.haoys.user.model.ProjectPatientPlan;
import com.haoys.user.model.ProjectPatientResult;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormTable;
import com.haoys.user.service.ProjectPatientCalendarService;
import com.haoys.user.service.ProjectPatientResultService;
import com.haoys.user.service.ProjectPatientService;
import com.haoys.user.service.ProjectPlanManageService;
import com.haoys.user.service.ProjectTaskManageService;
import com.haoys.user.service.ProjectTesteeConfigService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.service.TemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class ProjectPatientServiceImpl implements ProjectPatientService {


    @Autowired
    private ProjectPlanManageService projectPlanManageService;
    @Autowired
    private ProjectTaskManageService projectTaskManageService;
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private ProjectTesteeConfigService projectTesteeConfigService;
    @Autowired
    private TemplateConfigService templateConfigService;
    @Autowired
    private ProjectTesteeResultService projectTesteeResultService;
    @Autowired
    private ProjectTesteeTableService projectTesteeTableService;
    @Autowired
    private ProjectPatientCalendarService projectPatientCalendarService;
    @Autowired
    private ProjectPatientResultService projectPatientResultService;
    @Autowired
    private SystemUserInfoService systemUserInfoService;


    @Override
    public CustomResult savePatientTaskData(PatientTaskSubmitParam patientTaskSubmitParam) {
        CustomResult customResult = new CustomResult();
        boolean submitInitFormFlag = true;
        List<PatientTaskSubmitParam.PatientTaskDetail> dataList = patientTaskSubmitParam.getDataList();
        for (PatientTaskSubmitParam.PatientTaskDetail patientTaskDetail : dataList) {
            ProjectTesteeResultParam projectTesteeResultParam = new ProjectTesteeResultParam();
            projectTesteeResultParam.setTesteeId(patientTaskSubmitParam.getTesteeId());
            projectTesteeResultParam.setProjectId(patientTaskSubmitParam.getProjectId());
            projectTesteeResultParam.setVisitId(patientTaskDetail.getVisitId());
            projectTesteeResultParam.setFormId(patientTaskDetail.getFormId());
            ProjectTesteeResultParam.TesteeFormResultValue testeeFormResultValue = new ProjectTesteeResultParam.TesteeFormResultValue();
            if(patientTaskDetail.getFormResultId() != null){
                testeeFormResultValue.setTesteeResultId(patientTaskDetail.getFormResultId());
                submitInitFormFlag = false;
            }
            testeeFormResultValue.setFormDetailId(patientTaskDetail.getFormDetailId());
            testeeFormResultValue.setLabel(patientTaskDetail.getLabel());
            testeeFormResultValue.setFieldName(patientTaskDetail.getKey());
            testeeFormResultValue.setFieldValue(patientTaskDetail.getResult());
            List<ProjectTesteeResultParam.TesteeFormResultValue> testeeFormResultValueList = new ArrayList<>();
            testeeFormResultValueList.add(testeeFormResultValue);
            projectTesteeResultParam.setDataList(testeeFormResultValueList);
            projectTesteeResultParam.setOperator(patientTaskSubmitParam.getCreateUserId());
            projectTesteeInfoService.saveTesteeVisitFormDetail(projectTesteeResultParam);
        }

        ProjectPatientResult currentDatePatientTask = projectPatientResultService.getCurrentDatePatientTask(patientTaskSubmitParam.getProjectId().toString(), patientTaskSubmitParam.getPlanId().toString(), patientTaskSubmitParam.getTaskId().toString(), patientTaskSubmitParam.getTaskDate(), patientTaskSubmitParam.getTesteeId().toString());
        if(currentDatePatientTask == null){
            //需要补偿同步患者端任务数据
        }else{
            currentDatePatientTask.setTaskRate(patientTaskSubmitParam.getTaskRate());
            currentDatePatientTask.setPushRate(patientTaskSubmitParam.getPushRate());
            currentDatePatientTask.setSubmitTime(new Date());
            currentDatePatientTask.setComplateStatus(patientTaskSubmitParam.getComplateStatus());
            //查询任务次数
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_01.getCode().equals(currentDatePatientTask.getComplateStatus())){
                currentDatePatientTask.setComplateCount(currentDatePatientTask.getComplateCount() + 1);
            }else{
                if(submitInitFormFlag){
                    currentDatePatientTask.setComplateCount(currentDatePatientTask.getComplateCount() + 1);
                }
            }
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(patientTaskSubmitParam.getComplateStatus())){
                currentDatePatientTask.setComplateTime(new Date());
            }
            projectPatientResultService.savePatientResult(currentDatePatientTask);
        }
        return customResult;
    }

    private int getPatientCurrentDateTaskCount(Long projectId, Long planId, Long taskId, Long testeeId) {
        return projectPatientResultService.getPatientCurrentDateTaskCount(projectId, planId, taskId, testeeId);
    }


    @Override
    public PatientTaskFormValueVo getPatientTaskDataList(String projectId, String testeeId) {
        PatientTaskFormValueVo patientTaskFormValueVo = new PatientTaskFormValueVo();

        String targetPlanId = queryComplianceRulePlanResult(projectId, testeeId);
        if(StringUtils.isEmpty(targetPlanId)){
            patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_01.getCode());
            return patientTaskFormValueVo;
        }

        ProjectTesteeVo testeeBaseInfo = projectTesteeInfoService.getProjectTesteeBaseInfo(projectId, testeeId);
        ProjectPatientBindResultVo projectPatientBindResult = this.getProjectPatientBindResult(projectId, testeeBaseInfo.getContant());
        Boolean bindResult = projectPatientBindResult.getBindResult() == null ? false : projectPatientBindResult.getBindResult();
        if(!bindResult){
            patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_04.getCode());
            return patientTaskFormValueVo;
        }
        String reviewStatus = projectPatientBindResult.getReviewStatus();
        if(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_01.getName().equals(reviewStatus)){
            patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_05.getCode());
            return patientTaskFormValueVo;
        }
        if(ProjectTesteeReviewEnum.PROJECT_TESTEE_REVIEW_STATUS_03.getName().equals(reviewStatus)){
            patientTaskFormValueVo.setViewTaskResult(PatientTaskViewResultEnum.PATIENT_TASK_VIEW_CODE_06.getCode());
            return patientTaskFormValueVo;
        }

        patientTaskFormValueVo = projectPatientResultService.getPatientTaskCalendarDetailByTaskDate(projectId, targetPlanId, testeeId, DateUtil.getCurrentDate());
        List<PatientTaskFormValueVo.PatientTaskFormVo> dataList = patientTaskFormValueVo.getDataList();
        if(CollectionUtil.isEmpty(dataList)){
            List<ProjectPatientPlan> projectPatientPlanList = projectPlanManageService.getProjectPlanListByProjectId(Long.parseLong(projectId));
            for (ProjectPatientPlan projectPatientPlan : projectPatientPlanList) {
                PatientTaskFormValueVo patientTaskFormValue = projectPatientResultService.getPatientComputeTaskList(projectId, projectPatientPlan.getId().toString(), testeeId);
                List<PatientTaskFormValueVo.PatientTaskFormVo> taskFormValueVoDataList = patientTaskFormValue.getDataList();
                for (PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo : taskFormValueVoDataList) {
                    ProjectPatientCalendar projectPatientCalendar = projectPatientCalendarService.getCurrentCalendarPatientTask(projectId, projectPatientPlan.getId().toString(), testeeId);
                    if(projectPatientCalendar == null){
                        projectPatientCalendarService.saveCurrentDateCalendarPatientTask(projectId, projectPatientPlan.getId().toString(), testeeId, true);
                    }
                    ProjectPatientResult projectPatientResult = projectPatientResultService.getCurrentDatePatientTask(projectId, projectPatientPlan.getId().toString(), patientTaskFormVo.getTaskId().toString(), null, testeeId);
                    if(projectPatientResult == null){
                        projectPatientResultService.saveCurrentDatePatientTask(projectId, projectPatientPlan.getId().toString(), patientTaskFormVo.getTaskId().toString(), testeeId);
                    }
                }
            }
        }

        List<String> complateCount = new ArrayList<>();
        for (PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo : dataList) {
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(patientTaskFormVo.getComplateStatus())){
                complateCount.add(patientTaskFormVo.getComplateStatus());
            }
            if(patientTaskFormVo.getTaskTypeId().equals(PatientTaskTypeEnum.PROJECT_TASK_04.getName()) ||
                    patientTaskFormVo.getTaskTypeId().equals(PatientTaskTypeEnum.PROJECT_TASK_05.getName())){
                ProjectPatientResult currentDatePatientTaskVo = projectPatientResultService.getCurrentDatePatientTask(projectId, targetPlanId, patientTaskFormVo.getTaskId().toString(), null, testeeId);
                if(currentDatePatientTaskVo != null){
                    //查询任务次数
                    String currentDate = DateUtil.getCurrentDate();
                    List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, "", patientTaskFormVo.getVisitId().toString(), patientTaskFormVo.getFormId().toString(), "", "", testeeId, "", currentDate, "", "");
                    currentDatePatientTaskVo.setComplateCount(rowNumberList.size());
                    projectPatientResultService.savePatientResult(currentDatePatientTaskVo);
                }
            }
            //返回定制标识
            ProjectPlanVo projectPlanVo = projectPlanManageService.getProjectPlanInfo(targetPlanId);
            List<ProjectPlanVo.ProjectPatientPlanVariableVo> projectPatientPlanVariableVoList = projectPlanVo.getDataList();
            for (ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo : projectPatientPlanVariableVoList) {
                if(projectPatientPlanVariableVo.getTaskId().equals(patientTaskFormVo.getTaskId()) && PatientTaskRateEnum.PROJECT_TASK_04.getName().equals(projectPatientPlanVariableVo.getSendRate())){
                    patientTaskFormVo.setIdentification("月");
                }
            }
        }
        if(complateCount.size() >0 && complateCount.size() == dataList.size()){
            ProjectPatientCalendar currentCalendarPatientTask = projectPatientCalendarService.getCurrentCalendarPatientTask(projectId, patientTaskFormValueVo.getPlanId(), testeeId);
            if(currentCalendarPatientTask != null){
                currentCalendarPatientTask.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
                projectPatientCalendarService.updatePatientCalendarTask(projectId, patientTaskFormValueVo.getPlanId(), testeeId, PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
            }
        }
        if(complateCount.size() >0 && dataList.size() > complateCount.size()){
            ProjectPatientCalendar currentCalendarPatientTask = projectPatientCalendarService.getCurrentCalendarPatientTask(projectId, patientTaskFormValueVo.getPlanId(), testeeId);
            if(currentCalendarPatientTask != null){
                currentCalendarPatientTask.setCompleteState(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
                projectPatientCalendarService.updatePatientCalendarTask(projectId, patientTaskFormValueVo.getPlanId(), testeeId, PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());
            }
        }
        return patientTaskFormValueVo;
    }

    private String queryComplianceRulePlanResult(String projectId, String testeeId) {
        List<ProjectPatientPlan> projectPatientPlanList = projectPlanManageService.getProjectPlanListByProjectId(Long.parseLong(projectId));
        for (ProjectPatientPlan projectPatientPlan : projectPatientPlanList) {
            if (projectPatientPlan.getIfOpen()) {
                ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", projectPatientPlan.getVisitId().toString(), projectPatientPlan.getFormId().toString(), "", projectPatientPlan.getFormDetailId().toString(), testeeId);
                if (projectTesteeResult != null) {
                    String fieldValue = projectTesteeResult.getFieldValue();
                    if(StringUtils.isNotEmpty(fieldValue)){
                        List<String> dataList = new ArrayList<>();
                        String formDetailValue = projectPatientPlan.getFormDetailValue();
                        String[] array = formDetailValue.split(",");
                        for (String value : array) {
                            if (fieldValue.contains(value)) {
                                dataList.add(value);
                            }
                        }
                        if(dataList.size() == array.length){
                            return projectPatientPlan.getId().toString();
                        }
                    }
                }
            }else{
                return projectPatientPlan.getId().toString();
            }
        }
        return null;
    }

    @Override
    public PatientTaskFormDetailVo getPatientTaskFormDetail(String projectId, String testeeId, String planId, String taskId, String taskDate) {
        PatientTaskFormDetailVo patientTaskFormDetailVo = new PatientTaskFormDetailVo();
        patientTaskFormDetailVo.setProjectId(projectId);
        patientTaskFormDetailVo.setPlanId(planId);
        patientTaskFormDetailVo.setTesteeId(testeeId);
        ProjectPlanVo projectPlanInfo = projectPlanManageService.getProjectPlanInfo(planId);
        List<ProjectPlanVo.ProjectPatientPlanVariableVo> projectPatientPlanVariableVoList = projectPlanInfo.getDataList();
        for (ProjectPlanVo.ProjectPatientPlanVariableVo projectPatientPlanVariableVo : projectPatientPlanVariableVoList) {
            if(projectPatientPlanVariableVo.getTaskId().toString().equals(taskId)){
                patientTaskFormDetailVo.setProjectPatientPlanVariableVo(projectPatientPlanVariableVo);
            }
        }

        ProjectTaskVo projectTaskVo = projectTaskManageService.getProjectTaskInfo(taskId);
        List<PatientTaskFormVariableVo> taskFormVariableList = new ArrayList<>();
        List<ProjectTaskVo.ProjectPatientTaskVariableVo> patientTaskVariableVoList = projectTaskVo.getDataList();
        for (ProjectTaskVo.ProjectPatientTaskVariableVo projectPatientTaskVariableVo : patientTaskVariableVoList) {
            PatientTaskFormVariableVo patientTaskFormVariableVo = new PatientTaskFormVariableVo();
            patientTaskFormVariableVo.setVisitId(projectTaskVo.getVisitId());
            patientTaskFormVariableVo.setFormId(projectTaskVo.getFormId());
            patientTaskFormVariableVo.setFormDetailId(projectPatientTaskVariableVo.getFormDetailId());
            TemplateFormDetail templateFormDetailConfig = templateConfigService.getTemplateFormDetailConfigByVariableId(projectPatientTaskVariableVo.getFormDetailId().toString());
            //如果是普通表单直接展示
            patientTaskFormVariableVo.setLabel(templateFormDetailConfig.getLabel());
            patientTaskFormVariableVo.setKey(templateFormDetailConfig.getFieldName());
            patientTaskFormVariableVo.setType(templateFormDetailConfig.getType());
            patientTaskFormVariableVo.setOptions(templateFormDetailConfig.getCombobox());
            patientTaskFormVariableVo.setExpand(templateFormDetailConfig.getExpand());
            patientTaskFormVariableVo.setRequired(templateFormDetailConfig.getRequired());
            ProjectTesteeResult projectTesteeResult = projectTesteeResultService.getProjectTesteeFormOneResult(projectId, "", projectTaskVo.getVisitId().toString(), projectTaskVo.getFormId().toString(), "", projectPatientTaskVariableVo.getFormDetailId().toString(), testeeId);
            if (projectTesteeResult != null) {
                patientTaskFormVariableVo.setFormResultId(projectTesteeResult.getId());
                patientTaskFormVariableVo.setResult(projectTesteeResult.getFieldValue());
            }
            //如果是表格类型
            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailConfig.getType())) {
                if(StringUtils.isEmpty(taskDate)){
                    taskDate = DateUtil.getCurrentDate();
                }
                List<TemplateTableRowHeadVo> dataRowHeadList = new ArrayList<>();
                //设置头部显示信息
                List<TemplateTableVo> projectTesteeTableRowList = projectTesteeTableService.getProjectTesteeTableRowHead(templateFormDetailConfig.getFormId().toString(), templateFormDetailConfig.getId().toString(), false);
                for (TemplateTableVo templateTableVo : projectTesteeTableRowList) {
                    TemplateTableRowHeadVo templateTableRowHeadVo = new TemplateTableRowHeadVo();
                    BeanUtils.copyProperties(templateTableVo, templateTableRowHeadVo);
                    templateTableRowHeadVo.setOptions(templateTableVo.getExpand());
                    TemplateFormTable templateFormTable = templateConfigService.getTemplateFormTableConfigById(templateTableVo.getId());
                    if(templateFormTable != null){
                        templateTableRowHeadVo.setHidden(templateFormTable.getHidden());
                        templateTableRowHeadVo.setExtData1(templateFormTable.getExtData1());
                        templateTableRowHeadVo.setExtData2(templateFormTable.getExtData2());
                    }
                    dataRowHeadList.add(templateTableRowHeadVo);
                }
                patientTaskFormVariableVo.setDataRowHeadList(dataRowHeadList);
                //设置行记录显示内容 数据记录行号和数据信息
                List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, "", projectTaskVo.getVisitId().toString(), templateFormDetailConfig.getFormId().toString(), "", "", testeeId, "", taskDate, "", "");
                List<ProjectTesteeTableBody.RowDataDesc> dataList = new ArrayList<>();
                for (Long rowNumber : rowNumberList) {
                    List<ProjectTesteeTableBody.ProjectTesteeTableData> gridList = new ArrayList<>();

                    ProjectTesteeTableBody.RowDataDesc rowDataDesc = new ProjectTesteeTableBody.RowDataDesc();
                    ProjectTesteeTableBody.ProjectTesteeTableData opratorCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                    opratorCol.setFieldName(BusinessConfig.PROJECT_FORM_OPERATE_KEY);
                    opratorCol.setLabel(BusinessConfig.PROJECT_FORM_OPERATE_LABEL);
                    opratorCol.setFieldValue(BusinessConfig.PROJECT_FORM_OPERATE_VALUE);

                    ProjectTesteeTableBody.ProjectTesteeTableData rowNumberDataCol = new ProjectTesteeTableBody.ProjectTesteeTableData();
                    rowNumberDataCol.setFieldName(BusinessConfig.PROJECT_FORM_NUMBER_KEY);
                    rowNumberDataCol.setLabel(BusinessConfig.PROJECT_FORM_NUMBER_LABEL);
                    rowNumberDataCol.setFieldValue(rowNumber.toString());
                    gridList.add(rowNumberDataCol);
                    List<ProjectTesteeTableWrapperVo> projectTesteeTableList = projectTesteeTableService.getProjectTesteeTableListForPage(projectId, "", projectTaskVo.getVisitId().toString(), projectTaskVo.getFormId().toString(), "", "", testeeId, rowNumber);
                    for (ProjectTesteeTableWrapperVo projectTesteeTable : projectTesteeTableList) {
                        ProjectTesteeTableBody.ProjectTesteeTableData data = new ProjectTesteeTableBody.ProjectTesteeTableData();
                        BeanUtils.copyProperties(projectTesteeTable, data);
                        data.setFieldName(projectTesteeTable.getFieldName());
                        data.setFieldValue(projectTesteeTable.getFieldValue());
                        data.setUnitValue(projectTesteeTable.getUnitValue());
                        TemplateFormTable templateFormTable = templateConfigService.getTemplateFormTableConfigById(projectTesteeTable.getFormTableId());
                        if(templateFormTable != null){
                            data.setHidden(templateFormTable.getHidden());
                            data.setExtData1(templateFormTable.getExtData1());
                            data.setExtData2(templateFormTable.getExtData2());
                        }
                        data.setTesteeResultTableId(projectTesteeTable.getId().toString());
                        rowDataDesc.setCreateTime(DateUtil.formatDate2String(projectTesteeTable.getCreateTime()));
                        gridList.add(data);
                    }
                    gridList.add(opratorCol);

                    rowDataDesc.setRowNumber(rowNumber.toString());
                    rowDataDesc.setRowData(gridList);
                    dataList.add(rowDataDesc);
                }
                patientTaskFormVariableVo.setRows(JSON.toJSONString(dataList, SerializerFeature.DisableCircularReferenceDetect));
            }
            taskFormVariableList.add(patientTaskFormVariableVo);
            patientTaskFormDetailVo.setDataList(taskFormVariableList);
        }
        return patientTaskFormDetailVo;
    }

    @Override
    public PatientTaskCalendarDetailVo getPatientTaskCalendarDetail(String projectId, String testeeId, String planId, String taskDateTime) {
        //查询 当月任务完成情况
        PatientTaskCalendarDetailVo patientTaskCalendarDetailVo = projectPatientCalendarService.getCurrentWholeMonthTask(projectId, testeeId, planId, taskDateTime);
        ProjectPlanVo projectPlanInfo = projectPlanManageService.getProjectPlanInfo(planId);
        patientTaskCalendarDetailVo.setPlanId(planId);
        patientTaskCalendarDetailVo.setPlanName(projectPlanInfo.getPlanName());
        return patientTaskCalendarDetailVo;
    }

    @Override
    public PatientTaskSummaryVo getPatientTaskSummary(String projectId, String testeeId, String planId) {
        PatientTaskSummaryVo patientTaskSummaryVo = new PatientTaskSummaryVo();
        ProjectVisitUser projectVisitUser = projectTesteeInfoService.getProjectTesteeUserInfo(projectId, "", testeeId);
        Integer bingDays = DateUtil.diffDays(projectVisitUser.getBindTime(), new Date());
        patientTaskSummaryVo.setExecutedDays(bingDays);
        List<Date> complateCount = new ArrayList<>();
        List<String> totalCount = new ArrayList<>();

        //当月总天数
        int currentMonthLastDay = DateUtil.getCurrentMonthLastDay();
        PatientTaskCalendarDetailVo patientTaskCalendarDetailVo = projectPatientCalendarService.getCurrentWholeMonthTask(projectId, testeeId, planId, null);
        List<ProjectPatientCalendarVo> dataList = patientTaskCalendarDetailVo.getDataList();
        for (ProjectPatientCalendarVo projectPatientCalendarVo : dataList) {
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectPatientCalendarVo.getCompleteState())){
                complateCount.add(projectPatientCalendarVo.getTaskDate());
            }
        }
        patientTaskSummaryVo.setCurrentMonthComplateDays(complateCount.size());
        patientTaskSummaryVo.setCurrentMonthNotfinishedDays(currentMonthLastDay - complateCount.size());

        patientTaskCalendarDetailVo = projectPatientCalendarService.getCustomBetweenTimeTask(projectId, testeeId, planId, projectVisitUser.getBindTime(), new Date());
        dataList = patientTaskCalendarDetailVo.getDataList();
        for (ProjectPatientCalendarVo projectPatientCalendarVo : dataList) {
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(projectPatientCalendarVo.getCompleteState())) {
                totalCount.add(DateUtil.formatDate2String(projectPatientCalendarVo.getTaskDate(),DateUtil.DEFAULTFORMAT));
            }
        }
        patientTaskSummaryVo.setTotalComplateDays(totalCount.size());
        if(totalCount.size() >0){
            int persistentDay = 0;
            try {
                persistentDay = PersistentDayUtil.getMaxUninterruptedDaysByOneZero(totalCount);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            patientTaskSummaryVo.setSuccessiveComplateDays(persistentDay);
        }
        return patientTaskSummaryVo;
    }

    @Override
    public PatientTaskFormValueVo getPatientTaskCalendarDetailByTaskDate(String projectId, String testeeId, String planId, String taskDate) {
        //查询指定日期任务
        PatientTaskFormValueVo patientTaskFormValueVo = projectPatientResultService.getPatientTaskCalendarDetailByTaskDate(projectId, planId, testeeId, taskDate);
        List<PatientTaskFormValueVo.PatientTaskFormVo> dataList = patientTaskFormValueVo.getDataList();

        List<String> complateCount = new ArrayList<>();
        for (PatientTaskFormValueVo.PatientTaskFormVo patientTaskFormVo : dataList) {
            if(PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode().equals(patientTaskFormVo.getComplateStatus())){
                complateCount.add(patientTaskFormVo.getComplateStatus());
            }
            if(patientTaskFormVo.getTaskTypeId().equals(PatientTaskTypeEnum.PROJECT_TASK_04.getName()) ||
                    patientTaskFormVo.getTaskTypeId().equals(PatientTaskTypeEnum.PROJECT_TASK_05.getName())){
                ProjectPatientResult currentDatePatientTaskVo = projectPatientResultService.getCurrentDatePatientTask(projectId, planId, patientTaskFormVo.getTaskId().toString(), null, testeeId);
                if(currentDatePatientTaskVo != null){
                    //查询任务次数
                    String currentDate = DateUtil.getCurrentDate();
                    List<Long> rowNumberList = projectTesteeTableService.getProjectTesteeTableRowNumber(projectId, "", patientTaskFormVo.getVisitId().toString(), patientTaskFormVo.getFormId().toString(), "", "", testeeId, "", currentDate, "", "");
                    currentDatePatientTaskVo.setComplateCount(rowNumberList.size());
                    projectPatientResultService.savePatientResult(currentDatePatientTaskVo);
                }
            }
        }
        ProjectPatientCalendar currentCalendarPatientTask = projectPatientCalendarService.getCurrentCalendarPatientTaskByTaskDate(projectId, patientTaskFormValueVo.getPlanId(), testeeId, taskDate);
        //ProjectPatientCalendar currentCalendarPatientTask = projectPatientCalendarService.getCurrentCalendarPatientTask(projectId, patientTaskFormValueVo.getPlanId(), testeeId);
        if(complateCount.size() >0 && complateCount.size() == dataList.size()){
            if(currentCalendarPatientTask != null){
                projectPatientCalendarService.updatePatientCalendarTaskByTaskDate(projectId, patientTaskFormValueVo.getPlanId(), testeeId, taskDate, PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_03.getCode());
            }
        }
        if(complateCount.size() >0 && dataList.size() > complateCount.size()){
            if(currentCalendarPatientTask != null){
                projectPatientCalendarService.updatePatientCalendarTaskByTaskDate(projectId, patientTaskFormValueVo.getPlanId(), testeeId, taskDate, PatientTaskComplateEnum.PATIENT_TASK_COMPLATE_CODE_02.getCode());

            }
        }
        return projectPatientResultService.getPatientTaskCalendarDetailByTaskDate(projectId, planId, testeeId, taskDate);
    }

    @Override
    public ProjectPatientBindResultVo getProjectPatientBindResult(String projectId, String mobile) {
        ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getProjectTesteeBaseInfoByMobile(projectId, mobile);
        if(projectTesteeVo != null){
            ProjectPatientBindResultVo projectPatientBindResultVo = new ProjectPatientBindResultVo();
            BeanUtils.copyProperties(projectTesteeVo, projectPatientBindResultVo);
            ProjectTesteeConfigVo projectTesteeConfig = projectTesteeConfigService.getProjectTesteeConfig(projectId);
            if(projectTesteeConfig != null){
                projectPatientBindResultVo.setSelfRecord("2".equals(projectTesteeConfig.getBindType()));
            }
            ProjectVisitUser projectVisitUser = projectTesteeInfoService.getProjectTesteeUserInfo(projectId, "", projectTesteeVo.getId().toString());
            if(projectVisitUser != null){
                boolean bindResult = projectVisitUser.getBindResult() == null ? false : projectVisitUser.getBindResult();
                projectPatientBindResultVo.setBindResult(projectVisitUser != null);
                if(projectVisitUser.getSelfRecord()){
                    projectPatientBindResultVo.setBindResult(bindResult);
                }
                //projectPatientBindResultVo.setSelfRecord(projectVisitUser.getSelfRecord());
                projectPatientBindResultVo.setReviewStatus(projectVisitUser.getReviewStatus());
                SystemUserInfo systemUserInfo = systemUserInfoService.getSystemUserInfoByMobile(mobile);
                String userType = systemUserInfo == null ? "" : systemUserInfo.getUserType();
                projectPatientBindResultVo.setExistPatientResult(Constants.USER_TYPE_VALUE_04.equals(userType) & projectPatientBindResultVo.getSelfRecord());
                projectPatientBindResultVo.setBindResource(projectVisitUser.getBindResource());
            }
            return projectPatientBindResultVo;
        }
        return null;
    }

    @Override
    public String getPatientTaskProjectId(String mobile) {
        ProjectTesteeVo projectTesteeVo = projectTesteeInfoService.getProjectTesteeBaseInfoByMobile(null, mobile);
        if(projectTesteeVo != null){
            ProjectVisitUser projectVisitUser = projectTesteeInfoService.getPatientTaskProjectId(projectTesteeVo.getId());
            if(projectVisitUser != null){
                return projectVisitUser.getProjectId().toString();
            }
        }
        return null;
    }


}
