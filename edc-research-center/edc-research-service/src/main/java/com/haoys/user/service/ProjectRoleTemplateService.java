package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.model.ProjectRole;

import java.util.List;

public interface ProjectRoleTemplateService {

    /**
     * 分页获取角色列表
     */
    CommonPage<ProjectRoleVo> getProjectTemplateRoleListForPage(ProjectRole projectRole, Integer pageNum, Integer pageSize);

    /**
     * 获取角色下拉列表
     */
    List<ProjectRole> getRoseList();

    /**
     * 添加角色
     */
    int saveProjectTemplateRole(ProjectRole projectRole);

    /**
     * 修改角色信息
     */
    int updateProjectRole(Long id, ProjectRole role);

    /**
     * 删除角色信息
     */
    int deleteProjectRole(List<Long> ids);

    /**
     * 角色开关
     */
    int editStatus(Long id);

    /**
     * 角色复制
     */
    int copyProjectRole(Long id);

    /**
     * 编辑/复制获取回显数据
     */
    ProjectRole getRoseDate(Long id);
}
