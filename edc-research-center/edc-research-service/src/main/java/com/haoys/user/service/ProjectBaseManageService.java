package com.haoys.user.service;


import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.domain.param.project.ProjectParam;
import com.haoys.user.domain.vo.overview.ProjectOverViewVo;
import com.haoys.user.domain.vo.project.ProjectApplyUserVo;
import com.haoys.user.domain.vo.project.ProjectVo;
import com.haoys.user.model.Project;

import java.util.List;

/**
 * 项目管理
 */
public interface ProjectBaseManageService {

    /**
     * 保存编辑项目
     * @param projectParam
     * @return
     */
    CustomResult saveProjectBaseInfo(ProjectParam projectParam);

    /**
     * 项目广场
     *
     * @param databaseId
     * @param projectName
     * @param userId
     * @param pageSize
     * @param pageNum
     * @return
     */
    CommonPage<ProjectVo> getPublicProjectListForPage(String databaseId, String projectName, String userId, Integer pageSize, Integer pageNum);

    /**
     * 我的项目列表
     *
     * @param databaseId
     * @param createUserId
     * @param projectOrgId
     * @param projectName
     * @param enableRandomizedConfig
     * @param pageSize
     * @param pageNum
     * @return
     */
    CommonPage<ProjectVo> getOwnerProjectListForPage(String databaseId, String createUserId, String projectOrgId, String projectName, Boolean enableRandomizedConfig, Integer pageSize, Integer pageNum);

    /**
     * 项目成员申请列表
     * @param projectId
     * @param applyUserName
     * @param projectCreateUser
     * @param orgId
     * @param applyDate
     * @param status
     * @param pageNum
     * @param pageSize
     * @return
     */
    CommonPage<ProjectApplyUserVo> getProjectUserApplyListForPage(String projectId, String applyUserName, String projectCreateUser, String orgId, String applyDate, String status, Integer pageNum, Integer pageSize);


    List<Project> getProjectUserInfoList(List<Long> projectIds);

    /**
     * 项目申请审核
     * @param id
     * @param status
     * @param operator
     * @return
     */
    String updateCheckProjectstatus(String id, String status, String operator);


    /**
     * 添加项目成员
     * @param projectId 项目id
     * @param orgId     中心id
     * @param userId    用户id
     * @return
     */
    String saveProjectUser(String projectId, String orgId, String userId);

    /**
     * 设置项目负责人
     * @param projectId 项目id
     * @param orgId     中心id
     * @param userId    用户id
     * @return
     */
    String saveProjectUserLeader(String projectId, String orgId, String userId);


    /**
     * 申请加入项目
     * @param projectId 项目id
     * @param orgId     中心id
     * @param userId    用户id
     * @return
     */
    String saveApplyProjectUser(String projectId, String orgId, String userId);

    /**
     * 查询项目基本信息
     * @param projectId
     * @return
     */
    Project getProjectBaseInfo(String projectId);

    /**
     * 删除项目用户
     * @param projectId 项目id
     * @param userId    用户id
     * @param orgId     机构id
     * @return
     */
    String deleteProjectUser(String projectId, String userId, String orgId);


    /**
     * 删除项目基本信息和项目授权
     * @param projectId
     * @param userId
     * @return
     */
    CustomResult deleteProjectBaseInfoAndProjectAuth(String projectId, String userId);

    /**
     * 删除项目
     * @param projectId     项目id
     * @param userId        操作人
     * @return
     */
    CustomResult deleteProjectBaseInfo(String projectId, String userId);

    /**
     * 后台管理项目列表
     * @param operator
     * @param pageSize
     * @param pageNum
     * @return
     */
    CommonPage<ProjectVo> getProjectListForPage(String projectName, String researchArea, Boolean ifPublic, String operator, Integer pageSize, Integer pageNum);
    
    List<ProjectVo> getProjectList(String projectName, String researchArea, Boolean ifPublic);
    
    /**
     * 项目概览
     * @param projectId
     * @param userId
     * @return
     */
    ProjectOverViewVo getProjectOverViewData(String projectId, String userId);

    /**
     * 查询项目详情
     * @param projectId
     * @return
     */
    ProjectVo getProjectViewInfo(String projectId);

    /**
     * 项目开关
     * @param id
     * @return
     */
    CustomResult modifyIfPublicByProjectId(Long id);

    List<ProjectVo> getOwnerProjectListForH5(String userId);
    
    CustomResult updateProjectRandomConfig(String projectId, Boolean ifRandom);
    
    Project getEnableProjectBaseInfo(String loginTenantId, String loginPlatformId);
}
