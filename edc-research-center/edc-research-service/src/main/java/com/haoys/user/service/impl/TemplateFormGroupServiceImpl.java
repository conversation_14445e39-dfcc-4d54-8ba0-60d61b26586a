package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.crf.TemplateFormGroupParam;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormGroupVariableVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.mapper.*;
import com.haoys.user.model.*;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class TemplateFormGroupServiceImpl implements TemplateFormGroupService {

    private final ProjectTesteeTableMapper projectTesteeTableMapper;
    private final ProjectTesteeResultMapper projectTesteeResultMapper;
    private final TemplateFormGroupTableMapper templateFormGroupTableMapper;
    private final TemplateFormGroupVariableMapper templateFormGroupVariableMapper;
    private final ProjectTesteeTableService projectTesteeTableService;
    private final TemplateConfigService templateConfigService;
    private final FlowPlanService flowPlanService;

    /**
     * 参与者新增字段组
     * @param groupParam 分组数据
     * @param userId 创建者
     * @return
     */
    @Override
    public CommonResult<Object> saveTesteeFormGroupDetail(TemplateFormGroupParam groupParam, String userId) {
        if (groupParam.getTesteeId()==null){
            return CommonResult.failed( "参与者id不能为空");
        }
        if (groupParam.getProjectId()==null){
            return CommonResult.failed( "项目id不能为空");
        }
        if (groupParam.getVisitId()==null){
            return CommonResult.failed( "访视id不能为空");
        }
        if (groupParam.getFormId()==null){
            return CommonResult.failed( "表单id不能为空");
        }
        Long groupId = SnowflakeIdWorker.getUuid();
            Date now = new Date();
        if (CollectionUtil.isNotEmpty(groupParam.getVariableList())) {
            for (TemplateFormGroupVariable variable : groupParam.getVariableList()) {
                TemplateFormDetail templateFormDetail = templateConfigService.getTemplateFormDetailConfigByVariableId(variable.getResourceVariableId().toString());
                if (templateFormDetail != null) {
                    variable.setId(SnowflakeIdWorker.getUuid());
                    String label = templateFormDetail.getLabel();
                    variable.setGroupName(label);
                    variable.setGroupId(groupId);
                    variable.setProjectId(groupParam.getProjectId());
                    variable.setPlanId(groupParam.getPlanId());
                    variable.setVisitId(groupParam.getVisitId());
                    variable.setFormId(groupParam.getFormId());
                    variable.setTesteeId(groupParam.getTesteeId());
                    variable.setStatus(BusinessConfig.VALID_STATUS);
                    variable.setCreateTime(now);
                    variable.setCreateUserId(userId);
                    variable.setTenantId(SecurityUtils.getSystemTenantId());
                    variable.setPlatformId(SecurityUtils.getSystemPlatformId());
                    templateFormGroupVariableMapper.insert(variable);
                    if (CollectionUtil.isNotEmpty(variable.getTableList())) {

                        Map<Long, List<TemplateFormGroupTable>> collect =
                                variable.getTableList().stream().collect(Collectors.groupingBy(TemplateFormGroupTable::getResourceVariableId));
                        List<TemplateFormGroupTable> groupTables = collect.get(variable.getResourceVariableId());
                        if (CollectionUtil.isNotEmpty(groupTables)){
                            // 根据分组内表格id获取表列信息,校验列是否存在
                            for (TemplateFormGroupTable table : groupTables) {
                                table.setCreateTime(now);
                                table.setCreateUserId(userId);
                                table.setGroupId(groupId);
                                table.setFormDetailId(variable.getId());
                                table.setTesteeId(groupParam.getTesteeId());
                                table.setProjectId(groupParam.getProjectId());
                                table.setPlanId(groupParam.getPlanId());
                                table.setVisitId(groupParam.getVisitId());
                                table.setFormId(groupParam.getFormId());
                                table.setId(SnowflakeIdWorker.getUuid());
                                table.setStatus(BusinessConfig.VALID_STATUS);
                                table.setTenantId(SecurityUtils.getSystemTenantId());
                                table.setPlatformId(SecurityUtils.getSystemPlatformId());
                                templateFormGroupTableMapper.insert(table);
                            }
                        }
                    }
                } else {
                    return CommonResult.failed("变量id为" + variable.getResourceVariableId() + "不存在");
                }
            }
        }
        return CommonResult.success(groupId+"", "创建成功");
    }


    @Override
    public CommonResult<Object> deleteProjectTesteeGroupInfoByGroupId(String groupId,String userId) {
        if (StringUtils.isEmpty(groupId)) {
            return CommonResult.failed("groupId为空");
        }
        // 删除组内表格数据
        templateFormGroupTableMapper.deleteTemplateFormGroupTableByGroupId(groupId,BusinessConfig.NO_VALID_STATUS,userId);
        // 删除组内普通变量数据
        templateFormGroupVariableMapper.deleteTemplateFormGroupVariableByGroupId(groupId,BusinessConfig.NO_VALID_STATUS,userId);
        // 删除题组的表格数据
        projectTesteeTableMapper.deleteByGroupId(groupId,BusinessConfig.NO_VALID_STATUS,userId);
        // 删除题组的普通变量数据
        projectTesteeResultMapper.deleteByGroupId(groupId,BusinessConfig.NO_VALID_STATUS,userId);

        return CommonResult.success("删除成功");
    }

    @Override
    public TemplateFormGroupVariable getTemplateGroupInfoByBaseVariableId(String projectId, String planId, String visitId, String formId, String resourceVariableId, String resourceGroupId, String testeeGroupId, String testeeId) {
        return templateFormGroupVariableMapper.getTemplateGroupInfoByBaseVariableId(projectId, planId, visitId, formId, resourceVariableId, resourceGroupId, testeeGroupId, testeeId);
    }

    @Override
    public List<TemplateFormGroupVariableVo> getProjectTesteeFormGroupListByFormId(String projectId, String planId, String visitId, String formId, String variableGroupTableId, String variableTableId, String testeeId) {
        return templateFormGroupVariableMapper.getProjectTesteeFormGroupListByFormId(projectId, planId, visitId, formId, variableGroupTableId, variableTableId, testeeId);
    }

    @Override
    public List<TemplateFormGroupTable> getTemplateGroupTableRowHeader(String projectId, String planId, String visitId, String formId, String resourceDetailId, String groupId, String baseGroupId, String testeeId) {
        TemplateFormGroupTableExample example = new TemplateFormGroupTableExample();
        TemplateFormGroupTableExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andResourceVariableIdEqualTo(Long.parseLong(resourceDetailId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        if(StringUtils.isNotEmpty(groupId)){
            criteria.andGroupIdEqualTo(Long.parseLong(groupId));
        }
        if(StringUtils.isNotEmpty(baseGroupId)){
            criteria.andResourceGroupIdEqualTo(Long.parseLong(baseGroupId));
        }
        if(StringUtils.isNotEmpty(testeeId)){
            criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        }
        example.setOrderByClause("sort asc, create_time asc");
        return templateFormGroupTableMapper.selectByExample(example);
    }

    @Override
    public CommonResult<Object> saveTesteeBaseFormVariableGroup(String projectId, String visitId, String formId, String testeeId, String userId) {
        String planId = "";
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        if(flowPlanInfo != null){
            planId = flowPlanInfo.getId().toString();
        }

        // 判断是否已经有组信息
        TemplateFormGroupVariableExample example = new TemplateFormGroupVariableExample();
        TemplateFormGroupVariableExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andPlanIdEqualTo(Long.parseLong(planId));
        criteria.andVisitIdEqualTo(Long.parseLong(visitId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        // 查询初始化的组内信息是否存在(普通变量信息)
        List<TemplateFormGroupVariable> groupVariables = templateFormGroupVariableMapper.selectByExample(example);
        if (CollectionUtil.isEmpty(groupVariables)){
            // 如果没有组信息，需要根据默认的组信息添加
            // 普通变量
            List<TemplateFormDetailVo> details = templateConfigService.getTemplateFormDetailByFormIdAndVariableType(null, formId, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
            if (CollectionUtil.isNotEmpty(details)){
                TemplateFormGroupParam groupParam = new TemplateFormGroupParam();
                groupParam.setProjectId(Long.parseLong(projectId));
                groupParam.setPlanId(Long.parseLong(planId));
                groupParam.setVisitId(Long.parseLong(visitId));
                groupParam.setFormId(Long.parseLong(formId));
                groupParam.setTesteeId(Long.parseLong(testeeId));
                List <TemplateFormGroupVariable> variableList =  new ArrayList<>();
                for (TemplateFormDetailVo detail : details) {
                    List<TemplateFormDetailVo> detailVoList = templateConfigService.getTemplateFormDetailByGroupId(formId, detail.getId()+"", "", false);
                    if (CollectionUtil.isNotEmpty(detailVoList)){
                        for (TemplateFormDetailVo templateFormDetailVo : detailVoList) {
                            TemplateFormGroupVariable variable = new TemplateFormGroupVariable();
                            variable.setGroupName(detail.getLabel());
                            variable.setSort(templateFormDetailVo.getSort());
                            variable.setResourceGroupId(templateFormDetailVo.getGroupId());
                            variable.setResourceVariableId(templateFormDetailVo.getId());
                            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                                // 获取表格变量的表头
                                // 查询表格Head数据
                                List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailVo.getId().toString(), false);
                                if (CollectionUtil.isNotEmpty(rowList)){
                                    List<TemplateFormGroupTable> list = new ArrayList<>(rowList.size());
                                    for (TemplateTableVo templateTableVo : rowList) {
                                        TemplateFormGroupTable table = new TemplateFormGroupTable();
                                        table.setResourceGroupId(templateFormDetailVo.getGroupId());
                                        table.setResourceTableId(templateTableVo.getId());
                                        table.setResourceVariableId(templateTableVo.getFormDetailId());
                                        list.add(table);
                                    }
                                    variable.setTableList(list);
                                }
                            }
                            variableList.add(variable);
                        }
                    }
                }
                groupParam.setVariableList(variableList);
                return saveTesteeFormGroupDetail(groupParam,userId);
            }
        }else {
            // 根据表单获取模板题组
            List<TemplateFormDetailVo> details = templateConfigService.getTemplateFormDetailByFormIdAndVariableType(null, formId, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
            Map<Long, List<TemplateFormGroupVariable>> groupVariablesMap = groupVariables.stream().collect(Collectors.groupingBy(TemplateFormGroupVariable::getGroupId));
            String finalPlanId = planId;
            groupVariablesMap.forEach((k, variableList)->{
                // 比较原始组的信息和新的信息是否相同，
                // 如果有变化需要把旧数据设置成为已删除状态，并添加最新的数据。
                Map<String,TemplateFormGroupVariable> oldVariablesMap =  new HashMap<>();
                for (TemplateFormGroupVariable groupVariable : variableList) {
                    oldVariablesMap.put(groupVariable.getResourceGroupId()+"-"+groupVariable.getResourceVariableId() ,groupVariable);
                }
                TemplateFormGroupVariable groupVariable0 = variableList.get(0);
                // 根据表单项id获取表单指定类型变量列表
                if (CollectionUtil.isNotEmpty(details)){
                    for (TemplateFormDetailVo detail : details) {
                        Map<Long,Integer> variableMap = new HashMap<>();
                        // 获取题组的变量
                        List<TemplateFormDetailVo> detailVoList = templateConfigService.getTemplateFormDetailByGroupId(formId, detail.getId()+"", "", true);
                        if (CollectionUtil.isNotEmpty(detailVoList)){
                            // 查询出是否有新增和更新的。
                            for (TemplateFormDetailVo templateFormDetailVo : detailVoList) {
                                TemplateFormGroupVariable groupVariable = oldVariablesMap.get(templateFormDetailVo.getGroupId() + "-" + templateFormDetailVo.getId());
                                if (groupVariable==null  ){
                                    // 如果题组变量表中没有该数据，需要新增
                                    if (templateFormDetailVo.getStatus().equals(BusinessConfig.VALID_STATUS)){
                                        // 新增
                                        TemplateFormGroupVariable variable = new TemplateFormGroupVariable();
                                        variable.setProjectId(Long.parseLong(projectId));
                                        variable.setVisitId(Long.parseLong(visitId));
                                        variable.setPlanId(Long.parseLong(finalPlanId));
                                        variable.setFormId(Long.parseLong(formId));
                                        variable.setTesteeId(Long.parseLong(testeeId));
                                        variable.setGroupName(templateFormDetailVo.getLabel());
                                        variable.setSort(templateFormDetailVo.getSort());
                                        variable.setResourceGroupId(templateFormDetailVo.getGroupId());
                                        variable.setResourceVariableId(templateFormDetailVo.getId());
                                        variable.setCreateTime(new Date());
                                        variable.setGroupName(templateFormDetailVo.getGroupName());
                                        variable.setCreateUserId(userId);
                                        variable.setSort(templateFormDetailVo.getSort());
                                        variable.setGroupId(groupVariable0.getGroupId());
                                        variable.setId(SnowflakeIdWorker.getUuid());
                                        variable.setStatus(BusinessConfig.VALID_STATUS);
                                        variable.setTenantId(SecurityUtils.getSystemTenantId());
                                        variable.setPlatformId(SecurityUtils.getSystemPlatformId());
                                        templateFormGroupVariableMapper.insert(variable);
                                        // 如果变量是表格变量，需要新增表格变量
                                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                                            // 获取表格变量的表头
                                            // 查询表格Head数据
                                            List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailVo.getId().toString(), true);
                                            if (CollectionUtil.isNotEmpty(rowList)){
                                                for (TemplateTableVo templateTableVo : rowList) {
                                                    // 组装表格每列信息
                                                    if (templateTableVo.getStatus().equals(BusinessConfig.VALID_STATUS)){
                                                        TemplateFormGroupTable table = new TemplateFormGroupTable();
                                                        table.setId(SnowflakeIdWorker.getUuid());
                                                        table.setResourceGroupId(templateFormDetailVo.getGroupId());
                                                        table.setResourceTableId(templateTableVo.getId());
                                                        table.setResourceVariableId(templateTableVo.getFormDetailId());
                                                        table.setGroupId(groupVariable0.getGroupId());
                                                        table.setProjectId(Long.parseLong(projectId));
                                                        table.setVisitId(Long.parseLong(visitId));
                                                        table.setPlanId(Long.parseLong(finalPlanId));
                                                        table.setFormId(Long.parseLong(formId));
                                                        table.setFormDetailId(variable.getId());
                                                        table.setTesteeId(Long.parseLong(testeeId));
                                                        table.setSort(templateTableVo.getSort());
                                                        table.setStatus(BusinessConfig.VALID_STATUS);
                                                        table.setCreateTime(new Date());
                                                        table.setCreateUserId(userId);
                                                        table.setTenantId(SecurityUtils.getSystemTenantId());
                                                        table.setPlatformId(SecurityUtils.getSystemPlatformId());
                                                        templateFormGroupTableMapper.insert(table);
                                                    }
                                                }
                                            }
                                        }

                                    }
                                }else {
                                    if (templateFormDetailVo.getStatus().equals(BusinessConfig.NO_VALID_STATUS)){
                                        // 删除
                                        // 删除变量对应的表格信息
                                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                                            templateFormGroupTableMapper.deleteByFormDetailId(groupVariable.getId());
                                        }
                                        // 删除变量信息
                                        groupVariable.setStatus(BusinessConfig.NO_VALID_STATUS);
                                        templateFormGroupVariableMapper.updateByPrimaryKeySelective(groupVariable);
                                    }else {
                                        // 修改
                                        if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())) {
                                            // 获取模板表格列信息
                                            List<TemplateTableVo> rowList = projectTesteeTableService.getProjectTesteeTableRowHead(formId, templateFormDetailVo.getId().toString(), true);
                                            // 获取初始化后的表格数据
                                            TemplateFormGroupTableExample tableExample = new TemplateFormGroupTableExample();
                                            TemplateFormGroupTableExample.Criteria tableExampleCriteria = tableExample.createCriteria();
                                            tableExampleCriteria.andProjectIdEqualTo(Long.parseLong(projectId));
                                            tableExampleCriteria.andVisitIdEqualTo(Long.parseLong(visitId));
                                            tableExampleCriteria.andFormIdEqualTo(Long.parseLong(formId));
                                            tableExampleCriteria.andTesteeIdEqualTo(Long.parseLong(testeeId));
                                            tableExampleCriteria.andResourceVariableIdEqualTo(templateFormDetailVo.getId());
                                            tableExampleCriteria.andResourceGroupIdEqualTo(templateFormDetailVo.getGroupId());
                                            tableExampleCriteria.andFormDetailIdEqualTo(groupVariable.getId());
                                            tableExampleCriteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                                            // 旧的表格变量
                                            List<TemplateFormGroupTable> oldTableList =
                                                    templateFormGroupTableMapper.selectByExample(tableExample);

                                            if (CollectionUtil.isEmpty(rowList) && CollectionUtil.isNotEmpty(oldTableList)) {
                                                // 如果模板为空，需要把数据全部设置为删除状态
                                                templateFormGroupTableMapper.deleteByFormDetailId(groupVariable.getId());
                                            }
                                            if (CollectionUtil.isNotEmpty(rowList) && CollectionUtil.isEmpty(oldTableList)) {
                                                // 如果模板不为空，需要把数据全部新增

                                                for (TemplateTableVo row : rowList) {
// 新增
                                                    // 组装表格每列信息
                                                    if (row.getStatus().equals(BusinessConfig.VALID_STATUS)){
                                                        TemplateFormGroupTable table = new TemplateFormGroupTable();
                                                        table.setId(SnowflakeIdWorker.getUuid());
                                                        table.setResourceGroupId(templateFormDetailVo.getGroupId());
                                                        table.setResourceTableId(row.getId());
                                                        table.setResourceVariableId(templateFormDetailVo.getId());
                                                        table.setGroupId(groupVariable.getGroupId());
                                                        table.setFormDetailId(groupVariable.getId());
                                                        table.setProjectId(Long.parseLong(projectId));
                                                        table.setVisitId(Long.parseLong(visitId));
                                                        table.setFormId(Long.parseLong(formId));
                                                        table.setTesteeId(Long.parseLong(testeeId));
                                                        table.setSort(row.getSort());
                                                        table.setStatus(BusinessConfig.VALID_STATUS);
                                                        table.setCreateTime(new Date());
                                                        table.setCreateUserId(userId);
                                                        table.setPlanId(Long.parseLong(finalPlanId));
                                                        table.setTenantId(SecurityUtils.getSystemTenantId());
                                                        table.setPlatformId(SecurityUtils.getSystemPlatformId());
                                                        templateFormGroupTableMapper.insert(table);
                                                    }
                                                }
                                            }
                                            if (CollectionUtil.isNotEmpty(rowList) && CollectionUtil.isNotEmpty(oldTableList)) {
                                                Map<String ,TemplateFormGroupTable> oMap = new HashMap<>();
                                                for (TemplateFormGroupTable table : oldTableList) {
                                                    oMap.put(table.getResourceVariableId()+"-"+table.getResourceTableId(),table);
                                                }
                                                rowList.forEach(row->{
                                                    TemplateFormGroupTable table1 = oMap.get(row.getFormDetailId() + "-" + row.getId());
                                                    if (table1 ==null){
                                                        if (row.getStatus().equals(BusinessConfig.VALID_STATUS)) {
                                                            TemplateFormGroupTable table = new TemplateFormGroupTable();
                                                            table.setResourceGroupId(templateFormDetailVo.getGroupId());
                                                            table.setResourceTableId(row.getId());
                                                            table.setResourceVariableId(templateFormDetailVo.getId());
                                                            table.setCreateTime(new Date());
                                                            table.setUpdateTime(new Date());
                                                            table.setCreateUserId(userId);
                                                            table.setUpdateUserId(userId);
                                                            table.setSort(row.getSort());
                                                            table.setGroupId(groupVariable.getGroupId());
                                                            table.setFormDetailId(groupVariable.getId());
                                                            table.setTesteeId(Long.parseLong(testeeId));
                                                            table.setProjectId(Long.parseLong(projectId));
                                                            table.setVisitId(Long.parseLong(visitId));
                                                            table.setFormId(Long.parseLong(formId));
                                                            table.setId(SnowflakeIdWorker.getUuid());
                                                            table.setStatus(BusinessConfig.VALID_STATUS);
                                                            table.setPlanId(Long.parseLong(finalPlanId));
                                                            table.setTenantId(SecurityUtils.getSystemTenantId());
                                                            table.setPlatformId(SecurityUtils.getSystemPlatformId());
                                                            templateFormGroupTableMapper.insert(table);
                                                        }
                                                    }else {
                                                        // 存在检查是否已经更新
                                                        if (row.getStatus().equals(BusinessConfig.NO_VALID_STATUS)) {
                                                            table1.setStatus(BusinessConfig.NO_VALID_STATUS);
                                                            templateFormGroupTableMapper.updateByPrimaryKey(table1);
                                                        }
                                                    }
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            });
        }
        return CommonResult.success("","组初始化成功");
    }

    @Override
    public List<String> saveBatchTesteeFormGroupDetail(String projectId, List<TemplateFormGroupParam> groupParamList, String userId) {
        FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
        List<String> groupList = new ArrayList<>();
        for (TemplateFormGroupParam templateFormGroupParam : groupParamList) {
            if(flowPlanInfo != null){templateFormGroupParam.setPlanId(flowPlanInfo.getId());}
            CommonResult<Object> commonResult = saveTesteeFormGroupDetail(templateFormGroupParam, userId);
            groupList.add(commonResult.getData().toString());
        }
        return groupList;
    }
}
