package com.haoys.user.mapper;

import com.haoys.user.domain.entity.ProjectMenuQuery;
import com.haoys.user.domain.vo.auth.ProjectMenuVo;
import com.haoys.user.model.ProjectMenu;
import com.haoys.user.model.ProjectMenuExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface ProjectMenuMapper {

    long countByExample(ProjectMenuExample example);

    int deleteByExample(ProjectMenuExample example);

    int deleteByPrimaryKey(@Param("projectId") Long projectId, @Param("menuId") Long menuId);

    int insert(ProjectMenu record);

    int insertSelective(ProjectMenu record);

    List<ProjectMenu> selectByExample(ProjectMenuExample example);

    ProjectMenu selectByPrimaryKey(@Param("projectId") Long projectId, @Param("menuId") Long menuId);

    int updateByExampleSelective(@Param("record") ProjectMenu record, @Param("example") ProjectMenuExample example);

    int updateByExample(@Param("record") ProjectMenu record, @Param("example") ProjectMenuExample example);

    int updateByPrimaryKeySelective(ProjectMenu record);

    int updateByPrimaryKey(ProjectMenu record);

    /**
     * 批量新增项目菜单信息
     */
    void batchSaveProjectMenuList(List<ProjectMenuQuery> projectMenuQueryList);

    /**
     * 删除项目和菜单关联信息
     */
    int deleteProjectMenuByProjectId(Long projectId);

    /**
     * 根据项目ID查询菜单
     * @return
     */
    List<ProjectMenu> selectProjectMenuByProjectId(Long projectId);

    /**
     * 查询项目角色模版
     * @param projectId
     * @param roleCode
     * @return
     */
    List<ProjectMenuVo> selectProjectTemplateMenuInfo(String projectId, String roleCode);

    /**
     * 查询项目角色模版菜单
     * @param systemProjectMennTemplateId
     * @return
     */
    List<ProjectMenuQuery> getProjectMenuListForTemplate(String systemProjectMennTemplateId);
}
