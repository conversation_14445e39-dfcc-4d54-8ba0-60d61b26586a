package com.haoys.user.mapper;

import com.haoys.user.domain.vo.ecrf.TemplateFormDvpRuleVo;
import com.haoys.user.model.TemplateFormDvpRule;
import com.haoys.user.model.TemplateFormDvpRuleExample;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface TemplateFormDvpRuleMapper {
    long countByExample(TemplateFormDvpRuleExample example);

    int deleteByExample(TemplateFormDvpRuleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TemplateFormDvpRule record);

    int insertSelective(TemplateFormDvpRule record);

    List<TemplateFormDvpRule> selectByExampleWithBLOBs(TemplateFormDvpRuleExample example);

    List<TemplateFormDvpRule> selectByExample(TemplateFormDvpRuleExample example);

    TemplateFormDvpRule selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TemplateFormDvpRule record, @Param("example") TemplateFormDvpRuleExample example);

    int updateByExampleWithBLOBs(@Param("record") TemplateFormDvpRule record, @Param("example") TemplateFormDvpRuleExample example);

    int updateByExample(@Param("record") TemplateFormDvpRule record, @Param("example") TemplateFormDvpRuleExample example);

    int updateByPrimaryKeySelective(TemplateFormDvpRule record);

    int updateByPrimaryKeyWithBLOBs(TemplateFormDvpRule record);

    int updateByPrimaryKey(TemplateFormDvpRule record);

    List<TemplateFormDvpRuleVo> getProjectTemplateDVPRuleListForPage(Map<String, Object> params);
}
