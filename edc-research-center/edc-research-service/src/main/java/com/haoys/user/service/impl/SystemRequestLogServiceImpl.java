package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.bean.BeanUtils;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.excel.ExcelUtils;
import com.haoys.user.common.excel.Template.SystemRequestLogExcel;
import com.haoys.user.domain.entity.SystemRequestLogQuery;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.mapper.SystemRequestLogMapper;
import com.haoys.user.model.SystemRequestLog;
import com.haoys.user.service.SystemRequestLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


@Service
public class SystemRequestLogServiceImpl extends BaseService implements SystemRequestLogService {

    @Autowired
    private SystemRequestLogMapper systemRequestLogMapper;

    @Override
    public void insertOperlog(SystemRequestLog systemRequestLog) {
        systemRequestLogMapper.insert(systemRequestLog);
    }

    @Override
    public CommonPage<SystemRequestLogQuery> selectRequestLogListForPage(SystemRequestLogQuery operLog, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum,pageSize);
        return commonPageListWrapper(pageNum, pageSize, page, systemRequestLogMapper.selectRequestLogListForPage(operLog));
    }

    @Override
    public int deleteOperLogByIds(Long[] operIds) {
        return systemRequestLogMapper.deleteOperLogByIds(operIds);
    }

    @Override
    public SystemRequestLogQuery selectOperLogById(Long operId) {
        SystemRequestLog systemRequestLog = systemRequestLogMapper.selectByPrimaryKey(operId);
        SystemRequestLogQuery systemRequestLogQuery = new SystemRequestLogQuery();
        BeanUtils.copyProperties(systemRequestLog, systemRequestLogQuery);
        return systemRequestLogQuery;
    }

    @Override
    public void cleanOperLog() {
        systemRequestLogMapper.clearSystemRequestLog();
    }

    @Override
    public void exportSystemOperLog(HttpServletResponse response, SystemRequestLogQuery operLog) {
        List<SystemRequestLogQuery> systemRequestLogList = systemRequestLogMapper.selectRequestLogListForPage(operLog);
        List<SystemRequestLogExcel> list = new ArrayList<>();

        for(SystemRequestLogQuery sl :systemRequestLogList){
            SystemRequestLogExcel systemRequestLogExcel = new SystemRequestLogExcel();
            BeanUtils.copyProperties(sl, systemRequestLogExcel);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateString = sdf.format(sl.getCreateTime());
            systemRequestLogExcel.setCreateTime(dateString);
            if(sl.getBusinessType().equals(BusinessType.INSERT.value())){
                systemRequestLogExcel.setBusinessType("新增");
            }else if(sl.getBusinessType().equals(BusinessType.UPDATE.value())){
                systemRequestLogExcel.setBusinessType("修改");
            }else if(sl.getBusinessType().equals(BusinessType.DELETE.value())){
                systemRequestLogExcel.setBusinessType("删除");
            }else {
                systemRequestLogExcel.setBusinessType("其它");
            }
            if(sl.getStatus() == 0){
                systemRequestLogExcel.setStatus("是");
            }else if(sl.getStatus() == 1){
                systemRequestLogExcel.setStatus("否");
            }
            list.add(systemRequestLogExcel);
        }
        try {
            ExcelUtils.export(response, "系统操作日志", list, SystemRequestLogExcel.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
