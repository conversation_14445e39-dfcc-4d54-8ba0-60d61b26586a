package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.project.ProjectTesteeStatisticsParam;
import com.haoys.user.domain.statvo.ProjectTesteeFormAduitCountVo;
import com.haoys.user.domain.statvo.ProjectTesteeProcessCountVo;
import com.haoys.user.domain.vo.testee.ProjectTesteeStatisticsVo;
import com.haoys.user.expand.ProjectUserExpand;
import com.haoys.user.model.ProjectVisitConfig;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 访视统计service
 */
public interface ProjectTesteeStatisticsService {
    
    
    CommonPage<ProjectTesteeStatisticsVo> list(ProjectTesteeStatisticsParam param);
    
    ProjectTesteeProcessCountVo getProjectTesteeProcessCount(String projectId, String visitId, String createUserId);
    
    ProjectTesteeFormAduitCountVo getProjectTesteeFormAuditCount(String projectId, String visitId, String createUserId);
    
    int getProjectTesteeCount(String projectId, String createUserId);
    
    CommonPage<ProjectUserExpand> getProjectUserExpandListForPage(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String enterpriseId, String group, String realName, String username, Integer status, Boolean activeStatus, Boolean lockStatus, Integer pageNum,Integer pageSize);
    
    CommonPage<ProjectUserExpand> getProjectMedicalReviewList(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, Integer pageNum, Integer pageSize);
    
    CommonPage<ProjectUserExpand> getProjectTesteeListForBoRui(List<ProjectVisitConfig> projectVisitConfigList, String projectId, String testeeCode, String realName, Integer pageNum, Integer pageSize);
}
