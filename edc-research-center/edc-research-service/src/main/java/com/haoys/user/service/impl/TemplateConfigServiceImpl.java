package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.expand.TemplateLabConfigExpand;
import com.haoys.user.domain.param.crf.TemplateCustomFormConfigParam;
import com.haoys.user.domain.param.crf.TemplateFormConfigBaseParam;
import com.haoys.user.domain.param.crf.TemplateFormConfigParam;
import com.haoys.user.domain.param.crf.TemplateFormDetailParam;
import com.haoys.user.domain.param.crf.TemplateFormLogicParam;
import com.haoys.user.domain.param.crf.TemplateTableConfigParam;
import com.haoys.user.domain.param.flow.FlowParam;
import com.haoys.user.domain.param.lab.TemplateLabConfigParam;
import com.haoys.user.domain.param.project.ProjectVisitConfigParam;
import com.haoys.user.domain.template.TemplateFormVariableWrapper;
import com.haoys.user.domain.vo.ecrf.FormVariableComplate;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeFormDetailAndTableResultVo;
import com.haoys.user.domain.vo.ecrf.ProjectTesteeTableVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormConfigVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDetailVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormDictionaryVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormLogicVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormTableExcelImportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableExportVo;
import com.haoys.user.domain.vo.ecrf.TemplateFormVariableViewVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableConfigResultVo;
import com.haoys.user.domain.vo.ecrf.TemplateTableVo;
import com.haoys.user.domain.vo.ecrf.TemplateTesteeJoinGroupFormVo;
import com.haoys.user.domain.vo.ecrf.TemplateVariableVo;
import com.haoys.user.domain.vo.ecrf.TemplateVo;
import com.haoys.user.domain.vo.flow.FlowPlanFormVo;
import com.haoys.user.domain.vo.lab.TemplateLabConfigVo;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.domain.vo.project.ProjectVisitVo;
import com.haoys.user.domain.vo.rcts.RandomizedVo;
import com.haoys.user.mapper.TemplateFormConfigMapper;
import com.haoys.user.mapper.TemplateFormDetailMapper;
import com.haoys.user.mapper.TemplateFormTableMapper;
import com.haoys.user.mapper.TemplateLabConfigMapper;
import com.haoys.user.mapper.TemplateLabOrgInfoMapper;
import com.haoys.user.mapper.TemplateMapper;
import com.haoys.user.model.Dictionary;
import com.haoys.user.model.FlowFormSet;
import com.haoys.user.model.FlowPlan;
import com.haoys.user.model.FlowPlanFormInfo;
import com.haoys.user.model.Organization;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectDictionary;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectTesteeResult;
import com.haoys.user.model.ProjectTesteeResultExample;
import com.haoys.user.model.ProjectVisitConfig;
import com.haoys.user.model.Template;
import com.haoys.user.model.TemplateExample;
import com.haoys.user.model.TemplateFormConfig;
import com.haoys.user.model.TemplateFormConfigExample;
import com.haoys.user.model.TemplateFormDetail;
import com.haoys.user.model.TemplateFormDetailExample;
import com.haoys.user.model.TemplateFormLogic;
import com.haoys.user.model.TemplateFormTable;
import com.haoys.user.model.TemplateFormTableExample;
import com.haoys.user.model.TemplateFormVariableRule;
import com.haoys.user.model.TemplateLabConfig;
import com.haoys.user.model.TemplateLabOrgInfo;
import com.haoys.user.model.TemplateLabOrgInfoExample;
import com.haoys.user.model.TemplateVariableViewBase;
import com.haoys.user.service.DictionaryService;
import com.haoys.user.service.FlowFormSetService;
import com.haoys.user.service.FlowPlanFormService;
import com.haoys.user.service.FlowPlanService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectDictionaryService;
import com.haoys.user.service.ProjectTesteeResultService;
import com.haoys.user.service.ProjectTesteeTableService;
import com.haoys.user.service.ProjectVisitConfigService;
import com.haoys.user.service.RandomizedControlledTrialsService;
import com.haoys.user.service.TemplateConfigService;
import com.haoys.user.service.TemplateFormLogicService;
import com.haoys.user.service.TemplateFormVariableRuleService;
import com.haoys.user.service.TemplateFormVariableViewConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class TemplateConfigServiceImpl extends BaseService implements TemplateConfigService {

    private final TemplateMapper templateMapper;
    private final ProjectBaseManageService projectBaseManageService;
    private final ProjectVisitConfigService projectVisitConfigService;
    private final TemplateFormConfigMapper templateFormConfigMapper;
    private final TemplateFormDetailMapper templateFormDetailMapper;
    private final TemplateFormTableMapper templateFormTableMapper;
    private final TemplateLabConfigMapper templateLabConfigMapper;
    private final TemplateLabOrgInfoMapper templateLabOrgInfoMapper;


    private final OrganizationService organizationService;
    private final DictionaryService dictionaryService;
    private final ProjectDictionaryService projectDictionaryService;
    private final FlowPlanService flowPlanService;
    private final FlowPlanFormService flowPlanFormService;
    private final FlowFormSetService flowFormSetService;
    private final ProjectTesteeResultService projectTesteeResultService;
    private final ProjectTesteeTableService projectTesteeTableService;
    private final TemplateFormLogicService templateFormLogicService;
    private final RedisTemplateService redisTemplateService;

    private final TemplateFormVariableRuleService variableRuleService;
    private final TemplateFormVariableViewConfigService templateFormVariableViewConfigService;
    private final RandomizedControlledTrialsService randomizedControlledTrialsService;

    @Override
    public void saveTemplateBaseInfo(Template template) {
        templateMapper.insertSelective(template);
    }

    @Override
    public Template getTemplateBaseInfoById(Long templateId) {
        return templateMapper.selectByPrimaryKey(templateId);
    }

    public Template getTemplateBaseInfoByTemplateName(String projectId, String templateName) {
        return templateMapper.getTemplateBaseInfoByTemplateName(projectId, templateName);
    }

    @Override
    public CommonPage<TemplateVo> getProjectTemplateListForPage(String templateName, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(templateName)) {
            params.put("templateName", templateName);
        }
        List<TemplateVo> dataList = templateMapper.getProjectTemplateListForPage(params);
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }

    @Override
    public TemplateVo getProjectTemplateInfo(String templateId) {
        TemplateVo templateVo = new TemplateVo();
        Template template = templateMapper.selectByPrimaryKey(Long.parseLong(templateId));
        if (template != null) {
            BeanUtils.copyProperties(template, templateVo);
            return templateVo;
        }
        return null;
    }

    @Override
    public String modifyProjectTemplateStatus(String templateId, String templateStatus, String userId) {
        Template template = templateMapper.selectByPrimaryKey(Long.parseLong(templateId));
        if (template == null) {
            return BusinessConfig.RETURN_MESSAGE_RECORD_NOT_FOUND;
        }
        template.setStatus(templateStatus);
        template.setUpdateTime(new Date());
        template.setUpdateUserId(userId);
        templateMapper.updateByPrimaryKeySelective(template);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public CustomResult<Object> saveTemplateConfig(String templateId, String templateName, String templateStatus, String templateDesc, String userId) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        Template template = templateMapper.selectByPrimaryKey(Long.parseLong(templateId));
        if (template != null) {
            template.setName(templateName);
            if (StringUtils.isNotEmpty(templateStatus)) {
                template.setStatus(templateStatus);
            }
            template.setDescription(templateDesc);
            template.setUpdateUserId(userId);
            template.setUpdateTime(new Date());
            templateMapper.updateByPrimaryKeySelective(template);
        }
        return customResult;
    }

    @Override
    public CustomResult<Object> saveBatchTemplateFormConfigBaseInfo(List<TemplateFormConfigBaseParam> templateFormConfigParamList, String userId) {
        AtomicReference<CustomResult<Object>> customResult = new AtomicReference<>(new CustomResult<Object>());
        templateFormConfigParamList.forEach(templateFormConfigParam -> {
            customResult.set(saveTemplateFormConfigBaseInfo(templateFormConfigParam));
        });
        return customResult.get();
    }

    @Override
    public CustomResult<Object> saveTemplateFormConfig(TemplateFormConfigParam templateFormConfigParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        if (templateFormConfigParam.getTemplateId() == null & templateFormConfigParam.getProjectId() == null) {
            customResult.setCode(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getCode());
            customResult.setMessage(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getMessage());
            return customResult;
        }
        TemplateFormConfig templateFormConfig = new TemplateFormConfig();
        // 另存为模版
        if (templateFormConfigParam.getCreateTemplate()) {
            if (StringUtils.isNotEmpty(templateFormConfigParam.getTemplateName())) {
                TemplateExample example = new TemplateExample();
                TemplateExample.Criteria criteria = example.createCriteria();
                criteria.andNameEqualTo(templateFormConfigParam.getTemplateName());
                criteria.andConfigTypeEqualTo(templateFormConfigParam.getConfigType());
                if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02.equals(templateFormConfigParam.getConfigType())) {
                    criteria.andProjectIdEqualTo(templateFormConfigParam.getProjectId());
                }
                if (templateMapper.countByExample(example) > 0) {
                    customResult.setMessage(BusinessConfig.SYSTEM_TEMPLATE_RECORD_FOUND);
                    return customResult;
                }
            }
            TemplateFormConfig templateFormConfigCopyVo = this.getTemplateFormConfigById(templateFormConfigParam.getCopyFormId());
            if (templateFormConfigCopyVo != null) {
                templateFormConfigParam.setFormName(templateFormConfigParam.getTemplateName());
            }

            Template template = new Template();
            if (templateFormConfigParam.getTemplateId() == null) {
                template.setId(SnowflakeIdWorker.getUuid());
                template.setName(templateFormConfigParam.getTemplateName());
                template.setCode(templateFormConfigParam.getTemplateCode());
                template.setIfPrivate(false);
                template.setConfigType(templateFormConfigParam.getConfigType());
                template.setStatus(BusinessConfig.VALID_STATUS);
                if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02.equals(templateFormConfigParam.getConfigType())) {
                    template.setProjectId(templateFormConfigParam.getProjectId());
                    templateFormConfigParam.setProjectId(null);
                }
                template.setDescription(templateFormConfigParam.getDescription());
                template.setCreateUserId(templateFormConfigParam.getCreateUser());
                template.setCreateTime(new Date());
                templateMapper.insertSelective(template);
                templateFormConfigParam.setTemplateId(template.getId());
                templateFormConfig.setTemplateId(template.getId());
            }
        }

        if (templateFormConfigParam.getFormId() == null) {
            //保存表单项
            if (templateFormConfigParam.getTemplateId() != null) {
                templateFormConfig.setTemplateId(templateFormConfigParam.getTemplateId());
            }
            templateFormConfig.setId(SnowflakeIdWorker.getUuid());
            templateFormConfig.setFormName(templateFormConfigParam.getFormName());
            if (templateFormConfigParam.getFormCode().equals(Constants.PROJECT_FORM_JOIN_GROUP_NAME)) {
                templateFormConfig.setJoinGroup(true);
            }
            templateFormConfig.setProjectId(templateFormConfigParam.getProjectId());
            templateFormConfig.setGroupName(templateFormConfigParam.getGroupName());

            templateFormConfig.setFormConfig(templateFormConfigParam.getFormConfig());
            templateFormConfig.setFormType(templateFormConfigParam.getFormType());
            templateFormConfig.setFormCode(templateFormConfigParam.getFormCode());
            templateFormConfig.setVersion(templateFormConfigParam.getVersion());
            templateFormConfig.setOpenEpro(templateFormConfigParam.getOpenEpro());
            templateFormConfig.setUploadResourceFile(templateFormConfigParam.getUploadResourceFile());
            templateFormConfig.setTesteeForm(templateFormConfigParam.getTesteeForm());

            templateFormConfig.setCreateTime(new Date());
            templateFormConfig.setCreateUser(templateFormConfigParam.getCreateUser());
            templateFormConfig.setTenantId(SecurityUtils.getSystemTenantId());
            templateFormConfig.setPlatformId(SecurityUtils.getSystemPlatformId());

            templateFormConfig.setStatus(BusinessConfig.VALID_STATUS);
            if (templateFormConfig.getSort() == null) {
                Integer formSort = getTemplateFormSortValue(templateFormConfigParam.getTemplateId(), templateFormConfigParam.getProjectId());
                templateFormConfig.setSort(formSort);
            } else {
                templateFormConfig.setSort(templateFormConfigParam.getSort());
            }
            if (StringUtils.isNotEmpty(templateFormConfigParam.getStatus())) {
                templateFormConfig.setStatus(templateFormConfigParam.getStatus());
            }
            templateFormConfigMapper.insertSelective(templateFormConfig);
        } else {
            templateFormConfig = templateFormConfigMapper.selectByPrimaryKey(templateFormConfigParam.getFormId());

            if (Constants.PROJECT_FORM_JOIN_GROUP_NAME.equals(templateFormConfigParam.getFormCode())) {
                templateFormConfig.setJoinGroup(true);
            } else {
                templateFormConfig.setJoinGroup(false);
            }
            templateFormConfig.setFormName(templateFormConfigParam.getFormName());
            templateFormConfig.setFormConfig(templateFormConfigParam.getFormConfig());
            templateFormConfig.setFormType(templateFormConfigParam.getFormType());
            templateFormConfig.setFormCode(templateFormConfigParam.getFormCode());
            templateFormConfig.setVersion(templateFormConfigParam.getVersion());
            templateFormConfig.setOpenEpro(templateFormConfigParam.getOpenEpro());
            templateFormConfig.setUploadResourceFile(templateFormConfigParam.getUploadResourceFile());
            templateFormConfig.setTesteeForm(templateFormConfigParam.getTesteeForm());
            templateFormConfig.setUpdateTime(new Date());
            templateFormConfig.setUpdateUser(templateFormConfigParam.getCreateUser());
            if (templateFormConfigParam.getTemplateId() != null) {
                templateFormConfig.setTemplateId(templateFormConfigParam.getTemplateId());
                Template template = templateMapper.selectByPrimaryKey(templateFormConfigParam.getTemplateId());
                if (template != null) {
                    template.setName(templateFormConfigParam.getTemplateName());
                    template.setUpdateUserId(templateFormConfigParam.getCreateUser());
                    template.setUpdateTime(new Date());
                    templateMapper.updateByPrimaryKeySelective(template);
                }
                if (StringUtils.isNotEmpty(templateFormConfigParam.getTemplateName())) {
                    templateFormConfig.setFormName(templateFormConfigParam.getTemplateName());
                }
            }
            if (templateFormConfigParam.getGlobalScope() != null) {
                templateFormConfig.setGlobalScope(templateFormConfigParam.getGlobalScope());
            }
            if (templateFormConfigParam.getSort() != null) {
                templateFormConfig.setSort(templateFormConfigParam.getSort());
            }
            templateFormConfigMapper.updateByPrimaryKeySelective(templateFormConfig);
        }
        //保存表单明细
        List<TemplateFormDetailParam> templateFormDetailList = templateFormConfigParam.getFormDetailParamList();
        for (TemplateFormDetailParam templateFormDetailParam : templateFormDetailList) {
            TemplateFormDetail templateFormDetail = new TemplateFormDetail();
            if (templateFormConfigParam.getCopyTemplateForm()) {
                // 如果复制模板逻辑
                templateFormConfig.setProjectId(null);
                templateFormDetailParam.setProjectId(null);
                templateFormDetailParam.setId(null);
                templateFormDetailParam.setCreateFormTemplate(true);
            }
            if (templateFormConfigParam.getCreateTemplate()) {
                templateFormDetailParam.setId(null);
                templateFormDetailParam.setCreateFormTemplate(true);
            }
            // 字段组复制
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailParam.getType())) {
                if (templateFormDetailParam.getId() == null) {
                    templateFormDetailParam.setTemplateId(templateFormConfig.getTemplateId());
                    templateFormDetailParam.setProjectId(null);
                    templateFormDetailParam.setFormId(templateFormConfig.getId());
                    templateFormDetailParam.setFieldName(RandomStringUtils.randomAlphanumeric(8));
                    templateFormDetailParam.setModel(SnowflakeIdWorker.getUuidValue());
                    templateFormDetailParam.setShowTitle(templateFormDetailParam.getShowTitle());
                    templateFormDetailParam.setShowContent(templateFormDetailParam.getShowContent());
                    CustomResult<Object> customGroupResult = this.saveTemplateFormDetailConfig(templateFormDetailParam);
                    Map<String, Object> dataMap = (Map<String, Object>) customGroupResult.getData();
                    String templateFormDetailGroupId = dataMap.get("id").toString();
                    List<TemplateFormDetailParam> groupTemplateBaseVariableList = templateFormDetailParam.getGroupTemplateBaseVariableList();
                    for (TemplateFormDetailParam templateFormDetailGroupParam : groupTemplateBaseVariableList) {
                        TemplateFormDetail templateFormDetailGroup = new TemplateFormDetail();
                        BeanUtils.copyProperties(templateFormDetail, templateFormDetailGroup);
                        templateFormDetailGroup.setId(SnowflakeIdWorker.getUuid());
                        templateFormDetailGroupParam.setGroupId(Long.parseLong(templateFormDetailGroupId));
                        if (templateFormConfigParam.getCreateTemplate()) {
                            templateFormDetailGroupParam.setCreateFormTemplate(true);
                        }
                        templateFormDetailGroup.setShowTitle(templateFormDetailGroupParam.getShowTitle());
                        templateFormDetailGroup.setShowContent(templateFormDetailGroupParam.getShowContent());
                        if (StringUtils.isNotBlank(templateFormDetailGroupParam.getExpand())) {
                            templateFormDetailGroup.setExpand(templateFormDetailGroupParam.getExpand());
                        }
                        CustomResult<Object> customResult1 = buildBatchFormConfigParams(templateFormConfig, templateFormDetailGroupParam, templateFormDetailGroup);
                        if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult1.getMessage())) {
                            return customResult;
                        }
                        if (templateFormDetail.getSort() == null) {
                            Integer detailSortValue = getTemplateFormDetailSortValue(templateFormConfig.getId());
                            templateFormDetailGroup.setSort(detailSortValue);
                        }
                        templateFormDetailGroup.setGroupId(Long.parseLong(templateFormDetailGroupId));
                        templateFormDetailGroup.setCreateUser(templateFormConfigParam.getCreateUser());
                        templateFormDetailGroup.setCreateTime(new Date());
                        templateFormDetailGroup.setTenantId(SecurityUtils.getSystemTenantId());
                        templateFormDetailGroup.setPlatformId(SecurityUtils.getSystemPlatformId());
                        templateFormDetailMapper.insert(templateFormDetailGroup);
                    }
                    //redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VARIABLE.concat(templateFormDetailGroup.getId().toString()), JSON.toJSONString(templateFormDetailGroup));
                } else {
                    TemplateFormDetail templateFormDetailGroup = templateFormDetailMapper.selectByPrimaryKey(templateFormDetailParam.getId());
                    if (templateFormDetailGroup != null) {
                        BeanUtils.copyProperties(templateFormDetailParam, templateFormDetailGroup);
                        templateFormDetailGroup.setUpdateUser(templateFormConfigParam.getCreateUser());
                        templateFormDetailGroup.setUpdateTime(new Date());
                        templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetailGroup);
                        redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VARIABLE.concat(templateFormDetailGroup.getId().toString()), JSON.toJSONString(templateFormDetailGroup));
                    }
                }
            } else {
                if (templateFormDetailParam.getId() == null) {
                    templateFormDetail.setId(SnowflakeIdWorker.getUuid());
                    CustomResult<Object> customResult1 = buildBatchFormConfigParams(templateFormConfig, templateFormDetailParam, templateFormDetail);
                    if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult1.getMessage())) {
                        return customResult;
                    }
                    templateFormDetail.setShowTitle(templateFormDetailParam.getShowTitle());
                    templateFormDetail.setShowContent(templateFormDetailParam.getShowContent());
                    if (templateFormDetail.getSort() == null) {
                        Integer detailSortValue = getTemplateFormDetailSortValue(templateFormConfig.getId());
                        templateFormDetail.setSort(detailSortValue);
                    }
                    templateFormDetail.setCreateUser(templateFormConfigParam.getCreateUser());
                    templateFormDetail.setCreateTime(new Date());
                    templateFormDetail.setTenantId(SecurityUtils.getSystemTenantId());
                    templateFormDetail.setPlatformId(SecurityUtils.getSystemPlatformId());
                    templateFormDetailMapper.insert(templateFormDetail);
                } else {
                    templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(templateFormDetailParam.getId());
                    CustomResult<Object> customResult1 = buildBatchFormConfigParams(templateFormConfig, templateFormDetailParam, templateFormDetail);
                    if (!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(customResult1.getMessage())) {
                        return customResult;
                    }
                    templateFormDetail.setUpdateUser(templateFormConfigParam.getCreateUser());
                    templateFormDetail.setUpdateTime(new Date());
                    templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
                }
                // 设置表单缓存
                redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VARIABLE.concat(templateFormDetail.getId().toString()), JSON.toJSONString(templateFormDetail));
            }
        }
        customResult.setData(templateFormConfig.getId().toString());
        return customResult;
    }

    private Integer getTemplateFormSortValue(Long templateId, Long projectId) {
        TemplateFormConfigExample example = new TemplateFormConfigExample();
        TemplateFormConfigExample.Criteria criteria = example.createCriteria();
        if (templateId != null) {
            criteria.andTemplateIdEqualTo(templateId);
        }
        if (projectId != null) {
            criteria.andProjectIdEqualTo(projectId);
        }
        return templateFormConfigMapper.selectByExample(example).size() + 1;
    }

    private Integer getTemplateFormDetailSortValue(Long formId) {
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        criteria.andFormIdEqualTo(formId);
        return templateFormDetailMapper.selectByExample(example).size() + 1;
    }

    private CustomResult<Object> buildBatchFormConfigParams(TemplateFormConfig templateFormConfig, TemplateFormDetailParam templateFormDetailParam, TemplateFormDetail targetFormDetail) {
        BeanUtils.copyProperties(templateFormDetailParam, targetFormDetail);
        targetFormDetail.setTemplateId(templateFormConfig.getTemplateId());
        targetFormDetail.setProjectId(templateFormConfig.getProjectId());
        targetFormDetail.setFormId(templateFormConfig.getId());
        targetFormDetail.setId(targetFormDetail.getId());
        targetFormDetail.setModel(targetFormDetail.getType() + SnowflakeIdWorker.getUuidValue());
        targetFormDetail.setStatus(BusinessConfig.VALID_STATUS);
        // 处理表格数据
        TemplateTableConfigParam templateTableConfigParam = new TemplateTableConfigParam();
        if (templateFormDetailParam.getCreateFormTemplate()) {
            targetFormDetail.setModel(targetFormDetail.getType() + SnowflakeIdWorker.getUuidValue());
            templateTableConfigParam.setCreateFormTemplate(true);
        }
        templateTableConfigParam.setTemplateId(templateFormConfig.getTemplateId());
        templateTableConfigParam.setProjectId(templateFormConfig.getProjectId());
        templateTableConfigParam.setFormId(templateFormConfig.getId());
        templateTableConfigParam.setFormDetailId(targetFormDetail.getId());
        templateTableConfigParam.setCustomTable(templateFormDetailParam.getCustomTable());
        templateTableConfigParam.setCreateUserId(templateFormConfig.getCreateUser());
        List<TemplateTableConfigParam.TableHeadRowInfo> tableRowList = templateFormDetailParam.getTableRowList();
        templateTableConfigParam.setTableRowList(tableRowList);
        CustomResult<Object> customResult = saveTemplateTableConfig(templateTableConfigParam);
        return customResult;
    }


    @Override
    public List<TemplateFormDetailVo> getTemplateFormDetailConfigListByFormId(String templateId, String formId, String variableTableId, String queryTable, String queryGroup, String queryWithoutGroupId) {
        List<TemplateFormDetailVo> templateFormDetailVoArrayList = new ArrayList<>();
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(templateId)) {
            criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
        }
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        if (StringUtils.isNotEmpty(variableTableId)) {
            criteria.andIdEqualTo(NumberUtil.parseLong(variableTableId));
        }
        if ("0".equals(queryTable)) {
            criteria.andTypeNotEqualTo(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE);
        }
        if ("1".equals(queryGroup) || "0".equals(queryWithoutGroupId)) {
            criteria.andGroupIdIsNull();
        }
        if ("0".equals(queryGroup)) {
            criteria.andTypeNotEqualTo(BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
        }
        if ("1".equals(queryWithoutGroupId)) {
            criteria.andGroupIdIsNotNull();
        }
        example.setOrderByClause("sort asc, create_time asc");
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        for (TemplateFormDetail templateFormDetail : templateFormDetailList) {
            TemplateFormDetailVo templateFormDetailVo = new TemplateFormDetailVo();
            BeanUtils.copyProperties(templateFormDetail, templateFormDetailVo);
            if (StringUtils.isNotEmpty(templateFormDetail.getExpand())) {
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetail.getType())) {
                    JSONArray parseArray = JSON.parseArray(templateFormDetail.getExpand());
                    templateFormDetailVo.setExpandValue(parseArray);
                } else {
                    Map<String, Object> dataMap = JSON.parseObject(templateFormDetail.getExpand(), new TypeReference<HashMap<String, Object>>(){}.getType());
                    templateFormDetailVo.setExpandValue(dataMap);
                }
            }
            // 设置查询选项联动配置
            if (templateFormDetailVo.getEnableViewConfig()) {
                List<TemplateVariableViewBase> templateVariableOptionList = templateFormVariableViewConfigService.getTemplateVariableOptionListByVariableId(templateFormDetailVo.getProjectId()!=null?templateFormDetailVo.getProjectId().toString():"", formId, templateFormDetailVo.getId().toString(), "");
                templateFormDetailVo.setTemplateVariableViewBaseList(templateVariableOptionList);
            }
            projectFormDictionaryWrapper(templateFormDetail, templateFormDetailVo);

            // 字段组
            List<String> varTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_GROUP, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
            if (varTypeList.contains(templateFormDetail.getType())) {
                Long groupId = templateFormDetailVo.getId();
                List<TemplateFormDetailVo> templateFormDetailInfoList = this.getTemplateFormDetailByGroupId(formId, groupId.toString(), "", false);
                for (TemplateFormDetailVo formDetaiInfo : templateFormDetailInfoList) {
                    if (formDetaiInfo.getType().equals(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE)) {
                        List<TemplateTableVo> templateTableVoArrayList = new ArrayList<>();
                        TemplateFormTableExample templateFormTableExample = new TemplateFormTableExample();
                        TemplateFormTableExample.Criteria criteria1 = templateFormTableExample.createCriteria();
                        criteria1.andFormDetailIdEqualTo(formDetaiInfo.getId());
                        criteria1.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                        templateFormTableExample.setOrderByClause("sort asc, create_time asc");
                        List<TemplateFormTable> templateFormTableList = templateFormTableMapper.selectByExample(templateFormTableExample);
                        for (TemplateFormTable templateFormTable : templateFormTableList) {
                            TemplateTableVo templateTableVo = new TemplateTableVo();
                            BeanUtils.copyProperties(templateFormTable, templateTableVo);
                            templateTableVo.setId(templateFormTable.getId());
                            if (StringUtils.isNotEmpty(templateFormTable.getExpand())) {
                                Map<String, Object> dataMap = JSON.parseObject(templateFormTable.getExpand(), new TypeReference<HashMap<String,Object>>(){}.getType());
                                templateTableVo.setExpandValue(dataMap);
                            }
                            projectTableDictionaryWrapper(templateFormTable, templateTableVo);
                            templateTableVo.setSort(templateFormTable.getSort());
                            templateTableVo.setCreateTime(templateFormTable.getCreateTime());
                            templateTableVoArrayList.add(templateTableVo);
                        }
                        formDetaiInfo.setTableHeadRowList(templateTableVoArrayList);
                    }
                }
                templateFormDetailVo.setGroupTemplateBaseVariableList(templateFormDetailInfoList);
            }
            // 表格
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetail.getType())) {
                List<TemplateTableVo> tableHeadRowList = new ArrayList<>();
                TemplateFormTableExample templateFormTableExample = new TemplateFormTableExample();
                TemplateFormTableExample.Criteria criteria1 = templateFormTableExample.createCriteria();
                criteria1.andFormDetailIdEqualTo(templateFormDetail.getId());
                criteria1.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                templateFormTableExample.setOrderByClause("sort asc, create_time asc");
                List<TemplateFormTable> templateFormTableList = templateFormTableMapper.selectByExample(templateFormTableExample);
                for (TemplateFormTable templateFormTable : templateFormTableList) {
                    TemplateTableVo templateTableVo = new TemplateTableVo();
                    BeanUtils.copyProperties(templateFormTable, templateTableVo);
                    templateTableVo.setId(templateFormTable.getId());
                    if (StringUtils.isNotEmpty(templateFormTable.getExpand())) {
                        Map<String, Object> dataMap = JSON.parseObject(templateFormTable.getExpand(), new TypeReference<HashMap<String, Object>>() {
                        }.getType());
                        templateTableVo.setExpandValue(dataMap);
                    }
                    projectTableDictionaryWrapper(templateFormTable, templateTableVo);
                    templateTableVo.setSort(templateFormTable.getSort());
                    templateTableVo.setCreateTime(templateFormTable.getCreateTime());

                    // 获取公式计算配置
                    TemplateFormVariableRule rule = variableRuleService.getFormVariableRule(null, null, formId, templateFormTable.getId().toString());
                    templateTableVo.setVariableRule(rule);

                    // 设置查询选项联动配置
                    if (templateFormTable.getEnableViewConfig() != null && templateFormTable.getEnableViewConfig()) {
                        List<TemplateVariableViewBase> templateVariableOptionList = templateFormVariableViewConfigService.getTemplateVariableOptionListByVariableId(templateFormDetail.getProjectId()!=null?templateFormDetail.getProjectId().toString():null, formId, templateFormDetailVo.getId().toString(), templateFormTable.getId().toString());
                        templateTableVo.setTemplateVariableViewBaseList(templateVariableOptionList);
                    }
                    tableHeadRowList.add(templateTableVo);
                }
                templateFormDetailVo.setTableHeadRowList(tableHeadRowList);
            }
            templateFormDetailVoArrayList.add(templateFormDetailVo);
        }
        //templateFormDetailVoArrayList.sort(Comparator.comparing(TemplateFormDetailVo::getSort));
        return templateFormDetailVoArrayList;
    }

    private void projectTableDictionaryWrapper(TemplateFormTable templateFormTable, TemplateTableVo templateTableVo) {
        if (StringUtils.isNotEmpty(templateFormTable.getDicResource())) {
            List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();
            if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormTable.getDicResource())) {
                if(StringUtils.isEmpty(templateFormTable.getRefDicId())){return;}
                List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId(templateFormTable.getRefDicId(), "", "");
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });

            }
            if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormTable.getDicResource())) {
                if(StringUtils.isEmpty(templateFormTable.getRefDicId())){return;}
                String projectId = templateFormTable.getProjectId() == null ? "0" : templateFormTable.getProjectId().toString();
                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormTable.getRefDicId(), "", Constants.PROJECT_DIC_TYPE);
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormTable.getDicResource())) {
                if(StringUtils.isEmpty(templateFormTable.getRefDicId())){return;}
                String projectId = templateFormTable.getProjectId() == null ? "0" : templateFormTable.getProjectId().toString();
                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormTable.getRefDicId(), "", Constants.UNIT_DIC_TYPE);
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            templateTableVo.setTemplateFormDictionaryList(templateFormDictionaryList);
        }
    }

    private void projectFormDictionaryWrapper(TemplateFormDetail templateFormDetail, TemplateFormDetailVo templateFormDetailVo) {
        if (StringUtils.isNotEmpty(templateFormDetail.getDicResource())) {
            String projectId = null;
//            String projectId = templateFormDetail.getProjectId() == null ? "0" : templateFormDetail.getProjectId().toString();
            if (templateFormDetail.getTemplateId() != null) {
                Template template = templateMapper.selectByPrimaryKey(templateFormDetail.getTemplateId());
                if ("3".equals(template.getConfigType())) {
                    projectId = "0";
                } else {
                    projectId = template.getProjectId().toString();
                }
            } else {
                projectId = templateFormDetail.getProjectId().toString();
            }

            List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();
            if (BusinessConfig.PROJECT_VISIT_CRF_SYSTEM_FORM.equals(templateFormDetail.getDicResource())) {
                if(StringUtils.isEmpty(templateFormDetail.getRefDicId())){return;}
                List<Dictionary> dictionaryList = dictionaryService.getDictionaryListByParentId(templateFormDetail.getRefDicId(), "", "");
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM.equals(templateFormDetail.getDicResource())) {
                if(StringUtils.isEmpty(templateFormDetail.getRefDicId())){return;}
                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormDetail.getRefDicId(), "", Constants.PROJECT_DIC_TYPE);
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM.equals(templateFormDetail.getDicResource())) {
                if(StringUtils.isEmpty(templateFormDetail.getRefDicId())){return;}
                List<Dictionary> dictionaryList = projectDictionaryService.getDictionaryListByParentId(projectId, templateFormDetail.getRefDicId(), "", Constants.UNIT_DIC_TYPE);
                dictionaryList.forEach(dictionary -> {
                    TemplateFormDictionaryVo templateFormDictionaryVo = new TemplateFormDictionaryVo();
                    templateFormDictionaryVo.setId(dictionary.getId().toString());
                    templateFormDictionaryVo.setName(dictionary.getName());
                    templateFormDictionaryVo.setOptionValue(dictionary.getValue());
                    templateFormDictionaryList.add(templateFormDictionaryVo);
                });
            }
            templateFormDetailVo.setTemplateFormDictionaryList(templateFormDictionaryList);
        }
    }


    @Override
    public List<TemplateFormVariableWrapper> getTemplateFormVariableListByFormIdForPage(String templateId, String formId, String formDetailName, String type, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<TemplateFormVariableWrapper> templateFormVariableList = templateFormConfigMapper.getTemplateFormVariableListByFormIdForPage(templateId, formId, formDetailName, type);
        for (TemplateFormVariableWrapper templateFormVariableWrapper : templateFormVariableList) {
            if (templateFormVariableWrapper.getType().equals(BusinessConfig.PROJECT_VISIT_CRF_TABLE)) {
                List<TemplateTableVo> tableHeadRowList = new ArrayList<>();
                TemplateFormTableExample templateFormTableExample = new TemplateFormTableExample();
                TemplateFormTableExample.Criteria criteria = templateFormTableExample.createCriteria();
                criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
                criteria.andFormDetailIdEqualTo(Long.parseLong(templateFormVariableWrapper.getFormDetailId()));
                criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                templateFormTableExample.setOrderByClause("sort asc, create_time asc");
                List<TemplateFormTable> templateFormTableList = templateFormTableMapper.selectByExample(templateFormTableExample);
                for (TemplateFormTable templateFormTable : templateFormTableList) {
                    TemplateTableVo templateTableVo = new TemplateTableVo();
                    BeanUtils.copyProperties(templateFormTable, templateTableVo);
                    templateTableVo.setId(templateFormTable.getId());
                    templateTableVo.setSort(templateFormTable.getSort());
                    templateTableVo.setCreateTime(templateFormTable.getCreateTime());
                    tableHeadRowList.add(templateTableVo);
                }
                templateFormVariableWrapper.setTableHeadRowList(tableHeadRowList);
            }
        }
        return templateFormVariableList;
    }

    @Override
    public List<TemplateFormDetailVo> getTemplateFormDetailByGroupId(String formId, String groupId, String groupTableId, Boolean queryIgnoreStatus) {
        List<TemplateFormDetailVo> dataList = new ArrayList<>();
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andGroupIdEqualTo(Long.parseLong(groupId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        if (StringUtils.isNotEmpty(groupTableId)) {
            criteria.andIdEqualTo(Long.parseLong(groupTableId));
        }
        if (!queryIgnoreStatus) {
            criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        }
        example.setOrderByClause("sort asc, create_time asc");
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        for (TemplateFormDetail templateFormDetail : templateFormDetailList) {
            TemplateFormDetailVo templateFormDetailVo = new TemplateFormDetailVo();
            BeanUtils.copyProperties(templateFormDetail, templateFormDetailVo);
            if (StringUtils.isNotEmpty(templateFormDetail.getExpand())) {
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetail.getType())) {
                    JSONArray parseArray = JSON.parseArray(templateFormDetail.getExpand());
                    templateFormDetailVo.setExpandValue(parseArray);
                } else {
                    Map<String, Object> dataMap = JSON.parseObject(templateFormDetail.getExpand(), new TypeReference<HashMap<String, Object>>() {
                    }.getType());
                    templateFormDetailVo.setExpandValue(dataMap);
                }
            }
            projectFormDictionaryWrapper(templateFormDetail, templateFormDetailVo);
            if (templateFormDetail.getType().equals(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE)) {
                List<TemplateTableVo> tableHeadRowList = new ArrayList<>();
                TemplateFormTableExample templateFormTableExample = new TemplateFormTableExample();
                TemplateFormTableExample.Criteria criteria1 = templateFormTableExample.createCriteria();
                criteria1.andFormDetailIdEqualTo(templateFormDetail.getId());
                if (!queryIgnoreStatus) {
                    criteria1.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                }
                templateFormTableExample.setOrderByClause("sort asc, create_time asc");
                List<TemplateFormTable> templateFormTableList = templateFormTableMapper.selectByExample(templateFormTableExample);
                for (TemplateFormTable templateFormTable : templateFormTableList) {
                    TemplateTableVo templateTableVo = new TemplateTableVo();
                    BeanUtils.copyProperties(templateFormTable, templateTableVo);
                    if (StringUtils.isNotEmpty(templateFormTable.getExpand())) {
                        Map<String, Object> dataMap = JSON.parseObject(templateFormTable.getExpand(), new TypeReference<HashMap<String, Object>>() {
                        }.getType());
                        templateTableVo.setExpandValue(dataMap);
                    }
                    projectTableDictionaryWrapper(templateFormTable, templateTableVo);
                    templateTableVo.setId(templateFormTable.getId());
                    templateTableVo.setSort(templateFormTable.getSort());
                    templateTableVo.setCreateTime(templateFormTable.getCreateTime());
                    tableHeadRowList.add(templateTableVo);
                }
                templateFormDetailVo.setTableHeadRowList(tableHeadRowList);
            }

            dataList.add(templateFormDetailVo);
        }
        return dataList;
    }

    @Override
    public String getTemplateFormDetailConfigByFieldName(String fieldName) {
        return templateFormConfigMapper.getTemplateFormDetailConfigByFieldName(fieldName);
    }

    @Override
    public CustomResult<Object> savePublishTemplateFormConfig(String projectId, String planId, String formId, boolean publishStatus, String createUserId) {
        CustomResult<Object> customResult = new CustomResult<>();
        // 校验表单变量数据是否完整
        if (publishStatus) {
            FormVariableComplate formVariableComplate = checkFormVariableComplate(projectId, formId);
            if (formVariableComplate.getComplateStatus()) {
                customResult.setCode(ResultCode.PROJECT_FORM_PUBLISH_FIELD_NAME_NO_COMPLATE.getCode());
                customResult.setMessage(ResultCode.PROJECT_FORM_PUBLISH_FIELD_NAME_NO_COMPLATE.getMessage());
                customResult.setData(formVariableComplate);
                return customResult;
            }
        }
        return flowPlanFormService.insertFlowPlanFormInfo(projectId, planId, formId, publishStatus, createUserId);
    }

    private FormVariableComplate checkFormVariableComplate(String projectId, String formId) {
        FormVariableComplate formVariableComplate = new FormVariableComplate();
        List<FormVariableComplate.VariableInputMessage> variableInputMessageList = new ArrayList<>();
        List<TemplateFormDetail> templateFormDetailList = templateFormConfigMapper.getFormDetailBaseConfigListByFormId(projectId, formId);
        if (CollectionUtil.isEmpty(templateFormDetailList)) {
            formVariableComplate.setComplateStatus(true);
            return formVariableComplate;
        }
        templateFormDetailList.forEach(templateFormDetail -> {
            FormVariableComplate.VariableInputMessage variableInputMessage = new FormVariableComplate.VariableInputMessage();
            variableInputMessage.setVariableName(templateFormDetail.getLabel());
            if (StringUtils.isEmpty(templateFormDetail.getLabel()) || StringUtils.isEmpty(templateFormDetail.getFieldName())) {
                variableInputMessage.setMessage("字段名称或字段编码不能为空");
                variableInputMessageList.add(variableInputMessage);
            }
            //查询变量字典验证
            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX);
            if (variableTypeList.contains(templateFormDetail.getType())) {
                if (StringUtils.isEmpty(templateFormDetail.getRefDicId())) {
                    variableInputMessage.setMessage("表单必填项字典不能为空");
                    variableInputMessageList.add(variableInputMessage);
                }
            }
            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetail.getType())) {
                List<TemplateFormTable> templateFormTableList = templateFormConfigMapper.getFormTableBaseConfigListByFormDetailId(templateFormDetail.getId());
                templateFormTableList.forEach(templateFormTable -> {
                    FormVariableComplate.VariableInputMessage variableTableInputMessage = new FormVariableComplate.VariableInputMessage();
                    variableTableInputMessage.setVariableName(templateFormTable.getLabel());
                    if (StringUtils.isEmpty(templateFormTable.getLabel()) || StringUtils.isEmpty(templateFormTable.getFieldName())) {
                        variableTableInputMessage.setMessage("字段名称或字段编码不能为空");
                        variableInputMessageList.add(variableTableInputMessage);
                    }
                    if (variableTypeList.contains(templateFormTable.getType())) {
                        if (StringUtils.isEmpty(templateFormTable.getRefDicId())) {
                            variableInputMessage.setMessage("表单必填项字典不能为空");
                            variableInputMessageList.add(variableInputMessage);
                        }
                    }
                });
            }
        });
        formVariableComplate.setComplateStatus(variableInputMessageList.size() != 0);
        formVariableComplate.setInputMessageList(variableInputMessageList);
        return formVariableComplate;
    }

    @Override
    public CustomResult<Object> batchPublishFormTemplateFormConfig(String projectId, String formIds, String planId, String userId) {
        return flowPlanFormService.batchPublishFormTemplateFormConfig(projectId, planId, formIds, userId);
    }

    @Override
    public List<TemplateFormConfigVo> getTemplateFormConfigListByProjectId(String templateId, String projectId, String formName, Boolean publishStatus) {
        return templateFormConfigMapper.getTemplateFormConfigListByProjectId(templateId, projectId, formName, publishStatus);
    }

    @Override
    public Boolean getTemplateFormCode(String templateId, String projectId, String formCode) {
        return templateFormConfigMapper.getTemplateFormCode(templateId, projectId, formCode) != null;
    }

    @Override
    public Boolean getTemplateFormDetailCode(String projectId, String formId, String formDetailId, String fieldName) {
        if (StringUtils.isNotEmpty(formDetailId)) {
            TemplateFormDetail templateFormDetailConfig = this.getTemplateFormDetailConfigByVariableId(formDetailId);
            if (templateFormDetailConfig != null && fieldName.equals(templateFormDetailConfig.getFieldName())) {
                return false;
            }
        }
        return templateFormConfigMapper.getTemplateFormDetailCode(projectId, formId, fieldName) != null;
    }

    @Override
    public Boolean getTemplateFormTableCodeResult(String projectId, String formDetailId, String formTableId, String fieldName) {
        if (StringUtils.isNotEmpty(formTableId)) {
            TemplateFormTable templateFormTable = this.getTemplateFormTableConfigById(Long.parseLong(formTableId));
            if (templateFormTable != null && fieldName.equals(templateFormTable.getFieldName())) {
                return false;
            }
        }
        return templateFormConfigMapper.getTemplateFormTableCodeResult(projectId, formDetailId, fieldName) != null;
    }

    @Override
    public Boolean checkSystemDictionaryTypeReference(String dictionaryId) {
        return templateFormConfigMapper.getSystemDictionaryTypeReference(dictionaryId) != null;
    }

    @Override
    public Boolean checkProjectDictionaryTypeReference(String projectId, String dictionaryId) {
        return templateFormConfigMapper.getProjectDictionaryTypeReference(projectId, dictionaryId) != null;
    }

    @Override
    public Boolean checkSystemDictionaryOptionReference(String dictionaryId) {
        return projectTesteeResultService.getSystemDictionaryOptionReference(dictionaryId) != null;
    }

    @Override
    public Boolean checkProjectDictionaryOptionReference(String projectId, String dictionaryId) {
        return projectTesteeResultService.getProjectDictionaryOptionReference(projectId, dictionaryId) != null;
    }

    @Override
    public List<TemplateFormDetailVo> getTesteeCustomTitleList(String projectId, String fieldName, String showTitle) {
        if (BusinessConfig.QUERY_ALL_STATUS.equals(showTitle)) {
            showTitle = null;
        }
        return templateFormConfigMapper.getTesteeCustomTitleList(projectId, fieldName, showTitle);
    }

    @Override
    public CustomResult<Object> modifyCustomTitleStatus(String id, String showTitle) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(id));
        if (templateFormDetail != null) {
            templateFormDetail.setShowTitle("1".equals(showTitle));
        }
        templateFormDetail.setUpdateTime(new Date());
        templateFormDetail.setUpdateUser(SecurityUtils.getUserIdValue());
        templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
        return customResult;
    }

    @Override
    public CustomResult<Object> saveTemplateTesteeFormDetailConfig(TemplateFormDetailParam templateFormDetailParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        // 查询是否存在患者表单信息
        TemplateFormConfigBaseParam templateFormConfigBaseParam = new TemplateFormConfigBaseParam();
        TemplateFormConfig templateFormConfig = templateFormConfigMapper.getTemplateTesteeFormConfig(templateFormDetailParam.getProjectId());
        if (templateFormConfig == null) {
            customResult.setCode(ResultCode.PROJECT_TESTEE_BASE_FORM_NOT_FOUND.getCode());
            customResult.setMessage(ResultCode.PROJECT_TESTEE_BASE_FORM_NOT_FOUND.getMessage());
            return customResult;
        } else {
            templateFormConfigBaseParam.setFormId(templateFormConfig.getId());
        }
        templateFormDetailParam.setProjectId(templateFormDetailParam.getProjectId());
        templateFormDetailParam.setFormId(templateFormConfigBaseParam.getFormId());
        templateFormDetailParam.setCustomTestee(true);
        templateFormDetailParam.setModel(SnowflakeIdWorker.getUuidValue());
        customResult = this.saveTemplateFormDetailConfig(templateFormDetailParam);
        return customResult;
    }

    public String saveProjectTesteeBaseFormInfo(TemplateFormConfigBaseParam templateFormConfigBaseParam) {
        TemplateFormConfig templateFormConfig = new TemplateFormConfig();
        templateFormConfig.setProjectId(templateFormConfigBaseParam.getProjectId());
        templateFormConfig.setId(SnowflakeIdWorker.getUuid());
        templateFormConfig.setFormName("患者基本信息");
        templateFormConfig.setId(SnowflakeIdWorker.getUuid());
        templateFormConfig.setFormCode("testee_base_code");
        templateFormConfig.setTesteeForm(true);
        templateFormConfig.setCreateTime(new Date());
        templateFormConfig.setCreateUser(templateFormConfigBaseParam.getCreateUser());
        templateFormConfig.setTenantId(SecurityUtils.getSystemTenantId());
        templateFormConfig.setPlatformId(SecurityUtils.getSystemPlatformId());
        templateFormConfig.setStatus(BusinessConfig.VALID_STATUS);
        templateFormConfig.setSort(1);
        templateFormConfigMapper.insertSelective(templateFormConfig);
        return templateFormConfig.getId().toString();
    }

    @Override
    public TemplateFormConfigVo getTemplateTesteeFormBaseInfo(String projectId) {
        TemplateFormConfigVo templateFormConfigVo = new TemplateFormConfigVo();
        TemplateFormConfig templateFormConfig = templateFormConfigMapper.getTemplateTesteeFormConfig(Long.parseLong(projectId));
        if (templateFormConfig == null) {
            TemplateFormConfigBaseParam templateFormConfigBaseParam = new TemplateFormConfigBaseParam();
            templateFormConfigBaseParam.setProjectId(Long.parseLong(projectId));
            String formInfo = saveProjectTesteeBaseFormInfo(templateFormConfigBaseParam);
            templateFormConfigVo.setFormId(formInfo);
        } else {
            templateFormConfigVo.setFormId(templateFormConfig.getId().toString());
        }
        return templateFormConfigVo;
    }

    @Override
    public List<TemplateFormDetailVo> getTemplateTesteeFormDetailBaseInfo(String projectId, String formId, Boolean showTitle) {
        return templateFormConfigMapper.getTemplateTesteeFormDetailBaseInfo(projectId, formId, showTitle);
    }

    @Override
    public TemplateFormDetailVo getTemplateTesteeFormDetailConfig(String projectId, String formId, String fieldName) {
        return templateFormConfigMapper.getTemplateTesteeFormDetailConfig(projectId, formId, fieldName);
    }

    @Override
    public List<TemplateFormConfigVo> getTemplateFormConfigListByPlanId(String templateId, String projectId, String planId, String ePro, String formName, Boolean isPublish, Boolean excludeJoinGroupFromConfig) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(formName)) {
            params.put("formName", formName);
        }
        if (StringUtils.isNotEmpty(templateId)) {
            params.put("templateId", templateId);
        }
        if (StringUtils.isNotEmpty(projectId)) {
            params.put("projectId", projectId);
        }
        if (StringUtils.isNotEmpty(planId)) {
            params.put("planId", planId);
        }
        if (StringUtils.isNotEmpty(ePro)) {
            params.put("ePro", "1".equals(ePro));
        }
        if (isPublish != null) {
            params.put("publishStatus", isPublish);
        }
        if (excludeJoinGroupFromConfig) {
            params.put("excludeJoinGroupFromConfig", true);
        }
        List<TemplateFormConfigVo> templateFormConfigList = templateFormConfigMapper.getTemplateFormConfigListByPlanId(params);
        return templateFormConfigList;
    }

    @Override
    public CustomResult<Object> saveTemplateTableConfig(TemplateTableConfigParam templateTableConfigParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        if (templateTableConfigParam.getTemplateId() == null & templateTableConfigParam.getProjectId() == null) {
            customResult.setCode(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getCode());
            customResult.setMessage(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getMessage());
            return customResult;
        }

        TemplateTableConfigResultVo templateTableConfigResultVo = new TemplateTableConfigResultVo();
        templateTableConfigResultVo.setFormId(templateTableConfigParam.getFormId().toString());
        templateTableConfigResultVo.setFormDetailId(templateTableConfigParam.getFormDetailId().toString());
        List<TemplateTableConfigParam.TableHeadRowInfo> tableHeadRowInfoList = templateTableConfigParam.getTableRowList();

        //查询表格变量
        TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(templateTableConfigParam.getFormDetailId());
        if (templateFormDetail != null) {
            boolean customTable = templateTableConfigParam.getCustomTable() != null && templateTableConfigParam.getCustomTable();
            if (customTable) {
                templateFormDetail.setCustomTable(customTable);
                templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
            }
        }

        List<TemplateTableConfigResultVo.TableHeadVo> tableHeadVoList = new ArrayList<>();
        for (TemplateTableConfigParam.TableHeadRowInfo tableHeadRowInfo : tableHeadRowInfoList) {
            TemplateTableConfigResultVo.TableHeadVo tableHeadVo = new TemplateTableConfigResultVo.TableHeadVo();
            TemplateFormTable templateFormTable = new TemplateFormTable();
            templateFormTable.setTemplateId(templateTableConfigParam.getTemplateId());
            templateFormTable.setProjectId(templateTableConfigParam.getProjectId());
            templateFormTable.setFormId(templateTableConfigParam.getFormId());
            templateFormTable.setFormDetailId(templateTableConfigParam.getFormDetailId());
            if (templateTableConfigParam.getCreateFormTemplate()) {
                tableHeadRowInfo.setId(null);
                templateFormTable.setProjectId(null);
            }
            // 如果fieldName为空 生成字段编码
            if (StringUtils.isEmpty(tableHeadRowInfo.getFieldName())) {
                //templateFormTable.setFieldName(tableHeadRowInfo.getType().concat("-").concat(DateUtil.getCurrentdate()));
                //String tablePinyin = PinyinUtil.getPinyin(tableHeadRowInfo.getLabel(),"");
                templateFormTable.setFieldName(RandomStringUtils.randomAlphanumeric(8));
            }
            if (StringUtils.isEmpty(tableHeadRowInfo.getModel())) {
                templateFormTable.setModel(tableHeadRowInfo.getType().concat(SnowflakeIdWorker.getUuidValue()));
            }
            if (tableHeadRowInfo.getId() != null) {
                templateFormTable = templateFormTableMapper.selectByPrimaryKey(tableHeadRowInfo.getId());
                BeanUtils.copyProperties(tableHeadRowInfo, templateFormTable);
                templateFormTable.setUpdateTime(new Date());
                templateFormTable.setUpdateUser(templateTableConfigParam.getCreateUserId());
                templateFormTableMapper.updateByPrimaryKeySelective(templateFormTable);
                tableHeadVo.setFormTableId(templateFormTable.getId().toString());
            } else {
                BeanUtils.copyProperties(tableHeadRowInfo, templateFormTable);
                if (templateTableConfigParam.getCreateFormTemplate()) {
                    templateFormTable.setModel(templateFormTable.getType() + SnowflakeIdWorker.getUuidValue());
                }
                templateFormTable.setId(SnowflakeIdWorker.getUuid());
                templateFormTable.setCreateTime(new Date());
                templateFormTable.setCreateUser(templateTableConfigParam.getCreateUserId());
                templateFormTable.setStatus(BusinessConfig.VALID_STATUS);
                if (tableHeadRowInfo.getSort() != null) {
                    templateFormTable.setSort(tableHeadRowInfo.getSort());
                }
                templateFormTable.setTenantId(SecurityUtils.getSystemTenantId());
                templateFormTable.setPlatformId(SecurityUtils.getSystemPlatformId());
                templateFormTableMapper.insert(templateFormTable);
                tableHeadVo.setFormTableId(templateFormTable.getId().toString());
                tableHeadVo.setFieldName(templateFormTable.getFieldName());
            }
            tableHeadVoList.add(tableHeadVo);
        }
        customResult.setData(tableHeadVoList);
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }

    @Override
    public CustomResult<Object> saveTemplateFormConfigBaseInfo(TemplateFormConfigBaseParam templateFormConfigBaseParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        TemplateFormConfigParam templateFormConfigParam = new TemplateFormConfigParam();
        BeanUtils.copyProperties(templateFormConfigBaseParam, templateFormConfigParam);
        if (templateFormConfigBaseParam.getCreateTemplate()) {
            if (templateFormConfigBaseParam.getTemplateId() == null) {
                Template template = new Template();
                template.setId(SnowflakeIdWorker.getUuid());
                template.setName(templateFormConfigParam.getFormName());
                template.setProjectId(templateFormConfigParam.getProjectId());
                template.setIfPrivate(false);
                template.setConfigType(templateFormConfigBaseParam.getConfigType());
                if (StringUtils.isEmpty(templateFormConfigBaseParam.getConfigType())) {
                    template.setConfigType(Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02);
                }
                template.setStatus(BusinessConfig.VALID_STATUS);
                if (StringUtils.isNotEmpty(templateFormConfigBaseParam.getTemplateStatus())) {
                    template.setStatus(templateFormConfigBaseParam.getTemplateStatus());
                }
                template.setCreateUserId(templateFormConfigBaseParam.getCreateUser());
                template.setCreateTime(new Date());
                template.setTenantId(SecurityUtils.getSystemTenantId());
                template.setPlatformId(SecurityUtils.getSystemPlatformId());
                saveTemplateBaseInfo(template);
                templateFormConfigParam.setTemplateId(template.getId());
                templateFormConfigParam.setProjectId(null);
            }
        } else {
            TemplateFormConfig templateFormConfig = templateFormConfigMapper.selectByPrimaryKey(templateFormConfigParam.getFormId());
            if (templateFormConfig == null || (StringUtils.isNotEmpty(templateFormConfigBaseParam.getFormCode()) && !templateFormConfigBaseParam.getFormCode().equals(templateFormConfig.getFormCode()))) {
                String templateId = templateFormConfigBaseParam.getTemplateId() == null ? "" : templateFormConfigBaseParam.getTemplateId().toString();
                String projectId = templateFormConfigBaseParam.getProjectId() == null ? "" : templateFormConfigBaseParam.getProjectId().toString();
                Boolean queryResult = getTemplateFormCode(templateId, projectId, templateFormConfigBaseParam.getFormCode());
                if (queryResult) {
                    customResult.setMessage(ResultCode.PROJECT_FORM_CODE_FOUND.getMessage());
                    customResult.setCode(ResultCode.PROJECT_FORM_CODE_FOUND.getCode());
                    return customResult;
                }
            }
        }
        return this.saveTemplateFormConfig(templateFormConfigParam);
    }

    @Override
    public TemplateFormDetail getTemplateFormDetailConfigByVariableId(String formDetailId) {
        Object variableCache = redisTemplateService.get(RedisKeyContants.PROJECT_TEMPLATE_VARIABLE.concat(formDetailId));
        if (variableCache != null) {
            return JSON.parseObject(variableCache.toString(), TemplateFormDetail.class);
        }
        return templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(formDetailId));
    }

    @Override
    public String saveCustomFormConfigByTemplateId(String projectId, String templateId, String updateCurrentLogic, String userId) {
        //验证当前项目是否设置了访视信息
        List<ProjectVisitConfig> visitList = projectVisitConfigService.getProjectVisitListByPlanId(projectId, "", "");
        if (CollectionUtil.isNotEmpty(visitList)) {
            return BusinessConfig.RETURN_MESSAGE_PROJECT_VISIT_FAIL_02;
        }
        Long preVisitId = 0L;
        int preSortValue = 0;
        // 根据templateId查询所有访视信息
        List<ProjectVisitConfig> projectVisitList = projectVisitConfigService.getProjectVisitListByTemplateId(templateId);
        for (ProjectVisitConfig projectVisitConfig : projectVisitList) {
            //新增访视
            ProjectVisitConfigParam projectVisitConfigParam = new ProjectVisitConfigParam();
            BeanUtils.copyProperties(projectVisitConfig, projectVisitConfigParam);
            projectVisitConfigParam.setId(null);
            projectVisitConfigParam.setProjectId(Long.parseLong(projectId));
            projectVisitConfigParam.setTemplateId(null);
            projectVisitConfigParam.setPreVisitId(preVisitId);
            projectVisitConfigParam.setCreateUserId(userId);
            projectVisitConfigParam.setCopyTemplate(true);
            //projectVisitConfigParam.setPreSortValue(preSortValue);
            CustomResult<Object> customResult = projectVisitConfigService.saveProjectVisitConfig(projectVisitConfigParam);
            Map<String, Object> data = (Map<String, Object>) customResult.getData();
            if (!data.isEmpty()) {
                preVisitId = Long.parseLong(data.get("id").toString());
                preSortValue = (int) data.get("sortValue");
            }

            List<TemplateFormConfigVo> templateFormList = getFormConfigListByTemplateIdAndVisitId(templateId, projectVisitConfig.getId().toString());
            for (TemplateFormConfigVo templateFormConfigVo : templateFormList) {
                saveTemplateConfigFromCustomTemplate(null, projectId, null, null, templateFormConfigVo.getFormId(), "", "", "", "", true, "", updateCurrentLogic, userId);
            }
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String saveTemplateConfigFromCustomTemplate(String templateId, String projectId, String groupId, String configType,
                                                       String templateFormId, String targetFormId, String replaceFormName, String formCode, String formType,
                                                       boolean appendForm, String formDetailId, String updateCurrentLogic, String operator) {

        TemplateFormConfig templateFormConfig = this.getTemplateFormConfigById(Long.parseLong(templateFormId));
        String copyTemplateId = templateFormConfig.getTemplateId() == null ? "" : templateFormConfig.getTemplateId().toString();
        Long queryTemplateId = StringUtils.isEmpty(templateId) ? null : Long.parseLong(templateId);
        Long queryProjectId = StringUtils.isEmpty(projectId) ? null : Long.parseLong(projectId);
        if (StringUtils.isEmpty(targetFormId)) {
            if (appendForm) {
                templateFormConfig.setId(SnowflakeIdWorker.getUuid());
                if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_01.equals(configType)) {
                    templateFormConfig.setTemplateId(Long.parseLong(templateId));
                } else {
                    templateFormConfig.setProjectId(Long.parseLong(projectId));
                    templateFormConfig.setTemplateId(null);
                }
                templateFormConfig.setFormName(templateFormConfig.getFormName());
                if (StringUtils.isNotEmpty(replaceFormName)) {
                    templateFormConfig.setFormName(replaceFormName);
                }
                templateFormConfig.setFormConfig(templateFormConfig.getFormConfig());
                templateFormConfig.setFormCode(templateFormConfig.getFormCode());
                if (StringUtils.isNotEmpty(formCode)) {
                    templateFormConfig.setFormCode(formCode);
                }
                if(Constants.PROJECT_FORM_JOIN_GROUP_NAME.equals(templateFormConfig.getFormCode())){
                    templateFormConfig.setJoinGroup(true);
                }
                templateFormConfig.setFormType(templateFormConfig.getFormType());
                if (StringUtils.isNotEmpty(formType)) {
                    templateFormConfig.setFormType(formType);
                }
                templateFormConfig.setTesteeForm(false);
                templateFormConfig.setGroupName(groupId);
                templateFormConfig.setCreateTime(new Date());
                templateFormConfig.setUpdateTime(null);
                templateFormConfig.setUpdateUser(null);
                templateFormConfig.setCreateUser(operator);
                templateFormConfig.setStatus(BusinessConfig.VALID_STATUS);
                Integer formSort = getTemplateFormSortValue(queryTemplateId, queryProjectId);
                templateFormConfig.setSort(formSort);
                templateFormConfigMapper.insertSelective(templateFormConfig);
            }
        } else {
            templateFormConfig = this.getTemplateFormConfigById(Long.parseLong(targetFormId));
        }
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        criteria.andFormIdEqualTo(Long.parseLong(templateFormId));
        if (StringUtils.isNotEmpty(formDetailId)) {
            criteria.andIdEqualTo(Long.parseLong(formDetailId));
        }
        criteria.andGroupIdIsNull();
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        for (TemplateFormDetail templateFormDetailVo : templateFormDetailList) {
            // copy form variables
            TemplateFormDetail templateFormVariableDetail = new TemplateFormDetail();
            BeanUtils.copyProperties(templateFormDetailVo, templateFormVariableDetail);
            templateFormVariableDetail.setId(SnowflakeIdWorker.getUuid());
            templateFormVariableDetail.setCopyVariableId(templateFormDetailVo.getId());
            if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_01.equals(configType)) {
                templateFormVariableDetail.setTemplateId(Long.parseLong(templateId));
            } else {
                templateFormVariableDetail.setTemplateId(null);
            }
            templateFormVariableDetail.setProjectId(Long.parseLong(projectId));
            templateFormVariableDetail.setFormId(templateFormConfig.getId());
            templateFormVariableDetail.setModel(templateFormVariableDetail.getType() + SnowflakeIdWorker.getUuid());
            // 引用模版需要重新生成字段code
            // String pinyin = PinyinUtil.getPinyin(templateFormVariableDetail.getLabel(),"");
            templateFormVariableDetail.setFieldName(RandomStringUtils.randomAlphanumeric(8));
            //templateFormVariableDetail.setFieldName(templateFormVariableDetail.getFieldName()+ DateUtil.getCurrenttime());
            templateFormVariableDetail.setCreateTime(new Date());
            templateFormVariableDetail.setCreateUser(operator);
            templateFormVariableDetail.setUpdateTime(null);
            templateFormVariableDetail.setUpdateUser(null);
            templateFormVariableDetail.setStatus(BusinessConfig.VALID_STATUS);
            templateFormVariableDetail.setSort(templateFormDetailVo.getSort());
            // copy dictionary records
            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT, BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
            if(variableTypeList.contains(templateFormVariableDetail.getType())) {
                List<String> projectDictionaryTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM, BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM);
                if(projectDictionaryTypeList.contains(templateFormVariableDetail.getDicResource())){
                    if(StringUtils.isEmpty(templateFormVariableDetail.getRefDicId())){
                        log.error("templateFormVariableDetail#refId() is null, form variable id is: {}, the form variable label is: {}  ", templateFormVariableDetail.getId(), templateFormVariableDetail.getLabel());
                    }else{
                        ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(templateFormVariableDetail.getRefDicId());
                        if(dictionaryInfo != null){
                            ProjectDictionary targetDictionaryInfo = projectDictionaryService.getProjectDictionaryByParentIdAndCode(projectId, BusinessConfig.PARENT_LEVEL_ROOT_NODE, dictionaryInfo.getCode());
                            if(targetDictionaryInfo != null){
                                templateFormVariableDetail.setRefDicId(targetDictionaryInfo.getId());
                            }else {
                                log.error("targetDictionaryInfo form variable id is: {}, the form variable label is: {}  ", templateFormVariableDetail.getId(), templateFormVariableDetail.getLabel());
                            }
                        }else{
                            log.error("dictionaryInfo form variable id is: {}, the form variable label is: {}  ", templateFormVariableDetail.getId(), templateFormVariableDetail.getLabel());
                        }
                    }
                }
            }

            templateFormDetailMapper.insertSelective(templateFormVariableDetail);
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormVariableDetail.getType())) {
                Long targetGroupId = templateFormVariableDetail.getId();
                List<TemplateFormDetailVo> templateFormDetailListForGroup = this.getTemplateFormDetailByGroupId(templateFormId, templateFormDetailVo.getId().toString(), "", false);
                for (TemplateFormDetailVo templateGroupFormDetailVo : templateFormDetailListForGroup) {
                    TemplateFormDetail templateGroupFormDetail = new TemplateFormDetail();
                    TemplateFormDetail templateGroupCopyFormDetail = new TemplateFormDetail();
                    BeanUtils.copyProperties(templateGroupFormDetailVo, templateGroupFormDetail);
                    BeanUtils.copyProperties(templateGroupFormDetailVo, templateGroupCopyFormDetail);
                    templateGroupFormDetail.setId(SnowflakeIdWorker.getUuid());
                    if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_01.equals(configType)) {
                        templateGroupFormDetail.setTemplateId(Long.parseLong(templateId));
                    } else {
                        templateGroupFormDetail.setTemplateId(null);
                    }
                    templateGroupFormDetail.setProjectId(Long.parseLong(projectId));
                    templateGroupFormDetail.setFormId(templateFormConfig.getId());
                    templateGroupFormDetail.setCopyVariableId(templateGroupFormDetailVo.getId());
                    templateGroupFormDetail.setGroupId(targetGroupId);
                    templateGroupFormDetail.setModel(templateGroupFormDetail.getType() + SnowflakeIdWorker.getUuid());
                    // String groupPinyin = PinyinUtil.getPinyin(templateGroupFormDetail.getLabel(),"");
                    templateGroupFormDetail.setFieldName(RandomStringUtils.randomAlphanumeric(8));
                    //templateGroupFormDetail.setFieldName(templateGroupFormDetail.getFieldName()+ DateUtil.getCurrenttime());
                    templateGroupFormDetail.setCreateTime(new Date());
                    templateGroupFormDetail.setCreateUser(operator);
                    templateGroupFormDetail.setStatus(BusinessConfig.VALID_STATUS);
                    templateGroupFormDetail.setTenantId(SecurityUtils.getSystemTenantId());
                    templateGroupFormDetail.setPlatformId(SecurityUtils.getSystemPlatformId());
                    templateGroupFormDetail.setSort(templateGroupFormDetailVo.getSort());
                    templateFormDetailMapper.insertSelective(templateGroupFormDetail);
                    if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateGroupFormDetailVo.getType())) {
                        templateFormTableBuildWrapper("", projectId, "", false, operator, templateFormConfig.getId().toString(), templateGroupCopyFormDetail.getId().toString(), templateGroupFormDetail.getId().toString());
                    }
                }
            }
            // 使用copyTemplateId保存变量逻辑条件
            if (StringUtils.isNotEmpty(copyTemplateId)) {
                List<TemplateFormLogicVo> templateFormLogicVoList = templateFormLogicService.getFormLogicConfigListByTemplateId(copyTemplateId, templateFormId, templateFormDetailVo.getId().toString());
                for (TemplateFormLogicVo formLogicVo : templateFormLogicVoList) {
                    TemplateFormLogic templateFormLogic = new TemplateFormLogic();
                    templateFormLogic.setFormId(templateFormConfig.getId());
                    templateFormLogic.setFormDetailId(templateFormVariableDetail.getId());
                    templateFormLogic.setTemplateId(null);
                    templateFormLogic.setProjectId(Long.parseLong(projectId));
                    templateFormLogic.setLabel(templateFormVariableDetail.getLabel());
                    templateFormLogic.setExpressionId(formLogicVo.getExpressionId());
                    templateFormLogic.setConditionList(formLogicVo.getConditionList());
                    templateFormLogic.setOptionList(formLogicVo.getOptionList());
                    templateFormLogic.setExtands(formLogicVo.getExtands());
                    templateFormLogic.setCreateTime(new Date());
                    templateFormLogic.setCreateUser(formLogicVo.getCreateUser());
                    templateFormLogic.setStatus(BusinessConfig.VALID_STATUS);
                    TemplateFormLogicParam templateFormLogicParam = new TemplateFormLogicParam();
                    BeanUtils.copyProperties(templateFormLogic, templateFormLogicParam);
                    List<TemplateFormLogicParam.TemplateFormLogicDataParam> dataList = templateFormLogicParam.getDataList();
                    List<TemplateFormLogicVo.TemplateFormLogicDataVo> templateFormLogicDataVoList = formLogicVo.getDataList();
                    for (TemplateFormLogicVo.TemplateFormLogicDataVo templateFormLogicDataVo : templateFormLogicDataVoList) {
                        TemplateFormLogicParam.TemplateFormLogicDataParam templateFormLogicData = new TemplateFormLogicParam.TemplateFormLogicDataParam();
                        BeanUtils.copyProperties(templateFormLogicDataVo, templateFormLogicData);
                        templateFormLogicData.setTemplateId(null);
                        templateFormLogicData.setUpdateTime(null);
                        templateFormLogicData.setUpdateUserId(null);
                        templateFormLogicData.setCreateTime(new Date());
                        templateFormLogicData.setCreateUserId(formLogicVo.getCreateUser());
                        templateFormLogicData.setStatus(BusinessConfig.VALID_STATUS);
                        if ("1".equals(updateCurrentLogic)) {
                            Long targetDetailId = templateFormLogicDataVo.getTargetDetailId();
                            TemplateFormDetail formDetail = templateFormDetailMapper.selectByPrimaryKey(targetDetailId);
                            TemplateFormDetailVo templateFormDetailResult = getTemplateFormVariableIdByName(projectId, "", templateFormConfig.getId().toString(), formDetail.getLabel());
                            templateFormLogicData.setTargetFormId(templateFormDetailResult.getFormId());
                            templateFormLogicData.setTargetDetailId(templateFormDetailResult.getId());
                        }
                        dataList.add(templateFormLogicData);
                    }
                    templateFormLogicParam.setDataList(dataList);
                    templateFormLogicService.saveFormVariableLogicData(templateFormLogicParam);
                }
            }
            // table parse
            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormVariableDetail.getType())) {
                templateFormTableBuildWrapper(templateId, projectId, "", false, operator, templateFormConfig.getId().toString(), templateFormDetailVo.getId().toString(), templateFormVariableDetail.getId().toString());
            }
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String initTemplateConfigFromTenantTemplate(String projectId, String copyTemplateId, String copyFormId, String operator) {
        Long copyFormIdValue = Long.parseLong(copyFormId);
        long copyTemplateIdValue = Long.parseLong(copyTemplateId);
        TemplateFormConfig templateFormConfig = this.getTemplateFormConfigById(copyFormIdValue);
        Template template = new Template();
        template.setId(SnowflakeIdWorker.getUuid());
        template.setName(templateFormConfig.getFormName());
        template.setIfPrivate(false);
        template.setConfigType(Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02);
        template.setStatus(BusinessConfig.VALID_STATUS);
        template.setProjectId(NumberUtil.parseLong(projectId));
        template.setCopyTemplateId(copyTemplateId);
        //template.setDescription(templateFormConfig.getDescription());
        template.setCreateUserId(operator);
        template.setCreateTime(new Date());
        templateMapper.insert(template);

        templateFormConfig.setId(SnowflakeIdWorker.getUuid());
        templateFormConfig.setProjectId(null);
        templateFormConfig.setTemplateId(template.getId());
        templateFormConfig.setFormType(templateFormConfig.getFormType());
        templateFormConfig.setCreateTime(new Date());
        templateFormConfig.setCreateUser(operator);
        templateFormConfig.setUpdateTime(null);
        templateFormConfig.setUpdateUser(null);
        templateFormConfig.setStatus(BusinessConfig.VALID_STATUS);
        Integer templateFormSortValue = getTemplateFormSortValue(templateFormConfig.getId(), copyTemplateIdValue);
        templateFormConfig.setSort(templateFormSortValue);
        templateFormConfigMapper.insertSelective(templateFormConfig);
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        criteria.andFormIdEqualTo(copyFormIdValue);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        example.setOrderByClause("sort asc, create_time asc");
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        for (TemplateFormDetail templateFormDetailVo : templateFormDetailList) {
            // copy form variables
            TemplateFormDetail templateFormVariableDetail = new TemplateFormDetail();
            BeanUtils.copyProperties(templateFormDetailVo, templateFormVariableDetail);
            templateFormVariableDetail.setId(SnowflakeIdWorker.getUuid());
            templateFormVariableDetail.setCopyVariableId(templateFormDetailVo.getId());
            templateFormVariableDetail.setTemplateId(template.getId());
            templateFormVariableDetail.setProjectId(null);
            templateFormVariableDetail.setFormId(templateFormConfig.getId());
            templateFormVariableDetail.setFieldName(RandomStringUtils.randomAlphanumeric(8));
            templateFormVariableDetail.setModel(RandomStringUtils.randomAlphanumeric(8));
            templateFormVariableDetail.setCreateTime(new Date());
            templateFormVariableDetail.setCreateUser(operator);
            templateFormVariableDetail.setStatus(BusinessConfig.VALID_STATUS);
            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormVariableDetail.getType())) {
                templateFormVariableDetail.setExpand(null);
            }
            //Integer detailSortValue = getTemplateFormDetailSortValue(templateFormConfig.getId());
            //templateFormVariableDetail.setSort(detailSortValue);
            templateFormVariableDetail.setSort(templateFormVariableDetail.getSort());
            //字典信息处理
            List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT, BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX, BusinessConfig.PROJECT_VISIT_CRF_FORM_NUMBER);
            if(variableTypeList.contains(templateFormVariableDetail.getType())) {
                List<String> projectDictionaryTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM, BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM);
                if(projectDictionaryTypeList.contains(templateFormDetailVo.getDicResource())){
                    templateFormVariableDetail.setDefaultDicValue(null);
                    ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(templateFormDetailVo.getRefDicId());
                    if(dictionaryInfo != null){
                        ProjectDictionary targetDictionaryInfo = projectDictionaryService.getProjectDictionaryByParentIdAndCode(projectId, BusinessConfig.PARENT_LEVEL_ROOT_NODE, dictionaryInfo.getCode());
                        if(targetDictionaryInfo != null){
                            templateFormVariableDetail.setRefDicId(targetDictionaryInfo.getId());
                            if(StringUtils.isNotEmpty(templateFormDetailVo.getDefaultDicValue())){
                                ProjectDictionary dictionaryInfo1 = projectDictionaryService.getDictionaryInfo(templateFormDetailVo.getDefaultDicValue());
                                if(dictionaryInfo1 != null){
                                    ProjectDictionary targetDictionaryOptionInfo = projectDictionaryService.getDictByName(projectId, dictionaryInfo1.getName(), targetDictionaryInfo.getId());
                                    if(targetDictionaryOptionInfo != null){
                                        templateFormVariableDetail.setDefaultDicValue(targetDictionaryOptionInfo.getId());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            templateFormDetailMapper.insertSelective(templateFormVariableDetail);

            // table parse
            if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormVariableDetail.getType())) {
                templateFormTableBuildWrapper(template.getId().toString(), projectId, "", true, operator, templateFormConfig.getId().toString(), templateFormDetailVo.getId().toString(), templateFormVariableDetail.getId().toString());
            }
            // init fieldGroup record
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormVariableDetail.getType())) {
                Long targetGroupId;
                TemplateFormDetail templateFormDetail = getTemplateFormCopyVariableByGroupId(templateFormVariableDetail.getId());
                if (templateFormDetail == null) {
                    TemplateFormDetail templateGroupVariable = new TemplateFormDetail();
                    BeanUtils.copyProperties(templateFormVariableDetail, templateGroupVariable);
                    templateGroupVariable.setTemplateId(template.getId());
                    templateGroupVariable.setProjectId(null);
                    templateGroupVariable.setId(SnowflakeIdWorker.getUuid());
                    templateGroupVariable.setCreateTime(new Date());
                    templateGroupVariable.setCreateUser(operator);
                    templateGroupVariable.setStatus(BusinessConfig.VALID_STATUS);
                    templateGroupVariable.setModel(templateGroupVariable.getType() + SnowflakeIdWorker.getUuidValue());
                    templateGroupVariable.setFieldName(RandomStringUtils.randomAlphanumeric(8));
                    //Integer tamplateGroupVarSortValue = getTemplateFormDetailSortValue(templateFormConfig.getId());
                    templateGroupVariable.setModel(templateGroupVariable.getType() + SnowflakeIdWorker.getUuid());
                    templateGroupVariable.setSort(templateGroupVariable.getSort());

                    templateGroupVariable.setTenantId(SecurityUtils.getSystemTenantId());
                    templateGroupVariable.setPlatformId(SecurityUtils.getSystemPlatformId());
                    templateFormDetailMapper.insertSelective(templateGroupVariable);
                    targetGroupId = templateGroupVariable.getId();
                } else {
                    targetGroupId = templateFormDetail.getId();
                }
                List<TemplateFormDetailVo> templateFormDetailListForGroup = this.getTemplateFormDetailByGroupId(templateFormVariableDetail.getFormId().toString(), templateFormVariableDetail.getId().toString(), "", false);
                for (TemplateFormDetailVo templateGroupFormDetailVo : templateFormDetailListForGroup) {
                    TemplateFormDetail templateGroupFormDetail = new TemplateFormDetail();
                    BeanUtils.copyProperties(templateGroupFormDetailVo, templateGroupFormDetail);
                    templateGroupFormDetail.setId(SnowflakeIdWorker.getUuid());
                    templateGroupFormDetail.setTemplateId(template.getId());
                    templateGroupFormDetail.setProjectId(null);
                    templateGroupFormDetail.setFormId(templateFormConfig.getId());
                    templateGroupFormDetail.setCopyVariableId(templateGroupFormDetailVo.getId());
                    templateGroupFormDetail.setGroupId(targetGroupId);
                    templateGroupFormDetail.setModel(templateGroupFormDetailVo.getType() + SnowflakeIdWorker.getUuid());
                    templateGroupFormDetail.setFieldName(RandomStringUtils.randomAlphanumeric(8));
                    templateGroupFormDetail.setCreateTime(new Date());
                    templateGroupFormDetail.setCreateUser(operator);
                    templateGroupFormDetail.setStatus(BusinessConfig.VALID_STATUS);
                    templateGroupFormDetail.setTenantId(SecurityUtils.getSystemTenantId());
                    templateGroupFormDetail.setPlatformId(SecurityUtils.getSystemPlatformId());
                    //Integer tamplateGroupVarSortValue = getTemplateFormDetailSortValue(templateFormConfig.getId());
                    templateGroupFormDetail.setSort(templateGroupFormDetailVo.getSort());
                    templateFormDetailMapper.insertSelective(templateGroupFormDetail);
                    if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormVariableDetail.getType())) {
                        templateFormTableBuildWrapper(template.getId().toString(), projectId, "", true, operator, templateFormConfig.getId().toString(), templateFormDetailVo.getId().toString(), templateFormVariableDetail.getId().toString());
                    }
                }
            }
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String initTemplateConfigFromReferenceTemplate(String projectId, String referenceProjectId, boolean copyPlanAndFlow, String operator) {
        if(copyPlanAndFlow){
            FlowPlan resourceFlowPlan = flowPlanService.getPlanByProjectId(referenceProjectId);
            if(resourceFlowPlan == null){return "该项目未配置流程方案，请选择其他项目进行引用或者表单复制。";}
            // 查询方案
            String targetFlowPlanId = "";
            FlowPlan flowPlan = flowPlanService.getPlanByProjectId(projectId);
            if(flowPlan == null){
                FlowPlan flowPlanParam = new FlowPlan();
                BeanUtils.copyProperties(resourceFlowPlan, flowPlanParam);
                flowPlanParam.setProjectId(NumberUtil.parseLong(projectId));
                flowPlanParam.setPublishDate(null);
                flowPlanParam.setPublishStatus(BusinessConfig.NOT_PUBLISH_STATUS);
                flowPlanParam.setVersion(null);
                flowPlanParam.setCreateUser(operator);
                flowPlanParam.setCreateTime(new Date());
                flowPlanParam.setUpdateTime(null);
                flowPlanParam.setUpdateUser(null);
                flowPlanParam.setStatus(BusinessConfig.ENABLED_STATUS);
                flowPlanParam.setTenantId(SecurityUtils.getSystemTenantId());
                flowPlanParam.setPlatformId(SecurityUtils.getSystemPlatformId());
                flowPlanService.create(flowPlanParam);
                targetFlowPlanId = flowPlanParam.getId().toString();
            }else{
                targetFlowPlanId = flowPlan.getId().toString();
            }

            List<FlowPlan> projectFlowPlanList = flowPlanService.getProjectFlowPlanList(NumberUtil.parseLong(referenceProjectId));
            for (FlowPlan projectFlowPlan : projectFlowPlanList) {
                if(projectFlowPlan.getPublishStatus().equals(BusinessConfig.PUBLISH_STATUS)){continue;}
                FlowPlan flowPlanParam = getFlowPlan(projectId, operator, projectFlowPlan);
                flowPlanService.create(flowPlanParam);
            }

            // 查询流程
            List<ProjectVisitConfig> projectVisitConfigList = projectVisitConfigService.list(resourceFlowPlan.getId());
            for (ProjectVisitConfig projectVisitConfig : projectVisitConfigList) {
                FlowParam projectVisitConfigParam = new FlowParam();
                BeanUtils.copyProperties(projectVisitConfig, projectVisitConfigParam);
                projectVisitConfigParam.setProjectId(NumberUtil.parseLong(projectId));
                projectVisitConfigParam.setPlanId(NumberUtil.parseLong(targetFlowPlanId));
                projectVisitConfigService.create(projectVisitConfigParam);
                // 查询流程表单
                List<FlowPlanFormVo> FlowPlanFormList = flowPlanFormService.getFormConfigListByPlanIdAndVisitId(referenceProjectId, resourceFlowPlan.getId().toString(), projectVisitConfigParam.getId().toString(), "");
                for (FlowPlanFormVo flowPlanFormVo : FlowPlanFormList) {
                    flowPlanFormService.insertFlowPlanFormInfo(projectId, flowPlanFormVo.getPlanId(), flowPlanFormVo.getFormId(), false, operator);
                }
            }
        }
        // 查询方案流程表单列表
        List<TemplateFormConfigVo> templateFormConfigList = templateFormConfigMapper.getTemplateFormConfigListByProjectId(null, referenceProjectId, "", null);
        for (TemplateFormConfigVo templateFormConfigVo : templateFormConfigList) {
            TemplateFormConfig templateFormConfig = new TemplateFormConfig();
            BeanUtils.copyProperties(templateFormConfigVo, templateFormConfig);
            templateFormConfig.setId(SnowflakeIdWorker.getUuid());
            templateFormConfig.setProjectId(NumberUtil.parseLong(projectId));
            templateFormConfig.setTesteeForm(false);
            templateFormConfig.setJoinGroup(false);
            templateFormConfig.setCreateUser(operator);
            templateFormConfig.setCreateTime(new Date());
            templateFormConfig.setUpdateTime(null);
            templateFormConfig.setUpdateUser(null);
            templateFormConfig.setStatus(BusinessConfig.VALID_STATUS);
            templateFormConfig.setTenantId(SecurityUtils.getSystemTenantId());
            templateFormConfig.setPlatformId(SecurityUtils.getSystemPlatformId());
            templateFormConfigMapper.insertSelective(templateFormConfig);
            List<TemplateFormDetailVo> templateFormDetailList = getTemplateFormDetailConfigListByFormId(null, templateFormConfigVo.getFormId(), "", "1", "1", "0");
            for (TemplateFormDetailVo templateFormDetailVo : templateFormDetailList) {
                TemplateFormDetail templateFormDetail = templateFormVariableBuildWrapper(projectId, templateFormConfig.getId().toString(), templateFormDetailVo, operator);
                if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailVo.getType())) {
                    templateFormDetail.setExpand(null);
                }
                List<String> variableSelectTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_SELECT, BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX);
                if(variableSelectTypeList.contains(templateFormDetailVo.getType())) {
                    getTemplateFormVariableDictionaryId(referenceProjectId, templateFormDetailVo.getDicResource(), templateFormDetailVo.getRefDicId(), templateFormDetailVo.getDefaultDicValue(), templateFormDetail);
                }
                List<String> variableTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE, BusinessConfig.PROJECT_VISIT_CRF_FORM_GROUP, BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP);
                if(!variableTypeList.contains(templateFormDetailVo.getType())){
                    if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetail.getType())) {
                        templateFormDetail.setExpand(null);
                    }
                    templateFormDetailMapper.insertSelective(templateFormDetail);
                }
                if(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetailVo.getType())){
                    TemplateFormDetail templateFormTableDetail = templateFormVariableBuildWrapper(projectId, templateFormConfig.getId().toString(), templateFormDetailVo, operator);
                    getTemplateFormVariableDictionaryId(referenceProjectId, templateFormDetailVo.getDicResource(), templateFormDetailVo.getRefDicId(), templateFormDetailVo.getDefaultDicValue(), templateFormTableDetail);
                    templateFormDetailMapper.insertSelective(templateFormTableDetail);
                    templateFormTableBuildWrapper(null, projectId, "", false, operator, templateFormConfig.getId().toString(), templateFormDetailVo.getId().toString(), templateFormTableDetail.getId().toString());
                }
                if(BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailVo.getType())){
                    String formId = templateFormDetailVo.getFormId().toString();
                    String groupId = templateFormDetailVo.getId().toString();
                    TemplateFormDetail templateFormGroupDetailParam = templateFormVariableBuildWrapper(projectId, templateFormConfig.getId().toString(), templateFormDetailVo, operator);
                    templateFormDetailMapper.insertSelective(templateFormGroupDetailParam);
                    List<TemplateFormDetailVo> templateFormDetailInfoList = this.getTemplateFormDetailByGroupId(formId, groupId, "", false);
                    for (TemplateFormDetailVo templateFormGroupDetail : templateFormDetailInfoList) {
                        TemplateFormDetail templateFormGroupDetailObject = templateFormVariableBuildWrapper(projectId, templateFormConfig.getId().toString(), templateFormGroupDetail, operator);
                        if(variableSelectTypeList.contains(templateFormGroupDetailObject.getType())) {
                            if(StringUtils.isNotEmpty(templateFormGroupDetailObject.getRefDicId())){
                                getTemplateFormVariableDictionaryId(projectId, templateFormGroupDetail.getDicResource(), templateFormGroupDetail.getRefDicId(), templateFormGroupDetail.getDefaultDicValue(), templateFormGroupDetailObject);
                            }
                        }
                        templateFormGroupDetailObject.setProjectId(NumberUtil.parseLong(projectId));
                        templateFormGroupDetailObject.setFormId(templateFormConfig.getId());
                        templateFormGroupDetailObject.setCopyVariableId(templateFormGroupDetail.getId());
                        templateFormGroupDetailObject.setGroupId(templateFormGroupDetailParam.getId());
                        templateFormDetailMapper.insertSelective(templateFormGroupDetailObject);
                        if (templateFormGroupDetail.getType().equals(BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE)) {
                            // 表格
                            templateFormTableBuildWrapper(null, projectId, referenceProjectId, false, operator, templateFormGroupDetail.getFormId().toString(), templateFormGroupDetail.getId().toString(), templateFormGroupDetailObject.getId().toString());
                        }
                    }
                }
            }
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @NotNull
    private FlowPlan getFlowPlan(String projectId, String operator, FlowPlan resourceFlowPlan) {
        FlowPlan flowPlanParam = new FlowPlan();
        BeanUtils.copyProperties(resourceFlowPlan, flowPlanParam);
        flowPlanParam.setProjectId(NumberUtil.parseLong(projectId));
        flowPlanParam.setPublishDate(null);
        flowPlanParam.setPublishStatus(BusinessConfig.NOT_PUBLISH_STATUS);
        flowPlanParam.setVersion(null);
        flowPlanParam.setCreateUser(operator);
        flowPlanParam.setCreateTime(new Date());
        flowPlanParam.setUpdateTime(null);
        flowPlanParam.setUpdateUser(null);
        flowPlanParam.setStatus(BusinessConfig.ENABLED_STATUS);
        flowPlanParam.setTenantId(SecurityUtils.getSystemTenantId());
        flowPlanParam.setPlatformId(SecurityUtils.getSystemPlatformId());
        return flowPlanParam;
    }

    private TemplateFormDetail getTemplateFormCopyVariableByGroupId(Long id) {
        return templateFormDetailMapper.getTemplateFormCopyVariableByGroupId(id);
    }

    public void getTemplateFormVariableDictionaryId(String projectId, String dicResource, String refDicId, String defaultDicValue,TemplateFormDetail templateFormDetail) {
        List<String> projectDictionaryTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM, BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM);
        if(projectDictionaryTypeList.contains(dicResource)){
            templateFormDetail.setDefaultDicValue(null);
            ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(refDicId);
            if(dictionaryInfo != null){
                ProjectDictionary targetDictionaryInfo = projectDictionaryService.getProjectDictionaryByParentIdAndCode(projectId, BusinessConfig.PARENT_LEVEL_ROOT_NODE, dictionaryInfo.getCode());
                if(targetDictionaryInfo != null){
                    templateFormDetail.setRefDicId(targetDictionaryInfo.getId());
                    if(StringUtils.isNotEmpty(defaultDicValue)){
                        ProjectDictionary dictionaryInfo1 = projectDictionaryService.getDictionaryInfo(defaultDicValue);
                        if(dictionaryInfo1 != null){
                            ProjectDictionary targetDictionaryOptionInfo = projectDictionaryService.getDictByName(projectId, dictionaryInfo1.getName(), targetDictionaryInfo.getId());
                            if(targetDictionaryOptionInfo != null){
                                templateFormDetail.setDefaultDicValue(targetDictionaryOptionInfo.getId());
                            }
                        }
                    }
                }
            }
        }
    }

    public TemplateFormDetail templateFormVariableBuildWrapper(String projectId, String templateFormId, TemplateFormDetailVo templateFormDetailVo, String operator){
        TemplateFormDetail templateFormDetail = new TemplateFormDetail();
        BeanUtils.copyProperties(templateFormDetailVo, templateFormDetail);
        templateFormDetail.setId(SnowflakeIdWorker.getUuid());
        templateFormDetail.setTemplateId(null);
        templateFormDetail.setProjectId(NumberUtil.parseLong(projectId));
        templateFormDetail.setFormId(NumberUtil.parseLong(templateFormId));
        templateFormDetail.setCopyVariableId(templateFormDetailVo.getId());
        templateFormDetail.setModel(SnowflakeIdWorker.getUuidValue());
        templateFormDetail.setEnableViewConfig(false);
        templateFormDetail.setLabConfigScope(null);
        templateFormDetail.setCreateTime(new Date());
        templateFormDetail.setCreateUser(operator);
        templateFormDetail.setUpdateTime(null);
        templateFormDetail.setUpdateUser(null);
        templateFormDetail.setStatus(BusinessConfig.VALID_STATUS);
        templateFormDetail.setTenantId(SecurityUtils.getSystemTenantId());
        templateFormDetail.setPlatformId(SecurityUtils.getSystemPlatformId());
        return templateFormDetail;
    }

    public void templateFormTableBuildWrapper(String templateId, String projectId, String referenceProjectId, Boolean createProjectTemplate, String operator,
                                              String templateFormId, String resourceFormDetailId, String templateFormDetailId) {
        TemplateFormTableExample templateFormTableExample = new TemplateFormTableExample();
        TemplateFormTableExample.Criteria criteria1 = templateFormTableExample.createCriteria();
        criteria1.andFormDetailIdEqualTo(NumberUtil.parseLong(resourceFormDetailId));
        criteria1.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<TemplateFormTable> templateFormTableList = templateFormTableMapper.selectByExample(templateFormTableExample);
        for (TemplateFormTable templateFormTableVo : templateFormTableList) {
            TemplateFormTable templateFormTable = new TemplateFormTable();
            BeanUtils.copyProperties(templateFormTableVo, templateFormTable);
            templateFormTable.setId(SnowflakeIdWorker.getUuid());
            templateFormTable.setTemplateId(null);
            templateFormTable.setProjectId(Long.parseLong(projectId));
            if (createProjectTemplate) {
                templateFormTable.setTemplateId(Long.parseLong(templateId));
                templateFormTable.setProjectId(null);
            }
            templateFormTable.setFormId(NumberUtil.parseLong(templateFormId));
            templateFormTable.setFormDetailId(NumberUtil.parseLong(templateFormDetailId));
            templateFormTable.setCopyVariableId(templateFormTableVo.getFormDetailId());
            templateFormTable.setCopyTableId(templateFormTableVo.getId());
            templateFormTable.setFieldName(RandomStringUtils.randomAlphanumeric(8));
            templateFormTable.setModel(RandomStringUtils.randomAlphabetic(8));
            templateFormTable.setExpand(templateFormTableVo.getExpand());
            List<String> projectDictionaryTypeList = Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_PRO_FORM, BusinessConfig.PROJECT_VISIT_CRF_NUMBER_FORM);
            if(projectDictionaryTypeList.contains(templateFormTableVo.getDicResource())){
                templateFormTable.setDefaultDicValue(null);
                ProjectDictionary dictionaryInfo = projectDictionaryService.getDictionaryInfo(templateFormTableVo.getRefDicId());
                if(dictionaryInfo != null){
                    ProjectDictionary targetDictionaryInfo;
                    if(StringUtils.isNotBlank(referenceProjectId)){
                        targetDictionaryInfo = projectDictionaryService.getProjectDictionaryByParentIdAndCode(referenceProjectId, BusinessConfig.PARENT_LEVEL_ROOT_NODE, dictionaryInfo.getCode());
                        if(targetDictionaryInfo != null){
                            templateFormTable.setRefDicId(targetDictionaryInfo.getId());
                        }
                    }else{
                        targetDictionaryInfo = projectDictionaryService.getProjectDictionaryByParentIdAndCode(projectId, BusinessConfig.PARENT_LEVEL_ROOT_NODE, dictionaryInfo.getCode());
                        if(targetDictionaryInfo != null){
                            templateFormTable.setRefDicId(targetDictionaryInfo.getId());
                        }
                    }
                    if(StringUtils.isNotEmpty(templateFormTableVo.getDefaultDicValue())){
                        ProjectDictionary dictionaryInfo1 = projectDictionaryService.getDictionaryInfo(templateFormTableVo.getDefaultDicValue());
                        if(dictionaryInfo1 != null){
                            String targetProjectId = StringUtils.isEmpty(referenceProjectId) ? projectId : referenceProjectId;
                            ProjectDictionary targetDictionaryOptionInfo = projectDictionaryService.getDictByName(targetProjectId, dictionaryInfo1.getName(), targetDictionaryInfo.getId());
                            if(targetDictionaryOptionInfo != null){
                                templateFormTable.setDefaultDicValue(targetDictionaryOptionInfo.getId());
                            }
                        }
                    }
                }
            }
            templateFormTable.setCreateUser(operator);
            templateFormTable.setCreateTime(new Date());
            templateFormTable.setUpdateUser(null);
            templateFormTable.setUpdateTime(null);
            templateFormTable.setStatus(BusinessConfig.VALID_STATUS);
            templateFormTable.setTenantId(SecurityUtils.getSystemTenantId());
            templateFormTable.setPlatformId(SecurityUtils.getSystemPlatformId());
            templateFormTableMapper.insertSelective(templateFormTable);
        }
    }

    public List<TemplateFormConfigVo> getFormConfigListByTemplateIdAndVisitId(String templateId, String visitId) {
        return this.getTemplateFormConfigListByPlanId(templateId, null, "", null, null, null, false);
    }

    @Override
    public String deleteTemplateFormConfigById(String projectId, String formId, String operator) {
        // 发布过的表单禁止删除
        boolean visitFormPublishResult = checkProjectVisitFormPublishResult(projectId, formId);
        if (visitFormPublishResult) {
            return BusinessConfig.RETURN_MESSAGE_FORM_FORBIDDEN_01;
        }
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        for (TemplateFormDetail templateFormDetail : templateFormDetailList) {
            templateFormDetail.setStatus(BusinessConfig.NO_VALID_STATUS);
            templateFormDetail.setUpdateTime(new Date());
            templateFormDetail.setUpdateUser(operator);
            templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
        }
        TemplateFormConfig templateFormConfig = templateFormConfigMapper.selectByPrimaryKey(Long.parseLong(formId));
        templateFormConfig.setStatus(BusinessConfig.NO_VALID_STATUS);
        templateFormConfig.setUpdateTime(new Date());
        templateFormConfig.setUpdateUser(operator);
        templateFormConfigMapper.updateByPrimaryKeySelective(templateFormConfig);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    private boolean checkProjectVisitFormPublishResult(String projectId, String formId) {
        List<FlowPlanFormInfo> dataList = flowPlanFormService.getFormConfigListByProjectIdAndFormId(projectId, formId);
        return (dataList != null && dataList.size() > 0);
    }

    @Override
    public void updateTemplateFormDetailConfigByVaribaleId(String variableId, String BaseVariableId, String operator) {
        TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(variableId));
        if (templateFormDetail != null) {
            if (StringUtils.isEmpty(BaseVariableId)) {
                templateFormDetail.setBaseVariableId(null);
            } else {
                templateFormDetail.setBaseVariableId(Long.parseLong(BaseVariableId));
            }
            templateFormDetail.setUpdateUser(operator);
            templateFormDetail.setUpdateTime(new Date());
            templateFormDetailMapper.updateByPrimaryKey(templateFormDetail);
            redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VARIABLE.concat(templateFormDetail.getId().toString()), JSON.toJSONString(templateFormDetail));
        }
    }

    @Override
    public void updateTemplateFormDetailConfigById(TemplateFormDetail templateFormDetail) {
        templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
    }

    @Override
    public void updateTemplateFormTableConfigById(TemplateFormTable templateFormTable) {
        templateFormTableMapper.updateByPrimaryKeySelective(templateFormTable);
    }

    @Override
    public String deleteCustomTemplateFormByTemplateId(String templateId, String configType, String operator) {
        Template template = templateMapper.selectByPrimaryKey(Long.parseLong(templateId));
        if (template != null) {
//            template.setUpdateUserId(operator);
//            template.setUpdateTime(new Date());
//            template.setStatus(BusinessConfig.NO_VALID_STATUS);
            templateMapper.deleteByPrimaryKey(template.getId());
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public String deleteTemplateFormDetailById(String projectId, String formId, String id, String forceDelete, String operator) {
        TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(id));
        if (templateFormDetail != null) {
            //验证变量是否参与录入参与者数据
            ProjectTesteeResultExample example = new ProjectTesteeResultExample();
            ProjectTesteeResultExample.Criteria criteria = example.createCriteria();
            if (StringUtils.isNotEmpty(projectId)) {
                criteria.andProjectIdEqualTo(Long.parseLong(projectId));
            }
            if (StringUtils.isNotEmpty(formId)) {
                criteria.andFormIdEqualTo(Long.parseLong(formId));
            }
            criteria.andFormDetailIdEqualTo(Long.parseLong(id));
            criteria.andFieldValueIsNotNull();
            criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
            List<ProjectTesteeResult> projectTesteeResults = projectTesteeResultService.selectProjectTesteeFormVariableValues(example);
            if ("0".equals(forceDelete) && projectTesteeResults != null && projectTesteeResults.size() > 0) {
                return BusinessConfig.RETURN_MESSAGE_TABLE_FORBIDDEN_02;
            }
            templateFormDetail.setStatus(BusinessConfig.NO_VALID_STATUS);
            templateFormDetail.setUpdateTime(new Date());
            templateFormDetail.setUpdateUser(operator);
            templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);

            //如果是动态题组 删除模版信息
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetail.getType())) {
                Long groupId = templateFormDetail.getId();
                List<TemplateFormDetailVo> templateFormDetailInfoList = this.getTemplateFormDetailByGroupId(formId, groupId.toString(), "", false);
                for (TemplateFormDetailVo formDetaiInfo : templateFormDetailInfoList) {
                    TemplateFormDetail templateFormDetailParam = new TemplateFormDetail();
                    BeanUtils.copyProperties(formDetaiInfo, templateFormDetailParam);
                    templateFormDetailParam.setStatus(BusinessConfig.NO_VALID_STATUS);
                    templateFormDetailParam.setUpdateTime(new Date());
                    templateFormDetailParam.setUpdateUser(operator);
                    templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
                    if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetail.getType())) {
                        Long formDetailId = templateFormDetail.getId();
                        List<TemplateTableVo> templateFormTableList = this.getTemplateFormTableByFormDetailId(formDetailId.toString());
                        for (TemplateTableVo templateTableVo : templateFormTableList) {
                            TemplateFormTable templateFormTable = new TemplateFormTable();
                            BeanUtils.copyProperties(templateTableVo, templateFormTable);
                            templateFormTable.setStatus(BusinessConfig.NO_VALID_STATUS);
                            templateFormTable.setUpdateTime(new Date());
                            templateFormTable.setUpdateUser(operator);
                            templateFormTableMapper.updateByPrimaryKeySelective(templateFormTable);
                        }
                    }
                }
            }

            //如果是表格 则删除所有列
            if (BusinessConfig.PROJECT_VISIT_CRF_FORM_TABLE.equals(templateFormDetail.getType())) {
                Long formDetailId = templateFormDetail.getId();
                List<TemplateTableVo> templateFormTableList = this.getTemplateFormTableByFormDetailId(formDetailId.toString());
                for (TemplateTableVo templateTableVo : templateFormTableList) {
                    TemplateFormTable templateFormTable = new TemplateFormTable();
                    BeanUtils.copyProperties(templateTableVo, templateFormTable);
                    templateFormTable.setStatus(BusinessConfig.NO_VALID_STATUS);
                    templateFormTable.setUpdateTime(new Date());
                    templateFormTable.setUpdateUser(operator);
                    templateFormTableMapper.updateByPrimaryKeySelective(templateFormTable);
                }
            }
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public List<TemplateFormVariableExportVo> getProjectFormAndTableLabelByProjectIdAndFormVariableIds(String projectId, String visitId, String formId, String formDetailId, String formTableIds) {
        return templateFormDetailMapper.getProjectFormTableLabelListByProjectIdAndVisitId(projectId, visitId, formId, formDetailId, formTableIds);
    }

    // TODO
    @Override
    public List<TemplateFormConfigVo> getFormConfigListByVisitId(String projectId, String planId, String visitId, String excludeJoinGroupFromConfig) {
        if (StringUtils.isEmpty(planId)) {
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if (flowPlanInfo == null) {
                return null;
            }
            planId = flowPlanInfo.getId().toString();
        }
        return this.getTemplateFormConfigListByPlanId(null, projectId, planId, null, null, null, "1".equals(excludeJoinGroupFromConfig));
    }

    @Override
    public CustomResult<Object> saveTemplateFormVariableLogicConfig(TemplateFormLogicParam templateFormLogicParam) {
        return templateFormLogicService.saveFormVariableLogicData(templateFormLogicParam);
    }

    @Override
    public CustomResult<Object> saveTemplateFormDetailConfig(TemplateFormDetailParam templateFormDetailParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        Map<String, Object> dataMap = new HashMap<>();
        if (templateFormDetailParam.getAutoCreateForm() && templateFormDetailParam.getFormId() == null) {
            //生成模版
            Template template = new Template();
            if (templateFormDetailParam.getTemplateId() == null) {
                template.setId(SnowflakeIdWorker.getUuid());
                template.setIfPrivate(false);
                template.setConfigType(templateFormDetailParam.getConfigType());
                template.setStatus(BusinessConfig.VALID_STATUS);
                template.setCreateUserId(templateFormDetailParam.getOperator());
                template.setCreateTime(new Date());
                template.setTenantId(SecurityUtils.getSystemTenantId());
                template.setPlatformId(SecurityUtils.getSystemPlatformId());
                templateMapper.insert(template);
                templateFormDetailParam.setTemplateId(template.getId());
            } else {
                template = templateMapper.selectByPrimaryKey(templateFormDetailParam.getTemplateId());
            }
            TemplateFormConfigParam templateFormConfigParam = new TemplateFormConfigParam();
            templateFormConfigParam.setTemplateId(template.getId());
            templateFormConfigParam.setOpenEpro(false);
            templateFormConfigParam.setUploadResourceFile(false);
            templateFormConfigParam.setFormConfig("{'type':'table'}");
            templateFormConfigParam.setCreateUser(templateFormDetailParam.getOperator());
            CustomResult<Object> customResult1 = saveTemplateFormConfig(templateFormConfigParam);
            if (customResult1.getData() != null) {
                templateFormDetailParam.setFormId(Long.parseLong(customResult1.getData().toString()));
            }
        }

        TemplateFormDetail templateFormDetail = new TemplateFormDetail();
        String projectId = templateFormDetailParam.getProjectId() == null ? "" : templateFormDetailParam.getProjectId().toString();
        Boolean templateFormDetailCode = this.getTemplateFormDetailCode(projectId, templateFormDetailParam.getFormId().toString(), templateFormDetailParam.getId() == null ? "" : templateFormDetailParam.getId().toString(), templateFormDetailParam.getFieldName());
        if (templateFormDetailCode) {
            customResult.setCode(ResultCode.PROJECT_TESTEE_BASE_FORM_FIELD_FOUND.getCode());
            customResult.setMessage(ResultCode.PROJECT_TESTEE_BASE_FORM_FIELD_FOUND.getMessage());
            return customResult;
        }
        if (templateFormDetailParam.getId() == null) {
            templateFormDetail.setId(SnowflakeIdWorker.getUuid());
            buildTemplateFormDetailParams(templateFormDetailParam, templateFormDetail);
            if (templateFormDetailParam.getTemplateId() != null) {
                templateFormDetail.setTemplateId(templateFormDetailParam.getTemplateId());
            }
            // 如果fieldName为空 生成字段编码
            if (StringUtils.isEmpty(templateFormDetailParam.getFieldName())) {
                //String pinyin = PinyinUtil.getPinyin(templateFormDetailParam.getLabel(),"");
                templateFormDetail.setFieldName(RandomStringUtils.randomAlphanumeric(8));
            }
            if (StringUtils.isEmpty(templateFormDetailParam.getModel())) {
                templateFormDetail.setModel(templateFormDetailParam.getType().concat(SnowflakeIdWorker.getUuidValue()));
            }
            templateFormDetail.setCreateTime(new Date());
            templateFormDetail.setCreateUser(templateFormDetailParam.getOperator());
            Integer detailSortValue = getTemplateFormDetailSortValue(templateFormDetailParam.getFormId());
            templateFormDetail.setSort(detailSortValue);
            templateFormDetail.setTenantId(SecurityUtils.getSystemTenantId());
            templateFormDetail.setPlatformId(SecurityUtils.getSystemPlatformId());
            templateFormDetailMapper.insertSelective(templateFormDetail);
        } else {
            templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(templateFormDetailParam.getId());
            buildTemplateFormDetailParams(templateFormDetailParam, templateFormDetail);

            templateFormDetail.setUpdateTime(new Date());
            templateFormDetail.setUpdateUser(templateFormDetailParam.getOperator());
            templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
        }
        redisTemplateService.set(RedisKeyContants.PROJECT_TEMPLATE_VARIABLE.concat(templateFormDetail.getId().toString()), JSON.toJSONString(templateFormDetail));
        dataMap.put("templateId", templateFormDetailParam.getTemplateId() == null ? "" : templateFormDetailParam.getTemplateId().toString());
        dataMap.put("formId", templateFormDetailParam.getFormId().toString());
        dataMap.put("id", templateFormDetail.getId().toString());
        dataMap.put("fieldName", templateFormDetail.getFieldName());
        dataMap.put("model", templateFormDetail.getModel());
        customResult.setData(dataMap);
        return customResult;
    }

    private void buildTemplateFormDetailParams(TemplateFormDetailParam templateFormDetailParam, TemplateFormDetail templateFormDetail) {
        BeanUtils.copyProperties(templateFormDetailParam, templateFormDetail);
        //前端改字段再编辑的时候为更新导致编辑后会变为false 所以需要重新设置一下
        Boolean showTitle = templateFormDetail.getShowTitle();
        Boolean showContent = templateFormDetail.getShowContent();
        templateFormDetail.setShowTitle(showTitle);
        templateFormDetail.setShowContent(showContent);
        templateFormDetail.setFormId(templateFormDetailParam.getFormId());
        if (templateFormDetailParam.getExpand() != null) {
            templateFormDetail.setExpand(templateFormDetailParam.getExpand());
        }
    }

    @Override
    public CustomResult<Object> saveCustomTemplateFormConfig(TemplateCustomFormConfigParam templateConfigParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        if (StringUtils.isNotEmpty(templateConfigParam.getTemplateName())) {
            TemplateExample example = new TemplateExample();
            TemplateExample.Criteria criteria = example.createCriteria();
            criteria.andNameEqualTo(templateConfigParam.getTemplateName());
            criteria.andConfigTypeEqualTo(templateConfigParam.getConfigType());
            if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02.equals(templateConfigParam.getConfigType())) {
                criteria.andProjectIdEqualTo(templateConfigParam.getProjectId());
            }
            if (templateMapper.countByExample(example) > 0) {
                customResult.setMessage(BusinessConfig.SYSTEM_TEMPLATE_RECORD_FOUND);
                return customResult;
            }
        }

        Template template = new Template();
        if (templateConfigParam.getTemplateId() == null) {
            template.setId(SnowflakeIdWorker.getUuid());
            template.setName(templateConfigParam.getTemplateName());
            template.setCode(templateConfigParam.getTemplateCode());
            template.setIfPrivate(false);
            template.setConfigType(templateConfigParam.getConfigType());
            template.setStatus(BusinessConfig.VALID_STATUS);
            if (Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02.equals(templateConfigParam.getConfigType())) {
                template.setProjectId(templateConfigParam.getProjectId());
            }
            template.setDescription(templateConfigParam.getDescription());
            template.setCreateUserId(templateConfigParam.getCreateUser());
            template.setCreateTime(new Date());
            templateMapper.insert(template);
        } else {
            template = templateMapper.selectByPrimaryKey(templateConfigParam.getTemplateId());
        }

        if (templateConfigParam.getFormId() == null) {
            TemplateFormConfig templateFormConfig = new TemplateFormConfig();
            templateFormConfig.setId(SnowflakeIdWorker.getUuid());
            templateFormConfig.setTemplateId(template.getId());

            if (templateConfigParam.getProjectId() != null) {
                templateFormConfig.setProjectId(templateConfigParam.getProjectId());
            }
            //全局模版需要设置方案、流程相关
            templateFormConfig.setFormName(templateConfigParam.getTemplateName());
            templateFormConfig.setFormCode(templateConfigParam.getFormCode());
            templateFormConfig.setFormConfig(templateConfigParam.getFormConfig());
            templateFormConfig.setFormType(templateConfigParam.getFormType());
            templateFormConfig.setUploadResourceFile(templateConfigParam.getUploadResourceFile());
            templateFormConfig.setOpenEpro(templateConfigParam.getOpenEpro());
            templateFormConfig.setStatus(BusinessConfig.VALID_STATUS);
            templateFormConfig.setCreateUser(templateConfigParam.getCreateUser());
            templateFormConfig.setCreateTime(new Date());
            templateFormConfigMapper.insertSelective(templateFormConfig);
            //保存当前表单所有的数据字段
            List<TemplateFormDetailParam> templateFormDetailParamList = templateConfigParam.getTemplateFormDetailParamList();
            for (TemplateFormDetailParam templateFormDetailParam : templateFormDetailParamList) {
                TemplateFormDetail templateFormDetail = new TemplateFormDetail();
                BeanUtils.copyProperties(templateFormDetailParam, templateFormDetail);
                templateFormDetail.setId(SnowflakeIdWorker.getUuid());
                templateFormDetail.setTemplateId(template.getId());
                templateFormDetail.setFormId(templateFormConfig.getId());
                templateFormDetail.setModel(templateFormDetailParam.getType() + SnowflakeIdWorker.getUuid());
                templateFormDetail.setCopyVariableId(null);
                templateFormDetail.setStatus(BusinessConfig.VALID_STATUS);
                templateFormDetail.setCreateUser(templateConfigParam.getCreateUser());
                templateFormDetail.setCreateTime(new Date());
                templateFormDetail.setTenantId(SecurityUtils.getSystemTenantId());
                templateFormDetail.setPlatformId(SecurityUtils.getSystemPlatformId());
                templateFormDetailMapper.insert(templateFormDetail);

                //同步变量逻辑条件
                if (templateConfigParam.getCopyFormId() != null) {
                    List<TemplateFormLogicVo> templateFormLogicVoList = templateFormLogicService.getFormVariableLogicList("", null, "", templateConfigParam.getCopyFormId(), templateFormDetailParam.getId().toString(), null);
                    for (TemplateFormLogicVo templateFormLogicVo : templateFormLogicVoList) {
                        TemplateFormLogic templateFormLogic = new TemplateFormLogic();
                        templateFormLogic.setFormId(templateFormConfig.getId());
                        templateFormLogic.setFormDetailId(templateFormDetail.getId());
                        templateFormLogic.setLabel(templateFormDetail.getLabel());
                        templateFormLogic.setTemplateId(template.getId());
                        templateFormLogic.setStatus(BusinessConfig.VALID_STATUS);
                        templateFormLogic.setExpressionId(templateFormLogicVo.getExpressionId());
                        templateFormLogic.setConditionList(templateFormLogicVo.getConditionList());
                        templateFormLogic.setOptionList(templateFormLogicVo.getOptionList());
                        templateFormLogic.setExtands(templateFormLogicVo.getExtands());
                        templateFormLogic.setCreateTime(new Date());
                        templateFormLogic.setCreateUser(templateFormLogicVo.getCreateUser());

                        TemplateFormLogicParam templateFormLogicParam = new TemplateFormLogicParam();
                        BeanUtils.copyProperties(templateFormLogic, templateFormLogicParam);
                        List<TemplateFormLogicParam.TemplateFormLogicDataParam> dataList = templateFormLogicParam.getDataList();
                        List<TemplateFormLogicVo.TemplateFormLogicDataVo> templateFormLogicDataVoList = templateFormLogicVo.getDataList();
                        for (TemplateFormLogicVo.TemplateFormLogicDataVo templateFormLogicDataVo : templateFormLogicDataVoList) {
                            TemplateFormLogicParam.TemplateFormLogicDataParam templateFormLogicData = new TemplateFormLogicParam.TemplateFormLogicDataParam();
                            BeanUtils.copyProperties(templateFormLogicDataVo, templateFormLogicData);
                            templateFormLogicData.setTemplateId(template.getId());
                            templateFormLogicData.setCreateTime(new Date());
                            templateFormLogicData.setCreateUserId(templateFormLogicVo.getCreateUser());
                            templateFormLogicData.setStatus(BusinessConfig.VALID_STATUS);
                            dataList.add(templateFormLogicData);
                        }
                        templateFormLogicParam.setDataList(dataList);
                        templateFormLogicService.saveFormVariableLogicData(templateFormLogicParam);
                    }
                }

                // 表格类型
                if (BusinessConfig.PROJECT_VISIT_CRF_TABLE.equals(templateFormDetailParam.getType())) {
                    TemplateFormTableExample templateFormTableExample = new TemplateFormTableExample();
                    TemplateFormTableExample.Criteria criteria1 = templateFormTableExample.createCriteria();
                    criteria1.andFormDetailIdEqualTo(templateFormDetailParam.getId());
                    criteria1.andStatusEqualTo(BusinessConfig.VALID_STATUS);
                    List<TemplateFormTable> templateFormTableList = templateFormTableMapper.selectByExample(templateFormTableExample);
                    for (TemplateFormTable templateFormTableVo : templateFormTableList) {
                        TemplateFormTable templateFormTable = new TemplateFormTable();
                        BeanUtils.copyProperties(templateFormTableVo, templateFormTable);
                        templateFormTable.setId(SnowflakeIdWorker.getUuid());
                        templateFormTable.setFormId(templateFormConfig.getId());
                        templateFormTable.setFormDetailId(templateFormDetail.getId());
                        templateFormTable.setTemplateId(template.getId());
                        templateFormTable.setModel(templateFormDetailParam.getModel() + RandomStringUtils.randomNumeric(6));
                        templateFormTable.setCopyVariableId(null);
                        templateFormTable.setCopyTableId(null);
                        templateFormTable.setExpand(templateFormTableVo.getExpand());
                        templateFormTable.setCreateUser(templateConfigParam.getCreateUser());
                        templateFormTable.setCreateTime(new Date());
                        templateFormTable.setStatus(BusinessConfig.VALID_STATUS);
                        templateFormTable.setTenantId(SecurityUtils.getSystemTenantId());
                        templateFormTable.setPlatformId(SecurityUtils.getSystemPlatformId());
                        templateFormTableMapper.insertSelective(templateFormTable);
                    }
                }

                // 字段组类型
                if (BusinessConfig.PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP.equals(templateFormDetailParam.getType())) {

                }
            }
            customResult.setData(templateFormConfig.getId().toString());
        }
        customResult.setMessage(BusinessConfig.RETURN_MESSAGE_DEFAULT);
        return customResult;
    }


    public TemplateFormDetailVo getTemplateFormVariableIdByName(String projectId, String visitId, String formId, String variableName) {
        TemplateFormDetailVo templateFormDetailVo = templateFormDetailMapper.getTemplateFormVariableIdByName(projectId, visitId, formId, variableName);
        return templateFormDetailVo;
    }

    @Override
    public List<TemplateFormConfigVo> getCustomTemplateFormConfigList(String templateId, String templateName, String projectId, String configType, String templateStatus, String createUserId, String tenantId) {
        return templateFormConfigMapper.getCustomTemplateFormConfigList(templateId, templateName, projectId, configType, templateStatus, createUserId, tenantId);
    }

    @Override
    public CommonPage<TemplateFormConfigVo> getCustomTemplateFormConfigForPage(String projectId, String templateName, String templateStatus, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        if (StringUtils.isEmpty(templateStatus)) {
            templateStatus = BusinessConfig.VALID_STATUS;
        }
        String tenantId = SecurityUtils.getSystemTenantId();
        List<TemplateFormConfigVo> templateFormConfigList = templateFormConfigMapper.getCustomTemplateFormConfigList(null, templateName, projectId, Constants.PROJECT_TEMPLATE_CONFIG_TYPE_02, templateStatus, null, tenantId);
        return commonPageListWrapper(pageNum, pageSize, page, templateFormConfigList);
    }

    @Override
    public List<TemplateFormLogicVo> getFormVariableLogicList(String templateId, String projectId, String visitId, String formId, String formDetailId, String testeeId) {
        return templateFormLogicService.getFormVariableLogicList(templateId, projectId, visitId, formId, formDetailId, testeeId);
    }

    @Override
    public List<TemplateFormConfig> getTemplateFormConfigByGroupName(String projectId, String groupName) {
        TemplateFormConfigExample example = new TemplateFormConfigExample();
        TemplateFormConfigExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(projectId)) {
            criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        }
        if (StringUtils.isNotEmpty(groupName)) {
            criteria.andGroupNameEqualTo(groupName);
        }
        List<TemplateFormConfig> templateFormConfigList = templateFormConfigMapper.selectByExample(example);
        return templateFormConfigList;
    }

    @Override
    public List<TemplateFormDetail> getResearchTemplateFormVariableList(String projectId, String planId) {
        return templateFormConfigMapper.getResearchTemplateFormVariableList(projectId, planId);
    }

    @Override
    public List<TemplateFormConfig> getProjectTemplateFormListByProjectId(String projectId, String queryTable) {
        return templateFormConfigMapper.getProjectFormListByProjectId(projectId, queryTable);
    }

    @Override
    public String deleteTemplateFormTableById(String projectId, String tableId, boolean forceDelete, String operator) {
        TemplateFormTable templateFormTable = templateFormTableMapper.selectByPrimaryKey(Long.parseLong(tableId));
        if (templateFormTable == null) {
            return BusinessConfig.TEMPLATE_TABLE_NO_FOUND;
        }
        //如果已经录入参与者数据 禁止删除
        List<ProjectTesteeTableVo> projectTesteeTableRowFieldRecords = projectTesteeTableService.getProjectTesteeTableRowFieldRecord(projectId, tableId);
        if (CollectionUtil.isNotEmpty(projectTesteeTableRowFieldRecords) && !forceDelete) {
            return BusinessConfig.RETURN_MESSAGE_TABLE_FORBIDDEN_01;
        }
        templateFormTable.setStatus(BusinessConfig.NO_VALID_STATUS);
        templateFormTable.setUpdateTime(new Date());
        templateFormTable.setUpdateUser(operator);
        templateFormTableMapper.updateByPrimaryKeySelective(templateFormTable);
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public TemplateFormConfig getTemplateFormConfigById(Long formId) {
        return templateFormConfigMapper.selectByPrimaryKey(formId);
    }

    @Override
    public CustomResult<Object> deleteFormLogicData(String id) {
        return templateFormLogicService.deleteFormLogicData(id);
    }

    @Override
    public List<TemplateFormLogicVo> getTemplateVariableLoginConfig(String templateFormId, String userId) {
        TemplateFormConfig templateFormConfig = templateFormConfigMapper.selectByPrimaryKey(Long.parseLong(templateFormId));
        String copyTemplateId = templateFormConfig.getTemplateId().toString();
        List<TemplateFormLogicVo> templateFormLogicVoList = templateFormLogicService.getFormLogicConfigListByTemplateId(copyTemplateId, templateFormId, null);
        return templateFormLogicVoList;
    }

    @Override
    public List<TemplateFormDetailVo> getTemplateFormDetailByFormIdAndVariableType(String templateId, String formId, String variableType) {
        List<TemplateFormDetailVo> dataList = new ArrayList<>();
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(templateId)) {
            criteria.andTemplateIdEqualTo(Long.parseLong(templateId));
        }
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        if (StringUtils.isEmpty(variableType)) {
            criteria.andTypeIn(Arrays.asList(BusinessConfig.PROJECT_VISIT_CRF_FORM_RADIO, BusinessConfig.PROJECT_VISIT_CRF_FORM_CHECKBOX));
        } else {
            criteria.andTypeIn(Arrays.asList(variableType));
        }
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        for (TemplateFormDetail templateFormDetail : templateFormDetailList) {
            TemplateFormDetailVo templateFormDetailVo = new TemplateFormDetailVo();
            BeanUtils.copyProperties(templateFormDetail, templateFormDetailVo);
            dataList.add(templateFormDetailVo);
        }
        return dataList;
    }

    @Override
    public TemplateFormConfigVo getTemplateFormConfigBaseInfoByFormId(String formId) {
        TemplateFormConfig templateFormConfig = getTemplateFormConfigById(Long.parseLong(formId));
        if (templateFormConfig != null) {
            TemplateFormConfigVo templateFormConfigVo = new TemplateFormConfigVo();
            BeanUtils.copyProperties(templateFormConfig, templateFormConfigVo);
            return templateFormConfigVo;
        }
        return null;
    }

    @Override
    public TemplateFormTable getTemplateFormTableConfigById(Long formTableId) {
        return templateFormTableMapper.selectByPrimaryKey(formTableId);
    }

    @Override
    public TemplateFormConfig getFormIdByCoustomVisitName(String projectId, String visitId, String projectLabelVisitName) {
        TemplateFormConfigExample example = new TemplateFormConfigExample();
        TemplateFormConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andFormNameEqualTo(projectLabelVisitName);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<TemplateFormConfig> templateFormConfigList = templateFormConfigMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(templateFormConfigList)) {
            return templateFormConfigList.get(0);
        }
        return null;
    }

    @Override
    public List<ProjectTesteeFormDetailAndTableResultVo> getTemplateFormDetailAndTableResult(String projectId, String planId, String visitId, String formId, String formDetailId, String tableId) {
        List<ProjectTesteeFormDetailAndTableResultVo> dataList = projectTesteeResultService.getTemplateFormDetailAndTableResult(projectId, "", visitId, formId, formDetailId, tableId);
        return dataList;
    }

    @Override
    public List<TemplateTableVo> getTemplateFormTableByFormDetailId(String formDetailId) {
        List<TemplateTableVo> projectTesteeTableRowHead = projectTesteeTableService.getProjectTesteeTableRowHead(null, formDetailId, false);
        return projectTesteeTableRowHead;
    }

    @Override
    public TemplateFormDetailExcelImportVo getFormDetailByFormNameAndVariableName(String projectId, String visitName, String formName, String variableKey) {
        //TemplateFormConfig templateFormConfig = templateFormConfigMapper.getTemplateConfigByFormName(projectId, formName);
        return templateFormDetailMapper.getFormDetailByFormNameAndVariableName(projectId, visitName, formName, variableKey);
    }


    @Override
    public TemplateFormTableExcelImportVo getFormTableInfoByFormNameAndVariableName(String projectId, String visitName, String formName, String variableKey) {
        return templateFormDetailMapper.getFormTableInfoByFormNameAndVariableName(projectId, visitName, formName, variableKey);
    }

    @Override
    public Boolean getProjectTemplateNameResult(String projectId, String templateName, String configType) {
        if (StringUtils.isEmpty(configType)) {
            configType = Constants.PROJECT_TEMPLATE_CONFIG_TYPE_03;
        }
        TemplateExample example = new TemplateExample();
        TemplateExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(templateName);
        criteria.andConfigTypeEqualTo(configType);
        if (StringUtils.isNotEmpty(projectId)) {
            criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        }
        return templateMapper.countByExample(example) > 0;
    }

    @Override
    public Boolean getProjectTemplateFormConfigNameResult(String projectId, String formConfigName) {
        TemplateFormConfigExample example = new TemplateFormConfigExample();
        TemplateFormConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andFormNameEqualTo(formConfigName);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        return templateFormConfigMapper.countByExample(example) > 0;
    }

    @Override
    public CustomResult<Object> editSortByIds(String[] ids) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        for (int i = 0; i < ids.length; i++) {
            TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(Long.parseLong(ids[i]));
            if (templateFormDetail != null) {
                templateFormDetail.setSort(i + 1);
                templateFormDetail.setUpdateTime(new Date());
                templateFormDetail.setUpdateUser(SecurityUtils.getUserIdValue());
                templateFormDetailMapper.updateByPrimaryKey(templateFormDetail);
            }
        }
        return customResult;
    }

    @Override
    public List<TemplateFormVariableViewVo> getTemplateCurrentFormVariableList(String projectId, String formId, String optionId) {
        if(StringUtils.isNotEmpty(projectId)&& "0".equals(projectId)){
            projectId = null;
        }
        List<TemplateFormVariableViewVo> templateCurrentFormVariableList = templateFormDetailMapper.getTemplateCurrentFormVariableList(projectId, formId);
        return templateCurrentFormVariableList;
    }

    @Override
    public List<TemplateFormVariableViewVo> getFormVariableAndRuleList(String projectId, String formId) {
        return templateFormDetailMapper.getFormVariableAndRuleList(projectId, formId);
    }

    @Override
    public List<TemplateVariableVo> getTemplateVariableDetailConfig() {
        return templateFormDetailMapper.getTemplateVariableDetailConfig();
    }

    @Override
    public CommonPage<TemplateLabConfigVo> getProjectTemplateLabConfigListForPage(String projectId, String variableName, String labType, Integer pageNum, Integer pageSize) {
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        List<TemplateLabConfigVo> templateLabConfigList = templateLabConfigMapper.getProjectTemplateLabConfigListForPage(projectId, variableName, labType, SecurityUtils.getSystemTenantId());
        for (TemplateLabConfigVo templateLabConfigVo : templateLabConfigList) {
            List<String> projectOrgNames = new ArrayList<>();
            if (StringUtils.isNotEmpty(templateLabConfigVo.getProjectOrgIds())) {
                Boolean applyTotalOrg = templateLabConfigVo.getApplyTotalOrg();
                if (applyTotalOrg) {
                    templateLabConfigVo.setProjectOrgNames("全部中心");
                } else {
                    String[] projectOrgIds = templateLabConfigVo.getProjectOrgIds().split(",");
                    for (String projectOrgId : projectOrgIds) {
                        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectOrgId);
                        Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
                        projectOrgNames.add(systemOrganizationInfo.getName());
                    }
                    String projectOrgNameValue = projectOrgNames.stream().collect(Collectors.joining(","));
                    templateLabConfigVo.setProjectOrgNames(projectOrgNameValue);
                }
            }
            if (templateLabConfigVo.getLabResource() != null) {
                ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(templateLabConfigVo.getLabResource().toString());
                Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
                templateLabConfigVo.setLabResourceName(systemOrganizationInfo.getName());
            }
        }
        return commonPageListWrapper(pageNum, pageSize, page, templateLabConfigList);
    }

    @Override
    public CustomResult<Object> saveTemplateFormVariableLabConfig(TemplateLabConfigParam templateLabConfigParam) {
        CustomResult<Object> customResult = new CustomResult<Object>();
        TemplateLabConfig templateLabConfigResult = templateLabConfigMapper.checkTemplateLabConfigParam(templateLabConfigParam);
        if (templateLabConfigResult != null && !templateLabConfigResult.getId().equals(templateLabConfigParam.getId())) {
            customResult.setCode(ResultCode.TEMPLATE_LAB_CONFIG_VARIABLE_FOUND.getCode());
            customResult.setMessage(ResultCode.TEMPLATE_LAB_CONFIG_VARIABLE_FOUND.getMessage());
            return customResult;
        }
        TemplateLabConfig templateLabConfig = new TemplateLabConfig();
        if (templateLabConfigParam.getId() == null) {
            BeanUtils.copyProperties(templateLabConfigParam, templateLabConfig);
            templateLabConfig.setDicResource(templateLabConfigParam.getDictionaryResourceType());
            templateLabConfig.setId(SnowflakeIdWorker.getUuid());
            templateLabConfig.setStatus(BusinessConfig.VALID_STATUS);
            templateLabConfig.setCreateUserId(SecurityUtils.getUserIdValue());
            templateLabConfig.setCreateTime(new Date());
            templateLabConfig.setTenantId(SecurityUtils.getSystemTenantId());
            templateLabConfig.setPlatformId(SecurityUtils.getSystemPlatformId());
            templateLabConfigMapper.insertSelective(templateLabConfig);
        } else {
            templateLabConfig = templateLabConfigMapper.selectByPrimaryKey(templateLabConfigParam.getId());
            BeanUtils.copyProperties(templateLabConfigParam, templateLabConfig);
            templateLabConfig.setDicResource(templateLabConfigParam.getDictionaryResourceType());
            templateLabConfig.setUpdateTime(new Date());
            templateLabConfig.setUpdateUserId(SecurityUtils.getUserIdValue());
            templateLabConfigMapper.updateByPrimaryKeySelective(templateLabConfig);
        }
        // 更新crf表单变量属性 按照字段组、表格、普通变量查询对应的字段类型
        if (BusinessConfig.LAB_CONFIG_SCOPE_FORM.equals(templateLabConfigParam.getScope())) {
            if (templateLabConfigParam.getFormTableId() != null) {
                TemplateFormTable templateFormTable = getTemplateFormTableConfigById(templateLabConfigParam.getFormTableId());
                templateFormTable.setLabConfigScope(templateLabConfig.getScope());
                templateFormTableMapper.updateByPrimaryKeySelective(templateFormTable);
            } else {
                TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(templateLabConfigParam.getFormDetailId());
                if (templateFormDetail != null) {
                    templateFormDetail.setLabConfigScope(templateLabConfig.getScope());
                    templateFormDetailMapper.updateByPrimaryKeySelective(templateFormDetail);
                }
            }
        }
        if (BusinessConfig.LAB_CONFIG_SCOPE_VISIT.equals(templateLabConfigParam.getScope())) {
            String projectId = templateLabConfigParam.getProjectId().toString();
            String planId = templateLabConfigParam.getPlanId().toString();
            String visitId = templateLabConfigParam.getVisitId().toString();
            String formId = templateLabConfigParam.getFormId().toString();
            FlowFormSet flowFormSet = flowFormSetService.getFormConfigListByProjectIdAndFormId(projectId, planId, visitId, formId);
            flowFormSet.setLabConfigScope(templateLabConfig.getScope());
            flowFormSetService.updateFlowFormBaseInfo(flowFormSet);
            TemplateFormDetail templateFormDetail = templateFormDetailMapper.selectByPrimaryKey(templateLabConfigParam.getFormDetailId());
            if (templateFormDetail != null) {
                templateFormDetail.setLabConfigScope(null);
                templateFormDetailMapper.updateByPrimaryKey(templateFormDetail);
            }
        }
        return customResult;
    }

    @Override
    public TemplateLabConfigVo getProjectTemplateLabConfigDetail(String id) {
        TemplateLabConfig templateLabConfig = templateLabConfigMapper.selectByPrimaryKey(NumberUtil.parseLong(id));
        if (templateLabConfig == null) {
            return null;
        }
        TemplateLabConfigVo templateLabConfigVo = new TemplateLabConfigVo();
        BeanUtils.copyProperties(templateLabConfig, templateLabConfigVo);
        // 如果是定型配置返回项目字典
        if (BusinessConfig.LAB_CONFIG_TYPE_02.equals(templateLabConfig.getLabType())) {
            templateLabConfigVo.setDictionaryResourceType(templateLabConfig.getDicResource());
            templateLabConfigVo.setDictionaryResourceId(templateLabConfig.getRefDicId().toString());
        }
        return templateLabConfigVo;
    }

    @Override
    public CustomResult<Object> deleteProjectTemplateLabConfig(String id) {
        CustomResult<Object> customResult = new CustomResult<>();
        TemplateLabConfig templateLabConfig = templateLabConfigMapper.selectByPrimaryKey(NumberUtil.parseLong(id));
        if (templateLabConfig == null) {
            customResult.setCode(ResultCode.TEMPLATE_LAB_CONFIG_NO_FOUND.getCode());
            customResult.setMessage(ResultCode.TEMPLATE_LAB_CONFIG_NO_FOUND.getMessage());
            return customResult;
        }
        TemplateLabOrgInfoExample templateLabOrgInfoExample = new TemplateLabOrgInfoExample();
        templateLabOrgInfoExample.createCriteria().andLabConfigIdEqualTo(templateLabConfig.getId()).andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<TemplateLabOrgInfo> templateLabOrgInfoList = templateLabOrgInfoMapper.selectByExample(templateLabOrgInfoExample);
        if (CollectionUtil.isNotEmpty(templateLabOrgInfoList)) {
            customResult.setCode(ResultCode.TEMPLATE_LAB_CONFIG_ORG_LIST_FOUND.getCode());
            customResult.setMessage(ResultCode.TEMPLATE_LAB_CONFIG_ORG_LIST_FOUND.getMessage());
            return customResult;
        }
        templateLabConfig.setStatus(BusinessConfig.NO_VALID_STATUS);
        templateLabConfig.setUpdateTime(new Date());
        templateLabConfig.setUpdateUserId(SecurityUtils.getUserIdValue());
        templateLabConfigMapper.updateByPrimaryKeySelective(templateLabConfig);
        return customResult;
    }

    @Override
    public CustomResult<Object> saveProjectTemplateLabConfigForOrg(String labConfigId, String projectOrgIds, boolean applyTotalOrg) {
        CustomResult<Object> customResult = new CustomResult<>();
        TemplateLabConfig templateLabConfig = templateLabConfigMapper.selectByPrimaryKey(NumberUtil.parseLong(labConfigId));
        if (templateLabConfig == null) {
            customResult.setCode(ResultCode.TEMPLATE_LAB_CONFIG_NO_FOUND.getCode());
            customResult.setMessage(ResultCode.TEMPLATE_LAB_CONFIG_NO_FOUND.getMessage());
            return customResult;
        }
        if (StringUtils.isEmpty(projectOrgIds)) {
            TemplateLabOrgInfoExample example = new TemplateLabOrgInfoExample();
            example.createCriteria().andLabConfigIdEqualTo(NumberUtil.parseLong(labConfigId));
            templateLabOrgInfoMapper.deleteByExample(example);
            templateLabConfig.setEnabled(false);
            templateLabConfigMapper.updateByPrimaryKeySelective(templateLabConfig);
            return customResult;
        } else {
            String[] projectOrgIdArray = projectOrgIds.split(",");
            if (applyTotalOrg) {
                List<ProjectOrgVo> projectOrgList = organizationService.getProjectOrgListByCondition(templateLabConfig.getProjectId().toString(), null, null);
                for (ProjectOrgVo projectOrg : projectOrgList) {
                    TemplateLabOrgInfo templateLabOrgInfo = new TemplateLabOrgInfo();
                    templateLabOrgInfo.setId(SnowflakeIdWorker.getUuid());
                    templateLabOrgInfo.setProjectId(templateLabConfig.getProjectId());
                    templateLabOrgInfo.setLabConfigId(NumberUtil.parseLong(labConfigId));
                    templateLabOrgInfo.setProjectOrgId(projectOrg.getId());
                    templateLabOrgInfo.setStatus(BusinessConfig.VALID_STATUS);
                    templateLabOrgInfo.setCreateUserId(SecurityUtils.getUserIdValue());
                    templateLabOrgInfo.setCreateTime(new Date());
                    templateLabOrgInfo.setTenantId(SecurityUtils.getSystemTenantId());
                    templateLabOrgInfo.setPlatformId(SecurityUtils.getSystemPlatformId());
                    TemplateLabOrgInfo templateLabConfigOrgInfo = templateLabOrgInfoMapper.getTemplateLabOrgInfoByLabConfigIdAndOrgId(templateLabConfig.getId().toString(), projectOrg.getId().toString());
                    if (templateLabConfigOrgInfo == null) {
                        templateLabOrgInfoMapper.insertSelective(templateLabOrgInfo);
                    }
                }
            } else {
                for (String projectOrgId : projectOrgIdArray) {
                    TemplateLabOrgInfo templateLabOrgInfo = new TemplateLabOrgInfo();
                    templateLabOrgInfo.setId(SnowflakeIdWorker.getUuid());
                    templateLabOrgInfo.setProjectId(templateLabConfig.getProjectId());
                    templateLabOrgInfo.setLabConfigId(NumberUtil.parseLong(labConfigId));
                    templateLabOrgInfo.setProjectOrgId(NumberUtil.parseLong(projectOrgId));
                    templateLabOrgInfo.setStatus(BusinessConfig.VALID_STATUS);
                    templateLabOrgInfo.setCreateUserId(SecurityUtils.getUserIdValue());
                    templateLabOrgInfo.setCreateTime(new Date());
                    templateLabOrgInfo.setTenantId(SecurityUtils.getSystemTenantId());
                    templateLabOrgInfo.setPlatformId(SecurityUtils.getSystemPlatformId());
                    TemplateLabOrgInfo templateLabConfigOrgInfo = templateLabOrgInfoMapper.getTemplateLabOrgInfoByLabConfigIdAndOrgId(templateLabConfig.getId().toString(), projectOrgId);
                    if (templateLabConfigOrgInfo == null) {
                        templateLabOrgInfoMapper.insertSelective(templateLabOrgInfo);
                    }
                }
            }
            templateLabConfig.setEnabled(true);
            templateLabConfig.setApplyTotalOrg(applyTotalOrg);
            templateLabConfigMapper.updateByPrimaryKeySelective(templateLabConfig);
        }
        return customResult;
    }

    @Override
    public TemplateLabConfigExpand getTemplateLabConfigByVariableId(String projectOrgId, String labConfigScope, String visitId, String formId, String variableId) {
        return templateLabConfigMapper.getTemplateLabConfigByVariableId(projectOrgId, labConfigScope, visitId, formId, variableId);
    }

    @Override
    public TemplateLabConfigExpand getTemplateLabConfigByVariableIdAndTableId(String projectOrgId, String labConfigScope, String visitId, String formId, String variableId, String tableId) {
        return templateLabConfigMapper.getTemplateLabConfigByVariableIdAndTableId(projectOrgId, labConfigScope, visitId, formId, variableId, tableId);
    }

    @Override
    public TemplateLabConfigExpand getTemplateLabConfigByGroupIdAndVariableId(String projectOrgId, String labConfigScope, String visitId, String formId, String groupId, String variableId) {
        return templateLabConfigMapper.getTemplateLabConfigByGroupIdAndVariableId(projectOrgId, labConfigScope, visitId, formId, groupId, variableId);
    }

    @Override
    public TemplateLabConfigExpand getTemplateLabConfigByGroupIdAndTableId(String projectOrgId, String labConfigScope, String visitId, String formId, String groupId, String variableId, String tableId) {
        return templateLabConfigMapper.getTemplateLabConfigByGroupIdAndTableId(projectOrgId, labConfigScope, visitId, formId, groupId, variableId, tableId);
    }

    @Override
    public TemplateTesteeJoinGroupFormVo getTemplateTesteeJoinGroupFormConfig(String projectId, String planId) {
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if (StringUtils.isEmpty(planId)) {
            FlowPlan flowPlanInfo = flowPlanService.getPlanByProjectId(projectId);
            if (flowPlanInfo == null) {
                return null;
            }
            planId = flowPlanInfo.getId().toString();
        }
        TemplateTesteeJoinGroupFormVo templateTesteeJoinGroupForm = templateFormConfigMapper.getTemplateTesteeJoinGroupFormConfig(projectId, planId);
        if (templateTesteeJoinGroupForm != null) {
            templateTesteeJoinGroupForm.setEnableProjectRandomizedConfig(projectBaseInfo.getEnableRandomizedConfig());
            ProjectVisitVo projectVisitConfig = projectVisitConfigService.getProjectVisitBaseConfigByVisitId(templateTesteeJoinGroupForm.getVisitId().toString());
            if (projectVisitConfig != null) {
                templateTesteeJoinGroupForm.setVisitName(projectVisitConfig.getVisitName());
            }
            //
            List<TemplateFormDetailVo> variableList = new ArrayList();
            List<TemplateFormDetail> templateTesteeFormDetailList = templateFormConfigMapper.getFormDetailBaseConfigListByFormId(projectId, templateTesteeJoinGroupForm.getFormId().toString());
            for (TemplateFormDetail templateFormDetail : templateTesteeFormDetailList) {
                TemplateFormDetailVo templateFormDetailVo = new TemplateFormDetailVo();
                BeanUtils.copyProperties(templateFormDetail, templateFormDetailVo);
                variableList.add(templateFormDetailVo);
            }
            templateTesteeJoinGroupForm.setVariableList(variableList);
        }
        return templateTesteeJoinGroupForm;
    }

    @Override
    public List<TemplateFormDetailVo> getTemplateFormVariableListByFormId(String projectId, String formId, boolean enableRandomizedConfig) {
        List<String> randomizedConfigVariableList = new ArrayList<>();
        if (enableRandomizedConfig) {
            List<RandomizedVo> randomizedConfigList = randomizedControlledTrialsService.getRandomizedConfig(projectId);
            for (RandomizedVo randomizedConfig : randomizedConfigList) {
                randomizedConfigVariableList.add(randomizedConfig.getFormId()+"_"+randomizedConfig.getVariableId());
            }
        }
        List<TemplateFormDetailVo> variableList = new ArrayList();
        List<TemplateFormDetail> templateTesteeFormDetailList = templateFormConfigMapper.getFormDetailBaseConfigListByFormId(projectId, formId);
        for (TemplateFormDetail templateFormDetail : templateTesteeFormDetailList) {
            TemplateFormDetailVo templateFormDetailVo = new TemplateFormDetailVo();
            BeanUtils.copyProperties(templateFormDetail, templateFormDetailVo);
            if (enableRandomizedConfig && randomizedConfigVariableList.contains(formId+"_"+templateFormDetail.getId().toString())) {
                continue;
            }
            variableList.add(templateFormDetailVo);
        }
        return variableList;
    }
    
    /**
     * @param projectId
     * @param formCode
     * @return
     */
    @Override
    public TemplateFormConfig getProjectTemplateFormICF(String projectId, String formCode) {
        TemplateFormConfigExample example = new TemplateFormConfigExample();
        TemplateFormConfigExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andFormCodeEqualTo(formCode);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<TemplateFormConfig> templateFormConfigList = templateFormConfigMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(templateFormConfigList)) {
            return templateFormConfigList.get(0);
        }
        return null;
    }
    
    /**
     * @param projectId
     * @param formId
     * @param variableCode
     * @return
     */
    @Override
    public TemplateFormDetail getTemplateVariableValueForICFDATE(String projectId, String formId, String variableCode) {
        TemplateFormDetailExample example = new TemplateFormDetailExample();
        TemplateFormDetailExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andFormIdEqualTo(Long.parseLong(formId));
        criteria.andFieldNameEqualTo(variableCode);
        criteria.andStatusEqualTo(BusinessConfig.VALID_STATUS);
        List<TemplateFormDetail> templateFormDetailList = templateFormDetailMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(templateFormDetailList)) {
            return templateFormDetailList.get(0);
        }
        return null;
    }
    
}
