package com.haoys.user.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.haoys.user.domain.vo.testee.ProjectTesteeDynamicColumnVo;
import javassist.CannotCompileException;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtField;
import javassist.CtNewMethod;
import javassist.Modifier;
import javassist.NotFoundException;
import javassist.bytecode.AnnotationsAttribute;
import javassist.bytecode.ClassFile;
import javassist.bytecode.ConstPool;
import javassist.bytecode.annotation.Annotation;
import javassist.bytecode.annotation.StringMemberValue;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class EasyPoiDynamicGenerationClassUtil {

    public static final String CLASS_NAME_PREFIX = "com.jtech.toa.utils.EasyPoiExcelVO@";
    public static final String ANNOTATION_PACKAGE_NAME = "cn.afterturn.easypoi.excel.annotation.Excel";
    public static final String STRING_PACKAGE_NAME = "java.lang.String";

    public static Class<?> generatePrototypeClass(List<ProjectTesteeDynamicColumnVo> list)
            throws NotFoundException, CannotCompileException, IOException {
        String className = CLASS_NAME_PREFIX + UUID.randomUUID().toString();
        ClassPool pool = ClassPool.getDefault();
        CtClass clazz = pool.makeClass(className);
        ClassFile ccFile = clazz.getClassFile();
        ConstPool constpool = ccFile.getConstPool();
        //添加fields
        addExpressField(pool, clazz, constpool,list);

        return clazz.toClass();
    }

    private static void addExpressField(ClassPool pool, CtClass clazz, ConstPool constpool,List<ProjectTesteeDynamicColumnVo> list) throws CannotCompileException, NotFoundException {
        for (ProjectTesteeDynamicColumnVo dynamicColumnDto : list) {
            addFieldAndAnnotation(pool, clazz, constpool, dynamicColumnDto.getTitle(), dynamicColumnDto.getField());
        }
    }


    private static void addFieldAndAnnotation(ClassPool pool, CtClass clazz, ConstPool constpool, String titleName, String fieldName) throws NotFoundException, CannotCompileException {
        //生成field
        CtField field = new CtField(pool.get(STRING_PACKAGE_NAME), fieldName, clazz);
        field.setModifiers(Modifier.PUBLIC);
        //添加easypoi的注解
        AnnotationsAttribute fieldAttr = new AnnotationsAttribute(constpool, AnnotationsAttribute.visibleTag);
        Annotation annotation = new Annotation(ANNOTATION_PACKAGE_NAME, constpool);
        annotation.addMemberValue("name", new StringMemberValue(titleName, constpool));
        fieldAttr.addAnnotation(annotation);
        field.getFieldInfo().addAttribute(fieldAttr);

        //生成get,set方法
        clazz.addMethod(CtNewMethod.getter("get" + upperFirstLatter(fieldName), field));
        clazz.addMethod(CtNewMethod.setter("set" + upperFirstLatter(fieldName), field));

        clazz.addField(field);
    }

    private static String upperFirstLatter(String letter) {
        return letter.substring(0, 1).toUpperCase() + letter.substring(1);
    }

    private static String getFieldValue(String fieldName, Object data) throws Exception{
        Method m =  (Method) data.getClass().getMethod("get" + EasyPoiDynamicGenerationClassUtil.upperFirstLatter(fieldName));
        return (String)m.invoke(data);
    }

    public static List<Map<String, String>> parseObjectList(List<?> result) throws Exception {
        List<Map<String, String>> parseResult = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(result)) {
            Class<?> clazz = result.get(0).getClass();
            Field[] fields = clazz.getDeclaredFields();
            for(Object data:result){
                Map<String, String> parseDataMap = Maps.newConcurrentMap();
                for (Field field : fields) {
                    String value = getFieldValue(field.getName(), data);
                    parseDataMap.put(field.getName(), value == null ? "" : value);
                }
                parseResult.add(parseDataMap);
            }
        }
        return parseResult;
    }
}
