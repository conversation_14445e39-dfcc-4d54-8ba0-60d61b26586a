package com.haoys.user.service;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.domain.param.ProjectTesteeVariableMappingParam;
import com.haoys.user.domain.vo.testee.ProjectTesteeVariableMappingVo;

import java.util.List;

/**
 * 自定义标题-映射到
 */
public interface ProjectTesteeVariableMappingService {

    /**
     * 保存配置
     * @param projectTesteeVariableMappingParam
     * @return 保存结果
     */
    CommonResult<Object> create(ProjectTesteeVariableMappingParam projectTesteeVariableMappingParam);

    /**
     * 根据id获取
     * @param baseVariableId
     * @return
     */
    CommonResult<List<ProjectTesteeVariableMappingVo>> getByBaseVariableId(Long baseVariableId);
}
