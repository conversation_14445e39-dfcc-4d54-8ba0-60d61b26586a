package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectUserOrgRoleVo;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.enums.ProjectRoleResultEnums;
import com.haoys.user.domain.vo.auth.ProjectOrgUserRoleVo;
import com.haoys.user.domain.vo.project.ProjectOrgRoleVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.mapper.*;
import com.haoys.user.model.*;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectOrgRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectOrgRoleServiceImpl implements ProjectOrgRoleService {

    private final ProjectOrgRoleMapper projectOrgRoleMapper;
    private final ProjectOrgMenuMapper projectOrgMenuMapper;
    private final ProjectRoleMapper projectRoleMapper;
    private final ProjectRoleMenuMapper projectRoleMenuMapper;
    private final ProjectOrgUserRoleMapper projectOrgUserRoleMapper;
    private final ProjectOrgRoleMenuMapper projectOrgRoleMenuMapper;
    private final OrganizationService organizationService;

    @Override
    public List<ProjectRoleQuery> selectProjectOrgRoleList(ProjectRoleQuery projectRoleQuery) {
        PageHelper.startPage(projectRoleQuery.getPageNum(), projectRoleQuery.getPageSize());
        return projectOrgRoleMapper.selectProjectOrgRoleList(projectRoleQuery);
    }

    @Override
    public ProjectOrgRole getProjectOrgRoleByProjectOrgIdAndRoleName(String projectId, String projectOrgId, String projectRoleName) {
        return projectOrgRoleMapper.getProjectOrgRoleByProjectOrgIdAndRoleName(projectId, projectOrgId, projectRoleName);
    }

    @Override
    public Boolean checkProjectRoleNameUnique(ProjectRoleQuery role) {
        Long roleId = StringUtils.isNull(role.getId()) ? -1L : role.getId();
        // 根据项目id和名称检查是否已经存在，并且id也不相同，因为如果是编辑查出的是相同的
        ProjectOrgRole projectRole = projectOrgRoleMapper.getProjectRoleInfoByRoleNameOrEnName(role.getProjectId().toString(),
                role.getProjectOrgId().toString(), role.getName(), role.getEnname());
        if(StringUtils.isNotNull(projectRole) && !projectRole.getId().equals(roleId)){
            return false;
        }
        return true;
    }

    @Override
    public CommonResult insertProjectOrgRole(ProjectRoleQuery projectRoleQuery) {
        if (!checkProjectRoleNameUnique(projectRoleQuery)){
            return CommonResult.failed(ProjectRoleResultEnums.B40006);
        }
        ProjectOrgRole projectOrgRole = new ProjectOrgRole();
        projectOrgRole.setProjectId(projectRoleQuery.getProjectId());
        projectOrgRole.setId(SnowflakeIdWorker.getUuid());
        projectOrgRole.setOrgId(projectRoleQuery.getOrgId());
        projectOrgRole.setProjectOrgId(projectRoleQuery.getProjectOrgId());
        projectOrgRole.setOrgCode(projectRoleQuery.getProjectOrgCode());
        projectOrgRole.setRoleName(projectRoleQuery.getName());
        projectOrgRole.setEname(projectRoleQuery.getEnname());
        projectOrgRole.setResourceRoleId(projectRoleQuery.getResourceRoleId());
        projectOrgRole.setDescription(projectRoleQuery.getDescription());
        projectOrgRole.setCreateUserId(SecurityUtils.getUserId().toString());
        projectOrgRole.setStatus(BusinessConfig.VALID_STATUS);
        projectOrgRole.setCreateTime(new Date());

        projectOrgRoleMapper.insert(projectOrgRole);
        // 获取角色绑定的菜单。新增到中心角色表中
        List<String> menuIds =projectRoleQuery.getMenuIds();
        if(CollectionUtil.isEmpty(menuIds)){
            throw new ServiceException(ResultCode.PROJECT_ROLE_MENU_NOT_FOUND);
        }
        // 新增用户与角色管理
        List<ProjectOrgRoleMenu> lists = new ArrayList<>();
        menuIds.forEach(menuId -> lists.add(new ProjectOrgRoleMenu(projectRoleQuery.getProjectId(), Long.valueOf(projectRoleQuery.getOrgId()), projectOrgRole.getId(), Long.valueOf(menuId),
                                            projectOrgRole.getTenantId(), projectOrgRole.getPlatformId()))
        );
        projectOrgRoleMenuMapper.batchSaveInsertProjectOrgRoleMenuList(lists);
        CustomResult customResult = new CustomResult();
        customResult.setData(projectOrgRole.getId().toString());
        return CommonResult.success(customResult);
    }

    @Override
    public CommonResult updateProjectOrgRole(ProjectRoleQuery projectRoleQuery) {
        if (!checkProjectRoleNameUnique(projectRoleQuery)){
            return CommonResult.failed(ProjectRoleResultEnums.B40006);
        }
        ProjectOrgRole projectOrgRole = projectOrgRoleMapper.selectByPrimaryKey(projectRoleQuery.getId());
        if (projectOrgRole!=null) {
            projectOrgRole.setRoleName(projectRoleQuery.getName());
            projectOrgRole.setEname(projectRoleQuery.getEnname());
            projectOrgRole.setProjectOrgId(projectRoleQuery.getProjectOrgId());
            if(StringUtils.isNotEmpty(projectRoleQuery.getProjectOrgCode())){
                projectOrgRole.setOrgCode(projectRoleQuery.getProjectOrgCode());
            }
            projectOrgRole.setDescription(projectRoleQuery.getDescription());
            projectOrgRole.setUpdateUserId(SecurityUtils.getUserId().toString());
            projectOrgRole.setUpdateTime(new Date());
            projectOrgRoleMapper.updateByPrimaryKeySelective(projectOrgRole);
            // 获取角色绑定的菜单。新增到中心角色表中
            projectOrgRoleMenuMapper.deleteByRoleId(projectOrgRole.getId());
            List<String> menuIds = projectRoleQuery.getMenuIds();
            if (CollectionUtil.isNotEmpty(menuIds)) {
                // 新增用户与角色管理
                List<ProjectOrgRoleMenu> lists = new ArrayList<>();
                menuIds.forEach(menuId ->
                        lists.add(new ProjectOrgRoleMenu(projectOrgRole.getId(), Long.valueOf(projectRoleQuery.getOrgId()), Long.valueOf(menuId), projectRoleQuery.getProjectId()
                                , projectOrgRole.getTenantId(), projectOrgRole.getPlatformId()))
                );
                projectOrgRoleMenuMapper.batchSaveInsertProjectOrgRoleMenuList(lists);
            }
            return CommonResult.success("");
        }
        return CommonResult.failed("更新失败");
    }

    @Override
    public CommonResult deleteProjectOrgRoleById(ProjectRoleQuery projectRoleQuery) {
        // 首先校验是否已经被使用。
        int count = projectOrgUserRoleMapper.countByRoleId(projectRoleQuery.getId());
        if (count>0){
            return CommonResult.failed(ProjectRoleResultEnums.D40002);
        }
        int i = projectOrgRoleMapper.deleteProjectOrgRoleById(projectRoleQuery.getId());
        if (i>0){
            // 删除角色和菜单之间的关系。
            projectOrgRoleMenuMapper.deleteByRoleId(projectRoleQuery.getId());
            return CommonResult.success(null);
        }else {
            return CommonResult.failed();
        }
    }

    @Override // TODO
    public ProjectRoleQuery getProjectOrgRoleAndMenuIdsByRoleId(Long roleId) {
        ProjectOrgRole role = projectOrgRoleMapper.selectByPrimaryKey(roleId);
        if (role!=null){
            ProjectRoleQuery query = new ProjectRoleQuery();
            BeanUtils.copyProperties(role,query);
            List<String> menuIds = projectOrgRoleMenuMapper.selectProjectOrgMenusListByRoleId(roleId);
            query.setMenuIds(menuIds);
            return query;
        }
        return null;
    }


    @Override
    public CustomResult<String> initProjectOrgTemplateRole(String projectId, String systemOrgId, String projectOrgId, String projectOrgCode, String projectRoleName, String createUserId) {
        CustomResult customResult = new CustomResult();
        String tenantId = SecurityUtils.getSystemTenantId();
        String platformId = SecurityUtils.getSystemPlatformId();
        // 获取项目的角色信息，并新增给所属中心
        ProjectRoleQuery projectRoleQuery1 = new ProjectRoleQuery();
        projectRoleQuery1.setProjectId(Long.parseLong(projectId));
        projectRoleQuery1.setName(projectRoleName);
        //projectRoleQuery1.setStatus(BusinessConfig.VALID_STATUS);
        List<ProjectRoleQuery> projectRoleQueryList = projectRoleMapper.selectProjectRoleListByQueryCondition(projectRoleQuery1);
        if (CollectionUtil.isEmpty(projectRoleQueryList)){
            throw new ServiceException(ResultCode.PROJECT_TEMPLATE_ROLE_NOT_FOUND);
        }
        if (CollectionUtil.isNotEmpty(projectRoleQueryList)){
            projectRoleQueryList.forEach(projectRoleQuery->{
                // 如果当前研究已存在角色信息 跳过写入记录
                ProjectOrgRole projectOrgRoleInfo = projectOrgRoleMapper.getProjectOrgRoleByProjectOrgIdAndRoleName(projectId, projectOrgId, projectRoleQuery.getName());
                if(projectOrgRoleInfo == null){
                    ProjectOrgRole projectOrgRole = new ProjectOrgRole();
                    projectOrgRole.setId(SnowflakeIdWorker.getUuid());
                    projectOrgRole.setProjectId(Long.parseLong(projectId));
                    projectOrgRole.setOrgId(Long.parseLong(systemOrgId));
                    projectOrgRole.setProjectOrgId(Long.parseLong(projectOrgId));
                    projectOrgRole.setOrgCode(projectOrgCode);
                    projectOrgRole.setResourceRoleId(projectRoleQuery.getId());
                    projectOrgRole.setRoleName(projectRoleQuery.getName());
                    projectOrgRole.setEname(projectRoleQuery.getEnname());
                    projectOrgRole.setStatus(BusinessConfig.VALID_STATUS);
                    projectOrgRole.setCreateTime(new Date());
                    projectOrgRole.setCreateUserId(createUserId);
                    projectOrgRole.setTenantId(StringUtils.isBlank(projectRoleQuery.getTenantId()) ? tenantId : projectRoleQuery.getTenantId());
                    projectOrgRole.setPlatformId(StringUtils.isBlank(projectRoleQuery.getPlatformId()) ? platformId : projectRoleQuery.getPlatformId());
                    projectOrgRoleMapper.insertSelective(projectOrgRole);
                    List<String> menuIds = projectRoleMenuMapper.selectProjectMenuIdsByProjectRoleId(projectRoleQuery.getId().toString());
                    if (CollectionUtil.isNotEmpty(menuIds)) {
                        //设置研究中心project_org_menu
                        for (String menuId : menuIds) {
                            ProjectOrgMenu projectOrgMenu = new ProjectOrgMenu();
                            projectOrgMenu.setProjectId(Long.parseLong(projectId));
                            projectOrgMenu.setOrgId(Long.parseLong(systemOrgId));
                            projectOrgMenu.setMenuId(Long.parseLong(menuId));
                            projectOrgMenuMapper.insertSelective(projectOrgMenu);
                        }
                        projectOrgRoleMenuMapper.batchSaveProjectOrgRoleMenuList(projectId, projectOrgRole.getId().toString(), projectOrgId, menuIds, tenantId, platformId);
                    }
                    customResult.setData(projectOrgRole.getId().toString());
                }else{
                    customResult.setData(projectOrgRoleInfo.getId().toString());
                }
            });
        }
        return customResult;
    }

    @Override
    public CommonResult updateProjectOrgRoleStatus(ProjectRoleQuery projectRoleQuery) {
        ProjectOrgRole role = projectOrgRoleMapper.selectByPrimaryKey(projectRoleQuery.getId());
        if (role!=null){
            role.setStatus(projectRoleQuery.getStatus());
            int update = projectOrgRoleMapper.updateByPrimaryKeySelective(role);
            if (update>0){
                if (BusinessConfig.VALID_STATUS.equals(projectRoleQuery.getStatus())){
                    return CommonResult.success(ProjectRoleResultEnums.Q40005);
                }else {
                    return CommonResult.success(ProjectRoleResultEnums.S40003);
                }
            }else {
                return CommonResult.failed();
            }
        }
        return CommonResult.failed();
    }

    @Override
    public void saveProjectUserOrgRole(String projectId, String userId, String projectOrgRoleId, Boolean ownerTotalAuth) {
        ProjectOrgUserRole projectOrgUserRoleInfo = projectOrgUserRoleMapper.getProjectOrgUserRoleByUserIdAndRoleId(projectId, userId, projectOrgRoleId);
        if(projectOrgUserRoleInfo == null){
            ProjectOrgUserRole projectOrgUserRole = new ProjectOrgUserRole();
            projectOrgUserRole.setProjectId(Long.parseLong(projectId));
            projectOrgUserRole.setUserId(Long.parseLong(userId));
            projectOrgUserRole.setRoleId(Long.parseLong(projectOrgRoleId));
            if(ownerTotalAuth != null){
                projectOrgUserRole.setOwnerTotalAuth(ownerTotalAuth);
            }
            projectOrgUserRole.setTenantId(SecurityUtils.getSystemTenantId());
            projectOrgUserRole.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectOrgUserRoleMapper.insertSelective(projectOrgUserRole);
        }
    }

    @Override
    public void deleteProjectOrgUserRoleByUserId(String projectId, String userId) {
        projectOrgUserRoleMapper.deleteProjectOrgUserRoleByUserId(projectId, userId);
    }

    @Override
    public List<ProjectUserOrgRoleVo> getProjectOrgUserRoleListByProjectIdAndUserId(String projectId, String userId) {
        return projectOrgUserRoleMapper.getProjectOrgUserRoleListByProjectIdAndUserId(projectId,userId);
    }

    @Override
    public List<ProjectOrgRole> getProjectOrgRoleByProjectOrgCode(Long projectId, Long orgId, String projectOrgCode) {
        return projectOrgRoleMapper.getProjectOrgRoleByProjectOrgCode(projectId, orgId, projectOrgCode);
    }

    @Override
    public List<ProjectOrgUserRoleVo> getProjectUserOrgRoleByOwnerTotalAuth(Long projectId) {
        return projectOrgUserRoleMapper.getProjectUserOrgRoleByOwnerTotalAuth(projectId);
    }

    @Override
    public List<ProjectOrgRoleVo> getProjectOrgRoleListForPage(Long projectId, Long projectOrgId, Integer status, Long id, String ename, Integer pageSize, Integer pageNum){
        PageHelper.startPage(pageNum, pageSize);
        ProjectOrgRoleExample example = new ProjectOrgRoleExample();
        ProjectOrgRoleExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andProjectOrgIdEqualTo(projectOrgId);
        if(status != null){
            criteria.andStatusEqualTo(String.valueOf(status));
        }
        if(id != null){
            criteria.andIdEqualTo(id);
        }
        if(ename != null && !"".equals(ename)){
            criteria.andEnameLike("%" + ename + "%");
        }
        List<ProjectOrgRole> roles = projectOrgRoleMapper.selectByExample(example);
        List<ProjectOrgRoleVo> dataList = new ArrayList<>();
        for(ProjectOrgRole pro : roles){
            ProjectOrgRoleVo projectOrgRoleVo = new ProjectOrgRoleVo();
            BeanUtils.copyProperties(pro, projectOrgRoleVo);
            dataList.add(projectOrgRoleVo);
        }
        return dataList;
    }

    /**
     * 获取角色下拉列表
     * @return
     */
    @Override
    public List<ProjectOrgRoleVo> getRoseList(Long projectId, Long projectOrgId){
        ProjectOrgRoleExample example = new ProjectOrgRoleExample();
        ProjectOrgRoleExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(projectId);
        criteria.andProjectOrgIdEqualTo(projectOrgId);
        List<ProjectOrgRole> roles = projectOrgRoleMapper.selectByExample(example);
        List<ProjectOrgRoleVo> dataList = new ArrayList<>();
        for(ProjectOrgRole pro : roles){
            ProjectOrgRoleVo projectOrgRoleVo = new ProjectOrgRoleVo();
            BeanUtils.copyProperties(pro, projectOrgRoleVo);
            dataList.add(projectOrgRoleVo);
        }
        return dataList;
    }

    /**
     * 添加角色和菜单权限
     * @param role
     * @return
     */
    @Override
    public CommonResult saveProjectOrgRole(ProjectOrgRole role, String menuIds){
        ProjectOrgRoleExample example = new ProjectOrgRoleExample();
        ProjectOrgRoleExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(role.getProjectId());
        criteria.andRoleNameEqualTo(role.getRoleName());
        List<ProjectOrgRole> projectOrgRoles = projectOrgRoleMapper.selectByExample(example);
        if(!projectOrgRoles.isEmpty()){
            return CommonResult.failed(ProjectRoleResultEnums.B40006.getCode(),"角色名称已存在");
        }

        ProjectOrgRoleExample exampleByEname = new ProjectOrgRoleExample();
        ProjectOrgRoleExample.Criteria criteriaByEname = exampleByEname.createCriteria();
        criteriaByEname.andProjectIdEqualTo(role.getProjectId());
        criteriaByEname.andEnameEqualTo(role.getEname());
        List<ProjectOrgRole> projectOrgRolesByEname = projectOrgRoleMapper.selectByExample(exampleByEname);
        if(!projectOrgRolesByEname.isEmpty()){
            return CommonResult.failed(ProjectRoleResultEnums.B40006.getCode(),"角色英文名称已存在");
        }


        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(String.valueOf(role.getProjectOrgId()));
        role.setId(SnowflakeIdWorker.getUuid());
        role.setOrgId(projectOrgInfo.getOrgId());
        role.setOrgCode(projectOrgInfo.getCode());
        role.setStatus(BusinessConfig.VALID_STATUS);
        role.setCreateTime(new Date());
        role.setTenantId(SecurityUtils.getSystemTenantId());
        role.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectOrgRoleMapper.insertSelective(role);
        String[] arr = menuIds.split(",");
        Long[] menuIdsArr = new Long[arr.length];
        for(int i = 0 ; i < arr.length ; i++){
            menuIdsArr[i] = Long.valueOf(arr[i]);
        }
        role.setMenuIds(menuIdsArr);
        insertRoleMenu(role);
        return CommonResult.success(null);
    }

    /**
     * 编辑/复制获取回显数据
     * @param id
     * @return
     */
    @Override
    public ProjectOrgRole getProjectOrgRoleBaseInfo(Long id){
        ProjectOrgRole projectOrgRole =projectOrgRoleMapper.selectByPrimaryKey(id);
        List<String> menuIdList = projectOrgRoleMenuMapper.selectProjectOrgMenusListByRoleId(id);
        Long[] arr = new Long[menuIdList.size()];
        for(int i = 0 ; i < menuIdList.size() ; i++){
            arr[i] = Long.valueOf(menuIdList.get(i));
        }
        projectOrgRole.setMenuIds(arr);
        return projectOrgRole;
    }

    /**
     * 编辑/复制
     * @param id
     * @param role
     * @return
     */
    public int updateProjectOrgRole(Long id, ProjectOrgRole role){
        role.setId(id);
        role.setTenantId(SecurityUtils.getSystemTenantId());
        role.setPlatformId(SecurityUtils.getSystemPlatformId());
        role.setUpdateTime(new Date());
        projectOrgRoleMapper.updateByPrimaryKeySelective(role);
        //先删除原有关系
        ProjectOrgRoleMenuExample exampleParam = new ProjectOrgRoleMenuExample();
        exampleParam.createCriteria().andRoleIdEqualTo(id);
        projectOrgRoleMenuMapper.deleteByExample(exampleParam);
        return insertRoleMenu(role);
    }

    /**
     * 删除角色
     * @param ids
     * @return
     */
    @Override
    public int deleteProjectOrgRole(List<Long> ids) {
        ProjectOrgRoleExample example = new ProjectOrgRoleExample();
        example.createCriteria().andIdIn(ids);
        int count = projectOrgRoleMapper.deleteByExample(example);
        //先删除原有关系
        ProjectOrgRoleMenuExample exampleParam = new ProjectOrgRoleMenuExample();
        exampleParam.createCriteria().andRoleIdIn(ids);
        projectOrgRoleMenuMapper.deleteByExample(exampleParam);
        return count;
    }
    
    @Override
    public ProjectOrgRole getProjectOrgRoleByOrgRoleCode(String projectId, String projectOrgId, String code, String loginTenantId, String loginPlatformId) {
        return projectOrgRoleMapper.getProjectOrgRoleByOrgRoleCode(projectId, projectOrgId, code, loginTenantId, loginPlatformId);
    }
    
    /**
     * 新增角色菜单信息
     */
    public int insertRoleMenu(ProjectOrgRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<ProjectOrgRoleMenu> list = new ArrayList<>();
        for (Long menuId : role.getMenuIds()) {
            ProjectOrgRoleMenu rm = new ProjectOrgRoleMenu();
            rm.setRoleId(role.getId());
            rm.setMenuId(menuId);
            rm.setProjectId(role.getProjectId());
            rm.setOrgId(role.getOrgId());
            rm.setTenantId(role.getTenantId());
            rm.setPlatformId(role.getPlatformId());
            list.add(rm);
        }
        if (list.size() > 0) {
            for(int i = 0 ; i < list.size() ; i++){
                rows = projectOrgRoleMenuMapper.insert(list.get(i));
                rows++;
            }
        }
        return list.size();
    }
}
