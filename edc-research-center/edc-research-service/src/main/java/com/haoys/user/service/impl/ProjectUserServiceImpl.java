package com.haoys.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.constants.EmailMsgConstants;
import com.haoys.user.common.core.domain.entity.BaseSystemUser;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.SendMessageUtil;
import com.haoys.user.common.util.NumberUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.dto.ProjectUserExportDto;
import com.haoys.user.domain.dto.ProjectUserOrgRoleVo;
import com.haoys.user.domain.dto.ProjectUserParam;
import com.haoys.user.domain.entity.ProjectRoleQuery;
import com.haoys.user.domain.enums.ProjectUserErrorEnums;
import com.haoys.user.domain.param.auth.ProjectRoleParam;
import com.haoys.user.domain.param.auth.ProjectUserAuthParam;
import com.haoys.user.domain.param.project.ProjectUserQueryParam;
import com.haoys.user.domain.param.system.SystemUserInfoParam;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import com.haoys.user.domain.vo.project.ProjectOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserOrgVo;
import com.haoys.user.domain.vo.project.ProjectUserVo;
import com.haoys.user.domain.wrapper.ProjectUserInfoWrapper;
import com.haoys.user.enums.ProjectRoleEnum;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.mapper.ProjectApplyUserMapper;
import com.haoys.user.mapper.ProjectUserInfoMapper;
import com.haoys.user.mapper.ProjectUserOrgMapper;
import com.haoys.user.mapper.SystemTenantUserMapper;
import com.haoys.user.mapper.SystemUserInfoMapper;
import com.haoys.user.model.Organization;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectApplyUser;
import com.haoys.user.model.ProjectApplyUserExample;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectOrgRole;
import com.haoys.user.model.ProjectUserInfo;
import com.haoys.user.model.ProjectUserInfoExample;
import com.haoys.user.model.SystemTenantUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.model.SystemUserRole;
import com.haoys.user.service.CreateTokenService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectOrgRoleService;
import com.haoys.user.service.ProjectRoleService;
import com.haoys.user.service.ProjectUserService;
import com.haoys.user.service.SystemMenuService;
import com.haoys.user.service.SystemRoleService;
import com.haoys.user.service.SystemTenantUserService;
import com.haoys.user.service.SystemUserInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class ProjectUserServiceImpl extends BaseService implements ProjectUserService {
    
    private final SendMessageUtil sendMessageUtil;
    private final SystemUserInfoMapper systemUserInfoMapper;
    private final ProjectApplyUserMapper projectApplyUserMapper;
    private final ProjectUserInfoMapper projectUserInfoMapper;
    private final ProjectUserOrgMapper projectUserOrgMapper;
    private final SystemMenuService systemMenuService;
    private final PasswordEncoder passwordEncoder;
    private final ProjectRoleService projectRoleService;
    private final ProjectOrgRoleService projectOrgRoleService;
    private final ProjectBaseManageService projectBaseManageService;
    private final SystemRoleService systemRoleService;
    private final SystemUserInfoService systemUserInfoService;
    private final OrganizationService organizationService;
    private final SystemTenantUserMapper systemTenantUserMapper;
    private final CreateTokenService createTokenService;
    private final SystemTenantUserService systemTenantUserService;

    @Value("${user.act-page}")
    private String actPage;

    @Override
    public List<ProjectUserVo> selectProjectUserListForPage(ProjectUserQueryParam projectUserQueryParam) {
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectUserQueryParam.getProjectId());
        String projectId = projectUserQueryParam.getProjectId();
        String accountName = projectUserQueryParam.getAccountName();
        String projectOrgId = projectUserQueryParam.getProjectOrgId();
        String activeStatus = projectUserQueryParam.getActiveStatus();
        String status = projectUserQueryParam.getStatus();
        Boolean lockStatus = projectUserQueryParam.getLockStatus();
        PageHelper.startPage(projectUserQueryParam.getPageNum(), projectUserQueryParam.getPageSize());

        String aseMobile="";
        if (StringUtils.isNotBlank(accountName)){
            // 手机号是加密的，所以需要把username 加密查是否有该手机号
            aseMobile =DesensitizeUtil.aesEncrypt(accountName);
        }
        List<ProjectUserVo> projectUserList = projectUserInfoMapper.selectProjectUserListForPage(projectId, accountName,aseMobile, projectOrgId, activeStatus, status, projectBaseInfo.getCreateUser(),lockStatus);
        for (ProjectUserVo projectUserVo : projectUserList) {
            ProjectUserVo projectUser = getProjectUserInfo(projectUserQueryParam.getProjectId(), projectUserVo.getId().toString());
            projectUserVo.setProjectOrgRoleList(projectUser.getProjectOrgRoleList());
            if(StringUtils.isNotEmpty(projectUserVo.getRoleCode())){
                if(projectBaseInfo.getCreateUser().equals(projectUserVo.getId().toString()) || projectUserVo.getRoleCode().contains(ProjectRoleEnum.PROJECT_PA.getCode())){
                    projectUserVo.setProjectCreateUser(true);
                }
            }
            if(StringUtils.isNotEmpty(projectUserVo.getMobile())){
                projectUserVo.setMobile(DesensitizeUtil.aesDecrypt(projectUserVo.getMobile()));
            }
        }
        return projectUserList;
    }

    @Override
    public List<ProjectUserVo> selectProjectUserList(String projectId, String orgId, String departmentId, String realName, String mobile, String roleIds, String projectLeader) {
        if(StringUtils.isNotEmpty(orgId)){
            orgId = getQueryWrapperParams(orgId);
        }
        String aseMobile="";
        if (StringUtils.isNotBlank(realName)){
            // 手机号是加密的，所以需要把username 加密查是否有该手机号
            aseMobile =DesensitizeUtil.aesEncrypt(realName);
        }
        List<ProjectUserVo> projectUserParams = projectUserInfoMapper.selectProjectUserListForPage(projectId, realName,aseMobile, orgId, "", "", "", null);
        return projectUserParams;
    }

    @Override
    public CustomResult saveProjectUserInfo(ProjectUserAuthParam projectUserAuthParam) {
        CustomResult customResult = new CustomResult();
        boolean defaultActiveStatus = false;
        boolean companyOwnerUser = false;
        String systemTenantId = SecurityUtils.getSystemTenantId();
        String systemPlatformId = SecurityUtils.getSystemPlatformId();
        if(StringUtils.isNotEmpty(projectUserAuthParam.getSystemUserId())){
            SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfo(Long.parseLong(projectUserAuthParam.getSystemUserId()));
            if(systemUserInfo == null){
                throw new ServiceException(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode()+"",ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
            }
            companyOwnerUser = systemUserInfo.getCompanyOwnerUser() != null && systemUserInfo.getCompanyOwnerUser();
            ProjectUserInfo projectUserInfo = this.getProjectUserInfoByUserId(projectUserAuthParam.getProjectId(), projectUserAuthParam.getSystemUserId());
            // 如果项目用户是未激活，需要判断是否是首次激活用户，如果不是邮箱中的连接跳转到登录页面，否则跳转到注册页面
            Long count = this.countProjectUserByUserId(projectUserAuthParam.getSystemUserId());
            if(projectUserInfo != null){
                defaultActiveStatus = projectUserInfo.getActiveStatus() != null && projectUserInfo.getActiveStatus();
                if(!defaultActiveStatus){
                    if (count>0){
                        sendMsg(systemUserInfo,projectUserAuthParam.getProjectId(),true);
                    }else {
                        sendMsg(systemUserInfo,projectUserAuthParam.getProjectId(),false);
                        customResult.setMessage(ResultCode.SUCCESS.getMessage());
                    }
                }
            }else {
                if (count>0){
                    sendMsg(systemUserInfo,projectUserAuthParam.getProjectId(),true);
                }else {
                    sendMsg(systemUserInfo,projectUserAuthParam.getProjectId(),false);
                    customResult.setMessage(ResultCode.SUCCESS.getMessage());
                }
            }
        }else{
            String accountName = Constants.USER_TYPE_VALUE_02.equals(projectUserAuthParam.getRegisterType()) ? projectUserAuthParam.getEmail() : projectUserAuthParam.getMobile();
            SystemUserInfo systemUserInfo = systemUserInfoService.getUserBaseInfoByUserName(accountName);
            if(systemUserInfo == null){
                //写入用户信息
                SystemUserInfoParam systemUserInfoParam = new SystemUserInfoParam();
                if(Constants.USER_TYPE_VALUE_01.equals(projectUserAuthParam.getRegisterType())){
                    systemUserInfoParam.setMobile(accountName);
                }
                if(Constants.USER_TYPE_VALUE_02.equals(projectUserAuthParam.getRegisterType())){
                    systemUserInfoParam.setEmail(accountName);
                }
                systemUserInfoParam.setUsername(accountName);
                systemUserInfoParam.setRegisterFrom(projectUserAuthParam.getRegisterType());
                systemUserInfoParam.setSealFlag(false);
                systemUserInfoParam.setStatus(BusinessConfig.ENABLED_STATUS);
                systemUserInfoParam.setCreateUserId(projectUserAuthParam.getCreateUserId());
                systemUserInfoParam.setRealName(projectUserAuthParam.getRealName());
                systemUserInfoParam.setDepartment(projectUserAuthParam.getDepartmentId());
                systemUserInfoParam.setUserType(projectUserAuthParam.getRegisterType());
                systemUserInfoParam.setCompanyOwnerUser(false);
                customResult = systemUserInfoService.saveSystemUserInfo(systemUserInfoParam);
                if(ResultCode.SUCCESS.getCode() == customResult.getCode()){
                    long userId = Long.parseLong(customResult.getData().toString());
                    systemUserInfoParam.setId(userId);
                    //绑定企业用户
                    SystemTenantUser systemTenantUser = new SystemTenantUser();
                    systemTenantUser.setId(SnowflakeIdWorker.getUuid());
                    systemTenantUser.setUserId(Long.valueOf(userId));
                    systemTenantUser.setCreateTime(new Date());
                    systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
                    systemTenantUser.setTenantId(systemTenantId);
                    systemTenantUser.setPlatformId(systemPlatformId);
                    systemTenantUser.setActiveStatus(false);
                    systemTenantUser.setCompanyOwnerUser(false);
                    systemTenantUser.setDataFrom(Constants.USER_LOGIN_RESOURCE_1);
                    systemTenantUserMapper.insert(systemTenantUser);
                    projectUserAuthParam.setSystemUserId(customResult.getData().toString());
                    // 发送短信或者邮箱
                    systemUserInfo= new SystemUserInfo();
                    BeanUtils.copyProperties(systemUserInfoParam,systemUserInfo);
                    sendMsg(systemUserInfo,projectUserAuthParam.getProjectId(),false);
                }
                customResult.setMessage(ResultCode.SUCCESS.getMessage());
            }else{
                projectUserAuthParam.setSystemUserId(systemUserInfo.getId().toString());
                // 已经存在，需要判断是否已经激活，
                SystemUserInfo userBaseInfo = systemUserInfoService.getUserBaseInfo(systemUserInfo.getId());
                if (userBaseInfo!=null){
                    SystemTenantUser systemTenantUser = systemTenantUserService.getSystemTenantUserByUserId(userBaseInfo.getId().toString());
                    companyOwnerUser = systemTenantUser.getCompanyOwnerUser() != null && systemTenantUser.getCompanyOwnerUser();
                    ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectUserAuthParam.getProjectId(), systemUserInfo.getId().toString());
                    if (!projectUserInfo.getActiveStatus()){
                    // 需要查出用户的信息（同登录信息），前端可以拿着token直接进入系统或者激活
                    sendMsg(userBaseInfo,projectUserAuthParam.getProjectId(),false);
                    }
                }
            }
        }


        // 项目PA 新增同步项目角色
        String projectId = projectUserAuthParam.getProjectId();
        String systemUserId = projectUserAuthParam.getSystemUserId();
        if(projectUserAuthParam.getSystemUserId().equals(projectUserAuthParam.getCreateUserId())){
            projectRoleService.deleteProjectUserRole(projectId, systemUserId);
            List<ProjectRoleParam> projectRoleList = projectUserAuthParam.getProjectRoleList();
            for (ProjectRoleParam projectRole : projectRoleList) {
                //ProjectUserRoleVo projectUseRoleVo = projectRoleService.getProjectRoleListByProjectIdAndUserId(projectId, systemUserId, projectRole.getRoleId());
                projectRoleService.insertProjectUserRole(projectId, systemUserId, projectRole.getRoleId(), systemTenantId, systemPlatformId);
            }
        }else{
            //设置研究中心角色
            deleteProjectUser(projectUserAuthParam.getProjectId(), projectUserAuthParam.getSystemUserId(), true);
            saveProjectUserInfo(projectUserAuthParam.getProjectId(), projectUserAuthParam.getSystemUserId(), defaultActiveStatus, companyOwnerUser);
            List<ProjectUserAuthParam.ProjectOrgRoleInfo> projectOrgRoleList = projectUserAuthParam.getProjectOrgRoleList();
            for (ProjectUserAuthParam.ProjectOrgRoleInfo projectOrgRoleInfo : projectOrgRoleList) {
                List<ProjectUserAuthParam.ProjectOrgInfo> projectOrgList = projectOrgRoleInfo.getProjectOrgList();
                for (ProjectUserAuthParam.ProjectOrgInfo projectOrgInfo : projectOrgList) {
                    //查询项目研究中心是否完成写入
                    String projectOrgRoleId;
                    ProjectOrgRole projectOrgRole = projectOrgRoleService.getProjectOrgRoleByProjectOrgIdAndRoleName(projectUserAuthParam.getProjectId(), projectOrgInfo.getProjectOrgCode(), projectOrgRoleInfo.getRoleName());
                    if(projectOrgRole == null){
                        CustomResult result = projectOrgRoleService.initProjectOrgTemplateRole(projectUserAuthParam.getProjectId(), projectOrgInfo.getOrgId().toString(), projectOrgInfo.getProjectOrgId().toString(), projectOrgInfo.getProjectOrgCode(), projectOrgRoleInfo.getRoleName(), projectUserAuthParam.getCreateUserId());
                        projectOrgRoleId = result.getData().toString();
                    }else{
                        projectOrgRoleId = projectOrgRole.getId().toString();
                    }
                    //保存项目成员记录(研究中心和研究中心角色)
                    organizationService.saveProjectUserOrgInfo(projectUserAuthParam.getProjectId(), projectUserAuthParam.getSystemUserId(), projectOrgInfo.getOrgId().toString(), projectOrgInfo.getProjectOrgCode());
                    projectOrgRoleService.saveProjectUserOrgRole(projectUserAuthParam.getProjectId(), projectUserAuthParam.getSystemUserId(), projectOrgRoleId, projectOrgRoleInfo.getOwnerTotalAuth());
                    log.info("projectId:{} systemUserId:{} projectOrgRoleId:{}", projectUserAuthParam.getProjectId(), projectUserAuthParam.getSystemUserId(), projectOrgRoleId);
                }
            }
        }
        return customResult;
    }

    /**
     * 获取菜单数据权限
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(BaseSystemUser user) {
        Set<String> perms = new HashSet<>();
        if(user.isAdmin()){
            perms.add("*:*:*");
        }else{
            perms.addAll(systemMenuService.selectMenuPermsByUserId(user.getId()));
        }
        return perms;
    }

    public void saveProjectUserInfo(String projectId, String systemUserId, boolean activeStatus, boolean companyOwnerUser) {
        ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectId, systemUserId);
        if(projectUserInfo == null){
            ProjectUserInfo projectUserInfoParam = new ProjectUserInfo();
            projectUserInfoParam.setId(SnowflakeIdWorker.getUuid());
            projectUserInfoParam.setProjectId(Long.parseLong(projectId));
            projectUserInfoParam.setUserId(Long.parseLong(systemUserId));
            projectUserInfoParam.setStatus(BusinessConfig.VALID_STATUS);
            projectUserInfoParam.setActiveStatus(activeStatus);
            projectUserInfoParam.setCreateTime(new Date());
            projectUserInfoParam.setTenantId(SecurityUtils.getSystemTenantId());
            projectUserInfoParam.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectUserInfoParam.setPaRole(companyOwnerUser);
            projectUserInfoMapper.insertSelective(projectUserInfoParam);
        }
    }


    /**
     * 发送邀请激活邮件和短信
     * @param projectId 项目id
     * @param active 用户是否已经激活状态，激活状态直接发送登录连接邮箱
     */
    public void sendMsg(SystemUserInfo user,String projectId,Boolean active){
        try{
            // 发送短信或者邮箱
            Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
            if (Constants.USER_TYPE_VALUE_01.equals(user.getUserType())){
                // 手机号邀请
                sendMessageUtil.sendInviteMessage(user.getUsername(), projectBaseInfo.getName());

            } else if (Constants.USER_TYPE_VALUE_02.equals(user.getUserType())) {
                if (active){
                    // 邮箱邀请
                    String path = actPage + "login?source=email";
                    String contents = EmailMsgConstants.CONTENT.replace("####", projectBaseInfo.getName()).replace("@@@@", path);
                    CommonResult commonResult = sendMessageUtil.sendMail(user.getEmail(), contents);
                    AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertSendMessageRecord(user.getEmail(), "", commonResult.getData().toString(), projectBaseInfo.getName()),500);
                }else {
                    String tokenValue = createTokenService.createToken(user);
                    // 邮箱邀请
                    String path = actPage + "register?" + "pd=" + projectId + "&token=" + tokenValue;
                    String contents = EmailMsgConstants.CONTENT.replace("####", "【"+projectBaseInfo.getName()+"】").replace("@@@@", path);
                    CommonResult commonResult = sendMessageUtil.sendMail(user.getEmail(), contents);
                    AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.insertSendMessageRecord(user.getEmail(), "", commonResult.getData().toString(), projectBaseInfo.getName()),500);
                }
            }
        }catch (Exception e){}
    }

    @Override
    public String deleteProjectUser(String projectId, String userId, boolean forceDelete) {
        // 针对项目管理员进行判断
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if(!forceDelete && projectBaseInfo.getCreateUser().equals(userId)){
            throw new ServiceException(ResultCode.PROJECT_MANAGE_USER_NOT_UPDATE.getMessage());
        }

        String message = projectBaseManageService.deleteProjectUser(projectId, userId, "");
        if(!BusinessConfig.RETURN_MESSAGE_DEFAULT.equals(message)){
            throw new ServiceException(message);
        }
        projectUserInfoMapper.deleteProjectUserInfo(projectId, userId);
        
        

        if(!forceDelete){
            // 查询是否取得项目授权
            Set<String> realNameSet = new HashSet<>();
            List<ProjectUserOrgRoleVo> projectOrgUserRoleList = projectOrgRoleService.getProjectOrgUserRoleListByProjectIdAndUserId(projectId, userId);
            if(CollectionUtil.isNotEmpty(projectOrgUserRoleList)){
                projectOrgUserRoleList.forEach(projectUserOrgRole->{
                    SystemUserInfo userBaseInfo = systemUserInfoService.getUserBaseInfo(projectUserOrgRole.getUserId());
                    if(userBaseInfo != null){
                        realNameSet.add(userBaseInfo.getRealName());
                    }
                });
                throw new ServiceException(ResultCode.BUSINESS_DELETE_SYSTEM_USER_MESSAGE.getCode()+"", ResultCode.BUSINESS_DELETE_SYSTEM_USER_MESSAGE.getMessage().replace("${realName}", realNameSet.toString()));
            }
        }
        projectOrgRoleService.deleteProjectOrgUserRoleByUserId(projectId, userId);
        return message;
    }

    @Override
    public CustomResult updateProjectUserInfo(ProjectUserParam projectUserParam) {
        CustomResult customResult = new CustomResult();
        BaseSystemUser sysUser = new BaseSystemUser();
        sysUser.setId(projectUserParam.getId());
        sysUser.setMobile(projectUserParam.getMobile());
        String info = systemUserInfoService.checkPhoneUnique(sysUser);
        if(BusinessConfig.RETURN_MESSAGE_FAIL.equals(info)){
            throw new ServiceException(ProjectUserErrorEnums.E10301.getCode()+"",ProjectUserErrorEnums.E10301.getMessage());
        }
        //删除项目用户角色信息
        //projectUserRoleMapper.deleteUserRoleByProjectIdUserId(Long.parseLong(projectUserParam.getProjectId()), projectUserParam.getId());
        //新增项目用户角色信息
        //insertProjectUserRole(Long.parseLong(projectUserParam.getProjectId()), projectUserParam.getId(), Long.parseLong(projectUserParam.getRoleId()));
        //设置系统关联机构信息
        organizationService.saveSystemUserOrgInfo(projectUserParam.getId().toString(), projectUserParam.getOrgId());

        updateProjectUserInfo(projectUserParam);
        return customResult;
    }

    @Override
    public Integer updateProjectUserInfoById(ProjectUserInfo projectUserInfo) {
        return projectUserInfoMapper.updateByPrimaryKey(projectUserInfo);
    }

    @Override
    public String saveBatchProjectUser(List<ProjectUserParam> projectUserParam, Long projectId, List<ProjectUserExportDto> errorList) {
        if (StringUtils.isNull(projectUserParam) || projectUserParam.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        return null;
    }


    public void insertSystemUserInfo(ProjectUserParam projectUserParam){
        SystemUserInfo sysUser = new SystemUserInfo();
        BeanUtils.copyProperties(projectUserParam, sysUser);
        sysUser.setUsername(projectUserParam.getMobile()+"_"+NumberUtils.getHysNumber(System.currentTimeMillis()));
        sysUser.setRealName(projectUserParam.getRealName());
        sysUser.setUserType(projectUserParam.getUserType());
        sysUser.setCreateUser(SecurityUtils.getUserName());
        sysUser.setStatus(1);
        sysUser.setSealFlag(false);
        //将密码进行加密操作
        String encodePassword = passwordEncoder.encode(Constants.USER_DEFAULT_LOGIN_PASSWORD);
        sysUser.setPassword(encodePassword);
        systemUserInfoMapper.insert(sysUser);
        insertUserRole(sysUser);
    }

    /**
     * 新增用户角色信息
     * @param user 用户对象
     */
    public void insertUserRole(SystemUserInfo user) {
        // 新增用户与角色管理
        List<SystemUserRole> list = new ArrayList<>();
        SystemUserRole ur = new SystemUserRole();
        ur.setUserId(user.getId());
        // 设置系统默认角色ID为1
        ur.setRoleId(1L);
        list.add(ur);
        systemRoleService.batchSaveUserRole(list);

    }

    public String updateProjectUser(ProjectUserParam projectUserParam){
        SystemUserInfo systemUserInfo = new SystemUserInfo();
        if(StringUtils.isBlank(projectUserParam.getOrgId())){
            throw new ServiceException(ProjectUserErrorEnums.E10304.getCode()+"", ProjectUserErrorEnums.E10304.getMessage());
        }
        BeanUtils.copyProperties(projectUserParam, systemUserInfo);
        systemUserInfo.setUsername(null);
        systemUserInfo.setRealName(projectUserParam.getRealName());
        systemUserInfo.setUpdateUser(SecurityUtils.getUserName());
        systemUserInfo.setUpdateTime(new Date());
        systemUserInfoMapper.updateByPrimaryKeySelective(systemUserInfo);
        return null;
    }


    public String insertProjectApplyUser(ProjectUserParam projectUserParam){
        return projectBaseManageService.saveProjectUser(String.valueOf(projectUserParam.getProjectId()), String.valueOf(projectUserParam.getOrgId()), String.valueOf(projectUserParam.getId()));
    }

    @Override
    public String saveProjectManageUserInfo(String projectId, String userId, String tenantId, String platformId){
        ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectId, userId);
        if(projectUserInfo == null){
            ProjectUserInfo projectUser = new ProjectUserInfo();
            projectUser.setId(SnowflakeIdWorker.getUuid());
            projectUser.setProjectId(Long.parseLong(projectId));
            projectUser.setUserId(Long.parseLong(userId));
            projectUser.setStatus(BusinessConfig.VALID_STATUS);
            projectUser.setActiveStatus(true);
            projectUser.setRejectStatus(false);
            projectUser.setPaRole(true);
            projectUser.setCreateTime(new Date());
            projectUser.setTenantId(tenantId);
            projectUser.setPlatformId(platformId);
            projectUserInfoMapper.insertSelective(projectUser);
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    public String getProjectLeader(String projectId, String userId){
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();
        criteria.andProjectIdEqualTo(Long.parseLong(projectId));
        criteria.andUserIdEqualTo(userId);
        criteria.andProjectLeaderEqualTo(true);
        List<ProjectApplyUser> projectApplyUserList = projectApplyUserMapper.selectByExample(example);
        if(projectApplyUserList != null || projectApplyUserList.size() >0){
            return BusinessConfig.RETURN_MESSAGE_PROJECT_ORG_03;
        }
        return BusinessConfig.RETURN_MESSAGE_DEFAULT;
    }

    @Override
    public ProjectUserInfoWrapper getProjectUserRoleInfoByUserId(String projectId, String orgId, String userId) {
        ProjectUserInfoWrapper projectUserInfoWrapper = new ProjectUserInfoWrapper();
        List<ProjectUserInfoWrapper.ProjectOrgRoleInfo> projectOrgRoleList = new ArrayList<>();
        projectUserInfoWrapper.setProjectId(projectId);
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if(projectBaseInfo.getCreateUser().equals(userId)){
            List<ProjectRoleVo> roleList = projectRoleService.getProjectRoleListByUserId(projectId, userId);
            roleList.forEach(projectRoleVo -> {
                ProjectUserInfoWrapper.ProjectOrgRoleInfo projectOrgRoleInfo = new ProjectUserInfoWrapper.ProjectOrgRoleInfo();
                projectOrgRoleInfo.setRoleId(projectRoleVo.getRoleId());
                projectOrgRoleInfo.setRoleName(projectRoleVo.getOrgRoleName());
                projectOrgRoleInfo.setRoleCode(projectRoleVo.getEnname());
                projectOrgRoleList.add(projectOrgRoleInfo);
            });
            projectUserInfoWrapper.setProjectOrgRoleList(projectOrgRoleList);
        }else{
            List<ProjectRoleVo> orgRoleList = projectRoleService.getProjectOrgRoleListByUserId(projectId, orgId, userId);
            orgRoleList.forEach(projectRoleVo -> {
                ProjectUserInfoWrapper.ProjectOrgRoleInfo projectOrgRoleInfo = new ProjectUserInfoWrapper.ProjectOrgRoleInfo();
                projectOrgRoleInfo.setRoleId(projectRoleVo.getRoleId());
                projectOrgRoleInfo.setRoleName(projectRoleVo.getOrgRoleName());
                projectOrgRoleInfo.setRoleCode(projectRoleVo.getEnname());
                projectOrgRoleList.add(projectOrgRoleInfo);
            });
        }
        projectUserInfoWrapper.setProjectOrgRoleList(projectOrgRoleList);
        return projectUserInfoWrapper;
    }

    @Override
    public String deleteProjectUserByRoleCode(String projectId, String userId, String roleCode) {
        return null;
    }

    @Override
    public List<ProjectApplyUser> getProjectUserResultByUserId(String userId) {
        ProjectApplyUserExample example = new ProjectApplyUserExample();
        ProjectApplyUserExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(userId);
        criteria.andTenantIdEqualTo(SecurityUtils.getSystemTenantId());
        criteria.andPlatformIdEqualTo(SecurityUtils.getSystemPlatformId());
        List<ProjectApplyUser> projectApplyUserList = projectApplyUserMapper.selectByExample(example);
        return projectApplyUserList;
    }

    @Override
    public ProjectUserVo getProjectUserInfo(String projectId, String userId) {

        ProjectUserVo projectUserVo = new ProjectUserVo();
        // 根据用户id 获取用户信息
        SystemUserInfo userBaseInfo = systemUserInfoService.getUserBaseInfo(Long.parseLong(userId));
        if (userBaseInfo!=null){
            // 设置对应的属性值
            projectUserVo.setId(Long.parseLong(userId));
            projectUserVo.setProjectId(Long.parseLong(projectId));
            projectUserVo.setEmail(userBaseInfo.getEmail());
            projectUserVo.setMobile(userBaseInfo.getMobile());
            projectUserVo.setUsername(userBaseInfo.getUsername());
            projectUserVo.setRealName(userBaseInfo.getRealName());
            projectUserVo.setDepartmentId(userBaseInfo.getDepartment());
            projectUserVo.setRegisterType(userBaseInfo.getUserType());

            if(StringUtils.isNotEmpty(userBaseInfo.getDepartment())){
                Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(userBaseInfo.getDepartment());
                if(systemOrganizationInfo != null){
                    projectUserVo.setDepartmentName(systemOrganizationInfo.getName());
                }
            }

            if(SecurityUtils.getUserIdValue().equals(userId)){
                projectUserVo.setProjectCreateUser(true);
            }

            ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectId, userId);
            projectUserVo.setPaRole(projectUserInfo!= null && projectUserInfo.getPaRole());
            List<ProjectRoleVo> projectRoleList = projectRoleService.getProjectRoleListByUserId(projectId, userId);
            for (ProjectRoleVo projectRoleVo : projectRoleList) {
                if(projectRoleVo.getEnname().equals(ProjectRoleEnum.PROJECT_PA.getCode())){
                    projectRoleVo.setDefaultProjectRole(true);
                }
            }
            projectUserVo.setProjectRoleList(projectRoleList);

            // 根据用户id和项目id获取绑定的中心和角色
            List<ProjectUserOrgRoleVo> projectUserOrgRoleVoList = projectOrgRoleService.getProjectOrgUserRoleListByProjectIdAndUserId(projectId, userId);
            if (CollectionUtil.isNotEmpty(projectUserOrgRoleVoList)){
                // 根据项目角色id进行分组
                Map<Long, List<ProjectUserOrgRoleVo>> roleMap = projectUserOrgRoleVoList.stream().collect(Collectors.groupingBy(ProjectUserOrgRoleVo::getRoleId));
                if (roleMap.size()>0){
                    List<ProjectUserVo.ProjectOrgRoleInfo> roleInfoList = new ArrayList<>();
                    roleMap.forEach((roleId,userOrgRoleVoList)->{
                        // 设置角色信息
                        ProjectUserVo.ProjectOrgRoleInfo projectOrgRoleInfo = new ProjectUserVo.ProjectOrgRoleInfo();
                        projectOrgRoleInfo.setRoleId(roleId);
                        ProjectRoleQuery projectRoleQuery = projectRoleService.getProjectRoleByRoleId(roleId);
                        projectOrgRoleInfo.setRoleName(projectRoleQuery.getName());
                        // 设置中心信息
                        if (CollectionUtil.isNotEmpty(userOrgRoleVoList)){
                            List<ProjectUserVo.ProjectOrgInfo> projectOrgInfoList = new ArrayList<>();
                            userOrgRoleVoList.forEach(projectOrgInfoVo ->{
                                projectOrgRoleInfo.setOwnerTotalAuth(projectOrgInfoVo.getOwnerTotalAuth());
                                ProjectUserVo.ProjectOrgInfo projectOrgInfo = new ProjectUserVo.ProjectOrgInfo();
                                projectOrgInfo.setOrgId(projectOrgInfoVo.getOrgId());
                                projectOrgInfo.setProjectOrgId(projectOrgInfoVo.getProjectOrgId());
                                projectOrgInfo.setProjectOrgCode(projectOrgInfoVo.getOrgCode());
                                projectOrgInfo.setOrgName(projectOrgInfoVo.getOrgName());
                                projectOrgInfoList.add(projectOrgInfo);
                            });
                            projectOrgRoleInfo.setProjectOrgList(projectOrgInfoList);
                        }
                        String projectOrgIds = projectUserVo.getProjectOrgIds();
                        if(BusinessConfig.DEFAULT_INPUT_VALUE_STRING.equals(projectOrgIds)){
                            projectOrgRoleInfo.setShowAllOwnerOrgName(true);
                        }
                        roleInfoList.add(projectOrgRoleInfo);
                    });
                    projectUserVo.setProjectOrgRoleList(roleInfoList);
                }
            }
        }

        return projectUserVo;
    }

    @Override
    public ProjectUserInfo getProjectUserInfoByUserId(String projectId, String userId) {
        ProjectUserInfo projectUserInfo = projectUserInfoMapper.getProjectUserInfo(projectId, userId);
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if(projectBaseInfo.getCreateUser().equalsIgnoreCase(userId)){
            projectUserInfo.setProjectCreateUser(true);
        }
        return projectUserInfo;
    }


    /**
     * 判断在其他的项目中是否已经存在已经激活的信息
     * @param userId
     * @return
     */
    private Long countProjectUserByUserId(String userId) {
        ProjectUserInfoExample example = new ProjectUserInfoExample();
        ProjectUserInfoExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(Long.parseLong(userId));
        criteria.andActiveStatusEqualTo(true);
        return projectUserInfoMapper.countByExample(example);
    }

    @Override
    public int updateStatus(String projectId, String userId) {
        // 更新project_user_info 表中的数据
        ProjectUserInfo projectUserInfo = getProjectUserInfoByUserId(projectId, userId);
        if (projectUserInfo != null){
            projectUserInfo.setActiveStatus(true);
            projectUserInfo.setStatus(BusinessConfig.VALID_STATUS);
            int i = projectUserInfoMapper.updateByPrimaryKey(projectUserInfo);
            return i;
        }
        return 0;
    }


    @Override
    public CommonResult joinProject(String projectId) {
        ProjectUserInfo userInfo = this.getProjectUserInfoByUserId(projectId, SecurityUtils.getUserIdValue());
        if (userInfo!=null){
            userInfo.setActiveStatus(true);
            projectUserInfoMapper.updateByPrimaryKey(userInfo);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    @Override
    public CommonResult refuseJoinProject(String projectId) {
        ProjectUserInfo userInfo = this.getProjectUserInfoByUserId(projectId, SecurityUtils.getUserIdValue());
        if (userInfo!=null){
            userInfo.setRejectStatus(true);
            projectUserInfoMapper.updateByPrimaryKey(userInfo);
            return CommonResult.success(null);
        }
        return CommonResult.failed();
    }

    @Override
    public CommonPage<ProjectUserVo> list(ProjectUserQueryParam projectUserQueryParam) {
        Page<Object> page = PageHelper.startPage(projectUserQueryParam.getPageNum(), projectUserQueryParam.getPageSize());
        List<ProjectUserVo> projectUserList = projectUserInfoMapper.selectProjectUserListAndUserId(projectUserQueryParam);
        for (ProjectUserVo projectUserVo : projectUserList) {
            ProjectUserVo projectUser = getProjectUserInfo(projectUserVo.getProjectId().toString(), projectUserVo.getId().toString());
            projectUserVo.setProjectOrgRoleList(projectUser.getProjectOrgRoleList());
            if(StringUtils.isNotBlank(projectUserVo.getMobile())){
                projectUserVo.setMobile(DesensitizeUtil.secretMobile(DesensitizeUtil.aesDecrypt(projectUserVo.getMobile())));
            }
        }
        return commonPageListWrapper(projectUserQueryParam.getPageNum(), projectUserQueryParam.getPageSize(), page, projectUserList);
    }

    @Override
    public List<ProjectUserInfo> getProjectUserListByUserId(String systemUserId) {
        return projectUserInfoMapper.getProjectUserListByUserId(systemUserId);
    }

    @Override
    public Map<String, Object> getProjectRoleListAndOrgList(List<Long> projectIds) {
        Map<String, Object> dataMap = new HashMap<>();
        Map<String, List<ProjectRoleVo>> projectRoleMap = new HashMap<>();
        Map<String, List<ProjectOrgVo>> projectOrgMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(projectIds)){
            for (Long projectId : projectIds) {
                ProjectRoleQuery projectRoleQuery = new ProjectRoleQuery();
                projectRoleQuery.setProjectId(projectId);
                List<ProjectRoleVo> projectRoleList = projectRoleService.getProjectRoleListForCombobox(projectRoleQuery);
                projectRoleMap.put(projectId.toString(), projectRoleList);
                List<ProjectOrgVo> projectOrgList = organizationService.getProjectOrgListByCondition(projectId.toString(), null, null);
                projectOrgMap.put(projectId.toString(), projectOrgList);
            }
        }
        dataMap.put("projectRoleMap", projectRoleMap);
        dataMap.put("projectOrgMap", projectOrgMap);
        return dataMap;
    }
    
    @Override
    public List<ProjectUserOrgVo> getProjectUserJoinOrgListByUserId(String projectId, String userId) {
        return projectUserOrgMapper.getProjectUserJoinOrgListByUserId(projectId, userId);
    }
    
    @Override
    public void saveAuthorizeProjectUser(String projectId, String userId, String projectOrgId, String loginTenantId, String loginPlatformId) {
        deleteProjectUser(projectId, userId, true);
        saveProjectUserInfo(projectId, userId, false, false);
        ProjectOrgRole projectOrgRoleInfo = projectOrgRoleService.getProjectOrgRoleByOrgRoleCode(projectId, projectOrgId, ProjectRoleEnum.PROJECT_SUB_PI.getCode(), loginTenantId, loginPlatformId);
        String projectOrgRoleId;
        ProjectOrgRole projectOrgRole = projectOrgRoleService.getProjectOrgRoleByProjectOrgIdAndRoleName(projectId, projectOrgId, projectOrgRoleInfo.getRoleName());
        ProjectOrgInfo projectOrgInfo = organizationService.getProjectOrgInfo(projectOrgId);
        Organization systemOrganizationInfo = organizationService.getSystemOrganizationInfo(projectOrgInfo.getOrgId().toString());
        if(projectOrgRole == null){
            CustomResult result = projectOrgRoleService.initProjectOrgTemplateRole(projectId, systemOrganizationInfo.getId().toString(), projectOrgId, projectOrgInfo.getCode(), projectOrgRoleInfo.getRoleName(), SecurityUtils.getUserIdValue());
            projectOrgRoleId = result.getData().toString();
        }else{
            projectOrgRoleId = projectOrgRole.getId().toString();
        }
        //保存项目成员记录(研究中心和研究中心角色)
        organizationService.saveProjectUserOrgInfo(projectId, userId, systemOrganizationInfo.getId().toString(), projectOrgInfo.getCode());
        projectOrgRoleService.saveProjectUserOrgRole(projectId, userId, projectOrgRoleId, true);
    }
}
