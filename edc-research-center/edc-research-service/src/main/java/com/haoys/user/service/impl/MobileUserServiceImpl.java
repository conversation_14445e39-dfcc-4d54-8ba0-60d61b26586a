package com.haoys.user.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.constants.Constants;
import com.haoys.user.common.util.DesensitizeUtil;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.domain.enums.SystemUserReturnEnums;
import com.haoys.user.domain.param.mobile.MobileUserRegisterParam;
import com.haoys.user.domain.param.project.ProjectTesteeParam;
import com.haoys.user.domain.vo.project.ProjectConfigVo;
import com.haoys.user.domain.vo.system.SystemUserInfoVo;
import com.haoys.user.enums.ProjectTesteeEnum;
import com.haoys.user.mapper.SystemTenantUserMapper;
import com.haoys.user.model.Project;
import com.haoys.user.model.ProjectOrgInfo;
import com.haoys.user.model.ProjectTesteeInfo;
import com.haoys.user.model.ProjectVisitUser;
import com.haoys.user.model.SystemUserInfo;
import com.haoys.user.service.MobileUserService;
import com.haoys.user.service.OrganizationService;
import com.haoys.user.service.ProjectBaseManageService;
import com.haoys.user.service.ProjectConfigService;
import com.haoys.user.service.ProjectTesteeInfoService;
import com.haoys.user.service.ProjectVisitUserService;
import com.haoys.user.service.SystemUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class MobileUserServiceImpl implements MobileUserService {
    @Autowired
    private ProjectTesteeInfoService projectTesteeInfoService;
    @Autowired
    private SystemUserInfoService systemUserInfoService;
    @Autowired
    private ProjectTesteeInfoService testeeInfoService;
    @Autowired
    private SystemTenantUserMapper systemTenantUserMapper;
    @Autowired
    private ProjectBaseManageService projectBaseManageService;
    @Autowired
    private ProjectConfigService projectConfigService;
    @Autowired
    private ProjectVisitUserService projectVisitUserService;
    @Autowired
    private OrganizationService organizationService;

    @Override
    public CommonResult registerMobileUserInfo(MobileUserRegisterParam mobileUserRegisterParam) {
        // 校验用户名是否已经存在
        SystemUserInfoVo userInfo2 = systemUserInfoService.getSystemUserInfoByAccountName(mobileUserRegisterParam.getMobilePhone());
        if (userInfo2!=null){
            return CommonResult.failed(SystemUserReturnEnums.E20015);
        }
        SystemUserInfo userInfo = systemUserInfoService.getUserBaseInfoByUserName(mobileUserRegisterParam.getUsername());
        if (userInfo!=null){
            return CommonResult.failed(SystemUserReturnEnums.E20017);
        }
        String aseMobile = DesensitizeUtil.aesEncrypt(mobileUserRegisterParam.getMobilePhone());

        // 创建用户表信息
        SystemUserInfo systemUserInfo = new SystemUserInfo();
        Long userId = SnowflakeIdWorker.getUuid();
        systemUserInfo.setId(userId);
        systemUserInfo.setMobile(aseMobile);
        systemUserInfo.setRealName(mobileUserRegisterParam.getRealName());
        systemUserInfo.setUsername(mobileUserRegisterParam.getUsername());
        systemUserInfo.setLoginCode(DesensitizeUtil.aesEncrypt(mobileUserRegisterParam.getPassword()));
        // 临时注释BCrypt加密，使用简单加密替代
        systemUserInfo.setPassword(DesensitizeUtil.aesEncrypt(mobileUserRegisterParam.getPassword()));
        systemUserInfo.setUserType(Constants.USER_TYPE_VALUE_01);
        systemUserInfo.setRegisterFrom(Constants.USER_TYPE_VALUE_04);
        systemUserInfo.setActiveStatus(true);
        systemUserInfo.setCreateTime(new Date());
        systemUserInfo.setStatus(BusinessConfig.ENABLED_STATUS);
        systemUserInfo.setSealFlag(false);
        // 保存用户表
        int i = systemUserInfoService.insertSystemUser(systemUserInfo);
        if (i>0){
            // 创建参与者主表信息
            ProjectTesteeInfo testeeInfo = new ProjectTesteeInfo();
            testeeInfo.setId(SnowflakeIdWorker.getUuid());
            testeeInfo.setUserId(userId);
            testeeInfo.setRealName(mobileUserRegisterParam.getRealName());
            testeeInfo.setStatus(BusinessConfig.ENABLED_STATUS.toString());
            testeeInfo.setBirthday(mobileUserRegisterParam.getBirthday());
            testeeInfo.setCreateTime(new Date());
            testeeInfo.setCreateUser(userId.toString());
            testeeInfo.setGender(mobileUserRegisterParam.getSex());
            testeeInfoService.saveProjectTesteeInfo(testeeInfo);

            //租户和用户直接的关联表，没有租户默认为0
//            SystemTenantUser systemTenantUser = new SystemTenantUser();
//            systemTenantUser.setId(SnowflakeIdWorker.getUuid());
//            systemTenantUser.setUserId(userId);
//            systemTenantUser.setCreateTime(new Date());
//            systemTenantUser.setStatus(BusinessConfig.VALID_STATUS);
//            systemTenantUser.setPlatformId(dto.getPlatformId() == null ? "" : dto.getPlatformId());
//            systemTenantUser.setTenantId(dto.getTenantId() == null ? "" : dto.getTenantId());
//            systemTenantUserMapper.insert(systemTenantUser);

            return CommonResult.success("");
        }
        return CommonResult.failed();
    }

    @Override
    public CommonResult<Object> mobileUserJoinProject(String projectId, String orgId, String userId) {
        // 根据当前登录用户，获取参与者信息
        String tenantId = SecurityUtils.getSystemTenantId();
        String platformId = SecurityUtils.getSystemPlatformId();
        ProjectVisitUser projectTesteeUser = projectVisitUserService.getProjectTesteeUserByUserId(projectId, userId, tenantId, platformId);
        if(projectTesteeUser != null){
            return CommonResult.failed("请勿重复加入");
        }

        String formattedNumber = getNextTesteeCode(Long.parseLong(projectId), orgId);//获取项目配置的参与者编号配置信息
        Project projectBaseInfo = projectBaseManageService.getProjectBaseInfo(projectId);
        if (projectBaseInfo != null){
            SystemUserInfo userBaseInfo = systemUserInfoService.getUserBaseInfo(NumberUtil.parseLong(userId));
            ProjectTesteeParam param = new ProjectTesteeParam();
            param.setProjectId(Long.parseLong(projectId));
            param.setUserId(NumberUtil.parseLong(userId));
            param.setOwnerOrgId(orgId);
            param.setRealName(userBaseInfo.getRealName());
            param.setResearchStatus(ProjectTesteeEnum.PROJECT_TESTEE_STATUS_01.getName());
            param.setCode(formattedNumber);
            param.setTesteeCode(formattedNumber);
            param.setOperator(SecurityUtils.getUserIdValue());
            param.setTenantId(SecurityUtils.getSystemTenantId());
            param.setPlatformId(SecurityUtils.getSystemPlatformId());
            projectTesteeInfoService.saveMobileProjectTesteeBaseInfo(param);
        }
        return CommonResult.success(null);
    }

    public String getNextTesteeCode(Long projectId, String orgId){
        // 获取项目配置的参与者编号配置信息
        StringBuilder formattedNumber = new StringBuilder();
        ProjectConfigVo prefixVo = projectConfigService.getProjectBaseConfig( projectId.toString(), "prefix_code");
        ProjectConfigVo startVo = projectConfigService.getProjectBaseConfig( projectId.toString(), "start_code");
        String prefixS = prefixVo == null ? "S" : prefixVo.getConfigValue();
        String maxtesteeCode = projectVisitUserService.getMaxtesteeCode(projectId, Long.valueOf(orgId), prefixS);

        maxtesteeCode = maxtesteeCode == null ? "0" : maxtesteeCode;
        if(prefixVo == null){
            int codeInt = Integer.valueOf(maxtesteeCode) + 1;
            formattedNumber.append("S").append(String.format("%06d", codeInt));
        }else {
            int codeInt = 0;
            String prefix = prefixVo.getConfigValue();
            if("0".equals(maxtesteeCode)){
                codeInt = Integer.valueOf(maxtesteeCode) + 1;
            }else {
                maxtesteeCode = maxtesteeCode.substring(prefix.length());
                codeInt = Integer.valueOf(maxtesteeCode) + 1;
            }
            String codeStr = String.valueOf(codeInt);
            int num = startVo.getConfigValue().length() - codeStr.length();
            for(int i = 0 ; i < num ; i++){
                codeStr = "0" + codeStr;
            }
            formattedNumber.append(prefix).append(codeStr);
        }

        return formattedNumber.toString();
    }

    @Override
    public List<ProjectOrgInfo> getProjectBaseInfoByIdentCode(String identCode) {
        return organizationService.selectProjectByIdentCode(identCode);
    }

}
