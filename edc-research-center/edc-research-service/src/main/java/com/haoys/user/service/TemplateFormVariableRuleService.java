package com.haoys.user.service;


import com.haoys.user.common.api.CommonResult;
import com.haoys.user.model.TemplateFormVariableFun;
import com.haoys.user.model.TemplateFormVariableRule;

import java.util.List;
import java.util.Map;

/**
 * 配置表单-公式计算service
 */
public interface TemplateFormVariableRuleService {


    /**
     * 获取公式配置信息
     * @param projectId 项目id
     * @param formId  表单id
     * @param variableId 变量id
     * @param groupId 组id
     * @return
     */
    TemplateFormVariableRule getFormVariableRule(String projectId,String groupId, String formId, String variableId);


    /**
     * 获取公式配置信息
     * @param projectId 项目id
     * @param formId  表单id
     * @param variableId 变量id
     * @param groupId 组id
     * @return
     */
    List<TemplateFormVariableRule> getFormVariableRuleList(String projectId,String groupId, String formId);

    /**
     * 保存或者更新公式
     * @param rule 公式信息
     * @return
     */
    CommonResult<TemplateFormVariableRule> saveFormVariableRule(TemplateFormVariableRule rule);

    /**
     * 获取函数列表
     * @return
     */
    Map<Integer,List<TemplateFormVariableFun>> getFormVariableFun();
}
