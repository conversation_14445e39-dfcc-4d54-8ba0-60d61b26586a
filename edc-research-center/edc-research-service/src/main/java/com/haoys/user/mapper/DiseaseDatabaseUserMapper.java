package com.haoys.user.mapper;

import com.haoys.user.model.DiseaseDatabaseAuth;
import com.haoys.user.model.DiseaseDatabaseUser;
import com.haoys.user.model.DiseaseDatabaseUserExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DiseaseDatabaseUserMapper {
    long countByExample(DiseaseDatabaseUserExample example);

    int deleteByExample(DiseaseDatabaseUserExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DiseaseDatabaseUser record);

    int insertSelective(DiseaseDatabaseUser record);

    List<DiseaseDatabaseUser> selectByExample(DiseaseDatabaseUserExample example);

    DiseaseDatabaseUser selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DiseaseDatabaseUser record, @Param("example") DiseaseDatabaseUserExample example);

    int updateByExample(@Param("record") DiseaseDatabaseUser record, @Param("example") DiseaseDatabaseUserExample example);

    int updateByPrimaryKeySelective(DiseaseDatabaseUser record);

    int updateByPrimaryKey(DiseaseDatabaseUser record);

    DiseaseDatabaseUser getDiseaseDatabaseUser(String dataBaseId, String userId);

    void deleteDiseaseDatabaseUserByUserId(String systemUserId);

    List<DiseaseDatabaseUser> getDiseaseDatabaseListByUserId(String systemUserId);

    DiseaseDatabaseAuth getDiseaseDatabaseAuth(String userId);
}