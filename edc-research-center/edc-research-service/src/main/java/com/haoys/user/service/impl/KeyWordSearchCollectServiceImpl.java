package com.haoys.user.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.core.service.BaseService;
import com.haoys.user.common.util.BeanUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.SnowflakeIdWorker;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.domain.param.export.ProjectSearchCollectParam;
import com.haoys.user.domain.vo.export.ProjectSearchCollectVo;
import com.haoys.user.mapper.ProjectSearchCollectMapper;
import com.haoys.user.model.ProjectSearchCollect;
import com.haoys.user.model.ProjectSearchCollectExample;
import com.haoys.user.service.KeyWordSearchCollectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class KeyWordSearchCollectServiceImpl extends BaseService implements KeyWordSearchCollectService {

    @Autowired
    private ProjectSearchCollectMapper projectSearchCollectMapper;

    @Override
    public CustomResult saveDiseaseSearchCollectRecord(ProjectSearchCollectParam projectSearchCollectParam) {
        CustomResult customResult = new CustomResult();
        ProjectSearchCollect projectSearchCollect = new ProjectSearchCollect();
        BeanUtils.copyProperties(projectSearchCollectParam, projectSearchCollect);
        projectSearchCollect.setId(SnowflakeIdWorker.getUuid());

        if(projectSearchCollectParam.getProjectId() != null){
            projectSearchCollect.setProjectId(projectSearchCollectParam.getProjectId());
        }

        if(StringUtils.isNotEmpty(projectSearchCollectParam.getDataBaseId())){
            projectSearchCollect.setDataBaseId(projectSearchCollectParam.getDataBaseId());
        }

        projectSearchCollect.setCreateTime(new Date());
        projectSearchCollect.setStatus(BusinessConfig.VALID_STATUS);
        projectSearchCollect.setCreateUserId(SecurityUtils.getUserIdValue());
        projectSearchCollect.setTenantId(SecurityUtils.getSystemTenantId());
        projectSearchCollect.setPlatformId(SecurityUtils.getSystemPlatformId());
        projectSearchCollectMapper.insertSelective(projectSearchCollect);
        return customResult;
    }

    @Override
    public CustomResult deleteDiseaseSearchCollectRecord(String id) {
        CustomResult customResult = new CustomResult();
        ProjectSearchCollect projectSearchCollect = projectSearchCollectMapper.selectByPrimaryKey(Long.parseLong(id));
        if(projectSearchCollect == null){
            customResult.setCode(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getCode());
            customResult.setMessage(ResultCode.RETURN_MESSAGE_RECORD_NOT_FOUND.getMessage());
            return customResult;
        }
        projectSearchCollectMapper.deleteByPrimaryKey(Long.parseLong(id));
        return customResult;
    }

    @Override
    public CommonPage<ProjectSearchCollectVo> getDiseaseSearchCollectListForPage(String dataBaseId, String diseaseType, String systemUseType, String searchType, Integer pageNum, Integer pageSize) {
        List<ProjectSearchCollectVo> dataList = new ArrayList<>();
        Page<Object> page = PageHelper.startPage(pageNum, pageSize);
        ProjectSearchCollectExample example = new ProjectSearchCollectExample();
        ProjectSearchCollectExample.Criteria criteria = example.createCriteria();
        criteria.andDiseaseTypeEqualTo(diseaseType);
        criteria.andCreateUserIdEqualTo(SecurityUtils.getUserIdValue());
        if(StringUtils.isNotEmpty(dataBaseId)){
            criteria.andDataBaseIdEqualTo(dataBaseId);
        }
        if(StringUtils.isNotEmpty(systemUseType)){
            criteria.andSystemUseTypeEqualTo(systemUseType);
        }
        if(StringUtils.isNotEmpty(searchType)){
            criteria.andSearchTypeEqualTo(searchType);
        }
        example.setOrderByClause("create_time desc");
        List<ProjectSearchCollect> projectSearchCollectList = projectSearchCollectMapper.selectByExample(example);
        projectSearchCollectList.forEach(data->{
            ProjectSearchCollectVo projectSearchCollectVo = new ProjectSearchCollectVo();
            BeanUtils.copyProperties(data, projectSearchCollectVo);
            dataList.add(projectSearchCollectVo);
        });
        return commonPageListWrapper(pageNum, pageSize, page, dataList);
    }
}
