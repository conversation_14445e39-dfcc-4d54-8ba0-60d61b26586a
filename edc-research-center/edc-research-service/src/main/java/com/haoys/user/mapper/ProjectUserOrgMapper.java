package com.haoys.user.mapper;

import com.haoys.user.domain.vo.project.ProjectUserOrgVo;
import com.haoys.user.model.ProjectUserOrg;
import com.haoys.user.model.ProjectUserOrgExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ProjectUserOrgMapper {
    long countByExample(ProjectUserOrgExample example);

    int deleteByExample(ProjectUserOrgExample example);

    int insert(ProjectUserOrg record);

    int insertSelective(ProjectUserOrg record);

    List<ProjectUserOrg> selectByExample(ProjectUserOrgExample example);

    int updateByExampleSelective(@Param("record") ProjectUserOrg record, @Param("example") ProjectUserOrgExample example);

    int updateByExample(@Param("record") ProjectUserOrg record, @Param("example") ProjectUserOrgExample example);

    List<ProjectUserOrg> selectOrgListByUserId(String userId, String orgId);

    /**
     * 查询项目研究中心id集合
     * @param projectId
     * @param userId
     * @param code
     * @return
     */
    List<String> getOrgIdsByProjectIdAndUserId(@Param("projectId") String projectId, @Param("userId") String userId, @Param("code") String code);

    /**
     * 根据项目和研究中心查询用户列表
     * @param projectId
     * @param projectOrgId
     * @return
     */
    List<ProjectUserOrg> selectOrgListByProjectIdAndOrgId(String projectId, String projectOrgId);
    
    List<ProjectUserOrgVo> getProjectUserJoinOrgListByUserId(String projectId, String userId);
}