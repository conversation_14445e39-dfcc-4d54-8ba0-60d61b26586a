package com.haoys.user.service.impl;

import com.haoys.user.domain.vo.monitor.SystemPerformanceVo;
import com.haoys.user.service.SystemPerformanceMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.actuate.health.HealthEndpoint;
// import org.springframework.boot.actuate.metrics.MetricsEndpoint;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.management.MBeanServer;
import javax.management.ObjectName;
import javax.sql.DataSource;
import java.io.File;
import java.lang.management.*;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 系统性能监控服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
@Slf4j
@Service
public class SystemPerformanceMonitorServiceImpl implements SystemPerformanceMonitorService {

    // @Autowired(required = false)
    // private HealthEndpoint healthEndpoint;

    // @Autowired(required = false)
    // private MetricsEndpoint metricsEndpoint;

    @Autowired(required = false)
    private DataSource dataSource;

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired(required = false)
    private CacheManager cacheManager;

    private final OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
    private final ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
    private final List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();

    @Override
    public SystemPerformanceVo getSystemPerformanceOverview() {
        try {
            return SystemPerformanceVo.builder()
                    .monitorTime(LocalDateTime.now())
                    .systemInfo(buildSystemInfo())
                    .cpuInfo(buildCpuInfo())
                    .memoryInfo(buildMemoryInfo())
                    .diskInfo(buildDiskInfo())
                    .jvmInfo(buildJvmInfo())
                    .databaseInfo(buildDatabaseInfo())
                    .threadPoolInfo(buildThreadPoolInfo())
                    .cacheInfo(buildCacheInfo())
                    .processInfo(buildProcessInfo())
                    .build();
        } catch (Exception e) {
            log.error("获取系统性能概览失败", e);
            throw new RuntimeException("获取系统性能概览失败", e);
        }
    }

    @Override
    public Map<String, Object> getCpuInfo() {
        Map<String, Object> cpuInfo = new HashMap<>();
        try {
            // CPU使用率
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = 
                    (com.sun.management.OperatingSystemMXBean) osBean;
                cpuInfo.put("systemCpuLoad", formatPercentage(sunOsBean.getSystemCpuLoad()));
                cpuInfo.put("processCpuLoad", formatPercentage(sunOsBean.getProcessCpuLoad()));
                cpuInfo.put("processCpuTime", sunOsBean.getProcessCpuTime());
            }
            
            cpuInfo.put("availableProcessors", osBean.getAvailableProcessors());
            cpuInfo.put("systemLoadAverage", osBean.getSystemLoadAverage());
            
            // 由于没有Actuator依赖，使用基本的CPU信息
            // 注释掉Actuator相关代码
            
        } catch (Exception e) {
            log.error("获取CPU信息失败", e);
            cpuInfo.put("error", "获取CPU信息失败: " + e.getMessage());
        }
        return cpuInfo;
    }

    @Override
    public Map<String, Object> getMemoryInfo() {
        Map<String, Object> memoryInfo = new HashMap<>();
        try {
            // 系统内存
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean = 
                    (com.sun.management.OperatingSystemMXBean) osBean;
                
                long totalPhysicalMemory = sunOsBean.getTotalPhysicalMemorySize();
                long freePhysicalMemory = sunOsBean.getFreePhysicalMemorySize();
                long usedPhysicalMemory = totalPhysicalMemory - freePhysicalMemory;
                
                memoryInfo.put("totalPhysicalMemory", formatBytes(totalPhysicalMemory));
                memoryInfo.put("usedPhysicalMemory", formatBytes(usedPhysicalMemory));
                memoryInfo.put("freePhysicalMemory", formatBytes(freePhysicalMemory));
                memoryInfo.put("physicalMemoryUsage", 
                    formatPercentage((double) usedPhysicalMemory / totalPhysicalMemory));
                
                // 交换区
                long totalSwapSpace = sunOsBean.getTotalSwapSpaceSize();
                long freeSwapSpace = sunOsBean.getFreeSwapSpaceSize();
                memoryInfo.put("totalSwapSpace", formatBytes(totalSwapSpace));
                memoryInfo.put("freeSwapSpace", formatBytes(freeSwapSpace));
                memoryInfo.put("usedSwapSpace", formatBytes(totalSwapSpace - freeSwapSpace));
            }
            
            // JVM内存
            MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
            
            Map<String, Object> heapMemoryMap = new HashMap<>();
            heapMemoryMap.put("init", formatBytes(heapMemory.getInit()));
            heapMemoryMap.put("used", formatBytes(heapMemory.getUsed()));
            heapMemoryMap.put("committed", formatBytes(heapMemory.getCommitted()));
            heapMemoryMap.put("max", formatBytes(heapMemory.getMax()));
            heapMemoryMap.put("usage", formatPercentage((double) heapMemory.getUsed() / heapMemory.getMax()));
            memoryInfo.put("heapMemory", heapMemoryMap);
            
            Map<String, Object> nonHeapMemoryMap = new HashMap<>();
            nonHeapMemoryMap.put("init", formatBytes(nonHeapMemory.getInit()));
            nonHeapMemoryMap.put("used", formatBytes(nonHeapMemory.getUsed()));
            nonHeapMemoryMap.put("committed", formatBytes(nonHeapMemory.getCommitted()));
            nonHeapMemoryMap.put("max", nonHeapMemory.getMax() == -1 ? "无限制" : formatBytes(nonHeapMemory.getMax()));
            memoryInfo.put("nonHeapMemory", nonHeapMemoryMap);
            
        } catch (Exception e) {
            log.error("获取内存信息失败", e);
            memoryInfo.put("error", "获取内存信息失败: " + e.getMessage());
        }
        return memoryInfo;
    }

    @Override
    public Map<String, Object> getDiskInfo() {
        Map<String, Object> diskInfo = new HashMap<>();
        try {
            List<Map<String, Object>> partitions = new ArrayList<>();
            
            File[] roots = File.listRoots();
            for (File root : roots) {
                Map<String, Object> partition = new HashMap<>();
                partition.put("path", root.getAbsolutePath());
                partition.put("totalSpace", formatBytes(root.getTotalSpace()));
                partition.put("freeSpace", formatBytes(root.getFreeSpace()));
                partition.put("usedSpace", formatBytes(root.getTotalSpace() - root.getFreeSpace()));
                
                if (root.getTotalSpace() > 0) {
                    double usage = (double) (root.getTotalSpace() - root.getFreeSpace()) / root.getTotalSpace();
                    partition.put("usage", formatPercentage(usage));
                }
                
                partitions.add(partition);
            }
            
            diskInfo.put("partitions", partitions);
            
        } catch (Exception e) {
            log.error("获取磁盘信息失败", e);
            diskInfo.put("error", "获取磁盘信息失败: " + e.getMessage());
        }
        return diskInfo;
    }

    @Override
    public Map<String, Object> getJvmInfo() {
        Map<String, Object> jvmInfo = new HashMap<>();
        try {
            // 基本信息
            jvmInfo.put("jvmName", runtimeBean.getVmName());
            jvmInfo.put("jvmVersion", runtimeBean.getVmVersion());
            jvmInfo.put("jvmVendor", runtimeBean.getVmVendor());
            jvmInfo.put("startTime", LocalDateTime.ofInstant(
                new Date(runtimeBean.getStartTime()).toInstant(), ZoneId.systemDefault()));
            jvmInfo.put("uptime", formatDuration(runtimeBean.getUptime()));
            
            // 内存信息
            MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
            
            Map<String, Object> jvmHeapMemoryMap = new HashMap<>();
            jvmHeapMemoryMap.put("used", formatBytes(heapMemory.getUsed()));
            jvmHeapMemoryMap.put("committed", formatBytes(heapMemory.getCommitted()));
            jvmHeapMemoryMap.put("max", formatBytes(heapMemory.getMax()));
            jvmInfo.put("heapMemory", jvmHeapMemoryMap);
            
            Map<String, Object> jvmNonHeapMemoryMap = new HashMap<>();
            jvmNonHeapMemoryMap.put("used", formatBytes(nonHeapMemory.getUsed()));
            jvmNonHeapMemoryMap.put("committed", formatBytes(nonHeapMemory.getCommitted()));
            jvmInfo.put("nonHeapMemory", jvmNonHeapMemoryMap);
            
            // 线程信息
            jvmInfo.put("threadCount", threadBean.getThreadCount());
            jvmInfo.put("daemonThreadCount", threadBean.getDaemonThreadCount());
            jvmInfo.put("peakThreadCount", threadBean.getPeakThreadCount());
            jvmInfo.put("totalStartedThreadCount", threadBean.getTotalStartedThreadCount());
            
            // 垃圾回收信息
            List<Map<String, Object>> gcInfo = new ArrayList<>();
            for (GarbageCollectorMXBean gcBean : gcBeans) {
                Map<String, Object> gc = new HashMap<>();
                gc.put("name", gcBean.getName());
                gc.put("collectionCount", gcBean.getCollectionCount());
                gc.put("collectionTime", gcBean.getCollectionTime() + "ms");
                gcInfo.add(gc);
            }
            jvmInfo.put("garbageCollectors", gcInfo);
            
            // 类加载信息
            ClassLoadingMXBean classLoadingBean = ManagementFactory.getClassLoadingMXBean();
            Map<String, Object> classLoadingMap = new HashMap<>();
            classLoadingMap.put("loadedClassCount", classLoadingBean.getLoadedClassCount());
            classLoadingMap.put("totalLoadedClassCount", classLoadingBean.getTotalLoadedClassCount());
            classLoadingMap.put("unloadedClassCount", classLoadingBean.getUnloadedClassCount());
            jvmInfo.put("classLoading", classLoadingMap);
            
        } catch (Exception e) {
            log.error("获取JVM信息失败", e);
            jvmInfo.put("error", "获取JVM信息失败: " + e.getMessage());
        }
        return jvmInfo;
    }

    // 辅助方法
    private SystemPerformanceVo.SystemInfoVo buildSystemInfo() {
        return SystemPerformanceVo.SystemInfoVo.builder()
                .osName(System.getProperty("os.name"))
                .osVersion(System.getProperty("os.version"))
                .osArch(System.getProperty("os.arch"))
                .javaVersion(System.getProperty("java.version"))
                .startTime(LocalDateTime.ofInstant(
                    new Date(runtimeBean.getStartTime()).toInstant(), ZoneId.systemDefault()))
                .uptime(runtimeBean.getUptime() / 1000)
                .processorCount(osBean.getAvailableProcessors())
                .build();
    }

    private SystemPerformanceVo.CpuInfoVo buildCpuInfo() {
        SystemPerformanceVo.CpuInfoVo.CpuInfoVoBuilder builder = SystemPerformanceVo.CpuInfoVo.builder()
                .availableProcessors(osBean.getAvailableProcessors())
                .systemLoadAverage(osBean.getSystemLoadAverage());

        if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
            com.sun.management.OperatingSystemMXBean sunOsBean = 
                (com.sun.management.OperatingSystemMXBean) osBean;
            builder.cpuUsage(sunOsBean.getSystemCpuLoad() * 100)
                   .processCpuUsage(sunOsBean.getProcessCpuLoad() * 100);
        }

        return builder.build();
    }

    private SystemPerformanceVo.MemoryInfoVo buildMemoryInfo() {
        SystemPerformanceVo.MemoryInfoVo.MemoryInfoVoBuilder builder = SystemPerformanceVo.MemoryInfoVo.builder();

        if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
            com.sun.management.OperatingSystemMXBean sunOsBean = 
                (com.sun.management.OperatingSystemMXBean) osBean;
            
            long totalMemory = sunOsBean.getTotalPhysicalMemorySize() / (1024 * 1024);
            long freeMemory = sunOsBean.getFreePhysicalMemorySize() / (1024 * 1024);
            long usedMemory = totalMemory - freeMemory;
            
            builder.totalMemory(totalMemory)
                   .freeMemory(freeMemory)
                   .usedMemory(usedMemory)
                   .memoryUsage((double) usedMemory / totalMemory * 100)
                   .totalSwap(sunOsBean.getTotalSwapSpaceSize() / (1024 * 1024))
                   .usedSwap((sunOsBean.getTotalSwapSpaceSize() - sunOsBean.getFreeSwapSpaceSize()) / (1024 * 1024));
        }

        return builder.build();
    }

    private SystemPerformanceVo.DiskInfoVo buildDiskInfo() {
        List<SystemPerformanceVo.DiskInfoVo.DiskPartitionVo> partitions = new ArrayList<>();
        
        File[] roots = File.listRoots();
        for (File root : roots) {
            double totalSpace = root.getTotalSpace() / (1024.0 * 1024.0 * 1024.0);
            double freeSpace = root.getFreeSpace() / (1024.0 * 1024.0 * 1024.0);
            double usedSpace = totalSpace - freeSpace;
            double usage = totalSpace > 0 ? (usedSpace / totalSpace * 100) : 0;
            
            partitions.add(SystemPerformanceVo.DiskInfoVo.DiskPartitionVo.builder()
                    .path(root.getAbsolutePath())
                    .totalSpace(totalSpace)
                    .freeSpace(freeSpace)
                    .usedSpace(usedSpace)
                    .usage(usage)
                    .build());
        }
        
        return SystemPerformanceVo.DiskInfoVo.builder()
                .partitions(partitions)
                .build();
    }

    private SystemPerformanceVo.JvmInfoVo buildJvmInfo() {
        MemoryUsage heapMemory = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemory = memoryBean.getNonHeapMemoryUsage();
        
        List<SystemPerformanceVo.JvmInfoVo.GcInfoVo> gcInfo = new ArrayList<>();
        for (GarbageCollectorMXBean gcBean : gcBeans) {
            gcInfo.add(SystemPerformanceVo.JvmInfoVo.GcInfoVo.builder()
                    .name(gcBean.getName())
                    .collectionCount(gcBean.getCollectionCount())
                    .collectionTime(gcBean.getCollectionTime())
                    .build());
        }
        
        return SystemPerformanceVo.JvmInfoVo.builder()
                .heapTotal(heapMemory.getCommitted() / (1024 * 1024))
                .heapUsed(heapMemory.getUsed() / (1024 * 1024))
                .heapMax(heapMemory.getMax() / (1024 * 1024))
                .nonHeapTotal(nonHeapMemory.getCommitted() / (1024 * 1024))
                .nonHeapUsed(nonHeapMemory.getUsed() / (1024 * 1024))
                .totalThreads(threadBean.getThreadCount())
                .activeThreads(threadBean.getThreadCount() - threadBean.getDaemonThreadCount())
                .daemonThreads(threadBean.getDaemonThreadCount())
                .gcInfo(gcInfo)
                .build();
    }

    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
    }

    private String formatPercentage(double value) {
        return String.format("%.2f%%", value * 100);
    }

    private String formatDuration(long millis) {
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    @Override
    public Map<String, Object> getDatabaseInfo() {
        Map<String, Object> databaseInfo = new HashMap<>();
        try {
            if (dataSource != null) {
                // HikariCP连接池信息
                if (dataSource instanceof com.zaxxer.hikari.HikariDataSource) {
                    com.zaxxer.hikari.HikariDataSource hikariDataSource =
                        (com.zaxxer.hikari.HikariDataSource) dataSource;

                    Map<String, Object> poolInfo = new HashMap<>();
                    poolInfo.put("poolName", hikariDataSource.getPoolName());
                    poolInfo.put("activeConnections", hikariDataSource.getHikariPoolMXBean().getActiveConnections());
                    poolInfo.put("idleConnections", hikariDataSource.getHikariPoolMXBean().getIdleConnections());
                    poolInfo.put("totalConnections", hikariDataSource.getHikariPoolMXBean().getTotalConnections());
                    poolInfo.put("threadsAwaitingConnection", hikariDataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
                    poolInfo.put("maximumPoolSize", hikariDataSource.getMaximumPoolSize());
                    poolInfo.put("minimumIdle", hikariDataSource.getMinimumIdle());

                    databaseInfo.put("connectionPool", poolInfo);
                }

                // 数据库基本信息
                try (Connection connection = dataSource.getConnection()) {
                    Map<String, Object> dbInfo = new HashMap<>();
                    dbInfo.put("databaseProductName", connection.getMetaData().getDatabaseProductName());
                    dbInfo.put("databaseProductVersion", connection.getMetaData().getDatabaseProductVersion());
                    dbInfo.put("driverName", connection.getMetaData().getDriverName());
                    dbInfo.put("driverVersion", connection.getMetaData().getDriverVersion());
                    dbInfo.put("url", connection.getMetaData().getURL());

                    databaseInfo.put("database", dbInfo);
                }
            } else {
                databaseInfo.put("error", "数据源未配置");
            }
        } catch (Exception e) {
            log.error("获取数据库信息失败", e);
            databaseInfo.put("error", "获取数据库信息失败: " + e.getMessage());
        }
        return databaseInfo;
    }

    @Override
    public Map<String, Object> getThreadPoolInfo() {
        Map<String, Object> threadPoolInfo = new HashMap<>();
        try {
            // 获取所有线程池信息
            MBeanServer mBeanServer = ManagementFactory.getPlatformMBeanServer();
            Set<ObjectName> objectNames = mBeanServer.queryNames(
                new ObjectName("java.util.concurrent:type=ThreadPoolExecutor,*"), null);

            Map<String, Object> threadPools = new HashMap<>();
            for (ObjectName objectName : objectNames) {
                try {
                    Map<String, Object> poolInfo = new HashMap<>();
                    poolInfo.put("corePoolSize", mBeanServer.getAttribute(objectName, "CorePoolSize"));
                    poolInfo.put("maximumPoolSize", mBeanServer.getAttribute(objectName, "MaximumPoolSize"));
                    poolInfo.put("poolSize", mBeanServer.getAttribute(objectName, "PoolSize"));
                    poolInfo.put("activeCount", mBeanServer.getAttribute(objectName, "ActiveCount"));
                    poolInfo.put("taskCount", mBeanServer.getAttribute(objectName, "TaskCount"));
                    poolInfo.put("completedTaskCount", mBeanServer.getAttribute(objectName, "CompletedTaskCount"));

                    threadPools.put(objectName.getKeyProperty("name"), poolInfo);
                } catch (Exception e) {
                    log.warn("获取线程池 {} 信息失败", objectName, e);
                }
            }

            threadPoolInfo.put("threadPools", threadPools);
            threadPoolInfo.put("totalThreads", threadBean.getThreadCount());
            threadPoolInfo.put("activeThreads", threadBean.getThreadCount() - threadBean.getDaemonThreadCount());
            threadPoolInfo.put("daemonThreads", threadBean.getDaemonThreadCount());

        } catch (Exception e) {
            log.error("获取线程池信息失败", e);
            threadPoolInfo.put("error", "获取线程池信息失败: " + e.getMessage());
        }
        return threadPoolInfo;
    }

    @Override
    public Map<String, Object> getProcessInfo() {
        Map<String, Object> processInfo = new HashMap<>();
        try {
            // 当前进程信息
            String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
            processInfo.put("currentPid", currentPid);

            // Java进程信息
            List<Map<String, Object>> javaProcesses = new ArrayList<>();
            try {
                Process process = Runtime.getRuntime().exec("jps -l");
                Scanner scanner = new Scanner(process.getInputStream());
                while (scanner.hasNextLine()) {
                    String line = scanner.nextLine();
                    if (!line.trim().isEmpty() && !line.contains("jps")) {
                        String[] parts = line.split(" ", 2);
                        if (parts.length >= 2) {
                            Map<String, Object> javaProcess = new HashMap<>();
                            javaProcess.put("pid", parts[0]);
                            javaProcess.put("mainClass", parts[1]);
                            javaProcesses.add(javaProcess);
                        }
                    }
                }
                scanner.close();
            } catch (Exception e) {
                log.warn("获取Java进程信息失败", e);
            }

            processInfo.put("javaProcesses", javaProcesses);

            // 系统进程统计
            try {
                if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                    Process process = Runtime.getRuntime().exec("tasklist /fo csv | find /c /v \"\"");
                    Scanner scanner = new Scanner(process.getInputStream());
                    if (scanner.hasNextLine()) {
                        processInfo.put("totalProcesses", Integer.parseInt(scanner.nextLine().trim()) - 1);
                    }
                    scanner.close();
                } else {
                    Process process = Runtime.getRuntime().exec("ps aux | wc -l");
                    Scanner scanner = new Scanner(process.getInputStream());
                    if (scanner.hasNextLine()) {
                        processInfo.put("totalProcesses", Integer.parseInt(scanner.nextLine().trim()) - 1);
                    }
                    scanner.close();
                }
            } catch (Exception e) {
                log.warn("获取系统进程统计失败", e);
            }

        } catch (Exception e) {
            log.error("获取进程信息失败", e);
            processInfo.put("error", "获取进程信息失败: " + e.getMessage());
        }
        return processInfo;
    }

    @Override
    public Map<String, Object> getDeadlockInfo() {
        Map<String, Object> deadlockInfo = new HashMap<>();
        try {
            // JVM死锁检测
            long[] deadlockedThreads = threadBean.findDeadlockedThreads();
            List<Map<String, Object>> jvmDeadlocks = new ArrayList<>();

            if (deadlockedThreads != null && deadlockedThreads.length > 0) {
                ThreadInfo[] threadInfos = threadBean.getThreadInfo(deadlockedThreads);
                for (ThreadInfo threadInfo : threadInfos) {
                    if (threadInfo != null) {
                        Map<String, Object> deadlock = new HashMap<>();
                        deadlock.put("threadId", threadInfo.getThreadId());
                        deadlock.put("threadName", threadInfo.getThreadName());
                        deadlock.put("threadState", threadInfo.getThreadState().toString());
                        deadlock.put("blockedTime", threadInfo.getBlockedTime());
                        deadlock.put("waitedTime", threadInfo.getWaitedTime());
                        deadlock.put("lockName", threadInfo.getLockName());
                        deadlock.put("lockOwnerId", threadInfo.getLockOwnerId());
                        deadlock.put("lockOwnerName", threadInfo.getLockOwnerName());
                        jvmDeadlocks.add(deadlock);
                    }
                }
            }

            deadlockInfo.put("jvmDeadlocks", jvmDeadlocks);
            deadlockInfo.put("hasDeadlock", !jvmDeadlocks.isEmpty());

            // 数据库死锁检测
            if (dataSource != null) {
                try (Connection connection = dataSource.getConnection()) {
                    String dbProductName = connection.getMetaData().getDatabaseProductName().toLowerCase();
                    List<Map<String, Object>> dbDeadlocks = new ArrayList<>();

                    if (dbProductName.contains("mysql")) {
                        // MySQL死锁检测
                        try (PreparedStatement stmt = connection.prepareStatement(
                                "SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCKS")) {
                            ResultSet rs = stmt.executeQuery();
                            while (rs.next()) {
                                Map<String, Object> lock = new HashMap<>();
                                lock.put("lockId", rs.getString("lock_id"));
                                lock.put("lockTrxId", rs.getString("lock_trx_id"));
                                lock.put("lockMode", rs.getString("lock_mode"));
                                lock.put("lockType", rs.getString("lock_type"));
                                lock.put("lockTable", rs.getString("lock_table"));
                                dbDeadlocks.add(lock);
                            }
                        } catch (Exception e) {
                            log.warn("查询MySQL锁信息失败", e);
                        }
                    }

                    deadlockInfo.put("databaseDeadlocks", dbDeadlocks);
                } catch (Exception e) {
                    log.warn("检测数据库死锁失败", e);
                    deadlockInfo.put("databaseError", "检测数据库死锁失败: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("检测死锁失败", e);
            deadlockInfo.put("error", "检测死锁失败: " + e.getMessage());
        }
        return deadlockInfo;
    }

    @Override
    public Map<String, Object> getCacheInfo() {
        Map<String, Object> cacheInfo = new HashMap<>();
        try {
            // Redis连接状态
            if (redisTemplate != null) {
                try {
                    RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();
                    String pong = connection.ping();
                    cacheInfo.put("redisStatus", "PONG".equals(pong) ? "连接正常" : "连接异常");

                    // Redis信息
                    Properties info = connection.info();
                    Map<String, Object> redisInfo = new HashMap<>();
                    redisInfo.put("version", info.getProperty("redis_version"));
                    redisInfo.put("connectedClients", info.getProperty("connected_clients"));
                    redisInfo.put("usedMemory", info.getProperty("used_memory_human"));
                    redisInfo.put("totalCommandsProcessed", info.getProperty("total_commands_processed"));
                    cacheInfo.put("redisInfo", redisInfo);

                    connection.close();
                } catch (Exception e) {
                    cacheInfo.put("redisStatus", "连接失败: " + e.getMessage());
                }
            } else {
                cacheInfo.put("redisStatus", "Redis未配置");
            }

            // 缓存管理器信息
            if (cacheManager != null) {
                Map<String, Object> cacheStats = new HashMap<>();
                for (String cacheName : cacheManager.getCacheNames()) {
                    Map<String, Object> stats = new HashMap<>();
                    stats.put("name", cacheName);
                    // 这里可以添加更多缓存统计信息
                    cacheStats.put(cacheName, stats);
                }
                cacheInfo.put("cacheStats", cacheStats);
            }

        } catch (Exception e) {
            log.error("获取缓存信息失败", e);
            cacheInfo.put("error", "获取缓存信息失败: " + e.getMessage());
        }
        return cacheInfo;
    }

    @Override
    public Map<String, Object> getHealthInfo() {
        Map<String, Object> healthInfo = new HashMap<>();
        try {
            // 由于没有Actuator依赖，提供基本的健康检查
            healthInfo.put("status", "UP");
            healthInfo.put("message", "健康检查端点未配置");
            healthInfo.put("timestamp", LocalDateTime.now());

            // 基本的系统检查
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsage = (double) usedMemory / totalMemory * 100;

            if (memoryUsage > 90) {
                healthInfo.put("status", "WARNING");
                healthInfo.put("message", "内存使用率过高: " + String.format("%.2f%%", memoryUsage));
            }

        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            healthInfo.put("error", "获取健康状态失败: " + e.getMessage());
        }
        return healthInfo;
    }

    @Override
    public Map<String, Object> getSystemMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        try {
            // 由于没有Actuator依赖，提供基本的系统指标
            metrics.put("message", "指标端点未配置");
            metrics.put("timestamp", LocalDateTime.now());

            // 提供基本的JVM指标
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> jvmMetrics = new HashMap<>();
            jvmMetrics.put("totalMemory", formatBytes(runtime.totalMemory()));
            jvmMetrics.put("freeMemory", formatBytes(runtime.freeMemory()));
            jvmMetrics.put("usedMemory", formatBytes(runtime.totalMemory() - runtime.freeMemory()));
            jvmMetrics.put("maxMemory", formatBytes(runtime.maxMemory()));
            jvmMetrics.put("availableProcessors", runtime.availableProcessors());

            metrics.put("jvm", jvmMetrics);

            // 提供基本的系统指标
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            Map<String, Object> systemMetrics = new HashMap<>();
            systemMetrics.put("systemLoadAverage", osBean.getSystemLoadAverage());
            systemMetrics.put("availableProcessors", osBean.getAvailableProcessors());

            metrics.put("system", systemMetrics);

        } catch (Exception e) {
            log.error("获取系统指标失败", e);
            metrics.put("error", "获取系统指标失败: " + e.getMessage());
        }
        return metrics;
    }

    @Override
    public void forceGarbageCollection() {
        try {
            log.info("执行强制垃圾回收");
            System.gc();
            log.info("垃圾回收执行完成");
        } catch (Exception e) {
            log.error("执行垃圾回收失败", e);
            throw new RuntimeException("执行垃圾回收失败", e);
        }
    }

    @Override
    public void clearSystemCache(String cacheName) {
        try {
            if (cacheManager != null) {
                if (cacheName != null && !cacheName.trim().isEmpty()) {
                    // 清理指定缓存
                    org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
                    if (cache != null) {
                        cache.clear();
                        log.info("缓存 [{}] 清理完成", cacheName);
                    } else {
                        log.warn("缓存 [{}] 不存在", cacheName);
                    }
                } else {
                    // 清理所有缓存
                    for (String name : cacheManager.getCacheNames()) {
                        org.springframework.cache.Cache cache = cacheManager.getCache(name);
                        if (cache != null) {
                            cache.clear();
                        }
                    }
                    log.info("所有缓存清理完成");
                }
            } else {
                log.warn("缓存管理器未配置");
            }
        } catch (Exception e) {
            log.error("清理缓存失败", e);
            throw new RuntimeException("清理缓存失败", e);
        }
    }

    private SystemPerformanceVo.DatabaseInfoVo buildDatabaseInfo() {
        Map<String, SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo> connectionPools = new HashMap<>();
        List<SystemPerformanceVo.DatabaseInfoVo.DeadlockVo> deadlocks = new ArrayList<>();

        try {
            if (dataSource != null) {
                // 获取连接池信息
                buildConnectionPoolInfo(connectionPools);

                // 检测数据库死锁
                detectDatabaseDeadlocks(deadlocks);
            } else {
                log.warn("数据源未配置，无法获取数据库信息");
            }
        } catch (Exception e) {
            log.error("构建数据库信息失败", e);
        }

        return SystemPerformanceVo.DatabaseInfoVo.builder()
                .connectionPools(connectionPools)
                .deadlocks(deadlocks)
                .build();
    }

    private void buildConnectionPoolInfo(Map<String, SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo> connectionPools) {
        try {
            if (dataSource instanceof com.zaxxer.hikari.HikariDataSource) {
                // HikariCP连接池
                com.zaxxer.hikari.HikariDataSource hikariDataSource = (com.zaxxer.hikari.HikariDataSource) dataSource;

                int activeConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
                int idleConnections = hikariDataSource.getHikariPoolMXBean().getIdleConnections();
                int totalConnections = hikariDataSource.getHikariPoolMXBean().getTotalConnections();
                int maxConnections = hikariDataSource.getMaximumPoolSize();
                int waitingConnections = hikariDataSource.getHikariPoolMXBean().getThreadsAwaitingConnection();

                connectionPools.put("HikariCP", SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo.builder()
                        .poolName(hikariDataSource.getPoolName() != null ? hikariDataSource.getPoolName() : "HikariCP")
                        .activeConnections(activeConnections)
                        .idleConnections(idleConnections)
                        .totalConnections(totalConnections)
                        .maxConnections(maxConnections)
                        .waitingConnections(waitingConnections)
                        .usage(maxConnections > 0 ? (double) totalConnections / maxConnections * 100 : 0.0)
                        .build());

                log.debug("HikariCP连接池信息: 活跃={}, 空闲={}, 总计={}, 最大={}, 等待={}",
                    activeConnections, idleConnections, totalConnections, maxConnections, waitingConnections);

            } else if (dataSource.getClass().getName().contains("tomcat.jdbc.pool.DataSource")) {
                // Tomcat JDBC连接池 - 使用反射避免编译时依赖
                try {
                    int activeConnections = (Integer) dataSource.getClass().getMethod("getActive").invoke(dataSource);
                    int idleConnections = (Integer) dataSource.getClass().getMethod("getIdle").invoke(dataSource);
                    int totalConnections = (Integer) dataSource.getClass().getMethod("getSize").invoke(dataSource);
                    int maxConnections = (Integer) dataSource.getClass().getMethod("getMaxActive").invoke(dataSource);

                    connectionPools.put("TomcatJDBC", SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo.builder()
                            .poolName("TomcatJDBC")
                            .activeConnections(activeConnections)
                            .idleConnections(idleConnections)
                            .totalConnections(totalConnections)
                            .maxConnections(maxConnections)
                            .waitingConnections(0) // Tomcat JDBC不直接提供等待连接数
                            .usage(maxConnections > 0 ? (double) totalConnections / maxConnections * 100 : 0.0)
                            .build());

                    log.debug("TomcatJDBC连接池信息: 活跃={}, 空闲={}, 总计={}, 最大={}",
                        activeConnections, idleConnections, totalConnections, maxConnections);
                } catch (Exception e) {
                    log.warn("获取TomcatJDBC连接池信息失败", e);
                }

            } else if (dataSource.getClass().getName().contains("druid.pool.DruidDataSource")) {
                // Druid连接池 - 使用反射避免编译时依赖
                try {
                    int activeConnections = (Integer) dataSource.getClass().getMethod("getActiveCount").invoke(dataSource);
                    int idleConnections = (Integer) dataSource.getClass().getMethod("getPoolingCount").invoke(dataSource);
                    int totalConnections = activeConnections + idleConnections;
                    int maxConnections = (Integer) dataSource.getClass().getMethod("getMaxActive").invoke(dataSource);
                    int waitingConnections = (Integer) dataSource.getClass().getMethod("getNotEmptyWaitThreadCount").invoke(dataSource);

                    connectionPools.put("Druid", SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo.builder()
                            .poolName("Druid")
                            .activeConnections(activeConnections)
                            .idleConnections(idleConnections)
                            .totalConnections(totalConnections)
                            .maxConnections(maxConnections)
                            .waitingConnections(waitingConnections)
                            .usage(maxConnections > 0 ? (double) totalConnections / maxConnections * 100 : 0.0)
                            .build());

                    log.debug("Druid连接池信息: 活跃={}, 空闲={}, 总计={}, 最大={}, 等待={}",
                        activeConnections, idleConnections, totalConnections, maxConnections, waitingConnections);
                } catch (Exception e) {
                    log.warn("获取Druid连接池信息失败", e);
                }

            } else {
                // 通用数据源，尝试获取基本信息
                try (Connection connection = dataSource.getConnection()) {
                    connectionPools.put("Generic", SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo.builder()
                            .poolName("Generic DataSource")
                            .activeConnections(1) // 当前连接
                            .idleConnections(0)
                            .totalConnections(1)
                            .maxConnections(0) // 未知
                            .waitingConnections(0)
                            .usage(0.0)
                            .build());

                    log.debug("通用数据源信息获取成功");
                }
            }
        } catch (Exception e) {
            log.error("获取连接池信息失败", e);
            // 添加错误信息到连接池
            connectionPools.put("Error", SystemPerformanceVo.DatabaseInfoVo.ConnectionPoolVo.builder()
                    .poolName("Error: " + e.getMessage())
                    .activeConnections(0)
                    .idleConnections(0)
                    .totalConnections(0)
                    .maxConnections(0)
                    .waitingConnections(0)
                    .usage(0.0)
                    .build());
        }
    }

    private void detectDatabaseDeadlocks(List<SystemPerformanceVo.DatabaseInfoVo.DeadlockVo> deadlocks) {
        try {
            if (dataSource != null) {
                try (Connection connection = dataSource.getConnection()) {
                    String databaseProductName = connection.getMetaData().getDatabaseProductName().toLowerCase();

                    if (databaseProductName.contains("mysql")) {
                        detectMySQLDeadlocks(connection, deadlocks);
                    } else if (databaseProductName.contains("postgresql")) {
                        detectPostgreSQLDeadlocks(connection, deadlocks);
                    } else if (databaseProductName.contains("oracle")) {
                        detectOracleDeadlocks(connection, deadlocks);
                    } else {
                        log.debug("数据库类型 {} 暂不支持死锁检测", databaseProductName);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("检测数据库死锁失败", e);
        }
    }

    private void detectMySQLDeadlocks(Connection connection, List<SystemPerformanceVo.DatabaseInfoVo.DeadlockVo> deadlocks) {
        try {
            // 检查MySQL死锁信息
            String sql = "SHOW ENGINE INNODB STATUS";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {

                if (rs.next()) {
                    String status = rs.getString("Status");
                    if (status != null && status.contains("LATEST DETECTED DEADLOCK")) {
                        // 解析死锁信息（简化版本）
                        deadlocks.add(SystemPerformanceVo.DatabaseInfoVo.DeadlockVo.builder()
                                .deadlockId("MySQL-" + System.currentTimeMillis())
                                .threads(Arrays.asList("Thread-1", "Thread-2"))
                                .deadlockTime(LocalDateTime.now())
                                .description("检测到MySQL死锁，详细信息请查看InnoDB状态")
                                .build());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("MySQL死锁检测失败", e);
        }
    }

    private void detectPostgreSQLDeadlocks(Connection connection, List<SystemPerformanceVo.DatabaseInfoVo.DeadlockVo> deadlocks) {
        try {
            // 检查PostgreSQL锁等待
            String sql = "SELECT pid, usename, query, state FROM pg_stat_activity WHERE state = 'active' AND wait_event_type = 'Lock'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {

                List<String> waitingThreads = new ArrayList<>();
                while (rs.next()) {
                    waitingThreads.add("PID-" + rs.getInt("pid"));
                }

                if (!waitingThreads.isEmpty()) {
                    deadlocks.add(SystemPerformanceVo.DatabaseInfoVo.DeadlockVo.builder()
                            .deadlockId("PostgreSQL-" + System.currentTimeMillis())
                            .threads(waitingThreads)
                            .deadlockTime(LocalDateTime.now())
                            .description("检测到PostgreSQL锁等待，可能存在死锁风险")
                            .build());
                }
            }
        } catch (Exception e) {
            log.debug("PostgreSQL死锁检测失败", e);
        }
    }

    private void detectOracleDeadlocks(Connection connection, List<SystemPerformanceVo.DatabaseInfoVo.DeadlockVo> deadlocks) {
        try {
            // 检查Oracle死锁
            String sql = "SELECT s1.username, s1.sid, s1.serial#, s2.username, s2.sid, s2.serial# " +
                        "FROM v$lock l1, v$session s1, v$lock l2, v$session s2 " +
                        "WHERE s1.sid = l1.sid AND s2.sid = l2.sid " +
                        "AND l1.block = 1 AND l2.request > 0 " +
                        "AND l1.id1 = l2.id1 AND l1.id2 = l2.id2";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {

                List<String> blockedSessions = new ArrayList<>();
                while (rs.next()) {
                    blockedSessions.add("SID-" + rs.getInt(2) + "(" + rs.getString(1) + ")");
                }

                if (!blockedSessions.isEmpty()) {
                    deadlocks.add(SystemPerformanceVo.DatabaseInfoVo.DeadlockVo.builder()
                            .deadlockId("Oracle-" + System.currentTimeMillis())
                            .threads(blockedSessions)
                            .deadlockTime(LocalDateTime.now())
                            .description("检测到Oracle会话阻塞，可能存在死锁")
                            .build());
                }
            }
        } catch (Exception e) {
            log.debug("Oracle死锁检测失败", e);
        }
    }

    private SystemPerformanceVo.ThreadPoolInfoVo buildThreadPoolInfo() {
        Map<String, SystemPerformanceVo.ThreadPoolInfoVo.ThreadPoolDetailVo> threadPools = new HashMap<>();

        try {
            // 这里可以添加具体的线程池监控逻辑
            threadPools.put("default", SystemPerformanceVo.ThreadPoolInfoVo.ThreadPoolDetailVo.builder()
                    .poolName("default")
                    .corePoolSize(10)
                    .maximumPoolSize(50)
                    .poolSize(threadBean.getThreadCount())
                    .activeCount(threadBean.getThreadCount() - threadBean.getDaemonThreadCount())
                    .queueSize(0)
                    .completedTaskCount(0L)
                    .taskCount(0L)
                    .build());
        } catch (Exception e) {
            log.warn("构建线程池信息失败", e);
        }

        return SystemPerformanceVo.ThreadPoolInfoVo.builder()
                .threadPools(threadPools)
                .build();
    }

    private SystemPerformanceVo.CacheInfoVo buildCacheInfo() {
        String redisStatus = "未配置";
        Map<String, SystemPerformanceVo.CacheInfoVo.CacheStatsVo> cacheStats = new HashMap<>();

        try {
            // 获取Redis状态和统计信息
            redisStatus = getRedisStatus();

            // 获取缓存管理器统计信息
            getCacheManagerStats(cacheStats);

        } catch (Exception e) {
            log.error("构建缓存信息失败", e);
            redisStatus = "连接失败: " + e.getMessage();
        }

        return SystemPerformanceVo.CacheInfoVo.builder()
                .redisStatus(redisStatus)
                .cacheStats(cacheStats)
                .build();
    }

    private String getRedisStatus() {
        if (redisTemplate == null) {
            return "未配置";
        }

        try {
            RedisConnection connection = redisTemplate.getConnectionFactory().getConnection();

            // 测试连接
            String pong = connection.ping();
            if (!"PONG".equals(pong)) {
                connection.close();
                return "连接异常: ping响应异常";
            }

            // 获取Redis信息
            Properties info = connection.info();
            StringBuilder statusBuilder = new StringBuilder("连接正常");

            if (info != null) {
                String version = info.getProperty("redis_version");
                String mode = info.getProperty("redis_mode");
                String usedMemory = info.getProperty("used_memory_human");
                String connectedClients = info.getProperty("connected_clients");

                if (version != null) statusBuilder.append(", 版本: ").append(version);
                if (mode != null) statusBuilder.append(", 模式: ").append(mode);
                if (usedMemory != null) statusBuilder.append(", 内存: ").append(usedMemory);
                if (connectedClients != null) statusBuilder.append(", 客户端: ").append(connectedClients);
            }

            connection.close();
            return statusBuilder.toString();

        } catch (Exception e) {
            log.warn("获取Redis状态失败", e);
            return "连接失败: " + e.getMessage();
        }
    }

    private void getCacheManagerStats(Map<String, SystemPerformanceVo.CacheInfoVo.CacheStatsVo> cacheStats) {
        if (cacheManager == null) {
            log.debug("缓存管理器未配置");
            return;
        }

        try {
            Collection<String> cacheNames = cacheManager.getCacheNames();
            log.debug("发现缓存: {}", cacheNames);

            for (String cacheName : cacheNames) {
                try {
                    org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
                    if (cache != null) {
                        SystemPerformanceVo.CacheInfoVo.CacheStatsVo statsVo = buildCacheStatsVo(cacheName, cache);
                        cacheStats.put(cacheName, statsVo);
                    }
                } catch (Exception e) {
                    log.warn("获取缓存 {} 统计信息失败", cacheName, e);
                    // 添加错误信息
                    cacheStats.put(cacheName, SystemPerformanceVo.CacheInfoVo.CacheStatsVo.builder()
                            .cacheName(cacheName)
                            .size(0L)
                            .hitCount(0L)
                            .missCount(0L)
                            .hitRate(0.0)
                            .build());
                }
            }
        } catch (Exception e) {
            log.error("获取缓存管理器统计信息失败", e);
        }
    }

    private SystemPerformanceVo.CacheInfoVo.CacheStatsVo buildCacheStatsVo(String cacheName, org.springframework.cache.Cache cache) {
        long size = 0L;
        long hitCount = 0L;
        long missCount = 0L;
        double hitRate = 0.0;

        try {
            // 尝试获取不同类型缓存的统计信息
            Object nativeCache = cache.getNativeCache();

            if (nativeCache.getClass().getName().contains("caffeine.cache.Cache")) {
                // Caffeine缓存 - 使用反射避免编译时依赖
                try {
                    size = (Long) nativeCache.getClass().getMethod("estimatedSize").invoke(nativeCache);
                    Object stats = nativeCache.getClass().getMethod("stats").invoke(nativeCache);
                    if (stats != null) {
                        hitCount = (Long) stats.getClass().getMethod("hitCount").invoke(stats);
                        missCount = (Long) stats.getClass().getMethod("missCount").invoke(stats);
                        hitRate = (Double) stats.getClass().getMethod("hitRate").invoke(stats) * 100;
                    }

                    log.debug("Caffeine缓存 {} 统计: size={}, hit={}, miss={}, hitRate={}%",
                        cacheName, size, hitCount, missCount, hitRate);
                } catch (Exception e) {
                    log.debug("获取Caffeine缓存统计失败", e);
                }

            } else if (nativeCache.getClass().getName().contains("google.common.cache.Cache")) {
                // Guava缓存 - 使用反射避免编译时依赖
                try {
                    size = (Long) nativeCache.getClass().getMethod("size").invoke(nativeCache);
                    Object stats = nativeCache.getClass().getMethod("stats").invoke(nativeCache);
                    if (stats != null) {
                        hitCount = (Long) stats.getClass().getMethod("hitCount").invoke(stats);
                        missCount = (Long) stats.getClass().getMethod("missCount").invoke(stats);
                        hitRate = (Double) stats.getClass().getMethod("hitRate").invoke(stats) * 100;
                    }

                    log.debug("Guava缓存 {} 统计: size={}, hit={}, miss={}, hitRate={}%",
                        cacheName, size, hitCount, missCount, hitRate);
                } catch (Exception e) {
                    log.debug("获取Guava缓存统计失败", e);
                }

            } else if (nativeCache instanceof java.util.concurrent.ConcurrentMap) {
                // ConcurrentMap缓存
                java.util.concurrent.ConcurrentMap<?, ?> mapCache =
                    (java.util.concurrent.ConcurrentMap<?, ?>) nativeCache;
                size = mapCache.size();

                log.debug("ConcurrentMap缓存 {} 统计: size={}", cacheName, size);

            } else {
                log.debug("未知缓存类型: {}, 缓存名称: {}", nativeCache.getClass().getName(), cacheName);
            }

        } catch (Exception e) {
            log.warn("获取缓存 {} 详细统计信息失败", cacheName, e);
        }

        return SystemPerformanceVo.CacheInfoVo.CacheStatsVo.builder()
                .cacheName(cacheName)
                .size(size)
                .hitCount(hitCount)
                .missCount(missCount)
                .hitRate(hitRate)
                .build();
    }

    private SystemPerformanceVo.ProcessInfoVo buildProcessInfo() {
        String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        List<SystemPerformanceVo.ProcessInfoVo.ProcessDetailVo> javaProcesses = new ArrayList<>();
        Integer totalProcesses = 0;

        try {
            // 获取Java进程信息
            Process process = Runtime.getRuntime().exec("jps -l");
            Scanner scanner = new Scanner(process.getInputStream());
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine();
                if (!line.trim().isEmpty() && !line.contains("jps")) {
                    String[] parts = line.split(" ", 2);
                    if (parts.length >= 2) {
                        javaProcesses.add(SystemPerformanceVo.ProcessInfoVo.ProcessDetailVo.builder()
                                .pid(parts[0])
                                .name(parts[1])
                                .cpuUsage(0.0)
                                .memoryUsage(0L)
                                .startTime(LocalDateTime.now())
                                .build());
                    }
                }
            }
            scanner.close();
        } catch (Exception e) {
            log.warn("获取Java进程信息失败", e);
        }

        return SystemPerformanceVo.ProcessInfoVo.builder()
                .currentPid(currentPid)
                .javaProcesses(javaProcesses)
                .totalProcesses(totalProcesses)
                .build();
    }
}
