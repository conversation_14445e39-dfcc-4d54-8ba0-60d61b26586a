package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TemplateFormDvpRuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TemplateFormDvpRuleExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCodeIsNull() {
            addCriterion("code is null");
            return (Criteria) this;
        }

        public Criteria andCodeIsNotNull() {
            addCriterion("code is not null");
            return (Criteria) this;
        }

        public Criteria andCodeEqualTo(String value) {
            addCriterion("code =", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotEqualTo(String value) {
            addCriterion("code <>", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThan(String value) {
            addCriterion("code >", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeGreaterThanOrEqualTo(String value) {
            addCriterion("code >=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThan(String value) {
            addCriterion("code <", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLessThanOrEqualTo(String value) {
            addCriterion("code <=", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeLike(String value) {
            addCriterion("code like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotLike(String value) {
            addCriterion("code not like", value, "code");
            return (Criteria) this;
        }

        public Criteria andCodeIn(List<String> values) {
            addCriterion("code in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotIn(List<String> values) {
            addCriterion("code not in", values, "code");
            return (Criteria) this;
        }

        public Criteria andCodeBetween(String value1, String value2) {
            addCriterion("code between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCodeNotBetween(String value1, String value2) {
            addCriterion("code not between", value1, value2, "code");
            return (Criteria) this;
        }

        public Criteria andCheckNameIsNull() {
            addCriterion("check_name is null");
            return (Criteria) this;
        }

        public Criteria andCheckNameIsNotNull() {
            addCriterion("check_name is not null");
            return (Criteria) this;
        }

        public Criteria andCheckNameEqualTo(String value) {
            addCriterion("check_name =", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameNotEqualTo(String value) {
            addCriterion("check_name <>", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameGreaterThan(String value) {
            addCriterion("check_name >", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameGreaterThanOrEqualTo(String value) {
            addCriterion("check_name >=", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameLessThan(String value) {
            addCriterion("check_name <", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameLessThanOrEqualTo(String value) {
            addCriterion("check_name <=", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameLike(String value) {
            addCriterion("check_name like", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameNotLike(String value) {
            addCriterion("check_name not like", value, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameIn(List<String> values) {
            addCriterion("check_name in", values, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameNotIn(List<String> values) {
            addCriterion("check_name not in", values, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameBetween(String value1, String value2) {
            addCriterion("check_name between", value1, value2, "checkName");
            return (Criteria) this;
        }

        public Criteria andCheckNameNotBetween(String value1, String value2) {
            addCriterion("check_name not between", value1, value2, "checkName");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNull() {
            addCriterion("plan_id is null");
            return (Criteria) this;
        }

        public Criteria andPlanIdIsNotNull() {
            addCriterion("plan_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlanIdEqualTo(Long value) {
            addCriterion("plan_id =", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotEqualTo(Long value) {
            addCriterion("plan_id <>", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThan(Long value) {
            addCriterion("plan_id >", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdGreaterThanOrEqualTo(Long value) {
            addCriterion("plan_id >=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThan(Long value) {
            addCriterion("plan_id <", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdLessThanOrEqualTo(Long value) {
            addCriterion("plan_id <=", value, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdIn(List<Long> values) {
            addCriterion("plan_id in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotIn(List<Long> values) {
            addCriterion("plan_id not in", values, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdBetween(Long value1, Long value2) {
            addCriterion("plan_id between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andPlanIdNotBetween(Long value1, Long value2) {
            addCriterion("plan_id not between", value1, value2, "planId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNull() {
            addCriterion("visit_id is null");
            return (Criteria) this;
        }

        public Criteria andVisitIdIsNotNull() {
            addCriterion("visit_id is not null");
            return (Criteria) this;
        }

        public Criteria andVisitIdEqualTo(Long value) {
            addCriterion("visit_id =", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotEqualTo(Long value) {
            addCriterion("visit_id <>", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThan(Long value) {
            addCriterion("visit_id >", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdGreaterThanOrEqualTo(Long value) {
            addCriterion("visit_id >=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThan(Long value) {
            addCriterion("visit_id <", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdLessThanOrEqualTo(Long value) {
            addCriterion("visit_id <=", value, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdIn(List<Long> values) {
            addCriterion("visit_id in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotIn(List<Long> values) {
            addCriterion("visit_id not in", values, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdBetween(Long value1, Long value2) {
            addCriterion("visit_id between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andVisitIdNotBetween(Long value1, Long value2) {
            addCriterion("visit_id not between", value1, value2, "visitId");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNull() {
            addCriterion("form_id is null");
            return (Criteria) this;
        }

        public Criteria andFormIdIsNotNull() {
            addCriterion("form_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormIdEqualTo(Long value) {
            addCriterion("form_id =", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotEqualTo(Long value) {
            addCriterion("form_id <>", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThan(Long value) {
            addCriterion("form_id >", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_id >=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThan(Long value) {
            addCriterion("form_id <", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdLessThanOrEqualTo(Long value) {
            addCriterion("form_id <=", value, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdIn(List<Long> values) {
            addCriterion("form_id in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotIn(List<Long> values) {
            addCriterion("form_id not in", values, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdBetween(Long value1, Long value2) {
            addCriterion("form_id between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andFormIdNotBetween(Long value1, Long value2) {
            addCriterion("form_id not between", value1, value2, "formId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Long value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Long value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Long value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Long value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Long> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Long> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Long value1, Long value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNull() {
            addCriterion("form_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIsNotNull() {
            addCriterion("form_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdEqualTo(Long value) {
            addCriterion("form_detail_id =", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotEqualTo(Long value) {
            addCriterion("form_detail_id <>", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThan(Long value) {
            addCriterion("form_detail_id >", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_detail_id >=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThan(Long value) {
            addCriterion("form_detail_id <", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("form_detail_id <=", value, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdIn(List<Long> values) {
            addCriterion("form_detail_id in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotIn(List<Long> values) {
            addCriterion("form_detail_id not in", values, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdBetween(Long value1, Long value2) {
            addCriterion("form_detail_id between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("form_detail_id not between", value1, value2, "formDetailId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNull() {
            addCriterion("form_table_id is null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIsNotNull() {
            addCriterion("form_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormTableIdEqualTo(Long value) {
            addCriterion("form_table_id =", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotEqualTo(Long value) {
            addCriterion("form_table_id <>", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThan(Long value) {
            addCriterion("form_table_id >", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("form_table_id >=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThan(Long value) {
            addCriterion("form_table_id <", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdLessThanOrEqualTo(Long value) {
            addCriterion("form_table_id <=", value, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdIn(List<Long> values) {
            addCriterion("form_table_id in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotIn(List<Long> values) {
            addCriterion("form_table_id not in", values, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdBetween(Long value1, Long value2) {
            addCriterion("form_table_id between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andFormTableIdNotBetween(Long value1, Long value2) {
            addCriterion("form_table_id not between", value1, value2, "formTableId");
            return (Criteria) this;
        }

        public Criteria andVariableNameIsNull() {
            addCriterion("variable_name is null");
            return (Criteria) this;
        }

        public Criteria andVariableNameIsNotNull() {
            addCriterion("variable_name is not null");
            return (Criteria) this;
        }

        public Criteria andVariableNameEqualTo(String value) {
            addCriterion("variable_name =", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotEqualTo(String value) {
            addCriterion("variable_name <>", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameGreaterThan(String value) {
            addCriterion("variable_name >", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameGreaterThanOrEqualTo(String value) {
            addCriterion("variable_name >=", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLessThan(String value) {
            addCriterion("variable_name <", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLessThanOrEqualTo(String value) {
            addCriterion("variable_name <=", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameLike(String value) {
            addCriterion("variable_name like", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotLike(String value) {
            addCriterion("variable_name not like", value, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameIn(List<String> values) {
            addCriterion("variable_name in", values, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotIn(List<String> values) {
            addCriterion("variable_name not in", values, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameBetween(String value1, String value2) {
            addCriterion("variable_name between", value1, value2, "variableName");
            return (Criteria) this;
        }

        public Criteria andVariableNameNotBetween(String value1, String value2) {
            addCriterion("variable_name not between", value1, value2, "variableName");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdIsNull() {
            addCriterion("form_relation_id is null");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdIsNotNull() {
            addCriterion("form_relation_id is not null");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdEqualTo(String value) {
            addCriterion("form_relation_id =", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdNotEqualTo(String value) {
            addCriterion("form_relation_id <>", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdGreaterThan(String value) {
            addCriterion("form_relation_id >", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdGreaterThanOrEqualTo(String value) {
            addCriterion("form_relation_id >=", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdLessThan(String value) {
            addCriterion("form_relation_id <", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdLessThanOrEqualTo(String value) {
            addCriterion("form_relation_id <=", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdLike(String value) {
            addCriterion("form_relation_id like", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdNotLike(String value) {
            addCriterion("form_relation_id not like", value, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdIn(List<String> values) {
            addCriterion("form_relation_id in", values, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdNotIn(List<String> values) {
            addCriterion("form_relation_id not in", values, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdBetween(String value1, String value2) {
            addCriterion("form_relation_id between", value1, value2, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andFormRelationIdNotBetween(String value1, String value2) {
            addCriterion("form_relation_id not between", value1, value2, "formRelationId");
            return (Criteria) this;
        }

        public Criteria andCustomTableIsNull() {
            addCriterion("custom_table is null");
            return (Criteria) this;
        }

        public Criteria andCustomTableIsNotNull() {
            addCriterion("custom_table is not null");
            return (Criteria) this;
        }

        public Criteria andCustomTableEqualTo(Boolean value) {
            addCriterion("custom_table =", value, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableNotEqualTo(Boolean value) {
            addCriterion("custom_table <>", value, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableGreaterThan(Boolean value) {
            addCriterion("custom_table >", value, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("custom_table >=", value, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableLessThan(Boolean value) {
            addCriterion("custom_table <", value, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableLessThanOrEqualTo(Boolean value) {
            addCriterion("custom_table <=", value, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableIn(List<Boolean> values) {
            addCriterion("custom_table in", values, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableNotIn(List<Boolean> values) {
            addCriterion("custom_table not in", values, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableBetween(Boolean value1, Boolean value2) {
            addCriterion("custom_table between", value1, value2, "customTable");
            return (Criteria) this;
        }

        public Criteria andCustomTableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("custom_table not between", value1, value2, "customTable");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexIsNull() {
            addCriterion("table_row_index is null");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexIsNotNull() {
            addCriterion("table_row_index is not null");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexEqualTo(String value) {
            addCriterion("table_row_index =", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexNotEqualTo(String value) {
            addCriterion("table_row_index <>", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexGreaterThan(String value) {
            addCriterion("table_row_index >", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexGreaterThanOrEqualTo(String value) {
            addCriterion("table_row_index >=", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexLessThan(String value) {
            addCriterion("table_row_index <", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexLessThanOrEqualTo(String value) {
            addCriterion("table_row_index <=", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexLike(String value) {
            addCriterion("table_row_index like", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexNotLike(String value) {
            addCriterion("table_row_index not like", value, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexIn(List<String> values) {
            addCriterion("table_row_index in", values, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexNotIn(List<String> values) {
            addCriterion("table_row_index not in", values, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexBetween(String value1, String value2) {
            addCriterion("table_row_index between", value1, value2, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andTableRowIndexNotBetween(String value1, String value2) {
            addCriterion("table_row_index not between", value1, value2, "tableRowIndex");
            return (Criteria) this;
        }

        public Criteria andQueryTypeIsNull() {
            addCriterion("query_type is null");
            return (Criteria) this;
        }

        public Criteria andQueryTypeIsNotNull() {
            addCriterion("query_type is not null");
            return (Criteria) this;
        }

        public Criteria andQueryTypeEqualTo(String value) {
            addCriterion("query_type =", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeNotEqualTo(String value) {
            addCriterion("query_type <>", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeGreaterThan(String value) {
            addCriterion("query_type >", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeGreaterThanOrEqualTo(String value) {
            addCriterion("query_type >=", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeLessThan(String value) {
            addCriterion("query_type <", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeLessThanOrEqualTo(String value) {
            addCriterion("query_type <=", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeLike(String value) {
            addCriterion("query_type like", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeNotLike(String value) {
            addCriterion("query_type not like", value, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeIn(List<String> values) {
            addCriterion("query_type in", values, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeNotIn(List<String> values) {
            addCriterion("query_type not in", values, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeBetween(String value1, String value2) {
            addCriterion("query_type between", value1, value2, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryTypeNotBetween(String value1, String value2) {
            addCriterion("query_type not between", value1, value2, "queryType");
            return (Criteria) this;
        }

        public Criteria andQueryMethodIsNull() {
            addCriterion("query_method is null");
            return (Criteria) this;
        }

        public Criteria andQueryMethodIsNotNull() {
            addCriterion("query_method is not null");
            return (Criteria) this;
        }

        public Criteria andQueryMethodEqualTo(String value) {
            addCriterion("query_method =", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotEqualTo(String value) {
            addCriterion("query_method <>", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodGreaterThan(String value) {
            addCriterion("query_method >", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodGreaterThanOrEqualTo(String value) {
            addCriterion("query_method >=", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodLessThan(String value) {
            addCriterion("query_method <", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodLessThanOrEqualTo(String value) {
            addCriterion("query_method <=", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodLike(String value) {
            addCriterion("query_method like", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotLike(String value) {
            addCriterion("query_method not like", value, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodIn(List<String> values) {
            addCriterion("query_method in", values, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotIn(List<String> values) {
            addCriterion("query_method not in", values, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodBetween(String value1, String value2) {
            addCriterion("query_method between", value1, value2, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andQueryMethodNotBetween(String value1, String value2) {
            addCriterion("query_method not between", value1, value2, "queryMethod");
            return (Criteria) this;
        }

        public Criteria andFunctionValueIsNull() {
            addCriterion("function_value is null");
            return (Criteria) this;
        }

        public Criteria andFunctionValueIsNotNull() {
            addCriterion("function_value is not null");
            return (Criteria) this;
        }

        public Criteria andFunctionValueEqualTo(String value) {
            addCriterion("function_value =", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueNotEqualTo(String value) {
            addCriterion("function_value <>", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueGreaterThan(String value) {
            addCriterion("function_value >", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueGreaterThanOrEqualTo(String value) {
            addCriterion("function_value >=", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueLessThan(String value) {
            addCriterion("function_value <", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueLessThanOrEqualTo(String value) {
            addCriterion("function_value <=", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueLike(String value) {
            addCriterion("function_value like", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueNotLike(String value) {
            addCriterion("function_value not like", value, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueIn(List<String> values) {
            addCriterion("function_value in", values, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueNotIn(List<String> values) {
            addCriterion("function_value not in", values, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueBetween(String value1, String value2) {
            addCriterion("function_value between", value1, value2, "functionValue");
            return (Criteria) this;
        }

        public Criteria andFunctionValueNotBetween(String value1, String value2) {
            addCriterion("function_value not between", value1, value2, "functionValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueIsNull() {
            addCriterion("arithmetic_value is null");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueIsNotNull() {
            addCriterion("arithmetic_value is not null");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueEqualTo(String value) {
            addCriterion("arithmetic_value =", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueNotEqualTo(String value) {
            addCriterion("arithmetic_value <>", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueGreaterThan(String value) {
            addCriterion("arithmetic_value >", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueGreaterThanOrEqualTo(String value) {
            addCriterion("arithmetic_value >=", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueLessThan(String value) {
            addCriterion("arithmetic_value <", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueLessThanOrEqualTo(String value) {
            addCriterion("arithmetic_value <=", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueLike(String value) {
            addCriterion("arithmetic_value like", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueNotLike(String value) {
            addCriterion("arithmetic_value not like", value, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueIn(List<String> values) {
            addCriterion("arithmetic_value in", values, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueNotIn(List<String> values) {
            addCriterion("arithmetic_value not in", values, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueBetween(String value1, String value2) {
            addCriterion("arithmetic_value between", value1, value2, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andArithmeticValueNotBetween(String value1, String value2) {
            addCriterion("arithmetic_value not between", value1, value2, "arithmeticValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueIsNull() {
            addCriterion("condition_value is null");
            return (Criteria) this;
        }

        public Criteria andConditionValueIsNotNull() {
            addCriterion("condition_value is not null");
            return (Criteria) this;
        }

        public Criteria andConditionValueEqualTo(String value) {
            addCriterion("condition_value =", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueNotEqualTo(String value) {
            addCriterion("condition_value <>", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueGreaterThan(String value) {
            addCriterion("condition_value >", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueGreaterThanOrEqualTo(String value) {
            addCriterion("condition_value >=", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueLessThan(String value) {
            addCriterion("condition_value <", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueLessThanOrEqualTo(String value) {
            addCriterion("condition_value <=", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueLike(String value) {
            addCriterion("condition_value like", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueNotLike(String value) {
            addCriterion("condition_value not like", value, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueIn(List<String> values) {
            addCriterion("condition_value in", values, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueNotIn(List<String> values) {
            addCriterion("condition_value not in", values, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueBetween(String value1, String value2) {
            addCriterion("condition_value between", value1, value2, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andConditionValueNotBetween(String value1, String value2) {
            addCriterion("condition_value not between", value1, value2, "conditionValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueIsNull() {
            addCriterion("target_value is null");
            return (Criteria) this;
        }

        public Criteria andTargetValueIsNotNull() {
            addCriterion("target_value is not null");
            return (Criteria) this;
        }

        public Criteria andTargetValueEqualTo(String value) {
            addCriterion("target_value =", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueNotEqualTo(String value) {
            addCriterion("target_value <>", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueGreaterThan(String value) {
            addCriterion("target_value >", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueGreaterThanOrEqualTo(String value) {
            addCriterion("target_value >=", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueLessThan(String value) {
            addCriterion("target_value <", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueLessThanOrEqualTo(String value) {
            addCriterion("target_value <=", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueLike(String value) {
            addCriterion("target_value like", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueNotLike(String value) {
            addCriterion("target_value not like", value, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueIn(List<String> values) {
            addCriterion("target_value in", values, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueNotIn(List<String> values) {
            addCriterion("target_value not in", values, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueBetween(String value1, String value2) {
            addCriterion("target_value between", value1, value2, "targetValue");
            return (Criteria) this;
        }

        public Criteria andTargetValueNotBetween(String value1, String value2) {
            addCriterion("target_value not between", value1, value2, "targetValue");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIsNull() {
            addCriterion("rule_type is null");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIsNotNull() {
            addCriterion("rule_type is not null");
            return (Criteria) this;
        }

        public Criteria andRuleTypeEqualTo(String value) {
            addCriterion("rule_type =", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotEqualTo(String value) {
            addCriterion("rule_type <>", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThan(String value) {
            addCriterion("rule_type >", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("rule_type >=", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThan(String value) {
            addCriterion("rule_type <", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThanOrEqualTo(String value) {
            addCriterion("rule_type <=", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLike(String value) {
            addCriterion("rule_type like", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotLike(String value) {
            addCriterion("rule_type not like", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIn(List<String> values) {
            addCriterion("rule_type in", values, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotIn(List<String> values) {
            addCriterion("rule_type not in", values, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeBetween(String value1, String value2) {
            addCriterion("rule_type between", value1, value2, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotBetween(String value1, String value2) {
            addCriterion("rule_type not between", value1, value2, "ruleType");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("content is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("content is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("content =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("content <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("content >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("content >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("content <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("content <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("content like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("content not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("content in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("content not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("content between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("content not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNull() {
            addCriterion("enabled is null");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNotNull() {
            addCriterion("enabled is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledEqualTo(Boolean value) {
            addCriterion("enabled =", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotEqualTo(Boolean value) {
            addCriterion("enabled <>", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThan(Boolean value) {
            addCriterion("enabled >", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enabled >=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThan(Boolean value) {
            addCriterion("enabled <", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("enabled <=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledIn(List<Boolean> values) {
            addCriterion("enabled in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotIn(List<Boolean> values) {
            addCriterion("enabled not in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("enabled between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enabled not between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(String value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(String value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(String value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(String value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLike(String value) {
            addCriterion("update_user_id like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotLike(String value) {
            addCriterion("update_user_id not like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<String> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<String> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(String value1, String value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}