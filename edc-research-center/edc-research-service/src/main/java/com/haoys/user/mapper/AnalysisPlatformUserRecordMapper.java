package com.haoys.user.mapper;

import com.haoys.user.model.AnalysisPlatformUserRecord;
import com.haoys.user.model.AnalysisPlatformUserRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AnalysisPlatformUserRecordMapper {
    long countByExample(AnalysisPlatformUserRecordExample example);

    int deleteByExample(AnalysisPlatformUserRecordExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AnalysisPlatformUserRecord record);

    int insertSelective(AnalysisPlatformUserRecord record);

    List<AnalysisPlatformUserRecord> selectByExample(AnalysisPlatformUserRecordExample example);

    AnalysisPlatformUserRecord selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AnalysisPlatformUserRecord record, @Param("example") AnalysisPlatformUserRecordExample example);

    int updateByExample(@Param("record") AnalysisPlatformUserRecord record, @Param("example") AnalysisPlatformUserRecordExample example);

    int updateByPrimaryKeySelective(AnalysisPlatformUserRecord record);

    int updateByPrimaryKey(AnalysisPlatformUserRecord record);

    AnalysisPlatformUserRecord getPlatformUserAccountByUserName(String username, String systemPlatformId);
}