package com.haoys.user.service;

import com.haoys.user.common.api.CommonPage;
import com.haoys.user.domain.param.project.ProjectAnnouncementParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.domain.vo.project.ProjectAnnouncementVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.testee.UploadRichTextFileVo;
import com.haoys.user.model.ProjectAnnouncement;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface ProjectAnnouncementService {

    CommonPage<ProjectAnnouncementVo> selectProjectAnnouncementList(ProjectAnnouncement projectAnnouncement, Integer pageNum, Integer pageSize);

    ProjectAnnouncementVo selectProjectAnnouncementById(Long id);

    List<OrganizationVo> getProjectOrgList(String projectId);

    int addProjectAnnouncement(String userId, ProjectAnnouncementParam projectAnnouncementParam);

    int editPublish(ProjectAnnouncement projectAnnouncement);

    int editProjectAnnouncement(String userId, ProjectAnnouncementParam projectAnnouncementParam);

    int deleteProjectAnnouncement(Long id);

    List<UploadFileResultVo> saveUpload(MultipartFile multipartFiles) throws IOException;

    CommonPage<ProjectAnnouncementVo> selectProjectAnnouncementEprList(ProjectAnnouncement projectAnnouncement, Integer pageNum, Integer pageSize);

    UploadRichTextFileVo uploadRichTextFile(MultipartFile multipartFiles) throws IOException;
}
