package com.haoys.user.mapper;

import com.haoys.user.model.DiseaseDatabaseAuth;
import com.haoys.user.model.DiseaseDatabaseAuthExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DiseaseDatabaseAuthMapper {
    long countByExample(DiseaseDatabaseAuthExample example);

    int deleteByExample(DiseaseDatabaseAuthExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DiseaseDatabaseAuth record);

    int insertSelective(DiseaseDatabaseAuth record);

    List<DiseaseDatabaseAuth> selectByExample(DiseaseDatabaseAuthExample example);

    DiseaseDatabaseAuth selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DiseaseDatabaseAuth record, @Param("example") DiseaseDatabaseAuthExample example);

    int updateByExample(@Param("record") DiseaseDatabaseAuth record, @Param("example") DiseaseDatabaseAuthExample example);

    int updateByPrimaryKeySelective(DiseaseDatabaseAuth record);

    int updateByPrimaryKey(DiseaseDatabaseAuth record);
}