package com.haoys.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haoys.user.domain.param.testee.ProjectTesteeExportParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Arrays;

/**
 * PDF导出修复验证测试
 * 
 * 修复内容：
 * 1. 添加常量控制优化版本导出
 * 2. 增强JSON解析的错误处理
 * 3. 修复图片路径为空的问题
 * 4. 增强错误处理和日志记录
 * 
 * <AUTHOR>
 */
@Slf4j
public class ProjectTesteeExportFixTest {

    @Test
    public void testOptimizedVersionControl() {
        System.out.println("=== 测试优化版本控制 ===");
        
        // 测试常量配置
        boolean useOptimizedByDefault = ProjectTesteeExportManageImpl.isUseOptimizedVersionByDefault();
        System.out.println("默认使用优化版本: " + useOptimizedByDefault);
        
        // 测试不同参与者数量的场景
        testOptimizedVersionScenario("单个参与者", 1);
        testOptimizedVersionScenario("少量参与者", 3);
        testOptimizedVersionScenario("中等数量参与者", 10);
        testOptimizedVersionScenario("大量参与者", 50);
        
        System.out.println("✅ 优化版本控制测试完成");
    }
    
    private void testOptimizedVersionScenario(String scenarioName, int testeeCount) {
        ProjectTesteeExportParam param = new ProjectTesteeExportParam();
        param.setProjectId("TEST_PROJECT");
        param.setOrgId("TEST_ORG");
        
        // 创建测试参与者列表
        String[] testeeIds = new String[testeeCount];
        for (int i = 0; i < testeeCount; i++) {
            testeeIds[i] = "TESTEE_" + String.format("%04d", i + 1);
        }
        param.setTesteeIds(Arrays.asList(testeeIds));
        
        System.out.println(String.format("%s: %d个参与者", scenarioName, testeeCount));
    }

    @Test
    public void testJsonParsingErrorHandling() {
        System.out.println("=== 测试JSON解析错误处理 ===");
        
        // 测试各种异常JSON格式
        testJsonParsing("正常JSON", "{\"imgUrl\":\"http://example.com/image.jpg\"}");
        testJsonParsing("空JSON", "{}");
        testJsonParsing("缺少imgUrl", "{\"otherField\":\"value\"}");
        testJsonParsing("imgUrl为空", "{\"imgUrl\":\"\"}");
        testJsonParsing("imgUrl为null", "{\"imgUrl\":null}");
        testJsonParsing("无效JSON", "invalid json string");
        testJsonParsing("空字符串", "");
        testJsonParsing("null值", null);
        testJsonParsing("非JSON字符串", "just a string");
        testJsonParsing("语法错误JSON", "{\"imgUrl\":\"value\",}");
        
        System.out.println("✅ JSON解析错误处理测试完成");
    }
    
    private void testJsonParsing(String testName, String jsonString) {
        System.out.println("测试 " + testName + ": " + jsonString);
        
        try {
            if (jsonString != null && !jsonString.trim().isEmpty()) {
                try {
                    JSONObject object = JSON.parseObject(jsonString);
                    if (object != null && object.containsKey("imgUrl")) {
                        String imgUrl = object.getString("imgUrl");
                        if (imgUrl != null && !imgUrl.trim().isEmpty()) {
                            System.out.println("  ✅ 成功解析，imgUrl: " + imgUrl);
                        } else {
                            System.out.println("  ⚠️ imgUrl为空");
                        }
                    } else {
                        System.out.println("  ⚠️ JSON中缺少imgUrl属性");
                    }
                } catch (Exception e) {
                    System.out.println("  ❌ JSON解析失败: " + e.getMessage());
                }
            } else {
                System.out.println("  ⚠️ 输入为空");
            }
        } catch (Exception e) {
            System.out.println("  ❌ 处理异常: " + e.getMessage());
        }
        System.out.println();
    }

    @Test
    public void testImagePathHandling() {
        System.out.println("=== 测试图片路径处理 ===");
        
        // 模拟不同的图片路径情况
        testImagePath("完整URL", "http://example.com/image.jpg", "/upload/path/image.jpg");
        testImagePath("相对路径", null, "/upload/path/image.jpg");
        testImagePath("fileUrl为空", "", "/upload/path/image.jpg");
        testImagePath("uploadPath为空", "http://example.com/image.jpg", null);
        testImagePath("两者都为空", null, null);
        testImagePath("两者都为空字符串", "", "");
        
        System.out.println("✅ 图片路径处理测试完成");
    }
    
    private void testImagePath(String testName, String fileUrl, String uploadPath) {
        System.out.println("测试 " + testName);
        System.out.println("  fileUrl: " + fileUrl);
        System.out.println("  uploadPath: " + uploadPath);
        
        // 模拟图片路径处理逻辑
        if (fileUrl == null && uploadPath == null) {
            System.out.println("  ❌ 图片对象缺少URL信息");
        } else if ((fileUrl == null || fileUrl.trim().isEmpty()) && 
                   (uploadPath == null || uploadPath.trim().isEmpty())) {
            System.out.println("  ❌ 图片URL都为空");
        } else {
            String actualPath = uploadPath != null && !uploadPath.trim().isEmpty() ? uploadPath : fileUrl;
            System.out.println("  ✅ 使用路径: " + actualPath);
        }
        System.out.println();
    }

    @Test
    public void testErrorRecovery() {
        System.out.println("=== 测试错误恢复机制 ===");
        
        // 模拟PDF生成过程中的各种错误
        String[] testeeList = {"0001", "0002", "0069", "0004", "0005"};
        
        for (String testeeCode : testeeList) {
            try {
                System.out.println("处理参与者: " + testeeCode);
                
                // 模拟参与者0069的JSON解析错误
                if ("0069".equals(testeeCode)) {
                    throw new RuntimeException("syntax error, expect [, actual string, pos 0, fieldName null");
                }
                
                // 模拟其他参与者正常处理
                System.out.println("  ✅ 参与者 " + testeeCode + " 处理成功");
                
            } catch (Exception e) {
                System.out.println("  ❌ 参与者 " + testeeCode + " 处理失败: " + e.getMessage());
                System.out.println("  ↻ 继续处理下一个参与者");
            }
        }
        
        System.out.println("✅ 错误恢复机制测试完成");
    }

    @Test
    public void testConfigurationInfo() {
        System.out.println("=== 测试配置信息 ===");
        
        // 测试配置常量
        boolean useOriginalUrl = ProjectTesteeExportManageImpl.isUseOriginalImageUrl();
        boolean enableCache = ProjectTesteeExportManageImpl.isEnableImageCache();
        boolean useOptimized = ProjectTesteeExportManageImpl.isUseOptimizedVersionByDefault();
        
        System.out.println("PDF导出配置:");
        System.out.println("  使用原始图片URL: " + useOriginalUrl);
        System.out.println("  启用图片缓存: " + enableCache);
        System.out.println("  默认使用优化版本: " + useOptimized);
        
        // 推荐配置组合
        System.out.println("\n推荐配置组合:");
        if (useOriginalUrl && !enableCache && useOptimized) {
            System.out.println("  ✅ 当前配置为推荐的生产环境配置");
        } else {
            System.out.println("  ⚠️ 当前配置可能需要调整");
        }
        
        System.out.println("✅ 配置信息测试完成");
    }

    @Test
    public void testPerformanceScenarios() {
        System.out.println("=== 测试性能场景 ===");
        
        // 测试不同规模的导出场景
        testPerformanceScenario("小批量导出", 3, false);
        testPerformanceScenario("中等批量导出", 10, true);
        testPerformanceScenario("大批量导出", 50, true);
        testPerformanceScenario("超大批量导出", 100, true);
        
        System.out.println("✅ 性能场景测试完成");
    }
    
    private void testPerformanceScenario(String scenarioName, int testeeCount, boolean expectedOptimized) {
        System.out.println(scenarioName + ": " + testeeCount + "个参与者");
        
        // 模拟性能计算
        long estimatedTime = expectedOptimized ? 
            calculateOptimizedTime(testeeCount) : 
            calculateOriginalTime(testeeCount);
        
        String version = expectedOptimized ? "优化版本" : "原始版本";
        System.out.println("  预期使用: " + version);
        System.out.println("  预估耗时: " + estimatedTime + "ms");
        System.out.println();
    }
    
    private long calculateOptimizedTime(int testeeCount) {
        // 优化版本：批量查询 + 并行处理
        long queryTime = 500; // 批量查询固定时间
        long pdfTime = (long) (testeeCount * 200 / 4.0); // 并行处理，4倍提升
        return queryTime + pdfTime;
    }
    
    private long calculateOriginalTime(int testeeCount) {
        // 原始版本：N+1查询 + 串行处理
        long queryTime = testeeCount * 150; // 每个参与者单独查询
        long pdfTime = testeeCount * 800; // 串行PDF生成
        return queryTime + pdfTime;
    }
}
