package com.haoys.user.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 图片路径修复测试
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ImagePathFixTest {

    /**
     * 测试图片路径解析逻辑
     */
    @Test
    public void testImagePathExtraction() {
        log.info("开始测试图片路径解析逻辑");
        
        // 测试各种URL格式的路径提取
        String[] testCases = {
            "http://hysh-dev.haoyisheng.com.cn/edc-research-project/1074478992301625344/testee/1bfbc258fe494551b0df9ce2b0343976.jpg",
            "http://example.com/edc-research-project/project1/testee/image.png",
            "https://domain.com/edc-research-project/folder/subfolder/test.gif",
            "http://localhost:8080/edc-research-project/uploads/file.jpeg"
        };
        
        String[] expectedPaths = {
            "1074478992301625344/testee/1bfbc258fe494551b0df9ce2b0343976.jpg",
            "project1/testee/image.png", 
            "folder/subfolder/test.gif",
            "uploads/file.jpeg"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            String extractedPath = extractPathFromUrl(testCases[i]);
            log.info("URL: {} -> 提取路径: {}", testCases[i], extractedPath);
            
            // 验证路径提取是否正确（去掉了rootPath前缀）
            assert extractedPath != null : "提取的路径不应为null";
            assert !extractedPath.startsWith("/edc-research-project") : "路径不应包含rootPath前缀";
            assert !extractedPath.startsWith("/") : "路径不应以/开头";
        }
        
        log.info("图片路径解析逻辑测试通过");
    }

    /**
     * 测试压缩文件路径生成
     */
    @Test
    public void testCompressedPathGeneration() {
        log.info("开始测试压缩文件路径生成");
        
        String originalUrl = "http://hysh-dev.haoyisheng.com.cn/edc-research-project/1074478992301625344/testee/1bfbc258fe494551b0df9ce2b0343976.jpg";
        String expectedCompressedPath = "1074478992301625344/testee/1bfbc258fe494551b0df9ce2b0343976_compressed.jpg";
        
        // 模拟压缩路径生成
        String compressedPath = generateCompressedPath(originalUrl);
        
        log.info("原始URL: {}", originalUrl);
        log.info("生成的压缩路径: {}", compressedPath);
        
        // 验证压缩路径格式
        assert compressedPath != null : "压缩路径不应为null";
        assert compressedPath.contains("_compressed") : "压缩路径应包含_compressed标识";
        assert !compressedPath.startsWith("/edc-research-project") : "压缩路径不应包含rootPath前缀";
        assert !compressedPath.startsWith("/") : "压缩路径不应以/开头";
        
        log.info("压缩文件路径生成测试通过");
    }

    /**
     * 模拟从URL中提取路径的方法
     */
    private String extractPathFromUrl(String url) {
        if (url.startsWith("http")) {
            try {
                java.net.URL urlObj = new java.net.URL(url);
                String path = urlObj.getPath();
                
                // 模拟rootPath配置
                String rootPath = "edc-research-project";
                
                // 如果路径以rootPath开头，去掉rootPath前缀
                if (rootPath != null && path.startsWith("/" + rootPath)) {
                    path = path.substring(("/" + rootPath).length());
                    // 确保路径不以/开头
                    if (path.startsWith("/")) {
                        path = path.substring(1);
                    }
                } else if (path.startsWith("/")) {
                    // 如果路径以/开头但不包含rootPath，直接去掉/
                    path = path.substring(1);
                }
                
                return path;
            } catch (Exception e) {
                log.warn("解析URL失败，使用原始URL: {}", url);
                return url;
            }
        }
        return url;
    }

    /**
     * 模拟生成压缩文件路径的方法
     */
    private String generateCompressedPath(String originalUrl) {
        String relativePath = extractPathFromUrl(originalUrl);
        
        // 生成压缩文件名
        int lastDotIndex = relativePath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            String nameWithoutExt = relativePath.substring(0, lastDotIndex);
            String extension = relativePath.substring(lastDotIndex);
            return nameWithoutExt + "_compressed" + extension;
        } else {
            return relativePath + "_compressed";
        }
    }
}
