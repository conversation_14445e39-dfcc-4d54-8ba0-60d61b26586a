package com.haoys.user.service;

import com.haoys.user.common.service.ImageCompressionCacheService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

/**
 * 图片压缩缓存功能测试
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ImageCompressionCacheTest {

    @Autowired
    private ImageCompressionCacheService imageCompressionCacheService;

    /**
     * 测试基本的图片URL缓存功能
     */
    @Test
    public void testBasicImageUrlCache() {
        log.info("=== 测试基本图片URL缓存功能 ===");
        
        String originalUrl = "http://example.com/test-image-" + System.currentTimeMillis() + ".jpg";
        String compressedUrl = "http://example.com/compressed/test-image-" + System.currentTimeMillis() + "_compressed.jpg";
        
        // 1. 测试缓存不存在的情况
        String cachedUrl = imageCompressionCacheService.getCompressedImageUrl(originalUrl);
        assert cachedUrl == null : "新URL应该没有缓存";
        log.info("✓ 新URL缓存检查通过");
        
        // 2. 测试缓存图片URL
        imageCompressionCacheService.cacheCompressedImageUrl(originalUrl, compressedUrl);
        log.info("✓ 缓存图片URL: {} -> {}", originalUrl, compressedUrl);
        
        // 3. 测试从缓存获取
        String retrievedUrl = imageCompressionCacheService.getCompressedImageUrl(originalUrl);
        assert compressedUrl.equals(retrievedUrl) : "缓存的URL应该匹配";
        log.info("✓ 从缓存获取URL成功: {}", retrievedUrl);
        
        // 4. 测试缓存是否存在
        boolean hasCache = imageCompressionCacheService.hasCache(originalUrl);
        assert hasCache : "缓存应该存在";
        log.info("✓ 缓存存在性检查通过");
        
        log.info("=== 基本图片URL缓存功能测试完成 ===\n");
    }

    /**
     * 测试批量图片URL缓存功能
     */
    @Test
    public void testBatchImageUrlCache() {
        log.info("=== 测试批量图片URL缓存功能 ===");
        
        long timestamp = System.currentTimeMillis();
        List<String> originalUrls = Arrays.asList(
            "http://example.com/batch-test-1-" + timestamp + ".jpg",
            "http://example.com/batch-test-2-" + timestamp + ".png",
            "http://example.com/batch-test-3-" + timestamp + ".gif"
        );
        
        Map<String, String> compressedMap = new HashMap<>();
        for (int i = 0; i < originalUrls.size(); i++) {
            String originalUrl = originalUrls.get(i);
            String compressedUrl = originalUrl.replace("batch-test", "compressed-batch-test") + "_compressed";
            compressedMap.put(originalUrl, compressedUrl);
        }
        
        // 1. 测试批量缓存
        imageCompressionCacheService.batchCacheCompressedImageUrls(compressedMap);
        log.info("✓ 批量缓存{}个图片URL", compressedMap.size());
        
        // 2. 测试批量获取
        Map<String, String> retrievedMap = imageCompressionCacheService.batchGetCompressedImageUrls(originalUrls);
        assert retrievedMap.size() == originalUrls.size() : "批量获取的数量应该匹配";
        log.info("✓ 批量获取{}个图片URL", retrievedMap.size());
        
        // 3. 验证每个URL都正确
        for (String originalUrl : originalUrls) {
            String expected = compressedMap.get(originalUrl);
            String actual = retrievedMap.get(originalUrl);
            assert expected.equals(actual) : "批量获取的URL应该匹配: " + originalUrl;
        }
        log.info("✓ 批量获取URL验证通过");
        
        // 4. 测试缓存统计
        ImageCompressionCacheService.CacheStats stats = imageCompressionCacheService.getCacheStats(originalUrls);
        assert stats.getTotal() == originalUrls.size() : "统计总数应该匹配";
        assert stats.getHits() == originalUrls.size() : "命中数应该匹配";
        assert stats.getHitRate() == 1.0 : "命中率应该是100%";
        log.info("✓ 缓存统计: {}", stats);
        
        log.info("=== 批量图片URL缓存功能测试完成 ===\n");
    }

    /**
     * 测试参与者图片缓存功能
     */
    @Test
    public void testTesteeImageCache() {
        log.info("=== 测试参与者图片缓存功能 ===");
        
        String testeeCode = "TESTEE-" + System.currentTimeMillis();
        long timestamp = System.currentTimeMillis();
        
        Map<String, String> testeeImageMap = new HashMap<>();
        testeeImageMap.put("http://example.com/testee-image-1-" + timestamp + ".jpg", 
                          "http://example.com/compressed/testee-image-1-" + timestamp + "_compressed.jpg");
        testeeImageMap.put("http://example.com/testee-image-2-" + timestamp + ".png", 
                          "http://example.com/compressed/testee-image-2-" + timestamp + "_compressed.png");
        testeeImageMap.put("http://example.com/testee-image-3-" + timestamp + ".gif", 
                          "http://example.com/compressed/testee-image-3-" + timestamp + "_compressed.gif");
        
        // 1. 测试参与者缓存不存在的情况
        Map<String, String> emptyCache = imageCompressionCacheService.getTesteeImageCache(testeeCode);
        assert emptyCache.isEmpty() : "新参与者应该没有缓存";
        log.info("✓ 新参与者缓存检查通过");
        
        // 2. 测试缓存参与者图片
        imageCompressionCacheService.cacheTesteeImages(testeeCode, testeeImageMap);
        log.info("✓ 缓存参与者{}的{}个图片", testeeCode, testeeImageMap.size());
        
        // 3. 测试获取参与者图片缓存
        Map<String, String> retrievedCache = imageCompressionCacheService.getTesteeImageCache(testeeCode);
        assert retrievedCache.size() == testeeImageMap.size() : "获取的缓存数量应该匹配";
        log.info("✓ 获取参与者缓存{}个图片", retrievedCache.size());
        
        // 4. 验证每个图片URL都正确
        for (Map.Entry<String, String> entry : testeeImageMap.entrySet()) {
            String originalUrl = entry.getKey();
            String expectedCompressed = entry.getValue();
            String actualCompressed = retrievedCache.get(originalUrl);
            assert expectedCompressed.equals(actualCompressed) : "参与者图片URL应该匹配: " + originalUrl;
        }
        log.info("✓ 参与者图片URL验证通过");
        
        // 5. 测试清除参与者缓存
        imageCompressionCacheService.clearTesteeImageCache(testeeCode);
        Map<String, String> clearedCache = imageCompressionCacheService.getTesteeImageCache(testeeCode);
        assert clearedCache.isEmpty() : "清除后缓存应该为空";
        log.info("✓ 参与者缓存清除成功");
        
        log.info("=== 参与者图片缓存功能测试完成 ===\n");
    }

    /**
     * 测试缓存性能和并发安全性
     */
    @Test
    public void testCachePerformanceAndConcurrency() {
        log.info("=== 测试缓存性能和并发安全性 ===");
        
        long timestamp = System.currentTimeMillis();
        int imageCount = 100;
        List<String> originalUrls = new ArrayList<>();
        Map<String, String> compressedMap = new HashMap<>();
        
        // 准备测试数据
        for (int i = 0; i < imageCount; i++) {
            String originalUrl = "http://example.com/perf-test-" + timestamp + "-" + i + ".jpg";
            String compressedUrl = originalUrl.replace("perf-test", "compressed-perf-test") + "_compressed";
            originalUrls.add(originalUrl);
            compressedMap.put(originalUrl, compressedUrl);
        }
        
        // 1. 测试批量缓存性能
        long startTime = System.currentTimeMillis();
        imageCompressionCacheService.batchCacheCompressedImageUrls(compressedMap);
        long cacheTime = System.currentTimeMillis() - startTime;
        log.info("✓ 批量缓存{}个图片耗时: {}ms", imageCount, cacheTime);
        
        // 2. 测试批量获取性能
        startTime = System.currentTimeMillis();
        Map<String, String> retrievedMap = imageCompressionCacheService.batchGetCompressedImageUrls(originalUrls);
        long retrieveTime = System.currentTimeMillis() - startTime;
        log.info("✓ 批量获取{}个图片耗时: {}ms", retrievedMap.size(), retrieveTime);
        
        // 3. 验证数据完整性
        assert retrievedMap.size() == imageCount : "获取的数量应该匹配";
        for (String originalUrl : originalUrls) {
            assert retrievedMap.containsKey(originalUrl) : "应该包含所有原始URL";
            assert compressedMap.get(originalUrl).equals(retrievedMap.get(originalUrl)) : "压缩URL应该匹配";
        }
        log.info("✓ 数据完整性验证通过");
        
        // 4. 测试缓存命中率
        ImageCompressionCacheService.CacheStats stats = imageCompressionCacheService.getCacheStats(originalUrls);
        log.info("✓ 缓存统计: 总数={}, 命中={}, 命中率={:.2f}%", 
                stats.getTotal(), stats.getHits(), stats.getHitRate() * 100);
        
        assert stats.getHitRate() == 1.0 : "命中率应该是100%";
        
        log.info("=== 缓存性能和并发安全性测试完成 ===\n");
    }

    /**
     * 测试边界情况和异常处理
     */
    @Test
    public void testEdgeCasesAndExceptionHandling() {
        log.info("=== 测试边界情况和异常处理 ===");
        
        // 1. 测试空值处理
        String nullResult = imageCompressionCacheService.getCompressedImageUrl(null);
        assert nullResult == null : "null输入应该返回null";
        
        String emptyResult = imageCompressionCacheService.getCompressedImageUrl("");
        assert emptyResult == null : "空字符串输入应该返回null";
        
        String blankResult = imageCompressionCacheService.getCompressedImageUrl("   ");
        assert blankResult == null : "空白字符串输入应该返回null";
        log.info("✓ 空值处理测试通过");
        
        // 2. 测试空列表处理
        Map<String, String> emptyBatchResult = imageCompressionCacheService.batchGetCompressedImageUrls(new ArrayList<>());
        assert emptyBatchResult.isEmpty() : "空列表应该返回空映射";
        
        Map<String, String> nullListResult = imageCompressionCacheService.batchGetCompressedImageUrls(null);
        assert nullListResult.isEmpty() : "null列表应该返回空映射";
        log.info("✓ 空列表处理测试通过");
        
        // 3. 测试参与者缓存边界情况
        Map<String, String> nullTesteeResult = imageCompressionCacheService.getTesteeImageCache(null);
        assert nullTesteeResult.isEmpty() : "null参与者编码应该返回空映射";
        
        Map<String, String> emptyTesteeResult = imageCompressionCacheService.getTesteeImageCache("");
        assert emptyTesteeResult.isEmpty() : "空参与者编码应该返回空映射";
        log.info("✓ 参与者缓存边界情况测试通过");
        
        // 4. 测试缓存统计边界情况
        ImageCompressionCacheService.CacheStats emptyStats = imageCompressionCacheService.getCacheStats(new ArrayList<>());
        assert emptyStats.getTotal() == 0 : "空列表统计总数应该为0";
        assert emptyStats.getHits() == 0 : "空列表统计命中数应该为0";
        assert emptyStats.getHitRate() == 0.0 : "空列表统计命中率应该为0";
        log.info("✓ 缓存统计边界情况测试通过");
        
        log.info("=== 边界情况和异常处理测试完成 ===\n");
    }

    /**
     * 综合测试：模拟PDF导出场景
     */
    @Test
    public void testPdfExportScenario() {
        log.info("=== 综合测试：模拟PDF导出场景 ===");
        
        long timestamp = System.currentTimeMillis();
        
        // 模拟3个参与者的数据
        String[] testeeCodes = {"TESTEE-001-" + timestamp, "TESTEE-002-" + timestamp, "TESTEE-003-" + timestamp};
        
        for (String testeeCode : testeeCodes) {
            // 每个参与者有5张图片
            Map<String, String> testeeImages = new HashMap<>();
            for (int i = 1; i <= 5; i++) {
                String originalUrl = "http://example.com/" + testeeCode + "/image-" + i + ".jpg";
                String compressedUrl = originalUrl.replace("/image-", "/compressed-image-") + "_compressed";
                testeeImages.put(originalUrl, compressedUrl);
            }
            
            // 缓存参与者图片
            imageCompressionCacheService.cacheTesteeImages(testeeCode, testeeImages);
            log.info("✓ 缓存参与者{}的{}张图片", testeeCode, testeeImages.size());
        }
        
        // 模拟PDF导出过程：获取所有参与者的图片缓存
        int totalImages = 0;
        for (String testeeCode : testeeCodes) {
            Map<String, String> cachedImages = imageCompressionCacheService.getTesteeImageCache(testeeCode);
            totalImages += cachedImages.size();
            log.info("✓ 获取参与者{}的缓存图片{}张", testeeCode, cachedImages.size());
        }
        
        assert totalImages == testeeCodes.length * 5 : "总图片数应该匹配";
        log.info("✓ PDF导出场景模拟完成，总共处理{}张图片", totalImages);
        
        // 清理测试数据
        for (String testeeCode : testeeCodes) {
            imageCompressionCacheService.clearTesteeImageCache(testeeCode);
        }
        log.info("✓ 测试数据清理完成");
        
        log.info("=== PDF导出场景综合测试完成 ===\n");
    }
}
