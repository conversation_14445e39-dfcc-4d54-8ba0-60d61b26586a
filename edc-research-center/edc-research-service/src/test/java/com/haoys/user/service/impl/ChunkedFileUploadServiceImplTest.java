package com.haoys.user.service.impl;

import com.haoys.user.domain.param.file.ChunkUploadParam;
import com.haoys.user.domain.param.file.MergeChunkParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.exception.ServiceException;
import com.haoys.user.service.ChunkedFileUploadService;
import com.haoys.user.storge.cloud.OssStorageConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ChunkedFileUploadServiceImpl 单元测试
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-01
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
class ChunkedFileUploadServiceImplTest {

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private HashOperations<String, Object, Object> hashOperations;

    @Mock
    private OssStorageConfig ossStorageConfig;

    @InjectMocks
    private ChunkedFileUploadServiceImpl service;

    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForHash()).thenReturn(hashOperations);
    }

    @Test
    void testCheckUploadStatus_NewFile() {
        // 准备测试数据
        String fileHash = "test-hash";
        String fileName = "test.txt";
        Long fileSize = 1024L;
        Long userId = 1L;

        when(hashOperations.entries(anyString())).thenReturn(new HashMap<>());

        // 执行测试
        Map<String, Object> result = service.checkUploadStatus(fileHash, fileName, fileSize, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals("new", result.get("status"));
        assertFalse((Boolean) result.get("needChunked"));
        assertEquals(2097152L, result.get("chunkSize"));
        assertEquals(1, result.get("totalChunks"));
    }

    @Test
    void testCheckUploadStatus_LargeFile() {
        // 准备测试数据
        String fileHash = "test-hash";
        String fileName = "large-file.txt";
        Long fileSize = 50 * 1024 * 1024L; // 50MB
        Long userId = 1L;

        when(hashOperations.entries(anyString())).thenReturn(new HashMap<>());

        // 执行测试
        Map<String, Object> result = service.checkUploadStatus(fileHash, fileName, fileSize, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals("new", result.get("status"));
        assertTrue((Boolean) result.get("needChunked"));
        assertEquals(25, result.get("totalChunks")); // 50MB / 2MB = 25 chunks
    }

    @Test
    void testCalculateFileMD5_Success() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            "test content".getBytes()
        );

        // 执行测试
        String md5Hash = service.calculateFileMD5(file);

        // 验证结果
        assertNotNull(md5Hash);
        assertFalse(md5Hash.isEmpty());
        assertEquals(32, md5Hash.length()); // MD5 hash is 32 characters
    }

    @Test
    void testCalculateFileMD5_EmptyFile() {
        // 准备测试数据
        MockMultipartFile emptyFile = new MockMultipartFile("file", "", MediaType.TEXT_PLAIN_VALUE, new byte[0]);

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            service.calculateFileMD5(emptyFile);
        });

        assertEquals("文件不能为空", exception.getMessage());
    }

    @Test
    void testCalculateFileMD5_NullFile() {
        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            service.calculateFileMD5(null);
        });

        assertEquals("文件不能为空", exception.getMessage());
    }

    @Test
    void testGetFileInfoByHash_FileExists() {
        // 准备测试数据
        String fileHash = "test-hash";
        Long userId = 1L;

        Map<Object, Object> uploadInfo = new HashMap<>();
        uploadInfo.put("fileName", "test.txt");
        uploadInfo.put("fileSize", 1024L);
        uploadInfo.put("totalChunks", 1);
        uploadInfo.put("uploadedChunks", "[]");
        uploadInfo.put("lastUpdateTime", System.currentTimeMillis());
        uploadInfo.put("status", "completed");
        uploadInfo.put("filePath", "/path/to/file.txt");

        when(hashOperations.entries(anyString())).thenReturn(uploadInfo);

        // 执行测试
        Map<String, Object> result = service.getFileInfoByHash(fileHash, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals(fileHash, result.get("fileHash"));
        assertEquals("test.txt", result.get("fileName"));
        assertEquals(1024L, result.get("fileSize"));
        assertEquals("completed", result.get("status"));
        assertEquals("/path/to/file.txt", result.get("filePath"));
    }

    @Test
    void testGetFileInfoByHash_FileNotExists() {
        // 准备测试数据
        String fileHash = "non-existent-hash";
        Long userId = 1L;

        when(hashOperations.entries(anyString())).thenReturn(new HashMap<>());

        // 执行测试
        Map<String, Object> result = service.getFileInfoByHash(fileHash, userId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetFileInfoByHash_EmptyHash() {
        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            service.getFileInfoByHash("", 1L);
        });

        assertEquals("文件哈希值不能为空", exception.getMessage());
    }

    @Test
    void testValidateFileMD5_Success() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            "test content".getBytes()
        );

        // 计算实际的MD5
        String actualMD5 = service.calculateFileMD5(file);

        // 执行测试
        boolean isValid = service.validateFileMD5(file, actualMD5);

        // 验证结果
        assertTrue(isValid);
    }

    @Test
    void testValidateFileMD5_Failed() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            "test content".getBytes()
        );

        String wrongHash = "wrong-hash-value";

        // 执行测试
        boolean isValid = service.validateFileMD5(file, wrongHash);

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testValidateFileMD5_EmptyFile() {
        // 准备测试数据
        MockMultipartFile emptyFile = new MockMultipartFile("file", "", MediaType.TEXT_PLAIN_VALUE, new byte[0]);

        // 执行测试
        boolean isValid = service.validateFileMD5(emptyFile, "any-hash");

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testValidateFileMD5_EmptyHash() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            "test content".getBytes()
        );

        // 执行测试
        boolean isValid = service.validateFileMD5(file, "");

        // 验证结果
        assertFalse(isValid);
    }

    @Test
    void testGetUploadConfig() {
        // 执行测试
        Map<String, Object> config = service.getUploadConfig();

        // 验证结果
        assertNotNull(config);
        assertEquals(2097152L, config.get("chunkSize")); // 2MB
        assertEquals(20971520L, config.get("chunkThreshold")); // 20MB
        assertEquals(104857600L, config.get("maxFileSize")); // 100MB
        assertNotNull(config.get("allowedTypes"));
    }

    @Test
    void testDirectUpload_SmallFile() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "small.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            "small content".getBytes()
        );

        String projectId = "project1";
        String folderName = "folder1";
        Long userId = 1L;

        // 由于directUpload方法依赖外部存储服务，这里主要测试参数验证
        // 实际的存储逻辑需要在集成测试中验证

        // 验证文件大小检查
        assertTrue(file.getSize() < 20 * 1024 * 1024L); // 小于20MB
    }

    @Test
    void testDirectUpload_LargeFile() {
        // 准备测试数据 - 创建一个大文件（模拟）
        byte[] largeContent = new byte[25 * 1024 * 1024]; // 25MB
        MockMultipartFile largeFile = new MockMultipartFile(
            "file", 
            "large.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            largeContent
        );

        String projectId = "project1";
        String folderName = "folder1";
        Long userId = 1L;

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            service.directUpload(largeFile, projectId, folderName, userId);
        });

        assertTrue(exception.getMessage().contains("文件大小超过20MB"));
    }

    @Test
    void testMergeChunks_InvalidParameters() {
        // 准备测试数据
        MergeChunkParam param = new MergeChunkParam();
        param.setFileHash(""); // 空的文件哈希
        param.setFileName("test.txt");
        param.setTotalChunks(3);
        param.setFileSize(3072L);
        param.setUserId(1L);

        // 由于mergeChunks方法涉及文件系统操作，这里主要测试参数验证
        // 实际的合并逻辑需要在集成测试中验证
        
        // 验证参数
        assertTrue(param.getFileHash().isEmpty());
        assertNotNull(param.getFileName());
        assertTrue(param.getTotalChunks() > 0);
        assertTrue(param.getFileSize() > 0);
    }

    @Test
    void testUploadChunk_InvalidParameters() {
        // 准备测试数据
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "chunk.txt", 
            MediaType.TEXT_PLAIN_VALUE, 
            "chunk content".getBytes()
        );

        ChunkUploadParam param = new ChunkUploadParam();
        param.setFileHash("test-hash");
        param.setFileName("test.txt");
        param.setChunkIndex(-1); // 无效的分片索引
        param.setTotalChunks(0); // 无效的总分片数
        param.setUserId(1L);

        // 验证参数
        assertTrue(param.getChunkIndex() < 0);
        assertTrue(param.getTotalChunks() <= 0);
    }

    @Test
    void testCancelUpload() {
        // 准备测试数据
        String fileHash = "test-hash";
        Long userId = 1L;

        // 由于cancelUpload方法涉及Redis和文件系统操作，这里主要测试方法调用
        // 实际的取消逻辑需要在集成测试中验证
        
        // 验证参数
        assertNotNull(fileHash);
        assertNotNull(userId);
        assertFalse(fileHash.isEmpty());
        assertTrue(userId > 0);
    }

    @Test
    void testGetUploadProgress_EmptyHash() {
        // 准备测试数据
        String emptyHash = "";
        Long userId = 1L;

        when(hashOperations.entries(anyString())).thenReturn(new HashMap<>());

        // 执行测试
        Map<String, Object> result = service.getUploadProgress(emptyHash, userId);

        // 验证结果
        assertNotNull(result);
        assertEquals("not_found", result.get("status"));
        assertEquals(0, result.get("progress"));
    }

    @Test
    void testCleanupExpiredChunks() {
        // 执行测试
        int cleanedCount = service.cleanupExpiredChunks();

        // 验证结果（当前实现返回0）
        assertEquals(0, cleanedCount);
    }
}
