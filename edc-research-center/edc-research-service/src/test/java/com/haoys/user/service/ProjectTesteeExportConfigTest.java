package com.haoys.user.service;

import com.haoys.user.service.impl.ProjectTesteeExportManageImpl;
import com.haoys.user.service.impl.ProjectTesteeExportOptimizedImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * PDF导出配置测试类
 * 验证新增的常量配置是否正确工作
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
public class ProjectTesteeExportConfigTest {

    @Test
    public void testExportConfigConstants() {
        log.info("=== PDF导出配置常量测试 ===");
        
        // 测试ProjectTesteeExportManageImpl的配置
        log.info("ProjectTesteeExportManageImpl配置:");
        log.info("- 使用原始图片URL: {}", ProjectTesteeExportManageImpl.isUseOriginalImageUrl());
        log.info("- 启用图片缓存: {}", ProjectTesteeExportManageImpl.isEnableImageCache());
        log.info("- 配置描述: {}", ProjectTesteeExportManageImpl.getExportConfigDescription());
        
        // 测试ProjectTesteeExportOptimizedImpl的配置
        log.info("ProjectTesteeExportOptimizedImpl配置:");
        log.info("- 使用原始图片URL: {}", ProjectTesteeExportOptimizedImpl.isUseOriginalImageUrl());
        log.info("- 启用图片缓存: {}", ProjectTesteeExportOptimizedImpl.isEnableImageCache());
        log.info("- 配置描述: {}", ProjectTesteeExportOptimizedImpl.getExportConfigDescription());
        
        // 验证两个类的配置是否一致
        boolean configConsistent = 
            ProjectTesteeExportManageImpl.isUseOriginalImageUrl() == ProjectTesteeExportOptimizedImpl.isUseOriginalImageUrl() &&
            ProjectTesteeExportManageImpl.isEnableImageCache() == ProjectTesteeExportOptimizedImpl.isEnableImageCache();
        
        log.info("两个类的配置是否一致: {}", configConsistent);
        
        if (configConsistent) {
            log.info("✅ 配置常量测试通过");
        } else {
            log.error("❌ 配置常量测试失败 - 两个类的配置不一致");
        }
    }

    @Test
    public void testConfigurationValues() {
        log.info("=== PDF导出配置值验证 ===");
        
        // 验证默认配置值
        boolean useOriginalUrl = ProjectTesteeExportOptimizedImpl.isUseOriginalImageUrl();
        boolean enableCache = ProjectTesteeExportOptimizedImpl.isEnableImageCache();
        
        log.info("当前配置值:");
        log.info("- USE_ORIGINAL_IMAGE_URL: {} (默认应为true)", useOriginalUrl);
        log.info("- ENABLE_IMAGE_CACHE: {} (默认应为false)", enableCache);
        
        // 验证默认值是否符合预期
        if (useOriginalUrl && !enableCache) {
            log.info("✅ 默认配置值符合预期");
            log.info("  - 使用原始图片URL以确保图片质量");
            log.info("  - 不启用图片缓存以避免缓存一致性问题");
        } else {
            log.warn("⚠️ 默认配置值与预期不符，请检查常量定义");
        }
    }

    @Test
    public void testConfigurationDescription() {
        log.info("=== PDF导出配置描述测试 ===");
        
        String description = ProjectTesteeExportOptimizedImpl.getExportConfigDescription();
        log.info("配置描述: {}", description);
        
        // 验证描述是否包含必要信息
        boolean containsImageUrl = description.contains("使用原始图片URL");
        boolean containsCache = description.contains("启用图片缓存");
        
        if (containsImageUrl && containsCache) {
            log.info("✅ 配置描述包含所有必要信息");
        } else {
            log.error("❌ 配置描述缺少必要信息");
        }
    }
}
