package com.haoys.user.service;

import com.haoys.user.common.performance.PerformanceMonitor;
import com.haoys.user.domain.param.testee.ProjectTesteeExportParam;
import com.haoys.user.service.impl.ProjectTesteeExportManageImpl;
import com.haoys.user.service.impl.ProjectTesteeExportOptimizedImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * PDF导出性能测试类
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ProjectTesteeExportPerformanceTest {

    private final PerformanceMonitor performanceMonitor = new PerformanceMonitor();

    @Test
    public void testPerformanceMonitor() {
        // 测试性能监控工具
        testBasicPerformanceMonitoring();
        testDatabaseQueryMonitoring();
        testPdfGenerationMonitoring();
        
        // 打印性能报告
        performanceMonitor.printPerformanceReport();
    }

    private void testBasicPerformanceMonitoring() {
        log.info("=== 基础性能监控测试 ===");
        
        // 测试快速操作
        PerformanceMonitor.PerformanceContext fastContext = 
            performanceMonitor.startMonitoring("快速操作");
        try {
            Thread.sleep(100); // 模拟100ms操作
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        performanceMonitor.endMonitoring(fastContext);
        
        // 测试慢操作
        PerformanceMonitor.PerformanceContext slowContext = 
            performanceMonitor.startMonitoring("慢操作");
        try {
            Thread.sleep(1500); // 模拟1.5秒操作
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        performanceMonitor.endMonitoring(slowContext);
        
        // 测试多次执行
        for (int i = 0; i < 5; i++) {
            PerformanceMonitor.PerformanceContext context = 
                performanceMonitor.startMonitoring("重复操作");
            try {
                Thread.sleep(200 + i * 50); // 模拟不同耗时
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            performanceMonitor.endMonitoring(context);
        }
    }

    private void testDatabaseQueryMonitoring() {
        log.info("=== 数据库查询监控测试 ===");
        
        PerformanceMonitor.DatabaseQueryMonitor dbMonitor = 
            performanceMonitor.getDatabaseMonitor();
        
        // 模拟参与者列表查询
        List<String> testeeList = dbMonitor.monitorQuery("getProjectTesteeListByIds", () -> {
            try {
                Thread.sleep(300); // 模拟数据库查询耗时
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return Arrays.asList("testee1", "testee2", "testee3", "testee4", "testee5");
        });
        
        log.info("查询到参与者数量: {}", testeeList.size());
        
        // 模拟表单数据查询
        for (String testeeId : testeeList) {
            dbMonitor.monitorQuery("getTesteeVisitFormDetail_" + testeeId, () -> {
                try {
                    Thread.sleep(150); // 模拟单个参与者表单查询
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return Arrays.asList("form1", "form2", "form3");
            });
        }
        
        // 模拟批量查询优化
        dbMonitor.monitorQuery("batchLoadTesteeFormData", () -> {
            try {
                Thread.sleep(400); // 模拟批量查询，总时间更短
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "批量数据";
        });
    }

    private void testPdfGenerationMonitoring() {
        log.info("=== PDF生成监控测试 ===");
        
        PerformanceMonitor.PdfGenerationMonitor pdfMonitor = 
            performanceMonitor.getPdfMonitor();
        
        // 模拟串行PDF生成
        long serialStartTime = System.currentTimeMillis();
        for (int i = 1; i <= 5; i++) {
            String testeeCode = "TESTEE_" + String.format("%03d", i);
            pdfMonitor.monitorPdfGeneration(testeeCode, () -> {
                try {
                    Thread.sleep(800); // 模拟PDF生成耗时
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return "PDF_" + testeeCode;
            });
        }
        long serialEndTime = System.currentTimeMillis();
        log.info("串行PDF生成总耗时: {}ms", serialEndTime - serialStartTime);
        
        // 模拟并行PDF生成
        long parallelStartTime = System.currentTimeMillis();
        List<String> testeeCodes = Arrays.asList("TESTEE_001", "TESTEE_002", "TESTEE_003", "TESTEE_004", "TESTEE_005");
        testeeCodes.parallelStream().forEach(testeeCode -> {
            pdfMonitor.monitorPdfGeneration(testeeCode + "_PARALLEL", () -> {
                try {
                    Thread.sleep(800); // 模拟PDF生成耗时
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return "PDF_" + testeeCode;
            });
        });
        long parallelEndTime = System.currentTimeMillis();
        log.info("并行PDF生成总耗时: {}ms", parallelEndTime - parallelStartTime);
        
        // 计算性能提升
        double improvement = (double)(serialEndTime - serialStartTime) / (parallelEndTime - parallelStartTime);
        log.info("并行处理性能提升: {:.2f}倍", improvement);
    }

    @Test
    public void testExportParameterOptimization() {
        log.info("=== 导出参数优化测试 ===");
        
        // 测试小批量导出（应该使用原始版本）
        ProjectTesteeExportParam smallBatchParam = createTestParam(3);
        testExportDecision(smallBatchParam, "小批量导出");
        
        // 测试大批量导出（应该使用优化版本）
        ProjectTesteeExportParam largeBatchParam = createTestParam(10);
        testExportDecision(largeBatchParam, "大批量导出");
        
        // 测试超大批量导出
        ProjectTesteeExportParam hugeBatchParam = createTestParam(50);
        testExportDecision(hugeBatchParam, "超大批量导出");
    }

    private ProjectTesteeExportParam createTestParam(int testeeCount) {
        ProjectTesteeExportParam param = new ProjectTesteeExportParam();
        param.setProjectId("TEST_PROJECT_001");
        param.setOrgId("TEST_ORG_001");
        param.setFileUrlName("test_export_" + System.currentTimeMillis());
        
        // 创建测试参与者ID列表
        List<String> testeeIds = Arrays.asList();
        for (int i = 1; i <= testeeCount; i++) {
            ((List<String>) testeeIds).add("TESTEE_" + String.format("%03d", i));
        }
        param.setTesteeIds(testeeIds);
        
        return param;
    }

    private void testExportDecision(ProjectTesteeExportParam param, String testName) {
        PerformanceMonitor.PerformanceContext context = 
            performanceMonitor.startMonitoring(testName);
        
        try {
            // 模拟导出决策逻辑
            boolean shouldUseOptimized = param.getTesteeIds() != null && 
                                       param.getTesteeIds().size() >= 5;
            
            if (shouldUseOptimized) {
                log.info("{}: 使用优化版本，参与者数量: {}", testName, param.getTesteeIds().size());
                simulateOptimizedExport(param);
            } else {
                log.info("{}: 使用原始版本，参与者数量: {}", testName, param.getTesteeIds().size());
                simulateOriginalExport(param);
            }
            
        } finally {
            performanceMonitor.endMonitoring(context);
        }
    }

    private void simulateOptimizedExport(ProjectTesteeExportParam param) {
        int testeeCount = param.getTesteeIds().size();
        
        // 模拟数据预加载
        PerformanceMonitor.PerformanceContext preloadContext = 
            performanceMonitor.startMonitoring("数据预加载");
        try {
            Thread.sleep(200 + testeeCount * 10); // 批量加载，时间增长较慢
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        performanceMonitor.endMonitoring(preloadContext);
        
        // 模拟并行PDF生成
        PerformanceMonitor.PerformanceContext pdfContext = 
            performanceMonitor.startMonitoring("并行PDF生成");
        try {
            Thread.sleep(500 + testeeCount * 20); // 并行处理，时间增长较慢
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        performanceMonitor.endMonitoring(pdfContext);
    }

    private void simulateOriginalExport(ProjectTesteeExportParam param) {
        int testeeCount = param.getTesteeIds().size();
        
        // 模拟串行处理
        PerformanceMonitor.PerformanceContext originalContext = 
            performanceMonitor.startMonitoring("原始串行处理");
        try {
            Thread.sleep(testeeCount * 200); // 串行处理，时间线性增长
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        performanceMonitor.endMonitoring(originalContext);
    }

    @Test
    public void testMemoryUsageOptimization() {
        log.info("=== 内存使用优化测试 ===");
        
        // 获取初始内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        log.info("初始内存使用: {} MB", initialMemory / 1024 / 1024);
        
        // 模拟原始方式的内存使用（重复创建对象）
        PerformanceMonitor.PerformanceContext originalMemoryContext = 
            performanceMonitor.startMonitoring("原始内存使用模式");
        
        for (int i = 0; i < 100; i++) {
            // 模拟重复创建大对象
            StringBuilder largeString = new StringBuilder();
            for (int j = 0; j < 1000; j++) {
                largeString.append("重复数据").append(i).append(j);
            }
        }
        
        long afterOriginalMemory = runtime.totalMemory() - runtime.freeMemory();
        performanceMonitor.endMonitoring(originalMemoryContext);
        
        // 强制垃圾回收
        System.gc();
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 模拟优化后的内存使用（缓存和复用）
        PerformanceMonitor.PerformanceContext optimizedMemoryContext = 
            performanceMonitor.startMonitoring("优化内存使用模式");
        
        // 预分配和复用
        StringBuilder reusableString = new StringBuilder(10000);
        for (int i = 0; i < 100; i++) {
            reusableString.setLength(0); // 清空但保持容量
            for (int j = 0; j < 1000; j++) {
                reusableString.append("优化数据").append(i).append(j);
            }
        }
        
        long afterOptimizedMemory = runtime.totalMemory() - runtime.freeMemory();
        performanceMonitor.endMonitoring(optimizedMemoryContext);
        
        log.info("原始方式内存增长: {} MB", (afterOriginalMemory - initialMemory) / 1024 / 1024);
        log.info("优化方式内存增长: {} MB", (afterOptimizedMemory - afterOriginalMemory) / 1024 / 1024);
    }
}
