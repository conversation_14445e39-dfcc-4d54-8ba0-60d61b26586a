package com.haoys.user.service;

import com.haoys.user.common.file.ImageCompressionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片压缩功能测试类
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ImageCompressionTest {

    @Test
    public void testIsImageFile() {
        // 测试图片文件识别
        MultipartFile jpgFile = new MockMultipartFile("test", "test.jpg", "image/jpeg", "test content".getBytes());
        MultipartFile pngFile = new MockMultipartFile("test", "test.png", "image/png", "test content".getBytes());
        MultipartFile gifFile = new MockMultipartFile("test", "test.gif", "image/gif", "test content".getBytes());
        MultipartFile txtFile = new MockMultipartFile("test", "test.txt", "text/plain", "test content".getBytes());
        
        assert ImageCompressionUtils.isImageFile(jpgFile) : "JPG文件应该被识别为图片";
        assert ImageCompressionUtils.isImageFile(pngFile) : "PNG文件应该被识别为图片";
        assert ImageCompressionUtils.isImageFile(gifFile) : "GIF文件应该被识别为图片";
        assert !ImageCompressionUtils.isImageFile(txtFile) : "TXT文件不应该被识别为图片";
        
        log.info("图片文件识别测试通过");
    }

    @Test
    public void testImageExtensionRecognition() {
        // 测试扩展名识别
        assert ImageCompressionUtils.isImageExtension("jpg") : "jpg扩展名应该被识别";
        assert ImageCompressionUtils.isImageExtension("jpeg") : "jpeg扩展名应该被识别";
        assert ImageCompressionUtils.isImageExtension("png") : "png扩展名应该被识别";
        assert ImageCompressionUtils.isImageExtension("gif") : "gif扩展名应该被识别";
        assert ImageCompressionUtils.isImageExtension("bmp") : "bmp扩展名应该被识别";
        assert !ImageCompressionUtils.isImageExtension("txt") : "txt扩展名不应该被识别";
        assert !ImageCompressionUtils.isImageExtension("pdf") : "pdf扩展名不应该被识别";
        
        log.info("图片扩展名识别测试通过");
    }

    @Test
    public void testCompressImageWithMockFile() {
        try {
            // 创建模拟的图片文件
            byte[] mockImageData = createMockImageData();
            MultipartFile mockImageFile = new MockMultipartFile(
                "test", 
                "test.jpg", 
                "image/jpeg", 
                mockImageData
            );
            
            // 测试压缩功能
            byte[] compressedData = ImageCompressionUtils.compressImage(mockImageFile);
            
            assert compressedData != null : "压缩后的数据不应该为空";
            assert compressedData.length > 0 : "压缩后的数据长度应该大于0";
            
            log.info("模拟图片压缩测试通过，原始大小: {} bytes, 压缩后大小: {} bytes", 
                    mockImageData.length, compressedData.length);
            
        } catch (IOException e) {
            log.error("图片压缩测试失败", e);
            throw new RuntimeException("图片压缩测试失败", e);
        }
    }

    @Test
    public void testCreateCompressedInputStream() {
        try {
            // 创建模拟的图片文件
            byte[] mockImageData = createMockImageData();
            MultipartFile mockImageFile = new MockMultipartFile(
                "test", 
                "test.png", 
                "image/png", 
                mockImageData
            );
            
            // 测试创建压缩输入流
            InputStream compressedStream = ImageCompressionUtils.createCompressedInputStream(mockImageFile);
            
            assert compressedStream != null : "压缩输入流不应该为空";
            
            // 读取压缩流数据 (Java 8兼容方式)
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] data = new byte[1024];
            int nRead;
            while ((nRead = compressedStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }
            byte[] compressedData = buffer.toByteArray();
            assert compressedData.length > 0 : "压缩流数据长度应该大于0";
            
            compressedStream.close();
            
            log.info("压缩输入流创建测试通过，压缩后大小: {} bytes", compressedData.length);
            
        } catch (IOException e) {
            log.error("压缩输入流创建测试失败", e);
            throw new RuntimeException("压缩输入流创建测试失败", e);
        }
    }

    @Test
    public void testNonImageFileHandling() {
        try {
            // 测试非图片文件的处理
            MultipartFile txtFile = new MockMultipartFile(
                "test", 
                "test.txt", 
                "text/plain", 
                "This is a text file content".getBytes()
            );
            
            byte[] originalData = txtFile.getBytes();
            byte[] processedData = ImageCompressionUtils.compressImage(txtFile);
            
            // 非图片文件应该返回原始数据
            assert java.util.Arrays.equals(originalData, processedData) : "非图片文件应该返回原始数据";
            
            log.info("非图片文件处理测试通过");
            
        } catch (IOException e) {
            log.error("非图片文件处理测试失败", e);
            throw new RuntimeException("非图片文件处理测试失败", e);
        }
    }

    /**
     * 创建模拟的图片数据
     * 这里创建一个简单的BMP格式图片数据用于测试
     */
    private byte[] createMockImageData() {
        // 创建一个简单的1x1像素的BMP图片数据
        byte[] bmpHeader = {
            0x42, 0x4D,             // BM signature
            0x3A, 0x00, 0x00, 0x00, // File size (58 bytes)
            0x00, 0x00, 0x00, 0x00, // Reserved
            0x36, 0x00, 0x00, 0x00, // Offset to pixel data
            0x28, 0x00, 0x00, 0x00, // DIB header size
            0x01, 0x00, 0x00, 0x00, // Width (1 pixel)
            0x01, 0x00, 0x00, 0x00, // Height (1 pixel)
            0x01, 0x00,             // Planes
            0x18, 0x00,             // Bits per pixel (24)
            0x00, 0x00, 0x00, 0x00, // Compression
            0x04, 0x00, 0x00, 0x00, // Image size
            0x00, 0x00, 0x00, 0x00, // X pixels per meter
            0x00, 0x00, 0x00, 0x00, // Y pixels per meter
            0x00, 0x00, 0x00, 0x00, // Colors used
            0x00, 0x00, 0x00, 0x00, // Important colors
            // Pixel data (BGR format)
            (byte)0xFF, (byte)0xFF, (byte)0xFF, 0x00 // White pixel with padding
        };
        
        return bmpHeader;
    }

    @Test
    public void testImageCompressionIntegration() {
        log.info("=== 图片压缩功能集成测试开始 ===");
        
        // 测试各种场景
        testIsImageFile();
        testImageExtensionRecognition();
        testCompressImageWithMockFile();
        testCreateCompressedInputStream();
        testNonImageFileHandling();
        
        log.info("=== 图片压缩功能集成测试完成 ===");
        log.info("所有测试用例均通过，图片压缩功能工作正常");
    }
}
