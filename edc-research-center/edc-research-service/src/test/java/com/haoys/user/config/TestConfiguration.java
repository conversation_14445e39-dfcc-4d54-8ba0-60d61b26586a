package com.haoys.user.config;

import com.haoys.user.service.impl.ProjectConfigModuleServiceImpl;
import com.haoys.user.mapper.ProjectConfigModuleMapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import static org.mockito.Mockito.mock;

/**
 * 测试配置类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@SpringBootConfiguration
@EnableAutoConfiguration(exclude = {RedisAutoConfiguration.class})
@ComponentScan(basePackageClasses = {ProjectConfigModuleServiceImpl.class})
@MapperScan(basePackageClasses = {ProjectConfigModuleMapper.class})
public class TestConfiguration {

    /**
     * 模拟RedisTemplate用于测试
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(mock(RedisConnectionFactory.class));
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }
}
