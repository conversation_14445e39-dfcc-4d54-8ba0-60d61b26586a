package com.haoys.user.service.impl;

import com.haoys.user.domain.param.file.SaveUploadProjectFileParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.service.AsyncFileUploadService;
import com.haoys.user.service.ProjectTesteeFileService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 重构后的ProjectTesteeFileService测试类
 * 验证参数封装和异步上传功能
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class ProjectTesteeFileServiceImplTest {

    @Autowired
    private ProjectTesteeFileService projectTesteeFileService;

    @Autowired
    private AsyncFileUploadService asyncFileUploadService;

    /**
     * 测试使用参数对象的文件上传方法
     */
    @Test
    public void testSaveUploadProjectFileWithParam() throws IOException {
        // 创建模拟文件
        MockMultipartFile file1 = new MockMultipartFile(
                "file1", 
                "test1.jpg", 
                "image/jpeg", 
                "test image content 1".getBytes()
        );
        
        MockMultipartFile file2 = new MockMultipartFile(
                "file2", 
                "test2.png", 
                "image/png", 
                "test image content 2".getBytes()
        );
        
        MultipartFile[] files = {file1, file2};

        // 创建参数对象
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId("test_user")
                .projectId("1")
                .testeeId("1")
                .medicalType("default")
                .openOCR("0")
                .batchUpload("1")
                .ifMontage("0")
                .batchOpenOcr("0");

        // 执行上传
        List<UploadFileResultVo> results = projectTesteeFileService.saveUploadProjectFileWithParam(files, param);

        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        
        for (UploadFileResultVo result : results) {
            assertNotNull(result.getFileId());
            assertNotNull(result.getUrl());
            assertNotNull(result.getOriginalFilename());
        }
    }

    /**
     * 测试参数构建器模式
     */
    @Test
    public void testParameterBuilder() {
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId("user123")
                .projectId("project456")
                .visitId("visit789")
                .formId("form101")
                .testeeId("testee404")
                .medicalType("medical_type_01")
                .openOCR("1")
                .batchUpload("0");

        assertEquals("user123", param.getCreateUserId());
        assertEquals("project456", param.getProjectId());
        assertEquals("visit789", param.getVisitId());
        assertEquals("form101", param.getFormId());
        assertEquals("testee404", param.getTesteeId());
        assertEquals("medical_type_01", param.getMedicalType());
        assertEquals("1", param.getOpenOCR());
        assertEquals("0", param.getBatchUpload());
    }

    /**
     * 测试异步文件上传服务
     */
    @Test
    public void testAsyncFileUpload() throws Exception {
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "async_test.jpg", 
                "image/jpeg", 
                "async test content".getBytes()
        );

        String path = "test/async_upload/test.jpg";

        // 执行异步上传
        CompletableFuture<Boolean> future = asyncFileUploadService.uploadToLocalStorageAsync(file, path);

        // 等待异步操作完成
        Boolean result = future.get();

        // 验证结果
        assertNotNull(result);
        // 注意：如果当前使用本地存储，异步上传会直接返回true而不执行实际上传
    }

    /**
     * 测试批量异步文件上传
     */
    @Test
    public void testBatchAsyncFileUpload() throws Exception {
        MockMultipartFile file1 = new MockMultipartFile(
                "file1", 
                "batch_test1.jpg", 
                "image/jpeg", 
                "batch test content 1".getBytes()
        );
        
        MockMultipartFile file2 = new MockMultipartFile(
                "file2", 
                "batch_test2.png", 
                "image/png", 
                "batch test content 2".getBytes()
        );

        MultipartFile[] files = {file1, file2};
        String[] paths = {"test/batch1.jpg", "test/batch2.png"};

        // 执行批量异步上传
        CompletableFuture<Boolean> future = asyncFileUploadService.batchUploadToLocalStorageAsync(files, paths);

        // 等待异步操作完成
        Boolean result = future.get();

        // 验证结果
        assertNotNull(result);
    }

    /**
     * 测试空文件数组的处理
     */
    @Test
    public void testEmptyFileArray() throws IOException {
        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId("test_user")
                .projectId("1")
                .testeeId("1")
                .medicalType("default");

        // 测试null文件数组
        List<UploadFileResultVo> results1 = projectTesteeFileService.saveUploadProjectFileWithParam(null, param);
        assertNotNull(results1);
        assertTrue(results1.isEmpty());

        // 测试空文件数组
        List<UploadFileResultVo> results2 = projectTesteeFileService.saveUploadProjectFileWithParam(new MultipartFile[0], param);
        assertNotNull(results2);
        assertTrue(results2.isEmpty());
    }

    /**
     * 测试OCR功能参数
     */
    @Test
    public void testOcrParameters() throws IOException {
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "ocr_test.jpg", 
                "image/jpeg", 
                "ocr test content".getBytes()
        );

        SaveUploadProjectFileParam param = SaveUploadProjectFileParam.builder()
                .createUserId("test_user")
                .projectId("1")
                .testeeId("1")
                .medicalType("medical_type_12")  // 使用支持OCR的医疗类型
                .openOCR("1")
                .extendStruct("1")
                .generalAccurate("1")
                .templateId("template123");

        List<UploadFileResultVo> results = projectTesteeFileService.saveUploadProjectFileWithParam(new MultipartFile[]{file}, param);

        assertNotNull(results);
        assertEquals(1, results.size());
        
        UploadFileResultVo result = results.get(0);
        assertNotNull(result.getFileId());
        // OCR结果可能为null，取决于OCR服务的可用性
    }
}
