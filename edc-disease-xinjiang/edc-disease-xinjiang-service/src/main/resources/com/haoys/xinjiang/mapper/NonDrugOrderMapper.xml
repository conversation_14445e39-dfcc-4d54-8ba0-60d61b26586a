<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.NonDrugOrderMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.NonDrugOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_create_datetime" jdbcType="TIMESTAMP" property="orderCreateDatetime" />
    <result column="order_start_datetime" jdbcType="TIMESTAMP" property="orderStartDatetime" />
    <result column="order_stop_datetime" jdbcType="TIMESTAMP" property="orderStopDatetime" />
    <result column="order_end_datetime" jdbcType="TIMESTAMP" property="orderEndDatetime" />
    <result column="order_text" jdbcType="VARCHAR" property="orderText" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="units" jdbcType="VARCHAR" property="units" />
    <result column="frequency" jdbcType="VARCHAR" property="frequency" />
    <result column="order_class" jdbcType="VARCHAR" property="orderClass" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="order_source" jdbcType="VARCHAR" property="orderSource" />
    <result column="order_create_dept" jdbcType="VARCHAR" property="orderCreateDept" />
    <result column="order_create_doctor" jdbcType="VARCHAR" property="orderCreateDoctor" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "order_create_datetime", "order_start_datetime", "order_stop_datetime", "order_end_datetime", 
    "order_text", "order_type", "order_num", "units", "frequency", "order_class", "order_status", 
    "order_source", "order_create_dept", "order_create_doctor", "patient_sn", "visit_sn", 
    "pkid"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.NonDrugOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."non_drug_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."non_drug_order"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."non_drug_order"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.NonDrugOrderExample">
    delete from "public"."non_drug_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.NonDrugOrder">
    insert into "public"."non_drug_order" ("id", "order_create_datetime", "order_start_datetime", 
      "order_stop_datetime", "order_end_datetime", 
      "order_text", "order_type", "order_num", 
      "units", "frequency", "order_class", 
      "order_status", "order_source", "order_create_dept", 
      "order_create_doctor", "patient_sn", "visit_sn", 
      "pkid")
    values (#{id,jdbcType=INTEGER}, #{orderCreateDatetime,jdbcType=TIMESTAMP}, #{orderStartDatetime,jdbcType=TIMESTAMP}, 
      #{orderStopDatetime,jdbcType=TIMESTAMP}, #{orderEndDatetime,jdbcType=TIMESTAMP}, 
      #{orderText,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{orderNum,jdbcType=VARCHAR}, 
      #{units,jdbcType=VARCHAR}, #{frequency,jdbcType=VARCHAR}, #{orderClass,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=VARCHAR}, #{orderSource,jdbcType=VARCHAR}, #{orderCreateDept,jdbcType=VARCHAR}, 
      #{orderCreateDoctor,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, 
      #{pkid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.NonDrugOrder">
    insert into "public"."non_drug_order"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="orderCreateDatetime != null">
        "order_create_datetime",
      </if>
      <if test="orderStartDatetime != null">
        "order_start_datetime",
      </if>
      <if test="orderStopDatetime != null">
        "order_stop_datetime",
      </if>
      <if test="orderEndDatetime != null">
        "order_end_datetime",
      </if>
      <if test="orderText != null">
        "order_text",
      </if>
      <if test="orderType != null">
        "order_type",
      </if>
      <if test="orderNum != null">
        "order_num",
      </if>
      <if test="units != null">
        "units",
      </if>
      <if test="frequency != null">
        "frequency",
      </if>
      <if test="orderClass != null">
        "order_class",
      </if>
      <if test="orderStatus != null">
        "order_status",
      </if>
      <if test="orderSource != null">
        "order_source",
      </if>
      <if test="orderCreateDept != null">
        "order_create_dept",
      </if>
      <if test="orderCreateDoctor != null">
        "order_create_doctor",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderCreateDatetime != null">
        #{orderCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStartDatetime != null">
        #{orderStartDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStopDatetime != null">
        #{orderStopDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndDatetime != null">
        #{orderEndDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderText != null">
        #{orderText,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="units != null">
        #{units,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        #{orderClass,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDept != null">
        #{orderCreateDept,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDoctor != null">
        #{orderCreateDoctor,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.NonDrugOrderExample" resultType="java.lang.Long">
    select count(*) from "public"."non_drug_order"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."non_drug_order"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderCreateDatetime != null">
        "order_create_datetime" = #{record.orderCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStartDatetime != null">
        "order_start_datetime" = #{record.orderStartDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStopDatetime != null">
        "order_stop_datetime" = #{record.orderStopDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderEndDatetime != null">
        "order_end_datetime" = #{record.orderEndDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderText != null">
        "order_text" = #{record.orderText,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        "order_type" = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNum != null">
        "order_num" = #{record.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.units != null">
        "units" = #{record.units,jdbcType=VARCHAR},
      </if>
      <if test="record.frequency != null">
        "frequency" = #{record.frequency,jdbcType=VARCHAR},
      </if>
      <if test="record.orderClass != null">
        "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null">
        "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null">
        "order_source" = #{record.orderSource,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCreateDept != null">
        "order_create_dept" = #{record.orderCreateDept,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCreateDoctor != null">
        "order_create_doctor" = #{record.orderCreateDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."non_drug_order"
    set "id" = #{record.id,jdbcType=INTEGER},
      "order_create_datetime" = #{record.orderCreateDatetime,jdbcType=TIMESTAMP},
      "order_start_datetime" = #{record.orderStartDatetime,jdbcType=TIMESTAMP},
      "order_stop_datetime" = #{record.orderStopDatetime,jdbcType=TIMESTAMP},
      "order_end_datetime" = #{record.orderEndDatetime,jdbcType=TIMESTAMP},
      "order_text" = #{record.orderText,jdbcType=VARCHAR},
      "order_type" = #{record.orderType,jdbcType=VARCHAR},
      "order_num" = #{record.orderNum,jdbcType=VARCHAR},
      "units" = #{record.units,jdbcType=VARCHAR},
      "frequency" = #{record.frequency,jdbcType=VARCHAR},
      "order_class" = #{record.orderClass,jdbcType=VARCHAR},
      "order_status" = #{record.orderStatus,jdbcType=VARCHAR},
      "order_source" = #{record.orderSource,jdbcType=VARCHAR},
      "order_create_dept" = #{record.orderCreateDept,jdbcType=VARCHAR},
      "order_create_doctor" = #{record.orderCreateDoctor,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.NonDrugOrder">
    update "public"."non_drug_order"
    <set>
      <if test="orderCreateDatetime != null">
        "order_create_datetime" = #{orderCreateDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStartDatetime != null">
        "order_start_datetime" = #{orderStartDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStopDatetime != null">
        "order_stop_datetime" = #{orderStopDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndDatetime != null">
        "order_end_datetime" = #{orderEndDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderText != null">
        "order_text" = #{orderText,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        "order_type" = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderNum != null">
        "order_num" = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="units != null">
        "units" = #{units,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        "frequency" = #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="orderClass != null">
        "order_class" = #{orderClass,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        "order_status" = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        "order_source" = #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDept != null">
        "order_create_dept" = #{orderCreateDept,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateDoctor != null">
        "order_create_doctor" = #{orderCreateDoctor,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.NonDrugOrder">
    update "public"."non_drug_order"
    set "order_create_datetime" = #{orderCreateDatetime,jdbcType=TIMESTAMP},
      "order_start_datetime" = #{orderStartDatetime,jdbcType=TIMESTAMP},
      "order_stop_datetime" = #{orderStopDatetime,jdbcType=TIMESTAMP},
      "order_end_datetime" = #{orderEndDatetime,jdbcType=TIMESTAMP},
      "order_text" = #{orderText,jdbcType=VARCHAR},
      "order_type" = #{orderType,jdbcType=VARCHAR},
      "order_num" = #{orderNum,jdbcType=VARCHAR},
      "units" = #{units,jdbcType=VARCHAR},
      "frequency" = #{frequency,jdbcType=VARCHAR},
      "order_class" = #{orderClass,jdbcType=VARCHAR},
      "order_status" = #{orderStatus,jdbcType=VARCHAR},
      "order_source" = #{orderSource,jdbcType=VARCHAR},
      "order_create_dept" = #{orderCreateDept,jdbcType=VARCHAR},
      "order_create_doctor" = #{orderCreateDoctor,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>