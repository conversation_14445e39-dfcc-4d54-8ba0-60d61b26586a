<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.EventRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.EventRecord">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="column_name" jdbcType="VARCHAR" property="columnName" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="event_start_index" jdbcType="INTEGER" property="eventStartIndex" />
    <result column="event_end_index" jdbcType="INTEGER" property="eventEndIndex" />
    <result column="event_name" jdbcType="VARCHAR" property="eventName" />
    <result column="event_entity_type" jdbcType="VARCHAR" property="eventEntityType" />
    <result column="standard_name" jdbcType="VARCHAR" property="standardName" />
    <result column="is_sub_entity" jdbcType="VARCHAR" property="isSubEntity" />
    <result column="negative_word" jdbcType="VARCHAR" property="negativeWord" />
    <result column="probability" jdbcType="VARCHAR" property="probability" />
    <result column="duration" jdbcType="VARCHAR" property="duration" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="standard_start_date" jdbcType="VARCHAR" property="standardStartDate" />
    <result column="standard_end_date" jdbcType="VARCHAR" property="standardEndDate" />
    <result column="event_body" jdbcType="VARCHAR" property="eventBody" />
    <result column="body_direction" jdbcType="VARCHAR" property="bodyDirection" />
    <result column="medicinal_problem_type" jdbcType="VARCHAR" property="medicinalProblemType" />
    <result column="medical_condition" jdbcType="VARCHAR" property="medicalCondition" />
    <result column="event_qualifier" jdbcType="VARCHAR" property="eventQualifier" />
    <result column="grade" jdbcType="VARCHAR" property="grade" />
    <result column="measurements" jdbcType="VARCHAR" property="measurements" />
    <result column="efficacy_evaluation" jdbcType="VARCHAR" property="efficacyEvaluation" />
    <result column="blood_signal" jdbcType="VARCHAR" property="bloodSignal" />
    <result column="blood_signal_level" jdbcType="VARCHAR" property="bloodSignalLevel" />
    <result column="texture" jdbcType="VARCHAR" property="texture" />
    <result column="size" jdbcType="VARCHAR" property="size" />
    <result column="boundary" jdbcType="VARCHAR" property="boundary" />
    <result column="morphology" jdbcType="VARCHAR" property="morphology" />
    <result column="metabolism" jdbcType="VARCHAR" property="metabolism" />
    <result column="max_value" jdbcType="VARCHAR" property="maxValue" />
    <result column="echo" jdbcType="VARCHAR" property="echo" />
    <result column="necrosis" jdbcType="VARCHAR" property="necrosis" />
    <result column="calcification" jdbcType="VARCHAR" property="calcification" />
    <result column="patho_pattern" jdbcType="VARCHAR" property="pathoPattern" />
    <result column="patho_grade" jdbcType="VARCHAR" property="pathoGrade" />
    <result column="differentiation" jdbcType="VARCHAR" property="differentiation" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="examination_method" jdbcType="VARCHAR" property="examinationMethod" />
    <result column="qualitative_conclusion" jdbcType="VARCHAR" property="qualitativeConclusion" />
    <result column="approach" jdbcType="VARCHAR" property="approach" />
    <result column="dosage" jdbcType="VARCHAR" property="dosage" />
    <result column="frequency" jdbcType="VARCHAR" property="frequency" />
    <result column="period" jdbcType="VARCHAR" property="period" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="operation_method" jdbcType="VARCHAR" property="operationMethod" />
    <result column="instrument" jdbcType="VARCHAR" property="instrument" />
    <result column="anesthesia_method" jdbcType="VARCHAR" property="anesthesiaMethod" />
    <result column="has_marry_history" jdbcType="VARCHAR" property="hasMarryHistory" />
    <result column="marry_time" jdbcType="VARCHAR" property="marryTime" />
    <result column="marry_age" jdbcType="VARCHAR" property="marryAge" />
    <result column="partner_health_status" jdbcType="VARCHAR" property="partnerHealthStatus" />
    <result column="partner_illness_death_cause" jdbcType="VARCHAR" property="partnerIllnessDeathCause" />
    <result column="childbearing_history" jdbcType="VARCHAR" property="childbearingHistory" />
    <result column="children_status" jdbcType="VARCHAR" property="childrenStatus" />
    <result column="pregnancy_time" jdbcType="VARCHAR" property="pregnancyTime" />
    <result column="give_birth_time" jdbcType="VARCHAR" property="giveBirthTime" />
    <result column="misbirth_time" jdbcType="VARCHAR" property="misbirthTime" />
    <result column="has_drink_history" jdbcType="VARCHAR" property="hasDrinkHistory" />
    <result column="drink_type" jdbcType="VARCHAR" property="drinkType" />
    <result column="drink_time" jdbcType="VARCHAR" property="drinkTime" />
    <result column="drink_year" jdbcType="VARCHAR" property="drinkYear" />
    <result column="drink_freq" jdbcType="VARCHAR" property="drinkFreq" />
    <result column="drink_amount" jdbcType="VARCHAR" property="drinkAmount" />
    <result column="drink_unit" jdbcType="VARCHAR" property="drinkUnit" />
    <result column="has_alcohol" jdbcType="VARCHAR" property="hasAlcohol" />
    <result column="alcohol_time" jdbcType="VARCHAR" property="alcoholTime" />
    <result column="alcohol_year" jdbcType="VARCHAR" property="alcoholYear" />
    <result column="has_smoke" jdbcType="VARCHAR" property="hasSmoke" />
    <result column="smoke_time" jdbcType="VARCHAR" property="smokeTime" />
    <result column="smoke_year" jdbcType="VARCHAR" property="smokeYear" />
    <result column="smoke_freq" jdbcType="VARCHAR" property="smokeFreq" />
    <result column="smoke_amount" jdbcType="VARCHAR" property="smokeAmount" />
    <result column="smoke_unit" jdbcType="VARCHAR" property="smokeUnit" />
    <result column="has_quit_smoke" jdbcType="VARCHAR" property="hasQuitSmoke" />
    <result column="has_quit_smoke_time" jdbcType="VARCHAR" property="hasQuitSmokeTime" />
    <result column="has_quit_smoke_year" jdbcType="VARCHAR" property="hasQuitSmokeYear" />
    <result column="has_menstruation" jdbcType="VARCHAR" property="hasMenstruation" />
    <result column="first_menstruation_time" jdbcType="VARCHAR" property="firstMenstruationTime" />
    <result column="first_menstruation_age" jdbcType="VARCHAR" property="firstMenstruationAge" />
    <result column="menstruation_duration" jdbcType="VARCHAR" property="menstruationDuration" />
    <result column="menstruation_period" jdbcType="VARCHAR" property="menstruationPeriod" />
    <result column="menstruation_amount" jdbcType="VARCHAR" property="menstruationAmount" />
    <result column="menstruation_colic" jdbcType="VARCHAR" property="menstruationColic" />
    <result column="last_menstruation" jdbcType="VARCHAR" property="lastMenstruation" />
    <result column="over_time" jdbcType="VARCHAR" property="overTime" />
    <result column="over_age" jdbcType="VARCHAR" property="overAge" />
    <result column="is_menopause" jdbcType="VARCHAR" property="isMenopause" />
    <result column="allergen" jdbcType="VARCHAR" property="allergen" />
    <result column="has_allergy" jdbcType="VARCHAR" property="hasAllergy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "table_name", "column_name", "patient_sn", "visit_sn", "pkid", "event_type", 
    "event_start_index", "event_end_index", "event_name", "event_entity_type", "standard_name", 
    "is_sub_entity", "negative_word", "probability", "duration", "start_date", "end_date", 
    "standard_start_date", "standard_end_date", "event_body", "body_direction", "medicinal_problem_type", 
    "medical_condition", "event_qualifier", "grade", "measurements", "efficacy_evaluation", 
    "blood_signal", "blood_signal_level", "texture", "size", "boundary", "morphology", 
    "metabolism", "max_value", "echo", "necrosis", "calcification", "patho_pattern", 
    "patho_grade", "differentiation", "status", "examination_method", "qualitative_conclusion", 
    "approach", "dosage", "frequency", "period", "category", "operation_method", "instrument", 
    "anesthesia_method", "has_marry_history", "marry_time", "marry_age", "partner_health_status", 
    "partner_illness_death_cause", "childbearing_history", "children_status", "pregnancy_time", 
    "give_birth_time", "misbirth_time", "has_drink_history", "drink_type", "drink_time", 
    "drink_year", "drink_freq", "drink_amount", "drink_unit", "has_alcohol", "alcohol_time", 
    "alcohol_year", "has_smoke", "smoke_time", "smoke_year", "smoke_freq", "smoke_amount", 
    "smoke_unit", "has_quit_smoke", "has_quit_smoke_time", "has_quit_smoke_year", "has_menstruation", 
    "first_menstruation_time", "first_menstruation_age", "menstruation_duration", "menstruation_period", 
    "menstruation_amount", "menstruation_colic", "last_menstruation", "over_time", "over_age", 
    "is_menopause", "allergen", "has_allergy"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.EventRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."event_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."event_record"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."event_record"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.EventRecordExample">
    delete from "public"."event_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.EventRecord">
    insert into "public"."event_record" ("id", "table_name", "column_name", 
      "patient_sn", "visit_sn", "pkid", 
      "event_type", "event_start_index", "event_end_index", 
      "event_name", "event_entity_type", "standard_name", 
      "is_sub_entity", "negative_word", "probability", 
      "duration", "start_date", "end_date", 
      "standard_start_date", "standard_end_date", "event_body", 
      "body_direction", "medicinal_problem_type", "medical_condition", 
      "event_qualifier", "grade", "measurements", 
      "efficacy_evaluation", "blood_signal", "blood_signal_level", 
      "texture", "size", "boundary", 
      "morphology", "metabolism", "max_value", 
      "echo", "necrosis", "calcification", 
      "patho_pattern", "patho_grade", "differentiation", 
      "status", "examination_method", "qualitative_conclusion", 
      "approach", "dosage", "frequency", 
      "period", "category", "operation_method", 
      "instrument", "anesthesia_method", "has_marry_history", 
      "marry_time", "marry_age", "partner_health_status", 
      "partner_illness_death_cause", "childbearing_history", 
      "children_status", "pregnancy_time", "give_birth_time", 
      "misbirth_time", "has_drink_history", "drink_type", 
      "drink_time", "drink_year", "drink_freq", 
      "drink_amount", "drink_unit", "has_alcohol", 
      "alcohol_time", "alcohol_year", "has_smoke", 
      "smoke_time", "smoke_year", "smoke_freq", 
      "smoke_amount", "smoke_unit", "has_quit_smoke", 
      "has_quit_smoke_time", "has_quit_smoke_year", "has_menstruation", 
      "first_menstruation_time", "first_menstruation_age", 
      "menstruation_duration", "menstruation_period", 
      "menstruation_amount", "menstruation_colic", 
      "last_menstruation", "over_time", "over_age", 
      "is_menopause", "allergen", "has_allergy"
      )
    values (#{id,jdbcType=VARCHAR}, #{tableName,jdbcType=VARCHAR}, #{columnName,jdbcType=VARCHAR}, 
      #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}, 
      #{eventType,jdbcType=VARCHAR}, #{eventStartIndex,jdbcType=INTEGER}, #{eventEndIndex,jdbcType=INTEGER}, 
      #{eventName,jdbcType=VARCHAR}, #{eventEntityType,jdbcType=VARCHAR}, #{standardName,jdbcType=VARCHAR}, 
      #{isSubEntity,jdbcType=VARCHAR}, #{negativeWord,jdbcType=VARCHAR}, #{probability,jdbcType=VARCHAR}, 
      #{duration,jdbcType=VARCHAR}, #{startDate,jdbcType=VARCHAR}, #{endDate,jdbcType=VARCHAR}, 
      #{standardStartDate,jdbcType=VARCHAR}, #{standardEndDate,jdbcType=VARCHAR}, #{eventBody,jdbcType=VARCHAR}, 
      #{bodyDirection,jdbcType=VARCHAR}, #{medicinalProblemType,jdbcType=VARCHAR}, #{medicalCondition,jdbcType=VARCHAR}, 
      #{eventQualifier,jdbcType=VARCHAR}, #{grade,jdbcType=VARCHAR}, #{measurements,jdbcType=VARCHAR}, 
      #{efficacyEvaluation,jdbcType=VARCHAR}, #{bloodSignal,jdbcType=VARCHAR}, #{bloodSignalLevel,jdbcType=VARCHAR}, 
      #{texture,jdbcType=VARCHAR}, #{size,jdbcType=VARCHAR}, #{boundary,jdbcType=VARCHAR}, 
      #{morphology,jdbcType=VARCHAR}, #{metabolism,jdbcType=VARCHAR}, #{maxValue,jdbcType=VARCHAR}, 
      #{echo,jdbcType=VARCHAR}, #{necrosis,jdbcType=VARCHAR}, #{calcification,jdbcType=VARCHAR}, 
      #{pathoPattern,jdbcType=VARCHAR}, #{pathoGrade,jdbcType=VARCHAR}, #{differentiation,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{examinationMethod,jdbcType=VARCHAR}, #{qualitativeConclusion,jdbcType=VARCHAR}, 
      #{approach,jdbcType=VARCHAR}, #{dosage,jdbcType=VARCHAR}, #{frequency,jdbcType=VARCHAR}, 
      #{period,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{operationMethod,jdbcType=VARCHAR}, 
      #{instrument,jdbcType=VARCHAR}, #{anesthesiaMethod,jdbcType=VARCHAR}, #{hasMarryHistory,jdbcType=VARCHAR}, 
      #{marryTime,jdbcType=VARCHAR}, #{marryAge,jdbcType=VARCHAR}, #{partnerHealthStatus,jdbcType=VARCHAR}, 
      #{partnerIllnessDeathCause,jdbcType=VARCHAR}, #{childbearingHistory,jdbcType=VARCHAR}, 
      #{childrenStatus,jdbcType=VARCHAR}, #{pregnancyTime,jdbcType=VARCHAR}, #{giveBirthTime,jdbcType=VARCHAR}, 
      #{misbirthTime,jdbcType=VARCHAR}, #{hasDrinkHistory,jdbcType=VARCHAR}, #{drinkType,jdbcType=VARCHAR}, 
      #{drinkTime,jdbcType=VARCHAR}, #{drinkYear,jdbcType=VARCHAR}, #{drinkFreq,jdbcType=VARCHAR}, 
      #{drinkAmount,jdbcType=VARCHAR}, #{drinkUnit,jdbcType=VARCHAR}, #{hasAlcohol,jdbcType=VARCHAR}, 
      #{alcoholTime,jdbcType=VARCHAR}, #{alcoholYear,jdbcType=VARCHAR}, #{hasSmoke,jdbcType=VARCHAR}, 
      #{smokeTime,jdbcType=VARCHAR}, #{smokeYear,jdbcType=VARCHAR}, #{smokeFreq,jdbcType=VARCHAR}, 
      #{smokeAmount,jdbcType=VARCHAR}, #{smokeUnit,jdbcType=VARCHAR}, #{hasQuitSmoke,jdbcType=VARCHAR}, 
      #{hasQuitSmokeTime,jdbcType=VARCHAR}, #{hasQuitSmokeYear,jdbcType=VARCHAR}, #{hasMenstruation,jdbcType=VARCHAR}, 
      #{firstMenstruationTime,jdbcType=VARCHAR}, #{firstMenstruationAge,jdbcType=VARCHAR}, 
      #{menstruationDuration,jdbcType=VARCHAR}, #{menstruationPeriod,jdbcType=VARCHAR}, 
      #{menstruationAmount,jdbcType=VARCHAR}, #{menstruationColic,jdbcType=VARCHAR}, 
      #{lastMenstruation,jdbcType=VARCHAR}, #{overTime,jdbcType=VARCHAR}, #{overAge,jdbcType=VARCHAR}, 
      #{isMenopause,jdbcType=VARCHAR}, #{allergen,jdbcType=VARCHAR}, #{hasAllergy,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.EventRecord">
    insert into "public"."event_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="tableName != null">
        "table_name",
      </if>
      <if test="columnName != null">
        "column_name",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
      <if test="eventType != null">
        "event_type",
      </if>
      <if test="eventStartIndex != null">
        "event_start_index",
      </if>
      <if test="eventEndIndex != null">
        "event_end_index",
      </if>
      <if test="eventName != null">
        "event_name",
      </if>
      <if test="eventEntityType != null">
        "event_entity_type",
      </if>
      <if test="standardName != null">
        "standard_name",
      </if>
      <if test="isSubEntity != null">
        "is_sub_entity",
      </if>
      <if test="negativeWord != null">
        "negative_word",
      </if>
      <if test="probability != null">
        "probability",
      </if>
      <if test="duration != null">
        "duration",
      </if>
      <if test="startDate != null">
        "start_date",
      </if>
      <if test="endDate != null">
        "end_date",
      </if>
      <if test="standardStartDate != null">
        "standard_start_date",
      </if>
      <if test="standardEndDate != null">
        "standard_end_date",
      </if>
      <if test="eventBody != null">
        "event_body",
      </if>
      <if test="bodyDirection != null">
        "body_direction",
      </if>
      <if test="medicinalProblemType != null">
        "medicinal_problem_type",
      </if>
      <if test="medicalCondition != null">
        "medical_condition",
      </if>
      <if test="eventQualifier != null">
        "event_qualifier",
      </if>
      <if test="grade != null">
        "grade",
      </if>
      <if test="measurements != null">
        "measurements",
      </if>
      <if test="efficacyEvaluation != null">
        "efficacy_evaluation",
      </if>
      <if test="bloodSignal != null">
        "blood_signal",
      </if>
      <if test="bloodSignalLevel != null">
        "blood_signal_level",
      </if>
      <if test="texture != null">
        "texture",
      </if>
      <if test="size != null">
        "size",
      </if>
      <if test="boundary != null">
        "boundary",
      </if>
      <if test="morphology != null">
        "morphology",
      </if>
      <if test="metabolism != null">
        "metabolism",
      </if>
      <if test="maxValue != null">
        "max_value",
      </if>
      <if test="echo != null">
        "echo",
      </if>
      <if test="necrosis != null">
        "necrosis",
      </if>
      <if test="calcification != null">
        "calcification",
      </if>
      <if test="pathoPattern != null">
        "patho_pattern",
      </if>
      <if test="pathoGrade != null">
        "patho_grade",
      </if>
      <if test="differentiation != null">
        "differentiation",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="examinationMethod != null">
        "examination_method",
      </if>
      <if test="qualitativeConclusion != null">
        "qualitative_conclusion",
      </if>
      <if test="approach != null">
        "approach",
      </if>
      <if test="dosage != null">
        "dosage",
      </if>
      <if test="frequency != null">
        "frequency",
      </if>
      <if test="period != null">
        "period",
      </if>
      <if test="category != null">
        "category",
      </if>
      <if test="operationMethod != null">
        "operation_method",
      </if>
      <if test="instrument != null">
        "instrument",
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method",
      </if>
      <if test="hasMarryHistory != null">
        "has_marry_history",
      </if>
      <if test="marryTime != null">
        "marry_time",
      </if>
      <if test="marryAge != null">
        "marry_age",
      </if>
      <if test="partnerHealthStatus != null">
        "partner_health_status",
      </if>
      <if test="partnerIllnessDeathCause != null">
        "partner_illness_death_cause",
      </if>
      <if test="childbearingHistory != null">
        "childbearing_history",
      </if>
      <if test="childrenStatus != null">
        "children_status",
      </if>
      <if test="pregnancyTime != null">
        "pregnancy_time",
      </if>
      <if test="giveBirthTime != null">
        "give_birth_time",
      </if>
      <if test="misbirthTime != null">
        "misbirth_time",
      </if>
      <if test="hasDrinkHistory != null">
        "has_drink_history",
      </if>
      <if test="drinkType != null">
        "drink_type",
      </if>
      <if test="drinkTime != null">
        "drink_time",
      </if>
      <if test="drinkYear != null">
        "drink_year",
      </if>
      <if test="drinkFreq != null">
        "drink_freq",
      </if>
      <if test="drinkAmount != null">
        "drink_amount",
      </if>
      <if test="drinkUnit != null">
        "drink_unit",
      </if>
      <if test="hasAlcohol != null">
        "has_alcohol",
      </if>
      <if test="alcoholTime != null">
        "alcohol_time",
      </if>
      <if test="alcoholYear != null">
        "alcohol_year",
      </if>
      <if test="hasSmoke != null">
        "has_smoke",
      </if>
      <if test="smokeTime != null">
        "smoke_time",
      </if>
      <if test="smokeYear != null">
        "smoke_year",
      </if>
      <if test="smokeFreq != null">
        "smoke_freq",
      </if>
      <if test="smokeAmount != null">
        "smoke_amount",
      </if>
      <if test="smokeUnit != null">
        "smoke_unit",
      </if>
      <if test="hasQuitSmoke != null">
        "has_quit_smoke",
      </if>
      <if test="hasQuitSmokeTime != null">
        "has_quit_smoke_time",
      </if>
      <if test="hasQuitSmokeYear != null">
        "has_quit_smoke_year",
      </if>
      <if test="hasMenstruation != null">
        "has_menstruation",
      </if>
      <if test="firstMenstruationTime != null">
        "first_menstruation_time",
      </if>
      <if test="firstMenstruationAge != null">
        "first_menstruation_age",
      </if>
      <if test="menstruationDuration != null">
        "menstruation_duration",
      </if>
      <if test="menstruationPeriod != null">
        "menstruation_period",
      </if>
      <if test="menstruationAmount != null">
        "menstruation_amount",
      </if>
      <if test="menstruationColic != null">
        "menstruation_colic",
      </if>
      <if test="lastMenstruation != null">
        "last_menstruation",
      </if>
      <if test="overTime != null">
        "over_time",
      </if>
      <if test="overAge != null">
        "over_age",
      </if>
      <if test="isMenopause != null">
        "is_menopause",
      </if>
      <if test="allergen != null">
        "allergen",
      </if>
      <if test="hasAllergy != null">
        "has_allergy",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="eventStartIndex != null">
        #{eventStartIndex,jdbcType=INTEGER},
      </if>
      <if test="eventEndIndex != null">
        #{eventEndIndex,jdbcType=INTEGER},
      </if>
      <if test="eventName != null">
        #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="eventEntityType != null">
        #{eventEntityType,jdbcType=VARCHAR},
      </if>
      <if test="standardName != null">
        #{standardName,jdbcType=VARCHAR},
      </if>
      <if test="isSubEntity != null">
        #{isSubEntity,jdbcType=VARCHAR},
      </if>
      <if test="negativeWord != null">
        #{negativeWord,jdbcType=VARCHAR},
      </if>
      <if test="probability != null">
        #{probability,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="standardStartDate != null">
        #{standardStartDate,jdbcType=VARCHAR},
      </if>
      <if test="standardEndDate != null">
        #{standardEndDate,jdbcType=VARCHAR},
      </if>
      <if test="eventBody != null">
        #{eventBody,jdbcType=VARCHAR},
      </if>
      <if test="bodyDirection != null">
        #{bodyDirection,jdbcType=VARCHAR},
      </if>
      <if test="medicinalProblemType != null">
        #{medicinalProblemType,jdbcType=VARCHAR},
      </if>
      <if test="medicalCondition != null">
        #{medicalCondition,jdbcType=VARCHAR},
      </if>
      <if test="eventQualifier != null">
        #{eventQualifier,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=VARCHAR},
      </if>
      <if test="measurements != null">
        #{measurements,jdbcType=VARCHAR},
      </if>
      <if test="efficacyEvaluation != null">
        #{efficacyEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="bloodSignal != null">
        #{bloodSignal,jdbcType=VARCHAR},
      </if>
      <if test="bloodSignalLevel != null">
        #{bloodSignalLevel,jdbcType=VARCHAR},
      </if>
      <if test="texture != null">
        #{texture,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=VARCHAR},
      </if>
      <if test="boundary != null">
        #{boundary,jdbcType=VARCHAR},
      </if>
      <if test="morphology != null">
        #{morphology,jdbcType=VARCHAR},
      </if>
      <if test="metabolism != null">
        #{metabolism,jdbcType=VARCHAR},
      </if>
      <if test="maxValue != null">
        #{maxValue,jdbcType=VARCHAR},
      </if>
      <if test="echo != null">
        #{echo,jdbcType=VARCHAR},
      </if>
      <if test="necrosis != null">
        #{necrosis,jdbcType=VARCHAR},
      </if>
      <if test="calcification != null">
        #{calcification,jdbcType=VARCHAR},
      </if>
      <if test="pathoPattern != null">
        #{pathoPattern,jdbcType=VARCHAR},
      </if>
      <if test="pathoGrade != null">
        #{pathoGrade,jdbcType=VARCHAR},
      </if>
      <if test="differentiation != null">
        #{differentiation,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="examinationMethod != null">
        #{examinationMethod,jdbcType=VARCHAR},
      </if>
      <if test="qualitativeConclusion != null">
        #{qualitativeConclusion,jdbcType=VARCHAR},
      </if>
      <if test="approach != null">
        #{approach,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="period != null">
        #{period,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="operationMethod != null">
        #{operationMethod,jdbcType=VARCHAR},
      </if>
      <if test="instrument != null">
        #{instrument,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="hasMarryHistory != null">
        #{hasMarryHistory,jdbcType=VARCHAR},
      </if>
      <if test="marryTime != null">
        #{marryTime,jdbcType=VARCHAR},
      </if>
      <if test="marryAge != null">
        #{marryAge,jdbcType=VARCHAR},
      </if>
      <if test="partnerHealthStatus != null">
        #{partnerHealthStatus,jdbcType=VARCHAR},
      </if>
      <if test="partnerIllnessDeathCause != null">
        #{partnerIllnessDeathCause,jdbcType=VARCHAR},
      </if>
      <if test="childbearingHistory != null">
        #{childbearingHistory,jdbcType=VARCHAR},
      </if>
      <if test="childrenStatus != null">
        #{childrenStatus,jdbcType=VARCHAR},
      </if>
      <if test="pregnancyTime != null">
        #{pregnancyTime,jdbcType=VARCHAR},
      </if>
      <if test="giveBirthTime != null">
        #{giveBirthTime,jdbcType=VARCHAR},
      </if>
      <if test="misbirthTime != null">
        #{misbirthTime,jdbcType=VARCHAR},
      </if>
      <if test="hasDrinkHistory != null">
        #{hasDrinkHistory,jdbcType=VARCHAR},
      </if>
      <if test="drinkType != null">
        #{drinkType,jdbcType=VARCHAR},
      </if>
      <if test="drinkTime != null">
        #{drinkTime,jdbcType=VARCHAR},
      </if>
      <if test="drinkYear != null">
        #{drinkYear,jdbcType=VARCHAR},
      </if>
      <if test="drinkFreq != null">
        #{drinkFreq,jdbcType=VARCHAR},
      </if>
      <if test="drinkAmount != null">
        #{drinkAmount,jdbcType=VARCHAR},
      </if>
      <if test="drinkUnit != null">
        #{drinkUnit,jdbcType=VARCHAR},
      </if>
      <if test="hasAlcohol != null">
        #{hasAlcohol,jdbcType=VARCHAR},
      </if>
      <if test="alcoholTime != null">
        #{alcoholTime,jdbcType=VARCHAR},
      </if>
      <if test="alcoholYear != null">
        #{alcoholYear,jdbcType=VARCHAR},
      </if>
      <if test="hasSmoke != null">
        #{hasSmoke,jdbcType=VARCHAR},
      </if>
      <if test="smokeTime != null">
        #{smokeTime,jdbcType=VARCHAR},
      </if>
      <if test="smokeYear != null">
        #{smokeYear,jdbcType=VARCHAR},
      </if>
      <if test="smokeFreq != null">
        #{smokeFreq,jdbcType=VARCHAR},
      </if>
      <if test="smokeAmount != null">
        #{smokeAmount,jdbcType=VARCHAR},
      </if>
      <if test="smokeUnit != null">
        #{smokeUnit,jdbcType=VARCHAR},
      </if>
      <if test="hasQuitSmoke != null">
        #{hasQuitSmoke,jdbcType=VARCHAR},
      </if>
      <if test="hasQuitSmokeTime != null">
        #{hasQuitSmokeTime,jdbcType=VARCHAR},
      </if>
      <if test="hasQuitSmokeYear != null">
        #{hasQuitSmokeYear,jdbcType=VARCHAR},
      </if>
      <if test="hasMenstruation != null">
        #{hasMenstruation,jdbcType=VARCHAR},
      </if>
      <if test="firstMenstruationTime != null">
        #{firstMenstruationTime,jdbcType=VARCHAR},
      </if>
      <if test="firstMenstruationAge != null">
        #{firstMenstruationAge,jdbcType=VARCHAR},
      </if>
      <if test="menstruationDuration != null">
        #{menstruationDuration,jdbcType=VARCHAR},
      </if>
      <if test="menstruationPeriod != null">
        #{menstruationPeriod,jdbcType=VARCHAR},
      </if>
      <if test="menstruationAmount != null">
        #{menstruationAmount,jdbcType=VARCHAR},
      </if>
      <if test="menstruationColic != null">
        #{menstruationColic,jdbcType=VARCHAR},
      </if>
      <if test="lastMenstruation != null">
        #{lastMenstruation,jdbcType=VARCHAR},
      </if>
      <if test="overTime != null">
        #{overTime,jdbcType=VARCHAR},
      </if>
      <if test="overAge != null">
        #{overAge,jdbcType=VARCHAR},
      </if>
      <if test="isMenopause != null">
        #{isMenopause,jdbcType=VARCHAR},
      </if>
      <if test="allergen != null">
        #{allergen,jdbcType=VARCHAR},
      </if>
      <if test="hasAllergy != null">
        #{hasAllergy,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.EventRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."event_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."event_record"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.tableName != null">
        "table_name" = #{record.tableName,jdbcType=VARCHAR},
      </if>
      <if test="record.columnName != null">
        "column_name" = #{record.columnName,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
      <if test="record.eventType != null">
        "event_type" = #{record.eventType,jdbcType=VARCHAR},
      </if>
      <if test="record.eventStartIndex != null">
        "event_start_index" = #{record.eventStartIndex,jdbcType=INTEGER},
      </if>
      <if test="record.eventEndIndex != null">
        "event_end_index" = #{record.eventEndIndex,jdbcType=INTEGER},
      </if>
      <if test="record.eventName != null">
        "event_name" = #{record.eventName,jdbcType=VARCHAR},
      </if>
      <if test="record.eventEntityType != null">
        "event_entity_type" = #{record.eventEntityType,jdbcType=VARCHAR},
      </if>
      <if test="record.standardName != null">
        "standard_name" = #{record.standardName,jdbcType=VARCHAR},
      </if>
      <if test="record.isSubEntity != null">
        "is_sub_entity" = #{record.isSubEntity,jdbcType=VARCHAR},
      </if>
      <if test="record.negativeWord != null">
        "negative_word" = #{record.negativeWord,jdbcType=VARCHAR},
      </if>
      <if test="record.probability != null">
        "probability" = #{record.probability,jdbcType=VARCHAR},
      </if>
      <if test="record.duration != null">
        "duration" = #{record.duration,jdbcType=VARCHAR},
      </if>
      <if test="record.startDate != null">
        "start_date" = #{record.startDate,jdbcType=VARCHAR},
      </if>
      <if test="record.endDate != null">
        "end_date" = #{record.endDate,jdbcType=VARCHAR},
      </if>
      <if test="record.standardStartDate != null">
        "standard_start_date" = #{record.standardStartDate,jdbcType=VARCHAR},
      </if>
      <if test="record.standardEndDate != null">
        "standard_end_date" = #{record.standardEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.eventBody != null">
        "event_body" = #{record.eventBody,jdbcType=VARCHAR},
      </if>
      <if test="record.bodyDirection != null">
        "body_direction" = #{record.bodyDirection,jdbcType=VARCHAR},
      </if>
      <if test="record.medicinalProblemType != null">
        "medicinal_problem_type" = #{record.medicinalProblemType,jdbcType=VARCHAR},
      </if>
      <if test="record.medicalCondition != null">
        "medical_condition" = #{record.medicalCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.eventQualifier != null">
        "event_qualifier" = #{record.eventQualifier,jdbcType=VARCHAR},
      </if>
      <if test="record.grade != null">
        "grade" = #{record.grade,jdbcType=VARCHAR},
      </if>
      <if test="record.measurements != null">
        "measurements" = #{record.measurements,jdbcType=VARCHAR},
      </if>
      <if test="record.efficacyEvaluation != null">
        "efficacy_evaluation" = #{record.efficacyEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="record.bloodSignal != null">
        "blood_signal" = #{record.bloodSignal,jdbcType=VARCHAR},
      </if>
      <if test="record.bloodSignalLevel != null">
        "blood_signal_level" = #{record.bloodSignalLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.texture != null">
        "texture" = #{record.texture,jdbcType=VARCHAR},
      </if>
      <if test="record.size != null">
        "size" = #{record.size,jdbcType=VARCHAR},
      </if>
      <if test="record.boundary != null">
        "boundary" = #{record.boundary,jdbcType=VARCHAR},
      </if>
      <if test="record.morphology != null">
        "morphology" = #{record.morphology,jdbcType=VARCHAR},
      </if>
      <if test="record.metabolism != null">
        "metabolism" = #{record.metabolism,jdbcType=VARCHAR},
      </if>
      <if test="record.maxValue != null">
        "max_value" = #{record.maxValue,jdbcType=VARCHAR},
      </if>
      <if test="record.echo != null">
        "echo" = #{record.echo,jdbcType=VARCHAR},
      </if>
      <if test="record.necrosis != null">
        "necrosis" = #{record.necrosis,jdbcType=VARCHAR},
      </if>
      <if test="record.calcification != null">
        "calcification" = #{record.calcification,jdbcType=VARCHAR},
      </if>
      <if test="record.pathoPattern != null">
        "patho_pattern" = #{record.pathoPattern,jdbcType=VARCHAR},
      </if>
      <if test="record.pathoGrade != null">
        "patho_grade" = #{record.pathoGrade,jdbcType=VARCHAR},
      </if>
      <if test="record.differentiation != null">
        "differentiation" = #{record.differentiation,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.examinationMethod != null">
        "examination_method" = #{record.examinationMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.qualitativeConclusion != null">
        "qualitative_conclusion" = #{record.qualitativeConclusion,jdbcType=VARCHAR},
      </if>
      <if test="record.approach != null">
        "approach" = #{record.approach,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        "dosage" = #{record.dosage,jdbcType=VARCHAR},
      </if>
      <if test="record.frequency != null">
        "frequency" = #{record.frequency,jdbcType=VARCHAR},
      </if>
      <if test="record.period != null">
        "period" = #{record.period,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        "category" = #{record.category,jdbcType=VARCHAR},
      </if>
      <if test="record.operationMethod != null">
        "operation_method" = #{record.operationMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.instrument != null">
        "instrument" = #{record.instrument,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaMethod != null">
        "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.hasMarryHistory != null">
        "has_marry_history" = #{record.hasMarryHistory,jdbcType=VARCHAR},
      </if>
      <if test="record.marryTime != null">
        "marry_time" = #{record.marryTime,jdbcType=VARCHAR},
      </if>
      <if test="record.marryAge != null">
        "marry_age" = #{record.marryAge,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerHealthStatus != null">
        "partner_health_status" = #{record.partnerHealthStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerIllnessDeathCause != null">
        "partner_illness_death_cause" = #{record.partnerIllnessDeathCause,jdbcType=VARCHAR},
      </if>
      <if test="record.childbearingHistory != null">
        "childbearing_history" = #{record.childbearingHistory,jdbcType=VARCHAR},
      </if>
      <if test="record.childrenStatus != null">
        "children_status" = #{record.childrenStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.pregnancyTime != null">
        "pregnancy_time" = #{record.pregnancyTime,jdbcType=VARCHAR},
      </if>
      <if test="record.giveBirthTime != null">
        "give_birth_time" = #{record.giveBirthTime,jdbcType=VARCHAR},
      </if>
      <if test="record.misbirthTime != null">
        "misbirth_time" = #{record.misbirthTime,jdbcType=VARCHAR},
      </if>
      <if test="record.hasDrinkHistory != null">
        "has_drink_history" = #{record.hasDrinkHistory,jdbcType=VARCHAR},
      </if>
      <if test="record.drinkType != null">
        "drink_type" = #{record.drinkType,jdbcType=VARCHAR},
      </if>
      <if test="record.drinkTime != null">
        "drink_time" = #{record.drinkTime,jdbcType=VARCHAR},
      </if>
      <if test="record.drinkYear != null">
        "drink_year" = #{record.drinkYear,jdbcType=VARCHAR},
      </if>
      <if test="record.drinkFreq != null">
        "drink_freq" = #{record.drinkFreq,jdbcType=VARCHAR},
      </if>
      <if test="record.drinkAmount != null">
        "drink_amount" = #{record.drinkAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.drinkUnit != null">
        "drink_unit" = #{record.drinkUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.hasAlcohol != null">
        "has_alcohol" = #{record.hasAlcohol,jdbcType=VARCHAR},
      </if>
      <if test="record.alcoholTime != null">
        "alcohol_time" = #{record.alcoholTime,jdbcType=VARCHAR},
      </if>
      <if test="record.alcoholYear != null">
        "alcohol_year" = #{record.alcoholYear,jdbcType=VARCHAR},
      </if>
      <if test="record.hasSmoke != null">
        "has_smoke" = #{record.hasSmoke,jdbcType=VARCHAR},
      </if>
      <if test="record.smokeTime != null">
        "smoke_time" = #{record.smokeTime,jdbcType=VARCHAR},
      </if>
      <if test="record.smokeYear != null">
        "smoke_year" = #{record.smokeYear,jdbcType=VARCHAR},
      </if>
      <if test="record.smokeFreq != null">
        "smoke_freq" = #{record.smokeFreq,jdbcType=VARCHAR},
      </if>
      <if test="record.smokeAmount != null">
        "smoke_amount" = #{record.smokeAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.smokeUnit != null">
        "smoke_unit" = #{record.smokeUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.hasQuitSmoke != null">
        "has_quit_smoke" = #{record.hasQuitSmoke,jdbcType=VARCHAR},
      </if>
      <if test="record.hasQuitSmokeTime != null">
        "has_quit_smoke_time" = #{record.hasQuitSmokeTime,jdbcType=VARCHAR},
      </if>
      <if test="record.hasQuitSmokeYear != null">
        "has_quit_smoke_year" = #{record.hasQuitSmokeYear,jdbcType=VARCHAR},
      </if>
      <if test="record.hasMenstruation != null">
        "has_menstruation" = #{record.hasMenstruation,jdbcType=VARCHAR},
      </if>
      <if test="record.firstMenstruationTime != null">
        "first_menstruation_time" = #{record.firstMenstruationTime,jdbcType=VARCHAR},
      </if>
      <if test="record.firstMenstruationAge != null">
        "first_menstruation_age" = #{record.firstMenstruationAge,jdbcType=VARCHAR},
      </if>
      <if test="record.menstruationDuration != null">
        "menstruation_duration" = #{record.menstruationDuration,jdbcType=VARCHAR},
      </if>
      <if test="record.menstruationPeriod != null">
        "menstruation_period" = #{record.menstruationPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.menstruationAmount != null">
        "menstruation_amount" = #{record.menstruationAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.menstruationColic != null">
        "menstruation_colic" = #{record.menstruationColic,jdbcType=VARCHAR},
      </if>
      <if test="record.lastMenstruation != null">
        "last_menstruation" = #{record.lastMenstruation,jdbcType=VARCHAR},
      </if>
      <if test="record.overTime != null">
        "over_time" = #{record.overTime,jdbcType=VARCHAR},
      </if>
      <if test="record.overAge != null">
        "over_age" = #{record.overAge,jdbcType=VARCHAR},
      </if>
      <if test="record.isMenopause != null">
        "is_menopause" = #{record.isMenopause,jdbcType=VARCHAR},
      </if>
      <if test="record.allergen != null">
        "allergen" = #{record.allergen,jdbcType=VARCHAR},
      </if>
      <if test="record.hasAllergy != null">
        "has_allergy" = #{record.hasAllergy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."event_record"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "table_name" = #{record.tableName,jdbcType=VARCHAR},
      "column_name" = #{record.columnName,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR},
      "event_type" = #{record.eventType,jdbcType=VARCHAR},
      "event_start_index" = #{record.eventStartIndex,jdbcType=INTEGER},
      "event_end_index" = #{record.eventEndIndex,jdbcType=INTEGER},
      "event_name" = #{record.eventName,jdbcType=VARCHAR},
      "event_entity_type" = #{record.eventEntityType,jdbcType=VARCHAR},
      "standard_name" = #{record.standardName,jdbcType=VARCHAR},
      "is_sub_entity" = #{record.isSubEntity,jdbcType=VARCHAR},
      "negative_word" = #{record.negativeWord,jdbcType=VARCHAR},
      "probability" = #{record.probability,jdbcType=VARCHAR},
      "duration" = #{record.duration,jdbcType=VARCHAR},
      "start_date" = #{record.startDate,jdbcType=VARCHAR},
      "end_date" = #{record.endDate,jdbcType=VARCHAR},
      "standard_start_date" = #{record.standardStartDate,jdbcType=VARCHAR},
      "standard_end_date" = #{record.standardEndDate,jdbcType=VARCHAR},
      "event_body" = #{record.eventBody,jdbcType=VARCHAR},
      "body_direction" = #{record.bodyDirection,jdbcType=VARCHAR},
      "medicinal_problem_type" = #{record.medicinalProblemType,jdbcType=VARCHAR},
      "medical_condition" = #{record.medicalCondition,jdbcType=VARCHAR},
      "event_qualifier" = #{record.eventQualifier,jdbcType=VARCHAR},
      "grade" = #{record.grade,jdbcType=VARCHAR},
      "measurements" = #{record.measurements,jdbcType=VARCHAR},
      "efficacy_evaluation" = #{record.efficacyEvaluation,jdbcType=VARCHAR},
      "blood_signal" = #{record.bloodSignal,jdbcType=VARCHAR},
      "blood_signal_level" = #{record.bloodSignalLevel,jdbcType=VARCHAR},
      "texture" = #{record.texture,jdbcType=VARCHAR},
      "size" = #{record.size,jdbcType=VARCHAR},
      "boundary" = #{record.boundary,jdbcType=VARCHAR},
      "morphology" = #{record.morphology,jdbcType=VARCHAR},
      "metabolism" = #{record.metabolism,jdbcType=VARCHAR},
      "max_value" = #{record.maxValue,jdbcType=VARCHAR},
      "echo" = #{record.echo,jdbcType=VARCHAR},
      "necrosis" = #{record.necrosis,jdbcType=VARCHAR},
      "calcification" = #{record.calcification,jdbcType=VARCHAR},
      "patho_pattern" = #{record.pathoPattern,jdbcType=VARCHAR},
      "patho_grade" = #{record.pathoGrade,jdbcType=VARCHAR},
      "differentiation" = #{record.differentiation,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "examination_method" = #{record.examinationMethod,jdbcType=VARCHAR},
      "qualitative_conclusion" = #{record.qualitativeConclusion,jdbcType=VARCHAR},
      "approach" = #{record.approach,jdbcType=VARCHAR},
      "dosage" = #{record.dosage,jdbcType=VARCHAR},
      "frequency" = #{record.frequency,jdbcType=VARCHAR},
      "period" = #{record.period,jdbcType=VARCHAR},
      "category" = #{record.category,jdbcType=VARCHAR},
      "operation_method" = #{record.operationMethod,jdbcType=VARCHAR},
      "instrument" = #{record.instrument,jdbcType=VARCHAR},
      "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      "has_marry_history" = #{record.hasMarryHistory,jdbcType=VARCHAR},
      "marry_time" = #{record.marryTime,jdbcType=VARCHAR},
      "marry_age" = #{record.marryAge,jdbcType=VARCHAR},
      "partner_health_status" = #{record.partnerHealthStatus,jdbcType=VARCHAR},
      "partner_illness_death_cause" = #{record.partnerIllnessDeathCause,jdbcType=VARCHAR},
      "childbearing_history" = #{record.childbearingHistory,jdbcType=VARCHAR},
      "children_status" = #{record.childrenStatus,jdbcType=VARCHAR},
      "pregnancy_time" = #{record.pregnancyTime,jdbcType=VARCHAR},
      "give_birth_time" = #{record.giveBirthTime,jdbcType=VARCHAR},
      "misbirth_time" = #{record.misbirthTime,jdbcType=VARCHAR},
      "has_drink_history" = #{record.hasDrinkHistory,jdbcType=VARCHAR},
      "drink_type" = #{record.drinkType,jdbcType=VARCHAR},
      "drink_time" = #{record.drinkTime,jdbcType=VARCHAR},
      "drink_year" = #{record.drinkYear,jdbcType=VARCHAR},
      "drink_freq" = #{record.drinkFreq,jdbcType=VARCHAR},
      "drink_amount" = #{record.drinkAmount,jdbcType=VARCHAR},
      "drink_unit" = #{record.drinkUnit,jdbcType=VARCHAR},
      "has_alcohol" = #{record.hasAlcohol,jdbcType=VARCHAR},
      "alcohol_time" = #{record.alcoholTime,jdbcType=VARCHAR},
      "alcohol_year" = #{record.alcoholYear,jdbcType=VARCHAR},
      "has_smoke" = #{record.hasSmoke,jdbcType=VARCHAR},
      "smoke_time" = #{record.smokeTime,jdbcType=VARCHAR},
      "smoke_year" = #{record.smokeYear,jdbcType=VARCHAR},
      "smoke_freq" = #{record.smokeFreq,jdbcType=VARCHAR},
      "smoke_amount" = #{record.smokeAmount,jdbcType=VARCHAR},
      "smoke_unit" = #{record.smokeUnit,jdbcType=VARCHAR},
      "has_quit_smoke" = #{record.hasQuitSmoke,jdbcType=VARCHAR},
      "has_quit_smoke_time" = #{record.hasQuitSmokeTime,jdbcType=VARCHAR},
      "has_quit_smoke_year" = #{record.hasQuitSmokeYear,jdbcType=VARCHAR},
      "has_menstruation" = #{record.hasMenstruation,jdbcType=VARCHAR},
      "first_menstruation_time" = #{record.firstMenstruationTime,jdbcType=VARCHAR},
      "first_menstruation_age" = #{record.firstMenstruationAge,jdbcType=VARCHAR},
      "menstruation_duration" = #{record.menstruationDuration,jdbcType=VARCHAR},
      "menstruation_period" = #{record.menstruationPeriod,jdbcType=VARCHAR},
      "menstruation_amount" = #{record.menstruationAmount,jdbcType=VARCHAR},
      "menstruation_colic" = #{record.menstruationColic,jdbcType=VARCHAR},
      "last_menstruation" = #{record.lastMenstruation,jdbcType=VARCHAR},
      "over_time" = #{record.overTime,jdbcType=VARCHAR},
      "over_age" = #{record.overAge,jdbcType=VARCHAR},
      "is_menopause" = #{record.isMenopause,jdbcType=VARCHAR},
      "allergen" = #{record.allergen,jdbcType=VARCHAR},
      "has_allergy" = #{record.hasAllergy,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.EventRecord">
    update "public"."event_record"
    <set>
      <if test="tableName != null">
        "table_name" = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        "column_name" = #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        "event_type" = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="eventStartIndex != null">
        "event_start_index" = #{eventStartIndex,jdbcType=INTEGER},
      </if>
      <if test="eventEndIndex != null">
        "event_end_index" = #{eventEndIndex,jdbcType=INTEGER},
      </if>
      <if test="eventName != null">
        "event_name" = #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="eventEntityType != null">
        "event_entity_type" = #{eventEntityType,jdbcType=VARCHAR},
      </if>
      <if test="standardName != null">
        "standard_name" = #{standardName,jdbcType=VARCHAR},
      </if>
      <if test="isSubEntity != null">
        "is_sub_entity" = #{isSubEntity,jdbcType=VARCHAR},
      </if>
      <if test="negativeWord != null">
        "negative_word" = #{negativeWord,jdbcType=VARCHAR},
      </if>
      <if test="probability != null">
        "probability" = #{probability,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        "duration" = #{duration,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        "start_date" = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null">
        "end_date" = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="standardStartDate != null">
        "standard_start_date" = #{standardStartDate,jdbcType=VARCHAR},
      </if>
      <if test="standardEndDate != null">
        "standard_end_date" = #{standardEndDate,jdbcType=VARCHAR},
      </if>
      <if test="eventBody != null">
        "event_body" = #{eventBody,jdbcType=VARCHAR},
      </if>
      <if test="bodyDirection != null">
        "body_direction" = #{bodyDirection,jdbcType=VARCHAR},
      </if>
      <if test="medicinalProblemType != null">
        "medicinal_problem_type" = #{medicinalProblemType,jdbcType=VARCHAR},
      </if>
      <if test="medicalCondition != null">
        "medical_condition" = #{medicalCondition,jdbcType=VARCHAR},
      </if>
      <if test="eventQualifier != null">
        "event_qualifier" = #{eventQualifier,jdbcType=VARCHAR},
      </if>
      <if test="grade != null">
        "grade" = #{grade,jdbcType=VARCHAR},
      </if>
      <if test="measurements != null">
        "measurements" = #{measurements,jdbcType=VARCHAR},
      </if>
      <if test="efficacyEvaluation != null">
        "efficacy_evaluation" = #{efficacyEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="bloodSignal != null">
        "blood_signal" = #{bloodSignal,jdbcType=VARCHAR},
      </if>
      <if test="bloodSignalLevel != null">
        "blood_signal_level" = #{bloodSignalLevel,jdbcType=VARCHAR},
      </if>
      <if test="texture != null">
        "texture" = #{texture,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        "size" = #{size,jdbcType=VARCHAR},
      </if>
      <if test="boundary != null">
        "boundary" = #{boundary,jdbcType=VARCHAR},
      </if>
      <if test="morphology != null">
        "morphology" = #{morphology,jdbcType=VARCHAR},
      </if>
      <if test="metabolism != null">
        "metabolism" = #{metabolism,jdbcType=VARCHAR},
      </if>
      <if test="maxValue != null">
        "max_value" = #{maxValue,jdbcType=VARCHAR},
      </if>
      <if test="echo != null">
        "echo" = #{echo,jdbcType=VARCHAR},
      </if>
      <if test="necrosis != null">
        "necrosis" = #{necrosis,jdbcType=VARCHAR},
      </if>
      <if test="calcification != null">
        "calcification" = #{calcification,jdbcType=VARCHAR},
      </if>
      <if test="pathoPattern != null">
        "patho_pattern" = #{pathoPattern,jdbcType=VARCHAR},
      </if>
      <if test="pathoGrade != null">
        "patho_grade" = #{pathoGrade,jdbcType=VARCHAR},
      </if>
      <if test="differentiation != null">
        "differentiation" = #{differentiation,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="examinationMethod != null">
        "examination_method" = #{examinationMethod,jdbcType=VARCHAR},
      </if>
      <if test="qualitativeConclusion != null">
        "qualitative_conclusion" = #{qualitativeConclusion,jdbcType=VARCHAR},
      </if>
      <if test="approach != null">
        "approach" = #{approach,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        "dosage" = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="frequency != null">
        "frequency" = #{frequency,jdbcType=VARCHAR},
      </if>
      <if test="period != null">
        "period" = #{period,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        "category" = #{category,jdbcType=VARCHAR},
      </if>
      <if test="operationMethod != null">
        "operation_method" = #{operationMethod,jdbcType=VARCHAR},
      </if>
      <if test="instrument != null">
        "instrument" = #{instrument,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="hasMarryHistory != null">
        "has_marry_history" = #{hasMarryHistory,jdbcType=VARCHAR},
      </if>
      <if test="marryTime != null">
        "marry_time" = #{marryTime,jdbcType=VARCHAR},
      </if>
      <if test="marryAge != null">
        "marry_age" = #{marryAge,jdbcType=VARCHAR},
      </if>
      <if test="partnerHealthStatus != null">
        "partner_health_status" = #{partnerHealthStatus,jdbcType=VARCHAR},
      </if>
      <if test="partnerIllnessDeathCause != null">
        "partner_illness_death_cause" = #{partnerIllnessDeathCause,jdbcType=VARCHAR},
      </if>
      <if test="childbearingHistory != null">
        "childbearing_history" = #{childbearingHistory,jdbcType=VARCHAR},
      </if>
      <if test="childrenStatus != null">
        "children_status" = #{childrenStatus,jdbcType=VARCHAR},
      </if>
      <if test="pregnancyTime != null">
        "pregnancy_time" = #{pregnancyTime,jdbcType=VARCHAR},
      </if>
      <if test="giveBirthTime != null">
        "give_birth_time" = #{giveBirthTime,jdbcType=VARCHAR},
      </if>
      <if test="misbirthTime != null">
        "misbirth_time" = #{misbirthTime,jdbcType=VARCHAR},
      </if>
      <if test="hasDrinkHistory != null">
        "has_drink_history" = #{hasDrinkHistory,jdbcType=VARCHAR},
      </if>
      <if test="drinkType != null">
        "drink_type" = #{drinkType,jdbcType=VARCHAR},
      </if>
      <if test="drinkTime != null">
        "drink_time" = #{drinkTime,jdbcType=VARCHAR},
      </if>
      <if test="drinkYear != null">
        "drink_year" = #{drinkYear,jdbcType=VARCHAR},
      </if>
      <if test="drinkFreq != null">
        "drink_freq" = #{drinkFreq,jdbcType=VARCHAR},
      </if>
      <if test="drinkAmount != null">
        "drink_amount" = #{drinkAmount,jdbcType=VARCHAR},
      </if>
      <if test="drinkUnit != null">
        "drink_unit" = #{drinkUnit,jdbcType=VARCHAR},
      </if>
      <if test="hasAlcohol != null">
        "has_alcohol" = #{hasAlcohol,jdbcType=VARCHAR},
      </if>
      <if test="alcoholTime != null">
        "alcohol_time" = #{alcoholTime,jdbcType=VARCHAR},
      </if>
      <if test="alcoholYear != null">
        "alcohol_year" = #{alcoholYear,jdbcType=VARCHAR},
      </if>
      <if test="hasSmoke != null">
        "has_smoke" = #{hasSmoke,jdbcType=VARCHAR},
      </if>
      <if test="smokeTime != null">
        "smoke_time" = #{smokeTime,jdbcType=VARCHAR},
      </if>
      <if test="smokeYear != null">
        "smoke_year" = #{smokeYear,jdbcType=VARCHAR},
      </if>
      <if test="smokeFreq != null">
        "smoke_freq" = #{smokeFreq,jdbcType=VARCHAR},
      </if>
      <if test="smokeAmount != null">
        "smoke_amount" = #{smokeAmount,jdbcType=VARCHAR},
      </if>
      <if test="smokeUnit != null">
        "smoke_unit" = #{smokeUnit,jdbcType=VARCHAR},
      </if>
      <if test="hasQuitSmoke != null">
        "has_quit_smoke" = #{hasQuitSmoke,jdbcType=VARCHAR},
      </if>
      <if test="hasQuitSmokeTime != null">
        "has_quit_smoke_time" = #{hasQuitSmokeTime,jdbcType=VARCHAR},
      </if>
      <if test="hasQuitSmokeYear != null">
        "has_quit_smoke_year" = #{hasQuitSmokeYear,jdbcType=VARCHAR},
      </if>
      <if test="hasMenstruation != null">
        "has_menstruation" = #{hasMenstruation,jdbcType=VARCHAR},
      </if>
      <if test="firstMenstruationTime != null">
        "first_menstruation_time" = #{firstMenstruationTime,jdbcType=VARCHAR},
      </if>
      <if test="firstMenstruationAge != null">
        "first_menstruation_age" = #{firstMenstruationAge,jdbcType=VARCHAR},
      </if>
      <if test="menstruationDuration != null">
        "menstruation_duration" = #{menstruationDuration,jdbcType=VARCHAR},
      </if>
      <if test="menstruationPeriod != null">
        "menstruation_period" = #{menstruationPeriod,jdbcType=VARCHAR},
      </if>
      <if test="menstruationAmount != null">
        "menstruation_amount" = #{menstruationAmount,jdbcType=VARCHAR},
      </if>
      <if test="menstruationColic != null">
        "menstruation_colic" = #{menstruationColic,jdbcType=VARCHAR},
      </if>
      <if test="lastMenstruation != null">
        "last_menstruation" = #{lastMenstruation,jdbcType=VARCHAR},
      </if>
      <if test="overTime != null">
        "over_time" = #{overTime,jdbcType=VARCHAR},
      </if>
      <if test="overAge != null">
        "over_age" = #{overAge,jdbcType=VARCHAR},
      </if>
      <if test="isMenopause != null">
        "is_menopause" = #{isMenopause,jdbcType=VARCHAR},
      </if>
      <if test="allergen != null">
        "allergen" = #{allergen,jdbcType=VARCHAR},
      </if>
      <if test="hasAllergy != null">
        "has_allergy" = #{hasAllergy,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.EventRecord">
    update "public"."event_record"
    set "table_name" = #{tableName,jdbcType=VARCHAR},
      "column_name" = #{columnName,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR},
      "event_type" = #{eventType,jdbcType=VARCHAR},
      "event_start_index" = #{eventStartIndex,jdbcType=INTEGER},
      "event_end_index" = #{eventEndIndex,jdbcType=INTEGER},
      "event_name" = #{eventName,jdbcType=VARCHAR},
      "event_entity_type" = #{eventEntityType,jdbcType=VARCHAR},
      "standard_name" = #{standardName,jdbcType=VARCHAR},
      "is_sub_entity" = #{isSubEntity,jdbcType=VARCHAR},
      "negative_word" = #{negativeWord,jdbcType=VARCHAR},
      "probability" = #{probability,jdbcType=VARCHAR},
      "duration" = #{duration,jdbcType=VARCHAR},
      "start_date" = #{startDate,jdbcType=VARCHAR},
      "end_date" = #{endDate,jdbcType=VARCHAR},
      "standard_start_date" = #{standardStartDate,jdbcType=VARCHAR},
      "standard_end_date" = #{standardEndDate,jdbcType=VARCHAR},
      "event_body" = #{eventBody,jdbcType=VARCHAR},
      "body_direction" = #{bodyDirection,jdbcType=VARCHAR},
      "medicinal_problem_type" = #{medicinalProblemType,jdbcType=VARCHAR},
      "medical_condition" = #{medicalCondition,jdbcType=VARCHAR},
      "event_qualifier" = #{eventQualifier,jdbcType=VARCHAR},
      "grade" = #{grade,jdbcType=VARCHAR},
      "measurements" = #{measurements,jdbcType=VARCHAR},
      "efficacy_evaluation" = #{efficacyEvaluation,jdbcType=VARCHAR},
      "blood_signal" = #{bloodSignal,jdbcType=VARCHAR},
      "blood_signal_level" = #{bloodSignalLevel,jdbcType=VARCHAR},
      "texture" = #{texture,jdbcType=VARCHAR},
      "size" = #{size,jdbcType=VARCHAR},
      "boundary" = #{boundary,jdbcType=VARCHAR},
      "morphology" = #{morphology,jdbcType=VARCHAR},
      "metabolism" = #{metabolism,jdbcType=VARCHAR},
      "max_value" = #{maxValue,jdbcType=VARCHAR},
      "echo" = #{echo,jdbcType=VARCHAR},
      "necrosis" = #{necrosis,jdbcType=VARCHAR},
      "calcification" = #{calcification,jdbcType=VARCHAR},
      "patho_pattern" = #{pathoPattern,jdbcType=VARCHAR},
      "patho_grade" = #{pathoGrade,jdbcType=VARCHAR},
      "differentiation" = #{differentiation,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      "examination_method" = #{examinationMethod,jdbcType=VARCHAR},
      "qualitative_conclusion" = #{qualitativeConclusion,jdbcType=VARCHAR},
      "approach" = #{approach,jdbcType=VARCHAR},
      "dosage" = #{dosage,jdbcType=VARCHAR},
      "frequency" = #{frequency,jdbcType=VARCHAR},
      "period" = #{period,jdbcType=VARCHAR},
      "category" = #{category,jdbcType=VARCHAR},
      "operation_method" = #{operationMethod,jdbcType=VARCHAR},
      "instrument" = #{instrument,jdbcType=VARCHAR},
      "anesthesia_method" = #{anesthesiaMethod,jdbcType=VARCHAR},
      "has_marry_history" = #{hasMarryHistory,jdbcType=VARCHAR},
      "marry_time" = #{marryTime,jdbcType=VARCHAR},
      "marry_age" = #{marryAge,jdbcType=VARCHAR},
      "partner_health_status" = #{partnerHealthStatus,jdbcType=VARCHAR},
      "partner_illness_death_cause" = #{partnerIllnessDeathCause,jdbcType=VARCHAR},
      "childbearing_history" = #{childbearingHistory,jdbcType=VARCHAR},
      "children_status" = #{childrenStatus,jdbcType=VARCHAR},
      "pregnancy_time" = #{pregnancyTime,jdbcType=VARCHAR},
      "give_birth_time" = #{giveBirthTime,jdbcType=VARCHAR},
      "misbirth_time" = #{misbirthTime,jdbcType=VARCHAR},
      "has_drink_history" = #{hasDrinkHistory,jdbcType=VARCHAR},
      "drink_type" = #{drinkType,jdbcType=VARCHAR},
      "drink_time" = #{drinkTime,jdbcType=VARCHAR},
      "drink_year" = #{drinkYear,jdbcType=VARCHAR},
      "drink_freq" = #{drinkFreq,jdbcType=VARCHAR},
      "drink_amount" = #{drinkAmount,jdbcType=VARCHAR},
      "drink_unit" = #{drinkUnit,jdbcType=VARCHAR},
      "has_alcohol" = #{hasAlcohol,jdbcType=VARCHAR},
      "alcohol_time" = #{alcoholTime,jdbcType=VARCHAR},
      "alcohol_year" = #{alcoholYear,jdbcType=VARCHAR},
      "has_smoke" = #{hasSmoke,jdbcType=VARCHAR},
      "smoke_time" = #{smokeTime,jdbcType=VARCHAR},
      "smoke_year" = #{smokeYear,jdbcType=VARCHAR},
      "smoke_freq" = #{smokeFreq,jdbcType=VARCHAR},
      "smoke_amount" = #{smokeAmount,jdbcType=VARCHAR},
      "smoke_unit" = #{smokeUnit,jdbcType=VARCHAR},
      "has_quit_smoke" = #{hasQuitSmoke,jdbcType=VARCHAR},
      "has_quit_smoke_time" = #{hasQuitSmokeTime,jdbcType=VARCHAR},
      "has_quit_smoke_year" = #{hasQuitSmokeYear,jdbcType=VARCHAR},
      "has_menstruation" = #{hasMenstruation,jdbcType=VARCHAR},
      "first_menstruation_time" = #{firstMenstruationTime,jdbcType=VARCHAR},
      "first_menstruation_age" = #{firstMenstruationAge,jdbcType=VARCHAR},
      "menstruation_duration" = #{menstruationDuration,jdbcType=VARCHAR},
      "menstruation_period" = #{menstruationPeriod,jdbcType=VARCHAR},
      "menstruation_amount" = #{menstruationAmount,jdbcType=VARCHAR},
      "menstruation_colic" = #{menstruationColic,jdbcType=VARCHAR},
      "last_menstruation" = #{lastMenstruation,jdbcType=VARCHAR},
      "over_time" = #{overTime,jdbcType=VARCHAR},
      "over_age" = #{overAge,jdbcType=VARCHAR},
      "is_menopause" = #{isMenopause,jdbcType=VARCHAR},
      "allergen" = #{allergen,jdbcType=VARCHAR},
      "has_allergy" = #{hasAllergy,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
</mapper>