package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.List;

public class EventRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EventRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("\"id\" like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("\"id\" not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTableNameIsNull() {
            addCriterion("\"table_name\" is null");
            return (Criteria) this;
        }

        public Criteria andTableNameIsNotNull() {
            addCriterion("\"table_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andTableNameEqualTo(String value) {
            addCriterion("\"table_name\" =", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameNotEqualTo(String value) {
            addCriterion("\"table_name\" <>", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameGreaterThan(String value) {
            addCriterion("\"table_name\" >", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"table_name\" >=", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameLessThan(String value) {
            addCriterion("\"table_name\" <", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameLessThanOrEqualTo(String value) {
            addCriterion("\"table_name\" <=", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameLike(String value) {
            addCriterion("\"table_name\" like", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameNotLike(String value) {
            addCriterion("\"table_name\" not like", value, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameIn(List<String> values) {
            addCriterion("\"table_name\" in", values, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameNotIn(List<String> values) {
            addCriterion("\"table_name\" not in", values, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameBetween(String value1, String value2) {
            addCriterion("\"table_name\" between", value1, value2, "tableName");
            return (Criteria) this;
        }

        public Criteria andTableNameNotBetween(String value1, String value2) {
            addCriterion("\"table_name\" not between", value1, value2, "tableName");
            return (Criteria) this;
        }

        public Criteria andColumnNameIsNull() {
            addCriterion("\"column_name\" is null");
            return (Criteria) this;
        }

        public Criteria andColumnNameIsNotNull() {
            addCriterion("\"column_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andColumnNameEqualTo(String value) {
            addCriterion("\"column_name\" =", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotEqualTo(String value) {
            addCriterion("\"column_name\" <>", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameGreaterThan(String value) {
            addCriterion("\"column_name\" >", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"column_name\" >=", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameLessThan(String value) {
            addCriterion("\"column_name\" <", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameLessThanOrEqualTo(String value) {
            addCriterion("\"column_name\" <=", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameLike(String value) {
            addCriterion("\"column_name\" like", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotLike(String value) {
            addCriterion("\"column_name\" not like", value, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameIn(List<String> values) {
            addCriterion("\"column_name\" in", values, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotIn(List<String> values) {
            addCriterion("\"column_name\" not in", values, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameBetween(String value1, String value2) {
            addCriterion("\"column_name\" between", value1, value2, "columnName");
            return (Criteria) this;
        }

        public Criteria andColumnNameNotBetween(String value1, String value2) {
            addCriterion("\"column_name\" not between", value1, value2, "columnName");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andEventTypeIsNull() {
            addCriterion("\"event_type\" is null");
            return (Criteria) this;
        }

        public Criteria andEventTypeIsNotNull() {
            addCriterion("\"event_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventTypeEqualTo(String value) {
            addCriterion("\"event_type\" =", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeNotEqualTo(String value) {
            addCriterion("\"event_type\" <>", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeGreaterThan(String value) {
            addCriterion("\"event_type\" >", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"event_type\" >=", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeLessThan(String value) {
            addCriterion("\"event_type\" <", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeLessThanOrEqualTo(String value) {
            addCriterion("\"event_type\" <=", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeLike(String value) {
            addCriterion("\"event_type\" like", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeNotLike(String value) {
            addCriterion("\"event_type\" not like", value, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeIn(List<String> values) {
            addCriterion("\"event_type\" in", values, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeNotIn(List<String> values) {
            addCriterion("\"event_type\" not in", values, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeBetween(String value1, String value2) {
            addCriterion("\"event_type\" between", value1, value2, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventTypeNotBetween(String value1, String value2) {
            addCriterion("\"event_type\" not between", value1, value2, "eventType");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexIsNull() {
            addCriterion("\"event_start_index\" is null");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexIsNotNull() {
            addCriterion("\"event_start_index\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexEqualTo(Integer value) {
            addCriterion("\"event_start_index\" =", value, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexNotEqualTo(Integer value) {
            addCriterion("\"event_start_index\" <>", value, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexGreaterThan(Integer value) {
            addCriterion("\"event_start_index\" >", value, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"event_start_index\" >=", value, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexLessThan(Integer value) {
            addCriterion("\"event_start_index\" <", value, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexLessThanOrEqualTo(Integer value) {
            addCriterion("\"event_start_index\" <=", value, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexIn(List<Integer> values) {
            addCriterion("\"event_start_index\" in", values, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexNotIn(List<Integer> values) {
            addCriterion("\"event_start_index\" not in", values, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexBetween(Integer value1, Integer value2) {
            addCriterion("\"event_start_index\" between", value1, value2, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventStartIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("\"event_start_index\" not between", value1, value2, "eventStartIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexIsNull() {
            addCriterion("\"event_end_index\" is null");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexIsNotNull() {
            addCriterion("\"event_end_index\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexEqualTo(Integer value) {
            addCriterion("\"event_end_index\" =", value, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexNotEqualTo(Integer value) {
            addCriterion("\"event_end_index\" <>", value, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexGreaterThan(Integer value) {
            addCriterion("\"event_end_index\" >", value, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"event_end_index\" >=", value, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexLessThan(Integer value) {
            addCriterion("\"event_end_index\" <", value, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexLessThanOrEqualTo(Integer value) {
            addCriterion("\"event_end_index\" <=", value, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexIn(List<Integer> values) {
            addCriterion("\"event_end_index\" in", values, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexNotIn(List<Integer> values) {
            addCriterion("\"event_end_index\" not in", values, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexBetween(Integer value1, Integer value2) {
            addCriterion("\"event_end_index\" between", value1, value2, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventEndIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("\"event_end_index\" not between", value1, value2, "eventEndIndex");
            return (Criteria) this;
        }

        public Criteria andEventNameIsNull() {
            addCriterion("\"event_name\" is null");
            return (Criteria) this;
        }

        public Criteria andEventNameIsNotNull() {
            addCriterion("\"event_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventNameEqualTo(String value) {
            addCriterion("\"event_name\" =", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameNotEqualTo(String value) {
            addCriterion("\"event_name\" <>", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameGreaterThan(String value) {
            addCriterion("\"event_name\" >", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"event_name\" >=", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameLessThan(String value) {
            addCriterion("\"event_name\" <", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameLessThanOrEqualTo(String value) {
            addCriterion("\"event_name\" <=", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameLike(String value) {
            addCriterion("\"event_name\" like", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameNotLike(String value) {
            addCriterion("\"event_name\" not like", value, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameIn(List<String> values) {
            addCriterion("\"event_name\" in", values, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameNotIn(List<String> values) {
            addCriterion("\"event_name\" not in", values, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameBetween(String value1, String value2) {
            addCriterion("\"event_name\" between", value1, value2, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventNameNotBetween(String value1, String value2) {
            addCriterion("\"event_name\" not between", value1, value2, "eventName");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeIsNull() {
            addCriterion("\"event_entity_type\" is null");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeIsNotNull() {
            addCriterion("\"event_entity_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeEqualTo(String value) {
            addCriterion("\"event_entity_type\" =", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeNotEqualTo(String value) {
            addCriterion("\"event_entity_type\" <>", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeGreaterThan(String value) {
            addCriterion("\"event_entity_type\" >", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"event_entity_type\" >=", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeLessThan(String value) {
            addCriterion("\"event_entity_type\" <", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeLessThanOrEqualTo(String value) {
            addCriterion("\"event_entity_type\" <=", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeLike(String value) {
            addCriterion("\"event_entity_type\" like", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeNotLike(String value) {
            addCriterion("\"event_entity_type\" not like", value, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeIn(List<String> values) {
            addCriterion("\"event_entity_type\" in", values, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeNotIn(List<String> values) {
            addCriterion("\"event_entity_type\" not in", values, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeBetween(String value1, String value2) {
            addCriterion("\"event_entity_type\" between", value1, value2, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andEventEntityTypeNotBetween(String value1, String value2) {
            addCriterion("\"event_entity_type\" not between", value1, value2, "eventEntityType");
            return (Criteria) this;
        }

        public Criteria andStandardNameIsNull() {
            addCriterion("\"standard_name\" is null");
            return (Criteria) this;
        }

        public Criteria andStandardNameIsNotNull() {
            addCriterion("\"standard_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andStandardNameEqualTo(String value) {
            addCriterion("\"standard_name\" =", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotEqualTo(String value) {
            addCriterion("\"standard_name\" <>", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameGreaterThan(String value) {
            addCriterion("\"standard_name\" >", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"standard_name\" >=", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameLessThan(String value) {
            addCriterion("\"standard_name\" <", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameLessThanOrEqualTo(String value) {
            addCriterion("\"standard_name\" <=", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameLike(String value) {
            addCriterion("\"standard_name\" like", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotLike(String value) {
            addCriterion("\"standard_name\" not like", value, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameIn(List<String> values) {
            addCriterion("\"standard_name\" in", values, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotIn(List<String> values) {
            addCriterion("\"standard_name\" not in", values, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameBetween(String value1, String value2) {
            addCriterion("\"standard_name\" between", value1, value2, "standardName");
            return (Criteria) this;
        }

        public Criteria andStandardNameNotBetween(String value1, String value2) {
            addCriterion("\"standard_name\" not between", value1, value2, "standardName");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityIsNull() {
            addCriterion("\"is_sub_entity\" is null");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityIsNotNull() {
            addCriterion("\"is_sub_entity\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityEqualTo(String value) {
            addCriterion("\"is_sub_entity\" =", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityNotEqualTo(String value) {
            addCriterion("\"is_sub_entity\" <>", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityGreaterThan(String value) {
            addCriterion("\"is_sub_entity\" >", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_sub_entity\" >=", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityLessThan(String value) {
            addCriterion("\"is_sub_entity\" <", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityLessThanOrEqualTo(String value) {
            addCriterion("\"is_sub_entity\" <=", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityLike(String value) {
            addCriterion("\"is_sub_entity\" like", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityNotLike(String value) {
            addCriterion("\"is_sub_entity\" not like", value, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityIn(List<String> values) {
            addCriterion("\"is_sub_entity\" in", values, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityNotIn(List<String> values) {
            addCriterion("\"is_sub_entity\" not in", values, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityBetween(String value1, String value2) {
            addCriterion("\"is_sub_entity\" between", value1, value2, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andIsSubEntityNotBetween(String value1, String value2) {
            addCriterion("\"is_sub_entity\" not between", value1, value2, "isSubEntity");
            return (Criteria) this;
        }

        public Criteria andNegativeWordIsNull() {
            addCriterion("\"negative_word\" is null");
            return (Criteria) this;
        }

        public Criteria andNegativeWordIsNotNull() {
            addCriterion("\"negative_word\" is not null");
            return (Criteria) this;
        }

        public Criteria andNegativeWordEqualTo(String value) {
            addCriterion("\"negative_word\" =", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordNotEqualTo(String value) {
            addCriterion("\"negative_word\" <>", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordGreaterThan(String value) {
            addCriterion("\"negative_word\" >", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordGreaterThanOrEqualTo(String value) {
            addCriterion("\"negative_word\" >=", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordLessThan(String value) {
            addCriterion("\"negative_word\" <", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordLessThanOrEqualTo(String value) {
            addCriterion("\"negative_word\" <=", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordLike(String value) {
            addCriterion("\"negative_word\" like", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordNotLike(String value) {
            addCriterion("\"negative_word\" not like", value, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordIn(List<String> values) {
            addCriterion("\"negative_word\" in", values, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordNotIn(List<String> values) {
            addCriterion("\"negative_word\" not in", values, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordBetween(String value1, String value2) {
            addCriterion("\"negative_word\" between", value1, value2, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andNegativeWordNotBetween(String value1, String value2) {
            addCriterion("\"negative_word\" not between", value1, value2, "negativeWord");
            return (Criteria) this;
        }

        public Criteria andProbabilityIsNull() {
            addCriterion("\"probability\" is null");
            return (Criteria) this;
        }

        public Criteria andProbabilityIsNotNull() {
            addCriterion("\"probability\" is not null");
            return (Criteria) this;
        }

        public Criteria andProbabilityEqualTo(String value) {
            addCriterion("\"probability\" =", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityNotEqualTo(String value) {
            addCriterion("\"probability\" <>", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityGreaterThan(String value) {
            addCriterion("\"probability\" >", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityGreaterThanOrEqualTo(String value) {
            addCriterion("\"probability\" >=", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityLessThan(String value) {
            addCriterion("\"probability\" <", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityLessThanOrEqualTo(String value) {
            addCriterion("\"probability\" <=", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityLike(String value) {
            addCriterion("\"probability\" like", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityNotLike(String value) {
            addCriterion("\"probability\" not like", value, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityIn(List<String> values) {
            addCriterion("\"probability\" in", values, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityNotIn(List<String> values) {
            addCriterion("\"probability\" not in", values, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityBetween(String value1, String value2) {
            addCriterion("\"probability\" between", value1, value2, "probability");
            return (Criteria) this;
        }

        public Criteria andProbabilityNotBetween(String value1, String value2) {
            addCriterion("\"probability\" not between", value1, value2, "probability");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("\"duration\" is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("\"duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(String value) {
            addCriterion("\"duration\" =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(String value) {
            addCriterion("\"duration\" <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(String value) {
            addCriterion("\"duration\" >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"duration\" >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(String value) {
            addCriterion("\"duration\" <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(String value) {
            addCriterion("\"duration\" <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLike(String value) {
            addCriterion("\"duration\" like", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotLike(String value) {
            addCriterion("\"duration\" not like", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<String> values) {
            addCriterion("\"duration\" in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<String> values) {
            addCriterion("\"duration\" not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(String value1, String value2) {
            addCriterion("\"duration\" between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(String value1, String value2) {
            addCriterion("\"duration\" not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("\"start_date\" is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("\"start_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(String value) {
            addCriterion("\"start_date\" =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(String value) {
            addCriterion("\"start_date\" <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(String value) {
            addCriterion("\"start_date\" >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("\"start_date\" >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(String value) {
            addCriterion("\"start_date\" <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(String value) {
            addCriterion("\"start_date\" <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLike(String value) {
            addCriterion("\"start_date\" like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotLike(String value) {
            addCriterion("\"start_date\" not like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<String> values) {
            addCriterion("\"start_date\" in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<String> values) {
            addCriterion("\"start_date\" not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(String value1, String value2) {
            addCriterion("\"start_date\" between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(String value1, String value2) {
            addCriterion("\"start_date\" not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("\"end_date\" is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("\"end_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(String value) {
            addCriterion("\"end_date\" =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(String value) {
            addCriterion("\"end_date\" <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(String value) {
            addCriterion("\"end_date\" >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("\"end_date\" >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(String value) {
            addCriterion("\"end_date\" <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(String value) {
            addCriterion("\"end_date\" <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLike(String value) {
            addCriterion("\"end_date\" like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotLike(String value) {
            addCriterion("\"end_date\" not like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<String> values) {
            addCriterion("\"end_date\" in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<String> values) {
            addCriterion("\"end_date\" not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(String value1, String value2) {
            addCriterion("\"end_date\" between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(String value1, String value2) {
            addCriterion("\"end_date\" not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateIsNull() {
            addCriterion("\"standard_start_date\" is null");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateIsNotNull() {
            addCriterion("\"standard_start_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateEqualTo(String value) {
            addCriterion("\"standard_start_date\" =", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateNotEqualTo(String value) {
            addCriterion("\"standard_start_date\" <>", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateGreaterThan(String value) {
            addCriterion("\"standard_start_date\" >", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("\"standard_start_date\" >=", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateLessThan(String value) {
            addCriterion("\"standard_start_date\" <", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateLessThanOrEqualTo(String value) {
            addCriterion("\"standard_start_date\" <=", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateLike(String value) {
            addCriterion("\"standard_start_date\" like", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateNotLike(String value) {
            addCriterion("\"standard_start_date\" not like", value, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateIn(List<String> values) {
            addCriterion("\"standard_start_date\" in", values, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateNotIn(List<String> values) {
            addCriterion("\"standard_start_date\" not in", values, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateBetween(String value1, String value2) {
            addCriterion("\"standard_start_date\" between", value1, value2, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardStartDateNotBetween(String value1, String value2) {
            addCriterion("\"standard_start_date\" not between", value1, value2, "standardStartDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateIsNull() {
            addCriterion("\"standard_end_date\" is null");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateIsNotNull() {
            addCriterion("\"standard_end_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateEqualTo(String value) {
            addCriterion("\"standard_end_date\" =", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateNotEqualTo(String value) {
            addCriterion("\"standard_end_date\" <>", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateGreaterThan(String value) {
            addCriterion("\"standard_end_date\" >", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("\"standard_end_date\" >=", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateLessThan(String value) {
            addCriterion("\"standard_end_date\" <", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateLessThanOrEqualTo(String value) {
            addCriterion("\"standard_end_date\" <=", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateLike(String value) {
            addCriterion("\"standard_end_date\" like", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateNotLike(String value) {
            addCriterion("\"standard_end_date\" not like", value, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateIn(List<String> values) {
            addCriterion("\"standard_end_date\" in", values, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateNotIn(List<String> values) {
            addCriterion("\"standard_end_date\" not in", values, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateBetween(String value1, String value2) {
            addCriterion("\"standard_end_date\" between", value1, value2, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andStandardEndDateNotBetween(String value1, String value2) {
            addCriterion("\"standard_end_date\" not between", value1, value2, "standardEndDate");
            return (Criteria) this;
        }

        public Criteria andEventBodyIsNull() {
            addCriterion("\"event_body\" is null");
            return (Criteria) this;
        }

        public Criteria andEventBodyIsNotNull() {
            addCriterion("\"event_body\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventBodyEqualTo(String value) {
            addCriterion("\"event_body\" =", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyNotEqualTo(String value) {
            addCriterion("\"event_body\" <>", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyGreaterThan(String value) {
            addCriterion("\"event_body\" >", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyGreaterThanOrEqualTo(String value) {
            addCriterion("\"event_body\" >=", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyLessThan(String value) {
            addCriterion("\"event_body\" <", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyLessThanOrEqualTo(String value) {
            addCriterion("\"event_body\" <=", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyLike(String value) {
            addCriterion("\"event_body\" like", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyNotLike(String value) {
            addCriterion("\"event_body\" not like", value, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyIn(List<String> values) {
            addCriterion("\"event_body\" in", values, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyNotIn(List<String> values) {
            addCriterion("\"event_body\" not in", values, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyBetween(String value1, String value2) {
            addCriterion("\"event_body\" between", value1, value2, "eventBody");
            return (Criteria) this;
        }

        public Criteria andEventBodyNotBetween(String value1, String value2) {
            addCriterion("\"event_body\" not between", value1, value2, "eventBody");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionIsNull() {
            addCriterion("\"body_direction\" is null");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionIsNotNull() {
            addCriterion("\"body_direction\" is not null");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionEqualTo(String value) {
            addCriterion("\"body_direction\" =", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionNotEqualTo(String value) {
            addCriterion("\"body_direction\" <>", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionGreaterThan(String value) {
            addCriterion("\"body_direction\" >", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionGreaterThanOrEqualTo(String value) {
            addCriterion("\"body_direction\" >=", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionLessThan(String value) {
            addCriterion("\"body_direction\" <", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionLessThanOrEqualTo(String value) {
            addCriterion("\"body_direction\" <=", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionLike(String value) {
            addCriterion("\"body_direction\" like", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionNotLike(String value) {
            addCriterion("\"body_direction\" not like", value, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionIn(List<String> values) {
            addCriterion("\"body_direction\" in", values, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionNotIn(List<String> values) {
            addCriterion("\"body_direction\" not in", values, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionBetween(String value1, String value2) {
            addCriterion("\"body_direction\" between", value1, value2, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andBodyDirectionNotBetween(String value1, String value2) {
            addCriterion("\"body_direction\" not between", value1, value2, "bodyDirection");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeIsNull() {
            addCriterion("\"medicinal_problem_type\" is null");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeIsNotNull() {
            addCriterion("\"medicinal_problem_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeEqualTo(String value) {
            addCriterion("\"medicinal_problem_type\" =", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeNotEqualTo(String value) {
            addCriterion("\"medicinal_problem_type\" <>", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeGreaterThan(String value) {
            addCriterion("\"medicinal_problem_type\" >", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"medicinal_problem_type\" >=", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeLessThan(String value) {
            addCriterion("\"medicinal_problem_type\" <", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeLessThanOrEqualTo(String value) {
            addCriterion("\"medicinal_problem_type\" <=", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeLike(String value) {
            addCriterion("\"medicinal_problem_type\" like", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeNotLike(String value) {
            addCriterion("\"medicinal_problem_type\" not like", value, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeIn(List<String> values) {
            addCriterion("\"medicinal_problem_type\" in", values, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeNotIn(List<String> values) {
            addCriterion("\"medicinal_problem_type\" not in", values, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeBetween(String value1, String value2) {
            addCriterion("\"medicinal_problem_type\" between", value1, value2, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicinalProblemTypeNotBetween(String value1, String value2) {
            addCriterion("\"medicinal_problem_type\" not between", value1, value2, "medicinalProblemType");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionIsNull() {
            addCriterion("\"medical_condition\" is null");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionIsNotNull() {
            addCriterion("\"medical_condition\" is not null");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionEqualTo(String value) {
            addCriterion("\"medical_condition\" =", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionNotEqualTo(String value) {
            addCriterion("\"medical_condition\" <>", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionGreaterThan(String value) {
            addCriterion("\"medical_condition\" >", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionGreaterThanOrEqualTo(String value) {
            addCriterion("\"medical_condition\" >=", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionLessThan(String value) {
            addCriterion("\"medical_condition\" <", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionLessThanOrEqualTo(String value) {
            addCriterion("\"medical_condition\" <=", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionLike(String value) {
            addCriterion("\"medical_condition\" like", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionNotLike(String value) {
            addCriterion("\"medical_condition\" not like", value, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionIn(List<String> values) {
            addCriterion("\"medical_condition\" in", values, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionNotIn(List<String> values) {
            addCriterion("\"medical_condition\" not in", values, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionBetween(String value1, String value2) {
            addCriterion("\"medical_condition\" between", value1, value2, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andMedicalConditionNotBetween(String value1, String value2) {
            addCriterion("\"medical_condition\" not between", value1, value2, "medicalCondition");
            return (Criteria) this;
        }

        public Criteria andEventQualifierIsNull() {
            addCriterion("\"event_qualifier\" is null");
            return (Criteria) this;
        }

        public Criteria andEventQualifierIsNotNull() {
            addCriterion("\"event_qualifier\" is not null");
            return (Criteria) this;
        }

        public Criteria andEventQualifierEqualTo(String value) {
            addCriterion("\"event_qualifier\" =", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierNotEqualTo(String value) {
            addCriterion("\"event_qualifier\" <>", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierGreaterThan(String value) {
            addCriterion("\"event_qualifier\" >", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierGreaterThanOrEqualTo(String value) {
            addCriterion("\"event_qualifier\" >=", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierLessThan(String value) {
            addCriterion("\"event_qualifier\" <", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierLessThanOrEqualTo(String value) {
            addCriterion("\"event_qualifier\" <=", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierLike(String value) {
            addCriterion("\"event_qualifier\" like", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierNotLike(String value) {
            addCriterion("\"event_qualifier\" not like", value, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierIn(List<String> values) {
            addCriterion("\"event_qualifier\" in", values, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierNotIn(List<String> values) {
            addCriterion("\"event_qualifier\" not in", values, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierBetween(String value1, String value2) {
            addCriterion("\"event_qualifier\" between", value1, value2, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andEventQualifierNotBetween(String value1, String value2) {
            addCriterion("\"event_qualifier\" not between", value1, value2, "eventQualifier");
            return (Criteria) this;
        }

        public Criteria andGradeIsNull() {
            addCriterion("\"grade\" is null");
            return (Criteria) this;
        }

        public Criteria andGradeIsNotNull() {
            addCriterion("\"grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andGradeEqualTo(String value) {
            addCriterion("\"grade\" =", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotEqualTo(String value) {
            addCriterion("\"grade\" <>", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeGreaterThan(String value) {
            addCriterion("\"grade\" >", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"grade\" >=", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLessThan(String value) {
            addCriterion("\"grade\" <", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLessThanOrEqualTo(String value) {
            addCriterion("\"grade\" <=", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeLike(String value) {
            addCriterion("\"grade\" like", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotLike(String value) {
            addCriterion("\"grade\" not like", value, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeIn(List<String> values) {
            addCriterion("\"grade\" in", values, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotIn(List<String> values) {
            addCriterion("\"grade\" not in", values, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeBetween(String value1, String value2) {
            addCriterion("\"grade\" between", value1, value2, "grade");
            return (Criteria) this;
        }

        public Criteria andGradeNotBetween(String value1, String value2) {
            addCriterion("\"grade\" not between", value1, value2, "grade");
            return (Criteria) this;
        }

        public Criteria andMeasurementsIsNull() {
            addCriterion("\"measurements\" is null");
            return (Criteria) this;
        }

        public Criteria andMeasurementsIsNotNull() {
            addCriterion("\"measurements\" is not null");
            return (Criteria) this;
        }

        public Criteria andMeasurementsEqualTo(String value) {
            addCriterion("\"measurements\" =", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsNotEqualTo(String value) {
            addCriterion("\"measurements\" <>", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsGreaterThan(String value) {
            addCriterion("\"measurements\" >", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsGreaterThanOrEqualTo(String value) {
            addCriterion("\"measurements\" >=", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsLessThan(String value) {
            addCriterion("\"measurements\" <", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsLessThanOrEqualTo(String value) {
            addCriterion("\"measurements\" <=", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsLike(String value) {
            addCriterion("\"measurements\" like", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsNotLike(String value) {
            addCriterion("\"measurements\" not like", value, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsIn(List<String> values) {
            addCriterion("\"measurements\" in", values, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsNotIn(List<String> values) {
            addCriterion("\"measurements\" not in", values, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsBetween(String value1, String value2) {
            addCriterion("\"measurements\" between", value1, value2, "measurements");
            return (Criteria) this;
        }

        public Criteria andMeasurementsNotBetween(String value1, String value2) {
            addCriterion("\"measurements\" not between", value1, value2, "measurements");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationIsNull() {
            addCriterion("\"efficacy_evaluation\" is null");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationIsNotNull() {
            addCriterion("\"efficacy_evaluation\" is not null");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationEqualTo(String value) {
            addCriterion("\"efficacy_evaluation\" =", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationNotEqualTo(String value) {
            addCriterion("\"efficacy_evaluation\" <>", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationGreaterThan(String value) {
            addCriterion("\"efficacy_evaluation\" >", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationGreaterThanOrEqualTo(String value) {
            addCriterion("\"efficacy_evaluation\" >=", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationLessThan(String value) {
            addCriterion("\"efficacy_evaluation\" <", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationLessThanOrEqualTo(String value) {
            addCriterion("\"efficacy_evaluation\" <=", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationLike(String value) {
            addCriterion("\"efficacy_evaluation\" like", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationNotLike(String value) {
            addCriterion("\"efficacy_evaluation\" not like", value, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationIn(List<String> values) {
            addCriterion("\"efficacy_evaluation\" in", values, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationNotIn(List<String> values) {
            addCriterion("\"efficacy_evaluation\" not in", values, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationBetween(String value1, String value2) {
            addCriterion("\"efficacy_evaluation\" between", value1, value2, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andEfficacyEvaluationNotBetween(String value1, String value2) {
            addCriterion("\"efficacy_evaluation\" not between", value1, value2, "efficacyEvaluation");
            return (Criteria) this;
        }

        public Criteria andBloodSignalIsNull() {
            addCriterion("\"blood_signal\" is null");
            return (Criteria) this;
        }

        public Criteria andBloodSignalIsNotNull() {
            addCriterion("\"blood_signal\" is not null");
            return (Criteria) this;
        }

        public Criteria andBloodSignalEqualTo(String value) {
            addCriterion("\"blood_signal\" =", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalNotEqualTo(String value) {
            addCriterion("\"blood_signal\" <>", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalGreaterThan(String value) {
            addCriterion("\"blood_signal\" >", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalGreaterThanOrEqualTo(String value) {
            addCriterion("\"blood_signal\" >=", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLessThan(String value) {
            addCriterion("\"blood_signal\" <", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLessThanOrEqualTo(String value) {
            addCriterion("\"blood_signal\" <=", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLike(String value) {
            addCriterion("\"blood_signal\" like", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalNotLike(String value) {
            addCriterion("\"blood_signal\" not like", value, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalIn(List<String> values) {
            addCriterion("\"blood_signal\" in", values, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalNotIn(List<String> values) {
            addCriterion("\"blood_signal\" not in", values, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalBetween(String value1, String value2) {
            addCriterion("\"blood_signal\" between", value1, value2, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalNotBetween(String value1, String value2) {
            addCriterion("\"blood_signal\" not between", value1, value2, "bloodSignal");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelIsNull() {
            addCriterion("\"blood_signal_level\" is null");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelIsNotNull() {
            addCriterion("\"blood_signal_level\" is not null");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelEqualTo(String value) {
            addCriterion("\"blood_signal_level\" =", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelNotEqualTo(String value) {
            addCriterion("\"blood_signal_level\" <>", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelGreaterThan(String value) {
            addCriterion("\"blood_signal_level\" >", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelGreaterThanOrEqualTo(String value) {
            addCriterion("\"blood_signal_level\" >=", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelLessThan(String value) {
            addCriterion("\"blood_signal_level\" <", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelLessThanOrEqualTo(String value) {
            addCriterion("\"blood_signal_level\" <=", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelLike(String value) {
            addCriterion("\"blood_signal_level\" like", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelNotLike(String value) {
            addCriterion("\"blood_signal_level\" not like", value, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelIn(List<String> values) {
            addCriterion("\"blood_signal_level\" in", values, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelNotIn(List<String> values) {
            addCriterion("\"blood_signal_level\" not in", values, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelBetween(String value1, String value2) {
            addCriterion("\"blood_signal_level\" between", value1, value2, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andBloodSignalLevelNotBetween(String value1, String value2) {
            addCriterion("\"blood_signal_level\" not between", value1, value2, "bloodSignalLevel");
            return (Criteria) this;
        }

        public Criteria andTextureIsNull() {
            addCriterion("\"texture\" is null");
            return (Criteria) this;
        }

        public Criteria andTextureIsNotNull() {
            addCriterion("\"texture\" is not null");
            return (Criteria) this;
        }

        public Criteria andTextureEqualTo(String value) {
            addCriterion("\"texture\" =", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureNotEqualTo(String value) {
            addCriterion("\"texture\" <>", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureGreaterThan(String value) {
            addCriterion("\"texture\" >", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureGreaterThanOrEqualTo(String value) {
            addCriterion("\"texture\" >=", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureLessThan(String value) {
            addCriterion("\"texture\" <", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureLessThanOrEqualTo(String value) {
            addCriterion("\"texture\" <=", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureLike(String value) {
            addCriterion("\"texture\" like", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureNotLike(String value) {
            addCriterion("\"texture\" not like", value, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureIn(List<String> values) {
            addCriterion("\"texture\" in", values, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureNotIn(List<String> values) {
            addCriterion("\"texture\" not in", values, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureBetween(String value1, String value2) {
            addCriterion("\"texture\" between", value1, value2, "texture");
            return (Criteria) this;
        }

        public Criteria andTextureNotBetween(String value1, String value2) {
            addCriterion("\"texture\" not between", value1, value2, "texture");
            return (Criteria) this;
        }

        public Criteria andSizeIsNull() {
            addCriterion("\"size\" is null");
            return (Criteria) this;
        }

        public Criteria andSizeIsNotNull() {
            addCriterion("\"size\" is not null");
            return (Criteria) this;
        }

        public Criteria andSizeEqualTo(String value) {
            addCriterion("\"size\" =", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualTo(String value) {
            addCriterion("\"size\" <>", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThan(String value) {
            addCriterion("\"size\" >", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualTo(String value) {
            addCriterion("\"size\" >=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThan(String value) {
            addCriterion("\"size\" <", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualTo(String value) {
            addCriterion("\"size\" <=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLike(String value) {
            addCriterion("\"size\" like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotLike(String value) {
            addCriterion("\"size\" not like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeIn(List<String> values) {
            addCriterion("\"size\" in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotIn(List<String> values) {
            addCriterion("\"size\" not in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeBetween(String value1, String value2) {
            addCriterion("\"size\" between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotBetween(String value1, String value2) {
            addCriterion("\"size\" not between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andBoundaryIsNull() {
            addCriterion("\"boundary\" is null");
            return (Criteria) this;
        }

        public Criteria andBoundaryIsNotNull() {
            addCriterion("\"boundary\" is not null");
            return (Criteria) this;
        }

        public Criteria andBoundaryEqualTo(String value) {
            addCriterion("\"boundary\" =", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryNotEqualTo(String value) {
            addCriterion("\"boundary\" <>", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryGreaterThan(String value) {
            addCriterion("\"boundary\" >", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryGreaterThanOrEqualTo(String value) {
            addCriterion("\"boundary\" >=", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryLessThan(String value) {
            addCriterion("\"boundary\" <", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryLessThanOrEqualTo(String value) {
            addCriterion("\"boundary\" <=", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryLike(String value) {
            addCriterion("\"boundary\" like", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryNotLike(String value) {
            addCriterion("\"boundary\" not like", value, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryIn(List<String> values) {
            addCriterion("\"boundary\" in", values, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryNotIn(List<String> values) {
            addCriterion("\"boundary\" not in", values, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryBetween(String value1, String value2) {
            addCriterion("\"boundary\" between", value1, value2, "boundary");
            return (Criteria) this;
        }

        public Criteria andBoundaryNotBetween(String value1, String value2) {
            addCriterion("\"boundary\" not between", value1, value2, "boundary");
            return (Criteria) this;
        }

        public Criteria andMorphologyIsNull() {
            addCriterion("\"morphology\" is null");
            return (Criteria) this;
        }

        public Criteria andMorphologyIsNotNull() {
            addCriterion("\"morphology\" is not null");
            return (Criteria) this;
        }

        public Criteria andMorphologyEqualTo(String value) {
            addCriterion("\"morphology\" =", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyNotEqualTo(String value) {
            addCriterion("\"morphology\" <>", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyGreaterThan(String value) {
            addCriterion("\"morphology\" >", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyGreaterThanOrEqualTo(String value) {
            addCriterion("\"morphology\" >=", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyLessThan(String value) {
            addCriterion("\"morphology\" <", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyLessThanOrEqualTo(String value) {
            addCriterion("\"morphology\" <=", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyLike(String value) {
            addCriterion("\"morphology\" like", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyNotLike(String value) {
            addCriterion("\"morphology\" not like", value, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyIn(List<String> values) {
            addCriterion("\"morphology\" in", values, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyNotIn(List<String> values) {
            addCriterion("\"morphology\" not in", values, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyBetween(String value1, String value2) {
            addCriterion("\"morphology\" between", value1, value2, "morphology");
            return (Criteria) this;
        }

        public Criteria andMorphologyNotBetween(String value1, String value2) {
            addCriterion("\"morphology\" not between", value1, value2, "morphology");
            return (Criteria) this;
        }

        public Criteria andMetabolismIsNull() {
            addCriterion("\"metabolism\" is null");
            return (Criteria) this;
        }

        public Criteria andMetabolismIsNotNull() {
            addCriterion("\"metabolism\" is not null");
            return (Criteria) this;
        }

        public Criteria andMetabolismEqualTo(String value) {
            addCriterion("\"metabolism\" =", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismNotEqualTo(String value) {
            addCriterion("\"metabolism\" <>", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismGreaterThan(String value) {
            addCriterion("\"metabolism\" >", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismGreaterThanOrEqualTo(String value) {
            addCriterion("\"metabolism\" >=", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismLessThan(String value) {
            addCriterion("\"metabolism\" <", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismLessThanOrEqualTo(String value) {
            addCriterion("\"metabolism\" <=", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismLike(String value) {
            addCriterion("\"metabolism\" like", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismNotLike(String value) {
            addCriterion("\"metabolism\" not like", value, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismIn(List<String> values) {
            addCriterion("\"metabolism\" in", values, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismNotIn(List<String> values) {
            addCriterion("\"metabolism\" not in", values, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismBetween(String value1, String value2) {
            addCriterion("\"metabolism\" between", value1, value2, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMetabolismNotBetween(String value1, String value2) {
            addCriterion("\"metabolism\" not between", value1, value2, "metabolism");
            return (Criteria) this;
        }

        public Criteria andMaxValueIsNull() {
            addCriterion("\"max_value\" is null");
            return (Criteria) this;
        }

        public Criteria andMaxValueIsNotNull() {
            addCriterion("\"max_value\" is not null");
            return (Criteria) this;
        }

        public Criteria andMaxValueEqualTo(String value) {
            addCriterion("\"max_value\" =", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueNotEqualTo(String value) {
            addCriterion("\"max_value\" <>", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueGreaterThan(String value) {
            addCriterion("\"max_value\" >", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueGreaterThanOrEqualTo(String value) {
            addCriterion("\"max_value\" >=", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueLessThan(String value) {
            addCriterion("\"max_value\" <", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueLessThanOrEqualTo(String value) {
            addCriterion("\"max_value\" <=", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueLike(String value) {
            addCriterion("\"max_value\" like", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueNotLike(String value) {
            addCriterion("\"max_value\" not like", value, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueIn(List<String> values) {
            addCriterion("\"max_value\" in", values, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueNotIn(List<String> values) {
            addCriterion("\"max_value\" not in", values, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueBetween(String value1, String value2) {
            addCriterion("\"max_value\" between", value1, value2, "maxValue");
            return (Criteria) this;
        }

        public Criteria andMaxValueNotBetween(String value1, String value2) {
            addCriterion("\"max_value\" not between", value1, value2, "maxValue");
            return (Criteria) this;
        }

        public Criteria andEchoIsNull() {
            addCriterion("\"echo\" is null");
            return (Criteria) this;
        }

        public Criteria andEchoIsNotNull() {
            addCriterion("\"echo\" is not null");
            return (Criteria) this;
        }

        public Criteria andEchoEqualTo(String value) {
            addCriterion("\"echo\" =", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoNotEqualTo(String value) {
            addCriterion("\"echo\" <>", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoGreaterThan(String value) {
            addCriterion("\"echo\" >", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoGreaterThanOrEqualTo(String value) {
            addCriterion("\"echo\" >=", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoLessThan(String value) {
            addCriterion("\"echo\" <", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoLessThanOrEqualTo(String value) {
            addCriterion("\"echo\" <=", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoLike(String value) {
            addCriterion("\"echo\" like", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoNotLike(String value) {
            addCriterion("\"echo\" not like", value, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoIn(List<String> values) {
            addCriterion("\"echo\" in", values, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoNotIn(List<String> values) {
            addCriterion("\"echo\" not in", values, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoBetween(String value1, String value2) {
            addCriterion("\"echo\" between", value1, value2, "echo");
            return (Criteria) this;
        }

        public Criteria andEchoNotBetween(String value1, String value2) {
            addCriterion("\"echo\" not between", value1, value2, "echo");
            return (Criteria) this;
        }

        public Criteria andNecrosisIsNull() {
            addCriterion("\"necrosis\" is null");
            return (Criteria) this;
        }

        public Criteria andNecrosisIsNotNull() {
            addCriterion("\"necrosis\" is not null");
            return (Criteria) this;
        }

        public Criteria andNecrosisEqualTo(String value) {
            addCriterion("\"necrosis\" =", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisNotEqualTo(String value) {
            addCriterion("\"necrosis\" <>", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisGreaterThan(String value) {
            addCriterion("\"necrosis\" >", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisGreaterThanOrEqualTo(String value) {
            addCriterion("\"necrosis\" >=", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisLessThan(String value) {
            addCriterion("\"necrosis\" <", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisLessThanOrEqualTo(String value) {
            addCriterion("\"necrosis\" <=", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisLike(String value) {
            addCriterion("\"necrosis\" like", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisNotLike(String value) {
            addCriterion("\"necrosis\" not like", value, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisIn(List<String> values) {
            addCriterion("\"necrosis\" in", values, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisNotIn(List<String> values) {
            addCriterion("\"necrosis\" not in", values, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisBetween(String value1, String value2) {
            addCriterion("\"necrosis\" between", value1, value2, "necrosis");
            return (Criteria) this;
        }

        public Criteria andNecrosisNotBetween(String value1, String value2) {
            addCriterion("\"necrosis\" not between", value1, value2, "necrosis");
            return (Criteria) this;
        }

        public Criteria andCalcificationIsNull() {
            addCriterion("\"calcification\" is null");
            return (Criteria) this;
        }

        public Criteria andCalcificationIsNotNull() {
            addCriterion("\"calcification\" is not null");
            return (Criteria) this;
        }

        public Criteria andCalcificationEqualTo(String value) {
            addCriterion("\"calcification\" =", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationNotEqualTo(String value) {
            addCriterion("\"calcification\" <>", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationGreaterThan(String value) {
            addCriterion("\"calcification\" >", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationGreaterThanOrEqualTo(String value) {
            addCriterion("\"calcification\" >=", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationLessThan(String value) {
            addCriterion("\"calcification\" <", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationLessThanOrEqualTo(String value) {
            addCriterion("\"calcification\" <=", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationLike(String value) {
            addCriterion("\"calcification\" like", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationNotLike(String value) {
            addCriterion("\"calcification\" not like", value, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationIn(List<String> values) {
            addCriterion("\"calcification\" in", values, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationNotIn(List<String> values) {
            addCriterion("\"calcification\" not in", values, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationBetween(String value1, String value2) {
            addCriterion("\"calcification\" between", value1, value2, "calcification");
            return (Criteria) this;
        }

        public Criteria andCalcificationNotBetween(String value1, String value2) {
            addCriterion("\"calcification\" not between", value1, value2, "calcification");
            return (Criteria) this;
        }

        public Criteria andPathoPatternIsNull() {
            addCriterion("\"patho_pattern\" is null");
            return (Criteria) this;
        }

        public Criteria andPathoPatternIsNotNull() {
            addCriterion("\"patho_pattern\" is not null");
            return (Criteria) this;
        }

        public Criteria andPathoPatternEqualTo(String value) {
            addCriterion("\"patho_pattern\" =", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternNotEqualTo(String value) {
            addCriterion("\"patho_pattern\" <>", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternGreaterThan(String value) {
            addCriterion("\"patho_pattern\" >", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternGreaterThanOrEqualTo(String value) {
            addCriterion("\"patho_pattern\" >=", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternLessThan(String value) {
            addCriterion("\"patho_pattern\" <", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternLessThanOrEqualTo(String value) {
            addCriterion("\"patho_pattern\" <=", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternLike(String value) {
            addCriterion("\"patho_pattern\" like", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternNotLike(String value) {
            addCriterion("\"patho_pattern\" not like", value, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternIn(List<String> values) {
            addCriterion("\"patho_pattern\" in", values, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternNotIn(List<String> values) {
            addCriterion("\"patho_pattern\" not in", values, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternBetween(String value1, String value2) {
            addCriterion("\"patho_pattern\" between", value1, value2, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoPatternNotBetween(String value1, String value2) {
            addCriterion("\"patho_pattern\" not between", value1, value2, "pathoPattern");
            return (Criteria) this;
        }

        public Criteria andPathoGradeIsNull() {
            addCriterion("\"patho_grade\" is null");
            return (Criteria) this;
        }

        public Criteria andPathoGradeIsNotNull() {
            addCriterion("\"patho_grade\" is not null");
            return (Criteria) this;
        }

        public Criteria andPathoGradeEqualTo(String value) {
            addCriterion("\"patho_grade\" =", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeNotEqualTo(String value) {
            addCriterion("\"patho_grade\" <>", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeGreaterThan(String value) {
            addCriterion("\"patho_grade\" >", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeGreaterThanOrEqualTo(String value) {
            addCriterion("\"patho_grade\" >=", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeLessThan(String value) {
            addCriterion("\"patho_grade\" <", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeLessThanOrEqualTo(String value) {
            addCriterion("\"patho_grade\" <=", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeLike(String value) {
            addCriterion("\"patho_grade\" like", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeNotLike(String value) {
            addCriterion("\"patho_grade\" not like", value, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeIn(List<String> values) {
            addCriterion("\"patho_grade\" in", values, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeNotIn(List<String> values) {
            addCriterion("\"patho_grade\" not in", values, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeBetween(String value1, String value2) {
            addCriterion("\"patho_grade\" between", value1, value2, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andPathoGradeNotBetween(String value1, String value2) {
            addCriterion("\"patho_grade\" not between", value1, value2, "pathoGrade");
            return (Criteria) this;
        }

        public Criteria andDifferentiationIsNull() {
            addCriterion("\"differentiation\" is null");
            return (Criteria) this;
        }

        public Criteria andDifferentiationIsNotNull() {
            addCriterion("\"differentiation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDifferentiationEqualTo(String value) {
            addCriterion("\"differentiation\" =", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationNotEqualTo(String value) {
            addCriterion("\"differentiation\" <>", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationGreaterThan(String value) {
            addCriterion("\"differentiation\" >", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationGreaterThanOrEqualTo(String value) {
            addCriterion("\"differentiation\" >=", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationLessThan(String value) {
            addCriterion("\"differentiation\" <", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationLessThanOrEqualTo(String value) {
            addCriterion("\"differentiation\" <=", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationLike(String value) {
            addCriterion("\"differentiation\" like", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationNotLike(String value) {
            addCriterion("\"differentiation\" not like", value, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationIn(List<String> values) {
            addCriterion("\"differentiation\" in", values, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationNotIn(List<String> values) {
            addCriterion("\"differentiation\" not in", values, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationBetween(String value1, String value2) {
            addCriterion("\"differentiation\" between", value1, value2, "differentiation");
            return (Criteria) this;
        }

        public Criteria andDifferentiationNotBetween(String value1, String value2) {
            addCriterion("\"differentiation\" not between", value1, value2, "differentiation");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("\"status\" is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("\"status\" is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("\"status\" =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("\"status\" <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("\"status\" >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"status\" >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("\"status\" <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("\"status\" <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("\"status\" like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("\"status\" not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("\"status\" in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("\"status\" not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("\"status\" between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("\"status\" not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodIsNull() {
            addCriterion("\"examination_method\" is null");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodIsNotNull() {
            addCriterion("\"examination_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodEqualTo(String value) {
            addCriterion("\"examination_method\" =", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodNotEqualTo(String value) {
            addCriterion("\"examination_method\" <>", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodGreaterThan(String value) {
            addCriterion("\"examination_method\" >", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"examination_method\" >=", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodLessThan(String value) {
            addCriterion("\"examination_method\" <", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodLessThanOrEqualTo(String value) {
            addCriterion("\"examination_method\" <=", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodLike(String value) {
            addCriterion("\"examination_method\" like", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodNotLike(String value) {
            addCriterion("\"examination_method\" not like", value, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodIn(List<String> values) {
            addCriterion("\"examination_method\" in", values, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodNotIn(List<String> values) {
            addCriterion("\"examination_method\" not in", values, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodBetween(String value1, String value2) {
            addCriterion("\"examination_method\" between", value1, value2, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andExaminationMethodNotBetween(String value1, String value2) {
            addCriterion("\"examination_method\" not between", value1, value2, "examinationMethod");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionIsNull() {
            addCriterion("\"qualitative_conclusion\" is null");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionIsNotNull() {
            addCriterion("\"qualitative_conclusion\" is not null");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionEqualTo(String value) {
            addCriterion("\"qualitative_conclusion\" =", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionNotEqualTo(String value) {
            addCriterion("\"qualitative_conclusion\" <>", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionGreaterThan(String value) {
            addCriterion("\"qualitative_conclusion\" >", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionGreaterThanOrEqualTo(String value) {
            addCriterion("\"qualitative_conclusion\" >=", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionLessThan(String value) {
            addCriterion("\"qualitative_conclusion\" <", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionLessThanOrEqualTo(String value) {
            addCriterion("\"qualitative_conclusion\" <=", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionLike(String value) {
            addCriterion("\"qualitative_conclusion\" like", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionNotLike(String value) {
            addCriterion("\"qualitative_conclusion\" not like", value, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionIn(List<String> values) {
            addCriterion("\"qualitative_conclusion\" in", values, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionNotIn(List<String> values) {
            addCriterion("\"qualitative_conclusion\" not in", values, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionBetween(String value1, String value2) {
            addCriterion("\"qualitative_conclusion\" between", value1, value2, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andQualitativeConclusionNotBetween(String value1, String value2) {
            addCriterion("\"qualitative_conclusion\" not between", value1, value2, "qualitativeConclusion");
            return (Criteria) this;
        }

        public Criteria andApproachIsNull() {
            addCriterion("\"approach\" is null");
            return (Criteria) this;
        }

        public Criteria andApproachIsNotNull() {
            addCriterion("\"approach\" is not null");
            return (Criteria) this;
        }

        public Criteria andApproachEqualTo(String value) {
            addCriterion("\"approach\" =", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachNotEqualTo(String value) {
            addCriterion("\"approach\" <>", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachGreaterThan(String value) {
            addCriterion("\"approach\" >", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachGreaterThanOrEqualTo(String value) {
            addCriterion("\"approach\" >=", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachLessThan(String value) {
            addCriterion("\"approach\" <", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachLessThanOrEqualTo(String value) {
            addCriterion("\"approach\" <=", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachLike(String value) {
            addCriterion("\"approach\" like", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachNotLike(String value) {
            addCriterion("\"approach\" not like", value, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachIn(List<String> values) {
            addCriterion("\"approach\" in", values, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachNotIn(List<String> values) {
            addCriterion("\"approach\" not in", values, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachBetween(String value1, String value2) {
            addCriterion("\"approach\" between", value1, value2, "approach");
            return (Criteria) this;
        }

        public Criteria andApproachNotBetween(String value1, String value2) {
            addCriterion("\"approach\" not between", value1, value2, "approach");
            return (Criteria) this;
        }

        public Criteria andDosageIsNull() {
            addCriterion("\"dosage\" is null");
            return (Criteria) this;
        }

        public Criteria andDosageIsNotNull() {
            addCriterion("\"dosage\" is not null");
            return (Criteria) this;
        }

        public Criteria andDosageEqualTo(String value) {
            addCriterion("\"dosage\" =", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotEqualTo(String value) {
            addCriterion("\"dosage\" <>", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageGreaterThan(String value) {
            addCriterion("\"dosage\" >", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageGreaterThanOrEqualTo(String value) {
            addCriterion("\"dosage\" >=", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLessThan(String value) {
            addCriterion("\"dosage\" <", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLessThanOrEqualTo(String value) {
            addCriterion("\"dosage\" <=", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageLike(String value) {
            addCriterion("\"dosage\" like", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotLike(String value) {
            addCriterion("\"dosage\" not like", value, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageIn(List<String> values) {
            addCriterion("\"dosage\" in", values, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotIn(List<String> values) {
            addCriterion("\"dosage\" not in", values, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageBetween(String value1, String value2) {
            addCriterion("\"dosage\" between", value1, value2, "dosage");
            return (Criteria) this;
        }

        public Criteria andDosageNotBetween(String value1, String value2) {
            addCriterion("\"dosage\" not between", value1, value2, "dosage");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNull() {
            addCriterion("\"frequency\" is null");
            return (Criteria) this;
        }

        public Criteria andFrequencyIsNotNull() {
            addCriterion("\"frequency\" is not null");
            return (Criteria) this;
        }

        public Criteria andFrequencyEqualTo(String value) {
            addCriterion("\"frequency\" =", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotEqualTo(String value) {
            addCriterion("\"frequency\" <>", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThan(String value) {
            addCriterion("\"frequency\" >", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyGreaterThanOrEqualTo(String value) {
            addCriterion("\"frequency\" >=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThan(String value) {
            addCriterion("\"frequency\" <", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLessThanOrEqualTo(String value) {
            addCriterion("\"frequency\" <=", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyLike(String value) {
            addCriterion("\"frequency\" like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotLike(String value) {
            addCriterion("\"frequency\" not like", value, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyIn(List<String> values) {
            addCriterion("\"frequency\" in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotIn(List<String> values) {
            addCriterion("\"frequency\" not in", values, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyBetween(String value1, String value2) {
            addCriterion("\"frequency\" between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andFrequencyNotBetween(String value1, String value2) {
            addCriterion("\"frequency\" not between", value1, value2, "frequency");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNull() {
            addCriterion("\"period\" is null");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNotNull() {
            addCriterion("\"period\" is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodEqualTo(String value) {
            addCriterion("\"period\" =", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotEqualTo(String value) {
            addCriterion("\"period\" <>", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThan(String value) {
            addCriterion("\"period\" >", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("\"period\" >=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThan(String value) {
            addCriterion("\"period\" <", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThanOrEqualTo(String value) {
            addCriterion("\"period\" <=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLike(String value) {
            addCriterion("\"period\" like", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotLike(String value) {
            addCriterion("\"period\" not like", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodIn(List<String> values) {
            addCriterion("\"period\" in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotIn(List<String> values) {
            addCriterion("\"period\" not in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodBetween(String value1, String value2) {
            addCriterion("\"period\" between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotBetween(String value1, String value2) {
            addCriterion("\"period\" not between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNull() {
            addCriterion("\"category\" is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIsNotNull() {
            addCriterion("\"category\" is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryEqualTo(String value) {
            addCriterion("\"category\" =", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotEqualTo(String value) {
            addCriterion("\"category\" <>", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThan(String value) {
            addCriterion("\"category\" >", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("\"category\" >=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThan(String value) {
            addCriterion("\"category\" <", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLessThanOrEqualTo(String value) {
            addCriterion("\"category\" <=", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryLike(String value) {
            addCriterion("\"category\" like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotLike(String value) {
            addCriterion("\"category\" not like", value, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryIn(List<String> values) {
            addCriterion("\"category\" in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotIn(List<String> values) {
            addCriterion("\"category\" not in", values, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryBetween(String value1, String value2) {
            addCriterion("\"category\" between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andCategoryNotBetween(String value1, String value2) {
            addCriterion("\"category\" not between", value1, value2, "category");
            return (Criteria) this;
        }

        public Criteria andOperationMethodIsNull() {
            addCriterion("\"operation_method\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationMethodIsNotNull() {
            addCriterion("\"operation_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationMethodEqualTo(String value) {
            addCriterion("\"operation_method\" =", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodNotEqualTo(String value) {
            addCriterion("\"operation_method\" <>", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodGreaterThan(String value) {
            addCriterion("\"operation_method\" >", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_method\" >=", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodLessThan(String value) {
            addCriterion("\"operation_method\" <", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodLessThanOrEqualTo(String value) {
            addCriterion("\"operation_method\" <=", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodLike(String value) {
            addCriterion("\"operation_method\" like", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodNotLike(String value) {
            addCriterion("\"operation_method\" not like", value, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodIn(List<String> values) {
            addCriterion("\"operation_method\" in", values, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodNotIn(List<String> values) {
            addCriterion("\"operation_method\" not in", values, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodBetween(String value1, String value2) {
            addCriterion("\"operation_method\" between", value1, value2, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andOperationMethodNotBetween(String value1, String value2) {
            addCriterion("\"operation_method\" not between", value1, value2, "operationMethod");
            return (Criteria) this;
        }

        public Criteria andInstrumentIsNull() {
            addCriterion("\"instrument\" is null");
            return (Criteria) this;
        }

        public Criteria andInstrumentIsNotNull() {
            addCriterion("\"instrument\" is not null");
            return (Criteria) this;
        }

        public Criteria andInstrumentEqualTo(String value) {
            addCriterion("\"instrument\" =", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentNotEqualTo(String value) {
            addCriterion("\"instrument\" <>", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentGreaterThan(String value) {
            addCriterion("\"instrument\" >", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentGreaterThanOrEqualTo(String value) {
            addCriterion("\"instrument\" >=", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentLessThan(String value) {
            addCriterion("\"instrument\" <", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentLessThanOrEqualTo(String value) {
            addCriterion("\"instrument\" <=", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentLike(String value) {
            addCriterion("\"instrument\" like", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentNotLike(String value) {
            addCriterion("\"instrument\" not like", value, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentIn(List<String> values) {
            addCriterion("\"instrument\" in", values, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentNotIn(List<String> values) {
            addCriterion("\"instrument\" not in", values, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentBetween(String value1, String value2) {
            addCriterion("\"instrument\" between", value1, value2, "instrument");
            return (Criteria) this;
        }

        public Criteria andInstrumentNotBetween(String value1, String value2) {
            addCriterion("\"instrument\" not between", value1, value2, "instrument");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNull() {
            addCriterion("\"anesthesia_method\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNotNull() {
            addCriterion("\"anesthesia_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodEqualTo(String value) {
            addCriterion("\"anesthesia_method\" =", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <>", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThan(String value) {
            addCriterion("\"anesthesia_method\" >", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" >=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThan(String value) {
            addCriterion("\"anesthesia_method\" <", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLike(String value) {
            addCriterion("\"anesthesia_method\" like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotLike(String value) {
            addCriterion("\"anesthesia_method\" not like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIn(List<String> values) {
            addCriterion("\"anesthesia_method\" in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotIn(List<String> values) {
            addCriterion("\"anesthesia_method\" not in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" not between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryIsNull() {
            addCriterion("\"has_marry_history\" is null");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryIsNotNull() {
            addCriterion("\"has_marry_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryEqualTo(String value) {
            addCriterion("\"has_marry_history\" =", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryNotEqualTo(String value) {
            addCriterion("\"has_marry_history\" <>", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryGreaterThan(String value) {
            addCriterion("\"has_marry_history\" >", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_marry_history\" >=", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryLessThan(String value) {
            addCriterion("\"has_marry_history\" <", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryLessThanOrEqualTo(String value) {
            addCriterion("\"has_marry_history\" <=", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryLike(String value) {
            addCriterion("\"has_marry_history\" like", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryNotLike(String value) {
            addCriterion("\"has_marry_history\" not like", value, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryIn(List<String> values) {
            addCriterion("\"has_marry_history\" in", values, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryNotIn(List<String> values) {
            addCriterion("\"has_marry_history\" not in", values, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryBetween(String value1, String value2) {
            addCriterion("\"has_marry_history\" between", value1, value2, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andHasMarryHistoryNotBetween(String value1, String value2) {
            addCriterion("\"has_marry_history\" not between", value1, value2, "hasMarryHistory");
            return (Criteria) this;
        }

        public Criteria andMarryTimeIsNull() {
            addCriterion("\"marry_time\" is null");
            return (Criteria) this;
        }

        public Criteria andMarryTimeIsNotNull() {
            addCriterion("\"marry_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andMarryTimeEqualTo(String value) {
            addCriterion("\"marry_time\" =", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeNotEqualTo(String value) {
            addCriterion("\"marry_time\" <>", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeGreaterThan(String value) {
            addCriterion("\"marry_time\" >", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"marry_time\" >=", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeLessThan(String value) {
            addCriterion("\"marry_time\" <", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeLessThanOrEqualTo(String value) {
            addCriterion("\"marry_time\" <=", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeLike(String value) {
            addCriterion("\"marry_time\" like", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeNotLike(String value) {
            addCriterion("\"marry_time\" not like", value, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeIn(List<String> values) {
            addCriterion("\"marry_time\" in", values, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeNotIn(List<String> values) {
            addCriterion("\"marry_time\" not in", values, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeBetween(String value1, String value2) {
            addCriterion("\"marry_time\" between", value1, value2, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryTimeNotBetween(String value1, String value2) {
            addCriterion("\"marry_time\" not between", value1, value2, "marryTime");
            return (Criteria) this;
        }

        public Criteria andMarryAgeIsNull() {
            addCriterion("\"marry_age\" is null");
            return (Criteria) this;
        }

        public Criteria andMarryAgeIsNotNull() {
            addCriterion("\"marry_age\" is not null");
            return (Criteria) this;
        }

        public Criteria andMarryAgeEqualTo(String value) {
            addCriterion("\"marry_age\" =", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeNotEqualTo(String value) {
            addCriterion("\"marry_age\" <>", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeGreaterThan(String value) {
            addCriterion("\"marry_age\" >", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeGreaterThanOrEqualTo(String value) {
            addCriterion("\"marry_age\" >=", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeLessThan(String value) {
            addCriterion("\"marry_age\" <", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeLessThanOrEqualTo(String value) {
            addCriterion("\"marry_age\" <=", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeLike(String value) {
            addCriterion("\"marry_age\" like", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeNotLike(String value) {
            addCriterion("\"marry_age\" not like", value, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeIn(List<String> values) {
            addCriterion("\"marry_age\" in", values, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeNotIn(List<String> values) {
            addCriterion("\"marry_age\" not in", values, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeBetween(String value1, String value2) {
            addCriterion("\"marry_age\" between", value1, value2, "marryAge");
            return (Criteria) this;
        }

        public Criteria andMarryAgeNotBetween(String value1, String value2) {
            addCriterion("\"marry_age\" not between", value1, value2, "marryAge");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusIsNull() {
            addCriterion("\"partner_health_status\" is null");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusIsNotNull() {
            addCriterion("\"partner_health_status\" is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusEqualTo(String value) {
            addCriterion("\"partner_health_status\" =", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusNotEqualTo(String value) {
            addCriterion("\"partner_health_status\" <>", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusGreaterThan(String value) {
            addCriterion("\"partner_health_status\" >", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"partner_health_status\" >=", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusLessThan(String value) {
            addCriterion("\"partner_health_status\" <", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusLessThanOrEqualTo(String value) {
            addCriterion("\"partner_health_status\" <=", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusLike(String value) {
            addCriterion("\"partner_health_status\" like", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusNotLike(String value) {
            addCriterion("\"partner_health_status\" not like", value, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusIn(List<String> values) {
            addCriterion("\"partner_health_status\" in", values, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusNotIn(List<String> values) {
            addCriterion("\"partner_health_status\" not in", values, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusBetween(String value1, String value2) {
            addCriterion("\"partner_health_status\" between", value1, value2, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerHealthStatusNotBetween(String value1, String value2) {
            addCriterion("\"partner_health_status\" not between", value1, value2, "partnerHealthStatus");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseIsNull() {
            addCriterion("\"partner_illness_death_cause\" is null");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseIsNotNull() {
            addCriterion("\"partner_illness_death_cause\" is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseEqualTo(String value) {
            addCriterion("\"partner_illness_death_cause\" =", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseNotEqualTo(String value) {
            addCriterion("\"partner_illness_death_cause\" <>", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseGreaterThan(String value) {
            addCriterion("\"partner_illness_death_cause\" >", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseGreaterThanOrEqualTo(String value) {
            addCriterion("\"partner_illness_death_cause\" >=", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseLessThan(String value) {
            addCriterion("\"partner_illness_death_cause\" <", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseLessThanOrEqualTo(String value) {
            addCriterion("\"partner_illness_death_cause\" <=", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseLike(String value) {
            addCriterion("\"partner_illness_death_cause\" like", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseNotLike(String value) {
            addCriterion("\"partner_illness_death_cause\" not like", value, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseIn(List<String> values) {
            addCriterion("\"partner_illness_death_cause\" in", values, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseNotIn(List<String> values) {
            addCriterion("\"partner_illness_death_cause\" not in", values, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseBetween(String value1, String value2) {
            addCriterion("\"partner_illness_death_cause\" between", value1, value2, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andPartnerIllnessDeathCauseNotBetween(String value1, String value2) {
            addCriterion("\"partner_illness_death_cause\" not between", value1, value2, "partnerIllnessDeathCause");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryIsNull() {
            addCriterion("\"childbearing_history\" is null");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryIsNotNull() {
            addCriterion("\"childbearing_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryEqualTo(String value) {
            addCriterion("\"childbearing_history\" =", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryNotEqualTo(String value) {
            addCriterion("\"childbearing_history\" <>", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryGreaterThan(String value) {
            addCriterion("\"childbearing_history\" >", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryGreaterThanOrEqualTo(String value) {
            addCriterion("\"childbearing_history\" >=", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryLessThan(String value) {
            addCriterion("\"childbearing_history\" <", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryLessThanOrEqualTo(String value) {
            addCriterion("\"childbearing_history\" <=", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryLike(String value) {
            addCriterion("\"childbearing_history\" like", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryNotLike(String value) {
            addCriterion("\"childbearing_history\" not like", value, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryIn(List<String> values) {
            addCriterion("\"childbearing_history\" in", values, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryNotIn(List<String> values) {
            addCriterion("\"childbearing_history\" not in", values, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryBetween(String value1, String value2) {
            addCriterion("\"childbearing_history\" between", value1, value2, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildbearingHistoryNotBetween(String value1, String value2) {
            addCriterion("\"childbearing_history\" not between", value1, value2, "childbearingHistory");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusIsNull() {
            addCriterion("\"children_status\" is null");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusIsNotNull() {
            addCriterion("\"children_status\" is not null");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusEqualTo(String value) {
            addCriterion("\"children_status\" =", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusNotEqualTo(String value) {
            addCriterion("\"children_status\" <>", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusGreaterThan(String value) {
            addCriterion("\"children_status\" >", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"children_status\" >=", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusLessThan(String value) {
            addCriterion("\"children_status\" <", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusLessThanOrEqualTo(String value) {
            addCriterion("\"children_status\" <=", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusLike(String value) {
            addCriterion("\"children_status\" like", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusNotLike(String value) {
            addCriterion("\"children_status\" not like", value, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusIn(List<String> values) {
            addCriterion("\"children_status\" in", values, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusNotIn(List<String> values) {
            addCriterion("\"children_status\" not in", values, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusBetween(String value1, String value2) {
            addCriterion("\"children_status\" between", value1, value2, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andChildrenStatusNotBetween(String value1, String value2) {
            addCriterion("\"children_status\" not between", value1, value2, "childrenStatus");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeIsNull() {
            addCriterion("\"pregnancy_time\" is null");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeIsNotNull() {
            addCriterion("\"pregnancy_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeEqualTo(String value) {
            addCriterion("\"pregnancy_time\" =", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeNotEqualTo(String value) {
            addCriterion("\"pregnancy_time\" <>", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeGreaterThan(String value) {
            addCriterion("\"pregnancy_time\" >", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"pregnancy_time\" >=", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeLessThan(String value) {
            addCriterion("\"pregnancy_time\" <", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeLessThanOrEqualTo(String value) {
            addCriterion("\"pregnancy_time\" <=", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeLike(String value) {
            addCriterion("\"pregnancy_time\" like", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeNotLike(String value) {
            addCriterion("\"pregnancy_time\" not like", value, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeIn(List<String> values) {
            addCriterion("\"pregnancy_time\" in", values, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeNotIn(List<String> values) {
            addCriterion("\"pregnancy_time\" not in", values, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeBetween(String value1, String value2) {
            addCriterion("\"pregnancy_time\" between", value1, value2, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andPregnancyTimeNotBetween(String value1, String value2) {
            addCriterion("\"pregnancy_time\" not between", value1, value2, "pregnancyTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeIsNull() {
            addCriterion("\"give_birth_time\" is null");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeIsNotNull() {
            addCriterion("\"give_birth_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeEqualTo(String value) {
            addCriterion("\"give_birth_time\" =", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeNotEqualTo(String value) {
            addCriterion("\"give_birth_time\" <>", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeGreaterThan(String value) {
            addCriterion("\"give_birth_time\" >", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"give_birth_time\" >=", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeLessThan(String value) {
            addCriterion("\"give_birth_time\" <", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeLessThanOrEqualTo(String value) {
            addCriterion("\"give_birth_time\" <=", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeLike(String value) {
            addCriterion("\"give_birth_time\" like", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeNotLike(String value) {
            addCriterion("\"give_birth_time\" not like", value, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeIn(List<String> values) {
            addCriterion("\"give_birth_time\" in", values, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeNotIn(List<String> values) {
            addCriterion("\"give_birth_time\" not in", values, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeBetween(String value1, String value2) {
            addCriterion("\"give_birth_time\" between", value1, value2, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andGiveBirthTimeNotBetween(String value1, String value2) {
            addCriterion("\"give_birth_time\" not between", value1, value2, "giveBirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeIsNull() {
            addCriterion("\"misbirth_time\" is null");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeIsNotNull() {
            addCriterion("\"misbirth_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeEqualTo(String value) {
            addCriterion("\"misbirth_time\" =", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeNotEqualTo(String value) {
            addCriterion("\"misbirth_time\" <>", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeGreaterThan(String value) {
            addCriterion("\"misbirth_time\" >", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"misbirth_time\" >=", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeLessThan(String value) {
            addCriterion("\"misbirth_time\" <", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeLessThanOrEqualTo(String value) {
            addCriterion("\"misbirth_time\" <=", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeLike(String value) {
            addCriterion("\"misbirth_time\" like", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeNotLike(String value) {
            addCriterion("\"misbirth_time\" not like", value, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeIn(List<String> values) {
            addCriterion("\"misbirth_time\" in", values, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeNotIn(List<String> values) {
            addCriterion("\"misbirth_time\" not in", values, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeBetween(String value1, String value2) {
            addCriterion("\"misbirth_time\" between", value1, value2, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andMisbirthTimeNotBetween(String value1, String value2) {
            addCriterion("\"misbirth_time\" not between", value1, value2, "misbirthTime");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryIsNull() {
            addCriterion("\"has_drink_history\" is null");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryIsNotNull() {
            addCriterion("\"has_drink_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryEqualTo(String value) {
            addCriterion("\"has_drink_history\" =", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryNotEqualTo(String value) {
            addCriterion("\"has_drink_history\" <>", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryGreaterThan(String value) {
            addCriterion("\"has_drink_history\" >", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_drink_history\" >=", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryLessThan(String value) {
            addCriterion("\"has_drink_history\" <", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryLessThanOrEqualTo(String value) {
            addCriterion("\"has_drink_history\" <=", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryLike(String value) {
            addCriterion("\"has_drink_history\" like", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryNotLike(String value) {
            addCriterion("\"has_drink_history\" not like", value, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryIn(List<String> values) {
            addCriterion("\"has_drink_history\" in", values, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryNotIn(List<String> values) {
            addCriterion("\"has_drink_history\" not in", values, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryBetween(String value1, String value2) {
            addCriterion("\"has_drink_history\" between", value1, value2, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andHasDrinkHistoryNotBetween(String value1, String value2) {
            addCriterion("\"has_drink_history\" not between", value1, value2, "hasDrinkHistory");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeIsNull() {
            addCriterion("\"drink_type\" is null");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeIsNotNull() {
            addCriterion("\"drink_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeEqualTo(String value) {
            addCriterion("\"drink_type\" =", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeNotEqualTo(String value) {
            addCriterion("\"drink_type\" <>", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeGreaterThan(String value) {
            addCriterion("\"drink_type\" >", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"drink_type\" >=", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeLessThan(String value) {
            addCriterion("\"drink_type\" <", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeLessThanOrEqualTo(String value) {
            addCriterion("\"drink_type\" <=", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeLike(String value) {
            addCriterion("\"drink_type\" like", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeNotLike(String value) {
            addCriterion("\"drink_type\" not like", value, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeIn(List<String> values) {
            addCriterion("\"drink_type\" in", values, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeNotIn(List<String> values) {
            addCriterion("\"drink_type\" not in", values, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeBetween(String value1, String value2) {
            addCriterion("\"drink_type\" between", value1, value2, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTypeNotBetween(String value1, String value2) {
            addCriterion("\"drink_type\" not between", value1, value2, "drinkType");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeIsNull() {
            addCriterion("\"drink_time\" is null");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeIsNotNull() {
            addCriterion("\"drink_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeEqualTo(String value) {
            addCriterion("\"drink_time\" =", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeNotEqualTo(String value) {
            addCriterion("\"drink_time\" <>", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeGreaterThan(String value) {
            addCriterion("\"drink_time\" >", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"drink_time\" >=", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeLessThan(String value) {
            addCriterion("\"drink_time\" <", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeLessThanOrEqualTo(String value) {
            addCriterion("\"drink_time\" <=", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeLike(String value) {
            addCriterion("\"drink_time\" like", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeNotLike(String value) {
            addCriterion("\"drink_time\" not like", value, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeIn(List<String> values) {
            addCriterion("\"drink_time\" in", values, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeNotIn(List<String> values) {
            addCriterion("\"drink_time\" not in", values, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeBetween(String value1, String value2) {
            addCriterion("\"drink_time\" between", value1, value2, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkTimeNotBetween(String value1, String value2) {
            addCriterion("\"drink_time\" not between", value1, value2, "drinkTime");
            return (Criteria) this;
        }

        public Criteria andDrinkYearIsNull() {
            addCriterion("\"drink_year\" is null");
            return (Criteria) this;
        }

        public Criteria andDrinkYearIsNotNull() {
            addCriterion("\"drink_year\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrinkYearEqualTo(String value) {
            addCriterion("\"drink_year\" =", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearNotEqualTo(String value) {
            addCriterion("\"drink_year\" <>", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearGreaterThan(String value) {
            addCriterion("\"drink_year\" >", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"drink_year\" >=", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearLessThan(String value) {
            addCriterion("\"drink_year\" <", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearLessThanOrEqualTo(String value) {
            addCriterion("\"drink_year\" <=", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearLike(String value) {
            addCriterion("\"drink_year\" like", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearNotLike(String value) {
            addCriterion("\"drink_year\" not like", value, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearIn(List<String> values) {
            addCriterion("\"drink_year\" in", values, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearNotIn(List<String> values) {
            addCriterion("\"drink_year\" not in", values, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearBetween(String value1, String value2) {
            addCriterion("\"drink_year\" between", value1, value2, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkYearNotBetween(String value1, String value2) {
            addCriterion("\"drink_year\" not between", value1, value2, "drinkYear");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqIsNull() {
            addCriterion("\"drink_freq\" is null");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqIsNotNull() {
            addCriterion("\"drink_freq\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqEqualTo(String value) {
            addCriterion("\"drink_freq\" =", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqNotEqualTo(String value) {
            addCriterion("\"drink_freq\" <>", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqGreaterThan(String value) {
            addCriterion("\"drink_freq\" >", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqGreaterThanOrEqualTo(String value) {
            addCriterion("\"drink_freq\" >=", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqLessThan(String value) {
            addCriterion("\"drink_freq\" <", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqLessThanOrEqualTo(String value) {
            addCriterion("\"drink_freq\" <=", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqLike(String value) {
            addCriterion("\"drink_freq\" like", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqNotLike(String value) {
            addCriterion("\"drink_freq\" not like", value, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqIn(List<String> values) {
            addCriterion("\"drink_freq\" in", values, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqNotIn(List<String> values) {
            addCriterion("\"drink_freq\" not in", values, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqBetween(String value1, String value2) {
            addCriterion("\"drink_freq\" between", value1, value2, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkFreqNotBetween(String value1, String value2) {
            addCriterion("\"drink_freq\" not between", value1, value2, "drinkFreq");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountIsNull() {
            addCriterion("\"drink_amount\" is null");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountIsNotNull() {
            addCriterion("\"drink_amount\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountEqualTo(String value) {
            addCriterion("\"drink_amount\" =", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountNotEqualTo(String value) {
            addCriterion("\"drink_amount\" <>", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountGreaterThan(String value) {
            addCriterion("\"drink_amount\" >", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountGreaterThanOrEqualTo(String value) {
            addCriterion("\"drink_amount\" >=", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountLessThan(String value) {
            addCriterion("\"drink_amount\" <", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountLessThanOrEqualTo(String value) {
            addCriterion("\"drink_amount\" <=", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountLike(String value) {
            addCriterion("\"drink_amount\" like", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountNotLike(String value) {
            addCriterion("\"drink_amount\" not like", value, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountIn(List<String> values) {
            addCriterion("\"drink_amount\" in", values, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountNotIn(List<String> values) {
            addCriterion("\"drink_amount\" not in", values, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountBetween(String value1, String value2) {
            addCriterion("\"drink_amount\" between", value1, value2, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkAmountNotBetween(String value1, String value2) {
            addCriterion("\"drink_amount\" not between", value1, value2, "drinkAmount");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitIsNull() {
            addCriterion("\"drink_unit\" is null");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitIsNotNull() {
            addCriterion("\"drink_unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitEqualTo(String value) {
            addCriterion("\"drink_unit\" =", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitNotEqualTo(String value) {
            addCriterion("\"drink_unit\" <>", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitGreaterThan(String value) {
            addCriterion("\"drink_unit\" >", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"drink_unit\" >=", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitLessThan(String value) {
            addCriterion("\"drink_unit\" <", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitLessThanOrEqualTo(String value) {
            addCriterion("\"drink_unit\" <=", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitLike(String value) {
            addCriterion("\"drink_unit\" like", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitNotLike(String value) {
            addCriterion("\"drink_unit\" not like", value, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitIn(List<String> values) {
            addCriterion("\"drink_unit\" in", values, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitNotIn(List<String> values) {
            addCriterion("\"drink_unit\" not in", values, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitBetween(String value1, String value2) {
            addCriterion("\"drink_unit\" between", value1, value2, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andDrinkUnitNotBetween(String value1, String value2) {
            addCriterion("\"drink_unit\" not between", value1, value2, "drinkUnit");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholIsNull() {
            addCriterion("\"has_alcohol\" is null");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholIsNotNull() {
            addCriterion("\"has_alcohol\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholEqualTo(String value) {
            addCriterion("\"has_alcohol\" =", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholNotEqualTo(String value) {
            addCriterion("\"has_alcohol\" <>", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholGreaterThan(String value) {
            addCriterion("\"has_alcohol\" >", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_alcohol\" >=", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholLessThan(String value) {
            addCriterion("\"has_alcohol\" <", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholLessThanOrEqualTo(String value) {
            addCriterion("\"has_alcohol\" <=", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholLike(String value) {
            addCriterion("\"has_alcohol\" like", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholNotLike(String value) {
            addCriterion("\"has_alcohol\" not like", value, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholIn(List<String> values) {
            addCriterion("\"has_alcohol\" in", values, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholNotIn(List<String> values) {
            addCriterion("\"has_alcohol\" not in", values, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholBetween(String value1, String value2) {
            addCriterion("\"has_alcohol\" between", value1, value2, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andHasAlcoholNotBetween(String value1, String value2) {
            addCriterion("\"has_alcohol\" not between", value1, value2, "hasAlcohol");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeIsNull() {
            addCriterion("\"alcohol_time\" is null");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeIsNotNull() {
            addCriterion("\"alcohol_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeEqualTo(String value) {
            addCriterion("\"alcohol_time\" =", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeNotEqualTo(String value) {
            addCriterion("\"alcohol_time\" <>", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeGreaterThan(String value) {
            addCriterion("\"alcohol_time\" >", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"alcohol_time\" >=", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeLessThan(String value) {
            addCriterion("\"alcohol_time\" <", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeLessThanOrEqualTo(String value) {
            addCriterion("\"alcohol_time\" <=", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeLike(String value) {
            addCriterion("\"alcohol_time\" like", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeNotLike(String value) {
            addCriterion("\"alcohol_time\" not like", value, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeIn(List<String> values) {
            addCriterion("\"alcohol_time\" in", values, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeNotIn(List<String> values) {
            addCriterion("\"alcohol_time\" not in", values, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeBetween(String value1, String value2) {
            addCriterion("\"alcohol_time\" between", value1, value2, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholTimeNotBetween(String value1, String value2) {
            addCriterion("\"alcohol_time\" not between", value1, value2, "alcoholTime");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearIsNull() {
            addCriterion("\"alcohol_year\" is null");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearIsNotNull() {
            addCriterion("\"alcohol_year\" is not null");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearEqualTo(String value) {
            addCriterion("\"alcohol_year\" =", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearNotEqualTo(String value) {
            addCriterion("\"alcohol_year\" <>", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearGreaterThan(String value) {
            addCriterion("\"alcohol_year\" >", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"alcohol_year\" >=", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearLessThan(String value) {
            addCriterion("\"alcohol_year\" <", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearLessThanOrEqualTo(String value) {
            addCriterion("\"alcohol_year\" <=", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearLike(String value) {
            addCriterion("\"alcohol_year\" like", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearNotLike(String value) {
            addCriterion("\"alcohol_year\" not like", value, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearIn(List<String> values) {
            addCriterion("\"alcohol_year\" in", values, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearNotIn(List<String> values) {
            addCriterion("\"alcohol_year\" not in", values, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearBetween(String value1, String value2) {
            addCriterion("\"alcohol_year\" between", value1, value2, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andAlcoholYearNotBetween(String value1, String value2) {
            addCriterion("\"alcohol_year\" not between", value1, value2, "alcoholYear");
            return (Criteria) this;
        }

        public Criteria andHasSmokeIsNull() {
            addCriterion("\"has_smoke\" is null");
            return (Criteria) this;
        }

        public Criteria andHasSmokeIsNotNull() {
            addCriterion("\"has_smoke\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasSmokeEqualTo(String value) {
            addCriterion("\"has_smoke\" =", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeNotEqualTo(String value) {
            addCriterion("\"has_smoke\" <>", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeGreaterThan(String value) {
            addCriterion("\"has_smoke\" >", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_smoke\" >=", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeLessThan(String value) {
            addCriterion("\"has_smoke\" <", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeLessThanOrEqualTo(String value) {
            addCriterion("\"has_smoke\" <=", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeLike(String value) {
            addCriterion("\"has_smoke\" like", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeNotLike(String value) {
            addCriterion("\"has_smoke\" not like", value, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeIn(List<String> values) {
            addCriterion("\"has_smoke\" in", values, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeNotIn(List<String> values) {
            addCriterion("\"has_smoke\" not in", values, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeBetween(String value1, String value2) {
            addCriterion("\"has_smoke\" between", value1, value2, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andHasSmokeNotBetween(String value1, String value2) {
            addCriterion("\"has_smoke\" not between", value1, value2, "hasSmoke");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeIsNull() {
            addCriterion("\"smoke_time\" is null");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeIsNotNull() {
            addCriterion("\"smoke_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeEqualTo(String value) {
            addCriterion("\"smoke_time\" =", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeNotEqualTo(String value) {
            addCriterion("\"smoke_time\" <>", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeGreaterThan(String value) {
            addCriterion("\"smoke_time\" >", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"smoke_time\" >=", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeLessThan(String value) {
            addCriterion("\"smoke_time\" <", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeLessThanOrEqualTo(String value) {
            addCriterion("\"smoke_time\" <=", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeLike(String value) {
            addCriterion("\"smoke_time\" like", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeNotLike(String value) {
            addCriterion("\"smoke_time\" not like", value, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeIn(List<String> values) {
            addCriterion("\"smoke_time\" in", values, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeNotIn(List<String> values) {
            addCriterion("\"smoke_time\" not in", values, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeBetween(String value1, String value2) {
            addCriterion("\"smoke_time\" between", value1, value2, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeTimeNotBetween(String value1, String value2) {
            addCriterion("\"smoke_time\" not between", value1, value2, "smokeTime");
            return (Criteria) this;
        }

        public Criteria andSmokeYearIsNull() {
            addCriterion("\"smoke_year\" is null");
            return (Criteria) this;
        }

        public Criteria andSmokeYearIsNotNull() {
            addCriterion("\"smoke_year\" is not null");
            return (Criteria) this;
        }

        public Criteria andSmokeYearEqualTo(String value) {
            addCriterion("\"smoke_year\" =", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearNotEqualTo(String value) {
            addCriterion("\"smoke_year\" <>", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearGreaterThan(String value) {
            addCriterion("\"smoke_year\" >", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"smoke_year\" >=", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearLessThan(String value) {
            addCriterion("\"smoke_year\" <", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearLessThanOrEqualTo(String value) {
            addCriterion("\"smoke_year\" <=", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearLike(String value) {
            addCriterion("\"smoke_year\" like", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearNotLike(String value) {
            addCriterion("\"smoke_year\" not like", value, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearIn(List<String> values) {
            addCriterion("\"smoke_year\" in", values, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearNotIn(List<String> values) {
            addCriterion("\"smoke_year\" not in", values, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearBetween(String value1, String value2) {
            addCriterion("\"smoke_year\" between", value1, value2, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeYearNotBetween(String value1, String value2) {
            addCriterion("\"smoke_year\" not between", value1, value2, "smokeYear");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqIsNull() {
            addCriterion("\"smoke_freq\" is null");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqIsNotNull() {
            addCriterion("\"smoke_freq\" is not null");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqEqualTo(String value) {
            addCriterion("\"smoke_freq\" =", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqNotEqualTo(String value) {
            addCriterion("\"smoke_freq\" <>", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqGreaterThan(String value) {
            addCriterion("\"smoke_freq\" >", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqGreaterThanOrEqualTo(String value) {
            addCriterion("\"smoke_freq\" >=", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqLessThan(String value) {
            addCriterion("\"smoke_freq\" <", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqLessThanOrEqualTo(String value) {
            addCriterion("\"smoke_freq\" <=", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqLike(String value) {
            addCriterion("\"smoke_freq\" like", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqNotLike(String value) {
            addCriterion("\"smoke_freq\" not like", value, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqIn(List<String> values) {
            addCriterion("\"smoke_freq\" in", values, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqNotIn(List<String> values) {
            addCriterion("\"smoke_freq\" not in", values, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqBetween(String value1, String value2) {
            addCriterion("\"smoke_freq\" between", value1, value2, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeFreqNotBetween(String value1, String value2) {
            addCriterion("\"smoke_freq\" not between", value1, value2, "smokeFreq");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountIsNull() {
            addCriterion("\"smoke_amount\" is null");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountIsNotNull() {
            addCriterion("\"smoke_amount\" is not null");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountEqualTo(String value) {
            addCriterion("\"smoke_amount\" =", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountNotEqualTo(String value) {
            addCriterion("\"smoke_amount\" <>", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountGreaterThan(String value) {
            addCriterion("\"smoke_amount\" >", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountGreaterThanOrEqualTo(String value) {
            addCriterion("\"smoke_amount\" >=", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountLessThan(String value) {
            addCriterion("\"smoke_amount\" <", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountLessThanOrEqualTo(String value) {
            addCriterion("\"smoke_amount\" <=", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountLike(String value) {
            addCriterion("\"smoke_amount\" like", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountNotLike(String value) {
            addCriterion("\"smoke_amount\" not like", value, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountIn(List<String> values) {
            addCriterion("\"smoke_amount\" in", values, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountNotIn(List<String> values) {
            addCriterion("\"smoke_amount\" not in", values, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountBetween(String value1, String value2) {
            addCriterion("\"smoke_amount\" between", value1, value2, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeAmountNotBetween(String value1, String value2) {
            addCriterion("\"smoke_amount\" not between", value1, value2, "smokeAmount");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitIsNull() {
            addCriterion("\"smoke_unit\" is null");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitIsNotNull() {
            addCriterion("\"smoke_unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitEqualTo(String value) {
            addCriterion("\"smoke_unit\" =", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitNotEqualTo(String value) {
            addCriterion("\"smoke_unit\" <>", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitGreaterThan(String value) {
            addCriterion("\"smoke_unit\" >", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"smoke_unit\" >=", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitLessThan(String value) {
            addCriterion("\"smoke_unit\" <", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitLessThanOrEqualTo(String value) {
            addCriterion("\"smoke_unit\" <=", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitLike(String value) {
            addCriterion("\"smoke_unit\" like", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitNotLike(String value) {
            addCriterion("\"smoke_unit\" not like", value, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitIn(List<String> values) {
            addCriterion("\"smoke_unit\" in", values, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitNotIn(List<String> values) {
            addCriterion("\"smoke_unit\" not in", values, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitBetween(String value1, String value2) {
            addCriterion("\"smoke_unit\" between", value1, value2, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andSmokeUnitNotBetween(String value1, String value2) {
            addCriterion("\"smoke_unit\" not between", value1, value2, "smokeUnit");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeIsNull() {
            addCriterion("\"has_quit_smoke\" is null");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeIsNotNull() {
            addCriterion("\"has_quit_smoke\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeEqualTo(String value) {
            addCriterion("\"has_quit_smoke\" =", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeNotEqualTo(String value) {
            addCriterion("\"has_quit_smoke\" <>", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeGreaterThan(String value) {
            addCriterion("\"has_quit_smoke\" >", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_quit_smoke\" >=", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeLessThan(String value) {
            addCriterion("\"has_quit_smoke\" <", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeLessThanOrEqualTo(String value) {
            addCriterion("\"has_quit_smoke\" <=", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeLike(String value) {
            addCriterion("\"has_quit_smoke\" like", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeNotLike(String value) {
            addCriterion("\"has_quit_smoke\" not like", value, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeIn(List<String> values) {
            addCriterion("\"has_quit_smoke\" in", values, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeNotIn(List<String> values) {
            addCriterion("\"has_quit_smoke\" not in", values, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeBetween(String value1, String value2) {
            addCriterion("\"has_quit_smoke\" between", value1, value2, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeNotBetween(String value1, String value2) {
            addCriterion("\"has_quit_smoke\" not between", value1, value2, "hasQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeIsNull() {
            addCriterion("\"has_quit_smoke_time\" is null");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeIsNotNull() {
            addCriterion("\"has_quit_smoke_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeEqualTo(String value) {
            addCriterion("\"has_quit_smoke_time\" =", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeNotEqualTo(String value) {
            addCriterion("\"has_quit_smoke_time\" <>", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeGreaterThan(String value) {
            addCriterion("\"has_quit_smoke_time\" >", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_quit_smoke_time\" >=", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeLessThan(String value) {
            addCriterion("\"has_quit_smoke_time\" <", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeLessThanOrEqualTo(String value) {
            addCriterion("\"has_quit_smoke_time\" <=", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeLike(String value) {
            addCriterion("\"has_quit_smoke_time\" like", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeNotLike(String value) {
            addCriterion("\"has_quit_smoke_time\" not like", value, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeIn(List<String> values) {
            addCriterion("\"has_quit_smoke_time\" in", values, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeNotIn(List<String> values) {
            addCriterion("\"has_quit_smoke_time\" not in", values, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeBetween(String value1, String value2) {
            addCriterion("\"has_quit_smoke_time\" between", value1, value2, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeTimeNotBetween(String value1, String value2) {
            addCriterion("\"has_quit_smoke_time\" not between", value1, value2, "hasQuitSmokeTime");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearIsNull() {
            addCriterion("\"has_quit_smoke_year\" is null");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearIsNotNull() {
            addCriterion("\"has_quit_smoke_year\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearEqualTo(String value) {
            addCriterion("\"has_quit_smoke_year\" =", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearNotEqualTo(String value) {
            addCriterion("\"has_quit_smoke_year\" <>", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearGreaterThan(String value) {
            addCriterion("\"has_quit_smoke_year\" >", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_quit_smoke_year\" >=", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearLessThan(String value) {
            addCriterion("\"has_quit_smoke_year\" <", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearLessThanOrEqualTo(String value) {
            addCriterion("\"has_quit_smoke_year\" <=", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearLike(String value) {
            addCriterion("\"has_quit_smoke_year\" like", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearNotLike(String value) {
            addCriterion("\"has_quit_smoke_year\" not like", value, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearIn(List<String> values) {
            addCriterion("\"has_quit_smoke_year\" in", values, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearNotIn(List<String> values) {
            addCriterion("\"has_quit_smoke_year\" not in", values, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearBetween(String value1, String value2) {
            addCriterion("\"has_quit_smoke_year\" between", value1, value2, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasQuitSmokeYearNotBetween(String value1, String value2) {
            addCriterion("\"has_quit_smoke_year\" not between", value1, value2, "hasQuitSmokeYear");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationIsNull() {
            addCriterion("\"has_menstruation\" is null");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationIsNotNull() {
            addCriterion("\"has_menstruation\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationEqualTo(String value) {
            addCriterion("\"has_menstruation\" =", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationNotEqualTo(String value) {
            addCriterion("\"has_menstruation\" <>", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationGreaterThan(String value) {
            addCriterion("\"has_menstruation\" >", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_menstruation\" >=", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationLessThan(String value) {
            addCriterion("\"has_menstruation\" <", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationLessThanOrEqualTo(String value) {
            addCriterion("\"has_menstruation\" <=", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationLike(String value) {
            addCriterion("\"has_menstruation\" like", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationNotLike(String value) {
            addCriterion("\"has_menstruation\" not like", value, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationIn(List<String> values) {
            addCriterion("\"has_menstruation\" in", values, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationNotIn(List<String> values) {
            addCriterion("\"has_menstruation\" not in", values, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationBetween(String value1, String value2) {
            addCriterion("\"has_menstruation\" between", value1, value2, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andHasMenstruationNotBetween(String value1, String value2) {
            addCriterion("\"has_menstruation\" not between", value1, value2, "hasMenstruation");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeIsNull() {
            addCriterion("\"first_menstruation_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeIsNotNull() {
            addCriterion("\"first_menstruation_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeEqualTo(String value) {
            addCriterion("\"first_menstruation_time\" =", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeNotEqualTo(String value) {
            addCriterion("\"first_menstruation_time\" <>", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeGreaterThan(String value) {
            addCriterion("\"first_menstruation_time\" >", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_menstruation_time\" >=", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeLessThan(String value) {
            addCriterion("\"first_menstruation_time\" <", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeLessThanOrEqualTo(String value) {
            addCriterion("\"first_menstruation_time\" <=", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeLike(String value) {
            addCriterion("\"first_menstruation_time\" like", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeNotLike(String value) {
            addCriterion("\"first_menstruation_time\" not like", value, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeIn(List<String> values) {
            addCriterion("\"first_menstruation_time\" in", values, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeNotIn(List<String> values) {
            addCriterion("\"first_menstruation_time\" not in", values, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeBetween(String value1, String value2) {
            addCriterion("\"first_menstruation_time\" between", value1, value2, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationTimeNotBetween(String value1, String value2) {
            addCriterion("\"first_menstruation_time\" not between", value1, value2, "firstMenstruationTime");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeIsNull() {
            addCriterion("\"first_menstruation_age\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeIsNotNull() {
            addCriterion("\"first_menstruation_age\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeEqualTo(String value) {
            addCriterion("\"first_menstruation_age\" =", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeNotEqualTo(String value) {
            addCriterion("\"first_menstruation_age\" <>", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeGreaterThan(String value) {
            addCriterion("\"first_menstruation_age\" >", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_menstruation_age\" >=", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeLessThan(String value) {
            addCriterion("\"first_menstruation_age\" <", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeLessThanOrEqualTo(String value) {
            addCriterion("\"first_menstruation_age\" <=", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeLike(String value) {
            addCriterion("\"first_menstruation_age\" like", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeNotLike(String value) {
            addCriterion("\"first_menstruation_age\" not like", value, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeIn(List<String> values) {
            addCriterion("\"first_menstruation_age\" in", values, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeNotIn(List<String> values) {
            addCriterion("\"first_menstruation_age\" not in", values, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeBetween(String value1, String value2) {
            addCriterion("\"first_menstruation_age\" between", value1, value2, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andFirstMenstruationAgeNotBetween(String value1, String value2) {
            addCriterion("\"first_menstruation_age\" not between", value1, value2, "firstMenstruationAge");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationIsNull() {
            addCriterion("\"menstruation_duration\" is null");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationIsNotNull() {
            addCriterion("\"menstruation_duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationEqualTo(String value) {
            addCriterion("\"menstruation_duration\" =", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationNotEqualTo(String value) {
            addCriterion("\"menstruation_duration\" <>", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationGreaterThan(String value) {
            addCriterion("\"menstruation_duration\" >", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"menstruation_duration\" >=", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationLessThan(String value) {
            addCriterion("\"menstruation_duration\" <", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationLessThanOrEqualTo(String value) {
            addCriterion("\"menstruation_duration\" <=", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationLike(String value) {
            addCriterion("\"menstruation_duration\" like", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationNotLike(String value) {
            addCriterion("\"menstruation_duration\" not like", value, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationIn(List<String> values) {
            addCriterion("\"menstruation_duration\" in", values, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationNotIn(List<String> values) {
            addCriterion("\"menstruation_duration\" not in", values, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationBetween(String value1, String value2) {
            addCriterion("\"menstruation_duration\" between", value1, value2, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationDurationNotBetween(String value1, String value2) {
            addCriterion("\"menstruation_duration\" not between", value1, value2, "menstruationDuration");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodIsNull() {
            addCriterion("\"menstruation_period\" is null");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodIsNotNull() {
            addCriterion("\"menstruation_period\" is not null");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodEqualTo(String value) {
            addCriterion("\"menstruation_period\" =", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodNotEqualTo(String value) {
            addCriterion("\"menstruation_period\" <>", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodGreaterThan(String value) {
            addCriterion("\"menstruation_period\" >", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("\"menstruation_period\" >=", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodLessThan(String value) {
            addCriterion("\"menstruation_period\" <", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodLessThanOrEqualTo(String value) {
            addCriterion("\"menstruation_period\" <=", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodLike(String value) {
            addCriterion("\"menstruation_period\" like", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodNotLike(String value) {
            addCriterion("\"menstruation_period\" not like", value, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodIn(List<String> values) {
            addCriterion("\"menstruation_period\" in", values, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodNotIn(List<String> values) {
            addCriterion("\"menstruation_period\" not in", values, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodBetween(String value1, String value2) {
            addCriterion("\"menstruation_period\" between", value1, value2, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationPeriodNotBetween(String value1, String value2) {
            addCriterion("\"menstruation_period\" not between", value1, value2, "menstruationPeriod");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountIsNull() {
            addCriterion("\"menstruation_amount\" is null");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountIsNotNull() {
            addCriterion("\"menstruation_amount\" is not null");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountEqualTo(String value) {
            addCriterion("\"menstruation_amount\" =", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountNotEqualTo(String value) {
            addCriterion("\"menstruation_amount\" <>", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountGreaterThan(String value) {
            addCriterion("\"menstruation_amount\" >", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountGreaterThanOrEqualTo(String value) {
            addCriterion("\"menstruation_amount\" >=", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountLessThan(String value) {
            addCriterion("\"menstruation_amount\" <", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountLessThanOrEqualTo(String value) {
            addCriterion("\"menstruation_amount\" <=", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountLike(String value) {
            addCriterion("\"menstruation_amount\" like", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountNotLike(String value) {
            addCriterion("\"menstruation_amount\" not like", value, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountIn(List<String> values) {
            addCriterion("\"menstruation_amount\" in", values, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountNotIn(List<String> values) {
            addCriterion("\"menstruation_amount\" not in", values, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountBetween(String value1, String value2) {
            addCriterion("\"menstruation_amount\" between", value1, value2, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationAmountNotBetween(String value1, String value2) {
            addCriterion("\"menstruation_amount\" not between", value1, value2, "menstruationAmount");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicIsNull() {
            addCriterion("\"menstruation_colic\" is null");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicIsNotNull() {
            addCriterion("\"menstruation_colic\" is not null");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicEqualTo(String value) {
            addCriterion("\"menstruation_colic\" =", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicNotEqualTo(String value) {
            addCriterion("\"menstruation_colic\" <>", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicGreaterThan(String value) {
            addCriterion("\"menstruation_colic\" >", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicGreaterThanOrEqualTo(String value) {
            addCriterion("\"menstruation_colic\" >=", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicLessThan(String value) {
            addCriterion("\"menstruation_colic\" <", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicLessThanOrEqualTo(String value) {
            addCriterion("\"menstruation_colic\" <=", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicLike(String value) {
            addCriterion("\"menstruation_colic\" like", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicNotLike(String value) {
            addCriterion("\"menstruation_colic\" not like", value, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicIn(List<String> values) {
            addCriterion("\"menstruation_colic\" in", values, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicNotIn(List<String> values) {
            addCriterion("\"menstruation_colic\" not in", values, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicBetween(String value1, String value2) {
            addCriterion("\"menstruation_colic\" between", value1, value2, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andMenstruationColicNotBetween(String value1, String value2) {
            addCriterion("\"menstruation_colic\" not between", value1, value2, "menstruationColic");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationIsNull() {
            addCriterion("\"last_menstruation\" is null");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationIsNotNull() {
            addCriterion("\"last_menstruation\" is not null");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationEqualTo(String value) {
            addCriterion("\"last_menstruation\" =", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationNotEqualTo(String value) {
            addCriterion("\"last_menstruation\" <>", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationGreaterThan(String value) {
            addCriterion("\"last_menstruation\" >", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationGreaterThanOrEqualTo(String value) {
            addCriterion("\"last_menstruation\" >=", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationLessThan(String value) {
            addCriterion("\"last_menstruation\" <", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationLessThanOrEqualTo(String value) {
            addCriterion("\"last_menstruation\" <=", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationLike(String value) {
            addCriterion("\"last_menstruation\" like", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationNotLike(String value) {
            addCriterion("\"last_menstruation\" not like", value, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationIn(List<String> values) {
            addCriterion("\"last_menstruation\" in", values, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationNotIn(List<String> values) {
            addCriterion("\"last_menstruation\" not in", values, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationBetween(String value1, String value2) {
            addCriterion("\"last_menstruation\" between", value1, value2, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andLastMenstruationNotBetween(String value1, String value2) {
            addCriterion("\"last_menstruation\" not between", value1, value2, "lastMenstruation");
            return (Criteria) this;
        }

        public Criteria andOverTimeIsNull() {
            addCriterion("\"over_time\" is null");
            return (Criteria) this;
        }

        public Criteria andOverTimeIsNotNull() {
            addCriterion("\"over_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andOverTimeEqualTo(String value) {
            addCriterion("\"over_time\" =", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeNotEqualTo(String value) {
            addCriterion("\"over_time\" <>", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeGreaterThan(String value) {
            addCriterion("\"over_time\" >", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeGreaterThanOrEqualTo(String value) {
            addCriterion("\"over_time\" >=", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeLessThan(String value) {
            addCriterion("\"over_time\" <", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeLessThanOrEqualTo(String value) {
            addCriterion("\"over_time\" <=", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeLike(String value) {
            addCriterion("\"over_time\" like", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeNotLike(String value) {
            addCriterion("\"over_time\" not like", value, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeIn(List<String> values) {
            addCriterion("\"over_time\" in", values, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeNotIn(List<String> values) {
            addCriterion("\"over_time\" not in", values, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeBetween(String value1, String value2) {
            addCriterion("\"over_time\" between", value1, value2, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverTimeNotBetween(String value1, String value2) {
            addCriterion("\"over_time\" not between", value1, value2, "overTime");
            return (Criteria) this;
        }

        public Criteria andOverAgeIsNull() {
            addCriterion("\"over_age\" is null");
            return (Criteria) this;
        }

        public Criteria andOverAgeIsNotNull() {
            addCriterion("\"over_age\" is not null");
            return (Criteria) this;
        }

        public Criteria andOverAgeEqualTo(String value) {
            addCriterion("\"over_age\" =", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeNotEqualTo(String value) {
            addCriterion("\"over_age\" <>", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeGreaterThan(String value) {
            addCriterion("\"over_age\" >", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeGreaterThanOrEqualTo(String value) {
            addCriterion("\"over_age\" >=", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeLessThan(String value) {
            addCriterion("\"over_age\" <", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeLessThanOrEqualTo(String value) {
            addCriterion("\"over_age\" <=", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeLike(String value) {
            addCriterion("\"over_age\" like", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeNotLike(String value) {
            addCriterion("\"over_age\" not like", value, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeIn(List<String> values) {
            addCriterion("\"over_age\" in", values, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeNotIn(List<String> values) {
            addCriterion("\"over_age\" not in", values, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeBetween(String value1, String value2) {
            addCriterion("\"over_age\" between", value1, value2, "overAge");
            return (Criteria) this;
        }

        public Criteria andOverAgeNotBetween(String value1, String value2) {
            addCriterion("\"over_age\" not between", value1, value2, "overAge");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseIsNull() {
            addCriterion("\"is_menopause\" is null");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseIsNotNull() {
            addCriterion("\"is_menopause\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseEqualTo(String value) {
            addCriterion("\"is_menopause\" =", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseNotEqualTo(String value) {
            addCriterion("\"is_menopause\" <>", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseGreaterThan(String value) {
            addCriterion("\"is_menopause\" >", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_menopause\" >=", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseLessThan(String value) {
            addCriterion("\"is_menopause\" <", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseLessThanOrEqualTo(String value) {
            addCriterion("\"is_menopause\" <=", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseLike(String value) {
            addCriterion("\"is_menopause\" like", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseNotLike(String value) {
            addCriterion("\"is_menopause\" not like", value, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseIn(List<String> values) {
            addCriterion("\"is_menopause\" in", values, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseNotIn(List<String> values) {
            addCriterion("\"is_menopause\" not in", values, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseBetween(String value1, String value2) {
            addCriterion("\"is_menopause\" between", value1, value2, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andIsMenopauseNotBetween(String value1, String value2) {
            addCriterion("\"is_menopause\" not between", value1, value2, "isMenopause");
            return (Criteria) this;
        }

        public Criteria andAllergenIsNull() {
            addCriterion("\"allergen\" is null");
            return (Criteria) this;
        }

        public Criteria andAllergenIsNotNull() {
            addCriterion("\"allergen\" is not null");
            return (Criteria) this;
        }

        public Criteria andAllergenEqualTo(String value) {
            addCriterion("\"allergen\" =", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenNotEqualTo(String value) {
            addCriterion("\"allergen\" <>", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenGreaterThan(String value) {
            addCriterion("\"allergen\" >", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenGreaterThanOrEqualTo(String value) {
            addCriterion("\"allergen\" >=", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenLessThan(String value) {
            addCriterion("\"allergen\" <", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenLessThanOrEqualTo(String value) {
            addCriterion("\"allergen\" <=", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenLike(String value) {
            addCriterion("\"allergen\" like", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenNotLike(String value) {
            addCriterion("\"allergen\" not like", value, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenIn(List<String> values) {
            addCriterion("\"allergen\" in", values, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenNotIn(List<String> values) {
            addCriterion("\"allergen\" not in", values, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenBetween(String value1, String value2) {
            addCriterion("\"allergen\" between", value1, value2, "allergen");
            return (Criteria) this;
        }

        public Criteria andAllergenNotBetween(String value1, String value2) {
            addCriterion("\"allergen\" not between", value1, value2, "allergen");
            return (Criteria) this;
        }

        public Criteria andHasAllergyIsNull() {
            addCriterion("\"has_allergy\" is null");
            return (Criteria) this;
        }

        public Criteria andHasAllergyIsNotNull() {
            addCriterion("\"has_allergy\" is not null");
            return (Criteria) this;
        }

        public Criteria andHasAllergyEqualTo(String value) {
            addCriterion("\"has_allergy\" =", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyNotEqualTo(String value) {
            addCriterion("\"has_allergy\" <>", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyGreaterThan(String value) {
            addCriterion("\"has_allergy\" >", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyGreaterThanOrEqualTo(String value) {
            addCriterion("\"has_allergy\" >=", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyLessThan(String value) {
            addCriterion("\"has_allergy\" <", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyLessThanOrEqualTo(String value) {
            addCriterion("\"has_allergy\" <=", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyLike(String value) {
            addCriterion("\"has_allergy\" like", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyNotLike(String value) {
            addCriterion("\"has_allergy\" not like", value, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyIn(List<String> values) {
            addCriterion("\"has_allergy\" in", values, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyNotIn(List<String> values) {
            addCriterion("\"has_allergy\" not in", values, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyBetween(String value1, String value2) {
            addCriterion("\"has_allergy\" between", value1, value2, "hasAllergy");
            return (Criteria) this;
        }

        public Criteria andHasAllergyNotBetween(String value1, String value2) {
            addCriterion("\"has_allergy\" not between", value1, value2, "hasAllergy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}