package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AdmissionRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AdmissionRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeIsNull() {
            addCriterion("\"admission_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeIsNotNull() {
            addCriterion("\"admission_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeEqualTo(Date value) {
            addCriterion("\"admission_datetime\" =", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeNotEqualTo(Date value) {
            addCriterion("\"admission_datetime\" <>", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeGreaterThan(Date value) {
            addCriterion("\"admission_datetime\" >", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"admission_datetime\" >=", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeLessThan(Date value) {
            addCriterion("\"admission_datetime\" <", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"admission_datetime\" <=", value, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeIn(List<Date> values) {
            addCriterion("\"admission_datetime\" in", values, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeNotIn(List<Date> values) {
            addCriterion("\"admission_datetime\" not in", values, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"admission_datetime\" between", value1, value2, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"admission_datetime\" not between", value1, value2, "admissionDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("\"record_time\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("\"record_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("\"record_time\" =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("\"record_time\" <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("\"record_time\" >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("\"record_time\" <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("\"record_time\" in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("\"record_time\" not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintIsNull() {
            addCriterion("\"chief_complaint\" is null");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintIsNotNull() {
            addCriterion("\"chief_complaint\" is not null");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintEqualTo(String value) {
            addCriterion("\"chief_complaint\" =", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintNotEqualTo(String value) {
            addCriterion("\"chief_complaint\" <>", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintGreaterThan(String value) {
            addCriterion("\"chief_complaint\" >", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintGreaterThanOrEqualTo(String value) {
            addCriterion("\"chief_complaint\" >=", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintLessThan(String value) {
            addCriterion("\"chief_complaint\" <", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintLessThanOrEqualTo(String value) {
            addCriterion("\"chief_complaint\" <=", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintLike(String value) {
            addCriterion("\"chief_complaint\" like", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintNotLike(String value) {
            addCriterion("\"chief_complaint\" not like", value, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintIn(List<String> values) {
            addCriterion("\"chief_complaint\" in", values, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintNotIn(List<String> values) {
            addCriterion("\"chief_complaint\" not in", values, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintBetween(String value1, String value2) {
            addCriterion("\"chief_complaint\" between", value1, value2, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andChiefComplaintNotBetween(String value1, String value2) {
            addCriterion("\"chief_complaint\" not between", value1, value2, "chiefComplaint");
            return (Criteria) this;
        }

        public Criteria andHyPresentIsNull() {
            addCriterion("\"hy_present\" is null");
            return (Criteria) this;
        }

        public Criteria andHyPresentIsNotNull() {
            addCriterion("\"hy_present\" is not null");
            return (Criteria) this;
        }

        public Criteria andHyPresentEqualTo(String value) {
            addCriterion("\"hy_present\" =", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentNotEqualTo(String value) {
            addCriterion("\"hy_present\" <>", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentGreaterThan(String value) {
            addCriterion("\"hy_present\" >", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentGreaterThanOrEqualTo(String value) {
            addCriterion("\"hy_present\" >=", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentLessThan(String value) {
            addCriterion("\"hy_present\" <", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentLessThanOrEqualTo(String value) {
            addCriterion("\"hy_present\" <=", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentLike(String value) {
            addCriterion("\"hy_present\" like", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentNotLike(String value) {
            addCriterion("\"hy_present\" not like", value, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentIn(List<String> values) {
            addCriterion("\"hy_present\" in", values, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentNotIn(List<String> values) {
            addCriterion("\"hy_present\" not in", values, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentBetween(String value1, String value2) {
            addCriterion("\"hy_present\" between", value1, value2, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPresentNotBetween(String value1, String value2) {
            addCriterion("\"hy_present\" not between", value1, value2, "hyPresent");
            return (Criteria) this;
        }

        public Criteria andHyPastIsNull() {
            addCriterion("\"hy_past\" is null");
            return (Criteria) this;
        }

        public Criteria andHyPastIsNotNull() {
            addCriterion("\"hy_past\" is not null");
            return (Criteria) this;
        }

        public Criteria andHyPastEqualTo(String value) {
            addCriterion("\"hy_past\" =", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastNotEqualTo(String value) {
            addCriterion("\"hy_past\" <>", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastGreaterThan(String value) {
            addCriterion("\"hy_past\" >", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastGreaterThanOrEqualTo(String value) {
            addCriterion("\"hy_past\" >=", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastLessThan(String value) {
            addCriterion("\"hy_past\" <", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastLessThanOrEqualTo(String value) {
            addCriterion("\"hy_past\" <=", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastLike(String value) {
            addCriterion("\"hy_past\" like", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastNotLike(String value) {
            addCriterion("\"hy_past\" not like", value, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastIn(List<String> values) {
            addCriterion("\"hy_past\" in", values, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastNotIn(List<String> values) {
            addCriterion("\"hy_past\" not in", values, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastBetween(String value1, String value2) {
            addCriterion("\"hy_past\" between", value1, value2, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyPastNotBetween(String value1, String value2) {
            addCriterion("\"hy_past\" not between", value1, value2, "hyPast");
            return (Criteria) this;
        }

        public Criteria andHyIndividualIsNull() {
            addCriterion("\"hy_individual\" is null");
            return (Criteria) this;
        }

        public Criteria andHyIndividualIsNotNull() {
            addCriterion("\"hy_individual\" is not null");
            return (Criteria) this;
        }

        public Criteria andHyIndividualEqualTo(String value) {
            addCriterion("\"hy_individual\" =", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualNotEqualTo(String value) {
            addCriterion("\"hy_individual\" <>", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualGreaterThan(String value) {
            addCriterion("\"hy_individual\" >", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualGreaterThanOrEqualTo(String value) {
            addCriterion("\"hy_individual\" >=", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualLessThan(String value) {
            addCriterion("\"hy_individual\" <", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualLessThanOrEqualTo(String value) {
            addCriterion("\"hy_individual\" <=", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualLike(String value) {
            addCriterion("\"hy_individual\" like", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualNotLike(String value) {
            addCriterion("\"hy_individual\" not like", value, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualIn(List<String> values) {
            addCriterion("\"hy_individual\" in", values, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualNotIn(List<String> values) {
            addCriterion("\"hy_individual\" not in", values, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualBetween(String value1, String value2) {
            addCriterion("\"hy_individual\" between", value1, value2, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyIndividualNotBetween(String value1, String value2) {
            addCriterion("\"hy_individual\" not between", value1, value2, "hyIndividual");
            return (Criteria) this;
        }

        public Criteria andHyFamilyIsNull() {
            addCriterion("\"hy_family\" is null");
            return (Criteria) this;
        }

        public Criteria andHyFamilyIsNotNull() {
            addCriterion("\"hy_family\" is not null");
            return (Criteria) this;
        }

        public Criteria andHyFamilyEqualTo(String value) {
            addCriterion("\"hy_family\" =", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyNotEqualTo(String value) {
            addCriterion("\"hy_family\" <>", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyGreaterThan(String value) {
            addCriterion("\"hy_family\" >", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyGreaterThanOrEqualTo(String value) {
            addCriterion("\"hy_family\" >=", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyLessThan(String value) {
            addCriterion("\"hy_family\" <", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyLessThanOrEqualTo(String value) {
            addCriterion("\"hy_family\" <=", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyLike(String value) {
            addCriterion("\"hy_family\" like", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyNotLike(String value) {
            addCriterion("\"hy_family\" not like", value, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyIn(List<String> values) {
            addCriterion("\"hy_family\" in", values, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyNotIn(List<String> values) {
            addCriterion("\"hy_family\" not in", values, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyBetween(String value1, String value2) {
            addCriterion("\"hy_family\" between", value1, value2, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyFamilyNotBetween(String value1, String value2) {
            addCriterion("\"hy_family\" not between", value1, value2, "hyFamily");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageIsNull() {
            addCriterion("\"hy_menstrual_marriage\" is null");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageIsNotNull() {
            addCriterion("\"hy_menstrual_marriage\" is not null");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageEqualTo(String value) {
            addCriterion("\"hy_menstrual_marriage\" =", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageNotEqualTo(String value) {
            addCriterion("\"hy_menstrual_marriage\" <>", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageGreaterThan(String value) {
            addCriterion("\"hy_menstrual_marriage\" >", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageGreaterThanOrEqualTo(String value) {
            addCriterion("\"hy_menstrual_marriage\" >=", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageLessThan(String value) {
            addCriterion("\"hy_menstrual_marriage\" <", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageLessThanOrEqualTo(String value) {
            addCriterion("\"hy_menstrual_marriage\" <=", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageLike(String value) {
            addCriterion("\"hy_menstrual_marriage\" like", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageNotLike(String value) {
            addCriterion("\"hy_menstrual_marriage\" not like", value, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageIn(List<String> values) {
            addCriterion("\"hy_menstrual_marriage\" in", values, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageNotIn(List<String> values) {
            addCriterion("\"hy_menstrual_marriage\" not in", values, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageBetween(String value1, String value2) {
            addCriterion("\"hy_menstrual_marriage\" between", value1, value2, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andHyMenstrualMarriageNotBetween(String value1, String value2) {
            addCriterion("\"hy_menstrual_marriage\" not between", value1, value2, "hyMenstrualMarriage");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamIsNull() {
            addCriterion("\"physical_exam\" is null");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamIsNotNull() {
            addCriterion("\"physical_exam\" is not null");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamEqualTo(String value) {
            addCriterion("\"physical_exam\" =", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamNotEqualTo(String value) {
            addCriterion("\"physical_exam\" <>", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamGreaterThan(String value) {
            addCriterion("\"physical_exam\" >", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamGreaterThanOrEqualTo(String value) {
            addCriterion("\"physical_exam\" >=", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamLessThan(String value) {
            addCriterion("\"physical_exam\" <", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamLessThanOrEqualTo(String value) {
            addCriterion("\"physical_exam\" <=", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamLike(String value) {
            addCriterion("\"physical_exam\" like", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamNotLike(String value) {
            addCriterion("\"physical_exam\" not like", value, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamIn(List<String> values) {
            addCriterion("\"physical_exam\" in", values, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamNotIn(List<String> values) {
            addCriterion("\"physical_exam\" not in", values, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamBetween(String value1, String value2) {
            addCriterion("\"physical_exam\" between", value1, value2, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andPhysicalExamNotBetween(String value1, String value2) {
            addCriterion("\"physical_exam\" not between", value1, value2, "physicalExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamIsNull() {
            addCriterion("\"speciality_exam\" is null");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamIsNotNull() {
            addCriterion("\"speciality_exam\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamEqualTo(String value) {
            addCriterion("\"speciality_exam\" =", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamNotEqualTo(String value) {
            addCriterion("\"speciality_exam\" <>", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamGreaterThan(String value) {
            addCriterion("\"speciality_exam\" >", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamGreaterThanOrEqualTo(String value) {
            addCriterion("\"speciality_exam\" >=", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamLessThan(String value) {
            addCriterion("\"speciality_exam\" <", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamLessThanOrEqualTo(String value) {
            addCriterion("\"speciality_exam\" <=", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamLike(String value) {
            addCriterion("\"speciality_exam\" like", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamNotLike(String value) {
            addCriterion("\"speciality_exam\" not like", value, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamIn(List<String> values) {
            addCriterion("\"speciality_exam\" in", values, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamNotIn(List<String> values) {
            addCriterion("\"speciality_exam\" not in", values, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamBetween(String value1, String value2) {
            addCriterion("\"speciality_exam\" between", value1, value2, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSpecialityExamNotBetween(String value1, String value2) {
            addCriterion("\"speciality_exam\" not between", value1, value2, "specialityExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamIsNull() {
            addCriterion("\"supplementary_exam\" is null");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamIsNotNull() {
            addCriterion("\"supplementary_exam\" is not null");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamEqualTo(String value) {
            addCriterion("\"supplementary_exam\" =", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamNotEqualTo(String value) {
            addCriterion("\"supplementary_exam\" <>", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamGreaterThan(String value) {
            addCriterion("\"supplementary_exam\" >", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamGreaterThanOrEqualTo(String value) {
            addCriterion("\"supplementary_exam\" >=", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamLessThan(String value) {
            addCriterion("\"supplementary_exam\" <", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamLessThanOrEqualTo(String value) {
            addCriterion("\"supplementary_exam\" <=", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamLike(String value) {
            addCriterion("\"supplementary_exam\" like", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamNotLike(String value) {
            addCriterion("\"supplementary_exam\" not like", value, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamIn(List<String> values) {
            addCriterion("\"supplementary_exam\" in", values, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamNotIn(List<String> values) {
            addCriterion("\"supplementary_exam\" not in", values, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamBetween(String value1, String value2) {
            addCriterion("\"supplementary_exam\" between", value1, value2, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andSupplementaryExamNotBetween(String value1, String value2) {
            addCriterion("\"supplementary_exam\" not between", value1, value2, "supplementaryExam");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNull() {
            addCriterion("\"temperature\" is null");
            return (Criteria) this;
        }

        public Criteria andTemperatureIsNotNull() {
            addCriterion("\"temperature\" is not null");
            return (Criteria) this;
        }

        public Criteria andTemperatureEqualTo(Double value) {
            addCriterion("\"temperature\" =", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotEqualTo(Double value) {
            addCriterion("\"temperature\" <>", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThan(Double value) {
            addCriterion("\"temperature\" >", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureGreaterThanOrEqualTo(Double value) {
            addCriterion("\"temperature\" >=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThan(Double value) {
            addCriterion("\"temperature\" <", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureLessThanOrEqualTo(Double value) {
            addCriterion("\"temperature\" <=", value, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureIn(List<Double> values) {
            addCriterion("\"temperature\" in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotIn(List<Double> values) {
            addCriterion("\"temperature\" not in", values, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureBetween(Double value1, Double value2) {
            addCriterion("\"temperature\" between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andTemperatureNotBetween(Double value1, Double value2) {
            addCriterion("\"temperature\" not between", value1, value2, "temperature");
            return (Criteria) this;
        }

        public Criteria andPulseRateIsNull() {
            addCriterion("\"pulse_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andPulseRateIsNotNull() {
            addCriterion("\"pulse_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andPulseRateEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" =", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateNotEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" <>", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateGreaterThan(Integer value) {
            addCriterion("\"pulse_rate\" >", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" >=", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateLessThan(Integer value) {
            addCriterion("\"pulse_rate\" <", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateLessThanOrEqualTo(Integer value) {
            addCriterion("\"pulse_rate\" <=", value, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateIn(List<Integer> values) {
            addCriterion("\"pulse_rate\" in", values, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateNotIn(List<Integer> values) {
            addCriterion("\"pulse_rate\" not in", values, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateBetween(Integer value1, Integer value2) {
            addCriterion("\"pulse_rate\" between", value1, value2, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andPulseRateNotBetween(Integer value1, Integer value2) {
            addCriterion("\"pulse_rate\" not between", value1, value2, "pulseRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateIsNull() {
            addCriterion("\"respiratory_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateIsNotNull() {
            addCriterion("\"respiratory_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" =", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateNotEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" <>", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateGreaterThan(Integer value) {
            addCriterion("\"respiratory_rate\" >", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" >=", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateLessThan(Integer value) {
            addCriterion("\"respiratory_rate\" <", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateLessThanOrEqualTo(Integer value) {
            addCriterion("\"respiratory_rate\" <=", value, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateIn(List<Integer> values) {
            addCriterion("\"respiratory_rate\" in", values, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateNotIn(List<Integer> values) {
            addCriterion("\"respiratory_rate\" not in", values, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateBetween(Integer value1, Integer value2) {
            addCriterion("\"respiratory_rate\" between", value1, value2, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andRespiratoryRateNotBetween(Integer value1, Integer value2) {
            addCriterion("\"respiratory_rate\" not between", value1, value2, "respiratoryRate");
            return (Criteria) this;
        }

        public Criteria andBloodPressureIsNull() {
            addCriterion("\"blood_pressure\" is null");
            return (Criteria) this;
        }

        public Criteria andBloodPressureIsNotNull() {
            addCriterion("\"blood_pressure\" is not null");
            return (Criteria) this;
        }

        public Criteria andBloodPressureEqualTo(String value) {
            addCriterion("\"blood_pressure\" =", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureNotEqualTo(String value) {
            addCriterion("\"blood_pressure\" <>", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureGreaterThan(String value) {
            addCriterion("\"blood_pressure\" >", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureGreaterThanOrEqualTo(String value) {
            addCriterion("\"blood_pressure\" >=", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureLessThan(String value) {
            addCriterion("\"blood_pressure\" <", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureLessThanOrEqualTo(String value) {
            addCriterion("\"blood_pressure\" <=", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureLike(String value) {
            addCriterion("\"blood_pressure\" like", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureNotLike(String value) {
            addCriterion("\"blood_pressure\" not like", value, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureIn(List<String> values) {
            addCriterion("\"blood_pressure\" in", values, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureNotIn(List<String> values) {
            addCriterion("\"blood_pressure\" not in", values, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureBetween(String value1, String value2) {
            addCriterion("\"blood_pressure\" between", value1, value2, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andBloodPressureNotBetween(String value1, String value2) {
            addCriterion("\"blood_pressure\" not between", value1, value2, "bloodPressure");
            return (Criteria) this;
        }

        public Criteria andHeartRateIsNull() {
            addCriterion("\"heart_rate\" is null");
            return (Criteria) this;
        }

        public Criteria andHeartRateIsNotNull() {
            addCriterion("\"heart_rate\" is not null");
            return (Criteria) this;
        }

        public Criteria andHeartRateEqualTo(Integer value) {
            addCriterion("\"heart_rate\" =", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateNotEqualTo(Integer value) {
            addCriterion("\"heart_rate\" <>", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateGreaterThan(Integer value) {
            addCriterion("\"heart_rate\" >", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"heart_rate\" >=", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateLessThan(Integer value) {
            addCriterion("\"heart_rate\" <", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateLessThanOrEqualTo(Integer value) {
            addCriterion("\"heart_rate\" <=", value, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateIn(List<Integer> values) {
            addCriterion("\"heart_rate\" in", values, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateNotIn(List<Integer> values) {
            addCriterion("\"heart_rate\" not in", values, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateBetween(Integer value1, Integer value2) {
            addCriterion("\"heart_rate\" between", value1, value2, "heartRate");
            return (Criteria) this;
        }

        public Criteria andHeartRateNotBetween(Integer value1, Integer value2) {
            addCriterion("\"heart_rate\" not between", value1, value2, "heartRate");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("\"weight\" is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("\"weight\" is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(Double value) {
            addCriterion("\"weight\" =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(Double value) {
            addCriterion("\"weight\" <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(Double value) {
            addCriterion("\"weight\" >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("\"weight\" >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(Double value) {
            addCriterion("\"weight\" <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(Double value) {
            addCriterion("\"weight\" <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<Double> values) {
            addCriterion("\"weight\" in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<Double> values) {
            addCriterion("\"weight\" not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(Double value1, Double value2) {
            addCriterion("\"weight\" between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(Double value1, Double value2) {
            addCriterion("\"weight\" not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomIsNull() {
            addCriterion("\"pmh_positive_symptom\" is null");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomIsNotNull() {
            addCriterion("\"pmh_positive_symptom\" is not null");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomEqualTo(Object value) {
            addCriterion("\"pmh_positive_symptom\" =", value, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomNotEqualTo(Object value) {
            addCriterion("\"pmh_positive_symptom\" <>", value, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomGreaterThan(Object value) {
            addCriterion("\"pmh_positive_symptom\" >", value, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomGreaterThanOrEqualTo(Object value) {
            addCriterion("\"pmh_positive_symptom\" >=", value, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomLessThan(Object value) {
            addCriterion("\"pmh_positive_symptom\" <", value, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomLessThanOrEqualTo(Object value) {
            addCriterion("\"pmh_positive_symptom\" <=", value, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomIn(List<Object> values) {
            addCriterion("\"pmh_positive_symptom\" in", values, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomNotIn(List<Object> values) {
            addCriterion("\"pmh_positive_symptom\" not in", values, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomBetween(Object value1, Object value2) {
            addCriterion("\"pmh_positive_symptom\" between", value1, value2, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPmhPositiveSymptomNotBetween(Object value1, Object value2) {
            addCriterion("\"pmh_positive_symptom\" not between", value1, value2, "pmhPositiveSymptom");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryIsNull() {
            addCriterion("\"past_medical_history\" is null");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryIsNotNull() {
            addCriterion("\"past_medical_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryEqualTo(Object value) {
            addCriterion("\"past_medical_history\" =", value, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryNotEqualTo(Object value) {
            addCriterion("\"past_medical_history\" <>", value, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryGreaterThan(Object value) {
            addCriterion("\"past_medical_history\" >", value, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryGreaterThanOrEqualTo(Object value) {
            addCriterion("\"past_medical_history\" >=", value, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryLessThan(Object value) {
            addCriterion("\"past_medical_history\" <", value, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryLessThanOrEqualTo(Object value) {
            addCriterion("\"past_medical_history\" <=", value, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryIn(List<Object> values) {
            addCriterion("\"past_medical_history\" in", values, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryNotIn(List<Object> values) {
            addCriterion("\"past_medical_history\" not in", values, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryBetween(Object value1, Object value2) {
            addCriterion("\"past_medical_history\" between", value1, value2, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andPastMedicalHistoryNotBetween(Object value1, Object value2) {
            addCriterion("\"past_medical_history\" not between", value1, value2, "pastMedicalHistory");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyIsNull() {
            addCriterion("\"is_isa_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyIsNotNull() {
            addCriterion("\"is_isa_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" =", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyNotEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" <>", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyGreaterThan(Boolean value) {
            addCriterion("\"is_isa_therapy\" >", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" >=", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyLessThan(Boolean value) {
            addCriterion("\"is_isa_therapy\" <", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" <=", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyIn(List<Boolean> values) {
            addCriterion("\"is_isa_therapy\" in", values, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyNotIn(List<Boolean> values) {
            addCriterion("\"is_isa_therapy\" not in", values, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_isa_therapy\" between", value1, value2, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_isa_therapy\" not between", value1, value2, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyIsNull() {
            addCriterion("\"is_hormone_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyIsNotNull() {
            addCriterion("\"is_hormone_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" =", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyNotEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" <>", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyGreaterThan(Boolean value) {
            addCriterion("\"is_hormone_therapy\" >", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" >=", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyLessThan(Boolean value) {
            addCriterion("\"is_hormone_therapy\" <", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" <=", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyIn(List<Boolean> values) {
            addCriterion("\"is_hormone_therapy\" in", values, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyNotIn(List<Boolean> values) {
            addCriterion("\"is_hormone_therapy\" not in", values, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_hormone_therapy\" between", value1, value2, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_hormone_therapy\" not between", value1, value2, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryIsNull() {
            addCriterion("\"is_tuberculosis_history\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryIsNotNull() {
            addCriterion("\"is_tuberculosis_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryEqualTo(Boolean value) {
            addCriterion("\"is_tuberculosis_history\" =", value, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryNotEqualTo(Boolean value) {
            addCriterion("\"is_tuberculosis_history\" <>", value, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryGreaterThan(Boolean value) {
            addCriterion("\"is_tuberculosis_history\" >", value, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_tuberculosis_history\" >=", value, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryLessThan(Boolean value) {
            addCriterion("\"is_tuberculosis_history\" <", value, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_tuberculosis_history\" <=", value, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryIn(List<Boolean> values) {
            addCriterion("\"is_tuberculosis_history\" in", values, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryNotIn(List<Boolean> values) {
            addCriterion("\"is_tuberculosis_history\" not in", values, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_tuberculosis_history\" between", value1, value2, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsTuberculosisHistoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_tuberculosis_history\" not between", value1, value2, "isTuberculosisHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryIsNull() {
            addCriterion("\"is_drug_allergy_history\" is null");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryIsNotNull() {
            addCriterion("\"is_drug_allergy_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" =", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryNotEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" <>", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryGreaterThan(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" >", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" >=", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryLessThan(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" <", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" <=", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryIn(List<Boolean> values) {
            addCriterion("\"is_drug_allergy_history\" in", values, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryNotIn(List<Boolean> values) {
            addCriterion("\"is_drug_allergy_history\" not in", values, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_drug_allergy_history\" between", value1, value2, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_drug_allergy_history\" not between", value1, value2, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameIsNull() {
            addCriterion("\"allergy_drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameIsNotNull() {
            addCriterion("\"allergy_drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameEqualTo(String value) {
            addCriterion("\"allergy_drug_name\" =", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotEqualTo(String value) {
            addCriterion("\"allergy_drug_name\" <>", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameGreaterThan(String value) {
            addCriterion("\"allergy_drug_name\" >", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"allergy_drug_name\" >=", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameLessThan(String value) {
            addCriterion("\"allergy_drug_name\" <", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameLessThanOrEqualTo(String value) {
            addCriterion("\"allergy_drug_name\" <=", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameLike(String value) {
            addCriterion("\"allergy_drug_name\" like", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotLike(String value) {
            addCriterion("\"allergy_drug_name\" not like", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameIn(List<String> values) {
            addCriterion("\"allergy_drug_name\" in", values, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotIn(List<String> values) {
            addCriterion("\"allergy_drug_name\" not in", values, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameBetween(String value1, String value2) {
            addCriterion("\"allergy_drug_name\" between", value1, value2, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotBetween(String value1, String value2) {
            addCriterion("\"allergy_drug_name\" not between", value1, value2, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsSmokeIsNull() {
            addCriterion("\"is_smoke\" is null");
            return (Criteria) this;
        }

        public Criteria andIsSmokeIsNotNull() {
            addCriterion("\"is_smoke\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsSmokeEqualTo(Boolean value) {
            addCriterion("\"is_smoke\" =", value, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeNotEqualTo(Boolean value) {
            addCriterion("\"is_smoke\" <>", value, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeGreaterThan(Boolean value) {
            addCriterion("\"is_smoke\" >", value, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_smoke\" >=", value, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeLessThan(Boolean value) {
            addCriterion("\"is_smoke\" <", value, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_smoke\" <=", value, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeIn(List<Boolean> values) {
            addCriterion("\"is_smoke\" in", values, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeNotIn(List<Boolean> values) {
            addCriterion("\"is_smoke\" not in", values, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_smoke\" between", value1, value2, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andIsSmokeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_smoke\" not between", value1, value2, "isSmoke");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationIsNull() {
            addCriterion("\"somke_duration\" is null");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationIsNotNull() {
            addCriterion("\"somke_duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationEqualTo(String value) {
            addCriterion("\"somke_duration\" =", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationNotEqualTo(String value) {
            addCriterion("\"somke_duration\" <>", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationGreaterThan(String value) {
            addCriterion("\"somke_duration\" >", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"somke_duration\" >=", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationLessThan(String value) {
            addCriterion("\"somke_duration\" <", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationLessThanOrEqualTo(String value) {
            addCriterion("\"somke_duration\" <=", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationLike(String value) {
            addCriterion("\"somke_duration\" like", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationNotLike(String value) {
            addCriterion("\"somke_duration\" not like", value, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationIn(List<String> values) {
            addCriterion("\"somke_duration\" in", values, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationNotIn(List<String> values) {
            addCriterion("\"somke_duration\" not in", values, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationBetween(String value1, String value2) {
            addCriterion("\"somke_duration\" between", value1, value2, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andSomkeDurationNotBetween(String value1, String value2) {
            addCriterion("\"somke_duration\" not between", value1, value2, "somkeDuration");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberIsNull() {
            addCriterion("\"daily_somke_number\" is null");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberIsNotNull() {
            addCriterion("\"daily_somke_number\" is not null");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberEqualTo(String value) {
            addCriterion("\"daily_somke_number\" =", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberNotEqualTo(String value) {
            addCriterion("\"daily_somke_number\" <>", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberGreaterThan(String value) {
            addCriterion("\"daily_somke_number\" >", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberGreaterThanOrEqualTo(String value) {
            addCriterion("\"daily_somke_number\" >=", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberLessThan(String value) {
            addCriterion("\"daily_somke_number\" <", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberLessThanOrEqualTo(String value) {
            addCriterion("\"daily_somke_number\" <=", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberLike(String value) {
            addCriterion("\"daily_somke_number\" like", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberNotLike(String value) {
            addCriterion("\"daily_somke_number\" not like", value, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberIn(List<String> values) {
            addCriterion("\"daily_somke_number\" in", values, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberNotIn(List<String> values) {
            addCriterion("\"daily_somke_number\" not in", values, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberBetween(String value1, String value2) {
            addCriterion("\"daily_somke_number\" between", value1, value2, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andDailySomkeNumberNotBetween(String value1, String value2) {
            addCriterion("\"daily_somke_number\" not between", value1, value2, "dailySomkeNumber");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeIsNull() {
            addCriterion("\"is_quit_smoke\" is null");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeIsNotNull() {
            addCriterion("\"is_quit_smoke\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeEqualTo(Boolean value) {
            addCriterion("\"is_quit_smoke\" =", value, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeNotEqualTo(Boolean value) {
            addCriterion("\"is_quit_smoke\" <>", value, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeGreaterThan(Boolean value) {
            addCriterion("\"is_quit_smoke\" >", value, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_quit_smoke\" >=", value, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeLessThan(Boolean value) {
            addCriterion("\"is_quit_smoke\" <", value, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_quit_smoke\" <=", value, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeIn(List<Boolean> values) {
            addCriterion("\"is_quit_smoke\" in", values, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeNotIn(List<Boolean> values) {
            addCriterion("\"is_quit_smoke\" not in", values, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_quit_smoke\" between", value1, value2, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsQuitSmokeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_quit_smoke\" not between", value1, value2, "isQuitSmoke");
            return (Criteria) this;
        }

        public Criteria andIsDrinkIsNull() {
            addCriterion("\"is_drink\" is null");
            return (Criteria) this;
        }

        public Criteria andIsDrinkIsNotNull() {
            addCriterion("\"is_drink\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsDrinkEqualTo(Boolean value) {
            addCriterion("\"is_drink\" =", value, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkNotEqualTo(Boolean value) {
            addCriterion("\"is_drink\" <>", value, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkGreaterThan(Boolean value) {
            addCriterion("\"is_drink\" >", value, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_drink\" >=", value, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkLessThan(Boolean value) {
            addCriterion("\"is_drink\" <", value, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_drink\" <=", value, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkIn(List<Boolean> values) {
            addCriterion("\"is_drink\" in", values, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkNotIn(List<Boolean> values) {
            addCriterion("\"is_drink\" not in", values, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_drink\" between", value1, value2, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDrinkNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_drink\" not between", value1, value2, "isDrink");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryIsNull() {
            addCriterion("\"is_dust_exposure_history\" is null");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryIsNotNull() {
            addCriterion("\"is_dust_exposure_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" =", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryNotEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" <>", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryGreaterThan(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" >", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" >=", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryLessThan(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" <", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" <=", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryIn(List<Boolean> values) {
            addCriterion("\"is_dust_exposure_history\" in", values, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryNotIn(List<Boolean> values) {
            addCriterion("\"is_dust_exposure_history\" not in", values, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_dust_exposure_history\" between", value1, value2, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_dust_exposure_history\" not between", value1, value2, "isDustExposureHistory");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}