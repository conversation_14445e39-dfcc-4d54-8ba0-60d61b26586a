package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.FirstCourseRecord;
import com.haoys.xinjiang.model.FirstCourseRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FirstCourseRecordMapper {
    long countByExample(FirstCourseRecordExample example);

    int deleteByExample(FirstCourseRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(FirstCourseRecord record);

    int insertSelective(FirstCourseRecord record);

    List<FirstCourseRecord> selectByExample(FirstCourseRecordExample example);

    FirstCourseRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") FirstCourseRecord record, @Param("example") FirstCourseRecordExample example);

    int updateByExample(@Param("record") FirstCourseRecord record, @Param("example") FirstCourseRecordExample example);

    int updateByPrimaryKeySelective(FirstCourseRecord record);

    int updateByPrimaryKey(FirstCourseRecord record);
}