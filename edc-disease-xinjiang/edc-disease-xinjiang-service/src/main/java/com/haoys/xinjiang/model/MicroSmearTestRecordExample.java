package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MicroSmearTestRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MicroSmearTestRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andLabResultIdIsNull() {
            addCriterion("\"lab_result_id\" is null");
            return (Criteria) this;
        }

        public Criteria andLabResultIdIsNotNull() {
            addCriterion("\"lab_result_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andLabResultIdEqualTo(String value) {
            addCriterion("\"lab_result_id\" =", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdNotEqualTo(String value) {
            addCriterion("\"lab_result_id\" <>", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdGreaterThan(String value) {
            addCriterion("\"lab_result_id\" >", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"lab_result_id\" >=", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdLessThan(String value) {
            addCriterion("\"lab_result_id\" <", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdLessThanOrEqualTo(String value) {
            addCriterion("\"lab_result_id\" <=", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdLike(String value) {
            addCriterion("\"lab_result_id\" like", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdNotLike(String value) {
            addCriterion("\"lab_result_id\" not like", value, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdIn(List<String> values) {
            addCriterion("\"lab_result_id\" in", values, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdNotIn(List<String> values) {
            addCriterion("\"lab_result_id\" not in", values, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdBetween(String value1, String value2) {
            addCriterion("\"lab_result_id\" between", value1, value2, "labResultId");
            return (Criteria) this;
        }

        public Criteria andLabResultIdNotBetween(String value1, String value2) {
            addCriterion("\"lab_result_id\" not between", value1, value2, "labResultId");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeIsNull() {
            addCriterion("\"exam_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeIsNotNull() {
            addCriterion("\"exam_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeEqualTo(Date value) {
            addCriterion("\"exam_datetime\" =", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeNotEqualTo(Date value) {
            addCriterion("\"exam_datetime\" <>", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeGreaterThan(Date value) {
            addCriterion("\"exam_datetime\" >", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"exam_datetime\" >=", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeLessThan(Date value) {
            addCriterion("\"exam_datetime\" <", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"exam_datetime\" <=", value, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeIn(List<Date> values) {
            addCriterion("\"exam_datetime\" in", values, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeNotIn(List<Date> values) {
            addCriterion("\"exam_datetime\" not in", values, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"exam_datetime\" between", value1, value2, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andExamDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"exam_datetime\" not between", value1, value2, "examDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeIsNull() {
            addCriterion("\"report_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeIsNotNull() {
            addCriterion("\"report_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeEqualTo(Date value) {
            addCriterion("\"report_datetime\" =", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeNotEqualTo(Date value) {
            addCriterion("\"report_datetime\" <>", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeGreaterThan(Date value) {
            addCriterion("\"report_datetime\" >", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"report_datetime\" >=", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeLessThan(Date value) {
            addCriterion("\"report_datetime\" <", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"report_datetime\" <=", value, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeIn(List<Date> values) {
            addCriterion("\"report_datetime\" in", values, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeNotIn(List<Date> values) {
            addCriterion("\"report_datetime\" not in", values, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"report_datetime\" between", value1, value2, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andReportDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"report_datetime\" not between", value1, value2, "reportDatetime");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIsNull() {
            addCriterion("\"test_group_items\" is null");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIsNotNull() {
            addCriterion("\"test_group_items\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsEqualTo(String value) {
            addCriterion("\"test_group_items\" =", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotEqualTo(String value) {
            addCriterion("\"test_group_items\" <>", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsGreaterThan(String value) {
            addCriterion("\"test_group_items\" >", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_group_items\" >=", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLessThan(String value) {
            addCriterion("\"test_group_items\" <", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLessThanOrEqualTo(String value) {
            addCriterion("\"test_group_items\" <=", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsLike(String value) {
            addCriterion("\"test_group_items\" like", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotLike(String value) {
            addCriterion("\"test_group_items\" not like", value, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsIn(List<String> values) {
            addCriterion("\"test_group_items\" in", values, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotIn(List<String> values) {
            addCriterion("\"test_group_items\" not in", values, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsBetween(String value1, String value2) {
            addCriterion("\"test_group_items\" between", value1, value2, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andTestGroupItemsNotBetween(String value1, String value2) {
            addCriterion("\"test_group_items\" not between", value1, value2, "testGroupItems");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIsNull() {
            addCriterion("\"spcm_type\" is null");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIsNotNull() {
            addCriterion("\"spcm_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeEqualTo(String value) {
            addCriterion("\"spcm_type\" =", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotEqualTo(String value) {
            addCriterion("\"spcm_type\" <>", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeGreaterThan(String value) {
            addCriterion("\"spcm_type\" >", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"spcm_type\" >=", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLessThan(String value) {
            addCriterion("\"spcm_type\" <", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLessThanOrEqualTo(String value) {
            addCriterion("\"spcm_type\" <=", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeLike(String value) {
            addCriterion("\"spcm_type\" like", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotLike(String value) {
            addCriterion("\"spcm_type\" not like", value, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeIn(List<String> values) {
            addCriterion("\"spcm_type\" in", values, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotIn(List<String> values) {
            addCriterion("\"spcm_type\" not in", values, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeBetween(String value1, String value2) {
            addCriterion("\"spcm_type\" between", value1, value2, "spcmType");
            return (Criteria) this;
        }

        public Criteria andSpcmTypeNotBetween(String value1, String value2) {
            addCriterion("\"spcm_type\" not between", value1, value2, "spcmType");
            return (Criteria) this;
        }

        public Criteria andTestMethodIsNull() {
            addCriterion("\"test_method\" is null");
            return (Criteria) this;
        }

        public Criteria andTestMethodIsNotNull() {
            addCriterion("\"test_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andTestMethodEqualTo(String value) {
            addCriterion("\"test_method\" =", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotEqualTo(String value) {
            addCriterion("\"test_method\" <>", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodGreaterThan(String value) {
            addCriterion("\"test_method\" >", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"test_method\" >=", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLessThan(String value) {
            addCriterion("\"test_method\" <", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLessThanOrEqualTo(String value) {
            addCriterion("\"test_method\" <=", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodLike(String value) {
            addCriterion("\"test_method\" like", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotLike(String value) {
            addCriterion("\"test_method\" not like", value, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodIn(List<String> values) {
            addCriterion("\"test_method\" in", values, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotIn(List<String> values) {
            addCriterion("\"test_method\" not in", values, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodBetween(String value1, String value2) {
            addCriterion("\"test_method\" between", value1, value2, "testMethod");
            return (Criteria) this;
        }

        public Criteria andTestMethodNotBetween(String value1, String value2) {
            addCriterion("\"test_method\" not between", value1, value2, "testMethod");
            return (Criteria) this;
        }

        public Criteria andResultIsNull() {
            addCriterion("\"result\" is null");
            return (Criteria) this;
        }

        public Criteria andResultIsNotNull() {
            addCriterion("\"result\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultEqualTo(String value) {
            addCriterion("\"result\" =", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotEqualTo(String value) {
            addCriterion("\"result\" <>", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThan(String value) {
            addCriterion("\"result\" >", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultGreaterThanOrEqualTo(String value) {
            addCriterion("\"result\" >=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThan(String value) {
            addCriterion("\"result\" <", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLessThanOrEqualTo(String value) {
            addCriterion("\"result\" <=", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultLike(String value) {
            addCriterion("\"result\" like", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotLike(String value) {
            addCriterion("\"result\" not like", value, "result");
            return (Criteria) this;
        }

        public Criteria andResultIn(List<String> values) {
            addCriterion("\"result\" in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotIn(List<String> values) {
            addCriterion("\"result\" not in", values, "result");
            return (Criteria) this;
        }

        public Criteria andResultBetween(String value1, String value2) {
            addCriterion("\"result\" between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultNotBetween(String value1, String value2) {
            addCriterion("\"result\" not between", value1, value2, "result");
            return (Criteria) this;
        }

        public Criteria andResultTextIsNull() {
            addCriterion("\"result_text\" is null");
            return (Criteria) this;
        }

        public Criteria andResultTextIsNotNull() {
            addCriterion("\"result_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultTextEqualTo(String value) {
            addCriterion("\"result_text\" =", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotEqualTo(String value) {
            addCriterion("\"result_text\" <>", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextGreaterThan(String value) {
            addCriterion("\"result_text\" >", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"result_text\" >=", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextLessThan(String value) {
            addCriterion("\"result_text\" <", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextLessThanOrEqualTo(String value) {
            addCriterion("\"result_text\" <=", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextLike(String value) {
            addCriterion("\"result_text\" like", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotLike(String value) {
            addCriterion("\"result_text\" not like", value, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextIn(List<String> values) {
            addCriterion("\"result_text\" in", values, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotIn(List<String> values) {
            addCriterion("\"result_text\" not in", values, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextBetween(String value1, String value2) {
            addCriterion("\"result_text\" between", value1, value2, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultTextNotBetween(String value1, String value2) {
            addCriterion("\"result_text\" not between", value1, value2, "resultText");
            return (Criteria) this;
        }

        public Criteria andResultNumIsNull() {
            addCriterion("\"result_num\" is null");
            return (Criteria) this;
        }

        public Criteria andResultNumIsNotNull() {
            addCriterion("\"result_num\" is not null");
            return (Criteria) this;
        }

        public Criteria andResultNumEqualTo(Double value) {
            addCriterion("\"result_num\" =", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumNotEqualTo(Double value) {
            addCriterion("\"result_num\" <>", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumGreaterThan(Double value) {
            addCriterion("\"result_num\" >", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumGreaterThanOrEqualTo(Double value) {
            addCriterion("\"result_num\" >=", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumLessThan(Double value) {
            addCriterion("\"result_num\" <", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumLessThanOrEqualTo(Double value) {
            addCriterion("\"result_num\" <=", value, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumIn(List<Double> values) {
            addCriterion("\"result_num\" in", values, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumNotIn(List<Double> values) {
            addCriterion("\"result_num\" not in", values, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumBetween(Double value1, Double value2) {
            addCriterion("\"result_num\" between", value1, value2, "resultNum");
            return (Criteria) this;
        }

        public Criteria andResultNumNotBetween(Double value1, Double value2) {
            addCriterion("\"result_num\" not between", value1, value2, "resultNum");
            return (Criteria) this;
        }

        public Criteria andUnitsIsNull() {
            addCriterion("\"units\" is null");
            return (Criteria) this;
        }

        public Criteria andUnitsIsNotNull() {
            addCriterion("\"units\" is not null");
            return (Criteria) this;
        }

        public Criteria andUnitsEqualTo(String value) {
            addCriterion("\"units\" =", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotEqualTo(String value) {
            addCriterion("\"units\" <>", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsGreaterThan(String value) {
            addCriterion("\"units\" >", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsGreaterThanOrEqualTo(String value) {
            addCriterion("\"units\" >=", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsLessThan(String value) {
            addCriterion("\"units\" <", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsLessThanOrEqualTo(String value) {
            addCriterion("\"units\" <=", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsLike(String value) {
            addCriterion("\"units\" like", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotLike(String value) {
            addCriterion("\"units\" not like", value, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsIn(List<String> values) {
            addCriterion("\"units\" in", values, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotIn(List<String> values) {
            addCriterion("\"units\" not in", values, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsBetween(String value1, String value2) {
            addCriterion("\"units\" between", value1, value2, "units");
            return (Criteria) this;
        }

        public Criteria andUnitsNotBetween(String value1, String value2) {
            addCriterion("\"units\" not between", value1, value2, "units");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNull() {
            addCriterion("\"item_name\" is null");
            return (Criteria) this;
        }

        public Criteria andItemNameIsNotNull() {
            addCriterion("\"item_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andItemNameEqualTo(String value) {
            addCriterion("\"item_name\" =", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotEqualTo(String value) {
            addCriterion("\"item_name\" <>", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThan(String value) {
            addCriterion("\"item_name\" >", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"item_name\" >=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThan(String value) {
            addCriterion("\"item_name\" <", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLessThanOrEqualTo(String value) {
            addCriterion("\"item_name\" <=", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameLike(String value) {
            addCriterion("\"item_name\" like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotLike(String value) {
            addCriterion("\"item_name\" not like", value, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameIn(List<String> values) {
            addCriterion("\"item_name\" in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotIn(List<String> values) {
            addCriterion("\"item_name\" not in", values, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameBetween(String value1, String value2) {
            addCriterion("\"item_name\" between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andItemNameNotBetween(String value1, String value2) {
            addCriterion("\"item_name\" not between", value1, value2, "itemName");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}