package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FirstCourseRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public FirstCourseRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeIsNull() {
            addCriterion("\"record_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeIsNotNull() {
            addCriterion("\"record_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeEqualTo(Date value) {
            addCriterion("\"record_datetime\" =", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeNotEqualTo(Date value) {
            addCriterion("\"record_datetime\" <>", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeGreaterThan(Date value) {
            addCriterion("\"record_datetime\" >", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_datetime\" >=", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeLessThan(Date value) {
            addCriterion("\"record_datetime\" <", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_datetime\" <=", value, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeIn(List<Date> values) {
            addCriterion("\"record_datetime\" in", values, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeNotIn(List<Date> values) {
            addCriterion("\"record_datetime\" not in", values, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"record_datetime\" between", value1, value2, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andRecordDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_datetime\" not between", value1, value2, "recordDatetime");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesIsNull() {
            addCriterion("\"case_features\" is null");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesIsNotNull() {
            addCriterion("\"case_features\" is not null");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesEqualTo(String value) {
            addCriterion("\"case_features\" =", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesNotEqualTo(String value) {
            addCriterion("\"case_features\" <>", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesGreaterThan(String value) {
            addCriterion("\"case_features\" >", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesGreaterThanOrEqualTo(String value) {
            addCriterion("\"case_features\" >=", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesLessThan(String value) {
            addCriterion("\"case_features\" <", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesLessThanOrEqualTo(String value) {
            addCriterion("\"case_features\" <=", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesLike(String value) {
            addCriterion("\"case_features\" like", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesNotLike(String value) {
            addCriterion("\"case_features\" not like", value, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesIn(List<String> values) {
            addCriterion("\"case_features\" in", values, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesNotIn(List<String> values) {
            addCriterion("\"case_features\" not in", values, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesBetween(String value1, String value2) {
            addCriterion("\"case_features\" between", value1, value2, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andCaseFeaturesNotBetween(String value1, String value2) {
            addCriterion("\"case_features\" not between", value1, value2, "caseFeatures");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisIsNull() {
            addCriterion("\"pre_diagnosis\" is null");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisIsNotNull() {
            addCriterion("\"pre_diagnosis\" is not null");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisEqualTo(String value) {
            addCriterion("\"pre_diagnosis\" =", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisNotEqualTo(String value) {
            addCriterion("\"pre_diagnosis\" <>", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisGreaterThan(String value) {
            addCriterion("\"pre_diagnosis\" >", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisGreaterThanOrEqualTo(String value) {
            addCriterion("\"pre_diagnosis\" >=", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisLessThan(String value) {
            addCriterion("\"pre_diagnosis\" <", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisLessThanOrEqualTo(String value) {
            addCriterion("\"pre_diagnosis\" <=", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisLike(String value) {
            addCriterion("\"pre_diagnosis\" like", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisNotLike(String value) {
            addCriterion("\"pre_diagnosis\" not like", value, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisIn(List<String> values) {
            addCriterion("\"pre_diagnosis\" in", values, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisNotIn(List<String> values) {
            addCriterion("\"pre_diagnosis\" not in", values, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisBetween(String value1, String value2) {
            addCriterion("\"pre_diagnosis\" between", value1, value2, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andPreDiagnosisNotBetween(String value1, String value2) {
            addCriterion("\"pre_diagnosis\" not between", value1, value2, "preDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisIsNull() {
            addCriterion("\"diagnosis_basis\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisIsNotNull() {
            addCriterion("\"diagnosis_basis\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisEqualTo(String value) {
            addCriterion("\"diagnosis_basis\" =", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisNotEqualTo(String value) {
            addCriterion("\"diagnosis_basis\" <>", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisGreaterThan(String value) {
            addCriterion("\"diagnosis_basis\" >", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisGreaterThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_basis\" >=", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisLessThan(String value) {
            addCriterion("\"diagnosis_basis\" <", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisLessThanOrEqualTo(String value) {
            addCriterion("\"diagnosis_basis\" <=", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisLike(String value) {
            addCriterion("\"diagnosis_basis\" like", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisNotLike(String value) {
            addCriterion("\"diagnosis_basis\" not like", value, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisIn(List<String> values) {
            addCriterion("\"diagnosis_basis\" in", values, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisNotIn(List<String> values) {
            addCriterion("\"diagnosis_basis\" not in", values, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisBetween(String value1, String value2) {
            addCriterion("\"diagnosis_basis\" between", value1, value2, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDiagnosisBasisNotBetween(String value1, String value2) {
            addCriterion("\"diagnosis_basis\" not between", value1, value2, "diagnosisBasis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisIsNull() {
            addCriterion("\"differential_diagnosis\" is null");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisIsNotNull() {
            addCriterion("\"differential_diagnosis\" is not null");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisEqualTo(String value) {
            addCriterion("\"differential_diagnosis\" =", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisNotEqualTo(String value) {
            addCriterion("\"differential_diagnosis\" <>", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisGreaterThan(String value) {
            addCriterion("\"differential_diagnosis\" >", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisGreaterThanOrEqualTo(String value) {
            addCriterion("\"differential_diagnosis\" >=", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisLessThan(String value) {
            addCriterion("\"differential_diagnosis\" <", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisLessThanOrEqualTo(String value) {
            addCriterion("\"differential_diagnosis\" <=", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisLike(String value) {
            addCriterion("\"differential_diagnosis\" like", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisNotLike(String value) {
            addCriterion("\"differential_diagnosis\" not like", value, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisIn(List<String> values) {
            addCriterion("\"differential_diagnosis\" in", values, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisNotIn(List<String> values) {
            addCriterion("\"differential_diagnosis\" not in", values, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisBetween(String value1, String value2) {
            addCriterion("\"differential_diagnosis\" between", value1, value2, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andDifferentialDiagnosisNotBetween(String value1, String value2) {
            addCriterion("\"differential_diagnosis\" not between", value1, value2, "differentialDiagnosis");
            return (Criteria) this;
        }

        public Criteria andAssessPlanIsNull() {
            addCriterion("\"assess_plan\" is null");
            return (Criteria) this;
        }

        public Criteria andAssessPlanIsNotNull() {
            addCriterion("\"assess_plan\" is not null");
            return (Criteria) this;
        }

        public Criteria andAssessPlanEqualTo(String value) {
            addCriterion("\"assess_plan\" =", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanNotEqualTo(String value) {
            addCriterion("\"assess_plan\" <>", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanGreaterThan(String value) {
            addCriterion("\"assess_plan\" >", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanGreaterThanOrEqualTo(String value) {
            addCriterion("\"assess_plan\" >=", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanLessThan(String value) {
            addCriterion("\"assess_plan\" <", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanLessThanOrEqualTo(String value) {
            addCriterion("\"assess_plan\" <=", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanLike(String value) {
            addCriterion("\"assess_plan\" like", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanNotLike(String value) {
            addCriterion("\"assess_plan\" not like", value, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanIn(List<String> values) {
            addCriterion("\"assess_plan\" in", values, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanNotIn(List<String> values) {
            addCriterion("\"assess_plan\" not in", values, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanBetween(String value1, String value2) {
            addCriterion("\"assess_plan\" between", value1, value2, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andAssessPlanNotBetween(String value1, String value2) {
            addCriterion("\"assess_plan\" not between", value1, value2, "assessPlan");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andPkidIsNull() {
            addCriterion("\"pkid\" is null");
            return (Criteria) this;
        }

        public Criteria andPkidIsNotNull() {
            addCriterion("\"pkid\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkidEqualTo(String value) {
            addCriterion("\"pkid\" =", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotEqualTo(String value) {
            addCriterion("\"pkid\" <>", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThan(String value) {
            addCriterion("\"pkid\" >", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidGreaterThanOrEqualTo(String value) {
            addCriterion("\"pkid\" >=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThan(String value) {
            addCriterion("\"pkid\" <", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLessThanOrEqualTo(String value) {
            addCriterion("\"pkid\" <=", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidLike(String value) {
            addCriterion("\"pkid\" like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotLike(String value) {
            addCriterion("\"pkid\" not like", value, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidIn(List<String> values) {
            addCriterion("\"pkid\" in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotIn(List<String> values) {
            addCriterion("\"pkid\" not in", values, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidBetween(String value1, String value2) {
            addCriterion("\"pkid\" between", value1, value2, "pkid");
            return (Criteria) this;
        }

        public Criteria andPkidNotBetween(String value1, String value2) {
            addCriterion("\"pkid\" not between", value1, value2, "pkid");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}