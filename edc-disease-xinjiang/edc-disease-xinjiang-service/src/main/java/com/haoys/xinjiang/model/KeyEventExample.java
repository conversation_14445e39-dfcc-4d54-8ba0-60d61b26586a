package com.haoys.xinjiang.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class KeyEventExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public KeyEventExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("\"id\" is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("\"id\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("\"id\" =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("\"id\" <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("\"id\" >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"id\" >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("\"id\" <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("\"id\" <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("\"id\" in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("\"id\" not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("\"id\" not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeIsNull() {
            addCriterion("\"first_visit_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeIsNotNull() {
            addCriterion("\"first_visit_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeEqualTo(Date value) {
            addCriterion("\"first_visit_datetime\" =", value, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeNotEqualTo(Date value) {
            addCriterion("\"first_visit_datetime\" <>", value, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeGreaterThan(Date value) {
            addCriterion("\"first_visit_datetime\" >", value, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_visit_datetime\" >=", value, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeLessThan(Date value) {
            addCriterion("\"first_visit_datetime\" <", value, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_visit_datetime\" <=", value, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeIn(List<Date> values) {
            addCriterion("\"first_visit_datetime\" in", values, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeNotIn(List<Date> values) {
            addCriterion("\"first_visit_datetime\" not in", values, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"first_visit_datetime\" between", value1, value2, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_visit_datetime\" not between", value1, value2, "firstVisitDatetime");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeIsNull() {
            addCriterion("\"first_visit_age\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeIsNotNull() {
            addCriterion("\"first_visit_age\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeEqualTo(Integer value) {
            addCriterion("\"first_visit_age\" =", value, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeNotEqualTo(Integer value) {
            addCriterion("\"first_visit_age\" <>", value, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeGreaterThan(Integer value) {
            addCriterion("\"first_visit_age\" >", value, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"first_visit_age\" >=", value, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeLessThan(Integer value) {
            addCriterion("\"first_visit_age\" <", value, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeLessThanOrEqualTo(Integer value) {
            addCriterion("\"first_visit_age\" <=", value, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeIn(List<Integer> values) {
            addCriterion("\"first_visit_age\" in", values, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeNotIn(List<Integer> values) {
            addCriterion("\"first_visit_age\" not in", values, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeBetween(Integer value1, Integer value2) {
            addCriterion("\"first_visit_age\" between", value1, value2, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("\"first_visit_age\" not between", value1, value2, "firstVisitAge");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptIsNull() {
            addCriterion("\"first_visit_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptIsNotNull() {
            addCriterion("\"first_visit_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptEqualTo(String value) {
            addCriterion("\"first_visit_dept\" =", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptNotEqualTo(String value) {
            addCriterion("\"first_visit_dept\" <>", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptGreaterThan(String value) {
            addCriterion("\"first_visit_dept\" >", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_visit_dept\" >=", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptLessThan(String value) {
            addCriterion("\"first_visit_dept\" <", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptLessThanOrEqualTo(String value) {
            addCriterion("\"first_visit_dept\" <=", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptLike(String value) {
            addCriterion("\"first_visit_dept\" like", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptNotLike(String value) {
            addCriterion("\"first_visit_dept\" not like", value, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptIn(List<String> values) {
            addCriterion("\"first_visit_dept\" in", values, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptNotIn(List<String> values) {
            addCriterion("\"first_visit_dept\" not in", values, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptBetween(String value1, String value2) {
            addCriterion("\"first_visit_dept\" between", value1, value2, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDeptNotBetween(String value1, String value2) {
            addCriterion("\"first_visit_dept\" not between", value1, value2, "firstVisitDept");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorIsNull() {
            addCriterion("\"first_visit_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorIsNotNull() {
            addCriterion("\"first_visit_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorEqualTo(String value) {
            addCriterion("\"first_visit_doctor\" =", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorNotEqualTo(String value) {
            addCriterion("\"first_visit_doctor\" <>", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorGreaterThan(String value) {
            addCriterion("\"first_visit_doctor\" >", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_visit_doctor\" >=", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorLessThan(String value) {
            addCriterion("\"first_visit_doctor\" <", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"first_visit_doctor\" <=", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorLike(String value) {
            addCriterion("\"first_visit_doctor\" like", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorNotLike(String value) {
            addCriterion("\"first_visit_doctor\" not like", value, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorIn(List<String> values) {
            addCriterion("\"first_visit_doctor\" in", values, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorNotIn(List<String> values) {
            addCriterion("\"first_visit_doctor\" not in", values, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorBetween(String value1, String value2) {
            addCriterion("\"first_visit_doctor\" between", value1, value2, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstVisitDoctorNotBetween(String value1, String value2) {
            addCriterion("\"first_visit_doctor\" not between", value1, value2, "firstVisitDoctor");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeIsNull() {
            addCriterion("\"disease_time\" is null");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeIsNotNull() {
            addCriterion("\"disease_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeEqualTo(Date value) {
            addCriterion("\"disease_time\" =", value, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeNotEqualTo(Date value) {
            addCriterion("\"disease_time\" <>", value, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeGreaterThan(Date value) {
            addCriterion("\"disease_time\" >", value, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"disease_time\" >=", value, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeLessThan(Date value) {
            addCriterion("\"disease_time\" <", value, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"disease_time\" <=", value, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeIn(List<Date> values) {
            addCriterion("\"disease_time\" in", values, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeNotIn(List<Date> values) {
            addCriterion("\"disease_time\" not in", values, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeBetween(Date value1, Date value2) {
            addCriterion("\"disease_time\" between", value1, value2, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andDiseaseTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"disease_time\" not between", value1, value2, "diseaseTime");
            return (Criteria) this;
        }

        public Criteria andSymptomSignIsNull() {
            addCriterion("\"symptom_sign\" is null");
            return (Criteria) this;
        }

        public Criteria andSymptomSignIsNotNull() {
            addCriterion("\"symptom_sign\" is not null");
            return (Criteria) this;
        }

        public Criteria andSymptomSignEqualTo(Object value) {
            addCriterion("\"symptom_sign\" =", value, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignNotEqualTo(Object value) {
            addCriterion("\"symptom_sign\" <>", value, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignGreaterThan(Object value) {
            addCriterion("\"symptom_sign\" >", value, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignGreaterThanOrEqualTo(Object value) {
            addCriterion("\"symptom_sign\" >=", value, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignLessThan(Object value) {
            addCriterion("\"symptom_sign\" <", value, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignLessThanOrEqualTo(Object value) {
            addCriterion("\"symptom_sign\" <=", value, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignIn(List<Object> values) {
            addCriterion("\"symptom_sign\" in", values, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignNotIn(List<Object> values) {
            addCriterion("\"symptom_sign\" not in", values, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignBetween(Object value1, Object value2) {
            addCriterion("\"symptom_sign\" between", value1, value2, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andSymptomSignNotBetween(Object value1, Object value2) {
            addCriterion("\"symptom_sign\" not between", value1, value2, "symptomSign");
            return (Criteria) this;
        }

        public Criteria andIsComplicationIsNull() {
            addCriterion("\"is_complication\" is null");
            return (Criteria) this;
        }

        public Criteria andIsComplicationIsNotNull() {
            addCriterion("\"is_complication\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsComplicationEqualTo(Boolean value) {
            addCriterion("\"is_complication\" =", value, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationNotEqualTo(Boolean value) {
            addCriterion("\"is_complication\" <>", value, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationGreaterThan(Boolean value) {
            addCriterion("\"is_complication\" >", value, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_complication\" >=", value, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationLessThan(Boolean value) {
            addCriterion("\"is_complication\" <", value, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_complication\" <=", value, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationIn(List<Boolean> values) {
            addCriterion("\"is_complication\" in", values, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationNotIn(List<Boolean> values) {
            addCriterion("\"is_complication\" not in", values, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_complication\" between", value1, value2, "isComplication");
            return (Criteria) this;
        }

        public Criteria andIsComplicationNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_complication\" not between", value1, value2, "isComplication");
            return (Criteria) this;
        }

        public Criteria andComplicationIsNull() {
            addCriterion("\"complication\" is null");
            return (Criteria) this;
        }

        public Criteria andComplicationIsNotNull() {
            addCriterion("\"complication\" is not null");
            return (Criteria) this;
        }

        public Criteria andComplicationEqualTo(Object value) {
            addCriterion("\"complication\" =", value, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationNotEqualTo(Object value) {
            addCriterion("\"complication\" <>", value, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationGreaterThan(Object value) {
            addCriterion("\"complication\" >", value, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationGreaterThanOrEqualTo(Object value) {
            addCriterion("\"complication\" >=", value, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationLessThan(Object value) {
            addCriterion("\"complication\" <", value, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationLessThanOrEqualTo(Object value) {
            addCriterion("\"complication\" <=", value, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationIn(List<Object> values) {
            addCriterion("\"complication\" in", values, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationNotIn(List<Object> values) {
            addCriterion("\"complication\" not in", values, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationBetween(Object value1, Object value2) {
            addCriterion("\"complication\" between", value1, value2, "complication");
            return (Criteria) this;
        }

        public Criteria andComplicationNotBetween(Object value1, Object value2) {
            addCriterion("\"complication\" not between", value1, value2, "complication");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeIsNull() {
            addCriterion("\"tb_therapy_type\" is null");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeIsNotNull() {
            addCriterion("\"tb_therapy_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeEqualTo(String value) {
            addCriterion("\"tb_therapy_type\" =", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeNotEqualTo(String value) {
            addCriterion("\"tb_therapy_type\" <>", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeGreaterThan(String value) {
            addCriterion("\"tb_therapy_type\" >", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_therapy_type\" >=", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeLessThan(String value) {
            addCriterion("\"tb_therapy_type\" <", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeLessThanOrEqualTo(String value) {
            addCriterion("\"tb_therapy_type\" <=", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeLike(String value) {
            addCriterion("\"tb_therapy_type\" like", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeNotLike(String value) {
            addCriterion("\"tb_therapy_type\" not like", value, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeIn(List<String> values) {
            addCriterion("\"tb_therapy_type\" in", values, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeNotIn(List<String> values) {
            addCriterion("\"tb_therapy_type\" not in", values, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeBetween(String value1, String value2) {
            addCriterion("\"tb_therapy_type\" between", value1, value2, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andTbTherapyTypeNotBetween(String value1, String value2) {
            addCriterion("\"tb_therapy_type\" not between", value1, value2, "tbTherapyType");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyIsNull() {
            addCriterion("\"is_antituberculosis_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyIsNotNull() {
            addCriterion("\"is_antituberculosis_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyEqualTo(Boolean value) {
            addCriterion("\"is_antituberculosis_therapy\" =", value, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyNotEqualTo(Boolean value) {
            addCriterion("\"is_antituberculosis_therapy\" <>", value, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyGreaterThan(Boolean value) {
            addCriterion("\"is_antituberculosis_therapy\" >", value, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_antituberculosis_therapy\" >=", value, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyLessThan(Boolean value) {
            addCriterion("\"is_antituberculosis_therapy\" <", value, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_antituberculosis_therapy\" <=", value, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyIn(List<Boolean> values) {
            addCriterion("\"is_antituberculosis_therapy\" in", values, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyNotIn(List<Boolean> values) {
            addCriterion("\"is_antituberculosis_therapy\" not in", values, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_antituberculosis_therapy\" between", value1, value2, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andIsAntituberculosisTherapyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_antituberculosis_therapy\" not between", value1, value2, "isAntituberculosisTherapy");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameIsNull() {
            addCriterion("\"antituberculosis_therapy_drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameIsNotNull() {
            addCriterion("\"antituberculosis_therapy_drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameEqualTo(Object value) {
            addCriterion("\"antituberculosis_therapy_drug_name\" =", value, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameNotEqualTo(Object value) {
            addCriterion("\"antituberculosis_therapy_drug_name\" <>", value, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameGreaterThan(Object value) {
            addCriterion("\"antituberculosis_therapy_drug_name\" >", value, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameGreaterThanOrEqualTo(Object value) {
            addCriterion("\"antituberculosis_therapy_drug_name\" >=", value, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameLessThan(Object value) {
            addCriterion("\"antituberculosis_therapy_drug_name\" <", value, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameLessThanOrEqualTo(Object value) {
            addCriterion("\"antituberculosis_therapy_drug_name\" <=", value, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameIn(List<Object> values) {
            addCriterion("\"antituberculosis_therapy_drug_name\" in", values, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameNotIn(List<Object> values) {
            addCriterion("\"antituberculosis_therapy_drug_name\" not in", values, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameBetween(Object value1, Object value2) {
            addCriterion("\"antituberculosis_therapy_drug_name\" between", value1, value2, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andAntituberculosisTherapyDrugNameNotBetween(Object value1, Object value2) {
            addCriterion("\"antituberculosis_therapy_drug_name\" not between", value1, value2, "antituberculosisTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyIsNull() {
            addCriterion("\"fist_antituberculosis_drug_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyIsNotNull() {
            addCriterion("\"fist_antituberculosis_drug_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyEqualTo(Date value) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" =", value, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyNotEqualTo(Date value) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" <>", value, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyGreaterThan(Date value) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" >", value, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyGreaterThanOrEqualTo(Date value) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" >=", value, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyLessThan(Date value) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" <", value, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyLessThanOrEqualTo(Date value) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" <=", value, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyIn(List<Date> values) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" in", values, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyNotIn(List<Date> values) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" not in", values, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyBetween(Date value1, Date value2) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" between", value1, value2, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andFistAntituberculosisDrugTherapyNotBetween(Date value1, Date value2) {
            addCriterion("\"fist_antituberculosis_drug_therapy\" not between", value1, value2, "fistAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyIsNull() {
            addCriterion("\"last_antituberculosis_drug_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyIsNotNull() {
            addCriterion("\"last_antituberculosis_drug_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyEqualTo(Date value) {
            addCriterion("\"last_antituberculosis_drug_therapy\" =", value, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyNotEqualTo(Date value) {
            addCriterion("\"last_antituberculosis_drug_therapy\" <>", value, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyGreaterThan(Date value) {
            addCriterion("\"last_antituberculosis_drug_therapy\" >", value, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyGreaterThanOrEqualTo(Date value) {
            addCriterion("\"last_antituberculosis_drug_therapy\" >=", value, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyLessThan(Date value) {
            addCriterion("\"last_antituberculosis_drug_therapy\" <", value, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyLessThanOrEqualTo(Date value) {
            addCriterion("\"last_antituberculosis_drug_therapy\" <=", value, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyIn(List<Date> values) {
            addCriterion("\"last_antituberculosis_drug_therapy\" in", values, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyNotIn(List<Date> values) {
            addCriterion("\"last_antituberculosis_drug_therapy\" not in", values, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyBetween(Date value1, Date value2) {
            addCriterion("\"last_antituberculosis_drug_therapy\" between", value1, value2, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andLastAntituberculosisDrugTherapyNotBetween(Date value1, Date value2) {
            addCriterion("\"last_antituberculosis_drug_therapy\" not between", value1, value2, "lastAntituberculosisDrugTherapy");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayIsNull() {
            addCriterion("\"tb_patient_discovery_way\" is null");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayIsNotNull() {
            addCriterion("\"tb_patient_discovery_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayEqualTo(String value) {
            addCriterion("\"tb_patient_discovery_way\" =", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayNotEqualTo(String value) {
            addCriterion("\"tb_patient_discovery_way\" <>", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayGreaterThan(String value) {
            addCriterion("\"tb_patient_discovery_way\" >", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_patient_discovery_way\" >=", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayLessThan(String value) {
            addCriterion("\"tb_patient_discovery_way\" <", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayLessThanOrEqualTo(String value) {
            addCriterion("\"tb_patient_discovery_way\" <=", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayLike(String value) {
            addCriterion("\"tb_patient_discovery_way\" like", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayNotLike(String value) {
            addCriterion("\"tb_patient_discovery_way\" not like", value, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayIn(List<String> values) {
            addCriterion("\"tb_patient_discovery_way\" in", values, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayNotIn(List<String> values) {
            addCriterion("\"tb_patient_discovery_way\" not in", values, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayBetween(String value1, String value2) {
            addCriterion("\"tb_patient_discovery_way\" between", value1, value2, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andTbPatientDiscoveryWayNotBetween(String value1, String value2) {
            addCriterion("\"tb_patient_discovery_way\" not between", value1, value2, "tbPatientDiscoveryWay");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveIsNull() {
            addCriterion("\"is_tb_smear_positive\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveIsNotNull() {
            addCriterion("\"is_tb_smear_positive\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveEqualTo(String value) {
            addCriterion("\"is_tb_smear_positive\" =", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveNotEqualTo(String value) {
            addCriterion("\"is_tb_smear_positive\" <>", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveGreaterThan(String value) {
            addCriterion("\"is_tb_smear_positive\" >", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_smear_positive\" >=", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveLessThan(String value) {
            addCriterion("\"is_tb_smear_positive\" <", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_smear_positive\" <=", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveLike(String value) {
            addCriterion("\"is_tb_smear_positive\" like", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveNotLike(String value) {
            addCriterion("\"is_tb_smear_positive\" not like", value, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveIn(List<String> values) {
            addCriterion("\"is_tb_smear_positive\" in", values, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveNotIn(List<String> values) {
            addCriterion("\"is_tb_smear_positive\" not in", values, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveBetween(String value1, String value2) {
            addCriterion("\"is_tb_smear_positive\" between", value1, value2, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSmearPositiveNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_smear_positive\" not between", value1, value2, "isTbSmearPositive");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeIsNull() {
            addCriterion("\"first_tb_smear_positive_exam_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeIsNotNull() {
            addCriterion("\"first_tb_smear_positive_exam_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeEqualTo(Date value) {
            addCriterion("\"first_tb_smear_positive_exam_time\" =", value, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeNotEqualTo(Date value) {
            addCriterion("\"first_tb_smear_positive_exam_time\" <>", value, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeGreaterThan(Date value) {
            addCriterion("\"first_tb_smear_positive_exam_time\" >", value, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_smear_positive_exam_time\" >=", value, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeLessThan(Date value) {
            addCriterion("\"first_tb_smear_positive_exam_time\" <", value, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_smear_positive_exam_time\" <=", value, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeIn(List<Date> values) {
            addCriterion("\"first_tb_smear_positive_exam_time\" in", values, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeNotIn(List<Date> values) {
            addCriterion("\"first_tb_smear_positive_exam_time\" not in", values, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_smear_positive_exam_time\" between", value1, value2, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSmearPositiveExamTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_smear_positive_exam_time\" not between", value1, value2, "firstTbSmearPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveIsNull() {
            addCriterion("\"is_tb_culture_positive\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveIsNotNull() {
            addCriterion("\"is_tb_culture_positive\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveEqualTo(String value) {
            addCriterion("\"is_tb_culture_positive\" =", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveNotEqualTo(String value) {
            addCriterion("\"is_tb_culture_positive\" <>", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveGreaterThan(String value) {
            addCriterion("\"is_tb_culture_positive\" >", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_culture_positive\" >=", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveLessThan(String value) {
            addCriterion("\"is_tb_culture_positive\" <", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_culture_positive\" <=", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveLike(String value) {
            addCriterion("\"is_tb_culture_positive\" like", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveNotLike(String value) {
            addCriterion("\"is_tb_culture_positive\" not like", value, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveIn(List<String> values) {
            addCriterion("\"is_tb_culture_positive\" in", values, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveNotIn(List<String> values) {
            addCriterion("\"is_tb_culture_positive\" not in", values, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveBetween(String value1, String value2) {
            addCriterion("\"is_tb_culture_positive\" between", value1, value2, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andIsTbCulturePositiveNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_culture_positive\" not between", value1, value2, "isTbCulturePositive");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeIsNull() {
            addCriterion("\"first_tb_culture_positive_exam_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeIsNotNull() {
            addCriterion("\"first_tb_culture_positive_exam_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeEqualTo(Date value) {
            addCriterion("\"first_tb_culture_positive_exam_time\" =", value, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeNotEqualTo(Date value) {
            addCriterion("\"first_tb_culture_positive_exam_time\" <>", value, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeGreaterThan(Date value) {
            addCriterion("\"first_tb_culture_positive_exam_time\" >", value, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_culture_positive_exam_time\" >=", value, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeLessThan(Date value) {
            addCriterion("\"first_tb_culture_positive_exam_time\" <", value, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_culture_positive_exam_time\" <=", value, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeIn(List<Date> values) {
            addCriterion("\"first_tb_culture_positive_exam_time\" in", values, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeNotIn(List<Date> values) {
            addCriterion("\"first_tb_culture_positive_exam_time\" not in", values, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_culture_positive_exam_time\" between", value1, value2, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbCulturePositiveExamTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_culture_positive_exam_time\" not between", value1, value2, "firstTbCulturePositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveIsNull() {
            addCriterion("\"is_tb_antibody_positive\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveIsNotNull() {
            addCriterion("\"is_tb_antibody_positive\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveEqualTo(String value) {
            addCriterion("\"is_tb_antibody_positive\" =", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveNotEqualTo(String value) {
            addCriterion("\"is_tb_antibody_positive\" <>", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveGreaterThan(String value) {
            addCriterion("\"is_tb_antibody_positive\" >", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_antibody_positive\" >=", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveLessThan(String value) {
            addCriterion("\"is_tb_antibody_positive\" <", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_antibody_positive\" <=", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveLike(String value) {
            addCriterion("\"is_tb_antibody_positive\" like", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveNotLike(String value) {
            addCriterion("\"is_tb_antibody_positive\" not like", value, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveIn(List<String> values) {
            addCriterion("\"is_tb_antibody_positive\" in", values, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveNotIn(List<String> values) {
            addCriterion("\"is_tb_antibody_positive\" not in", values, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveBetween(String value1, String value2) {
            addCriterion("\"is_tb_antibody_positive\" between", value1, value2, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbAntibodyPositiveNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_antibody_positive\" not between", value1, value2, "isTbAntibodyPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveIsNull() {
            addCriterion("\"is_tb_nucleic_acid_positive\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveIsNotNull() {
            addCriterion("\"is_tb_nucleic_acid_positive\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveEqualTo(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" =", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveNotEqualTo(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" <>", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveGreaterThan(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" >", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" >=", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveLessThan(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" <", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" <=", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveLike(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" like", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveNotLike(String value) {
            addCriterion("\"is_tb_nucleic_acid_positive\" not like", value, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveIn(List<String> values) {
            addCriterion("\"is_tb_nucleic_acid_positive\" in", values, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveNotIn(List<String> values) {
            addCriterion("\"is_tb_nucleic_acid_positive\" not in", values, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveBetween(String value1, String value2) {
            addCriterion("\"is_tb_nucleic_acid_positive\" between", value1, value2, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbNucleicAcidPositiveNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_nucleic_acid_positive\" not between", value1, value2, "isTbNucleicAcidPositive");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeIsNull() {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeIsNotNull() {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeEqualTo(Date value) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" =", value, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeNotEqualTo(Date value) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" <>", value, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeGreaterThan(Date value) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" >", value, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" >=", value, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeLessThan(Date value) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" <", value, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" <=", value, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeIn(List<Date> values) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" in", values, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeNotIn(List<Date> values) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" not in", values, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" between", value1, value2, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbNucleicAcidPositiveExamTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_nucleic_acid_positive_exam_time\" not between", value1, value2, "firstTbNucleicAcidPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeIsNull() {
            addCriterion("\"first_tb_antibody_positive_exam_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeIsNotNull() {
            addCriterion("\"first_tb_antibody_positive_exam_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeEqualTo(Date value) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" =", value, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeNotEqualTo(Date value) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" <>", value, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeGreaterThan(Date value) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" >", value, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" >=", value, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeLessThan(Date value) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" <", value, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" <=", value, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeIn(List<Date> values) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" in", values, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeNotIn(List<Date> values) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" not in", values, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" between", value1, value2, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbAntibodyPositiveExamTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_antibody_positive_exam_time\" not between", value1, value2, "firstTbAntibodyPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveIsNull() {
            addCriterion("\"is_tb_infection_t_cell_positive\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveIsNotNull() {
            addCriterion("\"is_tb_infection_t_cell_positive\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveEqualTo(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" =", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveNotEqualTo(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" <>", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveGreaterThan(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" >", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" >=", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveLessThan(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" <", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" <=", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveLike(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" like", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveNotLike(String value) {
            addCriterion("\"is_tb_infection_t_cell_positive\" not like", value, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveIn(List<String> values) {
            addCriterion("\"is_tb_infection_t_cell_positive\" in", values, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveNotIn(List<String> values) {
            addCriterion("\"is_tb_infection_t_cell_positive\" not in", values, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveBetween(String value1, String value2) {
            addCriterion("\"is_tb_infection_t_cell_positive\" between", value1, value2, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbInfectionTCellPositiveNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_infection_t_cell_positive\" not between", value1, value2, "isTbInfectionTCellPositive");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeIsNull() {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeIsNotNull() {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeEqualTo(Date value) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" =", value, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeNotEqualTo(Date value) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" <>", value, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeGreaterThan(Date value) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" >", value, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" >=", value, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeLessThan(Date value) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" <", value, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" <=", value, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeIn(List<Date> values) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" in", values, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeNotIn(List<Date> values) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" not in", values, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" between", value1, value2, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbInfectionTCellPositiveExamTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_infection_t_cell_positive_exam_time\" not between", value1, value2, "firstTbInfectionTCellPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveIsNull() {
            addCriterion("\"is_tb_skin_positive\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveIsNotNull() {
            addCriterion("\"is_tb_skin_positive\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveEqualTo(String value) {
            addCriterion("\"is_tb_skin_positive\" =", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveNotEqualTo(String value) {
            addCriterion("\"is_tb_skin_positive\" <>", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveGreaterThan(String value) {
            addCriterion("\"is_tb_skin_positive\" >", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_skin_positive\" >=", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveLessThan(String value) {
            addCriterion("\"is_tb_skin_positive\" <", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_skin_positive\" <=", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveLike(String value) {
            addCriterion("\"is_tb_skin_positive\" like", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveNotLike(String value) {
            addCriterion("\"is_tb_skin_positive\" not like", value, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveIn(List<String> values) {
            addCriterion("\"is_tb_skin_positive\" in", values, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveNotIn(List<String> values) {
            addCriterion("\"is_tb_skin_positive\" not in", values, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveBetween(String value1, String value2) {
            addCriterion("\"is_tb_skin_positive\" between", value1, value2, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andIsTbSkinPositiveNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_skin_positive\" not between", value1, value2, "isTbSkinPositive");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeIsNull() {
            addCriterion("\"first_tb_skin_positive_exam_time\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeIsNotNull() {
            addCriterion("\"first_tb_skin_positive_exam_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeEqualTo(Date value) {
            addCriterion("\"first_tb_skin_positive_exam_time\" =", value, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeNotEqualTo(Date value) {
            addCriterion("\"first_tb_skin_positive_exam_time\" <>", value, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeGreaterThan(Date value) {
            addCriterion("\"first_tb_skin_positive_exam_time\" >", value, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_skin_positive_exam_time\" >=", value, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeLessThan(Date value) {
            addCriterion("\"first_tb_skin_positive_exam_time\" <", value, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"first_tb_skin_positive_exam_time\" <=", value, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeIn(List<Date> values) {
            addCriterion("\"first_tb_skin_positive_exam_time\" in", values, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeNotIn(List<Date> values) {
            addCriterion("\"first_tb_skin_positive_exam_time\" not in", values, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_skin_positive_exam_time\" between", value1, value2, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andFirstTbSkinPositiveExamTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"first_tb_skin_positive_exam_time\" not between", value1, value2, "firstTbSkinPositiveExamTime");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantIsNull() {
            addCriterion("\"is_tb_drug_resistant\" is null");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantIsNotNull() {
            addCriterion("\"is_tb_drug_resistant\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantEqualTo(String value) {
            addCriterion("\"is_tb_drug_resistant\" =", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantNotEqualTo(String value) {
            addCriterion("\"is_tb_drug_resistant\" <>", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantGreaterThan(String value) {
            addCriterion("\"is_tb_drug_resistant\" >", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_tb_drug_resistant\" >=", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantLessThan(String value) {
            addCriterion("\"is_tb_drug_resistant\" <", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantLessThanOrEqualTo(String value) {
            addCriterion("\"is_tb_drug_resistant\" <=", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantLike(String value) {
            addCriterion("\"is_tb_drug_resistant\" like", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantNotLike(String value) {
            addCriterion("\"is_tb_drug_resistant\" not like", value, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantIn(List<String> values) {
            addCriterion("\"is_tb_drug_resistant\" in", values, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantNotIn(List<String> values) {
            addCriterion("\"is_tb_drug_resistant\" not in", values, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantBetween(String value1, String value2) {
            addCriterion("\"is_tb_drug_resistant\" between", value1, value2, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andIsTbDrugResistantNotBetween(String value1, String value2) {
            addCriterion("\"is_tb_drug_resistant\" not between", value1, value2, "isTbDrugResistant");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameIsNull() {
            addCriterion("\"tb_drug_resistant_drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameIsNotNull() {
            addCriterion("\"tb_drug_resistant_drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameEqualTo(Object value) {
            addCriterion("\"tb_drug_resistant_drug_name\" =", value, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameNotEqualTo(Object value) {
            addCriterion("\"tb_drug_resistant_drug_name\" <>", value, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameGreaterThan(Object value) {
            addCriterion("\"tb_drug_resistant_drug_name\" >", value, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameGreaterThanOrEqualTo(Object value) {
            addCriterion("\"tb_drug_resistant_drug_name\" >=", value, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameLessThan(Object value) {
            addCriterion("\"tb_drug_resistant_drug_name\" <", value, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameLessThanOrEqualTo(Object value) {
            addCriterion("\"tb_drug_resistant_drug_name\" <=", value, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameIn(List<Object> values) {
            addCriterion("\"tb_drug_resistant_drug_name\" in", values, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameNotIn(List<Object> values) {
            addCriterion("\"tb_drug_resistant_drug_name\" not in", values, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameBetween(Object value1, Object value2) {
            addCriterion("\"tb_drug_resistant_drug_name\" between", value1, value2, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantDrugNameNotBetween(Object value1, Object value2) {
            addCriterion("\"tb_drug_resistant_drug_name\" not between", value1, value2, "tbDrugResistantDrugName");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeIsNull() {
            addCriterion("\"tb_drug_resistant_type\" is null");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeIsNotNull() {
            addCriterion("\"tb_drug_resistant_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeEqualTo(String value) {
            addCriterion("\"tb_drug_resistant_type\" =", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeNotEqualTo(String value) {
            addCriterion("\"tb_drug_resistant_type\" <>", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeGreaterThan(String value) {
            addCriterion("\"tb_drug_resistant_type\" >", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_drug_resistant_type\" >=", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeLessThan(String value) {
            addCriterion("\"tb_drug_resistant_type\" <", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeLessThanOrEqualTo(String value) {
            addCriterion("\"tb_drug_resistant_type\" <=", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeLike(String value) {
            addCriterion("\"tb_drug_resistant_type\" like", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeNotLike(String value) {
            addCriterion("\"tb_drug_resistant_type\" not like", value, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeIn(List<String> values) {
            addCriterion("\"tb_drug_resistant_type\" in", values, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeNotIn(List<String> values) {
            addCriterion("\"tb_drug_resistant_type\" not in", values, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeBetween(String value1, String value2) {
            addCriterion("\"tb_drug_resistant_type\" between", value1, value2, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andTbDrugResistantTypeNotBetween(String value1, String value2) {
            addCriterion("\"tb_drug_resistant_type\" not between", value1, value2, "tbDrugResistantType");
            return (Criteria) this;
        }

        public Criteria andIsAidsIsNull() {
            addCriterion("\"is_aids\" is null");
            return (Criteria) this;
        }

        public Criteria andIsAidsIsNotNull() {
            addCriterion("\"is_aids\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsAidsEqualTo(Boolean value) {
            addCriterion("\"is_aids\" =", value, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsNotEqualTo(Boolean value) {
            addCriterion("\"is_aids\" <>", value, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsGreaterThan(Boolean value) {
            addCriterion("\"is_aids\" >", value, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_aids\" >=", value, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsLessThan(Boolean value) {
            addCriterion("\"is_aids\" <", value, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_aids\" <=", value, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsIn(List<Boolean> values) {
            addCriterion("\"is_aids\" in", values, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsNotIn(List<Boolean> values) {
            addCriterion("\"is_aids\" not in", values, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_aids\" between", value1, value2, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsAidsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_aids\" not between", value1, value2, "isAids");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesIsNull() {
            addCriterion("\"is_diabetes\" is null");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesIsNotNull() {
            addCriterion("\"is_diabetes\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesEqualTo(Boolean value) {
            addCriterion("\"is_diabetes\" =", value, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesNotEqualTo(Boolean value) {
            addCriterion("\"is_diabetes\" <>", value, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesGreaterThan(Boolean value) {
            addCriterion("\"is_diabetes\" >", value, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_diabetes\" >=", value, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesLessThan(Boolean value) {
            addCriterion("\"is_diabetes\" <", value, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_diabetes\" <=", value, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesIn(List<Boolean> values) {
            addCriterion("\"is_diabetes\" in", values, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesNotIn(List<Boolean> values) {
            addCriterion("\"is_diabetes\" not in", values, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_diabetes\" between", value1, value2, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsDiabetesNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_diabetes\" not between", value1, value2, "isDiabetes");
            return (Criteria) this;
        }

        public Criteria andIsCopdIsNull() {
            addCriterion("\"is_copd\" is null");
            return (Criteria) this;
        }

        public Criteria andIsCopdIsNotNull() {
            addCriterion("\"is_copd\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsCopdEqualTo(Boolean value) {
            addCriterion("\"is_copd\" =", value, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdNotEqualTo(Boolean value) {
            addCriterion("\"is_copd\" <>", value, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdGreaterThan(Boolean value) {
            addCriterion("\"is_copd\" >", value, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_copd\" >=", value, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdLessThan(Boolean value) {
            addCriterion("\"is_copd\" <", value, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_copd\" <=", value, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdIn(List<Boolean> values) {
            addCriterion("\"is_copd\" in", values, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdNotIn(List<Boolean> values) {
            addCriterion("\"is_copd\" not in", values, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_copd\" between", value1, value2, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsCopdNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_copd\" not between", value1, value2, "isCopd");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseIsNull() {
            addCriterion("\"is_interstitial_lung_disease\" is null");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseIsNotNull() {
            addCriterion("\"is_interstitial_lung_disease\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseEqualTo(Boolean value) {
            addCriterion("\"is_interstitial_lung_disease\" =", value, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseNotEqualTo(Boolean value) {
            addCriterion("\"is_interstitial_lung_disease\" <>", value, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseGreaterThan(Boolean value) {
            addCriterion("\"is_interstitial_lung_disease\" >", value, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_interstitial_lung_disease\" >=", value, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseLessThan(Boolean value) {
            addCriterion("\"is_interstitial_lung_disease\" <", value, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_interstitial_lung_disease\" <=", value, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseIn(List<Boolean> values) {
            addCriterion("\"is_interstitial_lung_disease\" in", values, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseNotIn(List<Boolean> values) {
            addCriterion("\"is_interstitial_lung_disease\" not in", values, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_interstitial_lung_disease\" between", value1, value2, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsInterstitialLungDiseaseNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_interstitial_lung_disease\" not between", value1, value2, "isInterstitialLungDisease");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismIsNull() {
            addCriterion("\"is_pulmonary_embolism\" is null");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismIsNotNull() {
            addCriterion("\"is_pulmonary_embolism\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismEqualTo(Boolean value) {
            addCriterion("\"is_pulmonary_embolism\" =", value, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismNotEqualTo(Boolean value) {
            addCriterion("\"is_pulmonary_embolism\" <>", value, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismGreaterThan(Boolean value) {
            addCriterion("\"is_pulmonary_embolism\" >", value, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_pulmonary_embolism\" >=", value, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismLessThan(Boolean value) {
            addCriterion("\"is_pulmonary_embolism\" <", value, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_pulmonary_embolism\" <=", value, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismIn(List<Boolean> values) {
            addCriterion("\"is_pulmonary_embolism\" in", values, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismNotIn(List<Boolean> values) {
            addCriterion("\"is_pulmonary_embolism\" not in", values, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_pulmonary_embolism\" between", value1, value2, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsPulmonaryEmbolismNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_pulmonary_embolism\" not between", value1, value2, "isPulmonaryEmbolism");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65IsNull() {
            addCriterion("\"is_age_ge_65\" is null");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65IsNotNull() {
            addCriterion("\"is_age_ge_65\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65EqualTo(Boolean value) {
            addCriterion("\"is_age_ge_65\" =", value, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65NotEqualTo(Boolean value) {
            addCriterion("\"is_age_ge_65\" <>", value, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65GreaterThan(Boolean value) {
            addCriterion("\"is_age_ge_65\" >", value, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65GreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_age_ge_65\" >=", value, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65LessThan(Boolean value) {
            addCriterion("\"is_age_ge_65\" <", value, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65LessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_age_ge_65\" <=", value, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65In(List<Boolean> values) {
            addCriterion("\"is_age_ge_65\" in", values, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65NotIn(List<Boolean> values) {
            addCriterion("\"is_age_ge_65\" not in", values, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65Between(Boolean value1, Boolean value2) {
            addCriterion("\"is_age_ge_65\" between", value1, value2, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andIsAgeGe65NotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_age_ge_65\" not between", value1, value2, "isAgeGe65");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeIsNull() {
            addCriterion("\"tuberculosis_type\" is null");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeIsNotNull() {
            addCriterion("\"tuberculosis_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeEqualTo(Object value) {
            addCriterion("\"tuberculosis_type\" =", value, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeNotEqualTo(Object value) {
            addCriterion("\"tuberculosis_type\" <>", value, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeGreaterThan(Object value) {
            addCriterion("\"tuberculosis_type\" >", value, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeGreaterThanOrEqualTo(Object value) {
            addCriterion("\"tuberculosis_type\" >=", value, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeLessThan(Object value) {
            addCriterion("\"tuberculosis_type\" <", value, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeLessThanOrEqualTo(Object value) {
            addCriterion("\"tuberculosis_type\" <=", value, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeIn(List<Object> values) {
            addCriterion("\"tuberculosis_type\" in", values, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeNotIn(List<Object> values) {
            addCriterion("\"tuberculosis_type\" not in", values, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeBetween(Object value1, Object value2) {
            addCriterion("\"tuberculosis_type\" between", value1, value2, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andTuberculosisTypeNotBetween(Object value1, Object value2) {
            addCriterion("\"tuberculosis_type\" not between", value1, value2, "tuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeIsNull() {
            addCriterion("\"pulmonary_tuberculosis_type\" is null");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeIsNotNull() {
            addCriterion("\"pulmonary_tuberculosis_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeEqualTo(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" =", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeNotEqualTo(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" <>", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeGreaterThan(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" >", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" >=", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeLessThan(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" <", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeLessThanOrEqualTo(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" <=", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeLike(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" like", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeNotLike(String value) {
            addCriterion("\"pulmonary_tuberculosis_type\" not like", value, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeIn(List<String> values) {
            addCriterion("\"pulmonary_tuberculosis_type\" in", values, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeNotIn(List<String> values) {
            addCriterion("\"pulmonary_tuberculosis_type\" not in", values, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeBetween(String value1, String value2) {
            addCriterion("\"pulmonary_tuberculosis_type\" between", value1, value2, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andPulmonaryTuberculosisTypeNotBetween(String value1, String value2) {
            addCriterion("\"pulmonary_tuberculosis_type\" not between", value1, value2, "pulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeIsNull() {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" is null");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeIsNotNull() {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeEqualTo(Object value) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" =", value, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeNotEqualTo(Object value) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" <>", value, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeGreaterThan(Object value) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" >", value, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeGreaterThanOrEqualTo(Object value) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" >=", value, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeLessThan(Object value) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" <", value, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeLessThanOrEqualTo(Object value) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" <=", value, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeIn(List<Object> values) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" in", values, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeNotIn(List<Object> values) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" not in", values, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeBetween(Object value1, Object value2) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" between", value1, value2, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andExtraPulmonaryTuberculosisTypeNotBetween(Object value1, Object value2) {
            addCriterion("\"extra_pulmonary_tuberculosis_type\" not between", value1, value2, "extraPulmonaryTuberculosisType");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryIsNull() {
            addCriterion("\"is_drug_allergy_history\" is null");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryIsNotNull() {
            addCriterion("\"is_drug_allergy_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" =", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryNotEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" <>", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryGreaterThan(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" >", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" >=", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryLessThan(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" <", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_drug_allergy_history\" <=", value, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryIn(List<Boolean> values) {
            addCriterion("\"is_drug_allergy_history\" in", values, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryNotIn(List<Boolean> values) {
            addCriterion("\"is_drug_allergy_history\" not in", values, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_drug_allergy_history\" between", value1, value2, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andIsDrugAllergyHistoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_drug_allergy_history\" not between", value1, value2, "isDrugAllergyHistory");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameIsNull() {
            addCriterion("\"allergy_drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameIsNotNull() {
            addCriterion("\"allergy_drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameEqualTo(Object value) {
            addCriterion("\"allergy_drug_name\" =", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotEqualTo(Object value) {
            addCriterion("\"allergy_drug_name\" <>", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameGreaterThan(Object value) {
            addCriterion("\"allergy_drug_name\" >", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameGreaterThanOrEqualTo(Object value) {
            addCriterion("\"allergy_drug_name\" >=", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameLessThan(Object value) {
            addCriterion("\"allergy_drug_name\" <", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameLessThanOrEqualTo(Object value) {
            addCriterion("\"allergy_drug_name\" <=", value, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameIn(List<Object> values) {
            addCriterion("\"allergy_drug_name\" in", values, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotIn(List<Object> values) {
            addCriterion("\"allergy_drug_name\" not in", values, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameBetween(Object value1, Object value2) {
            addCriterion("\"allergy_drug_name\" between", value1, value2, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andAllergyDrugNameNotBetween(Object value1, Object value2) {
            addCriterion("\"allergy_drug_name\" not between", value1, value2, "allergyDrugName");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryIsNull() {
            addCriterion("\"smoking_history\" is null");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryIsNotNull() {
            addCriterion("\"smoking_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryEqualTo(String value) {
            addCriterion("\"smoking_history\" =", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryNotEqualTo(String value) {
            addCriterion("\"smoking_history\" <>", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryGreaterThan(String value) {
            addCriterion("\"smoking_history\" >", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryGreaterThanOrEqualTo(String value) {
            addCriterion("\"smoking_history\" >=", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryLessThan(String value) {
            addCriterion("\"smoking_history\" <", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryLessThanOrEqualTo(String value) {
            addCriterion("\"smoking_history\" <=", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryLike(String value) {
            addCriterion("\"smoking_history\" like", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryNotLike(String value) {
            addCriterion("\"smoking_history\" not like", value, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryIn(List<String> values) {
            addCriterion("\"smoking_history\" in", values, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryNotIn(List<String> values) {
            addCriterion("\"smoking_history\" not in", values, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryBetween(String value1, String value2) {
            addCriterion("\"smoking_history\" between", value1, value2, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andSmokingHistoryNotBetween(String value1, String value2) {
            addCriterion("\"smoking_history\" not between", value1, value2, "smokingHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryIsNull() {
            addCriterion("\"is_dust_exposure_history\" is null");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryIsNotNull() {
            addCriterion("\"is_dust_exposure_history\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" =", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryNotEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" <>", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryGreaterThan(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" >", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" >=", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryLessThan(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" <", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_dust_exposure_history\" <=", value, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryIn(List<Boolean> values) {
            addCriterion("\"is_dust_exposure_history\" in", values, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryNotIn(List<Boolean> values) {
            addCriterion("\"is_dust_exposure_history\" not in", values, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_dust_exposure_history\" between", value1, value2, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsDustExposureHistoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_dust_exposure_history\" not between", value1, value2, "isDustExposureHistory");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyIsNull() {
            addCriterion("\"is_isa_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyIsNotNull() {
            addCriterion("\"is_isa_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" =", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyNotEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" <>", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyGreaterThan(Boolean value) {
            addCriterion("\"is_isa_therapy\" >", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" >=", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyLessThan(Boolean value) {
            addCriterion("\"is_isa_therapy\" <", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_isa_therapy\" <=", value, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyIn(List<Boolean> values) {
            addCriterion("\"is_isa_therapy\" in", values, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyNotIn(List<Boolean> values) {
            addCriterion("\"is_isa_therapy\" not in", values, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_isa_therapy\" between", value1, value2, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsIsaTherapyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_isa_therapy\" not between", value1, value2, "isIsaTherapy");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameIsNull() {
            addCriterion("\"isa_therapy_drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameIsNotNull() {
            addCriterion("\"isa_therapy_drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameEqualTo(Object value) {
            addCriterion("\"isa_therapy_drug_name\" =", value, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameNotEqualTo(Object value) {
            addCriterion("\"isa_therapy_drug_name\" <>", value, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameGreaterThan(Object value) {
            addCriterion("\"isa_therapy_drug_name\" >", value, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameGreaterThanOrEqualTo(Object value) {
            addCriterion("\"isa_therapy_drug_name\" >=", value, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameLessThan(Object value) {
            addCriterion("\"isa_therapy_drug_name\" <", value, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameLessThanOrEqualTo(Object value) {
            addCriterion("\"isa_therapy_drug_name\" <=", value, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameIn(List<Object> values) {
            addCriterion("\"isa_therapy_drug_name\" in", values, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameNotIn(List<Object> values) {
            addCriterion("\"isa_therapy_drug_name\" not in", values, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameBetween(Object value1, Object value2) {
            addCriterion("\"isa_therapy_drug_name\" between", value1, value2, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsaTherapyDrugNameNotBetween(Object value1, Object value2) {
            addCriterion("\"isa_therapy_drug_name\" not between", value1, value2, "isaTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameIsNull() {
            addCriterion("\"hormone_therapy_drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameIsNotNull() {
            addCriterion("\"hormone_therapy_drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameEqualTo(Object value) {
            addCriterion("\"hormone_therapy_drug_name\" =", value, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameNotEqualTo(Object value) {
            addCriterion("\"hormone_therapy_drug_name\" <>", value, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameGreaterThan(Object value) {
            addCriterion("\"hormone_therapy_drug_name\" >", value, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameGreaterThanOrEqualTo(Object value) {
            addCriterion("\"hormone_therapy_drug_name\" >=", value, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameLessThan(Object value) {
            addCriterion("\"hormone_therapy_drug_name\" <", value, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameLessThanOrEqualTo(Object value) {
            addCriterion("\"hormone_therapy_drug_name\" <=", value, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameIn(List<Object> values) {
            addCriterion("\"hormone_therapy_drug_name\" in", values, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameNotIn(List<Object> values) {
            addCriterion("\"hormone_therapy_drug_name\" not in", values, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameBetween(Object value1, Object value2) {
            addCriterion("\"hormone_therapy_drug_name\" between", value1, value2, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andHormoneTherapyDrugNameNotBetween(Object value1, Object value2) {
            addCriterion("\"hormone_therapy_drug_name\" not between", value1, value2, "hormoneTherapyDrugName");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyIsNull() {
            addCriterion("\"is_hormone_therapy\" is null");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyIsNotNull() {
            addCriterion("\"is_hormone_therapy\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" =", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyNotEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" <>", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyGreaterThan(Boolean value) {
            addCriterion("\"is_hormone_therapy\" >", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" >=", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyLessThan(Boolean value) {
            addCriterion("\"is_hormone_therapy\" <", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyLessThanOrEqualTo(Boolean value) {
            addCriterion("\"is_hormone_therapy\" <=", value, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyIn(List<Boolean> values) {
            addCriterion("\"is_hormone_therapy\" in", values, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyNotIn(List<Boolean> values) {
            addCriterion("\"is_hormone_therapy\" not in", values, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_hormone_therapy\" between", value1, value2, "isHormoneTherapy");
            return (Criteria) this;
        }

        public Criteria andIsHormoneTherapyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("\"is_hormone_therapy\" not between", value1, value2, "isHormoneTherapy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}