@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM EDC Research Project Windows 自动部署脚本
REM 作者: Augment Agent
REM 日期: %date%

REM =============================================================================
REM 配置参数
REM =============================================================================

set REMOTE_HOST=***************
set REMOTE_PORT=52189
set REMOTE_USER=tpkmGWXd36yj
set REMOTE_DEPLOY_DIR=/webserver/lib
set REMOTE_LOG_DIR=/webserver/logs
set REMOTE_CONFIG_DIR=/webserver/config

set PROJECT_NAME=edc-research-project
set JAR_NAME=edc-research-project-remote.jar
set CONFIG_FILE=application-remote.yml
set PROFILE=edc-research-remote
set SPRING_PROFILE=remote

set LOCAL_TARGET_DIR=edc-research-center\edc-research-api\target
set LOCAL_CONFIG_PATH=edc-research-center\edc-research-api\src\main\resources

set JVM_OPTS=-Xmx512m -Xms256m -Xss256k
set SPRING_OPTS=--spring.profiles.active=%SPRING_PROFILE%

REM =============================================================================
REM 工具函数
REM =============================================================================

:log_info
echo [INFO] %~1
goto :eof

:log_success
echo [SUCCESS] %~1
goto :eof

:log_warning
echo [WARNING] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

:check_dependencies
call :log_info "检查依赖工具..."

where mvn >nul 2>&1
if errorlevel 1 (
    call :log_error "Maven 未安装或不在 PATH 中"
    exit /b 1
)

where ssh >nul 2>&1
if errorlevel 1 (
    call :log_error "SSH 未安装或不在 PATH 中，请安装 Git for Windows 或 OpenSSH"
    exit /b 1
)

where scp >nul 2>&1
if errorlevel 1 (
    call :log_error "SCP 未安装或不在 PATH 中，请安装 Git for Windows 或 OpenSSH"
    exit /b 1
)

call :log_success "依赖检查完成"
goto :eof

:test_remote_connection
call :log_info "测试远程服务器连接..."

ssh -p %REMOTE_PORT% -o ConnectTimeout=10 -o BatchMode=yes %REMOTE_USER%@%REMOTE_HOST% "echo 'Connection test successful'" >nul 2>&1
if errorlevel 1 (
    call :log_error "无法连接到远程服务器 %REMOTE_USER%@%REMOTE_HOST%:%REMOTE_PORT%"
    call :log_error "请检查网络连接、SSH 密钥配置或服务器状态"
    exit /b 1
)

call :log_success "远程服务器连接正常"
goto :eof

:clean_project
call :log_info "清理项目..."
mvn clean -q
if errorlevel 1 (
    call :log_error "项目清理失败"
    exit /b 1
)
call :log_success "项目清理完成"
goto :eof

:build_project
call :log_info "开始构建项目 (Profile: %PROFILE%)..."

mvn clean package -DskipTests=true -P %PROFILE% -q
if errorlevel 1 (
    call :log_error "项目构建失败"
    exit /b 1
)

if not exist "%LOCAL_TARGET_DIR%\%JAR_NAME%" (
    call :log_error "JAR 文件未找到: %LOCAL_TARGET_DIR%\%JAR_NAME%"
    exit /b 1
)

if not exist "%LOCAL_CONFIG_PATH%\%CONFIG_FILE%" (
    call :log_error "配置文件未找到: %LOCAL_CONFIG_PATH%\%CONFIG_FILE%"
    exit /b 1
)

call :log_success "项目构建成功"
goto :eof

:create_remote_directories
call :log_info "创建远程目录..."

ssh -p %REMOTE_PORT% %REMOTE_USER%@%REMOTE_HOST% "mkdir -p %REMOTE_DEPLOY_DIR% && mkdir -p %REMOTE_LOG_DIR% && mkdir -p %REMOTE_CONFIG_DIR% && mkdir -p %REMOTE_DEPLOY_DIR%/backup"
if errorlevel 1 (
    call :log_error "创建远程目录失败"
    exit /b 1
)

call :log_success "远程目录创建完成"
goto :eof

:stop_remote_service
call :log_info "停止远程服务..."

ssh -p %REMOTE_PORT% %REMOTE_USER%@%REMOTE_HOST% "PID=$(ps aux | grep '%JAR_NAME%' | grep -v grep | awk '{print $2}'); if [ ! -z \"$PID\" ]; then echo \"找到运行中的进程 PID: $PID\"; kill -15 $PID; sleep 5; if ps -p $PID > /dev/null 2>&1; then echo \"进程仍在运行，强制终止...\"; kill -9 $PID; sleep 2; fi; echo \"服务已停止\"; else echo \"未找到运行中的服务\"; fi"

call :log_success "远程服务停止完成"
goto :eof

:upload_files
call :log_info "上传文件到远程服务器..."

scp -P %REMOTE_PORT% "%LOCAL_TARGET_DIR%\%JAR_NAME%" "%REMOTE_USER%@%REMOTE_HOST%:%REMOTE_DEPLOY_DIR%/"
if errorlevel 1 (
    call :log_error "JAR 文件上传失败"
    exit /b 1
)
call :log_success "JAR 文件上传成功"

scp -P %REMOTE_PORT% "%LOCAL_CONFIG_PATH%\%CONFIG_FILE%" "%REMOTE_USER%@%REMOTE_HOST%:%REMOTE_CONFIG_DIR%/"
if errorlevel 1 (
    call :log_error "配置文件上传失败"
    exit /b 1
)
call :log_success "配置文件上传成功"
goto :eof

:start_remote_service
call :log_info "启动远程服务..."

ssh -p %REMOTE_PORT% %REMOTE_USER%@%REMOTE_HOST% "cd %REMOTE_DEPLOY_DIR% && nohup java %JVM_OPTS% -jar %JAR_NAME% %SPRING_OPTS% --spring.config.location=%REMOTE_CONFIG_DIR%/%CONFIG_FILE% >> %REMOTE_LOG_DIR%/%PROJECT_NAME%.out 2>&1 & echo $! > %REMOTE_DEPLOY_DIR%/%PROJECT_NAME%.pid && echo \"服务启动命令已执行\" && echo \"PID 文件: %REMOTE_DEPLOY_DIR%/%PROJECT_NAME%.pid\" && echo \"日志文件: %REMOTE_LOG_DIR%/%PROJECT_NAME%.out\""

call :log_success "远程服务启动完成"
goto :eof

:check_service_status
call :log_info "检查服务状态..."

ssh -p %REMOTE_PORT% %REMOTE_USER%@%REMOTE_HOST% "if [ -f %REMOTE_DEPLOY_DIR%/%PROJECT_NAME%.pid ]; then PID=$(cat %REMOTE_DEPLOY_DIR%/%PROJECT_NAME%.pid); if ps -p $PID > /dev/null 2>&1; then echo \"✅ 服务正在运行 (PID: $PID)\"; else echo \"❌ 服务未运行 (PID 文件存在但进程不存在)\"; exit 1; fi; else echo \"❌ PID 文件不存在\"; exit 1; fi"

goto :eof

:show_logs
call :log_info "显示最新日志..."

ssh -p %REMOTE_PORT% %REMOTE_USER%@%REMOTE_HOST% "if [ -f %REMOTE_LOG_DIR%/%PROJECT_NAME%.out ]; then echo \"=== 最新 50 行日志 ===\"; tail -n 50 %REMOTE_LOG_DIR%/%PROJECT_NAME%.out; else echo \"日志文件不存在: %REMOTE_LOG_DIR%/%PROJECT_NAME%.out\"; fi"

goto :eof

:show_usage
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   deploy    完整部署 (构建 + 部署 + 启动)
echo   build     仅构建项目
echo   upload    仅上传文件
echo   start     仅启动服务
echo   stop      仅停止服务
echo   restart   重启服务
echo   status    检查服务状态
echo   logs      查看日志
echo   help      显示帮助信息
echo.
echo 示例:
echo   %~nx0 deploy     # 完整部署
echo   %~nx0 logs       # 查看日志
echo   %~nx0 status     # 检查状态
goto :eof

REM =============================================================================
REM 主函数
REM =============================================================================

:main
set ACTION=%~1
if "%ACTION%"=="" set ACTION=deploy

if "%ACTION%"=="build" (
    call :check_dependencies
    call :clean_project
    call :build_project
) else if "%ACTION%"=="upload" (
    call :check_dependencies
    call :test_remote_connection
    call :create_remote_directories
    call :upload_files
) else if "%ACTION%"=="start" (
    call :check_dependencies
    call :test_remote_connection
    call :start_remote_service
    timeout /t 10 /nobreak >nul
    call :check_service_status
) else if "%ACTION%"=="stop" (
    call :check_dependencies
    call :test_remote_connection
    call :stop_remote_service
) else if "%ACTION%"=="restart" (
    call :check_dependencies
    call :test_remote_connection
    call :stop_remote_service
    timeout /t 5 /nobreak >nul
    call :start_remote_service
    timeout /t 10 /nobreak >nul
    call :check_service_status
) else if "%ACTION%"=="status" (
    call :check_dependencies
    call :test_remote_connection
    call :check_service_status
) else if "%ACTION%"=="logs" (
    call :check_dependencies
    call :test_remote_connection
    call :show_logs
) else if "%ACTION%"=="deploy" (
    call :log_info "开始完整部署流程..."
    call :check_dependencies
    call :test_remote_connection
    call :clean_project
    call :build_project
    call :create_remote_directories
    call :stop_remote_service
    call :upload_files
    call :start_remote_service
    timeout /t 15 /nobreak >nul
    call :check_service_status
    call :show_logs
    call :log_success "部署完成！"
) else if "%ACTION%"=="help" (
    call :show_usage
) else (
    call :log_error "未知选项: %ACTION%"
    call :show_usage
    exit /b 1
)

goto :eof

REM 执行主函数
call :main %*
