-- =====================================================
-- 系统访问日志记录表安装测试脚本
-- 用于验证安装是否成功
-- =====================================================

-- 1. 检查表是否存在
SELECT 
    TABLE_NAME,
    ENGINE,
    TABLE_ROWS,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'system_request_record';

-- 2. 检查表结构
DESCRIBE system_request_record;

-- 3. 检查索引
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'system_request_record'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- 4. 检查视图
SELECT 
    TABLE_NAME as VIEW_NAME,
    VIEW_DEFINITION
FROM information_schema.VIEWS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('v_system_request_log_stats', 'v_hot_urls_stats');

-- 5. 检查存储过程
SELECT 
    ROUTINE_NAME,
    ROUTINE_TYPE,
    CREATED,
    LAST_ALTERED
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
AND ROUTINE_NAME = 'CleanSystemRequestRecord';

-- 6. 测试插入数据
INSERT INTO system_request_record (
    user_name, real_name, request_name, request_method, request_url,
    method_name, business_type, operator_type, data_from,
    request_ip, user_agent, browser, operating_system, device_type,
    response_time, http_status, is_success, status
) VALUES (
    'test_user', '测试用户', '测试请求', 'GET', '/api/test',
    'testMethod', 'TEST', 'SELECT', 'WEB',
    '127.0.0.1', 'Mozilla/5.0 Test Browser', 'Chrome', 'Windows', 'DESKTOP',
    150, 200, 1, 1
);

-- 7. 测试查询功能
SELECT 
    id, user_name, request_name, request_method, request_url,
    response_time, http_status, is_success, create_time
FROM system_request_record 
WHERE user_name = 'test_user'
ORDER BY create_time DESC 
LIMIT 5;

-- 8. 测试索引使用情况
EXPLAIN SELECT * FROM system_request_record 
WHERE user_name = 'test_user' 
AND create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 9. 测试统计视图
SELECT * FROM v_system_request_log_stats LIMIT 3;

-- 10. 测试存储过程 (注意：这会删除测试数据)
-- CALL CleanSystemRequestRecord(0);  -- 删除所有数据，谨慎使用

-- 11. 性能测试 - 批量插入
INSERT INTO system_request_record (
    user_name, real_name, request_name, request_method, request_url,
    method_name, business_type, operator_type, data_from,
    request_ip, response_time, http_status, is_success, status
) VALUES 
('user1', '用户1', '批量测试1', 'POST', '/api/batch1', 'batchMethod1', 'TEST', 'INSERT', 'WEB', '***********', 200, 200, 1, 1),
('user2', '用户2', '批量测试2', 'PUT', '/api/batch2', 'batchMethod2', 'TEST', 'UPDATE', 'WEB', '***********', 300, 200, 1, 1),
('user3', '用户3', '批量测试3', 'DELETE', '/api/batch3', 'batchMethod3', 'TEST', 'DELETE', 'WEB', '***********', 100, 200, 1, 1),
('user4', '用户4', '批量测试4', 'GET', '/api/batch4', 'batchMethod4', 'TEST', 'SELECT', 'API', '***********', 50, 200, 1, 1),
('user5', '用户5', '批量测试5', 'POST', '/api/batch5', 'batchMethod5', 'TEST', 'INSERT', 'MOBILE', '***********', 400, 500, 0, 0);

-- 12. 验证批量插入结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_success = 1 THEN 1 END) as success_count,
    COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
    AVG(response_time) as avg_response_time,
    COUNT(DISTINCT user_name) as unique_users
FROM system_request_record;

-- 13. 测试复杂查询性能
SELECT 
    user_name,
    COUNT(*) as request_count,
    AVG(response_time) as avg_response_time,
    MAX(response_time) as max_response_time,
    COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count
FROM system_request_record 
WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
GROUP BY user_name
ORDER BY request_count DESC;

-- 14. 清理测试数据 (可选)
-- DELETE FROM system_request_record WHERE user_name LIKE 'test_%' OR user_name LIKE 'user%';

-- 15. 最终验证
SELECT 
    '安装测试完成' as status,
    COUNT(*) as total_records,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record
FROM system_request_record;

-- =====================================================
-- 测试结果说明
-- =====================================================

/*
测试项目说明：

1. 表结构检查 - 验证表是否正确创建
2. 索引检查 - 验证所有索引是否正确创建
3. 视图检查 - 验证统计视图是否可用
4. 存储过程检查 - 验证清理存储过程是否可用
5. 数据插入测试 - 验证基本的CRUD操作
6. 查询性能测试 - 验证索引是否生效
7. 统计功能测试 - 验证统计视图是否正常工作
8. 批量操作测试 - 验证批量插入性能
9. 复杂查询测试 - 验证复杂统计查询性能

如果所有测试都通过，说明安装成功，可以正常使用。

注意事项：
- 测试会插入一些测试数据
- 可以通过注释中的DELETE语句清理测试数据
- 存储过程测试被注释掉，避免意外删除数据
*/
