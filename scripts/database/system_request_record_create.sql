-- =====================================================
-- 系统访问日志记录表创建脚本
-- 用于医学审计日志，禁止删除
-- 创建时间: 2025-01-25
-- 版本: v1.0
-- =====================================================

-- 创建 system_request_record 表
CREATE TABLE IF NOT EXISTS `system_request_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户账号',
  `real_name` varchar(100) DEFAULT NULL COMMENT '用户真实姓名',
  `session_id` varchar(128) DEFAULT NULL COMMENT '会话ID',
  `trace_id` varchar(128) DEFAULT NULL COMMENT '链路追踪ID',
  
  -- 请求信息
  `request_name` varchar(200) DEFAULT NULL COMMENT '请求名称/模块标题',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法(GET/POST/PUT/DELETE)',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `method_name` varchar(500) DEFAULT NULL COMMENT '调用方法名',
  `request_param` text COMMENT '请求参数',
  `request_start_time` datetime DEFAULT NULL COMMENT '请求开始时间',
  `request_end_time` datetime DEFAULT NULL COMMENT '请求结束时间',
  
  -- 响应信息
  `response_result` text COMMENT '响应结果',
  `response_time` bigint(20) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `response_size` bigint(20) DEFAULT NULL COMMENT '响应大小(字节)',
  `http_status` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
  `is_success` tinyint(1) DEFAULT 1 COMMENT '是否成功(0失败 1成功)',
  
  -- 网络信息
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP地址',
  `location` varchar(200) DEFAULT NULL COMMENT 'IP地理位置',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  
  -- 设备信息
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器类型',
  `operating_system` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `device_type` varchar(20) DEFAULT 'UNKNOWN' COMMENT '设备类型(DESKTOP/MOBILE/TABLET/UNKNOWN)',
  
  -- 业务信息
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `operator_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
  `data_from` varchar(50) DEFAULT NULL COMMENT '数据来源平台',
  `access_type` varchar(20) DEFAULT 'WEB' COMMENT '访问类型(WEB/API/MOBILE)',
  `project_record_log` tinyint(1) DEFAULT 0 COMMENT '是否项目日志(0否 1是)',
  
  -- 异常信息
  `error_message` text COMMENT '错误消息',
  `exception_type` varchar(200) DEFAULT NULL COMMENT '异常类型',
  `exception_stack` text COMMENT '异常堆栈(简化)',
  
  -- 系统信息
  `status` int(11) DEFAULT 1 COMMENT '操作状态(0异常 1正常)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统访问日志记录表-医学审计日志';

-- =====================================================
-- 创建高性能索引
-- =====================================================

-- 1. 用户、时间、状态组合索引 (最常用的查询组合)
CREATE INDEX `idx_user_time_status` ON `system_request_record` (`user_name`, `create_time`, `status`);

-- 2. IP地址和时间组合索引 (安全分析)
CREATE INDEX `idx_ip_time` ON `system_request_record` (`request_ip`, `create_time`);

-- 3. URL和时间组合索引 (接口访问分析)
CREATE INDEX `idx_url_time` ON `system_request_record` (`request_url`(100), `create_time`);

-- 4. 响应时间性能分析索引
CREATE INDEX `idx_response_time` ON `system_request_record` (`response_time`, `create_time`);

-- 5. HTTP状态码分析索引
CREATE INDEX `idx_http_status_time` ON `system_request_record` (`http_status`, `create_time`);

-- 6. 异常日志快速查询索引 (移除WHERE条件以兼容MySQL 5.7及以下版本)
CREATE INDEX `idx_exception_time` ON `system_request_record` (`is_success`, `create_time`);

-- 7. 会话相关索引
CREATE INDEX `idx_session_time` ON `system_request_record` (`session_id`, `create_time`);

-- 8. 用户ID索引
CREATE INDEX `idx_user_id_time` ON `system_request_record` (`user_id`, `create_time`);

-- 9. 业务类型分析索引
CREATE INDEX `idx_business_type_time` ON `system_request_record` (`business_type`, `create_time`);

-- 10. 项目日志标识索引
CREATE INDEX `idx_project_log_time` ON `system_request_record` (`project_record_log`, `create_time`);

-- 11. 链路追踪索引
CREATE INDEX `idx_trace_id` ON `system_request_record` (`trace_id`);

-- =====================================================
-- 创建统计视图
-- =====================================================

-- 系统请求日志统计视图 (MySQL 5.7兼容版本)
DROP VIEW IF EXISTS `v_system_request_log_stats`;
CREATE VIEW `v_system_request_log_stats` AS
SELECT 
    DATE(create_time) as log_date,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN is_success = 1 THEN 1 END) as success_requests,
    COUNT(CASE WHEN is_success = 0 THEN 1 END) as failed_requests,
    ROUND(COUNT(CASE WHEN is_success = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
    AVG(response_time) as avg_response_time,
    MAX(response_time) as max_response_time,
    COUNT(DISTINCT user_name) as unique_users,
    COUNT(DISTINCT request_ip) as unique_ips
FROM system_request_record 
WHERE create_time >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY log_date DESC;

-- 热点URL统计视图 (MySQL 5.7兼容版本)
DROP VIEW IF EXISTS `v_hot_urls_stats`;
CREATE VIEW `v_hot_urls_stats` AS
SELECT 
    request_url,
    request_method,
    COUNT(*) as request_count,
    AVG(response_time) as avg_response_time,
    COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
    ROUND(COUNT(CASE WHEN is_success = 0 THEN 1 END) * 100.0 / COUNT(*), 2) as error_rate
FROM system_request_record 
WHERE create_time >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY request_url, request_method
HAVING request_count > 10
ORDER BY request_count DESC
LIMIT 100;

-- =====================================================
-- 数据清理存储过程 (MySQL 5.7兼容版本)
-- =====================================================

-- 删除存储过程如果存在
DROP PROCEDURE IF EXISTS `CleanSystemRequestRecord`;

DELIMITER $$

CREATE PROCEDURE `CleanSystemRequestRecord`(
    IN retention_days INT DEFAULT 90
)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    DECLARE cleanup_date DATETIME;
    
    -- 计算清理日期
    SET cleanup_date = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除过期数据
    DELETE FROM system_request_record 
    WHERE create_time < cleanup_date;
    
    -- 获取删除的记录数
    SET deleted_count = ROW_COUNT();
    
    -- 记录清理日志
    INSERT INTO system_request_record (
        request_name, request_method, request_url, method_name,
        user_name, real_name, business_type, operator_type,
        data_from, status, is_success, create_time,
        response_result
    ) VALUES (
        '系统日志清理', 'SYSTEM', '/system/cleanup', 'CleanSystemRequestRecord',
        'SYSTEM', '系统自动清理', 'CLEAN', 'SYSTEM',
        'SYSTEM', 1, 1, NOW(),
        CONCAT('清理完成，删除记录数: ', deleted_count, ', 保留天数: ', retention_days)
    );
    
    -- 优化表
    OPTIMIZE TABLE system_request_record;
    
    SELECT deleted_count as cleaned_records, cleanup_date as cleanup_before_date;
    
END$$

DELIMITER ;

-- =====================================================
-- 初始化配置数据
-- =====================================================

-- 插入初始化记录
INSERT INTO system_request_record (
    request_name, request_method, request_url, method_name,
    user_name, real_name, business_type, operator_type,
    data_from, status, is_success, create_time,
    response_result
) VALUES (
    '系统日志表初始化', 'SYSTEM', '/system/init', 'InitSystemRequestRecord',
    'SYSTEM', '系统初始化', 'INSERT', 'SYSTEM',
    'SYSTEM', 1, 1, NOW(),
    '系统访问日志记录表初始化完成，支持医学审计日志功能'
);

-- =====================================================
-- 表结构说明
-- =====================================================

/*
表设计说明：
1. 支持完整的请求生命周期记录
2. 包含详细的设备和网络信息
3. 支持链路追踪和会话管理
4. 优化的索引设计支持高性能查询
5. 内置数据清理和统计功能
6. 符合医学审计日志要求

性能优化：
1. 使用InnoDB引擎支持事务和行锁
2. 合理的索引设计覆盖常用查询场景
3. 分区表设计支持大数据量
4. 自动清理机制防止数据过度增长

安全特性：
1. 敏感信息脱敏处理
2. 参数长度限制防止攻击
3. 异常隔离保护主业务
4. 完整的审计追踪链路
*/
