-- =====================================================
-- 系统访问日志记录表创建脚本 (MySQL 兼容版本)
-- 用于医学审计日志，禁止删除
-- 创建时间: 2025-01-25
-- 版本: v1.1 (MySQL 5.7+ 兼容)
-- 支持版本: MySQL 5.7, 8.0, 9.0+
-- =====================================================

-- 检查并删除已存在的表 (可选，谨慎使用)
-- DROP TABLE IF EXISTS `system_request_record`;

-- 创建 system_request_record 表
CREATE TABLE IF NOT EXISTS `system_request_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户账号',
  `real_name` varchar(100) DEFAULT NULL COMMENT '用户真实姓名',
  `session_id` varchar(128) DEFAULT NULL COMMENT '会话ID',
  `trace_id` varchar(128) DEFAULT NULL COMMENT '链路追踪ID',
  
  -- 请求信息
  `request_name` varchar(200) DEFAULT NULL COMMENT '请求名称/模块标题',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法(GET/POST/PUT/DELETE)',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `method_name` varchar(500) DEFAULT NULL COMMENT '调用方法名',
  `request_param` text COMMENT '请求参数',
  `request_start_time` datetime DEFAULT NULL COMMENT '请求开始时间',
  `request_end_time` datetime DEFAULT NULL COMMENT '请求结束时间',
  
  -- 响应信息
  `response_result` text COMMENT '响应结果',
  `response_time` bigint(20) DEFAULT NULL COMMENT '响应时间(毫秒)',
  `response_size` bigint(20) DEFAULT NULL COMMENT '响应大小(字节)',
  `http_status` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
  `is_success` tinyint(1) DEFAULT 1 COMMENT '是否成功(0失败 1成功)',
  
  -- 网络信息
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP地址',
  `location` varchar(200) DEFAULT NULL COMMENT 'IP地理位置',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  
  -- 设备信息
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器类型',
  `operating_system` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `device_type` varchar(20) DEFAULT 'UNKNOWN' COMMENT '设备类型(DESKTOP/MOBILE/TABLET/UNKNOWN)',
  
  -- 业务信息
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `operator_type` varchar(50) DEFAULT NULL COMMENT '操作类型',
  `data_from` varchar(50) DEFAULT NULL COMMENT '数据来源平台',
  `access_type` varchar(20) DEFAULT 'WEB' COMMENT '访问类型(WEB/API/MOBILE)',
  `project_record_log` tinyint(1) DEFAULT 0 COMMENT '是否项目日志(0否 1是)',
  
  -- 异常信息
  `error_message` text COMMENT '错误消息',
  `exception_type` varchar(200) DEFAULT NULL COMMENT '异常类型',
  `exception_stack` text COMMENT '异常堆栈(简化)',
  
  -- 系统信息
  `status` int(11) DEFAULT 1 COMMENT '操作状态(0异常 1正常)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统访问日志记录表-医学审计日志';

-- =====================================================
-- 创建高性能索引 (MySQL 5.7+ 兼容版本)
-- =====================================================

-- 1. 用户、时间、状态组合索引 (最常用的查询组合)
CREATE INDEX `idx_user_time_status` ON `system_request_record` (`user_name`, `create_time`, `status`);

-- 2. IP地址和时间组合索引 (安全分析)
CREATE INDEX `idx_ip_time` ON `system_request_record` (`request_ip`, `create_time`);

-- 3. URL和时间组合索引 (接口访问分析)
CREATE INDEX `idx_url_time` ON `system_request_record` (`request_url`(100), `create_time`);

-- 4. 响应时间性能分析索引
CREATE INDEX `idx_response_time` ON `system_request_record` (`response_time`, `create_time`);

-- 5. HTTP状态码分析索引
CREATE INDEX `idx_http_status_time` ON `system_request_record` (`http_status`, `create_time`);

-- 6. 异常日志快速查询索引 (移除WHERE条件以兼容所有MySQL版本)
CREATE INDEX `idx_exception_time` ON `system_request_record` (`is_success`, `create_time`);

-- 7. 会话相关索引
CREATE INDEX `idx_session_time` ON `system_request_record` (`session_id`, `create_time`);

-- 8. 用户ID索引
CREATE INDEX `idx_user_id_time` ON `system_request_record` (`user_id`, `create_time`);

-- 9. 业务类型分析索引
CREATE INDEX `idx_business_type_time` ON `system_request_record` (`business_type`, `create_time`);

-- 10. 项目日志标识索引
CREATE INDEX `idx_project_log_time` ON `system_request_record` (`project_record_log`, `create_time`);

-- 11. 链路追踪索引
CREATE INDEX `idx_trace_id` ON `system_request_record` (`trace_id`);

-- =====================================================
-- 创建统计视图 (MySQL 5.7+ 兼容版本)
-- =====================================================

-- 删除已存在的视图
DROP VIEW IF EXISTS `v_system_request_log_stats`;

-- 系统请求日志统计视图
CREATE VIEW `v_system_request_log_stats` AS
SELECT 
    DATE(create_time) as log_date,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN is_success = 1 THEN 1 END) as success_requests,
    COUNT(CASE WHEN is_success = 0 THEN 1 END) as failed_requests,
    ROUND(COUNT(CASE WHEN is_success = 1 THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
    AVG(response_time) as avg_response_time,
    MAX(response_time) as max_response_time,
    COUNT(DISTINCT user_name) as unique_users,
    COUNT(DISTINCT request_ip) as unique_ips
FROM system_request_record 
WHERE create_time >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY log_date DESC;

-- 删除已存在的视图
DROP VIEW IF EXISTS `v_hot_urls_stats`;

-- 热点URL统计视图
CREATE VIEW `v_hot_urls_stats` AS
SELECT 
    request_url,
    request_method,
    COUNT(*) as request_count,
    AVG(response_time) as avg_response_time,
    COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
    ROUND(COUNT(CASE WHEN is_success = 0 THEN 1 END) * 100.0 / COUNT(*), 2) as error_rate
FROM system_request_record 
WHERE create_time >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY request_url, request_method
HAVING request_count > 10
ORDER BY request_count DESC
LIMIT 100;

-- =====================================================
-- 数据清理存储过程 (MySQL 5.7+ 兼容版本)
-- =====================================================

-- 删除已存在的存储过程
DROP PROCEDURE IF EXISTS `CleanSystemRequestRecord`;

DELIMITER $$

CREATE PROCEDURE `CleanSystemRequestRecord`(
    IN retention_days INT DEFAULT 90
)
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    DECLARE cleanup_date DATETIME;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 计算清理日期
    SET cleanup_date = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 删除过期数据
    DELETE FROM system_request_record 
    WHERE create_time < cleanup_date;
    
    -- 获取删除的记录数
    SET deleted_count = ROW_COUNT();
    
    -- 记录清理日志
    INSERT INTO system_request_record (
        request_name, request_method, request_url, method_name,
        user_name, real_name, business_type, operator_type,
        data_from, status, is_success, create_time,
        response_result
    ) VALUES (
        '系统日志清理', 'SYSTEM', '/system/cleanup', 'CleanSystemRequestRecord',
        'SYSTEM', '系统自动清理', 'CLEAN', 'SYSTEM',
        'SYSTEM', 1, 1, NOW(),
        CONCAT('清理完成，删除记录数: ', deleted_count, ', 保留天数: ', retention_days)
    );
    
    COMMIT;
    
    -- 优化表 (在事务外执行)
    OPTIMIZE TABLE system_request_record;
    
    SELECT deleted_count as cleaned_records, cleanup_date as cleanup_before_date;
    
END$$

DELIMITER ;

-- =====================================================
-- 初始化配置数据
-- =====================================================

-- 插入初始化记录
INSERT INTO system_request_record (
    request_name, request_method, request_url, method_name,
    user_name, real_name, business_type, operator_type,
    data_from, status, is_success, create_time,
    response_result
) VALUES (
    '系统日志表初始化', 'SYSTEM', '/system/init', 'InitSystemRequestRecord',
    'SYSTEM', '系统初始化', 'INSERT', 'SYSTEM',
    'SYSTEM', 1, 1, NOW(),
    CONCAT('系统访问日志记录表初始化完成，MySQL版本: ', VERSION(), '，支持医学审计日志功能')
);

-- =====================================================
-- 验证安装
-- =====================================================

-- 显示表结构
DESCRIBE system_request_record;

-- 显示索引信息
SHOW INDEX FROM system_request_record;

-- 显示视图信息
SHOW CREATE VIEW v_system_request_log_stats;
SHOW CREATE VIEW v_hot_urls_stats;

-- 显示存储过程信息
SHOW CREATE PROCEDURE CleanSystemRequestRecord;

-- 验证初始化记录
SELECT * FROM system_request_record WHERE request_name = '系统日志表初始化';

-- 显示当前MySQL版本和兼容性信息
SELECT 
    VERSION() as mysql_version,
    'MySQL 5.7+ 兼容版本' as script_version,
    '系统访问日志记录表安装完成' as status;

-- =====================================================
-- 使用说明
-- =====================================================

/*
安装完成后的使用说明：

1. 表结构验证：
   DESCRIBE system_request_record;

2. 查看统计信息：
   SELECT * FROM v_system_request_log_stats LIMIT 10;
   SELECT * FROM v_hot_urls_stats LIMIT 10;

3. 执行数据清理：
   CALL CleanSystemRequestRecord(90);  -- 保留90天数据

4. 性能监控：
   SELECT COUNT(*) FROM system_request_record;
   SELECT AVG(response_time) FROM system_request_record WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 DAY);

5. 索引使用情况：
   EXPLAIN SELECT * FROM system_request_record WHERE user_name = 'test' AND create_time >= '2025-01-25';

注意事项：
- 本脚本已针对 MySQL 5.7+ 进行优化
- 移除了不兼容的 WHERE 条件索引
- 使用 DROP + CREATE 替代 CREATE OR REPLACE
- 添加了事务处理和错误处理
- 支持 MySQL 5.7, 8.0, 9.0+ 版本
*/
