package com.haoys.disease.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
public class PatientBaseVo {

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "就诊卡号")
    private String visitCardNo;

    @ApiModelProperty(value = "病案code")
    private String patientCode;

    @ApiModelProperty(value = "身份证号码-脱敏处理")
    private String idcardView;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "性别-男，女，未知")
    private String gender;

    @ApiModelProperty(value = "出生日期")
    private Date birthData;

    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;

    @ApiModelProperty(value = "国籍")
    private String nationality;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "邮政编码")
    private String zipcode;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "联系人关系")
    private String contactRelation;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "工作单位")
    private String department;

    @ApiModelProperty(value = "工作单位联系电话")
    private String departmentPhone;

    @ApiModelProperty(value = "省份code")
    private String privinceCode;

    @ApiModelProperty(value = "地市code")
    private String cityCode;

    @ApiModelProperty(value = "区县code")
    private String countryCode;
}
