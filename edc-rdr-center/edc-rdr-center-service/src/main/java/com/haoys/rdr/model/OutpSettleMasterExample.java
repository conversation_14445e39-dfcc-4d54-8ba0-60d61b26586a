package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OutpSettleMasterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public OutpSettleMasterExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitDateIsNull() {
            addCriterion("\"visit_date\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitDateIsNotNull() {
            addCriterion("\"visit_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitDateEqualTo(Date value) {
            addCriterion("\"visit_date\" =", value, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateNotEqualTo(Date value) {
            addCriterion("\"visit_date\" <>", value, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateGreaterThan(Date value) {
            addCriterion("\"visit_date\" >", value, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateGreaterThanOrEqualTo(Date value) {
            addCriterion("\"visit_date\" >=", value, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateLessThan(Date value) {
            addCriterion("\"visit_date\" <", value, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateLessThanOrEqualTo(Date value) {
            addCriterion("\"visit_date\" <=", value, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateIn(List<Date> values) {
            addCriterion("\"visit_date\" in", values, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateNotIn(List<Date> values) {
            addCriterion("\"visit_date\" not in", values, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateBetween(Date value1, Date value2) {
            addCriterion("\"visit_date\" between", value1, value2, "visitDate");
            return (Criteria) this;
        }

        public Criteria andVisitDateNotBetween(Date value1, Date value2) {
            addCriterion("\"visit_date\" not between", value1, value2, "visitDate");
            return (Criteria) this;
        }

        public Criteria andRcptIdIsNull() {
            addCriterion("\"rcpt_id\" is null");
            return (Criteria) this;
        }

        public Criteria andRcptIdIsNotNull() {
            addCriterion("\"rcpt_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andRcptIdEqualTo(String value) {
            addCriterion("\"rcpt_id\" =", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdNotEqualTo(String value) {
            addCriterion("\"rcpt_id\" <>", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdGreaterThan(String value) {
            addCriterion("\"rcpt_id\" >", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"rcpt_id\" >=", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdLessThan(String value) {
            addCriterion("\"rcpt_id\" <", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdLessThanOrEqualTo(String value) {
            addCriterion("\"rcpt_id\" <=", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdLike(String value) {
            addCriterion("\"rcpt_id\" like", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdNotLike(String value) {
            addCriterion("\"rcpt_id\" not like", value, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdIn(List<String> values) {
            addCriterion("\"rcpt_id\" in", values, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdNotIn(List<String> values) {
            addCriterion("\"rcpt_id\" not in", values, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdBetween(String value1, String value2) {
            addCriterion("\"rcpt_id\" between", value1, value2, "rcptId");
            return (Criteria) this;
        }

        public Criteria andRcptIdNotBetween(String value1, String value2) {
            addCriterion("\"rcpt_id\" not between", value1, value2, "rcptId");
            return (Criteria) this;
        }

        public Criteria andPayWayIsNull() {
            addCriterion("\"pay_way\" is null");
            return (Criteria) this;
        }

        public Criteria andPayWayIsNotNull() {
            addCriterion("\"pay_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andPayWayEqualTo(String value) {
            addCriterion("\"pay_way\" =", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotEqualTo(String value) {
            addCriterion("\"pay_way\" <>", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayGreaterThan(String value) {
            addCriterion("\"pay_way\" >", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"pay_way\" >=", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayLessThan(String value) {
            addCriterion("\"pay_way\" <", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayLessThanOrEqualTo(String value) {
            addCriterion("\"pay_way\" <=", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayLike(String value) {
            addCriterion("\"pay_way\" like", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotLike(String value) {
            addCriterion("\"pay_way\" not like", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayIn(List<String> values) {
            addCriterion("\"pay_way\" in", values, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotIn(List<String> values) {
            addCriterion("\"pay_way\" not in", values, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayBetween(String value1, String value2) {
            addCriterion("\"pay_way\" between", value1, value2, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotBetween(String value1, String value2) {
            addCriterion("\"pay_way\" not between", value1, value2, "payWay");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeIsNull() {
            addCriterion("\"insurance_type\" is null");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeIsNotNull() {
            addCriterion("\"insurance_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeEqualTo(String value) {
            addCriterion("\"insurance_type\" =", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeNotEqualTo(String value) {
            addCriterion("\"insurance_type\" <>", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeGreaterThan(String value) {
            addCriterion("\"insurance_type\" >", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"insurance_type\" >=", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeLessThan(String value) {
            addCriterion("\"insurance_type\" <", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeLessThanOrEqualTo(String value) {
            addCriterion("\"insurance_type\" <=", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeLike(String value) {
            addCriterion("\"insurance_type\" like", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeNotLike(String value) {
            addCriterion("\"insurance_type\" not like", value, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeIn(List<String> values) {
            addCriterion("\"insurance_type\" in", values, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeNotIn(List<String> values) {
            addCriterion("\"insurance_type\" not in", values, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeBetween(String value1, String value2) {
            addCriterion("\"insurance_type\" between", value1, value2, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andInsuranceTypeNotBetween(String value1, String value2) {
            addCriterion("\"insurance_type\" not between", value1, value2, "insuranceType");
            return (Criteria) this;
        }

        public Criteria andSettlingDateIsNull() {
            addCriterion("\"settling_date\" is null");
            return (Criteria) this;
        }

        public Criteria andSettlingDateIsNotNull() {
            addCriterion("\"settling_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andSettlingDateEqualTo(Date value) {
            addCriterion("\"settling_date\" =", value, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateNotEqualTo(Date value) {
            addCriterion("\"settling_date\" <>", value, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateGreaterThan(Date value) {
            addCriterion("\"settling_date\" >", value, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateGreaterThanOrEqualTo(Date value) {
            addCriterion("\"settling_date\" >=", value, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateLessThan(Date value) {
            addCriterion("\"settling_date\" <", value, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateLessThanOrEqualTo(Date value) {
            addCriterion("\"settling_date\" <=", value, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateIn(List<Date> values) {
            addCriterion("\"settling_date\" in", values, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateNotIn(List<Date> values) {
            addCriterion("\"settling_date\" not in", values, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateBetween(Date value1, Date value2) {
            addCriterion("\"settling_date\" between", value1, value2, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andSettlingDateNotBetween(Date value1, Date value2) {
            addCriterion("\"settling_date\" not between", value1, value2, "settlingDate");
            return (Criteria) this;
        }

        public Criteria andChargesIsNull() {
            addCriterion("\"charges\" is null");
            return (Criteria) this;
        }

        public Criteria andChargesIsNotNull() {
            addCriterion("\"charges\" is not null");
            return (Criteria) this;
        }

        public Criteria andChargesEqualTo(Double value) {
            addCriterion("\"charges\" =", value, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesNotEqualTo(Double value) {
            addCriterion("\"charges\" <>", value, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesGreaterThan(Double value) {
            addCriterion("\"charges\" >", value, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesGreaterThanOrEqualTo(Double value) {
            addCriterion("\"charges\" >=", value, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesLessThan(Double value) {
            addCriterion("\"charges\" <", value, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesLessThanOrEqualTo(Double value) {
            addCriterion("\"charges\" <=", value, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesIn(List<Double> values) {
            addCriterion("\"charges\" in", values, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesNotIn(List<Double> values) {
            addCriterion("\"charges\" not in", values, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesBetween(Double value1, Double value2) {
            addCriterion("\"charges\" between", value1, value2, "charges");
            return (Criteria) this;
        }

        public Criteria andChargesNotBetween(Double value1, Double value2) {
            addCriterion("\"charges\" not between", value1, value2, "charges");
            return (Criteria) this;
        }

        public Criteria andCostsIsNull() {
            addCriterion("\"costs\" is null");
            return (Criteria) this;
        }

        public Criteria andCostsIsNotNull() {
            addCriterion("\"costs\" is not null");
            return (Criteria) this;
        }

        public Criteria andCostsEqualTo(Double value) {
            addCriterion("\"costs\" =", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotEqualTo(Double value) {
            addCriterion("\"costs\" <>", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsGreaterThan(Double value) {
            addCriterion("\"costs\" >", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsGreaterThanOrEqualTo(Double value) {
            addCriterion("\"costs\" >=", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsLessThan(Double value) {
            addCriterion("\"costs\" <", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsLessThanOrEqualTo(Double value) {
            addCriterion("\"costs\" <=", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsIn(List<Double> values) {
            addCriterion("\"costs\" in", values, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotIn(List<Double> values) {
            addCriterion("\"costs\" not in", values, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsBetween(Double value1, Double value2) {
            addCriterion("\"costs\" between", value1, value2, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotBetween(Double value1, Double value2) {
            addCriterion("\"costs\" not between", value1, value2, "costs");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayIsNull() {
            addCriterion("\"insurance_self_pay\" is null");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayIsNotNull() {
            addCriterion("\"insurance_self_pay\" is not null");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayEqualTo(Double value) {
            addCriterion("\"insurance_self_pay\" =", value, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayNotEqualTo(Double value) {
            addCriterion("\"insurance_self_pay\" <>", value, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayGreaterThan(Double value) {
            addCriterion("\"insurance_self_pay\" >", value, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayGreaterThanOrEqualTo(Double value) {
            addCriterion("\"insurance_self_pay\" >=", value, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayLessThan(Double value) {
            addCriterion("\"insurance_self_pay\" <", value, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayLessThanOrEqualTo(Double value) {
            addCriterion("\"insurance_self_pay\" <=", value, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayIn(List<Double> values) {
            addCriterion("\"insurance_self_pay\" in", values, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayNotIn(List<Double> values) {
            addCriterion("\"insurance_self_pay\" not in", values, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayBetween(Double value1, Double value2) {
            addCriterion("\"insurance_self_pay\" between", value1, value2, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsuranceSelfPayNotBetween(Double value1, Double value2) {
            addCriterion("\"insurance_self_pay\" not between", value1, value2, "insuranceSelfPay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayIsNull() {
            addCriterion("\"insurance_pay\" is null");
            return (Criteria) this;
        }

        public Criteria andInsurancePayIsNotNull() {
            addCriterion("\"insurance_pay\" is not null");
            return (Criteria) this;
        }

        public Criteria andInsurancePayEqualTo(Double value) {
            addCriterion("\"insurance_pay\" =", value, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayNotEqualTo(Double value) {
            addCriterion("\"insurance_pay\" <>", value, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayGreaterThan(Double value) {
            addCriterion("\"insurance_pay\" >", value, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayGreaterThanOrEqualTo(Double value) {
            addCriterion("\"insurance_pay\" >=", value, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayLessThan(Double value) {
            addCriterion("\"insurance_pay\" <", value, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayLessThanOrEqualTo(Double value) {
            addCriterion("\"insurance_pay\" <=", value, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayIn(List<Double> values) {
            addCriterion("\"insurance_pay\" in", values, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayNotIn(List<Double> values) {
            addCriterion("\"insurance_pay\" not in", values, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayBetween(Double value1, Double value2) {
            addCriterion("\"insurance_pay\" between", value1, value2, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andInsurancePayNotBetween(Double value1, Double value2) {
            addCriterion("\"insurance_pay\" not between", value1, value2, "insurancePay");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeIsNull() {
            addCriterion("\"billing_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeIsNotNull() {
            addCriterion("\"billing_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeEqualTo(Date value) {
            addCriterion("\"billing_date_time\" =", value, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeNotEqualTo(Date value) {
            addCriterion("\"billing_date_time\" <>", value, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeGreaterThan(Date value) {
            addCriterion("\"billing_date_time\" >", value, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"billing_date_time\" >=", value, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeLessThan(Date value) {
            addCriterion("\"billing_date_time\" <", value, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"billing_date_time\" <=", value, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeIn(List<Date> values) {
            addCriterion("\"billing_date_time\" in", values, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeNotIn(List<Date> values) {
            addCriterion("\"billing_date_time\" not in", values, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"billing_date_time\" between", value1, value2, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andBillingDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"billing_date_time\" not between", value1, value2, "billingDateTime");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}