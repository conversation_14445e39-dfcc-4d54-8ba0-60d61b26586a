package com.haoys.rdr.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class RdrPatientDataBase implements Serializable {
    private String id;

    @ApiModelProperty(value = "数据库名称")
    private String dataBase;

    @ApiModelProperty(value = "数据库描述")
    private String dataDesc;

    @ApiModelProperty(value = "患者人数")
    private Integer patientNum;

    @ApiModelProperty(value = "科室")
    private String dept;

    @ApiModelProperty(value = "负责人")
    private String director;

    @ApiModelProperty(value = "数据来源：1.数据中心，2.专病库")
    private String dataSource;

    @ApiModelProperty(value = "数据库图像")
    private String dataImage;

    @ApiModelProperty(value = "入库规则")
    private String dataBaseRule;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "专病库的url")
    private String dataBaseUrl;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDataBase() {
        return dataBase;
    }

    public void setDataBase(String dataBase) {
        this.dataBase = dataBase;
    }

    public String getDataDesc() {
        return dataDesc;
    }

    public void setDataDesc(String dataDesc) {
        this.dataDesc = dataDesc;
    }

    public Integer getPatientNum() {
        return patientNum;
    }

    public void setPatientNum(Integer patientNum) {
        this.patientNum = patientNum;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getDirector() {
        return director;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getDataImage() {
        return dataImage;
    }

    public void setDataImage(String dataImage) {
        this.dataImage = dataImage;
    }

    public String getDataBaseRule() {
        return dataBaseRule;
    }

    public void setDataBaseRule(String dataBaseRule) {
        this.dataBaseRule = dataBaseRule;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDataBaseUrl() {
        return dataBaseUrl;
    }

    public void setDataBaseUrl(String dataBaseUrl) {
        this.dataBaseUrl = dataBaseUrl;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dataBase=").append(dataBase);
        sb.append(", dataDesc=").append(dataDesc);
        sb.append(", patientNum=").append(patientNum);
        sb.append(", dept=").append(dept);
        sb.append(", director=").append(director);
        sb.append(", dataSource=").append(dataSource);
        sb.append(", dataImage=").append(dataImage);
        sb.append(", dataBaseRule=").append(dataBaseRule);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createBy=").append(createBy);
        sb.append(", status=").append(status);
        sb.append(", dataBaseUrl=").append(dataBaseUrl);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}