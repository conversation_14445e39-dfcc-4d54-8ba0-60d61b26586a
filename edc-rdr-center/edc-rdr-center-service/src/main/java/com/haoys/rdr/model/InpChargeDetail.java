package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class InpChargeDetail implements Serializable {
    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "收费单号")
    private String rcptId;

    @ApiModelProperty(value = "费用项目子序号")
    private String itemNo;

    @ApiModelProperty(value = "对应医嘱序号")
    private String orderNo;

    @ApiModelProperty(value = "项目类别")
    private String itemClass;

    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @ApiModelProperty(value = "项目代码")
    private String itemCode;

    @ApiModelProperty(value = "项目规格")
    private String itemSpec;

    @ApiModelProperty(value = "数量")
    private Double amount;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单价")
    private Double unitPrice;

    @ApiModelProperty(value = "开单科室")
    private String orderDept;

    @ApiModelProperty(value = "执行科室")
    private String performDept;

    @ApiModelProperty(value = "应收费用")
    private Double charges;

    @ApiModelProperty(value = "实收费用")
    private Double costs;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    private static final long serialVersionUID = 1L;

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getRcptId() {
        return rcptId;
    }

    public void setRcptId(String rcptId) {
        this.rcptId = rcptId;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getItemClass() {
        return itemClass;
    }

    public void setItemClass(String itemClass) {
        this.itemClass = itemClass;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemSpec() {
        return itemSpec;
    }

    public void setItemSpec(String itemSpec) {
        this.itemSpec = itemSpec;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getOrderDept() {
        return orderDept;
    }

    public void setOrderDept(String orderDept) {
        this.orderDept = orderDept;
    }

    public String getPerformDept() {
        return performDept;
    }

    public void setPerformDept(String performDept) {
        this.performDept = performDept;
    }

    public Double getCharges() {
        return charges;
    }

    public void setCharges(Double charges) {
        this.charges = charges;
    }

    public Double getCosts() {
        return costs;
    }

    public void setCosts(Double costs) {
        this.costs = costs;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", pkId=").append(pkId);
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", rcptId=").append(rcptId);
        sb.append(", itemNo=").append(itemNo);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", itemClass=").append(itemClass);
        sb.append(", itemName=").append(itemName);
        sb.append(", itemCode=").append(itemCode);
        sb.append(", itemSpec=").append(itemSpec);
        sb.append(", amount=").append(amount);
        sb.append(", unit=").append(unit);
        sb.append(", unitPrice=").append(unitPrice);
        sb.append(", orderDept=").append(orderDept);
        sb.append(", performDept=").append(performDept);
        sb.append(", charges=").append(charges);
        sb.append(", costs=").append(costs);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", dataState=").append(dataState);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}