package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class EmrFirstCourseAfterOperExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public EmrFirstCourseAfterOperExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIsNull() {
            addCriterion("\"operation_time\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIsNotNull() {
            addCriterion("\"operation_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationTimeEqualTo(Date value) {
            addCriterion("\"operation_time\" =", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotEqualTo(Date value) {
            addCriterion("\"operation_time\" <>", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeGreaterThan(Date value) {
            addCriterion("\"operation_time\" >", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_time\" >=", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeLessThan(Date value) {
            addCriterion("\"operation_time\" <", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_time\" <=", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIn(List<Date> values) {
            addCriterion("\"operation_time\" in", values, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotIn(List<Date> values) {
            addCriterion("\"operation_time\" not in", values, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_time\" between", value1, value2, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_time\" not between", value1, value2, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIsNull() {
            addCriterion("\"operation_duration\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIsNotNull() {
            addCriterion("\"operation_duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationDurationEqualTo(String value) {
            addCriterion("\"operation_duration\" =", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotEqualTo(String value) {
            addCriterion("\"operation_duration\" <>", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationGreaterThan(String value) {
            addCriterion("\"operation_duration\" >", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_duration\" >=", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLessThan(String value) {
            addCriterion("\"operation_duration\" <", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLessThanOrEqualTo(String value) {
            addCriterion("\"operation_duration\" <=", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLike(String value) {
            addCriterion("\"operation_duration\" like", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotLike(String value) {
            addCriterion("\"operation_duration\" not like", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIn(List<String> values) {
            addCriterion("\"operation_duration\" in", values, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotIn(List<String> values) {
            addCriterion("\"operation_duration\" not in", values, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationBetween(String value1, String value2) {
            addCriterion("\"operation_duration\" between", value1, value2, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotBetween(String value1, String value2) {
            addCriterion("\"operation_duration\" not between", value1, value2, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationIsNull() {
            addCriterion("\"diag_perioperation\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationIsNotNull() {
            addCriterion("\"diag_perioperation\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationEqualTo(String value) {
            addCriterion("\"diag_perioperation\" =", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotEqualTo(String value) {
            addCriterion("\"diag_perioperation\" <>", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationGreaterThan(String value) {
            addCriterion("\"diag_perioperation\" >", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_perioperation\" >=", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationLessThan(String value) {
            addCriterion("\"diag_perioperation\" <", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationLessThanOrEqualTo(String value) {
            addCriterion("\"diag_perioperation\" <=", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationLike(String value) {
            addCriterion("\"diag_perioperation\" like", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotLike(String value) {
            addCriterion("\"diag_perioperation\" not like", value, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationIn(List<String> values) {
            addCriterion("\"diag_perioperation\" in", values, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotIn(List<String> values) {
            addCriterion("\"diag_perioperation\" not in", values, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationBetween(String value1, String value2) {
            addCriterion("\"diag_perioperation\" between", value1, value2, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andDiagPerioperationNotBetween(String value1, String value2) {
            addCriterion("\"diag_perioperation\" not between", value1, value2, "diagPerioperation");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNull() {
            addCriterion("\"anesthesia_method\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNotNull() {
            addCriterion("\"anesthesia_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodEqualTo(String value) {
            addCriterion("\"anesthesia_method\" =", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <>", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThan(String value) {
            addCriterion("\"anesthesia_method\" >", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" >=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThan(String value) {
            addCriterion("\"anesthesia_method\" <", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLike(String value) {
            addCriterion("\"anesthesia_method\" like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotLike(String value) {
            addCriterion("\"anesthesia_method\" not like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIn(List<String> values) {
            addCriterion("\"anesthesia_method\" in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotIn(List<String> values) {
            addCriterion("\"anesthesia_method\" not in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" not between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andOperationApproachIsNull() {
            addCriterion("\"operation_approach\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationApproachIsNotNull() {
            addCriterion("\"operation_approach\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationApproachEqualTo(String value) {
            addCriterion("\"operation_approach\" =", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachNotEqualTo(String value) {
            addCriterion("\"operation_approach\" <>", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachGreaterThan(String value) {
            addCriterion("\"operation_approach\" >", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_approach\" >=", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachLessThan(String value) {
            addCriterion("\"operation_approach\" <", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachLessThanOrEqualTo(String value) {
            addCriterion("\"operation_approach\" <=", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachLike(String value) {
            addCriterion("\"operation_approach\" like", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachNotLike(String value) {
            addCriterion("\"operation_approach\" not like", value, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachIn(List<String> values) {
            addCriterion("\"operation_approach\" in", values, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachNotIn(List<String> values) {
            addCriterion("\"operation_approach\" not in", values, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachBetween(String value1, String value2) {
            addCriterion("\"operation_approach\" between", value1, value2, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationApproachNotBetween(String value1, String value2) {
            addCriterion("\"operation_approach\" not between", value1, value2, "operationApproach");
            return (Criteria) this;
        }

        public Criteria andOperationProcessIsNull() {
            addCriterion("\"operation_process\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationProcessIsNotNull() {
            addCriterion("\"operation_process\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationProcessEqualTo(String value) {
            addCriterion("\"operation_process\" =", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessNotEqualTo(String value) {
            addCriterion("\"operation_process\" <>", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessGreaterThan(String value) {
            addCriterion("\"operation_process\" >", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_process\" >=", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessLessThan(String value) {
            addCriterion("\"operation_process\" <", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessLessThanOrEqualTo(String value) {
            addCriterion("\"operation_process\" <=", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessLike(String value) {
            addCriterion("\"operation_process\" like", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessNotLike(String value) {
            addCriterion("\"operation_process\" not like", value, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessIn(List<String> values) {
            addCriterion("\"operation_process\" in", values, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessNotIn(List<String> values) {
            addCriterion("\"operation_process\" not in", values, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessBetween(String value1, String value2) {
            addCriterion("\"operation_process\" between", value1, value2, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andOperationProcessNotBetween(String value1, String value2) {
            addCriterion("\"operation_process\" not between", value1, value2, "operationProcess");
            return (Criteria) this;
        }

        public Criteria andPostOperationIsNull() {
            addCriterion("\"post_operation\" is null");
            return (Criteria) this;
        }

        public Criteria andPostOperationIsNotNull() {
            addCriterion("\"post_operation\" is not null");
            return (Criteria) this;
        }

        public Criteria andPostOperationEqualTo(String value) {
            addCriterion("\"post_operation\" =", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationNotEqualTo(String value) {
            addCriterion("\"post_operation\" <>", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationGreaterThan(String value) {
            addCriterion("\"post_operation\" >", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationGreaterThanOrEqualTo(String value) {
            addCriterion("\"post_operation\" >=", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationLessThan(String value) {
            addCriterion("\"post_operation\" <", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationLessThanOrEqualTo(String value) {
            addCriterion("\"post_operation\" <=", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationLike(String value) {
            addCriterion("\"post_operation\" like", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationNotLike(String value) {
            addCriterion("\"post_operation\" not like", value, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationIn(List<String> values) {
            addCriterion("\"post_operation\" in", values, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationNotIn(List<String> values) {
            addCriterion("\"post_operation\" not in", values, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationBetween(String value1, String value2) {
            addCriterion("\"post_operation\" between", value1, value2, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOperationNotBetween(String value1, String value2) {
            addCriterion("\"post_operation\" not between", value1, value2, "postOperation");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationIsNull() {
            addCriterion("\"post_op_consideration\" is null");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationIsNotNull() {
            addCriterion("\"post_op_consideration\" is not null");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationEqualTo(String value) {
            addCriterion("\"post_op_consideration\" =", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationNotEqualTo(String value) {
            addCriterion("\"post_op_consideration\" <>", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationGreaterThan(String value) {
            addCriterion("\"post_op_consideration\" >", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationGreaterThanOrEqualTo(String value) {
            addCriterion("\"post_op_consideration\" >=", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationLessThan(String value) {
            addCriterion("\"post_op_consideration\" <", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationLessThanOrEqualTo(String value) {
            addCriterion("\"post_op_consideration\" <=", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationLike(String value) {
            addCriterion("\"post_op_consideration\" like", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationNotLike(String value) {
            addCriterion("\"post_op_consideration\" not like", value, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationIn(List<String> values) {
            addCriterion("\"post_op_consideration\" in", values, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationNotIn(List<String> values) {
            addCriterion("\"post_op_consideration\" not in", values, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationBetween(String value1, String value2) {
            addCriterion("\"post_op_consideration\" between", value1, value2, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andPostOpConsiderationNotBetween(String value1, String value2) {
            addCriterion("\"post_op_consideration\" not between", value1, value2, "postOpConsideration");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNull() {
            addCriterion("\"record_time\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIsNotNull() {
            addCriterion("\"record_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordTimeEqualTo(Date value) {
            addCriterion("\"record_time\" =", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotEqualTo(Date value) {
            addCriterion("\"record_time\" <>", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThan(Date value) {
            addCriterion("\"record_time\" >", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" >=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThan(Date value) {
            addCriterion("\"record_time\" <", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"record_time\" <=", value, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeIn(List<Date> values) {
            addCriterion("\"record_time\" in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotIn(List<Date> values) {
            addCriterion("\"record_time\" not in", values, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andRecordTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"record_time\" not between", value1, value2, "recordTime");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIsNull() {
            addCriterion("\"doctor_sign\" is null");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIsNotNull() {
            addCriterion("\"doctor_sign\" is not null");
            return (Criteria) this;
        }

        public Criteria andDoctorSignEqualTo(String value) {
            addCriterion("\"doctor_sign\" =", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotEqualTo(String value) {
            addCriterion("\"doctor_sign\" <>", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignGreaterThan(String value) {
            addCriterion("\"doctor_sign\" >", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignGreaterThanOrEqualTo(String value) {
            addCriterion("\"doctor_sign\" >=", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLessThan(String value) {
            addCriterion("\"doctor_sign\" <", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLessThanOrEqualTo(String value) {
            addCriterion("\"doctor_sign\" <=", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignLike(String value) {
            addCriterion("\"doctor_sign\" like", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotLike(String value) {
            addCriterion("\"doctor_sign\" not like", value, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignIn(List<String> values) {
            addCriterion("\"doctor_sign\" in", values, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotIn(List<String> values) {
            addCriterion("\"doctor_sign\" not in", values, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignBetween(String value1, String value2) {
            addCriterion("\"doctor_sign\" between", value1, value2, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andDoctorSignNotBetween(String value1, String value2) {
            addCriterion("\"doctor_sign\" not between", value1, value2, "doctorSign");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNull() {
            addCriterion("\"full_text\" is null");
            return (Criteria) this;
        }

        public Criteria andFullTextIsNotNull() {
            addCriterion("\"full_text\" is not null");
            return (Criteria) this;
        }

        public Criteria andFullTextEqualTo(String value) {
            addCriterion("\"full_text\" =", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotEqualTo(String value) {
            addCriterion("\"full_text\" <>", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThan(String value) {
            addCriterion("\"full_text\" >", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextGreaterThanOrEqualTo(String value) {
            addCriterion("\"full_text\" >=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThan(String value) {
            addCriterion("\"full_text\" <", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLessThanOrEqualTo(String value) {
            addCriterion("\"full_text\" <=", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextLike(String value) {
            addCriterion("\"full_text\" like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotLike(String value) {
            addCriterion("\"full_text\" not like", value, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextIn(List<String> values) {
            addCriterion("\"full_text\" in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotIn(List<String> values) {
            addCriterion("\"full_text\" not in", values, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextBetween(String value1, String value2) {
            addCriterion("\"full_text\" between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andFullTextNotBetween(String value1, String value2) {
            addCriterion("\"full_text\" not between", value1, value2, "fullText");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}