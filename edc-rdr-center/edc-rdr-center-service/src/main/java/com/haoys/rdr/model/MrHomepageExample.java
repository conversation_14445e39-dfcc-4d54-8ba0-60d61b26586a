package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MrHomepageExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MrHomepageExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalNameIsNull() {
            addCriterion("\"hospital_name\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalNameIsNotNull() {
            addCriterion("\"hospital_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalNameEqualTo(String value) {
            addCriterion("\"hospital_name\" =", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotEqualTo(String value) {
            addCriterion("\"hospital_name\" <>", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameGreaterThan(String value) {
            addCriterion("\"hospital_name\" >", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_name\" >=", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameLessThan(String value) {
            addCriterion("\"hospital_name\" <", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_name\" <=", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameLike(String value) {
            addCriterion("\"hospital_name\" like", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotLike(String value) {
            addCriterion("\"hospital_name\" not like", value, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameIn(List<String> values) {
            addCriterion("\"hospital_name\" in", values, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotIn(List<String> values) {
            addCriterion("\"hospital_name\" not in", values, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameBetween(String value1, String value2) {
            addCriterion("\"hospital_name\" between", value1, value2, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andHospitalNameNotBetween(String value1, String value2) {
            addCriterion("\"hospital_name\" not between", value1, value2, "hospitalName");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoIsNull() {
            addCriterion("\"insurance_card_no\" is null");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoIsNotNull() {
            addCriterion("\"insurance_card_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoEqualTo(String value) {
            addCriterion("\"insurance_card_no\" =", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoNotEqualTo(String value) {
            addCriterion("\"insurance_card_no\" <>", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoGreaterThan(String value) {
            addCriterion("\"insurance_card_no\" >", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"insurance_card_no\" >=", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoLessThan(String value) {
            addCriterion("\"insurance_card_no\" <", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoLessThanOrEqualTo(String value) {
            addCriterion("\"insurance_card_no\" <=", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoLike(String value) {
            addCriterion("\"insurance_card_no\" like", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoNotLike(String value) {
            addCriterion("\"insurance_card_no\" not like", value, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoIn(List<String> values) {
            addCriterion("\"insurance_card_no\" in", values, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoNotIn(List<String> values) {
            addCriterion("\"insurance_card_no\" not in", values, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoBetween(String value1, String value2) {
            addCriterion("\"insurance_card_no\" between", value1, value2, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andInsuranceCardNoNotBetween(String value1, String value2) {
            addCriterion("\"insurance_card_no\" not between", value1, value2, "insuranceCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoIsNull() {
            addCriterion("\"health_card_no\" is null");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoIsNotNull() {
            addCriterion("\"health_card_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoEqualTo(String value) {
            addCriterion("\"health_card_no\" =", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoNotEqualTo(String value) {
            addCriterion("\"health_card_no\" <>", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoGreaterThan(String value) {
            addCriterion("\"health_card_no\" >", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"health_card_no\" >=", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoLessThan(String value) {
            addCriterion("\"health_card_no\" <", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoLessThanOrEqualTo(String value) {
            addCriterion("\"health_card_no\" <=", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoLike(String value) {
            addCriterion("\"health_card_no\" like", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoNotLike(String value) {
            addCriterion("\"health_card_no\" not like", value, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoIn(List<String> values) {
            addCriterion("\"health_card_no\" in", values, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoNotIn(List<String> values) {
            addCriterion("\"health_card_no\" not in", values, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoBetween(String value1, String value2) {
            addCriterion("\"health_card_no\" between", value1, value2, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andHealthCardNoNotBetween(String value1, String value2) {
            addCriterion("\"health_card_no\" not between", value1, value2, "healthCardNo");
            return (Criteria) this;
        }

        public Criteria andPayWayIsNull() {
            addCriterion("\"pay_way\" is null");
            return (Criteria) this;
        }

        public Criteria andPayWayIsNotNull() {
            addCriterion("\"pay_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andPayWayEqualTo(String value) {
            addCriterion("\"pay_way\" =", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotEqualTo(String value) {
            addCriterion("\"pay_way\" <>", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayGreaterThan(String value) {
            addCriterion("\"pay_way\" >", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"pay_way\" >=", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayLessThan(String value) {
            addCriterion("\"pay_way\" <", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayLessThanOrEqualTo(String value) {
            addCriterion("\"pay_way\" <=", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayLike(String value) {
            addCriterion("\"pay_way\" like", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotLike(String value) {
            addCriterion("\"pay_way\" not like", value, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayIn(List<String> values) {
            addCriterion("\"pay_way\" in", values, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotIn(List<String> values) {
            addCriterion("\"pay_way\" not in", values, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayBetween(String value1, String value2) {
            addCriterion("\"pay_way\" between", value1, value2, "payWay");
            return (Criteria) this;
        }

        public Criteria andPayWayNotBetween(String value1, String value2) {
            addCriterion("\"pay_way\" not between", value1, value2, "payWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberIsNull() {
            addCriterion("\"admission_number\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberIsNotNull() {
            addCriterion("\"admission_number\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberEqualTo(Integer value) {
            addCriterion("\"admission_number\" =", value, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberNotEqualTo(Integer value) {
            addCriterion("\"admission_number\" <>", value, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberGreaterThan(Integer value) {
            addCriterion("\"admission_number\" >", value, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"admission_number\" >=", value, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberLessThan(Integer value) {
            addCriterion("\"admission_number\" <", value, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberLessThanOrEqualTo(Integer value) {
            addCriterion("\"admission_number\" <=", value, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberIn(List<Integer> values) {
            addCriterion("\"admission_number\" in", values, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberNotIn(List<Integer> values) {
            addCriterion("\"admission_number\" not in", values, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberBetween(Integer value1, Integer value2) {
            addCriterion("\"admission_number\" between", value1, value2, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andAdmissionNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("\"admission_number\" not between", value1, value2, "admissionNumber");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("\"name\" is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("\"name\" is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("\"name\" =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("\"name\" <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("\"name\" >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"name\" >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("\"name\" <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("\"name\" <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("\"name\" like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("\"name\" not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("\"name\" in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("\"name\" not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("\"name\" between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("\"name\" not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andGenderIsNull() {
            addCriterion("\"gender\" is null");
            return (Criteria) this;
        }

        public Criteria andGenderIsNotNull() {
            addCriterion("\"gender\" is not null");
            return (Criteria) this;
        }

        public Criteria andGenderEqualTo(String value) {
            addCriterion("\"gender\" =", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotEqualTo(String value) {
            addCriterion("\"gender\" <>", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThan(String value) {
            addCriterion("\"gender\" >", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderGreaterThanOrEqualTo(String value) {
            addCriterion("\"gender\" >=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThan(String value) {
            addCriterion("\"gender\" <", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLessThanOrEqualTo(String value) {
            addCriterion("\"gender\" <=", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderLike(String value) {
            addCriterion("\"gender\" like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotLike(String value) {
            addCriterion("\"gender\" not like", value, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderIn(List<String> values) {
            addCriterion("\"gender\" in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotIn(List<String> values) {
            addCriterion("\"gender\" not in", values, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderBetween(String value1, String value2) {
            addCriterion("\"gender\" between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andGenderNotBetween(String value1, String value2) {
            addCriterion("\"gender\" not between", value1, value2, "gender");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthIsNull() {
            addCriterion("\"date_of_birth\" is null");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthIsNotNull() {
            addCriterion("\"date_of_birth\" is not null");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthEqualTo(Date value) {
            addCriterion("\"date_of_birth\" =", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthNotEqualTo(Date value) {
            addCriterion("\"date_of_birth\" <>", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthGreaterThan(Date value) {
            addCriterion("\"date_of_birth\" >", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthGreaterThanOrEqualTo(Date value) {
            addCriterion("\"date_of_birth\" >=", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthLessThan(Date value) {
            addCriterion("\"date_of_birth\" <", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthLessThanOrEqualTo(Date value) {
            addCriterion("\"date_of_birth\" <=", value, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthIn(List<Date> values) {
            addCriterion("\"date_of_birth\" in", values, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthNotIn(List<Date> values) {
            addCriterion("\"date_of_birth\" not in", values, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthBetween(Date value1, Date value2) {
            addCriterion("\"date_of_birth\" between", value1, value2, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andDateOfBirthNotBetween(Date value1, Date value2) {
            addCriterion("\"date_of_birth\" not between", value1, value2, "dateOfBirth");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("\"age\" is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("\"age\" is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(String value) {
            addCriterion("\"age\" =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(String value) {
            addCriterion("\"age\" <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(String value) {
            addCriterion("\"age\" >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(String value) {
            addCriterion("\"age\" >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(String value) {
            addCriterion("\"age\" <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(String value) {
            addCriterion("\"age\" <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLike(String value) {
            addCriterion("\"age\" like", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotLike(String value) {
            addCriterion("\"age\" not like", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<String> values) {
            addCriterion("\"age\" in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<String> values) {
            addCriterion("\"age\" not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(String value1, String value2) {
            addCriterion("\"age\" between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(String value1, String value2) {
            addCriterion("\"age\" not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andMarriageIsNull() {
            addCriterion("\"marriage\" is null");
            return (Criteria) this;
        }

        public Criteria andMarriageIsNotNull() {
            addCriterion("\"marriage\" is not null");
            return (Criteria) this;
        }

        public Criteria andMarriageEqualTo(String value) {
            addCriterion("\"marriage\" =", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotEqualTo(String value) {
            addCriterion("\"marriage\" <>", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageGreaterThan(String value) {
            addCriterion("\"marriage\" >", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageGreaterThanOrEqualTo(String value) {
            addCriterion("\"marriage\" >=", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageLessThan(String value) {
            addCriterion("\"marriage\" <", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageLessThanOrEqualTo(String value) {
            addCriterion("\"marriage\" <=", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageLike(String value) {
            addCriterion("\"marriage\" like", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotLike(String value) {
            addCriterion("\"marriage\" not like", value, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageIn(List<String> values) {
            addCriterion("\"marriage\" in", values, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotIn(List<String> values) {
            addCriterion("\"marriage\" not in", values, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageBetween(String value1, String value2) {
            addCriterion("\"marriage\" between", value1, value2, "marriage");
            return (Criteria) this;
        }

        public Criteria andMarriageNotBetween(String value1, String value2) {
            addCriterion("\"marriage\" not between", value1, value2, "marriage");
            return (Criteria) this;
        }

        public Criteria andOccupationIsNull() {
            addCriterion("\"occupation\" is null");
            return (Criteria) this;
        }

        public Criteria andOccupationIsNotNull() {
            addCriterion("\"occupation\" is not null");
            return (Criteria) this;
        }

        public Criteria andOccupationEqualTo(String value) {
            addCriterion("\"occupation\" =", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationNotEqualTo(String value) {
            addCriterion("\"occupation\" <>", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationGreaterThan(String value) {
            addCriterion("\"occupation\" >", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationGreaterThanOrEqualTo(String value) {
            addCriterion("\"occupation\" >=", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationLessThan(String value) {
            addCriterion("\"occupation\" <", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationLessThanOrEqualTo(String value) {
            addCriterion("\"occupation\" <=", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationLike(String value) {
            addCriterion("\"occupation\" like", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationNotLike(String value) {
            addCriterion("\"occupation\" not like", value, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationIn(List<String> values) {
            addCriterion("\"occupation\" in", values, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationNotIn(List<String> values) {
            addCriterion("\"occupation\" not in", values, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationBetween(String value1, String value2) {
            addCriterion("\"occupation\" between", value1, value2, "occupation");
            return (Criteria) this;
        }

        public Criteria andOccupationNotBetween(String value1, String value2) {
            addCriterion("\"occupation\" not between", value1, value2, "occupation");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIsNull() {
            addCriterion("\"birth_place\" is null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIsNotNull() {
            addCriterion("\"birth_place\" is not null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceEqualTo(String value) {
            addCriterion("\"birth_place\" =", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotEqualTo(String value) {
            addCriterion("\"birth_place\" <>", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceGreaterThan(String value) {
            addCriterion("\"birth_place\" >", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceGreaterThanOrEqualTo(String value) {
            addCriterion("\"birth_place\" >=", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLessThan(String value) {
            addCriterion("\"birth_place\" <", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLessThanOrEqualTo(String value) {
            addCriterion("\"birth_place\" <=", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceLike(String value) {
            addCriterion("\"birth_place\" like", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotLike(String value) {
            addCriterion("\"birth_place\" not like", value, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceIn(List<String> values) {
            addCriterion("\"birth_place\" in", values, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotIn(List<String> values) {
            addCriterion("\"birth_place\" not in", values, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceBetween(String value1, String value2) {
            addCriterion("\"birth_place\" between", value1, value2, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceNotBetween(String value1, String value2) {
            addCriterion("\"birth_place\" not between", value1, value2, "birthPlace");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceIsNull() {
            addCriterion("\"birth_place_province\" is null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceIsNotNull() {
            addCriterion("\"birth_place_province\" is not null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceEqualTo(String value) {
            addCriterion("\"birth_place_province\" =", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceNotEqualTo(String value) {
            addCriterion("\"birth_place_province\" <>", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceGreaterThan(String value) {
            addCriterion("\"birth_place_province\" >", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("\"birth_place_province\" >=", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceLessThan(String value) {
            addCriterion("\"birth_place_province\" <", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceLessThanOrEqualTo(String value) {
            addCriterion("\"birth_place_province\" <=", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceLike(String value) {
            addCriterion("\"birth_place_province\" like", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceNotLike(String value) {
            addCriterion("\"birth_place_province\" not like", value, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceIn(List<String> values) {
            addCriterion("\"birth_place_province\" in", values, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceNotIn(List<String> values) {
            addCriterion("\"birth_place_province\" not in", values, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceBetween(String value1, String value2) {
            addCriterion("\"birth_place_province\" between", value1, value2, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceProvinceNotBetween(String value1, String value2) {
            addCriterion("\"birth_place_province\" not between", value1, value2, "birthPlaceProvince");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityIsNull() {
            addCriterion("\"birth_place_city\" is null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityIsNotNull() {
            addCriterion("\"birth_place_city\" is not null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityEqualTo(String value) {
            addCriterion("\"birth_place_city\" =", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityNotEqualTo(String value) {
            addCriterion("\"birth_place_city\" <>", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityGreaterThan(String value) {
            addCriterion("\"birth_place_city\" >", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityGreaterThanOrEqualTo(String value) {
            addCriterion("\"birth_place_city\" >=", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityLessThan(String value) {
            addCriterion("\"birth_place_city\" <", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityLessThanOrEqualTo(String value) {
            addCriterion("\"birth_place_city\" <=", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityLike(String value) {
            addCriterion("\"birth_place_city\" like", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityNotLike(String value) {
            addCriterion("\"birth_place_city\" not like", value, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityIn(List<String> values) {
            addCriterion("\"birth_place_city\" in", values, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityNotIn(List<String> values) {
            addCriterion("\"birth_place_city\" not in", values, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityBetween(String value1, String value2) {
            addCriterion("\"birth_place_city\" between", value1, value2, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCityNotBetween(String value1, String value2) {
            addCriterion("\"birth_place_city\" not between", value1, value2, "birthPlaceCity");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryIsNull() {
            addCriterion("\"birth_place_country\" is null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryIsNotNull() {
            addCriterion("\"birth_place_country\" is not null");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryEqualTo(String value) {
            addCriterion("\"birth_place_country\" =", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryNotEqualTo(String value) {
            addCriterion("\"birth_place_country\" <>", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryGreaterThan(String value) {
            addCriterion("\"birth_place_country\" >", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryGreaterThanOrEqualTo(String value) {
            addCriterion("\"birth_place_country\" >=", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryLessThan(String value) {
            addCriterion("\"birth_place_country\" <", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryLessThanOrEqualTo(String value) {
            addCriterion("\"birth_place_country\" <=", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryLike(String value) {
            addCriterion("\"birth_place_country\" like", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryNotLike(String value) {
            addCriterion("\"birth_place_country\" not like", value, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryIn(List<String> values) {
            addCriterion("\"birth_place_country\" in", values, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryNotIn(List<String> values) {
            addCriterion("\"birth_place_country\" not in", values, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryBetween(String value1, String value2) {
            addCriterion("\"birth_place_country\" between", value1, value2, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andBirthPlaceCountryNotBetween(String value1, String value2) {
            addCriterion("\"birth_place_country\" not between", value1, value2, "birthPlaceCountry");
            return (Criteria) this;
        }

        public Criteria andNationIsNull() {
            addCriterion("\"nation\" is null");
            return (Criteria) this;
        }

        public Criteria andNationIsNotNull() {
            addCriterion("\"nation\" is not null");
            return (Criteria) this;
        }

        public Criteria andNationEqualTo(String value) {
            addCriterion("\"nation\" =", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotEqualTo(String value) {
            addCriterion("\"nation\" <>", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationGreaterThan(String value) {
            addCriterion("\"nation\" >", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationGreaterThanOrEqualTo(String value) {
            addCriterion("\"nation\" >=", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationLessThan(String value) {
            addCriterion("\"nation\" <", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationLessThanOrEqualTo(String value) {
            addCriterion("\"nation\" <=", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationLike(String value) {
            addCriterion("\"nation\" like", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotLike(String value) {
            addCriterion("\"nation\" not like", value, "nation");
            return (Criteria) this;
        }

        public Criteria andNationIn(List<String> values) {
            addCriterion("\"nation\" in", values, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotIn(List<String> values) {
            addCriterion("\"nation\" not in", values, "nation");
            return (Criteria) this;
        }

        public Criteria andNationBetween(String value1, String value2) {
            addCriterion("\"nation\" between", value1, value2, "nation");
            return (Criteria) this;
        }

        public Criteria andNationNotBetween(String value1, String value2) {
            addCriterion("\"nation\" not between", value1, value2, "nation");
            return (Criteria) this;
        }

        public Criteria andCitizenshipIsNull() {
            addCriterion("\"citizenship\" is null");
            return (Criteria) this;
        }

        public Criteria andCitizenshipIsNotNull() {
            addCriterion("\"citizenship\" is not null");
            return (Criteria) this;
        }

        public Criteria andCitizenshipEqualTo(String value) {
            addCriterion("\"citizenship\" =", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipNotEqualTo(String value) {
            addCriterion("\"citizenship\" <>", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipGreaterThan(String value) {
            addCriterion("\"citizenship\" >", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipGreaterThanOrEqualTo(String value) {
            addCriterion("\"citizenship\" >=", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipLessThan(String value) {
            addCriterion("\"citizenship\" <", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipLessThanOrEqualTo(String value) {
            addCriterion("\"citizenship\" <=", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipLike(String value) {
            addCriterion("\"citizenship\" like", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipNotLike(String value) {
            addCriterion("\"citizenship\" not like", value, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipIn(List<String> values) {
            addCriterion("\"citizenship\" in", values, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipNotIn(List<String> values) {
            addCriterion("\"citizenship\" not in", values, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipBetween(String value1, String value2) {
            addCriterion("\"citizenship\" between", value1, value2, "citizenship");
            return (Criteria) this;
        }

        public Criteria andCitizenshipNotBetween(String value1, String value2) {
            addCriterion("\"citizenship\" not between", value1, value2, "citizenship");
            return (Criteria) this;
        }

        public Criteria andIdNoIsNull() {
            addCriterion("\"id_no\" is null");
            return (Criteria) this;
        }

        public Criteria andIdNoIsNotNull() {
            addCriterion("\"id_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andIdNoEqualTo(String value) {
            addCriterion("\"id_no\" =", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoNotEqualTo(String value) {
            addCriterion("\"id_no\" <>", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoGreaterThan(String value) {
            addCriterion("\"id_no\" >", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"id_no\" >=", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoLessThan(String value) {
            addCriterion("\"id_no\" <", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoLessThanOrEqualTo(String value) {
            addCriterion("\"id_no\" <=", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoLike(String value) {
            addCriterion("\"id_no\" like", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoNotLike(String value) {
            addCriterion("\"id_no\" not like", value, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoIn(List<String> values) {
            addCriterion("\"id_no\" in", values, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoNotIn(List<String> values) {
            addCriterion("\"id_no\" not in", values, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoBetween(String value1, String value2) {
            addCriterion("\"id_no\" between", value1, value2, "idNo");
            return (Criteria) this;
        }

        public Criteria andIdNoNotBetween(String value1, String value2) {
            addCriterion("\"id_no\" not between", value1, value2, "idNo");
            return (Criteria) this;
        }

        public Criteria andHomeAdressIsNull() {
            addCriterion("\"home_adress\" is null");
            return (Criteria) this;
        }

        public Criteria andHomeAdressIsNotNull() {
            addCriterion("\"home_adress\" is not null");
            return (Criteria) this;
        }

        public Criteria andHomeAdressEqualTo(String value) {
            addCriterion("\"home_adress\" =", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressNotEqualTo(String value) {
            addCriterion("\"home_adress\" <>", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressGreaterThan(String value) {
            addCriterion("\"home_adress\" >", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressGreaterThanOrEqualTo(String value) {
            addCriterion("\"home_adress\" >=", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressLessThan(String value) {
            addCriterion("\"home_adress\" <", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressLessThanOrEqualTo(String value) {
            addCriterion("\"home_adress\" <=", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressLike(String value) {
            addCriterion("\"home_adress\" like", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressNotLike(String value) {
            addCriterion("\"home_adress\" not like", value, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressIn(List<String> values) {
            addCriterion("\"home_adress\" in", values, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressNotIn(List<String> values) {
            addCriterion("\"home_adress\" not in", values, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressBetween(String value1, String value2) {
            addCriterion("\"home_adress\" between", value1, value2, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomeAdressNotBetween(String value1, String value2) {
            addCriterion("\"home_adress\" not between", value1, value2, "homeAdress");
            return (Criteria) this;
        }

        public Criteria andHomePhoneIsNull() {
            addCriterion("\"home_phone\" is null");
            return (Criteria) this;
        }

        public Criteria andHomePhoneIsNotNull() {
            addCriterion("\"home_phone\" is not null");
            return (Criteria) this;
        }

        public Criteria andHomePhoneEqualTo(String value) {
            addCriterion("\"home_phone\" =", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneNotEqualTo(String value) {
            addCriterion("\"home_phone\" <>", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneGreaterThan(String value) {
            addCriterion("\"home_phone\" >", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneGreaterThanOrEqualTo(String value) {
            addCriterion("\"home_phone\" >=", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneLessThan(String value) {
            addCriterion("\"home_phone\" <", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneLessThanOrEqualTo(String value) {
            addCriterion("\"home_phone\" <=", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneLike(String value) {
            addCriterion("\"home_phone\" like", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneNotLike(String value) {
            addCriterion("\"home_phone\" not like", value, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneIn(List<String> values) {
            addCriterion("\"home_phone\" in", values, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneNotIn(List<String> values) {
            addCriterion("\"home_phone\" not in", values, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneBetween(String value1, String value2) {
            addCriterion("\"home_phone\" between", value1, value2, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomePhoneNotBetween(String value1, String value2) {
            addCriterion("\"home_phone\" not between", value1, value2, "homePhone");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeIsNull() {
            addCriterion("\"home_adress_postcode\" is null");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeIsNotNull() {
            addCriterion("\"home_adress_postcode\" is not null");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeEqualTo(String value) {
            addCriterion("\"home_adress_postcode\" =", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeNotEqualTo(String value) {
            addCriterion("\"home_adress_postcode\" <>", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeGreaterThan(String value) {
            addCriterion("\"home_adress_postcode\" >", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"home_adress_postcode\" >=", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeLessThan(String value) {
            addCriterion("\"home_adress_postcode\" <", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeLessThanOrEqualTo(String value) {
            addCriterion("\"home_adress_postcode\" <=", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeLike(String value) {
            addCriterion("\"home_adress_postcode\" like", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeNotLike(String value) {
            addCriterion("\"home_adress_postcode\" not like", value, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeIn(List<String> values) {
            addCriterion("\"home_adress_postcode\" in", values, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeNotIn(List<String> values) {
            addCriterion("\"home_adress_postcode\" not in", values, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeBetween(String value1, String value2) {
            addCriterion("\"home_adress_postcode\" between", value1, value2, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andHomeAdressPostcodeNotBetween(String value1, String value2) {
            addCriterion("\"home_adress_postcode\" not between", value1, value2, "homeAdressPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressIsNull() {
            addCriterion("\"work_unit_and_adress\" is null");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressIsNotNull() {
            addCriterion("\"work_unit_and_adress\" is not null");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressEqualTo(String value) {
            addCriterion("\"work_unit_and_adress\" =", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressNotEqualTo(String value) {
            addCriterion("\"work_unit_and_adress\" <>", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressGreaterThan(String value) {
            addCriterion("\"work_unit_and_adress\" >", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressGreaterThanOrEqualTo(String value) {
            addCriterion("\"work_unit_and_adress\" >=", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressLessThan(String value) {
            addCriterion("\"work_unit_and_adress\" <", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressLessThanOrEqualTo(String value) {
            addCriterion("\"work_unit_and_adress\" <=", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressLike(String value) {
            addCriterion("\"work_unit_and_adress\" like", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressNotLike(String value) {
            addCriterion("\"work_unit_and_adress\" not like", value, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressIn(List<String> values) {
            addCriterion("\"work_unit_and_adress\" in", values, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressNotIn(List<String> values) {
            addCriterion("\"work_unit_and_adress\" not in", values, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressBetween(String value1, String value2) {
            addCriterion("\"work_unit_and_adress\" between", value1, value2, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andWorkUnitAndAdressNotBetween(String value1, String value2) {
            addCriterion("\"work_unit_and_adress\" not between", value1, value2, "workUnitAndAdress");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneIsNull() {
            addCriterion("\"contact_telephone\" is null");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneIsNotNull() {
            addCriterion("\"contact_telephone\" is not null");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneEqualTo(String value) {
            addCriterion("\"contact_telephone\" =", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneNotEqualTo(String value) {
            addCriterion("\"contact_telephone\" <>", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneGreaterThan(String value) {
            addCriterion("\"contact_telephone\" >", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("\"contact_telephone\" >=", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneLessThan(String value) {
            addCriterion("\"contact_telephone\" <", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneLessThanOrEqualTo(String value) {
            addCriterion("\"contact_telephone\" <=", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneLike(String value) {
            addCriterion("\"contact_telephone\" like", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneNotLike(String value) {
            addCriterion("\"contact_telephone\" not like", value, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneIn(List<String> values) {
            addCriterion("\"contact_telephone\" in", values, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneNotIn(List<String> values) {
            addCriterion("\"contact_telephone\" not in", values, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneBetween(String value1, String value2) {
            addCriterion("\"contact_telephone\" between", value1, value2, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andContactTelephoneNotBetween(String value1, String value2) {
            addCriterion("\"contact_telephone\" not between", value1, value2, "contactTelephone");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeIsNull() {
            addCriterion("\"work_unit_postcode\" is null");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeIsNotNull() {
            addCriterion("\"work_unit_postcode\" is not null");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeEqualTo(String value) {
            addCriterion("\"work_unit_postcode\" =", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeNotEqualTo(String value) {
            addCriterion("\"work_unit_postcode\" <>", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeGreaterThan(String value) {
            addCriterion("\"work_unit_postcode\" >", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"work_unit_postcode\" >=", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeLessThan(String value) {
            addCriterion("\"work_unit_postcode\" <", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeLessThanOrEqualTo(String value) {
            addCriterion("\"work_unit_postcode\" <=", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeLike(String value) {
            addCriterion("\"work_unit_postcode\" like", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeNotLike(String value) {
            addCriterion("\"work_unit_postcode\" not like", value, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeIn(List<String> values) {
            addCriterion("\"work_unit_postcode\" in", values, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeNotIn(List<String> values) {
            addCriterion("\"work_unit_postcode\" not in", values, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeBetween(String value1, String value2) {
            addCriterion("\"work_unit_postcode\" between", value1, value2, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andWorkUnitPostcodeNotBetween(String value1, String value2) {
            addCriterion("\"work_unit_postcode\" not between", value1, value2, "workUnitPostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceIsNull() {
            addCriterion("\"registered_residence\" is null");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceIsNotNull() {
            addCriterion("\"registered_residence\" is not null");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceEqualTo(String value) {
            addCriterion("\"registered_residence\" =", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceNotEqualTo(String value) {
            addCriterion("\"registered_residence\" <>", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceGreaterThan(String value) {
            addCriterion("\"registered_residence\" >", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceGreaterThanOrEqualTo(String value) {
            addCriterion("\"registered_residence\" >=", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceLessThan(String value) {
            addCriterion("\"registered_residence\" <", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceLessThanOrEqualTo(String value) {
            addCriterion("\"registered_residence\" <=", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceLike(String value) {
            addCriterion("\"registered_residence\" like", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceNotLike(String value) {
            addCriterion("\"registered_residence\" not like", value, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceIn(List<String> values) {
            addCriterion("\"registered_residence\" in", values, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceNotIn(List<String> values) {
            addCriterion("\"registered_residence\" not in", values, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceBetween(String value1, String value2) {
            addCriterion("\"registered_residence\" between", value1, value2, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidenceNotBetween(String value1, String value2) {
            addCriterion("\"registered_residence\" not between", value1, value2, "registeredResidence");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeIsNull() {
            addCriterion("\"registered_residence_postcode\" is null");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeIsNotNull() {
            addCriterion("\"registered_residence_postcode\" is not null");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeEqualTo(String value) {
            addCriterion("\"registered_residence_postcode\" =", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeNotEqualTo(String value) {
            addCriterion("\"registered_residence_postcode\" <>", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeGreaterThan(String value) {
            addCriterion("\"registered_residence_postcode\" >", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"registered_residence_postcode\" >=", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeLessThan(String value) {
            addCriterion("\"registered_residence_postcode\" <", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeLessThanOrEqualTo(String value) {
            addCriterion("\"registered_residence_postcode\" <=", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeLike(String value) {
            addCriterion("\"registered_residence_postcode\" like", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeNotLike(String value) {
            addCriterion("\"registered_residence_postcode\" not like", value, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeIn(List<String> values) {
            addCriterion("\"registered_residence_postcode\" in", values, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeNotIn(List<String> values) {
            addCriterion("\"registered_residence_postcode\" not in", values, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeBetween(String value1, String value2) {
            addCriterion("\"registered_residence_postcode\" between", value1, value2, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andRegisteredResidencePostcodeNotBetween(String value1, String value2) {
            addCriterion("\"registered_residence_postcode\" not between", value1, value2, "registeredResidencePostcode");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNull() {
            addCriterion("\"contact_name\" is null");
            return (Criteria) this;
        }

        public Criteria andContactNameIsNotNull() {
            addCriterion("\"contact_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andContactNameEqualTo(String value) {
            addCriterion("\"contact_name\" =", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotEqualTo(String value) {
            addCriterion("\"contact_name\" <>", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThan(String value) {
            addCriterion("\"contact_name\" >", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"contact_name\" >=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThan(String value) {
            addCriterion("\"contact_name\" <", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLessThanOrEqualTo(String value) {
            addCriterion("\"contact_name\" <=", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameLike(String value) {
            addCriterion("\"contact_name\" like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotLike(String value) {
            addCriterion("\"contact_name\" not like", value, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameIn(List<String> values) {
            addCriterion("\"contact_name\" in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotIn(List<String> values) {
            addCriterion("\"contact_name\" not in", values, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameBetween(String value1, String value2) {
            addCriterion("\"contact_name\" between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactNameNotBetween(String value1, String value2) {
            addCriterion("\"contact_name\" not between", value1, value2, "contactName");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipIsNull() {
            addCriterion("\"contact_relationship\" is null");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipIsNotNull() {
            addCriterion("\"contact_relationship\" is not null");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipEqualTo(String value) {
            addCriterion("\"contact_relationship\" =", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipNotEqualTo(String value) {
            addCriterion("\"contact_relationship\" <>", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipGreaterThan(String value) {
            addCriterion("\"contact_relationship\" >", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipGreaterThanOrEqualTo(String value) {
            addCriterion("\"contact_relationship\" >=", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipLessThan(String value) {
            addCriterion("\"contact_relationship\" <", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipLessThanOrEqualTo(String value) {
            addCriterion("\"contact_relationship\" <=", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipLike(String value) {
            addCriterion("\"contact_relationship\" like", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipNotLike(String value) {
            addCriterion("\"contact_relationship\" not like", value, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipIn(List<String> values) {
            addCriterion("\"contact_relationship\" in", values, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipNotIn(List<String> values) {
            addCriterion("\"contact_relationship\" not in", values, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipBetween(String value1, String value2) {
            addCriterion("\"contact_relationship\" between", value1, value2, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactRelationshipNotBetween(String value1, String value2) {
            addCriterion("\"contact_relationship\" not between", value1, value2, "contactRelationship");
            return (Criteria) this;
        }

        public Criteria andContactAdressIsNull() {
            addCriterion("\"contact_adress\" is null");
            return (Criteria) this;
        }

        public Criteria andContactAdressIsNotNull() {
            addCriterion("\"contact_adress\" is not null");
            return (Criteria) this;
        }

        public Criteria andContactAdressEqualTo(String value) {
            addCriterion("\"contact_adress\" =", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressNotEqualTo(String value) {
            addCriterion("\"contact_adress\" <>", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressGreaterThan(String value) {
            addCriterion("\"contact_adress\" >", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressGreaterThanOrEqualTo(String value) {
            addCriterion("\"contact_adress\" >=", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressLessThan(String value) {
            addCriterion("\"contact_adress\" <", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressLessThanOrEqualTo(String value) {
            addCriterion("\"contact_adress\" <=", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressLike(String value) {
            addCriterion("\"contact_adress\" like", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressNotLike(String value) {
            addCriterion("\"contact_adress\" not like", value, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressIn(List<String> values) {
            addCriterion("\"contact_adress\" in", values, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressNotIn(List<String> values) {
            addCriterion("\"contact_adress\" not in", values, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressBetween(String value1, String value2) {
            addCriterion("\"contact_adress\" between", value1, value2, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactAdressNotBetween(String value1, String value2) {
            addCriterion("\"contact_adress\" not between", value1, value2, "contactAdress");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNull() {
            addCriterion("\"contact_phone\" is null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIsNotNull() {
            addCriterion("\"contact_phone\" is not null");
            return (Criteria) this;
        }

        public Criteria andContactPhoneEqualTo(String value) {
            addCriterion("\"contact_phone\" =", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotEqualTo(String value) {
            addCriterion("\"contact_phone\" <>", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThan(String value) {
            addCriterion("\"contact_phone\" >", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("\"contact_phone\" >=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThan(String value) {
            addCriterion("\"contact_phone\" <", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLessThanOrEqualTo(String value) {
            addCriterion("\"contact_phone\" <=", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneLike(String value) {
            addCriterion("\"contact_phone\" like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotLike(String value) {
            addCriterion("\"contact_phone\" not like", value, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneIn(List<String> values) {
            addCriterion("\"contact_phone\" in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotIn(List<String> values) {
            addCriterion("\"contact_phone\" not in", values, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneBetween(String value1, String value2) {
            addCriterion("\"contact_phone\" between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andContactPhoneNotBetween(String value1, String value2) {
            addCriterion("\"contact_phone\" not between", value1, value2, "contactPhone");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayIsNull() {
            addCriterion("\"admission_way\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayIsNotNull() {
            addCriterion("\"admission_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayEqualTo(String value) {
            addCriterion("\"admission_way\" =", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotEqualTo(String value) {
            addCriterion("\"admission_way\" <>", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayGreaterThan(String value) {
            addCriterion("\"admission_way\" >", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"admission_way\" >=", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayLessThan(String value) {
            addCriterion("\"admission_way\" <", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayLessThanOrEqualTo(String value) {
            addCriterion("\"admission_way\" <=", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayLike(String value) {
            addCriterion("\"admission_way\" like", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotLike(String value) {
            addCriterion("\"admission_way\" not like", value, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayIn(List<String> values) {
            addCriterion("\"admission_way\" in", values, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotIn(List<String> values) {
            addCriterion("\"admission_way\" not in", values, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayBetween(String value1, String value2) {
            addCriterion("\"admission_way\" between", value1, value2, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionWayNotBetween(String value1, String value2) {
            addCriterion("\"admission_way\" not between", value1, value2, "admissionWay");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeIsNull() {
            addCriterion("\"admission_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeIsNotNull() {
            addCriterion("\"admission_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeEqualTo(Date value) {
            addCriterion("\"admission_date_time\" =", value, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeNotEqualTo(Date value) {
            addCriterion("\"admission_date_time\" <>", value, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeGreaterThan(Date value) {
            addCriterion("\"admission_date_time\" >", value, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"admission_date_time\" >=", value, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeLessThan(Date value) {
            addCriterion("\"admission_date_time\" <", value, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"admission_date_time\" <=", value, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeIn(List<Date> values) {
            addCriterion("\"admission_date_time\" in", values, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeNotIn(List<Date> values) {
            addCriterion("\"admission_date_time\" not in", values, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"admission_date_time\" between", value1, value2, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andAdmissionDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"admission_date_time\" not between", value1, value2, "admissionDateTime");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToIsNull() {
            addCriterion("\"dept_admission_to\" is null");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToIsNotNull() {
            addCriterion("\"dept_admission_to\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToEqualTo(String value) {
            addCriterion("\"dept_admission_to\" =", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToNotEqualTo(String value) {
            addCriterion("\"dept_admission_to\" <>", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToGreaterThan(String value) {
            addCriterion("\"dept_admission_to\" >", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToGreaterThanOrEqualTo(String value) {
            addCriterion("\"dept_admission_to\" >=", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToLessThan(String value) {
            addCriterion("\"dept_admission_to\" <", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToLessThanOrEqualTo(String value) {
            addCriterion("\"dept_admission_to\" <=", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToLike(String value) {
            addCriterion("\"dept_admission_to\" like", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToNotLike(String value) {
            addCriterion("\"dept_admission_to\" not like", value, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToIn(List<String> values) {
            addCriterion("\"dept_admission_to\" in", values, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToNotIn(List<String> values) {
            addCriterion("\"dept_admission_to\" not in", values, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToBetween(String value1, String value2) {
            addCriterion("\"dept_admission_to\" between", value1, value2, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptAdmissionToNotBetween(String value1, String value2) {
            addCriterion("\"dept_admission_to\" not between", value1, value2, "deptAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToIsNull() {
            addCriterion("\"ward_admission_to\" is null");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToIsNotNull() {
            addCriterion("\"ward_admission_to\" is not null");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToEqualTo(String value) {
            addCriterion("\"ward_admission_to\" =", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToNotEqualTo(String value) {
            addCriterion("\"ward_admission_to\" <>", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToGreaterThan(String value) {
            addCriterion("\"ward_admission_to\" >", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToGreaterThanOrEqualTo(String value) {
            addCriterion("\"ward_admission_to\" >=", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToLessThan(String value) {
            addCriterion("\"ward_admission_to\" <", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToLessThanOrEqualTo(String value) {
            addCriterion("\"ward_admission_to\" <=", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToLike(String value) {
            addCriterion("\"ward_admission_to\" like", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToNotLike(String value) {
            addCriterion("\"ward_admission_to\" not like", value, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToIn(List<String> values) {
            addCriterion("\"ward_admission_to\" in", values, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToNotIn(List<String> values) {
            addCriterion("\"ward_admission_to\" not in", values, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToBetween(String value1, String value2) {
            addCriterion("\"ward_admission_to\" between", value1, value2, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andWardAdmissionToNotBetween(String value1, String value2) {
            addCriterion("\"ward_admission_to\" not between", value1, value2, "wardAdmissionTo");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromIsNull() {
            addCriterion("\"dept_transfer_from\" is null");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromIsNotNull() {
            addCriterion("\"dept_transfer_from\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromEqualTo(String value) {
            addCriterion("\"dept_transfer_from\" =", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromNotEqualTo(String value) {
            addCriterion("\"dept_transfer_from\" <>", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromGreaterThan(String value) {
            addCriterion("\"dept_transfer_from\" >", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromGreaterThanOrEqualTo(String value) {
            addCriterion("\"dept_transfer_from\" >=", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromLessThan(String value) {
            addCriterion("\"dept_transfer_from\" <", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromLessThanOrEqualTo(String value) {
            addCriterion("\"dept_transfer_from\" <=", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromLike(String value) {
            addCriterion("\"dept_transfer_from\" like", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromNotLike(String value) {
            addCriterion("\"dept_transfer_from\" not like", value, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromIn(List<String> values) {
            addCriterion("\"dept_transfer_from\" in", values, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromNotIn(List<String> values) {
            addCriterion("\"dept_transfer_from\" not in", values, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromBetween(String value1, String value2) {
            addCriterion("\"dept_transfer_from\" between", value1, value2, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDeptTransferFromNotBetween(String value1, String value2) {
            addCriterion("\"dept_transfer_from\" not between", value1, value2, "deptTransferFrom");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeIsNull() {
            addCriterion("\"discharge_date_time\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeIsNotNull() {
            addCriterion("\"discharge_date_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeEqualTo(Date value) {
            addCriterion("\"discharge_date_time\" =", value, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeNotEqualTo(Date value) {
            addCriterion("\"discharge_date_time\" <>", value, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeGreaterThan(Date value) {
            addCriterion("\"discharge_date_time\" >", value, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"discharge_date_time\" >=", value, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeLessThan(Date value) {
            addCriterion("\"discharge_date_time\" <", value, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"discharge_date_time\" <=", value, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeIn(List<Date> values) {
            addCriterion("\"discharge_date_time\" in", values, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeNotIn(List<Date> values) {
            addCriterion("\"discharge_date_time\" not in", values, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeBetween(Date value1, Date value2) {
            addCriterion("\"discharge_date_time\" between", value1, value2, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDischargeDateTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"discharge_date_time\" not between", value1, value2, "dischargeDateTime");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromIsNull() {
            addCriterion("\"dept_discharge_from\" is null");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromIsNotNull() {
            addCriterion("\"dept_discharge_from\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromEqualTo(String value) {
            addCriterion("\"dept_discharge_from\" =", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromNotEqualTo(String value) {
            addCriterion("\"dept_discharge_from\" <>", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromGreaterThan(String value) {
            addCriterion("\"dept_discharge_from\" >", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromGreaterThanOrEqualTo(String value) {
            addCriterion("\"dept_discharge_from\" >=", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromLessThan(String value) {
            addCriterion("\"dept_discharge_from\" <", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromLessThanOrEqualTo(String value) {
            addCriterion("\"dept_discharge_from\" <=", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromLike(String value) {
            addCriterion("\"dept_discharge_from\" like", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromNotLike(String value) {
            addCriterion("\"dept_discharge_from\" not like", value, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromIn(List<String> values) {
            addCriterion("\"dept_discharge_from\" in", values, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromNotIn(List<String> values) {
            addCriterion("\"dept_discharge_from\" not in", values, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromBetween(String value1, String value2) {
            addCriterion("\"dept_discharge_from\" between", value1, value2, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andDeptDischargeFromNotBetween(String value1, String value2) {
            addCriterion("\"dept_discharge_from\" not between", value1, value2, "deptDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromIsNull() {
            addCriterion("\"ward_discharge_from\" is null");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromIsNotNull() {
            addCriterion("\"ward_discharge_from\" is not null");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromEqualTo(String value) {
            addCriterion("\"ward_discharge_from\" =", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromNotEqualTo(String value) {
            addCriterion("\"ward_discharge_from\" <>", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromGreaterThan(String value) {
            addCriterion("\"ward_discharge_from\" >", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromGreaterThanOrEqualTo(String value) {
            addCriterion("\"ward_discharge_from\" >=", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromLessThan(String value) {
            addCriterion("\"ward_discharge_from\" <", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromLessThanOrEqualTo(String value) {
            addCriterion("\"ward_discharge_from\" <=", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromLike(String value) {
            addCriterion("\"ward_discharge_from\" like", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromNotLike(String value) {
            addCriterion("\"ward_discharge_from\" not like", value, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromIn(List<String> values) {
            addCriterion("\"ward_discharge_from\" in", values, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromNotIn(List<String> values) {
            addCriterion("\"ward_discharge_from\" not in", values, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromBetween(String value1, String value2) {
            addCriterion("\"ward_discharge_from\" between", value1, value2, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andWardDischargeFromNotBetween(String value1, String value2) {
            addCriterion("\"ward_discharge_from\" not between", value1, value2, "wardDischargeFrom");
            return (Criteria) this;
        }

        public Criteria andInDaysIsNull() {
            addCriterion("\"in_days\" is null");
            return (Criteria) this;
        }

        public Criteria andInDaysIsNotNull() {
            addCriterion("\"in_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andInDaysEqualTo(Integer value) {
            addCriterion("\"in_days\" =", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysNotEqualTo(Integer value) {
            addCriterion("\"in_days\" <>", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysGreaterThan(Integer value) {
            addCriterion("\"in_days\" >", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"in_days\" >=", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysLessThan(Integer value) {
            addCriterion("\"in_days\" <", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"in_days\" <=", value, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysIn(List<Integer> values) {
            addCriterion("\"in_days\" in", values, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysNotIn(List<Integer> values) {
            addCriterion("\"in_days\" not in", values, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"in_days\" between", value1, value2, "inDays");
            return (Criteria) this;
        }

        public Criteria andInDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"in_days\" not between", value1, value2, "inDays");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesIsNull() {
            addCriterion("\"infected_times\" is null");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesIsNotNull() {
            addCriterion("\"infected_times\" is not null");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesEqualTo(Integer value) {
            addCriterion("\"infected_times\" =", value, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesNotEqualTo(Integer value) {
            addCriterion("\"infected_times\" <>", value, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesGreaterThan(Integer value) {
            addCriterion("\"infected_times\" >", value, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"infected_times\" >=", value, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesLessThan(Integer value) {
            addCriterion("\"infected_times\" <", value, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesLessThanOrEqualTo(Integer value) {
            addCriterion("\"infected_times\" <=", value, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesIn(List<Integer> values) {
            addCriterion("\"infected_times\" in", values, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesNotIn(List<Integer> values) {
            addCriterion("\"infected_times\" not in", values, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesBetween(Integer value1, Integer value2) {
            addCriterion("\"infected_times\" between", value1, value2, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInfectedTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("\"infected_times\" not between", value1, value2, "infectedTimes");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeIsNull() {
            addCriterion("\"injury_poisoning_code\" is null");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeIsNotNull() {
            addCriterion("\"injury_poisoning_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeEqualTo(String value) {
            addCriterion("\"injury_poisoning_code\" =", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeNotEqualTo(String value) {
            addCriterion("\"injury_poisoning_code\" <>", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeGreaterThan(String value) {
            addCriterion("\"injury_poisoning_code\" >", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"injury_poisoning_code\" >=", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeLessThan(String value) {
            addCriterion("\"injury_poisoning_code\" <", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeLessThanOrEqualTo(String value) {
            addCriterion("\"injury_poisoning_code\" <=", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeLike(String value) {
            addCriterion("\"injury_poisoning_code\" like", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeNotLike(String value) {
            addCriterion("\"injury_poisoning_code\" not like", value, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeIn(List<String> values) {
            addCriterion("\"injury_poisoning_code\" in", values, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeNotIn(List<String> values) {
            addCriterion("\"injury_poisoning_code\" not in", values, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeBetween(String value1, String value2) {
            addCriterion("\"injury_poisoning_code\" between", value1, value2, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCodeNotBetween(String value1, String value2) {
            addCriterion("\"injury_poisoning_code\" not between", value1, value2, "injuryPoisoningCode");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesIsNull() {
            addCriterion("\"injury_poisoning_causes\" is null");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesIsNotNull() {
            addCriterion("\"injury_poisoning_causes\" is not null");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesEqualTo(String value) {
            addCriterion("\"injury_poisoning_causes\" =", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesNotEqualTo(String value) {
            addCriterion("\"injury_poisoning_causes\" <>", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesGreaterThan(String value) {
            addCriterion("\"injury_poisoning_causes\" >", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesGreaterThanOrEqualTo(String value) {
            addCriterion("\"injury_poisoning_causes\" >=", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesLessThan(String value) {
            addCriterion("\"injury_poisoning_causes\" <", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesLessThanOrEqualTo(String value) {
            addCriterion("\"injury_poisoning_causes\" <=", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesLike(String value) {
            addCriterion("\"injury_poisoning_causes\" like", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesNotLike(String value) {
            addCriterion("\"injury_poisoning_causes\" not like", value, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesIn(List<String> values) {
            addCriterion("\"injury_poisoning_causes\" in", values, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesNotIn(List<String> values) {
            addCriterion("\"injury_poisoning_causes\" not in", values, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesBetween(String value1, String value2) {
            addCriterion("\"injury_poisoning_causes\" between", value1, value2, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andInjuryPoisoningCausesNotBetween(String value1, String value2) {
            addCriterion("\"injury_poisoning_causes\" not between", value1, value2, "injuryPoisoningCauses");
            return (Criteria) this;
        }

        public Criteria andIsAllergicIsNull() {
            addCriterion("\"is_allergic\" is null");
            return (Criteria) this;
        }

        public Criteria andIsAllergicIsNotNull() {
            addCriterion("\"is_allergic\" is not null");
            return (Criteria) this;
        }

        public Criteria andIsAllergicEqualTo(String value) {
            addCriterion("\"is_allergic\" =", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicNotEqualTo(String value) {
            addCriterion("\"is_allergic\" <>", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicGreaterThan(String value) {
            addCriterion("\"is_allergic\" >", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicGreaterThanOrEqualTo(String value) {
            addCriterion("\"is_allergic\" >=", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicLessThan(String value) {
            addCriterion("\"is_allergic\" <", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicLessThanOrEqualTo(String value) {
            addCriterion("\"is_allergic\" <=", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicLike(String value) {
            addCriterion("\"is_allergic\" like", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicNotLike(String value) {
            addCriterion("\"is_allergic\" not like", value, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicIn(List<String> values) {
            addCriterion("\"is_allergic\" in", values, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicNotIn(List<String> values) {
            addCriterion("\"is_allergic\" not in", values, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicBetween(String value1, String value2) {
            addCriterion("\"is_allergic\" between", value1, value2, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andIsAllergicNotBetween(String value1, String value2) {
            addCriterion("\"is_allergic\" not between", value1, value2, "isAllergic");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugIsNull() {
            addCriterion("\"allergen_drug\" is null");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugIsNotNull() {
            addCriterion("\"allergen_drug\" is not null");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugEqualTo(String value) {
            addCriterion("\"allergen_drug\" =", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugNotEqualTo(String value) {
            addCriterion("\"allergen_drug\" <>", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugGreaterThan(String value) {
            addCriterion("\"allergen_drug\" >", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugGreaterThanOrEqualTo(String value) {
            addCriterion("\"allergen_drug\" >=", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugLessThan(String value) {
            addCriterion("\"allergen_drug\" <", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugLessThanOrEqualTo(String value) {
            addCriterion("\"allergen_drug\" <=", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugLike(String value) {
            addCriterion("\"allergen_drug\" like", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugNotLike(String value) {
            addCriterion("\"allergen_drug\" not like", value, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugIn(List<String> values) {
            addCriterion("\"allergen_drug\" in", values, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugNotIn(List<String> values) {
            addCriterion("\"allergen_drug\" not in", values, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugBetween(String value1, String value2) {
            addCriterion("\"allergen_drug\" between", value1, value2, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andAllergenDrugNotBetween(String value1, String value2) {
            addCriterion("\"allergen_drug\" not between", value1, value2, "allergenDrug");
            return (Criteria) this;
        }

        public Criteria andHbsagIsNull() {
            addCriterion("\"hbsag\" is null");
            return (Criteria) this;
        }

        public Criteria andHbsagIsNotNull() {
            addCriterion("\"hbsag\" is not null");
            return (Criteria) this;
        }

        public Criteria andHbsagEqualTo(String value) {
            addCriterion("\"hbsag\" =", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagNotEqualTo(String value) {
            addCriterion("\"hbsag\" <>", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagGreaterThan(String value) {
            addCriterion("\"hbsag\" >", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagGreaterThanOrEqualTo(String value) {
            addCriterion("\"hbsag\" >=", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagLessThan(String value) {
            addCriterion("\"hbsag\" <", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagLessThanOrEqualTo(String value) {
            addCriterion("\"hbsag\" <=", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagLike(String value) {
            addCriterion("\"hbsag\" like", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagNotLike(String value) {
            addCriterion("\"hbsag\" not like", value, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagIn(List<String> values) {
            addCriterion("\"hbsag\" in", values, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagNotIn(List<String> values) {
            addCriterion("\"hbsag\" not in", values, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagBetween(String value1, String value2) {
            addCriterion("\"hbsag\" between", value1, value2, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHbsagNotBetween(String value1, String value2) {
            addCriterion("\"hbsag\" not between", value1, value2, "hbsag");
            return (Criteria) this;
        }

        public Criteria andHcvAbIsNull() {
            addCriterion("\"hcv_ab\" is null");
            return (Criteria) this;
        }

        public Criteria andHcvAbIsNotNull() {
            addCriterion("\"hcv_ab\" is not null");
            return (Criteria) this;
        }

        public Criteria andHcvAbEqualTo(String value) {
            addCriterion("\"hcv_ab\" =", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbNotEqualTo(String value) {
            addCriterion("\"hcv_ab\" <>", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbGreaterThan(String value) {
            addCriterion("\"hcv_ab\" >", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbGreaterThanOrEqualTo(String value) {
            addCriterion("\"hcv_ab\" >=", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbLessThan(String value) {
            addCriterion("\"hcv_ab\" <", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbLessThanOrEqualTo(String value) {
            addCriterion("\"hcv_ab\" <=", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbLike(String value) {
            addCriterion("\"hcv_ab\" like", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbNotLike(String value) {
            addCriterion("\"hcv_ab\" not like", value, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbIn(List<String> values) {
            addCriterion("\"hcv_ab\" in", values, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbNotIn(List<String> values) {
            addCriterion("\"hcv_ab\" not in", values, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbBetween(String value1, String value2) {
            addCriterion("\"hcv_ab\" between", value1, value2, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHcvAbNotBetween(String value1, String value2) {
            addCriterion("\"hcv_ab\" not between", value1, value2, "hcvAb");
            return (Criteria) this;
        }

        public Criteria andHivAbIsNull() {
            addCriterion("\"hiv_ab\" is null");
            return (Criteria) this;
        }

        public Criteria andHivAbIsNotNull() {
            addCriterion("\"hiv_ab\" is not null");
            return (Criteria) this;
        }

        public Criteria andHivAbEqualTo(String value) {
            addCriterion("\"hiv_ab\" =", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbNotEqualTo(String value) {
            addCriterion("\"hiv_ab\" <>", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbGreaterThan(String value) {
            addCriterion("\"hiv_ab\" >", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbGreaterThanOrEqualTo(String value) {
            addCriterion("\"hiv_ab\" >=", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbLessThan(String value) {
            addCriterion("\"hiv_ab\" <", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbLessThanOrEqualTo(String value) {
            addCriterion("\"hiv_ab\" <=", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbLike(String value) {
            addCriterion("\"hiv_ab\" like", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbNotLike(String value) {
            addCriterion("\"hiv_ab\" not like", value, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbIn(List<String> values) {
            addCriterion("\"hiv_ab\" in", values, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbNotIn(List<String> values) {
            addCriterion("\"hiv_ab\" not in", values, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbBetween(String value1, String value2) {
            addCriterion("\"hiv_ab\" between", value1, value2, "hivAb");
            return (Criteria) this;
        }

        public Criteria andHivAbNotBetween(String value1, String value2) {
            addCriterion("\"hiv_ab\" not between", value1, value2, "hivAb");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityIsNull() {
            addCriterion("\"outp_dis_diag_conformity\" is null");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityIsNotNull() {
            addCriterion("\"outp_dis_diag_conformity\" is not null");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityEqualTo(String value) {
            addCriterion("\"outp_dis_diag_conformity\" =", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityNotEqualTo(String value) {
            addCriterion("\"outp_dis_diag_conformity\" <>", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityGreaterThan(String value) {
            addCriterion("\"outp_dis_diag_conformity\" >", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityGreaterThanOrEqualTo(String value) {
            addCriterion("\"outp_dis_diag_conformity\" >=", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityLessThan(String value) {
            addCriterion("\"outp_dis_diag_conformity\" <", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityLessThanOrEqualTo(String value) {
            addCriterion("\"outp_dis_diag_conformity\" <=", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityLike(String value) {
            addCriterion("\"outp_dis_diag_conformity\" like", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityNotLike(String value) {
            addCriterion("\"outp_dis_diag_conformity\" not like", value, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityIn(List<String> values) {
            addCriterion("\"outp_dis_diag_conformity\" in", values, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityNotIn(List<String> values) {
            addCriterion("\"outp_dis_diag_conformity\" not in", values, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityBetween(String value1, String value2) {
            addCriterion("\"outp_dis_diag_conformity\" between", value1, value2, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andOutpDisDiagConformityNotBetween(String value1, String value2) {
            addCriterion("\"outp_dis_diag_conformity\" not between", value1, value2, "outpDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityIsNull() {
            addCriterion("\"admit_dis_diag_conformity\" is null");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityIsNotNull() {
            addCriterion("\"admit_dis_diag_conformity\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityEqualTo(String value) {
            addCriterion("\"admit_dis_diag_conformity\" =", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityNotEqualTo(String value) {
            addCriterion("\"admit_dis_diag_conformity\" <>", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityGreaterThan(String value) {
            addCriterion("\"admit_dis_diag_conformity\" >", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityGreaterThanOrEqualTo(String value) {
            addCriterion("\"admit_dis_diag_conformity\" >=", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityLessThan(String value) {
            addCriterion("\"admit_dis_diag_conformity\" <", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityLessThanOrEqualTo(String value) {
            addCriterion("\"admit_dis_diag_conformity\" <=", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityLike(String value) {
            addCriterion("\"admit_dis_diag_conformity\" like", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityNotLike(String value) {
            addCriterion("\"admit_dis_diag_conformity\" not like", value, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityIn(List<String> values) {
            addCriterion("\"admit_dis_diag_conformity\" in", values, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityNotIn(List<String> values) {
            addCriterion("\"admit_dis_diag_conformity\" not in", values, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityBetween(String value1, String value2) {
            addCriterion("\"admit_dis_diag_conformity\" between", value1, value2, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andAdmitDisDiagConformityNotBetween(String value1, String value2) {
            addCriterion("\"admit_dis_diag_conformity\" not between", value1, value2, "admitDisDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityIsNull() {
            addCriterion("\"preop_postop_diag_conformity\" is null");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityIsNotNull() {
            addCriterion("\"preop_postop_diag_conformity\" is not null");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityEqualTo(String value) {
            addCriterion("\"preop_postop_diag_conformity\" =", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityNotEqualTo(String value) {
            addCriterion("\"preop_postop_diag_conformity\" <>", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityGreaterThan(String value) {
            addCriterion("\"preop_postop_diag_conformity\" >", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityGreaterThanOrEqualTo(String value) {
            addCriterion("\"preop_postop_diag_conformity\" >=", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityLessThan(String value) {
            addCriterion("\"preop_postop_diag_conformity\" <", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityLessThanOrEqualTo(String value) {
            addCriterion("\"preop_postop_diag_conformity\" <=", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityLike(String value) {
            addCriterion("\"preop_postop_diag_conformity\" like", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityNotLike(String value) {
            addCriterion("\"preop_postop_diag_conformity\" not like", value, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityIn(List<String> values) {
            addCriterion("\"preop_postop_diag_conformity\" in", values, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityNotIn(List<String> values) {
            addCriterion("\"preop_postop_diag_conformity\" not in", values, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityBetween(String value1, String value2) {
            addCriterion("\"preop_postop_diag_conformity\" between", value1, value2, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andPreopPostopDiagConformityNotBetween(String value1, String value2) {
            addCriterion("\"preop_postop_diag_conformity\" not between", value1, value2, "preopPostopDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityIsNull() {
            addCriterion("\"clinic_patho_diag_conformity\" is null");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityIsNotNull() {
            addCriterion("\"clinic_patho_diag_conformity\" is not null");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityEqualTo(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" =", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityNotEqualTo(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" <>", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityGreaterThan(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" >", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityGreaterThanOrEqualTo(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" >=", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityLessThan(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" <", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityLessThanOrEqualTo(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" <=", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityLike(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" like", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityNotLike(String value) {
            addCriterion("\"clinic_patho_diag_conformity\" not like", value, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityIn(List<String> values) {
            addCriterion("\"clinic_patho_diag_conformity\" in", values, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityNotIn(List<String> values) {
            addCriterion("\"clinic_patho_diag_conformity\" not in", values, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityBetween(String value1, String value2) {
            addCriterion("\"clinic_patho_diag_conformity\" between", value1, value2, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andClinicPathoDiagConformityNotBetween(String value1, String value2) {
            addCriterion("\"clinic_patho_diag_conformity\" not between", value1, value2, "clinicPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityIsNull() {
            addCriterion("\"radio_patho_diag_conformity\" is null");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityIsNotNull() {
            addCriterion("\"radio_patho_diag_conformity\" is not null");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityEqualTo(String value) {
            addCriterion("\"radio_patho_diag_conformity\" =", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityNotEqualTo(String value) {
            addCriterion("\"radio_patho_diag_conformity\" <>", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityGreaterThan(String value) {
            addCriterion("\"radio_patho_diag_conformity\" >", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityGreaterThanOrEqualTo(String value) {
            addCriterion("\"radio_patho_diag_conformity\" >=", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityLessThan(String value) {
            addCriterion("\"radio_patho_diag_conformity\" <", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityLessThanOrEqualTo(String value) {
            addCriterion("\"radio_patho_diag_conformity\" <=", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityLike(String value) {
            addCriterion("\"radio_patho_diag_conformity\" like", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityNotLike(String value) {
            addCriterion("\"radio_patho_diag_conformity\" not like", value, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityIn(List<String> values) {
            addCriterion("\"radio_patho_diag_conformity\" in", values, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityNotIn(List<String> values) {
            addCriterion("\"radio_patho_diag_conformity\" not in", values, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityBetween(String value1, String value2) {
            addCriterion("\"radio_patho_diag_conformity\" between", value1, value2, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRadioPathoDiagConformityNotBetween(String value1, String value2) {
            addCriterion("\"radio_patho_diag_conformity\" not between", value1, value2, "radioPathoDiagConformity");
            return (Criteria) this;
        }

        public Criteria andRescueTimesIsNull() {
            addCriterion("\"rescue_times\" is null");
            return (Criteria) this;
        }

        public Criteria andRescueTimesIsNotNull() {
            addCriterion("\"rescue_times\" is not null");
            return (Criteria) this;
        }

        public Criteria andRescueTimesEqualTo(Integer value) {
            addCriterion("\"rescue_times\" =", value, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesNotEqualTo(Integer value) {
            addCriterion("\"rescue_times\" <>", value, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesGreaterThan(Integer value) {
            addCriterion("\"rescue_times\" >", value, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"rescue_times\" >=", value, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesLessThan(Integer value) {
            addCriterion("\"rescue_times\" <", value, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesLessThanOrEqualTo(Integer value) {
            addCriterion("\"rescue_times\" <=", value, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesIn(List<Integer> values) {
            addCriterion("\"rescue_times\" in", values, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesNotIn(List<Integer> values) {
            addCriterion("\"rescue_times\" not in", values, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesBetween(Integer value1, Integer value2) {
            addCriterion("\"rescue_times\" between", value1, value2, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("\"rescue_times\" not between", value1, value2, "rescueTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesIsNull() {
            addCriterion("\"rescue_success_times\" is null");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesIsNotNull() {
            addCriterion("\"rescue_success_times\" is not null");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesEqualTo(Integer value) {
            addCriterion("\"rescue_success_times\" =", value, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesNotEqualTo(Integer value) {
            addCriterion("\"rescue_success_times\" <>", value, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesGreaterThan(Integer value) {
            addCriterion("\"rescue_success_times\" >", value, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"rescue_success_times\" >=", value, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesLessThan(Integer value) {
            addCriterion("\"rescue_success_times\" <", value, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesLessThanOrEqualTo(Integer value) {
            addCriterion("\"rescue_success_times\" <=", value, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesIn(List<Integer> values) {
            addCriterion("\"rescue_success_times\" in", values, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesNotIn(List<Integer> values) {
            addCriterion("\"rescue_success_times\" not in", values, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesBetween(Integer value1, Integer value2) {
            addCriterion("\"rescue_success_times\" between", value1, value2, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andRescueSuccessTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("\"rescue_success_times\" not between", value1, value2, "rescueSuccessTimes");
            return (Criteria) this;
        }

        public Criteria andDiagBasisIsNull() {
            addCriterion("\"diag_basis\" is null");
            return (Criteria) this;
        }

        public Criteria andDiagBasisIsNotNull() {
            addCriterion("\"diag_basis\" is not null");
            return (Criteria) this;
        }

        public Criteria andDiagBasisEqualTo(String value) {
            addCriterion("\"diag_basis\" =", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisNotEqualTo(String value) {
            addCriterion("\"diag_basis\" <>", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisGreaterThan(String value) {
            addCriterion("\"diag_basis\" >", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisGreaterThanOrEqualTo(String value) {
            addCriterion("\"diag_basis\" >=", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisLessThan(String value) {
            addCriterion("\"diag_basis\" <", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisLessThanOrEqualTo(String value) {
            addCriterion("\"diag_basis\" <=", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisLike(String value) {
            addCriterion("\"diag_basis\" like", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisNotLike(String value) {
            addCriterion("\"diag_basis\" not like", value, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisIn(List<String> values) {
            addCriterion("\"diag_basis\" in", values, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisNotIn(List<String> values) {
            addCriterion("\"diag_basis\" not in", values, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisBetween(String value1, String value2) {
            addCriterion("\"diag_basis\" between", value1, value2, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDiagBasisNotBetween(String value1, String value2) {
            addCriterion("\"diag_basis\" not between", value1, value2, "diagBasis");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeIsNull() {
            addCriterion("\"differentiation_degree\" is null");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeIsNotNull() {
            addCriterion("\"differentiation_degree\" is not null");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeEqualTo(String value) {
            addCriterion("\"differentiation_degree\" =", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeNotEqualTo(String value) {
            addCriterion("\"differentiation_degree\" <>", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeGreaterThan(String value) {
            addCriterion("\"differentiation_degree\" >", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeGreaterThanOrEqualTo(String value) {
            addCriterion("\"differentiation_degree\" >=", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeLessThan(String value) {
            addCriterion("\"differentiation_degree\" <", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeLessThanOrEqualTo(String value) {
            addCriterion("\"differentiation_degree\" <=", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeLike(String value) {
            addCriterion("\"differentiation_degree\" like", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeNotLike(String value) {
            addCriterion("\"differentiation_degree\" not like", value, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeIn(List<String> values) {
            addCriterion("\"differentiation_degree\" in", values, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeNotIn(List<String> values) {
            addCriterion("\"differentiation_degree\" not in", values, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeBetween(String value1, String value2) {
            addCriterion("\"differentiation_degree\" between", value1, value2, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDifferentiationDegreeNotBetween(String value1, String value2) {
            addCriterion("\"differentiation_degree\" not between", value1, value2, "differentiationDegree");
            return (Criteria) this;
        }

        public Criteria andDirectorIsNull() {
            addCriterion("\"director\" is null");
            return (Criteria) this;
        }

        public Criteria andDirectorIsNotNull() {
            addCriterion("\"director\" is not null");
            return (Criteria) this;
        }

        public Criteria andDirectorEqualTo(String value) {
            addCriterion("\"director\" =", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotEqualTo(String value) {
            addCriterion("\"director\" <>", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorGreaterThan(String value) {
            addCriterion("\"director\" >", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorGreaterThanOrEqualTo(String value) {
            addCriterion("\"director\" >=", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorLessThan(String value) {
            addCriterion("\"director\" <", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorLessThanOrEqualTo(String value) {
            addCriterion("\"director\" <=", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorLike(String value) {
            addCriterion("\"director\" like", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotLike(String value) {
            addCriterion("\"director\" not like", value, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorIn(List<String> values) {
            addCriterion("\"director\" in", values, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotIn(List<String> values) {
            addCriterion("\"director\" not in", values, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorBetween(String value1, String value2) {
            addCriterion("\"director\" between", value1, value2, "director");
            return (Criteria) this;
        }

        public Criteria andDirectorNotBetween(String value1, String value2) {
            addCriterion("\"director\" not between", value1, value2, "director");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorIsNull() {
            addCriterion("\"chief_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorIsNotNull() {
            addCriterion("\"chief_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorEqualTo(String value) {
            addCriterion("\"chief_doctor\" =", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorNotEqualTo(String value) {
            addCriterion("\"chief_doctor\" <>", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorGreaterThan(String value) {
            addCriterion("\"chief_doctor\" >", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"chief_doctor\" >=", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorLessThan(String value) {
            addCriterion("\"chief_doctor\" <", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"chief_doctor\" <=", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorLike(String value) {
            addCriterion("\"chief_doctor\" like", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorNotLike(String value) {
            addCriterion("\"chief_doctor\" not like", value, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorIn(List<String> values) {
            addCriterion("\"chief_doctor\" in", values, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorNotIn(List<String> values) {
            addCriterion("\"chief_doctor\" not in", values, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorBetween(String value1, String value2) {
            addCriterion("\"chief_doctor\" between", value1, value2, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andChiefDoctorNotBetween(String value1, String value2) {
            addCriterion("\"chief_doctor\" not between", value1, value2, "chiefDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorIsNull() {
            addCriterion("\"attending_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorIsNotNull() {
            addCriterion("\"attending_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorEqualTo(String value) {
            addCriterion("\"attending_doctor\" =", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorNotEqualTo(String value) {
            addCriterion("\"attending_doctor\" <>", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorGreaterThan(String value) {
            addCriterion("\"attending_doctor\" >", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"attending_doctor\" >=", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorLessThan(String value) {
            addCriterion("\"attending_doctor\" <", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"attending_doctor\" <=", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorLike(String value) {
            addCriterion("\"attending_doctor\" like", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorNotLike(String value) {
            addCriterion("\"attending_doctor\" not like", value, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorIn(List<String> values) {
            addCriterion("\"attending_doctor\" in", values, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorNotIn(List<String> values) {
            addCriterion("\"attending_doctor\" not in", values, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorBetween(String value1, String value2) {
            addCriterion("\"attending_doctor\" between", value1, value2, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andAttendingDoctorNotBetween(String value1, String value2) {
            addCriterion("\"attending_doctor\" not between", value1, value2, "attendingDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorIsNull() {
            addCriterion("\"resident_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorIsNotNull() {
            addCriterion("\"resident_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorEqualTo(String value) {
            addCriterion("\"resident_doctor\" =", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorNotEqualTo(String value) {
            addCriterion("\"resident_doctor\" <>", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorGreaterThan(String value) {
            addCriterion("\"resident_doctor\" >", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"resident_doctor\" >=", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorLessThan(String value) {
            addCriterion("\"resident_doctor\" <", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"resident_doctor\" <=", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorLike(String value) {
            addCriterion("\"resident_doctor\" like", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorNotLike(String value) {
            addCriterion("\"resident_doctor\" not like", value, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorIn(List<String> values) {
            addCriterion("\"resident_doctor\" in", values, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorNotIn(List<String> values) {
            addCriterion("\"resident_doctor\" not in", values, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorBetween(String value1, String value2) {
            addCriterion("\"resident_doctor\" between", value1, value2, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResidentDoctorNotBetween(String value1, String value2) {
            addCriterion("\"resident_doctor\" not between", value1, value2, "residentDoctor");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseIsNull() {
            addCriterion("\"responsible_nurse\" is null");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseIsNotNull() {
            addCriterion("\"responsible_nurse\" is not null");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseEqualTo(String value) {
            addCriterion("\"responsible_nurse\" =", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseNotEqualTo(String value) {
            addCriterion("\"responsible_nurse\" <>", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseGreaterThan(String value) {
            addCriterion("\"responsible_nurse\" >", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseGreaterThanOrEqualTo(String value) {
            addCriterion("\"responsible_nurse\" >=", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseLessThan(String value) {
            addCriterion("\"responsible_nurse\" <", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseLessThanOrEqualTo(String value) {
            addCriterion("\"responsible_nurse\" <=", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseLike(String value) {
            addCriterion("\"responsible_nurse\" like", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseNotLike(String value) {
            addCriterion("\"responsible_nurse\" not like", value, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseIn(List<String> values) {
            addCriterion("\"responsible_nurse\" in", values, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseNotIn(List<String> values) {
            addCriterion("\"responsible_nurse\" not in", values, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseBetween(String value1, String value2) {
            addCriterion("\"responsible_nurse\" between", value1, value2, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andResponsibleNurseNotBetween(String value1, String value2) {
            addCriterion("\"responsible_nurse\" not between", value1, value2, "responsibleNurse");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorIsNull() {
            addCriterion("\"trainee_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorIsNotNull() {
            addCriterion("\"trainee_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorEqualTo(String value) {
            addCriterion("\"trainee_doctor\" =", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorNotEqualTo(String value) {
            addCriterion("\"trainee_doctor\" <>", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorGreaterThan(String value) {
            addCriterion("\"trainee_doctor\" >", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"trainee_doctor\" >=", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorLessThan(String value) {
            addCriterion("\"trainee_doctor\" <", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"trainee_doctor\" <=", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorLike(String value) {
            addCriterion("\"trainee_doctor\" like", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorNotLike(String value) {
            addCriterion("\"trainee_doctor\" not like", value, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorIn(List<String> values) {
            addCriterion("\"trainee_doctor\" in", values, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorNotIn(List<String> values) {
            addCriterion("\"trainee_doctor\" not in", values, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorBetween(String value1, String value2) {
            addCriterion("\"trainee_doctor\" between", value1, value2, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andTraineeDoctorNotBetween(String value1, String value2) {
            addCriterion("\"trainee_doctor\" not between", value1, value2, "traineeDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorIsNull() {
            addCriterion("\"graduate_intern_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorIsNotNull() {
            addCriterion("\"graduate_intern_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorEqualTo(String value) {
            addCriterion("\"graduate_intern_doctor\" =", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorNotEqualTo(String value) {
            addCriterion("\"graduate_intern_doctor\" <>", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorGreaterThan(String value) {
            addCriterion("\"graduate_intern_doctor\" >", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"graduate_intern_doctor\" >=", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorLessThan(String value) {
            addCriterion("\"graduate_intern_doctor\" <", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"graduate_intern_doctor\" <=", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorLike(String value) {
            addCriterion("\"graduate_intern_doctor\" like", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorNotLike(String value) {
            addCriterion("\"graduate_intern_doctor\" not like", value, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorIn(List<String> values) {
            addCriterion("\"graduate_intern_doctor\" in", values, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorNotIn(List<String> values) {
            addCriterion("\"graduate_intern_doctor\" not in", values, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorBetween(String value1, String value2) {
            addCriterion("\"graduate_intern_doctor\" between", value1, value2, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andGraduateInternDoctorNotBetween(String value1, String value2) {
            addCriterion("\"graduate_intern_doctor\" not between", value1, value2, "graduateInternDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorIsNull() {
            addCriterion("\"intern_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andInternDoctorIsNotNull() {
            addCriterion("\"intern_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andInternDoctorEqualTo(String value) {
            addCriterion("\"intern_doctor\" =", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorNotEqualTo(String value) {
            addCriterion("\"intern_doctor\" <>", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorGreaterThan(String value) {
            addCriterion("\"intern_doctor\" >", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"intern_doctor\" >=", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorLessThan(String value) {
            addCriterion("\"intern_doctor\" <", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"intern_doctor\" <=", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorLike(String value) {
            addCriterion("\"intern_doctor\" like", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorNotLike(String value) {
            addCriterion("\"intern_doctor\" not like", value, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorIn(List<String> values) {
            addCriterion("\"intern_doctor\" in", values, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorNotIn(List<String> values) {
            addCriterion("\"intern_doctor\" not in", values, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorBetween(String value1, String value2) {
            addCriterion("\"intern_doctor\" between", value1, value2, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andInternDoctorNotBetween(String value1, String value2) {
            addCriterion("\"intern_doctor\" not between", value1, value2, "internDoctor");
            return (Criteria) this;
        }

        public Criteria andCoderNameIsNull() {
            addCriterion("\"coder_name\" is null");
            return (Criteria) this;
        }

        public Criteria andCoderNameIsNotNull() {
            addCriterion("\"coder_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andCoderNameEqualTo(String value) {
            addCriterion("\"coder_name\" =", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameNotEqualTo(String value) {
            addCriterion("\"coder_name\" <>", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameGreaterThan(String value) {
            addCriterion("\"coder_name\" >", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"coder_name\" >=", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameLessThan(String value) {
            addCriterion("\"coder_name\" <", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameLessThanOrEqualTo(String value) {
            addCriterion("\"coder_name\" <=", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameLike(String value) {
            addCriterion("\"coder_name\" like", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameNotLike(String value) {
            addCriterion("\"coder_name\" not like", value, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameIn(List<String> values) {
            addCriterion("\"coder_name\" in", values, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameNotIn(List<String> values) {
            addCriterion("\"coder_name\" not in", values, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameBetween(String value1, String value2) {
            addCriterion("\"coder_name\" between", value1, value2, "coderName");
            return (Criteria) this;
        }

        public Criteria andCoderNameNotBetween(String value1, String value2) {
            addCriterion("\"coder_name\" not between", value1, value2, "coderName");
            return (Criteria) this;
        }

        public Criteria andMrQualityIsNull() {
            addCriterion("\"mr_quality\" is null");
            return (Criteria) this;
        }

        public Criteria andMrQualityIsNotNull() {
            addCriterion("\"mr_quality\" is not null");
            return (Criteria) this;
        }

        public Criteria andMrQualityEqualTo(String value) {
            addCriterion("\"mr_quality\" =", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityNotEqualTo(String value) {
            addCriterion("\"mr_quality\" <>", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityGreaterThan(String value) {
            addCriterion("\"mr_quality\" >", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityGreaterThanOrEqualTo(String value) {
            addCriterion("\"mr_quality\" >=", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityLessThan(String value) {
            addCriterion("\"mr_quality\" <", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityLessThanOrEqualTo(String value) {
            addCriterion("\"mr_quality\" <=", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityLike(String value) {
            addCriterion("\"mr_quality\" like", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityNotLike(String value) {
            addCriterion("\"mr_quality\" not like", value, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityIn(List<String> values) {
            addCriterion("\"mr_quality\" in", values, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityNotIn(List<String> values) {
            addCriterion("\"mr_quality\" not in", values, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityBetween(String value1, String value2) {
            addCriterion("\"mr_quality\" between", value1, value2, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andMrQualityNotBetween(String value1, String value2) {
            addCriterion("\"mr_quality\" not between", value1, value2, "mrQuality");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorIsNull() {
            addCriterion("\"quality_control_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorIsNotNull() {
            addCriterion("\"quality_control_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorEqualTo(String value) {
            addCriterion("\"quality_control_doctor\" =", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorNotEqualTo(String value) {
            addCriterion("\"quality_control_doctor\" <>", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorGreaterThan(String value) {
            addCriterion("\"quality_control_doctor\" >", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"quality_control_doctor\" >=", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorLessThan(String value) {
            addCriterion("\"quality_control_doctor\" <", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"quality_control_doctor\" <=", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorLike(String value) {
            addCriterion("\"quality_control_doctor\" like", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorNotLike(String value) {
            addCriterion("\"quality_control_doctor\" not like", value, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorIn(List<String> values) {
            addCriterion("\"quality_control_doctor\" in", values, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorNotIn(List<String> values) {
            addCriterion("\"quality_control_doctor\" not in", values, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorBetween(String value1, String value2) {
            addCriterion("\"quality_control_doctor\" between", value1, value2, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlDoctorNotBetween(String value1, String value2) {
            addCriterion("\"quality_control_doctor\" not between", value1, value2, "qualityControlDoctor");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseIsNull() {
            addCriterion("\"quality_control_nurse\" is null");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseIsNotNull() {
            addCriterion("\"quality_control_nurse\" is not null");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseEqualTo(String value) {
            addCriterion("\"quality_control_nurse\" =", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseNotEqualTo(String value) {
            addCriterion("\"quality_control_nurse\" <>", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseGreaterThan(String value) {
            addCriterion("\"quality_control_nurse\" >", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseGreaterThanOrEqualTo(String value) {
            addCriterion("\"quality_control_nurse\" >=", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseLessThan(String value) {
            addCriterion("\"quality_control_nurse\" <", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseLessThanOrEqualTo(String value) {
            addCriterion("\"quality_control_nurse\" <=", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseLike(String value) {
            addCriterion("\"quality_control_nurse\" like", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseNotLike(String value) {
            addCriterion("\"quality_control_nurse\" not like", value, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseIn(List<String> values) {
            addCriterion("\"quality_control_nurse\" in", values, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseNotIn(List<String> values) {
            addCriterion("\"quality_control_nurse\" not in", values, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseBetween(String value1, String value2) {
            addCriterion("\"quality_control_nurse\" between", value1, value2, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityControlNurseNotBetween(String value1, String value2) {
            addCriterion("\"quality_control_nurse\" not between", value1, value2, "qualityControlNurse");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeIsNull() {
            addCriterion("\"quality_confirm_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeIsNotNull() {
            addCriterion("\"quality_confirm_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeEqualTo(Date value) {
            addCriterion("\"quality_confirm_datetime\" =", value, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeNotEqualTo(Date value) {
            addCriterion("\"quality_confirm_datetime\" <>", value, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeGreaterThan(Date value) {
            addCriterion("\"quality_confirm_datetime\" >", value, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"quality_confirm_datetime\" >=", value, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeLessThan(Date value) {
            addCriterion("\"quality_confirm_datetime\" <", value, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"quality_confirm_datetime\" <=", value, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeIn(List<Date> values) {
            addCriterion("\"quality_confirm_datetime\" in", values, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeNotIn(List<Date> values) {
            addCriterion("\"quality_confirm_datetime\" not in", values, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"quality_confirm_datetime\" between", value1, value2, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andQualityConfirmDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"quality_confirm_datetime\" not between", value1, value2, "qualityConfirmDatetime");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysIsNull() {
            addCriterion("\"special_nursing_days\" is null");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysIsNotNull() {
            addCriterion("\"special_nursing_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysEqualTo(Integer value) {
            addCriterion("\"special_nursing_days\" =", value, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysNotEqualTo(Integer value) {
            addCriterion("\"special_nursing_days\" <>", value, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysGreaterThan(Integer value) {
            addCriterion("\"special_nursing_days\" >", value, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"special_nursing_days\" >=", value, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysLessThan(Integer value) {
            addCriterion("\"special_nursing_days\" <", value, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"special_nursing_days\" <=", value, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysIn(List<Integer> values) {
            addCriterion("\"special_nursing_days\" in", values, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysNotIn(List<Integer> values) {
            addCriterion("\"special_nursing_days\" not in", values, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"special_nursing_days\" between", value1, value2, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andSpecialNursingDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"special_nursing_days\" not between", value1, value2, "specialNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysIsNull() {
            addCriterion("\"first_grade_nursing_days\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysIsNotNull() {
            addCriterion("\"first_grade_nursing_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysEqualTo(Integer value) {
            addCriterion("\"first_grade_nursing_days\" =", value, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysNotEqualTo(Integer value) {
            addCriterion("\"first_grade_nursing_days\" <>", value, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysGreaterThan(Integer value) {
            addCriterion("\"first_grade_nursing_days\" >", value, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"first_grade_nursing_days\" >=", value, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysLessThan(Integer value) {
            addCriterion("\"first_grade_nursing_days\" <", value, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"first_grade_nursing_days\" <=", value, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysIn(List<Integer> values) {
            addCriterion("\"first_grade_nursing_days\" in", values, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysNotIn(List<Integer> values) {
            addCriterion("\"first_grade_nursing_days\" not in", values, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"first_grade_nursing_days\" between", value1, value2, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andFirstGradeNursingDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"first_grade_nursing_days\" not between", value1, value2, "firstGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysIsNull() {
            addCriterion("\"second_grade_nursing_days\" is null");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysIsNotNull() {
            addCriterion("\"second_grade_nursing_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysEqualTo(Integer value) {
            addCriterion("\"second_grade_nursing_days\" =", value, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysNotEqualTo(Integer value) {
            addCriterion("\"second_grade_nursing_days\" <>", value, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysGreaterThan(Integer value) {
            addCriterion("\"second_grade_nursing_days\" >", value, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"second_grade_nursing_days\" >=", value, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysLessThan(Integer value) {
            addCriterion("\"second_grade_nursing_days\" <", value, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"second_grade_nursing_days\" <=", value, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysIn(List<Integer> values) {
            addCriterion("\"second_grade_nursing_days\" in", values, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysNotIn(List<Integer> values) {
            addCriterion("\"second_grade_nursing_days\" not in", values, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"second_grade_nursing_days\" between", value1, value2, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andSecondGradeNursingDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"second_grade_nursing_days\" not between", value1, value2, "secondGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysIsNull() {
            addCriterion("\"third_grade_nursing_days\" is null");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysIsNotNull() {
            addCriterion("\"third_grade_nursing_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysEqualTo(Integer value) {
            addCriterion("\"third_grade_nursing_days\" =", value, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysNotEqualTo(Integer value) {
            addCriterion("\"third_grade_nursing_days\" <>", value, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysGreaterThan(Integer value) {
            addCriterion("\"third_grade_nursing_days\" >", value, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"third_grade_nursing_days\" >=", value, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysLessThan(Integer value) {
            addCriterion("\"third_grade_nursing_days\" <", value, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"third_grade_nursing_days\" <=", value, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysIn(List<Integer> values) {
            addCriterion("\"third_grade_nursing_days\" in", values, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysNotIn(List<Integer> values) {
            addCriterion("\"third_grade_nursing_days\" not in", values, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"third_grade_nursing_days\" between", value1, value2, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andThirdGradeNursingDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"third_grade_nursing_days\" not between", value1, value2, "thirdGradeNursingDays");
            return (Criteria) this;
        }

        public Criteria andIcuNameIsNull() {
            addCriterion("\"icu_name\" is null");
            return (Criteria) this;
        }

        public Criteria andIcuNameIsNotNull() {
            addCriterion("\"icu_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andIcuNameEqualTo(String value) {
            addCriterion("\"icu_name\" =", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameNotEqualTo(String value) {
            addCriterion("\"icu_name\" <>", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameGreaterThan(String value) {
            addCriterion("\"icu_name\" >", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"icu_name\" >=", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameLessThan(String value) {
            addCriterion("\"icu_name\" <", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameLessThanOrEqualTo(String value) {
            addCriterion("\"icu_name\" <=", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameLike(String value) {
            addCriterion("\"icu_name\" like", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameNotLike(String value) {
            addCriterion("\"icu_name\" not like", value, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameIn(List<String> values) {
            addCriterion("\"icu_name\" in", values, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameNotIn(List<String> values) {
            addCriterion("\"icu_name\" not in", values, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameBetween(String value1, String value2) {
            addCriterion("\"icu_name\" between", value1, value2, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuNameNotBetween(String value1, String value2) {
            addCriterion("\"icu_name\" not between", value1, value2, "icuName");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeIsNull() {
            addCriterion("\"icu_in_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeIsNotNull() {
            addCriterion("\"icu_in_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeEqualTo(Date value) {
            addCriterion("\"icu_in_datetime\" =", value, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeNotEqualTo(Date value) {
            addCriterion("\"icu_in_datetime\" <>", value, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeGreaterThan(Date value) {
            addCriterion("\"icu_in_datetime\" >", value, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"icu_in_datetime\" >=", value, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeLessThan(Date value) {
            addCriterion("\"icu_in_datetime\" <", value, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"icu_in_datetime\" <=", value, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeIn(List<Date> values) {
            addCriterion("\"icu_in_datetime\" in", values, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeNotIn(List<Date> values) {
            addCriterion("\"icu_in_datetime\" not in", values, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"icu_in_datetime\" between", value1, value2, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuInDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"icu_in_datetime\" not between", value1, value2, "icuInDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeIsNull() {
            addCriterion("\"icu_out_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeIsNotNull() {
            addCriterion("\"icu_out_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeEqualTo(Date value) {
            addCriterion("\"icu_out_datetime\" =", value, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeNotEqualTo(Date value) {
            addCriterion("\"icu_out_datetime\" <>", value, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeGreaterThan(Date value) {
            addCriterion("\"icu_out_datetime\" >", value, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"icu_out_datetime\" >=", value, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeLessThan(Date value) {
            addCriterion("\"icu_out_datetime\" <", value, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"icu_out_datetime\" <=", value, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeIn(List<Date> values) {
            addCriterion("\"icu_out_datetime\" in", values, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeNotIn(List<Date> values) {
            addCriterion("\"icu_out_datetime\" not in", values, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"icu_out_datetime\" between", value1, value2, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andIcuOutDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"icu_out_datetime\" not between", value1, value2, "icuOutDatetime");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyIsNull() {
            addCriterion("\"death_patient_autopsy\" is null");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyIsNotNull() {
            addCriterion("\"death_patient_autopsy\" is not null");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyEqualTo(String value) {
            addCriterion("\"death_patient_autopsy\" =", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyNotEqualTo(String value) {
            addCriterion("\"death_patient_autopsy\" <>", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyGreaterThan(String value) {
            addCriterion("\"death_patient_autopsy\" >", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyGreaterThanOrEqualTo(String value) {
            addCriterion("\"death_patient_autopsy\" >=", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyLessThan(String value) {
            addCriterion("\"death_patient_autopsy\" <", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyLessThanOrEqualTo(String value) {
            addCriterion("\"death_patient_autopsy\" <=", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyLike(String value) {
            addCriterion("\"death_patient_autopsy\" like", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyNotLike(String value) {
            addCriterion("\"death_patient_autopsy\" not like", value, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyIn(List<String> values) {
            addCriterion("\"death_patient_autopsy\" in", values, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyNotIn(List<String> values) {
            addCriterion("\"death_patient_autopsy\" not in", values, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyBetween(String value1, String value2) {
            addCriterion("\"death_patient_autopsy\" between", value1, value2, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andDeathPatientAutopsyNotBetween(String value1, String value2) {
            addCriterion("\"death_patient_autopsy\" not between", value1, value2, "deathPatientAutopsy");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalIsNull() {
            addCriterion("\"first_example_in_hospital\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalIsNotNull() {
            addCriterion("\"first_example_in_hospital\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalEqualTo(String value) {
            addCriterion("\"first_example_in_hospital\" =", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalNotEqualTo(String value) {
            addCriterion("\"first_example_in_hospital\" <>", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalGreaterThan(String value) {
            addCriterion("\"first_example_in_hospital\" >", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_example_in_hospital\" >=", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalLessThan(String value) {
            addCriterion("\"first_example_in_hospital\" <", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalLessThanOrEqualTo(String value) {
            addCriterion("\"first_example_in_hospital\" <=", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalLike(String value) {
            addCriterion("\"first_example_in_hospital\" like", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalNotLike(String value) {
            addCriterion("\"first_example_in_hospital\" not like", value, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalIn(List<String> values) {
            addCriterion("\"first_example_in_hospital\" in", values, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalNotIn(List<String> values) {
            addCriterion("\"first_example_in_hospital\" not in", values, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalBetween(String value1, String value2) {
            addCriterion("\"first_example_in_hospital\" between", value1, value2, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andFirstExampleInHospitalNotBetween(String value1, String value2) {
            addCriterion("\"first_example_in_hospital\" not between", value1, value2, "firstExampleInHospital");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeIsNull() {
            addCriterion("\"operation_patient_type\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeIsNotNull() {
            addCriterion("\"operation_patient_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeEqualTo(String value) {
            addCriterion("\"operation_patient_type\" =", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeNotEqualTo(String value) {
            addCriterion("\"operation_patient_type\" <>", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeGreaterThan(String value) {
            addCriterion("\"operation_patient_type\" >", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_patient_type\" >=", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeLessThan(String value) {
            addCriterion("\"operation_patient_type\" <", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeLessThanOrEqualTo(String value) {
            addCriterion("\"operation_patient_type\" <=", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeLike(String value) {
            addCriterion("\"operation_patient_type\" like", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeNotLike(String value) {
            addCriterion("\"operation_patient_type\" not like", value, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeIn(List<String> values) {
            addCriterion("\"operation_patient_type\" in", values, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeNotIn(List<String> values) {
            addCriterion("\"operation_patient_type\" not in", values, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeBetween(String value1, String value2) {
            addCriterion("\"operation_patient_type\" between", value1, value2, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andOperationPatientTypeNotBetween(String value1, String value2) {
            addCriterion("\"operation_patient_type\" not between", value1, value2, "operationPatientType");
            return (Criteria) this;
        }

        public Criteria andFollowUpIsNull() {
            addCriterion("\"follow_up\" is null");
            return (Criteria) this;
        }

        public Criteria andFollowUpIsNotNull() {
            addCriterion("\"follow_up\" is not null");
            return (Criteria) this;
        }

        public Criteria andFollowUpEqualTo(String value) {
            addCriterion("\"follow_up\" =", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpNotEqualTo(String value) {
            addCriterion("\"follow_up\" <>", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpGreaterThan(String value) {
            addCriterion("\"follow_up\" >", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpGreaterThanOrEqualTo(String value) {
            addCriterion("\"follow_up\" >=", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpLessThan(String value) {
            addCriterion("\"follow_up\" <", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpLessThanOrEqualTo(String value) {
            addCriterion("\"follow_up\" <=", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpLike(String value) {
            addCriterion("\"follow_up\" like", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpNotLike(String value) {
            addCriterion("\"follow_up\" not like", value, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpIn(List<String> values) {
            addCriterion("\"follow_up\" in", values, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpNotIn(List<String> values) {
            addCriterion("\"follow_up\" not in", values, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpBetween(String value1, String value2) {
            addCriterion("\"follow_up\" between", value1, value2, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpNotBetween(String value1, String value2) {
            addCriterion("\"follow_up\" not between", value1, value2, "followUp");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksIsNull() {
            addCriterion("\"follow_up_weeks\" is null");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksIsNotNull() {
            addCriterion("\"follow_up_weeks\" is not null");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksEqualTo(Double value) {
            addCriterion("\"follow_up_weeks\" =", value, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksNotEqualTo(Double value) {
            addCriterion("\"follow_up_weeks\" <>", value, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksGreaterThan(Double value) {
            addCriterion("\"follow_up_weeks\" >", value, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksGreaterThanOrEqualTo(Double value) {
            addCriterion("\"follow_up_weeks\" >=", value, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksLessThan(Double value) {
            addCriterion("\"follow_up_weeks\" <", value, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksLessThanOrEqualTo(Double value) {
            addCriterion("\"follow_up_weeks\" <=", value, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksIn(List<Double> values) {
            addCriterion("\"follow_up_weeks\" in", values, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksNotIn(List<Double> values) {
            addCriterion("\"follow_up_weeks\" not in", values, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksBetween(Double value1, Double value2) {
            addCriterion("\"follow_up_weeks\" between", value1, value2, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpWeeksNotBetween(Double value1, Double value2) {
            addCriterion("\"follow_up_weeks\" not between", value1, value2, "followUpWeeks");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsIsNull() {
            addCriterion("\"follow_up_months\" is null");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsIsNotNull() {
            addCriterion("\"follow_up_months\" is not null");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsEqualTo(Double value) {
            addCriterion("\"follow_up_months\" =", value, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsNotEqualTo(Double value) {
            addCriterion("\"follow_up_months\" <>", value, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsGreaterThan(Double value) {
            addCriterion("\"follow_up_months\" >", value, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsGreaterThanOrEqualTo(Double value) {
            addCriterion("\"follow_up_months\" >=", value, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsLessThan(Double value) {
            addCriterion("\"follow_up_months\" <", value, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsLessThanOrEqualTo(Double value) {
            addCriterion("\"follow_up_months\" <=", value, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsIn(List<Double> values) {
            addCriterion("\"follow_up_months\" in", values, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsNotIn(List<Double> values) {
            addCriterion("\"follow_up_months\" not in", values, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsBetween(Double value1, Double value2) {
            addCriterion("\"follow_up_months\" between", value1, value2, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpMonthsNotBetween(Double value1, Double value2) {
            addCriterion("\"follow_up_months\" not between", value1, value2, "followUpMonths");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsIsNull() {
            addCriterion("\"follow_up_years\" is null");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsIsNotNull() {
            addCriterion("\"follow_up_years\" is not null");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsEqualTo(Double value) {
            addCriterion("\"follow_up_years\" =", value, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsNotEqualTo(Double value) {
            addCriterion("\"follow_up_years\" <>", value, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsGreaterThan(Double value) {
            addCriterion("\"follow_up_years\" >", value, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsGreaterThanOrEqualTo(Double value) {
            addCriterion("\"follow_up_years\" >=", value, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsLessThan(Double value) {
            addCriterion("\"follow_up_years\" <", value, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsLessThanOrEqualTo(Double value) {
            addCriterion("\"follow_up_years\" <=", value, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsIn(List<Double> values) {
            addCriterion("\"follow_up_years\" in", values, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsNotIn(List<Double> values) {
            addCriterion("\"follow_up_years\" not in", values, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsBetween(Double value1, Double value2) {
            addCriterion("\"follow_up_years\" between", value1, value2, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andFollowUpYearsNotBetween(Double value1, Double value2) {
            addCriterion("\"follow_up_years\" not between", value1, value2, "followUpYears");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseIsNull() {
            addCriterion("\"demonstration_case\" is null");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseIsNotNull() {
            addCriterion("\"demonstration_case\" is not null");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseEqualTo(String value) {
            addCriterion("\"demonstration_case\" =", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseNotEqualTo(String value) {
            addCriterion("\"demonstration_case\" <>", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseGreaterThan(String value) {
            addCriterion("\"demonstration_case\" >", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseGreaterThanOrEqualTo(String value) {
            addCriterion("\"demonstration_case\" >=", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseLessThan(String value) {
            addCriterion("\"demonstration_case\" <", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseLessThanOrEqualTo(String value) {
            addCriterion("\"demonstration_case\" <=", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseLike(String value) {
            addCriterion("\"demonstration_case\" like", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseNotLike(String value) {
            addCriterion("\"demonstration_case\" not like", value, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseIn(List<String> values) {
            addCriterion("\"demonstration_case\" in", values, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseNotIn(List<String> values) {
            addCriterion("\"demonstration_case\" not in", values, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseBetween(String value1, String value2) {
            addCriterion("\"demonstration_case\" between", value1, value2, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andDemonstrationCaseNotBetween(String value1, String value2) {
            addCriterion("\"demonstration_case\" not between", value1, value2, "demonstrationCase");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeIsNull() {
            addCriterion("\"abo_blood_type\" is null");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeIsNotNull() {
            addCriterion("\"abo_blood_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeEqualTo(String value) {
            addCriterion("\"abo_blood_type\" =", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeNotEqualTo(String value) {
            addCriterion("\"abo_blood_type\" <>", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeGreaterThan(String value) {
            addCriterion("\"abo_blood_type\" >", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"abo_blood_type\" >=", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeLessThan(String value) {
            addCriterion("\"abo_blood_type\" <", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeLessThanOrEqualTo(String value) {
            addCriterion("\"abo_blood_type\" <=", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeLike(String value) {
            addCriterion("\"abo_blood_type\" like", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeNotLike(String value) {
            addCriterion("\"abo_blood_type\" not like", value, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeIn(List<String> values) {
            addCriterion("\"abo_blood_type\" in", values, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeNotIn(List<String> values) {
            addCriterion("\"abo_blood_type\" not in", values, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeBetween(String value1, String value2) {
            addCriterion("\"abo_blood_type\" between", value1, value2, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andAboBloodTypeNotBetween(String value1, String value2) {
            addCriterion("\"abo_blood_type\" not between", value1, value2, "aboBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeIsNull() {
            addCriterion("\"rh_blood_type\" is null");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeIsNotNull() {
            addCriterion("\"rh_blood_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeEqualTo(String value) {
            addCriterion("\"rh_blood_type\" =", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeNotEqualTo(String value) {
            addCriterion("\"rh_blood_type\" <>", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeGreaterThan(String value) {
            addCriterion("\"rh_blood_type\" >", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"rh_blood_type\" >=", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeLessThan(String value) {
            addCriterion("\"rh_blood_type\" <", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeLessThanOrEqualTo(String value) {
            addCriterion("\"rh_blood_type\" <=", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeLike(String value) {
            addCriterion("\"rh_blood_type\" like", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeNotLike(String value) {
            addCriterion("\"rh_blood_type\" not like", value, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeIn(List<String> values) {
            addCriterion("\"rh_blood_type\" in", values, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeNotIn(List<String> values) {
            addCriterion("\"rh_blood_type\" not in", values, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeBetween(String value1, String value2) {
            addCriterion("\"rh_blood_type\" between", value1, value2, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andRhBloodTypeNotBetween(String value1, String value2) {
            addCriterion("\"rh_blood_type\" not between", value1, value2, "rhBloodType");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionIsNull() {
            addCriterion("\"adverse_reaction\" is null");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionIsNotNull() {
            addCriterion("\"adverse_reaction\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionEqualTo(String value) {
            addCriterion("\"adverse_reaction\" =", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionNotEqualTo(String value) {
            addCriterion("\"adverse_reaction\" <>", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionGreaterThan(String value) {
            addCriterion("\"adverse_reaction\" >", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionGreaterThanOrEqualTo(String value) {
            addCriterion("\"adverse_reaction\" >=", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionLessThan(String value) {
            addCriterion("\"adverse_reaction\" <", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionLessThanOrEqualTo(String value) {
            addCriterion("\"adverse_reaction\" <=", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionLike(String value) {
            addCriterion("\"adverse_reaction\" like", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionNotLike(String value) {
            addCriterion("\"adverse_reaction\" not like", value, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionIn(List<String> values) {
            addCriterion("\"adverse_reaction\" in", values, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionNotIn(List<String> values) {
            addCriterion("\"adverse_reaction\" not in", values, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionBetween(String value1, String value2) {
            addCriterion("\"adverse_reaction\" between", value1, value2, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andAdverseReactionNotBetween(String value1, String value2) {
            addCriterion("\"adverse_reaction\" not between", value1, value2, "adverseReaction");
            return (Criteria) this;
        }

        public Criteria andErythrocyteIsNull() {
            addCriterion("\"erythrocyte\" is null");
            return (Criteria) this;
        }

        public Criteria andErythrocyteIsNotNull() {
            addCriterion("\"erythrocyte\" is not null");
            return (Criteria) this;
        }

        public Criteria andErythrocyteEqualTo(Double value) {
            addCriterion("\"erythrocyte\" =", value, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteNotEqualTo(Double value) {
            addCriterion("\"erythrocyte\" <>", value, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteGreaterThan(Double value) {
            addCriterion("\"erythrocyte\" >", value, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteGreaterThanOrEqualTo(Double value) {
            addCriterion("\"erythrocyte\" >=", value, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteLessThan(Double value) {
            addCriterion("\"erythrocyte\" <", value, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteLessThanOrEqualTo(Double value) {
            addCriterion("\"erythrocyte\" <=", value, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteIn(List<Double> values) {
            addCriterion("\"erythrocyte\" in", values, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteNotIn(List<Double> values) {
            addCriterion("\"erythrocyte\" not in", values, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteBetween(Double value1, Double value2) {
            addCriterion("\"erythrocyte\" between", value1, value2, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andErythrocyteNotBetween(Double value1, Double value2) {
            addCriterion("\"erythrocyte\" not between", value1, value2, "erythrocyte");
            return (Criteria) this;
        }

        public Criteria andPlateletIsNull() {
            addCriterion("\"platelet\" is null");
            return (Criteria) this;
        }

        public Criteria andPlateletIsNotNull() {
            addCriterion("\"platelet\" is not null");
            return (Criteria) this;
        }

        public Criteria andPlateletEqualTo(Double value) {
            addCriterion("\"platelet\" =", value, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletNotEqualTo(Double value) {
            addCriterion("\"platelet\" <>", value, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletGreaterThan(Double value) {
            addCriterion("\"platelet\" >", value, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletGreaterThanOrEqualTo(Double value) {
            addCriterion("\"platelet\" >=", value, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletLessThan(Double value) {
            addCriterion("\"platelet\" <", value, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletLessThanOrEqualTo(Double value) {
            addCriterion("\"platelet\" <=", value, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletIn(List<Double> values) {
            addCriterion("\"platelet\" in", values, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletNotIn(List<Double> values) {
            addCriterion("\"platelet\" not in", values, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletBetween(Double value1, Double value2) {
            addCriterion("\"platelet\" between", value1, value2, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlateletNotBetween(Double value1, Double value2) {
            addCriterion("\"platelet\" not between", value1, value2, "platelet");
            return (Criteria) this;
        }

        public Criteria andPlasmaIsNull() {
            addCriterion("\"plasma\" is null");
            return (Criteria) this;
        }

        public Criteria andPlasmaIsNotNull() {
            addCriterion("\"plasma\" is not null");
            return (Criteria) this;
        }

        public Criteria andPlasmaEqualTo(Double value) {
            addCriterion("\"plasma\" =", value, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaNotEqualTo(Double value) {
            addCriterion("\"plasma\" <>", value, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaGreaterThan(Double value) {
            addCriterion("\"plasma\" >", value, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaGreaterThanOrEqualTo(Double value) {
            addCriterion("\"plasma\" >=", value, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaLessThan(Double value) {
            addCriterion("\"plasma\" <", value, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaLessThanOrEqualTo(Double value) {
            addCriterion("\"plasma\" <=", value, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaIn(List<Double> values) {
            addCriterion("\"plasma\" in", values, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaNotIn(List<Double> values) {
            addCriterion("\"plasma\" not in", values, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaBetween(Double value1, Double value2) {
            addCriterion("\"plasma\" between", value1, value2, "plasma");
            return (Criteria) this;
        }

        public Criteria andPlasmaNotBetween(Double value1, Double value2) {
            addCriterion("\"plasma\" not between", value1, value2, "plasma");
            return (Criteria) this;
        }

        public Criteria andWholeBloodIsNull() {
            addCriterion("\"whole_blood\" is null");
            return (Criteria) this;
        }

        public Criteria andWholeBloodIsNotNull() {
            addCriterion("\"whole_blood\" is not null");
            return (Criteria) this;
        }

        public Criteria andWholeBloodEqualTo(Double value) {
            addCriterion("\"whole_blood\" =", value, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodNotEqualTo(Double value) {
            addCriterion("\"whole_blood\" <>", value, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodGreaterThan(Double value) {
            addCriterion("\"whole_blood\" >", value, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodGreaterThanOrEqualTo(Double value) {
            addCriterion("\"whole_blood\" >=", value, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodLessThan(Double value) {
            addCriterion("\"whole_blood\" <", value, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodLessThanOrEqualTo(Double value) {
            addCriterion("\"whole_blood\" <=", value, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodIn(List<Double> values) {
            addCriterion("\"whole_blood\" in", values, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodNotIn(List<Double> values) {
            addCriterion("\"whole_blood\" not in", values, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodBetween(Double value1, Double value2) {
            addCriterion("\"whole_blood\" between", value1, value2, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andWholeBloodNotBetween(Double value1, Double value2) {
            addCriterion("\"whole_blood\" not between", value1, value2, "wholeBlood");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackIsNull() {
            addCriterion("\"autologous_blood_callback\" is null");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackIsNotNull() {
            addCriterion("\"autologous_blood_callback\" is not null");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackEqualTo(String value) {
            addCriterion("\"autologous_blood_callback\" =", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackNotEqualTo(String value) {
            addCriterion("\"autologous_blood_callback\" <>", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackGreaterThan(String value) {
            addCriterion("\"autologous_blood_callback\" >", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackGreaterThanOrEqualTo(String value) {
            addCriterion("\"autologous_blood_callback\" >=", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackLessThan(String value) {
            addCriterion("\"autologous_blood_callback\" <", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackLessThanOrEqualTo(String value) {
            addCriterion("\"autologous_blood_callback\" <=", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackLike(String value) {
            addCriterion("\"autologous_blood_callback\" like", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackNotLike(String value) {
            addCriterion("\"autologous_blood_callback\" not like", value, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackIn(List<String> values) {
            addCriterion("\"autologous_blood_callback\" in", values, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackNotIn(List<String> values) {
            addCriterion("\"autologous_blood_callback\" not in", values, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackBetween(String value1, String value2) {
            addCriterion("\"autologous_blood_callback\" between", value1, value2, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andAutologousBloodCallbackNotBetween(String value1, String value2) {
            addCriterion("\"autologous_blood_callback\" not between", value1, value2, "autologousBloodCallback");
            return (Criteria) this;
        }

        public Criteria andOthersBloodIsNull() {
            addCriterion("\"others_blood\" is null");
            return (Criteria) this;
        }

        public Criteria andOthersBloodIsNotNull() {
            addCriterion("\"others_blood\" is not null");
            return (Criteria) this;
        }

        public Criteria andOthersBloodEqualTo(Double value) {
            addCriterion("\"others_blood\" =", value, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodNotEqualTo(Double value) {
            addCriterion("\"others_blood\" <>", value, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodGreaterThan(Double value) {
            addCriterion("\"others_blood\" >", value, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodGreaterThanOrEqualTo(Double value) {
            addCriterion("\"others_blood\" >=", value, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodLessThan(Double value) {
            addCriterion("\"others_blood\" <", value, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodLessThanOrEqualTo(Double value) {
            addCriterion("\"others_blood\" <=", value, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodIn(List<Double> values) {
            addCriterion("\"others_blood\" in", values, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodNotIn(List<Double> values) {
            addCriterion("\"others_blood\" not in", values, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodBetween(Double value1, Double value2) {
            addCriterion("\"others_blood\" between", value1, value2, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andOthersBloodNotBetween(Double value1, Double value2) {
            addCriterion("\"others_blood\" not between", value1, value2, "othersBlood");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearIsNull() {
            addCriterion("\"age_under_one_year\" is null");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearIsNotNull() {
            addCriterion("\"age_under_one_year\" is not null");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearEqualTo(String value) {
            addCriterion("\"age_under_one_year\" =", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearNotEqualTo(String value) {
            addCriterion("\"age_under_one_year\" <>", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearGreaterThan(String value) {
            addCriterion("\"age_under_one_year\" >", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearGreaterThanOrEqualTo(String value) {
            addCriterion("\"age_under_one_year\" >=", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearLessThan(String value) {
            addCriterion("\"age_under_one_year\" <", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearLessThanOrEqualTo(String value) {
            addCriterion("\"age_under_one_year\" <=", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearLike(String value) {
            addCriterion("\"age_under_one_year\" like", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearNotLike(String value) {
            addCriterion("\"age_under_one_year\" not like", value, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearIn(List<String> values) {
            addCriterion("\"age_under_one_year\" in", values, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearNotIn(List<String> values) {
            addCriterion("\"age_under_one_year\" not in", values, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearBetween(String value1, String value2) {
            addCriterion("\"age_under_one_year\" between", value1, value2, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andAgeUnderOneYearNotBetween(String value1, String value2) {
            addCriterion("\"age_under_one_year\" not between", value1, value2, "ageUnderOneYear");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightIsNull() {
            addCriterion("\"newborn_weight\" is null");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightIsNotNull() {
            addCriterion("\"newborn_weight\" is not null");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightEqualTo(Double value) {
            addCriterion("\"newborn_weight\" =", value, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightNotEqualTo(Double value) {
            addCriterion("\"newborn_weight\" <>", value, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightGreaterThan(Double value) {
            addCriterion("\"newborn_weight\" >", value, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("\"newborn_weight\" >=", value, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightLessThan(Double value) {
            addCriterion("\"newborn_weight\" <", value, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightLessThanOrEqualTo(Double value) {
            addCriterion("\"newborn_weight\" <=", value, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightIn(List<Double> values) {
            addCriterion("\"newborn_weight\" in", values, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightNotIn(List<Double> values) {
            addCriterion("\"newborn_weight\" not in", values, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightBetween(Double value1, Double value2) {
            addCriterion("\"newborn_weight\" between", value1, value2, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornWeightNotBetween(Double value1, Double value2) {
            addCriterion("\"newborn_weight\" not between", value1, value2, "newbornWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightIsNull() {
            addCriterion("\"newborn_admit_weight\" is null");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightIsNotNull() {
            addCriterion("\"newborn_admit_weight\" is not null");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightEqualTo(Double value) {
            addCriterion("\"newborn_admit_weight\" =", value, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightNotEqualTo(Double value) {
            addCriterion("\"newborn_admit_weight\" <>", value, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightGreaterThan(Double value) {
            addCriterion("\"newborn_admit_weight\" >", value, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightGreaterThanOrEqualTo(Double value) {
            addCriterion("\"newborn_admit_weight\" >=", value, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightLessThan(Double value) {
            addCriterion("\"newborn_admit_weight\" <", value, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightLessThanOrEqualTo(Double value) {
            addCriterion("\"newborn_admit_weight\" <=", value, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightIn(List<Double> values) {
            addCriterion("\"newborn_admit_weight\" in", values, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightNotIn(List<Double> values) {
            addCriterion("\"newborn_admit_weight\" not in", values, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightBetween(Double value1, Double value2) {
            addCriterion("\"newborn_admit_weight\" between", value1, value2, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andNewbornAdmitWeightNotBetween(Double value1, Double value2) {
            addCriterion("\"newborn_admit_weight\" not between", value1, value2, "newbornAdmitWeight");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitIsNull() {
            addCriterion("\"coma_hours_before_admit\" is null");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitIsNotNull() {
            addCriterion("\"coma_hours_before_admit\" is not null");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitEqualTo(Double value) {
            addCriterion("\"coma_hours_before_admit\" =", value, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitNotEqualTo(Double value) {
            addCriterion("\"coma_hours_before_admit\" <>", value, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitGreaterThan(Double value) {
            addCriterion("\"coma_hours_before_admit\" >", value, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitGreaterThanOrEqualTo(Double value) {
            addCriterion("\"coma_hours_before_admit\" >=", value, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitLessThan(Double value) {
            addCriterion("\"coma_hours_before_admit\" <", value, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitLessThanOrEqualTo(Double value) {
            addCriterion("\"coma_hours_before_admit\" <=", value, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitIn(List<Double> values) {
            addCriterion("\"coma_hours_before_admit\" in", values, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitNotIn(List<Double> values) {
            addCriterion("\"coma_hours_before_admit\" not in", values, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitBetween(Double value1, Double value2) {
            addCriterion("\"coma_hours_before_admit\" between", value1, value2, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursBeforeAdmitNotBetween(Double value1, Double value2) {
            addCriterion("\"coma_hours_before_admit\" not between", value1, value2, "comaHoursBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitIsNull() {
            addCriterion("\"coma_minutes_before_admit\" is null");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitIsNotNull() {
            addCriterion("\"coma_minutes_before_admit\" is not null");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitEqualTo(Double value) {
            addCriterion("\"coma_minutes_before_admit\" =", value, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitNotEqualTo(Double value) {
            addCriterion("\"coma_minutes_before_admit\" <>", value, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitGreaterThan(Double value) {
            addCriterion("\"coma_minutes_before_admit\" >", value, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitGreaterThanOrEqualTo(Double value) {
            addCriterion("\"coma_minutes_before_admit\" >=", value, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitLessThan(Double value) {
            addCriterion("\"coma_minutes_before_admit\" <", value, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitLessThanOrEqualTo(Double value) {
            addCriterion("\"coma_minutes_before_admit\" <=", value, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitIn(List<Double> values) {
            addCriterion("\"coma_minutes_before_admit\" in", values, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitNotIn(List<Double> values) {
            addCriterion("\"coma_minutes_before_admit\" not in", values, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitBetween(Double value1, Double value2) {
            addCriterion("\"coma_minutes_before_admit\" between", value1, value2, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesBeforeAdmitNotBetween(Double value1, Double value2) {
            addCriterion("\"coma_minutes_before_admit\" not between", value1, value2, "comaMinutesBeforeAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitIsNull() {
            addCriterion("\"coma_hours_after_admit\" is null");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitIsNotNull() {
            addCriterion("\"coma_hours_after_admit\" is not null");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitEqualTo(Double value) {
            addCriterion("\"coma_hours_after_admit\" =", value, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitNotEqualTo(Double value) {
            addCriterion("\"coma_hours_after_admit\" <>", value, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitGreaterThan(Double value) {
            addCriterion("\"coma_hours_after_admit\" >", value, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitGreaterThanOrEqualTo(Double value) {
            addCriterion("\"coma_hours_after_admit\" >=", value, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitLessThan(Double value) {
            addCriterion("\"coma_hours_after_admit\" <", value, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitLessThanOrEqualTo(Double value) {
            addCriterion("\"coma_hours_after_admit\" <=", value, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitIn(List<Double> values) {
            addCriterion("\"coma_hours_after_admit\" in", values, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitNotIn(List<Double> values) {
            addCriterion("\"coma_hours_after_admit\" not in", values, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitBetween(Double value1, Double value2) {
            addCriterion("\"coma_hours_after_admit\" between", value1, value2, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaHoursAfterAdmitNotBetween(Double value1, Double value2) {
            addCriterion("\"coma_hours_after_admit\" not between", value1, value2, "comaHoursAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitIsNull() {
            addCriterion("\"coma_minutes_after_admit\" is null");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitIsNotNull() {
            addCriterion("\"coma_minutes_after_admit\" is not null");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitEqualTo(Double value) {
            addCriterion("\"coma_minutes_after_admit\" =", value, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitNotEqualTo(Double value) {
            addCriterion("\"coma_minutes_after_admit\" <>", value, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitGreaterThan(Double value) {
            addCriterion("\"coma_minutes_after_admit\" >", value, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitGreaterThanOrEqualTo(Double value) {
            addCriterion("\"coma_minutes_after_admit\" >=", value, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitLessThan(Double value) {
            addCriterion("\"coma_minutes_after_admit\" <", value, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitLessThanOrEqualTo(Double value) {
            addCriterion("\"coma_minutes_after_admit\" <=", value, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitIn(List<Double> values) {
            addCriterion("\"coma_minutes_after_admit\" in", values, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitNotIn(List<Double> values) {
            addCriterion("\"coma_minutes_after_admit\" not in", values, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitBetween(Double value1, Double value2) {
            addCriterion("\"coma_minutes_after_admit\" between", value1, value2, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andComaMinutesAfterAdmitNotBetween(Double value1, Double value2) {
            addCriterion("\"coma_minutes_after_admit\" not between", value1, value2, "comaMinutesAfterAdmit");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeIsNull() {
            addCriterion("\"respirator_using_time\" is null");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeIsNotNull() {
            addCriterion("\"respirator_using_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeEqualTo(Double value) {
            addCriterion("\"respirator_using_time\" =", value, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeNotEqualTo(Double value) {
            addCriterion("\"respirator_using_time\" <>", value, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeGreaterThan(Double value) {
            addCriterion("\"respirator_using_time\" >", value, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"respirator_using_time\" >=", value, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeLessThan(Double value) {
            addCriterion("\"respirator_using_time\" <", value, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeLessThanOrEqualTo(Double value) {
            addCriterion("\"respirator_using_time\" <=", value, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeIn(List<Double> values) {
            addCriterion("\"respirator_using_time\" in", values, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeNotIn(List<Double> values) {
            addCriterion("\"respirator_using_time\" not in", values, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeBetween(Double value1, Double value2) {
            addCriterion("\"respirator_using_time\" between", value1, value2, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andRespiratorUsingTimeNotBetween(Double value1, Double value2) {
            addCriterion("\"respirator_using_time\" not between", value1, value2, "respiratorUsingTime");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysIsNull() {
            addCriterion("\"readmit_plan_in_thirty_days\" is null");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysIsNotNull() {
            addCriterion("\"readmit_plan_in_thirty_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysEqualTo(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" =", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysNotEqualTo(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" <>", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysGreaterThan(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" >", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysGreaterThanOrEqualTo(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" >=", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysLessThan(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" <", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysLessThanOrEqualTo(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" <=", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysLike(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" like", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysNotLike(String value) {
            addCriterion("\"readmit_plan_in_thirty_days\" not like", value, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysIn(List<String> values) {
            addCriterion("\"readmit_plan_in_thirty_days\" in", values, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysNotIn(List<String> values) {
            addCriterion("\"readmit_plan_in_thirty_days\" not in", values, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysBetween(String value1, String value2) {
            addCriterion("\"readmit_plan_in_thirty_days\" between", value1, value2, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitPlanInThirtyDaysNotBetween(String value1, String value2) {
            addCriterion("\"readmit_plan_in_thirty_days\" not between", value1, value2, "readmitPlanInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysIsNull() {
            addCriterion("\"readmit_reason_in_thirty_days\" is null");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysIsNotNull() {
            addCriterion("\"readmit_reason_in_thirty_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysEqualTo(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" =", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysNotEqualTo(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" <>", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysGreaterThan(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" >", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysGreaterThanOrEqualTo(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" >=", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysLessThan(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" <", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysLessThanOrEqualTo(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" <=", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysLike(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" like", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysNotLike(String value) {
            addCriterion("\"readmit_reason_in_thirty_days\" not like", value, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysIn(List<String> values) {
            addCriterion("\"readmit_reason_in_thirty_days\" in", values, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysNotIn(List<String> values) {
            addCriterion("\"readmit_reason_in_thirty_days\" not in", values, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysBetween(String value1, String value2) {
            addCriterion("\"readmit_reason_in_thirty_days\" between", value1, value2, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andReadmitReasonInThirtyDaysNotBetween(String value1, String value2) {
            addCriterion("\"readmit_reason_in_thirty_days\" not between", value1, value2, "readmitReasonInThirtyDays");
            return (Criteria) this;
        }

        public Criteria andDischargeWayIsNull() {
            addCriterion("\"discharge_way\" is null");
            return (Criteria) this;
        }

        public Criteria andDischargeWayIsNotNull() {
            addCriterion("\"discharge_way\" is not null");
            return (Criteria) this;
        }

        public Criteria andDischargeWayEqualTo(String value) {
            addCriterion("\"discharge_way\" =", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotEqualTo(String value) {
            addCriterion("\"discharge_way\" <>", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayGreaterThan(String value) {
            addCriterion("\"discharge_way\" >", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayGreaterThanOrEqualTo(String value) {
            addCriterion("\"discharge_way\" >=", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayLessThan(String value) {
            addCriterion("\"discharge_way\" <", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayLessThanOrEqualTo(String value) {
            addCriterion("\"discharge_way\" <=", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayLike(String value) {
            addCriterion("\"discharge_way\" like", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotLike(String value) {
            addCriterion("\"discharge_way\" not like", value, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayIn(List<String> values) {
            addCriterion("\"discharge_way\" in", values, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotIn(List<String> values) {
            addCriterion("\"discharge_way\" not in", values, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayBetween(String value1, String value2) {
            addCriterion("\"discharge_way\" between", value1, value2, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andDischargeWayNotBetween(String value1, String value2) {
            addCriterion("\"discharge_way\" not between", value1, value2, "dischargeWay");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalIsNull() {
            addCriterion("\"thansfer_to_hospital\" is null");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalIsNotNull() {
            addCriterion("\"thansfer_to_hospital\" is not null");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalEqualTo(String value) {
            addCriterion("\"thansfer_to_hospital\" =", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalNotEqualTo(String value) {
            addCriterion("\"thansfer_to_hospital\" <>", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalGreaterThan(String value) {
            addCriterion("\"thansfer_to_hospital\" >", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalGreaterThanOrEqualTo(String value) {
            addCriterion("\"thansfer_to_hospital\" >=", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalLessThan(String value) {
            addCriterion("\"thansfer_to_hospital\" <", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalLessThanOrEqualTo(String value) {
            addCriterion("\"thansfer_to_hospital\" <=", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalLike(String value) {
            addCriterion("\"thansfer_to_hospital\" like", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalNotLike(String value) {
            addCriterion("\"thansfer_to_hospital\" not like", value, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalIn(List<String> values) {
            addCriterion("\"thansfer_to_hospital\" in", values, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalNotIn(List<String> values) {
            addCriterion("\"thansfer_to_hospital\" not in", values, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalBetween(String value1, String value2) {
            addCriterion("\"thansfer_to_hospital\" between", value1, value2, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andThansferToHospitalNotBetween(String value1, String value2) {
            addCriterion("\"thansfer_to_hospital\" not between", value1, value2, "thansferToHospital");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andIcuDaysIsNull() {
            addCriterion("\"icu_days\" is null");
            return (Criteria) this;
        }

        public Criteria andIcuDaysIsNotNull() {
            addCriterion("\"icu_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andIcuDaysEqualTo(Integer value) {
            addCriterion("\"icu_days\" =", value, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysNotEqualTo(Integer value) {
            addCriterion("\"icu_days\" <>", value, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysGreaterThan(Integer value) {
            addCriterion("\"icu_days\" >", value, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"icu_days\" >=", value, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysLessThan(Integer value) {
            addCriterion("\"icu_days\" <", value, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"icu_days\" <=", value, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysIn(List<Integer> values) {
            addCriterion("\"icu_days\" in", values, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysNotIn(List<Integer> values) {
            addCriterion("\"icu_days\" not in", values, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"icu_days\" between", value1, value2, "icuDays");
            return (Criteria) this;
        }

        public Criteria andIcuDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"icu_days\" not between", value1, value2, "icuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysIsNull() {
            addCriterion("\"ccu_days\" is null");
            return (Criteria) this;
        }

        public Criteria andCcuDaysIsNotNull() {
            addCriterion("\"ccu_days\" is not null");
            return (Criteria) this;
        }

        public Criteria andCcuDaysEqualTo(Integer value) {
            addCriterion("\"ccu_days\" =", value, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysNotEqualTo(Integer value) {
            addCriterion("\"ccu_days\" <>", value, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysGreaterThan(Integer value) {
            addCriterion("\"ccu_days\" >", value, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"ccu_days\" >=", value, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysLessThan(Integer value) {
            addCriterion("\"ccu_days\" <", value, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysLessThanOrEqualTo(Integer value) {
            addCriterion("\"ccu_days\" <=", value, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysIn(List<Integer> values) {
            addCriterion("\"ccu_days\" in", values, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysNotIn(List<Integer> values) {
            addCriterion("\"ccu_days\" not in", values, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysBetween(Integer value1, Integer value2) {
            addCriterion("\"ccu_days\" between", value1, value2, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andCcuDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("\"ccu_days\" not between", value1, value2, "ccuDays");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntIsNull() {
            addCriterion("\"tb_patient_treatmrnt\" is null");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntIsNotNull() {
            addCriterion("\"tb_patient_treatmrnt\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntEqualTo(String value) {
            addCriterion("\"tb_patient_treatmrnt\" =", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntNotEqualTo(String value) {
            addCriterion("\"tb_patient_treatmrnt\" <>", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntGreaterThan(String value) {
            addCriterion("\"tb_patient_treatmrnt\" >", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_patient_treatmrnt\" >=", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntLessThan(String value) {
            addCriterion("\"tb_patient_treatmrnt\" <", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntLessThanOrEqualTo(String value) {
            addCriterion("\"tb_patient_treatmrnt\" <=", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntLike(String value) {
            addCriterion("\"tb_patient_treatmrnt\" like", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntNotLike(String value) {
            addCriterion("\"tb_patient_treatmrnt\" not like", value, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntIn(List<String> values) {
            addCriterion("\"tb_patient_treatmrnt\" in", values, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntNotIn(List<String> values) {
            addCriterion("\"tb_patient_treatmrnt\" not in", values, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntBetween(String value1, String value2) {
            addCriterion("\"tb_patient_treatmrnt\" between", value1, value2, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbPatientTreatmrntNotBetween(String value1, String value2) {
            addCriterion("\"tb_patient_treatmrnt\" not between", value1, value2, "tbPatientTreatmrnt");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeIsNull() {
            addCriterion("\"tb_resistance_type\" is null");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeIsNotNull() {
            addCriterion("\"tb_resistance_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeEqualTo(String value) {
            addCriterion("\"tb_resistance_type\" =", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeNotEqualTo(String value) {
            addCriterion("\"tb_resistance_type\" <>", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeGreaterThan(String value) {
            addCriterion("\"tb_resistance_type\" >", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_resistance_type\" >=", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeLessThan(String value) {
            addCriterion("\"tb_resistance_type\" <", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeLessThanOrEqualTo(String value) {
            addCriterion("\"tb_resistance_type\" <=", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeLike(String value) {
            addCriterion("\"tb_resistance_type\" like", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeNotLike(String value) {
            addCriterion("\"tb_resistance_type\" not like", value, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeIn(List<String> values) {
            addCriterion("\"tb_resistance_type\" in", values, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeNotIn(List<String> values) {
            addCriterion("\"tb_resistance_type\" not in", values, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeBetween(String value1, String value2) {
            addCriterion("\"tb_resistance_type\" between", value1, value2, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbResistanceTypeNotBetween(String value1, String value2) {
            addCriterion("\"tb_resistance_type\" not between", value1, value2, "tbResistanceType");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureIsNull() {
            addCriterion("\"tb_sputum_culture\" is null");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureIsNotNull() {
            addCriterion("\"tb_sputum_culture\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureEqualTo(String value) {
            addCriterion("\"tb_sputum_culture\" =", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureNotEqualTo(String value) {
            addCriterion("\"tb_sputum_culture\" <>", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureGreaterThan(String value) {
            addCriterion("\"tb_sputum_culture\" >", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_sputum_culture\" >=", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureLessThan(String value) {
            addCriterion("\"tb_sputum_culture\" <", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureLessThanOrEqualTo(String value) {
            addCriterion("\"tb_sputum_culture\" <=", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureLike(String value) {
            addCriterion("\"tb_sputum_culture\" like", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureNotLike(String value) {
            addCriterion("\"tb_sputum_culture\" not like", value, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureIn(List<String> values) {
            addCriterion("\"tb_sputum_culture\" in", values, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureNotIn(List<String> values) {
            addCriterion("\"tb_sputum_culture\" not in", values, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureBetween(String value1, String value2) {
            addCriterion("\"tb_sputum_culture\" between", value1, value2, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumCultureNotBetween(String value1, String value2) {
            addCriterion("\"tb_sputum_culture\" not between", value1, value2, "tbSputumCulture");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearIsNull() {
            addCriterion("\"tb_sputum_smear\" is null");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearIsNotNull() {
            addCriterion("\"tb_sputum_smear\" is not null");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearEqualTo(String value) {
            addCriterion("\"tb_sputum_smear\" =", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearNotEqualTo(String value) {
            addCriterion("\"tb_sputum_smear\" <>", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearGreaterThan(String value) {
            addCriterion("\"tb_sputum_smear\" >", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearGreaterThanOrEqualTo(String value) {
            addCriterion("\"tb_sputum_smear\" >=", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearLessThan(String value) {
            addCriterion("\"tb_sputum_smear\" <", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearLessThanOrEqualTo(String value) {
            addCriterion("\"tb_sputum_smear\" <=", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearLike(String value) {
            addCriterion("\"tb_sputum_smear\" like", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearNotLike(String value) {
            addCriterion("\"tb_sputum_smear\" not like", value, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearIn(List<String> values) {
            addCriterion("\"tb_sputum_smear\" in", values, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearNotIn(List<String> values) {
            addCriterion("\"tb_sputum_smear\" not in", values, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearBetween(String value1, String value2) {
            addCriterion("\"tb_sputum_smear\" between", value1, value2, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andTbSputumSmearNotBetween(String value1, String value2) {
            addCriterion("\"tb_sputum_smear\" not between", value1, value2, "tbSputumSmear");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}