package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class InpDrugDispenceExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public InpDrugDispenceExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNull() {
            addCriterion("\"record_id\" is null");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNotNull() {
            addCriterion("\"record_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andRecordIdEqualTo(String value) {
            addCriterion("\"record_id\" =", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotEqualTo(String value) {
            addCriterion("\"record_id\" <>", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThan(String value) {
            addCriterion("\"record_id\" >", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"record_id\" >=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThan(String value) {
            addCriterion("\"record_id\" <", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThanOrEqualTo(String value) {
            addCriterion("\"record_id\" <=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLike(String value) {
            addCriterion("\"record_id\" like", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotLike(String value) {
            addCriterion("\"record_id\" not like", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdIn(List<String> values) {
            addCriterion("\"record_id\" in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotIn(List<String> values) {
            addCriterion("\"record_id\" not in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdBetween(String value1, String value2) {
            addCriterion("\"record_id\" between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotBetween(String value1, String value2) {
            addCriterion("\"record_id\" not between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("\"order_id\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("\"order_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("\"order_id\" =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("\"order_id\" <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("\"order_id\" >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_id\" >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("\"order_id\" <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("\"order_id\" <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("\"order_id\" like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("\"order_id\" not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("\"order_id\" in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("\"order_id\" not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("\"order_id\" between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("\"order_id\" not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdIsNull() {
            addCriterion("\"order_group_id\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdIsNotNull() {
            addCriterion("\"order_group_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdEqualTo(String value) {
            addCriterion("\"order_group_id\" =", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdNotEqualTo(String value) {
            addCriterion("\"order_group_id\" <>", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdGreaterThan(String value) {
            addCriterion("\"order_group_id\" >", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_group_id\" >=", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdLessThan(String value) {
            addCriterion("\"order_group_id\" <", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdLessThanOrEqualTo(String value) {
            addCriterion("\"order_group_id\" <=", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdLike(String value) {
            addCriterion("\"order_group_id\" like", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdNotLike(String value) {
            addCriterion("\"order_group_id\" not like", value, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdIn(List<String> values) {
            addCriterion("\"order_group_id\" in", values, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdNotIn(List<String> values) {
            addCriterion("\"order_group_id\" not in", values, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdBetween(String value1, String value2) {
            addCriterion("\"order_group_id\" between", value1, value2, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andOrderGroupIdNotBetween(String value1, String value2) {
            addCriterion("\"order_group_id\" not between", value1, value2, "orderGroupId");
            return (Criteria) this;
        }

        public Criteria andDrugNameIsNull() {
            addCriterion("\"drug_name\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugNameIsNotNull() {
            addCriterion("\"drug_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugNameEqualTo(String value) {
            addCriterion("\"drug_name\" =", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotEqualTo(String value) {
            addCriterion("\"drug_name\" <>", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameGreaterThan(String value) {
            addCriterion("\"drug_name\" >", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_name\" >=", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameLessThan(String value) {
            addCriterion("\"drug_name\" <", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameLessThanOrEqualTo(String value) {
            addCriterion("\"drug_name\" <=", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameLike(String value) {
            addCriterion("\"drug_name\" like", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotLike(String value) {
            addCriterion("\"drug_name\" not like", value, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameIn(List<String> values) {
            addCriterion("\"drug_name\" in", values, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotIn(List<String> values) {
            addCriterion("\"drug_name\" not in", values, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameBetween(String value1, String value2) {
            addCriterion("\"drug_name\" between", value1, value2, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugNameNotBetween(String value1, String value2) {
            addCriterion("\"drug_name\" not between", value1, value2, "drugName");
            return (Criteria) this;
        }

        public Criteria andDrugCodeIsNull() {
            addCriterion("\"drug_code\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugCodeIsNotNull() {
            addCriterion("\"drug_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugCodeEqualTo(String value) {
            addCriterion("\"drug_code\" =", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeNotEqualTo(String value) {
            addCriterion("\"drug_code\" <>", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeGreaterThan(String value) {
            addCriterion("\"drug_code\" >", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_code\" >=", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeLessThan(String value) {
            addCriterion("\"drug_code\" <", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeLessThanOrEqualTo(String value) {
            addCriterion("\"drug_code\" <=", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeLike(String value) {
            addCriterion("\"drug_code\" like", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeNotLike(String value) {
            addCriterion("\"drug_code\" not like", value, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeIn(List<String> values) {
            addCriterion("\"drug_code\" in", values, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeNotIn(List<String> values) {
            addCriterion("\"drug_code\" not in", values, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeBetween(String value1, String value2) {
            addCriterion("\"drug_code\" between", value1, value2, "drugCode");
            return (Criteria) this;
        }

        public Criteria andDrugCodeNotBetween(String value1, String value2) {
            addCriterion("\"drug_code\" not between", value1, value2, "drugCode");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("\"brand_name\" is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("\"brand_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("\"brand_name\" =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("\"brand_name\" <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("\"brand_name\" >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"brand_name\" >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("\"brand_name\" <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("\"brand_name\" <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("\"brand_name\" like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("\"brand_name\" not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("\"brand_name\" in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("\"brand_name\" not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("\"brand_name\" between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("\"brand_name\" not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andGenericNameIsNull() {
            addCriterion("\"generic_name\" is null");
            return (Criteria) this;
        }

        public Criteria andGenericNameIsNotNull() {
            addCriterion("\"generic_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andGenericNameEqualTo(String value) {
            addCriterion("\"generic_name\" =", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotEqualTo(String value) {
            addCriterion("\"generic_name\" <>", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameGreaterThan(String value) {
            addCriterion("\"generic_name\" >", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"generic_name\" >=", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLessThan(String value) {
            addCriterion("\"generic_name\" <", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLessThanOrEqualTo(String value) {
            addCriterion("\"generic_name\" <=", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameLike(String value) {
            addCriterion("\"generic_name\" like", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotLike(String value) {
            addCriterion("\"generic_name\" not like", value, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameIn(List<String> values) {
            addCriterion("\"generic_name\" in", values, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotIn(List<String> values) {
            addCriterion("\"generic_name\" not in", values, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameBetween(String value1, String value2) {
            addCriterion("\"generic_name\" between", value1, value2, "genericName");
            return (Criteria) this;
        }

        public Criteria andGenericNameNotBetween(String value1, String value2) {
            addCriterion("\"generic_name\" not between", value1, value2, "genericName");
            return (Criteria) this;
        }

        public Criteria andDrugTypeIsNull() {
            addCriterion("\"drug_type\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugTypeIsNotNull() {
            addCriterion("\"drug_type\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugTypeEqualTo(String value) {
            addCriterion("\"drug_type\" =", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeNotEqualTo(String value) {
            addCriterion("\"drug_type\" <>", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeGreaterThan(String value) {
            addCriterion("\"drug_type\" >", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_type\" >=", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeLessThan(String value) {
            addCriterion("\"drug_type\" <", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeLessThanOrEqualTo(String value) {
            addCriterion("\"drug_type\" <=", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeLike(String value) {
            addCriterion("\"drug_type\" like", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeNotLike(String value) {
            addCriterion("\"drug_type\" not like", value, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeIn(List<String> values) {
            addCriterion("\"drug_type\" in", values, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeNotIn(List<String> values) {
            addCriterion("\"drug_type\" not in", values, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeBetween(String value1, String value2) {
            addCriterion("\"drug_type\" between", value1, value2, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugTypeNotBetween(String value1, String value2) {
            addCriterion("\"drug_type\" not between", value1, value2, "drugType");
            return (Criteria) this;
        }

        public Criteria andDrugFormIsNull() {
            addCriterion("\"drug_form\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugFormIsNotNull() {
            addCriterion("\"drug_form\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugFormEqualTo(String value) {
            addCriterion("\"drug_form\" =", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormNotEqualTo(String value) {
            addCriterion("\"drug_form\" <>", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormGreaterThan(String value) {
            addCriterion("\"drug_form\" >", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_form\" >=", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormLessThan(String value) {
            addCriterion("\"drug_form\" <", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormLessThanOrEqualTo(String value) {
            addCriterion("\"drug_form\" <=", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormLike(String value) {
            addCriterion("\"drug_form\" like", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormNotLike(String value) {
            addCriterion("\"drug_form\" not like", value, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormIn(List<String> values) {
            addCriterion("\"drug_form\" in", values, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormNotIn(List<String> values) {
            addCriterion("\"drug_form\" not in", values, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormBetween(String value1, String value2) {
            addCriterion("\"drug_form\" between", value1, value2, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugFormNotBetween(String value1, String value2) {
            addCriterion("\"drug_form\" not between", value1, value2, "drugForm");
            return (Criteria) this;
        }

        public Criteria andDrugSpecIsNull() {
            addCriterion("\"drug_spec\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugSpecIsNotNull() {
            addCriterion("\"drug_spec\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugSpecEqualTo(String value) {
            addCriterion("\"drug_spec\" =", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotEqualTo(String value) {
            addCriterion("\"drug_spec\" <>", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecGreaterThan(String value) {
            addCriterion("\"drug_spec\" >", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_spec\" >=", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecLessThan(String value) {
            addCriterion("\"drug_spec\" <", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecLessThanOrEqualTo(String value) {
            addCriterion("\"drug_spec\" <=", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecLike(String value) {
            addCriterion("\"drug_spec\" like", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotLike(String value) {
            addCriterion("\"drug_spec\" not like", value, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecIn(List<String> values) {
            addCriterion("\"drug_spec\" in", values, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotIn(List<String> values) {
            addCriterion("\"drug_spec\" not in", values, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecBetween(String value1, String value2) {
            addCriterion("\"drug_spec\" between", value1, value2, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andDrugSpecNotBetween(String value1, String value2) {
            addCriterion("\"drug_spec\" not between", value1, value2, "drugSpec");
            return (Criteria) this;
        }

        public Criteria andFirmIsNull() {
            addCriterion("\"firm\" is null");
            return (Criteria) this;
        }

        public Criteria andFirmIsNotNull() {
            addCriterion("\"firm\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirmEqualTo(String value) {
            addCriterion("\"firm\" =", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmNotEqualTo(String value) {
            addCriterion("\"firm\" <>", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmGreaterThan(String value) {
            addCriterion("\"firm\" >", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmGreaterThanOrEqualTo(String value) {
            addCriterion("\"firm\" >=", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmLessThan(String value) {
            addCriterion("\"firm\" <", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmLessThanOrEqualTo(String value) {
            addCriterion("\"firm\" <=", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmLike(String value) {
            addCriterion("\"firm\" like", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmNotLike(String value) {
            addCriterion("\"firm\" not like", value, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmIn(List<String> values) {
            addCriterion("\"firm\" in", values, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmNotIn(List<String> values) {
            addCriterion("\"firm\" not in", values, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmBetween(String value1, String value2) {
            addCriterion("\"firm\" between", value1, value2, "firm");
            return (Criteria) this;
        }

        public Criteria andFirmNotBetween(String value1, String value2) {
            addCriterion("\"firm\" not between", value1, value2, "firm");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIsNull() {
            addCriterion("\"administration_route\" is null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIsNotNull() {
            addCriterion("\"administration_route\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteEqualTo(String value) {
            addCriterion("\"administration_route\" =", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotEqualTo(String value) {
            addCriterion("\"administration_route\" <>", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteGreaterThan(String value) {
            addCriterion("\"administration_route\" >", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteGreaterThanOrEqualTo(String value) {
            addCriterion("\"administration_route\" >=", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLessThan(String value) {
            addCriterion("\"administration_route\" <", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLessThanOrEqualTo(String value) {
            addCriterion("\"administration_route\" <=", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteLike(String value) {
            addCriterion("\"administration_route\" like", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotLike(String value) {
            addCriterion("\"administration_route\" not like", value, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteIn(List<String> values) {
            addCriterion("\"administration_route\" in", values, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotIn(List<String> values) {
            addCriterion("\"administration_route\" not in", values, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteBetween(String value1, String value2) {
            addCriterion("\"administration_route\" between", value1, value2, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteNotBetween(String value1, String value2) {
            addCriterion("\"administration_route\" not between", value1, value2, "administrationRoute");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeIsNull() {
            addCriterion("\"administration_route_code\" is null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeIsNotNull() {
            addCriterion("\"administration_route_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeEqualTo(String value) {
            addCriterion("\"administration_route_code\" =", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeNotEqualTo(String value) {
            addCriterion("\"administration_route_code\" <>", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeGreaterThan(String value) {
            addCriterion("\"administration_route_code\" >", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"administration_route_code\" >=", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeLessThan(String value) {
            addCriterion("\"administration_route_code\" <", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeLessThanOrEqualTo(String value) {
            addCriterion("\"administration_route_code\" <=", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeLike(String value) {
            addCriterion("\"administration_route_code\" like", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeNotLike(String value) {
            addCriterion("\"administration_route_code\" not like", value, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeIn(List<String> values) {
            addCriterion("\"administration_route_code\" in", values, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeNotIn(List<String> values) {
            addCriterion("\"administration_route_code\" not in", values, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeBetween(String value1, String value2) {
            addCriterion("\"administration_route_code\" between", value1, value2, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andAdministrationRouteCodeNotBetween(String value1, String value2) {
            addCriterion("\"administration_route_code\" not between", value1, value2, "administrationRouteCode");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("\"quantity\" is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("\"quantity\" is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(String value) {
            addCriterion("\"quantity\" =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(String value) {
            addCriterion("\"quantity\" <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(String value) {
            addCriterion("\"quantity\" >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(String value) {
            addCriterion("\"quantity\" >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(String value) {
            addCriterion("\"quantity\" <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(String value) {
            addCriterion("\"quantity\" <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLike(String value) {
            addCriterion("\"quantity\" like", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotLike(String value) {
            addCriterion("\"quantity\" not like", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<String> values) {
            addCriterion("\"quantity\" in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<String> values) {
            addCriterion("\"quantity\" not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(String value1, String value2) {
            addCriterion("\"quantity\" between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(String value1, String value2) {
            addCriterion("\"quantity\" not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("\"unit\" is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("\"unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("\"unit\" =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("\"unit\" <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("\"unit\" >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"unit\" >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("\"unit\" <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("\"unit\" <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("\"unit\" like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("\"unit\" not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("\"unit\" in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("\"unit\" not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("\"unit\" between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("\"unit\" not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNull() {
            addCriterion("\"unit_code\" is null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIsNotNull() {
            addCriterion("\"unit_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andUnitCodeEqualTo(String value) {
            addCriterion("\"unit_code\" =", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotEqualTo(String value) {
            addCriterion("\"unit_code\" <>", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThan(String value) {
            addCriterion("\"unit_code\" >", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"unit_code\" >=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThan(String value) {
            addCriterion("\"unit_code\" <", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("\"unit_code\" <=", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeLike(String value) {
            addCriterion("\"unit_code\" like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotLike(String value) {
            addCriterion("\"unit_code\" not like", value, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeIn(List<String> values) {
            addCriterion("\"unit_code\" in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotIn(List<String> values) {
            addCriterion("\"unit_code\" not in", values, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeBetween(String value1, String value2) {
            addCriterion("\"unit_code\" between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andUnitCodeNotBetween(String value1, String value2) {
            addCriterion("\"unit_code\" not between", value1, value2, "unitCode");
            return (Criteria) this;
        }

        public Criteria andDaysIsNull() {
            addCriterion("\"days\" is null");
            return (Criteria) this;
        }

        public Criteria andDaysIsNotNull() {
            addCriterion("\"days\" is not null");
            return (Criteria) this;
        }

        public Criteria andDaysEqualTo(String value) {
            addCriterion("\"days\" =", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotEqualTo(String value) {
            addCriterion("\"days\" <>", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThan(String value) {
            addCriterion("\"days\" >", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysGreaterThanOrEqualTo(String value) {
            addCriterion("\"days\" >=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThan(String value) {
            addCriterion("\"days\" <", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLessThanOrEqualTo(String value) {
            addCriterion("\"days\" <=", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysLike(String value) {
            addCriterion("\"days\" like", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotLike(String value) {
            addCriterion("\"days\" not like", value, "days");
            return (Criteria) this;
        }

        public Criteria andDaysIn(List<String> values) {
            addCriterion("\"days\" in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotIn(List<String> values) {
            addCriterion("\"days\" not in", values, "days");
            return (Criteria) this;
        }

        public Criteria andDaysBetween(String value1, String value2) {
            addCriterion("\"days\" between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andDaysNotBetween(String value1, String value2) {
            addCriterion("\"days\" not between", value1, value2, "days");
            return (Criteria) this;
        }

        public Criteria andTimesIsNull() {
            addCriterion("\"times\" is null");
            return (Criteria) this;
        }

        public Criteria andTimesIsNotNull() {
            addCriterion("\"times\" is not null");
            return (Criteria) this;
        }

        public Criteria andTimesEqualTo(String value) {
            addCriterion("\"times\" =", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotEqualTo(String value) {
            addCriterion("\"times\" <>", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesGreaterThan(String value) {
            addCriterion("\"times\" >", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesGreaterThanOrEqualTo(String value) {
            addCriterion("\"times\" >=", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesLessThan(String value) {
            addCriterion("\"times\" <", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesLessThanOrEqualTo(String value) {
            addCriterion("\"times\" <=", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesLike(String value) {
            addCriterion("\"times\" like", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotLike(String value) {
            addCriterion("\"times\" not like", value, "times");
            return (Criteria) this;
        }

        public Criteria andTimesIn(List<String> values) {
            addCriterion("\"times\" in", values, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotIn(List<String> values) {
            addCriterion("\"times\" not in", values, "times");
            return (Criteria) this;
        }

        public Criteria andTimesBetween(String value1, String value2) {
            addCriterion("\"times\" between", value1, value2, "times");
            return (Criteria) this;
        }

        public Criteria andTimesNotBetween(String value1, String value2) {
            addCriterion("\"times\" not between", value1, value2, "times");
            return (Criteria) this;
        }

        public Criteria andCostsIsNull() {
            addCriterion("\"costs\" is null");
            return (Criteria) this;
        }

        public Criteria andCostsIsNotNull() {
            addCriterion("\"costs\" is not null");
            return (Criteria) this;
        }

        public Criteria andCostsEqualTo(String value) {
            addCriterion("\"costs\" =", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotEqualTo(String value) {
            addCriterion("\"costs\" <>", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsGreaterThan(String value) {
            addCriterion("\"costs\" >", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsGreaterThanOrEqualTo(String value) {
            addCriterion("\"costs\" >=", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsLessThan(String value) {
            addCriterion("\"costs\" <", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsLessThanOrEqualTo(String value) {
            addCriterion("\"costs\" <=", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsLike(String value) {
            addCriterion("\"costs\" like", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotLike(String value) {
            addCriterion("\"costs\" not like", value, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsIn(List<String> values) {
            addCriterion("\"costs\" in", values, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotIn(List<String> values) {
            addCriterion("\"costs\" not in", values, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsBetween(String value1, String value2) {
            addCriterion("\"costs\" between", value1, value2, "costs");
            return (Criteria) this;
        }

        public Criteria andCostsNotBetween(String value1, String value2) {
            addCriterion("\"costs\" not between", value1, value2, "costs");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberIsNull() {
            addCriterion("\"drug_approval_number\" is null");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberIsNotNull() {
            addCriterion("\"drug_approval_number\" is not null");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberEqualTo(String value) {
            addCriterion("\"drug_approval_number\" =", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberNotEqualTo(String value) {
            addCriterion("\"drug_approval_number\" <>", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberGreaterThan(String value) {
            addCriterion("\"drug_approval_number\" >", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberGreaterThanOrEqualTo(String value) {
            addCriterion("\"drug_approval_number\" >=", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberLessThan(String value) {
            addCriterion("\"drug_approval_number\" <", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberLessThanOrEqualTo(String value) {
            addCriterion("\"drug_approval_number\" <=", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberLike(String value) {
            addCriterion("\"drug_approval_number\" like", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberNotLike(String value) {
            addCriterion("\"drug_approval_number\" not like", value, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberIn(List<String> values) {
            addCriterion("\"drug_approval_number\" in", values, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberNotIn(List<String> values) {
            addCriterion("\"drug_approval_number\" not in", values, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberBetween(String value1, String value2) {
            addCriterion("\"drug_approval_number\" between", value1, value2, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andDrugApprovalNumberNotBetween(String value1, String value2) {
            addCriterion("\"drug_approval_number\" not between", value1, value2, "drugApprovalNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNull() {
            addCriterion("\"batch_no\" is null");
            return (Criteria) this;
        }

        public Criteria andBatchNoIsNotNull() {
            addCriterion("\"batch_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNoEqualTo(String value) {
            addCriterion("\"batch_no\" =", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotEqualTo(String value) {
            addCriterion("\"batch_no\" <>", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThan(String value) {
            addCriterion("\"batch_no\" >", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"batch_no\" >=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThan(String value) {
            addCriterion("\"batch_no\" <", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLessThanOrEqualTo(String value) {
            addCriterion("\"batch_no\" <=", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoLike(String value) {
            addCriterion("\"batch_no\" like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotLike(String value) {
            addCriterion("\"batch_no\" not like", value, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoIn(List<String> values) {
            addCriterion("\"batch_no\" in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotIn(List<String> values) {
            addCriterion("\"batch_no\" not in", values, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoBetween(String value1, String value2) {
            addCriterion("\"batch_no\" between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andBatchNoNotBetween(String value1, String value2) {
            addCriterion("\"batch_no\" not between", value1, value2, "batchNo");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIsNull() {
            addCriterion("\"expiration_date\" is null");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIsNotNull() {
            addCriterion("\"expiration_date\" is not null");
            return (Criteria) this;
        }

        public Criteria andExpirationDateEqualTo(Date value) {
            addCriterion("\"expiration_date\" =", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotEqualTo(Date value) {
            addCriterion("\"expiration_date\" <>", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateGreaterThan(Date value) {
            addCriterion("\"expiration_date\" >", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("\"expiration_date\" >=", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLessThan(Date value) {
            addCriterion("\"expiration_date\" <", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLessThanOrEqualTo(Date value) {
            addCriterion("\"expiration_date\" <=", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIn(List<Date> values) {
            addCriterion("\"expiration_date\" in", values, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotIn(List<Date> values) {
            addCriterion("\"expiration_date\" not in", values, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateBetween(Date value1, Date value2) {
            addCriterion("\"expiration_date\" between", value1, value2, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotBetween(Date value1, Date value2) {
            addCriterion("\"expiration_date\" not between", value1, value2, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitIsNull() {
            addCriterion("\"min_pack_unit\" is null");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitIsNotNull() {
            addCriterion("\"min_pack_unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitEqualTo(String value) {
            addCriterion("\"min_pack_unit\" =", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitNotEqualTo(String value) {
            addCriterion("\"min_pack_unit\" <>", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitGreaterThan(String value) {
            addCriterion("\"min_pack_unit\" >", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"min_pack_unit\" >=", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitLessThan(String value) {
            addCriterion("\"min_pack_unit\" <", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitLessThanOrEqualTo(String value) {
            addCriterion("\"min_pack_unit\" <=", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitLike(String value) {
            addCriterion("\"min_pack_unit\" like", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitNotLike(String value) {
            addCriterion("\"min_pack_unit\" not like", value, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitIn(List<String> values) {
            addCriterion("\"min_pack_unit\" in", values, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitNotIn(List<String> values) {
            addCriterion("\"min_pack_unit\" not in", values, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitBetween(String value1, String value2) {
            addCriterion("\"min_pack_unit\" between", value1, value2, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitNotBetween(String value1, String value2) {
            addCriterion("\"min_pack_unit\" not between", value1, value2, "minPackUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeIsNull() {
            addCriterion("\"min_pack_unit_code\" is null");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeIsNotNull() {
            addCriterion("\"min_pack_unit_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeEqualTo(String value) {
            addCriterion("\"min_pack_unit_code\" =", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeNotEqualTo(String value) {
            addCriterion("\"min_pack_unit_code\" <>", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeGreaterThan(String value) {
            addCriterion("\"min_pack_unit_code\" >", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"min_pack_unit_code\" >=", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeLessThan(String value) {
            addCriterion("\"min_pack_unit_code\" <", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("\"min_pack_unit_code\" <=", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeLike(String value) {
            addCriterion("\"min_pack_unit_code\" like", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeNotLike(String value) {
            addCriterion("\"min_pack_unit_code\" not like", value, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeIn(List<String> values) {
            addCriterion("\"min_pack_unit_code\" in", values, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeNotIn(List<String> values) {
            addCriterion("\"min_pack_unit_code\" not in", values, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeBetween(String value1, String value2) {
            addCriterion("\"min_pack_unit_code\" between", value1, value2, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackUnitCodeNotBetween(String value1, String value2) {
            addCriterion("\"min_pack_unit_code\" not between", value1, value2, "minPackUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseIsNull() {
            addCriterion("\"min_pack_dose\" is null");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseIsNotNull() {
            addCriterion("\"min_pack_dose\" is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseEqualTo(String value) {
            addCriterion("\"min_pack_dose\" =", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseNotEqualTo(String value) {
            addCriterion("\"min_pack_dose\" <>", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseGreaterThan(String value) {
            addCriterion("\"min_pack_dose\" >", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseGreaterThanOrEqualTo(String value) {
            addCriterion("\"min_pack_dose\" >=", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseLessThan(String value) {
            addCriterion("\"min_pack_dose\" <", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseLessThanOrEqualTo(String value) {
            addCriterion("\"min_pack_dose\" <=", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseLike(String value) {
            addCriterion("\"min_pack_dose\" like", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseNotLike(String value) {
            addCriterion("\"min_pack_dose\" not like", value, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseIn(List<String> values) {
            addCriterion("\"min_pack_dose\" in", values, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseNotIn(List<String> values) {
            addCriterion("\"min_pack_dose\" not in", values, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseBetween(String value1, String value2) {
            addCriterion("\"min_pack_dose\" between", value1, value2, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseNotBetween(String value1, String value2) {
            addCriterion("\"min_pack_dose\" not between", value1, value2, "minPackDose");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitIsNull() {
            addCriterion("\"min_pack_dose_unit\" is null");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitIsNotNull() {
            addCriterion("\"min_pack_dose_unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit\" =", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitNotEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit\" <>", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitGreaterThan(String value) {
            addCriterion("\"min_pack_dose_unit\" >", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit\" >=", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitLessThan(String value) {
            addCriterion("\"min_pack_dose_unit\" <", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitLessThanOrEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit\" <=", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitLike(String value) {
            addCriterion("\"min_pack_dose_unit\" like", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitNotLike(String value) {
            addCriterion("\"min_pack_dose_unit\" not like", value, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitIn(List<String> values) {
            addCriterion("\"min_pack_dose_unit\" in", values, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitNotIn(List<String> values) {
            addCriterion("\"min_pack_dose_unit\" not in", values, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitBetween(String value1, String value2) {
            addCriterion("\"min_pack_dose_unit\" between", value1, value2, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitNotBetween(String value1, String value2) {
            addCriterion("\"min_pack_dose_unit\" not between", value1, value2, "minPackDoseUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeIsNull() {
            addCriterion("\"min_pack_dose_unit_code\" is null");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeIsNotNull() {
            addCriterion("\"min_pack_dose_unit_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit_code\" =", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeNotEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit_code\" <>", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeGreaterThan(String value) {
            addCriterion("\"min_pack_dose_unit_code\" >", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit_code\" >=", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeLessThan(String value) {
            addCriterion("\"min_pack_dose_unit_code\" <", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("\"min_pack_dose_unit_code\" <=", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeLike(String value) {
            addCriterion("\"min_pack_dose_unit_code\" like", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeNotLike(String value) {
            addCriterion("\"min_pack_dose_unit_code\" not like", value, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeIn(List<String> values) {
            addCriterion("\"min_pack_dose_unit_code\" in", values, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeNotIn(List<String> values) {
            addCriterion("\"min_pack_dose_unit_code\" not in", values, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeBetween(String value1, String value2) {
            addCriterion("\"min_pack_dose_unit_code\" between", value1, value2, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andMinPackDoseUnitCodeNotBetween(String value1, String value2) {
            addCriterion("\"min_pack_dose_unit_code\" not between", value1, value2, "minPackDoseUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitIsNull() {
            addCriterion("\"pack_unit\" is null");
            return (Criteria) this;
        }

        public Criteria andPackUnitIsNotNull() {
            addCriterion("\"pack_unit\" is not null");
            return (Criteria) this;
        }

        public Criteria andPackUnitEqualTo(String value) {
            addCriterion("\"pack_unit\" =", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitNotEqualTo(String value) {
            addCriterion("\"pack_unit\" <>", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitGreaterThan(String value) {
            addCriterion("\"pack_unit\" >", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitGreaterThanOrEqualTo(String value) {
            addCriterion("\"pack_unit\" >=", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitLessThan(String value) {
            addCriterion("\"pack_unit\" <", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitLessThanOrEqualTo(String value) {
            addCriterion("\"pack_unit\" <=", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitLike(String value) {
            addCriterion("\"pack_unit\" like", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitNotLike(String value) {
            addCriterion("\"pack_unit\" not like", value, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitIn(List<String> values) {
            addCriterion("\"pack_unit\" in", values, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitNotIn(List<String> values) {
            addCriterion("\"pack_unit\" not in", values, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitBetween(String value1, String value2) {
            addCriterion("\"pack_unit\" between", value1, value2, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitNotBetween(String value1, String value2) {
            addCriterion("\"pack_unit\" not between", value1, value2, "packUnit");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeIsNull() {
            addCriterion("\"pack_unit_code\" is null");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeIsNotNull() {
            addCriterion("\"pack_unit_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeEqualTo(String value) {
            addCriterion("\"pack_unit_code\" =", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeNotEqualTo(String value) {
            addCriterion("\"pack_unit_code\" <>", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeGreaterThan(String value) {
            addCriterion("\"pack_unit_code\" >", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"pack_unit_code\" >=", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeLessThan(String value) {
            addCriterion("\"pack_unit_code\" <", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeLessThanOrEqualTo(String value) {
            addCriterion("\"pack_unit_code\" <=", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeLike(String value) {
            addCriterion("\"pack_unit_code\" like", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeNotLike(String value) {
            addCriterion("\"pack_unit_code\" not like", value, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeIn(List<String> values) {
            addCriterion("\"pack_unit_code\" in", values, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeNotIn(List<String> values) {
            addCriterion("\"pack_unit_code\" not in", values, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeBetween(String value1, String value2) {
            addCriterion("\"pack_unit_code\" between", value1, value2, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andPackUnitCodeNotBetween(String value1, String value2) {
            addCriterion("\"pack_unit_code\" not between", value1, value2, "packUnitCode");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackIsNull() {
            addCriterion("\"quantity_per_pack\" is null");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackIsNotNull() {
            addCriterion("\"quantity_per_pack\" is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackEqualTo(String value) {
            addCriterion("\"quantity_per_pack\" =", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackNotEqualTo(String value) {
            addCriterion("\"quantity_per_pack\" <>", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackGreaterThan(String value) {
            addCriterion("\"quantity_per_pack\" >", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackGreaterThanOrEqualTo(String value) {
            addCriterion("\"quantity_per_pack\" >=", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackLessThan(String value) {
            addCriterion("\"quantity_per_pack\" <", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackLessThanOrEqualTo(String value) {
            addCriterion("\"quantity_per_pack\" <=", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackLike(String value) {
            addCriterion("\"quantity_per_pack\" like", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackNotLike(String value) {
            addCriterion("\"quantity_per_pack\" not like", value, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackIn(List<String> values) {
            addCriterion("\"quantity_per_pack\" in", values, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackNotIn(List<String> values) {
            addCriterion("\"quantity_per_pack\" not in", values, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackBetween(String value1, String value2) {
            addCriterion("\"quantity_per_pack\" between", value1, value2, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andQuantityPerPackNotBetween(String value1, String value2) {
            addCriterion("\"quantity_per_pack\" not between", value1, value2, "quantityPerPack");
            return (Criteria) this;
        }

        public Criteria andOrderDeptIsNull() {
            addCriterion("\"order_dept\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderDeptIsNotNull() {
            addCriterion("\"order_dept\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDeptEqualTo(String value) {
            addCriterion("\"order_dept\" =", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotEqualTo(String value) {
            addCriterion("\"order_dept\" <>", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptGreaterThan(String value) {
            addCriterion("\"order_dept\" >", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_dept\" >=", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptLessThan(String value) {
            addCriterion("\"order_dept\" <", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptLessThanOrEqualTo(String value) {
            addCriterion("\"order_dept\" <=", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptLike(String value) {
            addCriterion("\"order_dept\" like", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotLike(String value) {
            addCriterion("\"order_dept\" not like", value, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptIn(List<String> values) {
            addCriterion("\"order_dept\" in", values, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotIn(List<String> values) {
            addCriterion("\"order_dept\" not in", values, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptBetween(String value1, String value2) {
            addCriterion("\"order_dept\" between", value1, value2, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptNotBetween(String value1, String value2) {
            addCriterion("\"order_dept\" not between", value1, value2, "orderDept");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeIsNull() {
            addCriterion("\"order_dept_code\" is null");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeIsNotNull() {
            addCriterion("\"order_dept_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeEqualTo(String value) {
            addCriterion("\"order_dept_code\" =", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeNotEqualTo(String value) {
            addCriterion("\"order_dept_code\" <>", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeGreaterThan(String value) {
            addCriterion("\"order_dept_code\" >", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"order_dept_code\" >=", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeLessThan(String value) {
            addCriterion("\"order_dept_code\" <", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeLessThanOrEqualTo(String value) {
            addCriterion("\"order_dept_code\" <=", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeLike(String value) {
            addCriterion("\"order_dept_code\" like", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeNotLike(String value) {
            addCriterion("\"order_dept_code\" not like", value, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeIn(List<String> values) {
            addCriterion("\"order_dept_code\" in", values, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeNotIn(List<String> values) {
            addCriterion("\"order_dept_code\" not in", values, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeBetween(String value1, String value2) {
            addCriterion("\"order_dept_code\" between", value1, value2, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderDeptCodeNotBetween(String value1, String value2) {
            addCriterion("\"order_dept_code\" not between", value1, value2, "orderDeptCode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("\"status\" is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("\"status\" is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("\"status\" =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("\"status\" <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("\"status\" >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("\"status\" >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("\"status\" <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("\"status\" <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("\"status\" like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("\"status\" not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("\"status\" in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("\"status\" not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("\"status\" between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("\"status\" not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIsNull() {
            addCriterion("\"operation_time\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIsNotNull() {
            addCriterion("\"operation_time\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationTimeEqualTo(Date value) {
            addCriterion("\"operation_time\" =", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotEqualTo(Date value) {
            addCriterion("\"operation_time\" <>", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeGreaterThan(Date value) {
            addCriterion("\"operation_time\" >", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_time\" >=", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeLessThan(Date value) {
            addCriterion("\"operation_time\" <", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_time\" <=", value, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeIn(List<Date> values) {
            addCriterion("\"operation_time\" in", values, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotIn(List<Date> values) {
            addCriterion("\"operation_time\" not in", values, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_time\" between", value1, value2, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperationTimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_time\" not between", value1, value2, "operationTime");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("\"operator\" is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("\"operator\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("\"operator\" =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("\"operator\" <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("\"operator\" >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("\"operator\" >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("\"operator\" <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("\"operator\" <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("\"operator\" like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("\"operator\" not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("\"operator\" in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("\"operator\" not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("\"operator\" between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("\"operator\" not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("\"operator_id\" is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("\"operator_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdEqualTo(String value) {
            addCriterion("\"operator_id\" =", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotEqualTo(String value) {
            addCriterion("\"operator_id\" <>", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThan(String value) {
            addCriterion("\"operator_id\" >", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"operator_id\" >=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThan(String value) {
            addCriterion("\"operator_id\" <", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(String value) {
            addCriterion("\"operator_id\" <=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLike(String value) {
            addCriterion("\"operator_id\" like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotLike(String value) {
            addCriterion("\"operator_id\" not like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIn(List<String> values) {
            addCriterion("\"operator_id\" in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotIn(List<String> values) {
            addCriterion("\"operator_id\" not in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdBetween(String value1, String value2) {
            addCriterion("\"operator_id\" between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotBetween(String value1, String value2) {
            addCriterion("\"operator_id\" not between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}