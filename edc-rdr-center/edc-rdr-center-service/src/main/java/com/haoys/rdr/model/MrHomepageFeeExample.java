package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.List;

public class MrHomepageFeeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MrHomepageFeeExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTotalCostsIsNull() {
            addCriterion("\"total_costs\" is null");
            return (Criteria) this;
        }

        public Criteria andTotalCostsIsNotNull() {
            addCriterion("\"total_costs\" is not null");
            return (Criteria) this;
        }

        public Criteria andTotalCostsEqualTo(Double value) {
            addCriterion("\"total_costs\" =", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsNotEqualTo(Double value) {
            addCriterion("\"total_costs\" <>", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsGreaterThan(Double value) {
            addCriterion("\"total_costs\" >", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsGreaterThanOrEqualTo(Double value) {
            addCriterion("\"total_costs\" >=", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsLessThan(Double value) {
            addCriterion("\"total_costs\" <", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsLessThanOrEqualTo(Double value) {
            addCriterion("\"total_costs\" <=", value, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsIn(List<Double> values) {
            addCriterion("\"total_costs\" in", values, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsNotIn(List<Double> values) {
            addCriterion("\"total_costs\" not in", values, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsBetween(Double value1, Double value2) {
            addCriterion("\"total_costs\" between", value1, value2, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andTotalCostsNotBetween(Double value1, Double value2) {
            addCriterion("\"total_costs\" not between", value1, value2, "totalCosts");
            return (Criteria) this;
        }

        public Criteria andSelfPayIsNull() {
            addCriterion("\"self_pay\" is null");
            return (Criteria) this;
        }

        public Criteria andSelfPayIsNotNull() {
            addCriterion("\"self_pay\" is not null");
            return (Criteria) this;
        }

        public Criteria andSelfPayEqualTo(Double value) {
            addCriterion("\"self_pay\" =", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayNotEqualTo(Double value) {
            addCriterion("\"self_pay\" <>", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayGreaterThan(Double value) {
            addCriterion("\"self_pay\" >", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayGreaterThanOrEqualTo(Double value) {
            addCriterion("\"self_pay\" >=", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayLessThan(Double value) {
            addCriterion("\"self_pay\" <", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayLessThanOrEqualTo(Double value) {
            addCriterion("\"self_pay\" <=", value, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayIn(List<Double> values) {
            addCriterion("\"self_pay\" in", values, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayNotIn(List<Double> values) {
            addCriterion("\"self_pay\" not in", values, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayBetween(Double value1, Double value2) {
            addCriterion("\"self_pay\" between", value1, value2, "selfPay");
            return (Criteria) this;
        }

        public Criteria andSelfPayNotBetween(Double value1, Double value2) {
            addCriterion("\"self_pay\" not between", value1, value2, "selfPay");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeIsNull() {
            addCriterion("\"general_medical_service_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeIsNotNull() {
            addCriterion("\"general_medical_service_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeEqualTo(Double value) {
            addCriterion("\"general_medical_service_fee\" =", value, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeNotEqualTo(Double value) {
            addCriterion("\"general_medical_service_fee\" <>", value, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeGreaterThan(Double value) {
            addCriterion("\"general_medical_service_fee\" >", value, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"general_medical_service_fee\" >=", value, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeLessThan(Double value) {
            addCriterion("\"general_medical_service_fee\" <", value, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"general_medical_service_fee\" <=", value, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeIn(List<Double> values) {
            addCriterion("\"general_medical_service_fee\" in", values, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeNotIn(List<Double> values) {
            addCriterion("\"general_medical_service_fee\" not in", values, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeBetween(Double value1, Double value2) {
            addCriterion("\"general_medical_service_fee\" between", value1, value2, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralMedicalServiceFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"general_medical_service_fee\" not between", value1, value2, "generalMedicalServiceFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeIsNull() {
            addCriterion("\"general_treatment_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeIsNotNull() {
            addCriterion("\"general_treatment_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeEqualTo(Double value) {
            addCriterion("\"general_treatment_fee\" =", value, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeNotEqualTo(Double value) {
            addCriterion("\"general_treatment_fee\" <>", value, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeGreaterThan(Double value) {
            addCriterion("\"general_treatment_fee\" >", value, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"general_treatment_fee\" >=", value, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeLessThan(Double value) {
            addCriterion("\"general_treatment_fee\" <", value, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"general_treatment_fee\" <=", value, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeIn(List<Double> values) {
            addCriterion("\"general_treatment_fee\" in", values, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeNotIn(List<Double> values) {
            addCriterion("\"general_treatment_fee\" not in", values, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeBetween(Double value1, Double value2) {
            addCriterion("\"general_treatment_fee\" between", value1, value2, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andGeneralTreatmentFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"general_treatment_fee\" not between", value1, value2, "generalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeIsNull() {
            addCriterion("\"nursing_care_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeIsNotNull() {
            addCriterion("\"nursing_care_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeEqualTo(Double value) {
            addCriterion("\"nursing_care_fee\" =", value, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeNotEqualTo(Double value) {
            addCriterion("\"nursing_care_fee\" <>", value, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeGreaterThan(Double value) {
            addCriterion("\"nursing_care_fee\" >", value, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"nursing_care_fee\" >=", value, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeLessThan(Double value) {
            addCriterion("\"nursing_care_fee\" <", value, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"nursing_care_fee\" <=", value, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeIn(List<Double> values) {
            addCriterion("\"nursing_care_fee\" in", values, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeNotIn(List<Double> values) {
            addCriterion("\"nursing_care_fee\" not in", values, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeBetween(Double value1, Double value2) {
            addCriterion("\"nursing_care_fee\" between", value1, value2, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andNursingCareFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"nursing_care_fee\" not between", value1, value2, "nursingCareFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeIsNull() {
            addCriterion("\"other_service_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeIsNotNull() {
            addCriterion("\"other_service_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeEqualTo(Double value) {
            addCriterion("\"other_service_fee\" =", value, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeNotEqualTo(Double value) {
            addCriterion("\"other_service_fee\" <>", value, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeGreaterThan(Double value) {
            addCriterion("\"other_service_fee\" >", value, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"other_service_fee\" >=", value, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeLessThan(Double value) {
            addCriterion("\"other_service_fee\" <", value, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"other_service_fee\" <=", value, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeIn(List<Double> values) {
            addCriterion("\"other_service_fee\" in", values, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeNotIn(List<Double> values) {
            addCriterion("\"other_service_fee\" not in", values, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeBetween(Double value1, Double value2) {
            addCriterion("\"other_service_fee\" between", value1, value2, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andOtherServiceFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"other_service_fee\" not between", value1, value2, "otherServiceFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeIsNull() {
            addCriterion("\"pathological_diagnostic_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeIsNotNull() {
            addCriterion("\"pathological_diagnostic_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeEqualTo(Double value) {
            addCriterion("\"pathological_diagnostic_fee\" =", value, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeNotEqualTo(Double value) {
            addCriterion("\"pathological_diagnostic_fee\" <>", value, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeGreaterThan(Double value) {
            addCriterion("\"pathological_diagnostic_fee\" >", value, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"pathological_diagnostic_fee\" >=", value, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeLessThan(Double value) {
            addCriterion("\"pathological_diagnostic_fee\" <", value, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"pathological_diagnostic_fee\" <=", value, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeIn(List<Double> values) {
            addCriterion("\"pathological_diagnostic_fee\" in", values, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeNotIn(List<Double> values) {
            addCriterion("\"pathological_diagnostic_fee\" not in", values, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeBetween(Double value1, Double value2) {
            addCriterion("\"pathological_diagnostic_fee\" between", value1, value2, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andPathologicalDiagnosticFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"pathological_diagnostic_fee\" not between", value1, value2, "pathologicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeIsNull() {
            addCriterion("\"laboratory_diagnostic_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeIsNotNull() {
            addCriterion("\"laboratory_diagnostic_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeEqualTo(Double value) {
            addCriterion("\"laboratory_diagnostic_fee\" =", value, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeNotEqualTo(Double value) {
            addCriterion("\"laboratory_diagnostic_fee\" <>", value, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeGreaterThan(Double value) {
            addCriterion("\"laboratory_diagnostic_fee\" >", value, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"laboratory_diagnostic_fee\" >=", value, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeLessThan(Double value) {
            addCriterion("\"laboratory_diagnostic_fee\" <", value, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"laboratory_diagnostic_fee\" <=", value, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeIn(List<Double> values) {
            addCriterion("\"laboratory_diagnostic_fee\" in", values, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeNotIn(List<Double> values) {
            addCriterion("\"laboratory_diagnostic_fee\" not in", values, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeBetween(Double value1, Double value2) {
            addCriterion("\"laboratory_diagnostic_fee\" between", value1, value2, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andLaboratoryDiagnosticFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"laboratory_diagnostic_fee\" not between", value1, value2, "laboratoryDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeIsNull() {
            addCriterion("\"imaging_diagnostic_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeIsNotNull() {
            addCriterion("\"imaging_diagnostic_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeEqualTo(Double value) {
            addCriterion("\"imaging_diagnostic_fee\" =", value, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeNotEqualTo(Double value) {
            addCriterion("\"imaging_diagnostic_fee\" <>", value, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeGreaterThan(Double value) {
            addCriterion("\"imaging_diagnostic_fee\" >", value, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"imaging_diagnostic_fee\" >=", value, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeLessThan(Double value) {
            addCriterion("\"imaging_diagnostic_fee\" <", value, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"imaging_diagnostic_fee\" <=", value, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeIn(List<Double> values) {
            addCriterion("\"imaging_diagnostic_fee\" in", values, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeNotIn(List<Double> values) {
            addCriterion("\"imaging_diagnostic_fee\" not in", values, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeBetween(Double value1, Double value2) {
            addCriterion("\"imaging_diagnostic_fee\" between", value1, value2, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andImagingDiagnosticFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"imaging_diagnostic_fee\" not between", value1, value2, "imagingDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeIsNull() {
            addCriterion("\"clinical_diagnostic_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeIsNotNull() {
            addCriterion("\"clinical_diagnostic_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeEqualTo(Double value) {
            addCriterion("\"clinical_diagnostic_fee\" =", value, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeNotEqualTo(Double value) {
            addCriterion("\"clinical_diagnostic_fee\" <>", value, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeGreaterThan(Double value) {
            addCriterion("\"clinical_diagnostic_fee\" >", value, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"clinical_diagnostic_fee\" >=", value, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeLessThan(Double value) {
            addCriterion("\"clinical_diagnostic_fee\" <", value, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"clinical_diagnostic_fee\" <=", value, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeIn(List<Double> values) {
            addCriterion("\"clinical_diagnostic_fee\" in", values, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeNotIn(List<Double> values) {
            addCriterion("\"clinical_diagnostic_fee\" not in", values, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeBetween(Double value1, Double value2) {
            addCriterion("\"clinical_diagnostic_fee\" between", value1, value2, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andClinicalDiagnosticFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"clinical_diagnostic_fee\" not between", value1, value2, "clinicalDiagnosticFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeIsNull() {
            addCriterion("\"nonsurgical_treatment_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeIsNotNull() {
            addCriterion("\"nonsurgical_treatment_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeEqualTo(Double value) {
            addCriterion("\"nonsurgical_treatment_fee\" =", value, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeNotEqualTo(Double value) {
            addCriterion("\"nonsurgical_treatment_fee\" <>", value, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeGreaterThan(Double value) {
            addCriterion("\"nonsurgical_treatment_fee\" >", value, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"nonsurgical_treatment_fee\" >=", value, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeLessThan(Double value) {
            addCriterion("\"nonsurgical_treatment_fee\" <", value, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"nonsurgical_treatment_fee\" <=", value, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeIn(List<Double> values) {
            addCriterion("\"nonsurgical_treatment_fee\" in", values, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeNotIn(List<Double> values) {
            addCriterion("\"nonsurgical_treatment_fee\" not in", values, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeBetween(Double value1, Double value2) {
            addCriterion("\"nonsurgical_treatment_fee\" between", value1, value2, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andNonsurgicalTreatmentFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"nonsurgical_treatment_fee\" not between", value1, value2, "nonsurgicalTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeIsNull() {
            addCriterion("\"clinical_physical_therapy_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeIsNotNull() {
            addCriterion("\"clinical_physical_therapy_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeEqualTo(Double value) {
            addCriterion("\"clinical_physical_therapy_fee\" =", value, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeNotEqualTo(Double value) {
            addCriterion("\"clinical_physical_therapy_fee\" <>", value, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeGreaterThan(Double value) {
            addCriterion("\"clinical_physical_therapy_fee\" >", value, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"clinical_physical_therapy_fee\" >=", value, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeLessThan(Double value) {
            addCriterion("\"clinical_physical_therapy_fee\" <", value, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"clinical_physical_therapy_fee\" <=", value, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeIn(List<Double> values) {
            addCriterion("\"clinical_physical_therapy_fee\" in", values, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeNotIn(List<Double> values) {
            addCriterion("\"clinical_physical_therapy_fee\" not in", values, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeBetween(Double value1, Double value2) {
            addCriterion("\"clinical_physical_therapy_fee\" between", value1, value2, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andClinicalPhysicalTherapyFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"clinical_physical_therapy_fee\" not between", value1, value2, "clinicalPhysicalTherapyFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeIsNull() {
            addCriterion("\"surgery_treatment_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeIsNotNull() {
            addCriterion("\"surgery_treatment_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeEqualTo(Double value) {
            addCriterion("\"surgery_treatment_fee\" =", value, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeNotEqualTo(Double value) {
            addCriterion("\"surgery_treatment_fee\" <>", value, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeGreaterThan(Double value) {
            addCriterion("\"surgery_treatment_fee\" >", value, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"surgery_treatment_fee\" >=", value, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeLessThan(Double value) {
            addCriterion("\"surgery_treatment_fee\" <", value, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"surgery_treatment_fee\" <=", value, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeIn(List<Double> values) {
            addCriterion("\"surgery_treatment_fee\" in", values, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeNotIn(List<Double> values) {
            addCriterion("\"surgery_treatment_fee\" not in", values, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeBetween(Double value1, Double value2) {
            addCriterion("\"surgery_treatment_fee\" between", value1, value2, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryTreatmentFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"surgery_treatment_fee\" not between", value1, value2, "surgeryTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeIsNull() {
            addCriterion("\"anesthesia_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeIsNotNull() {
            addCriterion("\"anesthesia_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeEqualTo(Double value) {
            addCriterion("\"anesthesia_fee\" =", value, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeNotEqualTo(Double value) {
            addCriterion("\"anesthesia_fee\" <>", value, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeGreaterThan(Double value) {
            addCriterion("\"anesthesia_fee\" >", value, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"anesthesia_fee\" >=", value, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeLessThan(Double value) {
            addCriterion("\"anesthesia_fee\" <", value, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"anesthesia_fee\" <=", value, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeIn(List<Double> values) {
            addCriterion("\"anesthesia_fee\" in", values, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeNotIn(List<Double> values) {
            addCriterion("\"anesthesia_fee\" not in", values, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeBetween(Double value1, Double value2) {
            addCriterion("\"anesthesia_fee\" between", value1, value2, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"anesthesia_fee\" not between", value1, value2, "anesthesiaFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeIsNull() {
            addCriterion("\"surgery_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeIsNotNull() {
            addCriterion("\"surgery_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeEqualTo(Double value) {
            addCriterion("\"surgery_fee\" =", value, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeNotEqualTo(Double value) {
            addCriterion("\"surgery_fee\" <>", value, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeGreaterThan(Double value) {
            addCriterion("\"surgery_fee\" >", value, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"surgery_fee\" >=", value, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeLessThan(Double value) {
            addCriterion("\"surgery_fee\" <", value, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"surgery_fee\" <=", value, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeIn(List<Double> values) {
            addCriterion("\"surgery_fee\" in", values, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeNotIn(List<Double> values) {
            addCriterion("\"surgery_fee\" not in", values, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeBetween(Double value1, Double value2) {
            addCriterion("\"surgery_fee\" between", value1, value2, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"surgery_fee\" not between", value1, value2, "surgeryFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeIsNull() {
            addCriterion("\"rehabilitation_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeIsNotNull() {
            addCriterion("\"rehabilitation_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeEqualTo(Double value) {
            addCriterion("\"rehabilitation_fee\" =", value, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeNotEqualTo(Double value) {
            addCriterion("\"rehabilitation_fee\" <>", value, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeGreaterThan(Double value) {
            addCriterion("\"rehabilitation_fee\" >", value, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"rehabilitation_fee\" >=", value, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeLessThan(Double value) {
            addCriterion("\"rehabilitation_fee\" <", value, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"rehabilitation_fee\" <=", value, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeIn(List<Double> values) {
            addCriterion("\"rehabilitation_fee\" in", values, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeNotIn(List<Double> values) {
            addCriterion("\"rehabilitation_fee\" not in", values, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeBetween(Double value1, Double value2) {
            addCriterion("\"rehabilitation_fee\" between", value1, value2, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andRehabilitationFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"rehabilitation_fee\" not between", value1, value2, "rehabilitationFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeIsNull() {
            addCriterion("\"chinese_medicine_treatment_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeIsNotNull() {
            addCriterion("\"chinese_medicine_treatment_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeEqualTo(Double value) {
            addCriterion("\"chinese_medicine_treatment_fee\" =", value, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeNotEqualTo(Double value) {
            addCriterion("\"chinese_medicine_treatment_fee\" <>", value, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeGreaterThan(Double value) {
            addCriterion("\"chinese_medicine_treatment_fee\" >", value, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"chinese_medicine_treatment_fee\" >=", value, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeLessThan(Double value) {
            addCriterion("\"chinese_medicine_treatment_fee\" <", value, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"chinese_medicine_treatment_fee\" <=", value, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeIn(List<Double> values) {
            addCriterion("\"chinese_medicine_treatment_fee\" in", values, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeNotIn(List<Double> values) {
            addCriterion("\"chinese_medicine_treatment_fee\" not in", values, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeBetween(Double value1, Double value2) {
            addCriterion("\"chinese_medicine_treatment_fee\" between", value1, value2, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andChineseMedicineTreatmentFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"chinese_medicine_treatment_fee\" not between", value1, value2, "chineseMedicineTreatmentFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeIsNull() {
            addCriterion("\"western_medicine_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeIsNotNull() {
            addCriterion("\"western_medicine_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeEqualTo(Double value) {
            addCriterion("\"western_medicine_fee\" =", value, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeNotEqualTo(Double value) {
            addCriterion("\"western_medicine_fee\" <>", value, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeGreaterThan(Double value) {
            addCriterion("\"western_medicine_fee\" >", value, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"western_medicine_fee\" >=", value, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeLessThan(Double value) {
            addCriterion("\"western_medicine_fee\" <", value, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"western_medicine_fee\" <=", value, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeIn(List<Double> values) {
            addCriterion("\"western_medicine_fee\" in", values, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeNotIn(List<Double> values) {
            addCriterion("\"western_medicine_fee\" not in", values, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeBetween(Double value1, Double value2) {
            addCriterion("\"western_medicine_fee\" between", value1, value2, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andWesternMedicineFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"western_medicine_fee\" not between", value1, value2, "westernMedicineFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeIsNull() {
            addCriterion("\"antibacterial_drug_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeIsNotNull() {
            addCriterion("\"antibacterial_drug_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeEqualTo(Double value) {
            addCriterion("\"antibacterial_drug_fee\" =", value, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeNotEqualTo(Double value) {
            addCriterion("\"antibacterial_drug_fee\" <>", value, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeGreaterThan(Double value) {
            addCriterion("\"antibacterial_drug_fee\" >", value, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"antibacterial_drug_fee\" >=", value, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeLessThan(Double value) {
            addCriterion("\"antibacterial_drug_fee\" <", value, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"antibacterial_drug_fee\" <=", value, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeIn(List<Double> values) {
            addCriterion("\"antibacterial_drug_fee\" in", values, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeNotIn(List<Double> values) {
            addCriterion("\"antibacterial_drug_fee\" not in", values, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeBetween(Double value1, Double value2) {
            addCriterion("\"antibacterial_drug_fee\" between", value1, value2, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andAntibacterialDrugFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"antibacterial_drug_fee\" not between", value1, value2, "antibacterialDrugFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeIsNull() {
            addCriterion("\"chinese_patent_medcine_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeIsNotNull() {
            addCriterion("\"chinese_patent_medcine_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeEqualTo(Double value) {
            addCriterion("\"chinese_patent_medcine_fee\" =", value, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeNotEqualTo(Double value) {
            addCriterion("\"chinese_patent_medcine_fee\" <>", value, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeGreaterThan(Double value) {
            addCriterion("\"chinese_patent_medcine_fee\" >", value, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"chinese_patent_medcine_fee\" >=", value, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeLessThan(Double value) {
            addCriterion("\"chinese_patent_medcine_fee\" <", value, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"chinese_patent_medcine_fee\" <=", value, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeIn(List<Double> values) {
            addCriterion("\"chinese_patent_medcine_fee\" in", values, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeNotIn(List<Double> values) {
            addCriterion("\"chinese_patent_medcine_fee\" not in", values, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeBetween(Double value1, Double value2) {
            addCriterion("\"chinese_patent_medcine_fee\" between", value1, value2, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChinesePatentMedcineFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"chinese_patent_medcine_fee\" not between", value1, value2, "chinesePatentMedcineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeIsNull() {
            addCriterion("\"chinese_herbal_medicine_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeIsNotNull() {
            addCriterion("\"chinese_herbal_medicine_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeEqualTo(Double value) {
            addCriterion("\"chinese_herbal_medicine_fee\" =", value, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeNotEqualTo(Double value) {
            addCriterion("\"chinese_herbal_medicine_fee\" <>", value, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeGreaterThan(Double value) {
            addCriterion("\"chinese_herbal_medicine_fee\" >", value, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"chinese_herbal_medicine_fee\" >=", value, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeLessThan(Double value) {
            addCriterion("\"chinese_herbal_medicine_fee\" <", value, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"chinese_herbal_medicine_fee\" <=", value, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeIn(List<Double> values) {
            addCriterion("\"chinese_herbal_medicine_fee\" in", values, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeNotIn(List<Double> values) {
            addCriterion("\"chinese_herbal_medicine_fee\" not in", values, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeBetween(Double value1, Double value2) {
            addCriterion("\"chinese_herbal_medicine_fee\" between", value1, value2, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andChineseHerbalMedicineFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"chinese_herbal_medicine_fee\" not between", value1, value2, "chineseHerbalMedicineFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeIsNull() {
            addCriterion("\"blood_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andBloodFeeIsNotNull() {
            addCriterion("\"blood_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andBloodFeeEqualTo(Double value) {
            addCriterion("\"blood_fee\" =", value, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeNotEqualTo(Double value) {
            addCriterion("\"blood_fee\" <>", value, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeGreaterThan(Double value) {
            addCriterion("\"blood_fee\" >", value, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"blood_fee\" >=", value, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeLessThan(Double value) {
            addCriterion("\"blood_fee\" <", value, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"blood_fee\" <=", value, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeIn(List<Double> values) {
            addCriterion("\"blood_fee\" in", values, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeNotIn(List<Double> values) {
            addCriterion("\"blood_fee\" not in", values, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeBetween(Double value1, Double value2) {
            addCriterion("\"blood_fee\" between", value1, value2, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andBloodFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"blood_fee\" not between", value1, value2, "bloodFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeIsNull() {
            addCriterion("\"albumin_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeIsNotNull() {
            addCriterion("\"albumin_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeEqualTo(Double value) {
            addCriterion("\"albumin_fee\" =", value, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeNotEqualTo(Double value) {
            addCriterion("\"albumin_fee\" <>", value, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeGreaterThan(Double value) {
            addCriterion("\"albumin_fee\" >", value, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"albumin_fee\" >=", value, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeLessThan(Double value) {
            addCriterion("\"albumin_fee\" <", value, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"albumin_fee\" <=", value, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeIn(List<Double> values) {
            addCriterion("\"albumin_fee\" in", values, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeNotIn(List<Double> values) {
            addCriterion("\"albumin_fee\" not in", values, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeBetween(Double value1, Double value2) {
            addCriterion("\"albumin_fee\" between", value1, value2, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andAlbuminFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"albumin_fee\" not between", value1, value2, "albuminFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeIsNull() {
            addCriterion("\"globulin_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeIsNotNull() {
            addCriterion("\"globulin_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeEqualTo(Double value) {
            addCriterion("\"globulin_fee\" =", value, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeNotEqualTo(Double value) {
            addCriterion("\"globulin_fee\" <>", value, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeGreaterThan(Double value) {
            addCriterion("\"globulin_fee\" >", value, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"globulin_fee\" >=", value, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeLessThan(Double value) {
            addCriterion("\"globulin_fee\" <", value, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"globulin_fee\" <=", value, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeIn(List<Double> values) {
            addCriterion("\"globulin_fee\" in", values, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeNotIn(List<Double> values) {
            addCriterion("\"globulin_fee\" not in", values, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeBetween(Double value1, Double value2) {
            addCriterion("\"globulin_fee\" between", value1, value2, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andGlobulinFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"globulin_fee\" not between", value1, value2, "globulinFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeIsNull() {
            addCriterion("\"blood_coagulation_factor_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeIsNotNull() {
            addCriterion("\"blood_coagulation_factor_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeEqualTo(Double value) {
            addCriterion("\"blood_coagulation_factor_fee\" =", value, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeNotEqualTo(Double value) {
            addCriterion("\"blood_coagulation_factor_fee\" <>", value, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeGreaterThan(Double value) {
            addCriterion("\"blood_coagulation_factor_fee\" >", value, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"blood_coagulation_factor_fee\" >=", value, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeLessThan(Double value) {
            addCriterion("\"blood_coagulation_factor_fee\" <", value, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"blood_coagulation_factor_fee\" <=", value, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeIn(List<Double> values) {
            addCriterion("\"blood_coagulation_factor_fee\" in", values, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeNotIn(List<Double> values) {
            addCriterion("\"blood_coagulation_factor_fee\" not in", values, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeBetween(Double value1, Double value2) {
            addCriterion("\"blood_coagulation_factor_fee\" between", value1, value2, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andBloodCoagulationFactorFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"blood_coagulation_factor_fee\" not between", value1, value2, "bloodCoagulationFactorFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeIsNull() {
            addCriterion("\"cytokine_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeIsNotNull() {
            addCriterion("\"cytokine_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeEqualTo(Double value) {
            addCriterion("\"cytokine_fee\" =", value, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeNotEqualTo(Double value) {
            addCriterion("\"cytokine_fee\" <>", value, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeGreaterThan(Double value) {
            addCriterion("\"cytokine_fee\" >", value, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"cytokine_fee\" >=", value, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeLessThan(Double value) {
            addCriterion("\"cytokine_fee\" <", value, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"cytokine_fee\" <=", value, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeIn(List<Double> values) {
            addCriterion("\"cytokine_fee\" in", values, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeNotIn(List<Double> values) {
            addCriterion("\"cytokine_fee\" not in", values, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeBetween(Double value1, Double value2) {
            addCriterion("\"cytokine_fee\" between", value1, value2, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andCytokineFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"cytokine_fee\" not between", value1, value2, "cytokineFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeIsNull() {
            addCriterion("\"exam_disposable_mat_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeIsNotNull() {
            addCriterion("\"exam_disposable_mat_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeEqualTo(Double value) {
            addCriterion("\"exam_disposable_mat_fee\" =", value, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeNotEqualTo(Double value) {
            addCriterion("\"exam_disposable_mat_fee\" <>", value, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeGreaterThan(Double value) {
            addCriterion("\"exam_disposable_mat_fee\" >", value, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"exam_disposable_mat_fee\" >=", value, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeLessThan(Double value) {
            addCriterion("\"exam_disposable_mat_fee\" <", value, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"exam_disposable_mat_fee\" <=", value, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeIn(List<Double> values) {
            addCriterion("\"exam_disposable_mat_fee\" in", values, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeNotIn(List<Double> values) {
            addCriterion("\"exam_disposable_mat_fee\" not in", values, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeBetween(Double value1, Double value2) {
            addCriterion("\"exam_disposable_mat_fee\" between", value1, value2, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andExamDisposableMatFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"exam_disposable_mat_fee\" not between", value1, value2, "examDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeIsNull() {
            addCriterion("\"treat_disposable_mat_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeIsNotNull() {
            addCriterion("\"treat_disposable_mat_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeEqualTo(Double value) {
            addCriterion("\"treat_disposable_mat_fee\" =", value, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeNotEqualTo(Double value) {
            addCriterion("\"treat_disposable_mat_fee\" <>", value, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeGreaterThan(Double value) {
            addCriterion("\"treat_disposable_mat_fee\" >", value, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"treat_disposable_mat_fee\" >=", value, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeLessThan(Double value) {
            addCriterion("\"treat_disposable_mat_fee\" <", value, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"treat_disposable_mat_fee\" <=", value, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeIn(List<Double> values) {
            addCriterion("\"treat_disposable_mat_fee\" in", values, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeNotIn(List<Double> values) {
            addCriterion("\"treat_disposable_mat_fee\" not in", values, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeBetween(Double value1, Double value2) {
            addCriterion("\"treat_disposable_mat_fee\" between", value1, value2, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andTreatDisposableMatFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"treat_disposable_mat_fee\" not between", value1, value2, "treatDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeIsNull() {
            addCriterion("\"surgery_disposable_mat_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeIsNotNull() {
            addCriterion("\"surgery_disposable_mat_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeEqualTo(Double value) {
            addCriterion("\"surgery_disposable_mat_fee\" =", value, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeNotEqualTo(Double value) {
            addCriterion("\"surgery_disposable_mat_fee\" <>", value, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeGreaterThan(Double value) {
            addCriterion("\"surgery_disposable_mat_fee\" >", value, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"surgery_disposable_mat_fee\" >=", value, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeLessThan(Double value) {
            addCriterion("\"surgery_disposable_mat_fee\" <", value, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"surgery_disposable_mat_fee\" <=", value, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeIn(List<Double> values) {
            addCriterion("\"surgery_disposable_mat_fee\" in", values, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeNotIn(List<Double> values) {
            addCriterion("\"surgery_disposable_mat_fee\" not in", values, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeBetween(Double value1, Double value2) {
            addCriterion("\"surgery_disposable_mat_fee\" between", value1, value2, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andSurgeryDisposableMatFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"surgery_disposable_mat_fee\" not between", value1, value2, "surgeryDisposableMatFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeIsNull() {
            addCriterion("\"other_fee\" is null");
            return (Criteria) this;
        }

        public Criteria andOtherFeeIsNotNull() {
            addCriterion("\"other_fee\" is not null");
            return (Criteria) this;
        }

        public Criteria andOtherFeeEqualTo(Double value) {
            addCriterion("\"other_fee\" =", value, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeNotEqualTo(Double value) {
            addCriterion("\"other_fee\" <>", value, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeGreaterThan(Double value) {
            addCriterion("\"other_fee\" >", value, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeGreaterThanOrEqualTo(Double value) {
            addCriterion("\"other_fee\" >=", value, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeLessThan(Double value) {
            addCriterion("\"other_fee\" <", value, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeLessThanOrEqualTo(Double value) {
            addCriterion("\"other_fee\" <=", value, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeIn(List<Double> values) {
            addCriterion("\"other_fee\" in", values, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeNotIn(List<Double> values) {
            addCriterion("\"other_fee\" not in", values, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeBetween(Double value1, Double value2) {
            addCriterion("\"other_fee\" between", value1, value2, "otherFee");
            return (Criteria) this;
        }

        public Criteria andOtherFeeNotBetween(Double value1, Double value2) {
            addCriterion("\"other_fee\" not between", value1, value2, "otherFee");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}