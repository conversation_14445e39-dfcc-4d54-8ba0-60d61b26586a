<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.MrHomepageOperMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.MrHomepageOper">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="operation_no" jdbcType="VARCHAR" property="operationNo" />
    <result column="operation_code" jdbcType="VARCHAR" property="operationCode" />
    <result column="operation_datetime" jdbcType="TIMESTAMP" property="operationDatetime" />
    <result column="operation_level" jdbcType="VARCHAR" property="operationLevel" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="operation_part" jdbcType="VARCHAR" property="operationPart" />
    <result column="operation_duration" jdbcType="VARCHAR" property="operationDuration" />
    <result column="surgeon_doctor" jdbcType="VARCHAR" property="surgeonDoctor" />
    <result column="first_assistant" jdbcType="VARCHAR" property="firstAssistant" />
    <result column="second_assistant" jdbcType="VARCHAR" property="secondAssistant" />
    <result column="anesthesia_method" jdbcType="VARCHAR" property="anesthesiaMethod" />
    <result column="anesthesia_level" jdbcType="VARCHAR" property="anesthesiaLevel" />
    <result column="surgical_incision_level" jdbcType="VARCHAR" property="surgicalIncisionLevel" />
    <result column="anaesthesia_doctor" jdbcType="VARCHAR" property="anaesthesiaDoctor" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "visit_sn", "tpatno", "operation_no", "operation_code",
    "operation_datetime", "operation_level", "operation_name", "operation_part", "operation_duration",
    "surgeon_doctor", "first_assistant", "second_assistant", "anesthesia_method", "anesthesia_level",
    "surgical_incision_level", "anaesthesia_doctor", "source_path", "pk_id", "data_state",
    "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.MrHomepageOperExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."mr_homepage_oper"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "public"."mr_homepage_oper"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."mr_homepage_oper"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.MrHomepageOperExample">
    delete from "public"."mr_homepage_oper"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.MrHomepageOper">
    insert into "public"."mr_homepage_oper" ("hospital_code", "patient_sn", "visit_sn",
      "tpatno", "operation_no", "operation_code",
      "operation_datetime", "operation_level", "operation_name",
      "operation_part", "operation_duration", "surgeon_doctor",
      "first_assistant", "second_assistant", "anesthesia_method",
      "anesthesia_level", "surgical_incision_level",
      "anaesthesia_doctor", "source_path", "pk_id",
      "data_state", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR},
      #{tpatno,jdbcType=VARCHAR}, #{operationNo,jdbcType=VARCHAR}, #{operationCode,jdbcType=VARCHAR},
      #{operationDatetime,jdbcType=TIMESTAMP}, #{operationLevel,jdbcType=VARCHAR}, #{operationName,jdbcType=VARCHAR},
      #{operationPart,jdbcType=VARCHAR}, #{operationDuration,jdbcType=VARCHAR}, #{surgeonDoctor,jdbcType=VARCHAR},
      #{firstAssistant,jdbcType=VARCHAR}, #{secondAssistant,jdbcType=VARCHAR}, #{anesthesiaMethod,jdbcType=VARCHAR},
      #{anesthesiaLevel,jdbcType=VARCHAR}, #{surgicalIncisionLevel,jdbcType=VARCHAR},
      #{anaesthesiaDoctor,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.MrHomepageOper">
    insert into "public"."mr_homepage_oper"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="operationNo != null">
        "operation_no",
      </if>
      <if test="operationCode != null">
        "operation_code",
      </if>
      <if test="operationDatetime != null">
        "operation_datetime",
      </if>
      <if test="operationLevel != null">
        "operation_level",
      </if>
      <if test="operationName != null">
        "operation_name",
      </if>
      <if test="operationPart != null">
        "operation_part",
      </if>
      <if test="operationDuration != null">
        "operation_duration",
      </if>
      <if test="surgeonDoctor != null">
        "surgeon_doctor",
      </if>
      <if test="firstAssistant != null">
        "first_assistant",
      </if>
      <if test="secondAssistant != null">
        "second_assistant",
      </if>
      <if test="anesthesiaMethod != null">
        "anesthesia_method",
      </if>
      <if test="anesthesiaLevel != null">
        "anesthesia_level",
      </if>
      <if test="surgicalIncisionLevel != null">
        "surgical_incision_level",
      </if>
      <if test="anaesthesiaDoctor != null">
        "anaesthesia_doctor",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="operationNo != null">
        #{operationNo,jdbcType=VARCHAR},
      </if>
      <if test="operationCode != null">
        #{operationCode,jdbcType=VARCHAR},
      </if>
      <if test="operationDatetime != null">
        #{operationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationLevel != null">
        #{operationLevel,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="operationPart != null">
        #{operationPart,jdbcType=VARCHAR},
      </if>
      <if test="operationDuration != null">
        #{operationDuration,jdbcType=VARCHAR},
      </if>
      <if test="surgeonDoctor != null">
        #{surgeonDoctor,jdbcType=VARCHAR},
      </if>
      <if test="firstAssistant != null">
        #{firstAssistant,jdbcType=VARCHAR},
      </if>
      <if test="secondAssistant != null">
        #{secondAssistant,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaMethod != null">
        #{anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="anesthesiaLevel != null">
        #{anesthesiaLevel,jdbcType=VARCHAR},
      </if>
      <if test="surgicalIncisionLevel != null">
        #{surgicalIncisionLevel,jdbcType=VARCHAR},
      </if>
      <if test="anaesthesiaDoctor != null">
        #{anaesthesiaDoctor,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.MrHomepageOperExample" resultType="java.lang.Long">
    select count(*) from "public"."mr_homepage_oper"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."mr_homepage_oper"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.operationNo != null">
        "operation_no" = #{record.operationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operationCode != null">
        "operation_code" = #{record.operationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.operationDatetime != null">
        "operation_datetime" = #{record.operationDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operationLevel != null">
        "operation_level" = #{record.operationLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.operationName != null">
        "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationPart != null">
        "operation_part" = #{record.operationPart,jdbcType=VARCHAR},
      </if>
      <if test="record.operationDuration != null">
        "operation_duration" = #{record.operationDuration,jdbcType=VARCHAR},
      </if>
      <if test="record.surgeonDoctor != null">
        "surgeon_doctor" = #{record.surgeonDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.firstAssistant != null">
        "first_assistant" = #{record.firstAssistant,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAssistant != null">
        "second_assistant" = #{record.secondAssistant,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaMethod != null">
        "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.anesthesiaLevel != null">
        "anesthesia_level" = #{record.anesthesiaLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.surgicalIncisionLevel != null">
        "surgical_incision_level" = #{record.surgicalIncisionLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.anaesthesiaDoctor != null">
        "anaesthesia_doctor" = #{record.anaesthesiaDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."mr_homepage_oper"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "operation_no" = #{record.operationNo,jdbcType=VARCHAR},
      "operation_code" = #{record.operationCode,jdbcType=VARCHAR},
      "operation_datetime" = #{record.operationDatetime,jdbcType=TIMESTAMP},
      "operation_level" = #{record.operationLevel,jdbcType=VARCHAR},
      "operation_name" = #{record.operationName,jdbcType=VARCHAR},
      "operation_part" = #{record.operationPart,jdbcType=VARCHAR},
      "operation_duration" = #{record.operationDuration,jdbcType=VARCHAR},
      "surgeon_doctor" = #{record.surgeonDoctor,jdbcType=VARCHAR},
      "first_assistant" = #{record.firstAssistant,jdbcType=VARCHAR},
      "second_assistant" = #{record.secondAssistant,jdbcType=VARCHAR},
      "anesthesia_method" = #{record.anesthesiaMethod,jdbcType=VARCHAR},
      "anesthesia_level" = #{record.anesthesiaLevel,jdbcType=VARCHAR},
      "surgical_incision_level" = #{record.surgicalIncisionLevel,jdbcType=VARCHAR},
      "anaesthesia_doctor" = #{record.anaesthesiaDoctor,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.MrHomepageOper">
    update "public"."mr_homepage_oper"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="admissionDateTime != null">
        "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dischargeDateTime != null">
        "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="diagnosisType != null">
        "diagnosis_type" = #{diagnosisType,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisClass != null">
        "diagnosis_class" = #{diagnosisClass,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisOrderNo != null">
        "diagnosis_order_no" = #{diagnosisOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisName != null">
        "diagnosis_name" = #{diagnosisName,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode != null">
        "diagnosis_code" = #{diagnosisCode,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisCode2 != null">
        "diagnosis_code2" = #{diagnosisCode2,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null">
        "is_main" = #{isMain,jdbcType=VARCHAR},
      </if>
      <if test="pathologicNo != null">
        "pathologic_no" = #{pathologicNo,jdbcType=VARCHAR},
      </if>
      <if test="admissionStatus != null">
        "admission_status" = #{admissionStatus,jdbcType=VARCHAR},
      </if>
      <if test="disDiagAdmitStat != null">
        "dis_diag_admit_stat" = #{disDiagAdmitStat,jdbcType=VARCHAR},
      </if>
      <if test="disDiagDisStat != null">
        "dis_diag_dis_stat" = #{disDiagDisStat,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="diagnosisDate != null">
        "diagnosis_date" = #{diagnosisDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.MrHomepageOper">
    update "public"."mr_homepage_oper"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "admission_date_time" = #{admissionDateTime,jdbcType=TIMESTAMP},
      "discharge_date_time" = #{dischargeDateTime,jdbcType=TIMESTAMP},
      "diagnosis_type" = #{diagnosisType,jdbcType=VARCHAR},
      "diagnosis_class" = #{diagnosisClass,jdbcType=VARCHAR},
      "diagnosis_order_no" = #{diagnosisOrderNo,jdbcType=VARCHAR},
      "diagnosis_name" = #{diagnosisName,jdbcType=VARCHAR},
      "diagnosis_code" = #{diagnosisCode,jdbcType=VARCHAR},
      "diagnosis_code2" = #{diagnosisCode2,jdbcType=VARCHAR},
      "is_main" = #{isMain,jdbcType=VARCHAR},
      "pathologic_no" = #{pathologicNo,jdbcType=VARCHAR},
      "admission_status" = #{admissionStatus,jdbcType=VARCHAR},
      "dis_diag_admit_stat" = #{disDiagAdmitStat,jdbcType=VARCHAR},
      "dis_diag_dis_stat" = #{disDiagDisStat,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "diagnosis_date" = #{diagnosisDate,jdbcType=TIMESTAMP}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="countPatientVisitOper" resultType="com.haoys.rdr.domain.bigscreen.PatientMedicalDataView">
    select
      mr_homepage_oper.operation_name "name",count(operation_name) count
    from mr_homepage_oper
    <if test="databaseId != null and databaseId!=''">
      join rdr_patient_data_base_record on rdr_patient_data_base_record.patient_sn=mr_homepage_oper.patient_sn and rdr_patient_data_base_record.data_base_id = #{databaseId}
    </if>
    INNER JOIN visit_information on visit_information.visit_sn = mr_homepage_oper.visit_sn
    where 1=1 and operation_name is not null
    <if test="year != null and year != ''">
      and visit_information.visit_or_admission_datetime between '${year}-01-01 00:00:00' and '${year}-12-31 23:59:59'
    </if>
    <if test="department != null and department != ''">
      and visit_information.visit_or_admission_dept = #{department,jdbcType=VARCHAR}
    </if>
    <if test="visitType != null and visitType != ''">
      and visit_information.visit_type = #{visitType,jdbcType=VARCHAR}
    </if>
    group by operation_name order by count desc limit 50
  </select>
</mapper>
