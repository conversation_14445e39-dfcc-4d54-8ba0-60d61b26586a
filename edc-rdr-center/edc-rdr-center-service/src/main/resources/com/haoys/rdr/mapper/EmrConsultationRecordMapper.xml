<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.EmrConsultationRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.EmrConsultationRecord">
    <id column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="tpatno" jdbcType="VARCHAR" property="tpatno" />
    <result column="consultation_no" jdbcType="VARCHAR" property="consultationNo" />
    <result column="consultation_type" jdbcType="VARCHAR" property="consultationType" />
    <result column="consultation_explain" jdbcType="VARCHAR" property="consultationExplain" />
    <result column="apply_date_time" jdbcType="TIMESTAMP" property="applyDateTime" />
    <result column="consultation_dept" jdbcType="VARCHAR" property="consultationDept" />
    <result column="consultation_doctor" jdbcType="VARCHAR" property="consultationDoctor" />
    <result column="affirm_date_time" jdbcType="TIMESTAMP" property="affirmDateTime" />
    <result column="commit_date_time" jdbcType="TIMESTAMP" property="commitDateTime" />
    <result column="consultation_idea" jdbcType="VARCHAR" property="consultationIdea" />
    <result column="dept_assign" jdbcType="VARCHAR" property="deptAssign" />
    <result column="consultation_commit" jdbcType="VARCHAR" property="consultationCommit" />
    <result column="end_date_time" jdbcType="TIMESTAMP" property="endDateTime" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="full_text" jdbcType="VARCHAR" property="fullText" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "pk_id", "hospital_code", "patient_sn", "visit_sn", "tpatno", "consultation_no", 
    "consultation_type", "consultation_explain", "apply_date_time", "consultation_dept", 
    "consultation_doctor", "affirm_date_time", "commit_date_time", "consultation_idea", 
    "dept_assign", "consultation_commit", "end_date_time", "source_path", "data_state", 
    "full_text"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.EmrConsultationRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."emr_consultation_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."emr_consultation_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."emr_consultation_record"
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.EmrConsultationRecordExample">
    delete from "public"."emr_consultation_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.EmrConsultationRecord">
    insert into "public"."emr_consultation_record" ("pk_id", "hospital_code", "patient_sn", 
      "visit_sn", "tpatno", "consultation_no", 
      "consultation_type", "consultation_explain", 
      "apply_date_time", "consultation_dept", "consultation_doctor", 
      "affirm_date_time", "commit_date_time", "consultation_idea", 
      "dept_assign", "consultation_commit", "end_date_time", 
      "source_path", "data_state", "full_text"
      )
    values (#{pkId,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, 
      #{visitSn,jdbcType=VARCHAR}, #{tpatno,jdbcType=VARCHAR}, #{consultationNo,jdbcType=VARCHAR}, 
      #{consultationType,jdbcType=VARCHAR}, #{consultationExplain,jdbcType=VARCHAR}, 
      #{applyDateTime,jdbcType=TIMESTAMP}, #{consultationDept,jdbcType=VARCHAR}, #{consultationDoctor,jdbcType=VARCHAR}, 
      #{affirmDateTime,jdbcType=TIMESTAMP}, #{commitDateTime,jdbcType=TIMESTAMP}, #{consultationIdea,jdbcType=VARCHAR}, 
      #{deptAssign,jdbcType=VARCHAR}, #{consultationCommit,jdbcType=VARCHAR}, #{endDateTime,jdbcType=TIMESTAMP}, 
      #{sourcePath,jdbcType=VARCHAR}, #{dataState,jdbcType=VARCHAR}, #{fullText,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.EmrConsultationRecord">
    insert into "public"."emr_consultation_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="tpatno != null">
        "tpatno",
      </if>
      <if test="consultationNo != null">
        "consultation_no",
      </if>
      <if test="consultationType != null">
        "consultation_type",
      </if>
      <if test="consultationExplain != null">
        "consultation_explain",
      </if>
      <if test="applyDateTime != null">
        "apply_date_time",
      </if>
      <if test="consultationDept != null">
        "consultation_dept",
      </if>
      <if test="consultationDoctor != null">
        "consultation_doctor",
      </if>
      <if test="affirmDateTime != null">
        "affirm_date_time",
      </if>
      <if test="commitDateTime != null">
        "commit_date_time",
      </if>
      <if test="consultationIdea != null">
        "consultation_idea",
      </if>
      <if test="deptAssign != null">
        "dept_assign",
      </if>
      <if test="consultationCommit != null">
        "consultation_commit",
      </if>
      <if test="endDateTime != null">
        "end_date_time",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="fullText != null">
        "full_text",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="consultationNo != null">
        #{consultationNo,jdbcType=VARCHAR},
      </if>
      <if test="consultationType != null">
        #{consultationType,jdbcType=VARCHAR},
      </if>
      <if test="consultationExplain != null">
        #{consultationExplain,jdbcType=VARCHAR},
      </if>
      <if test="applyDateTime != null">
        #{applyDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consultationDept != null">
        #{consultationDept,jdbcType=VARCHAR},
      </if>
      <if test="consultationDoctor != null">
        #{consultationDoctor,jdbcType=VARCHAR},
      </if>
      <if test="affirmDateTime != null">
        #{affirmDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitDateTime != null">
        #{commitDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consultationIdea != null">
        #{consultationIdea,jdbcType=VARCHAR},
      </if>
      <if test="deptAssign != null">
        #{deptAssign,jdbcType=VARCHAR},
      </if>
      <if test="consultationCommit != null">
        #{consultationCommit,jdbcType=VARCHAR},
      </if>
      <if test="endDateTime != null">
        #{endDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        #{fullText,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.EmrConsultationRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."emr_consultation_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."emr_consultation_record"
    <set>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.tpatno != null">
        "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      </if>
      <if test="record.consultationNo != null">
        "consultation_no" = #{record.consultationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.consultationType != null">
        "consultation_type" = #{record.consultationType,jdbcType=VARCHAR},
      </if>
      <if test="record.consultationExplain != null">
        "consultation_explain" = #{record.consultationExplain,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDateTime != null">
        "apply_date_time" = #{record.applyDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.consultationDept != null">
        "consultation_dept" = #{record.consultationDept,jdbcType=VARCHAR},
      </if>
      <if test="record.consultationDoctor != null">
        "consultation_doctor" = #{record.consultationDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.affirmDateTime != null">
        "affirm_date_time" = #{record.affirmDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commitDateTime != null">
        "commit_date_time" = #{record.commitDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.consultationIdea != null">
        "consultation_idea" = #{record.consultationIdea,jdbcType=VARCHAR},
      </if>
      <if test="record.deptAssign != null">
        "dept_assign" = #{record.deptAssign,jdbcType=VARCHAR},
      </if>
      <if test="record.consultationCommit != null">
        "consultation_commit" = #{record.consultationCommit,jdbcType=VARCHAR},
      </if>
      <if test="record.endDateTime != null">
        "end_date_time" = #{record.endDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.fullText != null">
        "full_text" = #{record.fullText,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."emr_consultation_record"
    set "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "tpatno" = #{record.tpatno,jdbcType=VARCHAR},
      "consultation_no" = #{record.consultationNo,jdbcType=VARCHAR},
      "consultation_type" = #{record.consultationType,jdbcType=VARCHAR},
      "consultation_explain" = #{record.consultationExplain,jdbcType=VARCHAR},
      "apply_date_time" = #{record.applyDateTime,jdbcType=TIMESTAMP},
      "consultation_dept" = #{record.consultationDept,jdbcType=VARCHAR},
      "consultation_doctor" = #{record.consultationDoctor,jdbcType=VARCHAR},
      "affirm_date_time" = #{record.affirmDateTime,jdbcType=TIMESTAMP},
      "commit_date_time" = #{record.commitDateTime,jdbcType=TIMESTAMP},
      "consultation_idea" = #{record.consultationIdea,jdbcType=VARCHAR},
      "dept_assign" = #{record.deptAssign,jdbcType=VARCHAR},
      "consultation_commit" = #{record.consultationCommit,jdbcType=VARCHAR},
      "end_date_time" = #{record.endDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "full_text" = #{record.fullText,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.EmrConsultationRecord">
    update "public"."emr_consultation_record"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="tpatno != null">
        "tpatno" = #{tpatno,jdbcType=VARCHAR},
      </if>
      <if test="consultationNo != null">
        "consultation_no" = #{consultationNo,jdbcType=VARCHAR},
      </if>
      <if test="consultationType != null">
        "consultation_type" = #{consultationType,jdbcType=VARCHAR},
      </if>
      <if test="consultationExplain != null">
        "consultation_explain" = #{consultationExplain,jdbcType=VARCHAR},
      </if>
      <if test="applyDateTime != null">
        "apply_date_time" = #{applyDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consultationDept != null">
        "consultation_dept" = #{consultationDept,jdbcType=VARCHAR},
      </if>
      <if test="consultationDoctor != null">
        "consultation_doctor" = #{consultationDoctor,jdbcType=VARCHAR},
      </if>
      <if test="affirmDateTime != null">
        "affirm_date_time" = #{affirmDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitDateTime != null">
        "commit_date_time" = #{commitDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="consultationIdea != null">
        "consultation_idea" = #{consultationIdea,jdbcType=VARCHAR},
      </if>
      <if test="deptAssign != null">
        "dept_assign" = #{deptAssign,jdbcType=VARCHAR},
      </if>
      <if test="consultationCommit != null">
        "consultation_commit" = #{consultationCommit,jdbcType=VARCHAR},
      </if>
      <if test="endDateTime != null">
        "end_date_time" = #{endDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="fullText != null">
        "full_text" = #{fullText,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.EmrConsultationRecord">
    update "public"."emr_consultation_record"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "tpatno" = #{tpatno,jdbcType=VARCHAR},
      "consultation_no" = #{consultationNo,jdbcType=VARCHAR},
      "consultation_type" = #{consultationType,jdbcType=VARCHAR},
      "consultation_explain" = #{consultationExplain,jdbcType=VARCHAR},
      "apply_date_time" = #{applyDateTime,jdbcType=TIMESTAMP},
      "consultation_dept" = #{consultationDept,jdbcType=VARCHAR},
      "consultation_doctor" = #{consultationDoctor,jdbcType=VARCHAR},
      "affirm_date_time" = #{affirmDateTime,jdbcType=TIMESTAMP},
      "commit_date_time" = #{commitDateTime,jdbcType=TIMESTAMP},
      "consultation_idea" = #{consultationIdea,jdbcType=VARCHAR},
      "dept_assign" = #{deptAssign,jdbcType=VARCHAR},
      "consultation_commit" = #{consultationCommit,jdbcType=VARCHAR},
      "end_date_time" = #{endDateTime,jdbcType=TIMESTAMP},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR},
      "full_text" = #{fullText,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
</mapper>