<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.RdrPatientAreaInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.RdrPatientAreaInfo">
    <id column="adcode" jdbcType="VARCHAR" property="adcode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="center" jdbcType="VARCHAR" property="center" />
    <result column="centroid" jdbcType="VARCHAR" property="centroid" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="childrenNum" jdbcType="INTEGER" property="childrennum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "adcode", "name", "center", "centroid", "parent_code", "level", "childrenNum"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.RdrPatientAreaInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_area_info"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."rdr_patient_area_info"
    where "adcode" = #{adcode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."rdr_patient_area_info"
    where "adcode" = #{adcode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.RdrPatientAreaInfoExample">
    delete from "public"."rdr_patient_area_info"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.RdrPatientAreaInfo">
    insert into "public"."rdr_patient_area_info" ("adcode", "name", "center", 
      "centroid", "parent_code", "level", 
      "childrenNum")
    values (#{adcode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{center,jdbcType=VARCHAR}, 
      #{centroid,jdbcType=VARCHAR}, #{parentCode,jdbcType=VARCHAR}, #{level,jdbcType=VARCHAR}, 
      #{childrennum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.RdrPatientAreaInfo">
    insert into "public"."rdr_patient_area_info"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adcode != null">
        "adcode",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="center != null">
        "center",
      </if>
      <if test="centroid != null">
        "centroid",
      </if>
      <if test="parentCode != null">
        "parent_code",
      </if>
      <if test="level != null">
        "level",
      </if>
      <if test="childrennum != null">
        "childrenNum",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adcode != null">
        #{adcode,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="center != null">
        #{center,jdbcType=VARCHAR},
      </if>
      <if test="centroid != null">
        #{centroid,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="childrennum != null">
        #{childrennum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.RdrPatientAreaInfoExample" resultType="java.lang.Long">
    select count(*) from "public"."rdr_patient_area_info"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."rdr_patient_area_info"
    <set>
      <if test="record.adcode != null">
        "adcode" = #{record.adcode,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.center != null">
        "center" = #{record.center,jdbcType=VARCHAR},
      </if>
      <if test="record.centroid != null">
        "centroid" = #{record.centroid,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCode != null">
        "parent_code" = #{record.parentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        "level" = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.childrennum != null">
        "childrenNum" = #{record.childrennum,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."rdr_patient_area_info"
    set "adcode" = #{record.adcode,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "center" = #{record.center,jdbcType=VARCHAR},
      "centroid" = #{record.centroid,jdbcType=VARCHAR},
      "parent_code" = #{record.parentCode,jdbcType=VARCHAR},
      "level" = #{record.level,jdbcType=VARCHAR},
      "childrenNum" = #{record.childrennum,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.RdrPatientAreaInfo">
    update "public"."rdr_patient_area_info"
    <set>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="center != null">
        "center" = #{center,jdbcType=VARCHAR},
      </if>
      <if test="centroid != null">
        "centroid" = #{centroid,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        "parent_code" = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        "level" = #{level,jdbcType=VARCHAR},
      </if>
      <if test="childrennum != null">
        "childrenNum" = #{childrennum,jdbcType=INTEGER},
      </if>
    </set>
    where "adcode" = #{adcode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.RdrPatientAreaInfo">
    update "public"."rdr_patient_area_info"
    set "name" = #{name,jdbcType=VARCHAR},
      "center" = #{center,jdbcType=VARCHAR},
      "centroid" = #{centroid,jdbcType=VARCHAR},
      "parent_code" = #{parentCode,jdbcType=VARCHAR},
      "level" = #{level,jdbcType=VARCHAR},
      "childrenNum" = #{childrennum,jdbcType=INTEGER}
    where "adcode" = #{adcode,jdbcType=VARCHAR}
  </update>
</mapper>