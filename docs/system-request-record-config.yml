# =====================================================
# 系统访问日志记录配置文件
# 用于配置医学审计日志功能
# =====================================================

# EDC系统日志配置
edc:
  system:
    log:
      # 基础配置
      enabled: true                    # 是否启用系统日志记录
      async-enabled: true              # 是否启用异步日志记录
      
      # 数据记录配置
      save-request-data: true          # 是否记录请求参数
      save-response-data: true         # 是否记录响应数据
      save-device-info: true           # 是否记录设备信息
      save-location-info: true         # 是否记录地理位置信息
      
      # 数据长度限制
      max-param-length: 2000           # 最大参数长度限制
      max-response-length: 5000        # 最大响应结果长度限制
      
      # 性能配置
      slow-request-threshold: 5000     # 慢请求阈值（毫秒）
      
      # 数据管理配置
      data-retention-days: 90          # 数据保留天数
      auto-cleanup-enabled: true       # 是否启用数据自动清理
      
      # 异步处理配置
      async-queue-size: 1000           # 异步队列大小
      async-thread-pool-size: 5        # 异步线程池大小
      
      # 过滤配置
      filter-static-resources: true    # 是否过滤静态资源
      filter-health-check: true        # 是否过滤健康检查请求
      
      # 敏感信息配置
      sensitive-keys:                  # 敏感参数关键字列表
        - password
        - pwd
        - token
        - secret
        - key
        - authorization
        - passwd
        - credential
        - auth
        - sign
        - signature
        - accessToken
        - refreshToken
        - authToken
        - apiSecret
        - clientSecret
        - privateKey
        - publicKey
      
      # URL过滤配置
      exclude-url-patterns:            # 需要过滤的URL模式
        - /actuator/**
        - /health/**
        - /static/**
        - /css/**
        - /js/**
        - /images/**
        - /favicon.ico
        - /webjars/**
        - "/*.css"
        - "/*.js"
        - "/*.png"
        - "/*.jpg"
        - "/*.gif"
        - "/*.ico"
        - "/*.svg"
        - "/*.woff"
        - "/*.woff2"
        - "/*.ttf"
        - "/*.eot"

# =====================================================
# 数据库连接池配置（针对日志表优化）
# =====================================================
spring:
  datasource:
    hikari:
      # 连接池配置
      minimum-idle: 5                  # 最小空闲连接数
      maximum-pool-size: 20            # 最大连接池大小
      connection-timeout: 30000        # 连接超时时间（毫秒）
      idle-timeout: 600000             # 空闲超时时间（毫秒）
      max-lifetime: 1800000            # 连接最大生命周期（毫秒）
      
      # 连接测试配置
      connection-test-query: SELECT 1
      validation-timeout: 5000
      
      # 连接池名称
      pool-name: SystemLogHikariCP
      
      # 性能优化配置
      leak-detection-threshold: 60000  # 连接泄漏检测阈值
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none                   # 不自动创建表结构
    show-sql: false                    # 不显示SQL语句
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: false
        use_sql_comments: false
        jdbc:
          batch_size: 50               # 批处理大小
          batch_versioned_data: true
        order_inserts: true            # 优化插入顺序
        order_updates: true            # 优化更新顺序

# =====================================================
# MyBatis配置
# =====================================================
mybatis:
  configuration:
    # 性能优化配置
    cache-enabled: true                # 启用二级缓存
    lazy-loading-enabled: true         # 启用延迟加载
    aggressive-lazy-loading: false     # 关闭积极延迟加载
    multiple-result-sets-enabled: true # 允许多结果集
    use-column-label: true             # 使用列标签
    use-generated-keys: true           # 使用生成的键
    auto-mapping-behavior: partial     # 自动映射行为
    auto-mapping-unknown-column-behavior: warning # 未知列映射行为
    default-executor-type: reuse       # 默认执行器类型
    default-statement-timeout: 25      # 默认语句超时时间
    default-fetch-size: 100            # 默认获取大小
    safe-row-bounds-enabled: false     # 安全行边界
    map-underscore-to-camel-case: true # 下划线转驼峰
    local-cache-scope: session         # 本地缓存作用域
    jdbc-type-for-null: other          # NULL值的JDBC类型
    call-setters-on-nulls: false       # 在NULL值上调用setter
    return-instance-for-empty-row: false # 空行返回实例
    log-prefix: mybatis.               # 日志前缀
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl # 日志实现

# =====================================================
# 线程池配置
# =====================================================
thread-pool:
  # 系统日志专用线程池
  system-log:
    core-size: 5                       # 核心线程数
    max-size: 10                       # 最大线程数
    queue-capacity: 500                # 队列容量
    keep-alive-seconds: 60             # 线程空闲时间
    thread-name-prefix: SystemLog-     # 线程名前缀
    
  # 数据清理专用线程池
  data-cleanup:
    core-size: 2                       # 核心线程数
    max-size: 4                        # 最大线程数
    queue-capacity: 100                # 队列容量
    keep-alive-seconds: 300            # 线程空闲时间
    thread-name-prefix: DataCleanup-   # 线程名前缀

# =====================================================
# 定时任务配置
# =====================================================
scheduled:
  # 数据清理任务
  data-cleanup:
    enabled: true                      # 是否启用数据清理任务
    cron: "0 0 2 * * ?"               # 每天凌晨2点执行
    retention-days: 90                 # 数据保留天数
    
  # 表优化任务
  table-optimize:
    enabled: true                      # 是否启用表优化任务
    cron: "0 0 3 * * SUN"             # 每周日凌晨3点执行
    
  # 监控报告任务
  monitor-report:
    enabled: true                      # 是否启用监控报告任务
    cron: "0 0 8 * * ?"               # 每天早上8点执行
    
  # 健康检查任务
  health-check:
    enabled: true                      # 是否启用健康检查任务
    cron: "0 */5 * * * ?"             # 每5分钟执行一次

# =====================================================
# 监控告警配置
# =====================================================
monitor:
  alert:
    # 错误率告警
    error-rate:
      enabled: true                    # 是否启用错误率告警
      threshold: 10                    # 错误率阈值（百分比）
      time-window: 300                 # 时间窗口（秒）
      
    # 慢请求告警
    slow-request:
      enabled: true                    # 是否启用慢请求告警
      threshold: 5000                  # 响应时间阈值（毫秒）
      
    # 异常请求告警
    error-request:
      enabled: true                    # 是否启用异常请求告警
      
    # 通知配置
    notification:
      email:
        enabled: false                 # 是否启用邮件通知
        recipients:                    # 收件人列表
          - <EMAIL>
      sms:
        enabled: false                 # 是否启用短信通知
      webhook:
        enabled: false                 # 是否启用Webhook通知
        url: ""                        # Webhook URL

# =====================================================
# 日志配置
# =====================================================
logging:
  level:
    # 系统日志相关包的日志级别
    com.haoys.user.service.impl.SystemRequestRecordServiceImpl: INFO
    com.haoys.user.security.aspect.SystemRequestRecordAspect: INFO
    com.haoys.user.mapper.SystemRequestRecordMapper: DEBUG
    
    # MyBatis SQL日志
    com.haoys.user.mapper: DEBUG
    
    # 根日志级别
    root: INFO
    
  # 日志文件配置
  file:
    name: logs/system-request-record.log
    max-size: 100MB
    max-history: 30
    
  pattern:
    # 控制台日志格式
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    # 文件日志格式
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# =====================================================
# 缓存配置
# =====================================================
cache:
  # Redis缓存配置（如果使用Redis）
  redis:
    # 统计数据缓存
    statistics:
      ttl: 300                         # 缓存过期时间（秒）
      key-prefix: "system:log:stats:"  # 缓存键前缀
      
    # 监控数据缓存
    monitor:
      ttl: 60                          # 缓存过期时间（秒）
      key-prefix: "system:log:monitor:" # 缓存键前缀

# =====================================================
# 安全配置
# =====================================================
security:
  # 访问控制
  access-control:
    # 系统日志查看权限
    view-permissions:
      - "system:request-record:list"
      - "system:request-record:query"
      
    # 系统日志统计权限
    statistics-permissions:
      - "system:request-record:statistics"
      
    # 系统日志管理权限
    manage-permissions:
      - "system:request-record:delete"
      - "system:request-record:manage"
      - "system:request-record:export"
      
    # 系统监控权限
    monitor-permissions:
      - "system:request-record:monitor"

# =====================================================
# 性能优化配置
# =====================================================
performance:
  # 批处理配置
  batch:
    size: 100                          # 批处理大小
    timeout: 30                        # 批处理超时时间（秒）
    
  # 分页配置
  pagination:
    max-page-size: 1000                # 最大分页大小
    default-page-size: 20              # 默认分页大小
    
  # 查询优化配置
  query:
    max-result-size: 10000             # 最大结果集大小
    timeout: 30                        # 查询超时时间（秒）
