# 系统访问日志记录功能说明

## 概述

本功能实现了一个完整的系统访问日志记录系统，专门用于医学审计日志，支持高性能、可靠的日志记录和查询功能。

## 功能特性

### 1. 完整的请求生命周期记录
- **请求信息**: URL、方法、参数、开始/结束时间
- **响应信息**: 结果、响应时间、大小、HTTP状态码
- **用户信息**: 用户ID、用户名、真实姓名、会话ID
- **网络信息**: IP地址、地理位置、User-Agent、来源页面
- **设备信息**: 浏览器类型、操作系统、设备类型
- **业务信息**: 业务类型、操作类型、数据来源、访问类型
- **异常信息**: 错误消息、异常类型、异常堆栈

### 2. 高性能设计
- **异步记录**: 使用异步任务，不阻塞主业务流程
- **批量处理**: 支持批量插入，提高数据库性能
- **索引优化**: 精心设计的数据库索引，支持高效查询
- **参数限制**: 自动限制参数长度，防止性能问题
- **智能过滤**: 自动过滤静态资源和健康检查请求

### 3. 安全特性
- **敏感信息脱敏**: 自动识别并脱敏密码、token等敏感信息
- **异常隔离**: 日志记录异常不影响主业务
- **权限控制**: 完整的权限控制体系
- **审计追踪**: 支持链路追踪和会话管理

### 4. 监控告警
- **实时监控**: 实时统计系统性能指标
- **慢请求检测**: 自动检测并告警慢请求
- **异常监控**: 监控系统异常情况
- **健康检查**: 定期检查系统健康状态

## 核心组件

### 1. 数据模型
- **SystemRequestRecord**: 系统访问记录实体类
- **SystemRequestRecordMapper**: MyBatis映射接口
- **SystemRequestRecordService**: 服务接口和实现

### 2. 切面处理
- **SystemRequestRecordAspect**: 增强版切面，支持完整的请求生命周期记录

### 3. 工具类
- **DeviceInfoUtils**: 设备信息解析工具
- **SensitiveDataUtils**: 敏感数据脱敏工具
- **AsyncTaskFactory**: 异步任务工厂

### 4. 配置管理
- **SystemLogProperties**: 系统日志配置属性
- **SystemLogScheduleConfig**: 定时任务配置

## 数据库设计

### 表结构
```sql
CREATE TABLE `system_request_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户账号',
  `real_name` varchar(100) DEFAULT NULL COMMENT '用户真实姓名',
  `session_id` varchar(128) DEFAULT NULL COMMENT '会话ID',
  `trace_id` varchar(128) DEFAULT NULL COMMENT '链路追踪ID',
  -- 更多字段...
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统访问日志记录表-医学审计日志';
```

### 索引设计
- **复合索引**: 用户、时间、状态组合索引
- **性能索引**: IP地址、URL、响应时间等性能分析索引
- **业务索引**: 会话、链路追踪、业务类型等业务查询索引

## API接口

### 查询接口
- `GET /system/request-record/list` - 分页查询日志记录
- `GET /system/request-record/{id}` - 根据ID查询记录
- `GET /system/request-record/trace/{traceId}` - 根据链路追踪ID查询
- `GET /system/request-record/session/{sessionId}` - 根据会话ID查询
- `GET /system/request-record/user` - 根据用户查询
- `GET /system/request-record/ip` - 根据IP查询
- `GET /system/request-record/errors` - 查询异常日志
- `GET /system/request-record/slow` - 查询慢请求日志

### 统计接口
- `GET /system/request-record/statistics/access` - 访问量统计
- `GET /system/request-record/statistics/hot-urls` - 热点URL统计
- `GET /system/request-record/statistics/users` - 用户访问统计
- `GET /system/request-record/statistics/ips` - IP访问统计
- `GET /system/request-record/statistics/response-time` - 响应时间分布
- `GET /system/request-record/statistics/device-types` - 设备类型分布
- `GET /system/request-record/statistics/browsers` - 浏览器分布

### 管理接口
- `DELETE /system/request-record/cleanup` - 清理过期数据
- `DELETE /system/request-record/batch` - 批量删除
- `POST /system/request-record/optimize` - 优化表
- `GET /system/request-record/export` - 导出数据

## 配置说明

### 基础配置
```yaml
edc:
  system:
    log:
      enabled: true                    # 是否启用系统日志记录
      async-enabled: true              # 是否启用异步日志记录
      save-request-data: true          # 是否记录请求参数
      save-response-data: true         # 是否记录响应数据
      max-param-length: 2000           # 最大参数长度限制
      slow-request-threshold: 5000     # 慢请求阈值（毫秒）
      data-retention-days: 90          # 数据保留天数
```

### 敏感信息配置
```yaml
edc:
  system:
    log:
      sensitive-keys:                  # 敏感参数关键字列表
        - password
        - token
        - secret
        - key
        - authorization
```

### 过滤配置
```yaml
edc:
  system:
    log:
      exclude-url-patterns:            # 需要过滤的URL模式
        - /actuator/**
        - /static/**
        - "/*.css"
        - "/*.js"
```

## 定时任务

### 数据清理任务
- **执行时间**: 每天凌晨2点
- **功能**: 清理过期的日志数据
- **配置**: `scheduled.data-cleanup.cron`

### 表优化任务
- **执行时间**: 每周日凌晨3点
- **功能**: 优化数据库表性能
- **配置**: `scheduled.table-optimize.cron`

### 监控报告任务
- **执行时间**: 每天早上8点
- **功能**: 生成系统监控报告
- **配置**: `scheduled.monitor-report.cron`

### 健康检查任务
- **执行时间**: 每5分钟
- **功能**: 检查系统健康状态
- **配置**: `scheduled.health-check.cron`

## 性能优化

### 1. 数据库优化
- **索引设计**: 覆盖常用查询场景的复合索引
- **分区表**: 支持按时间分区（可选）
- **连接池**: 优化的数据库连接池配置
- **批量操作**: 支持批量插入和更新

### 2. 应用优化
- **异步处理**: 所有日志记录操作异步执行
- **缓存机制**: 统计数据缓存
- **参数限制**: 防止超长参数影响性能
- **智能过滤**: 减少不必要的日志记录

### 3. 监控优化
- **实时统计**: 高效的实时统计算法
- **告警机制**: 智能告警，避免告警风暴
- **资源监控**: 监控系统资源使用情况

## 安全考虑

### 1. 数据安全
- **敏感信息脱敏**: 自动脱敏密码、token等敏感信息
- **访问控制**: 基于角色的权限控制
- **数据加密**: 支持敏感字段加密存储（可选）

### 2. 系统安全
- **异常隔离**: 日志记录异常不影响主业务
- **资源限制**: 防止日志记录消耗过多系统资源
- **审计追踪**: 完整的操作审计链路

## 部署说明

### 1. 数据库初始化
```bash
# 执行建表脚本
mysql -u username -p database_name < scripts/database/system_request_record_create.sql
```

### 2. 配置文件
```bash
# 复制配置文件
cp docs/system-request-record-config.yml src/main/resources/application-log.yml
```

### 3. 启动参数
```bash
# 启动时指定配置文件
java -jar app.jar --spring.profiles.active=log
```

## 监控指标

### 1. 性能指标
- **总请求数**: 系统总请求量
- **成功率**: 请求成功率
- **平均响应时间**: 系统平均响应时间
- **慢请求数**: 慢请求统计

### 2. 业务指标
- **活跃用户数**: 活跃用户统计
- **热点URL**: 访问频率最高的URL
- **设备分布**: 用户设备类型分布
- **地理分布**: 用户地理位置分布

### 3. 系统指标
- **数据库性能**: 数据库查询性能
- **存储使用**: 日志数据存储使用情况
- **异常统计**: 系统异常情况统计

## 故障排查

### 1. 常见问题
- **日志记录失败**: 检查数据库连接和权限
- **性能问题**: 检查索引和查询优化
- **内存泄漏**: 检查ThreadLocal清理

### 2. 调试方法
- **开启SQL日志**: 查看具体的SQL执行情况
- **性能分析**: 使用性能分析工具
- **日志分析**: 分析应用日志定位问题

## 最佳实践

### 1. 配置建议
- **合理设置保留天数**: 根据业务需求和存储容量
- **优化索引**: 根据实际查询模式调整索引
- **监控告警**: 设置合理的告警阈值

### 2. 运维建议
- **定期备份**: 定期备份重要的审计日志
- **性能监控**: 持续监控系统性能
- **容量规划**: 根据增长趋势进行容量规划

### 3. 开发建议
- **合理使用注解**: 在关键接口上添加@Log注解
- **敏感信息处理**: 注意敏感信息的脱敏处理
- **异常处理**: 完善的异常处理机制
