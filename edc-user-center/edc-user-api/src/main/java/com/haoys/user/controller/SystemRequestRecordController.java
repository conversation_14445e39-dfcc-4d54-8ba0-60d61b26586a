package com.haoys.user.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.model.SystemRequestRecord;
import com.haoys.user.service.SystemRequestRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问日志记录控制器
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Api(tags = "系统访问日志记录管理")
@RestController
@RequestMapping("/system/request-record")
public class SystemRequestRecordController extends BaseController {
    
    @Autowired
    private SystemRequestRecordService systemRequestRecordService;
    
    @ApiOperation("分页查询系统访问日志记录")
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('system:request-record:list')")
    public CommonResult<Map<String, Object>> list(
            @ApiParam("页码") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam("用户名") @RequestParam(value = "userName", required = false) String userName,
            @ApiParam("请求URL") @RequestParam(value = "requestUrl", required = false) String requestUrl,
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("是否成功") @RequestParam(value = "isSuccess", required = false) Boolean isSuccess) {
        
        Map<String, Object> result = systemRequestRecordService.getRecordsByPage(
                pageNum, pageSize, userName, requestUrl, startTime, endTime, isSuccess);
        return CommonResult.success(result);
    }
    
    @ApiOperation("根据ID查询系统访问日志记录")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<SystemRequestRecord> getById(
            @ApiParam("记录ID") @PathVariable Long id) {
        SystemRequestRecord record = systemRequestRecordService.getRecordById(id);
        return CommonResult.success(record);
    }
    
    @ApiOperation("根据链路追踪ID查询日志记录")
    @GetMapping("/trace/{traceId}")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<List<SystemRequestRecord>> getByTraceId(
            @ApiParam("链路追踪ID") @PathVariable String traceId) {
        List<SystemRequestRecord> records = systemRequestRecordService.getRecordsByTraceId(traceId);
        return CommonResult.success(records);
    }
    
    @ApiOperation("根据会话ID查询日志记录")
    @GetMapping("/session/{sessionId}")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<List<SystemRequestRecord>> getBySessionId(
            @ApiParam("会话ID") @PathVariable String sessionId) {
        List<SystemRequestRecord> records = systemRequestRecordService.getRecordsBySessionId(sessionId);
        return CommonResult.success(records);
    }
    
    @ApiOperation("根据用户查询日志记录")
    @GetMapping("/user")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<List<SystemRequestRecord>> getByUser(
            @ApiParam("用户ID") @RequestParam(value = "userId", required = false) Long userId,
            @ApiParam("用户名") @RequestParam(value = "userName", required = false) String userName,
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "100") Integer limit) {
        
        List<SystemRequestRecord> records = systemRequestRecordService.getRecordsByUser(
                userId, userName, startTime, endTime, limit);
        return CommonResult.success(records);
    }
    
    @ApiOperation("根据IP地址查询日志记录")
    @GetMapping("/ip")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<List<SystemRequestRecord>> getByIp(
            @ApiParam("IP地址") @RequestParam("requestIp") String requestIp,
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "100") Integer limit) {
        
        List<SystemRequestRecord> records = systemRequestRecordService.getRecordsByIp(
                requestIp, startTime, endTime, limit);
        return CommonResult.success(records);
    }
    
    @ApiOperation("查询异常日志记录")
    @GetMapping("/errors")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<List<SystemRequestRecord>> getErrorRecords(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "100") Integer limit) {
        
        List<SystemRequestRecord> records = systemRequestRecordService.getErrorRecords(startTime, endTime, limit);
        return CommonResult.success(records);
    }
    
    @ApiOperation("查询慢请求日志记录")
    @GetMapping("/slow")
    @PreAuthorize("hasAuthority('system:request-record:query')")
    public CommonResult<List<SystemRequestRecord>> getSlowRecords(
            @ApiParam("最小响应时间") @RequestParam(value = "minResponseTime", required = false) Long minResponseTime,
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "100") Integer limit) {
        
        List<SystemRequestRecord> records = systemRequestRecordService.getSlowRecords(
                minResponseTime, startTime, endTime, limit);
        return CommonResult.success(records);
    }
    
    @ApiOperation("获取访问量统计")
    @GetMapping("/statistics/access")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getAccessStatistics(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("分组字段") @RequestParam(value = "groupBy", defaultValue = "day") String groupBy) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getAccessStatistics(startTime, endTime, groupBy);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取热点URL统计")
    @GetMapping("/statistics/hot-urls")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getHotUrlStatistics(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getHotUrlStatistics(startTime, endTime, limit);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取用户访问统计")
    @GetMapping("/statistics/users")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getUserStatistics(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getUserStatistics(startTime, endTime, limit);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取IP访问统计")
    @GetMapping("/statistics/ips")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getIpStatistics(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("限制数量") @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getIpStatistics(startTime, endTime, limit);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取响应时间分布统计")
    @GetMapping("/statistics/response-time")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getResponseTimeDistribution(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getResponseTimeDistribution(startTime, endTime);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取设备类型分布统计")
    @GetMapping("/statistics/device-types")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getDeviceTypeDistribution(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getDeviceTypeDistribution(startTime, endTime);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取浏览器分布统计")
    @GetMapping("/statistics/browsers")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<List<Map<String, Object>>> getBrowserDistribution(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        List<Map<String, Object>> statistics = systemRequestRecordService.getBrowserDistribution(startTime, endTime);
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取表统计信息")
    @GetMapping("/statistics/table")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<Map<String, Object>> getTableStatistics() {
        Map<String, Object> statistics = systemRequestRecordService.getTableStatistics();
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("获取系统监控数据")
    @GetMapping("/monitor")
    @PreAuthorize("hasAuthority('system:request-record:monitor')")
    public CommonResult<Map<String, Object>> getSystemMonitorData() {
        Map<String, Object> monitorData = systemRequestRecordService.getSystemMonitorData();
        return CommonResult.success(monitorData);
    }
    
    @ApiOperation("获取实时统计数据")
    @GetMapping("/statistics/realtime")
    @PreAuthorize("hasAuthority('system:request-record:statistics')")
    public CommonResult<Map<String, Object>> getRealTimeStatistics() {
        Map<String, Object> statistics = systemRequestRecordService.getRealTimeStatistics();
        return CommonResult.success(statistics);
    }
    
    @ApiOperation("删除过期日志记录")
    @DeleteMapping("/cleanup")
    @PreAuthorize("hasAuthority('system:request-record:delete')")
    public CommonResult<Integer> deleteExpiredRecords(
            @ApiParam("保留天数") @RequestParam(value = "retentionDays", defaultValue = "90") Integer retentionDays) {
        int deletedCount = systemRequestRecordService.deleteExpiredRecords(retentionDays);
        return CommonResult.success(deletedCount);
    }
    
    @ApiOperation("根据ID列表删除日志记录")
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:request-record:delete')")
    public CommonResult<Integer> deleteRecordsByIds(@RequestBody List<Long> ids) {
        int deletedCount = systemRequestRecordService.deleteRecordsByIds(ids);
        return CommonResult.success(deletedCount);
    }
    
    @ApiOperation("优化表")
    @PostMapping("/optimize")
    @PreAuthorize("hasAuthority('system:request-record:manage')")
    public CommonResult<Void> optimizeTable() {
        systemRequestRecordService.optimizeTable();
        return CommonResult.success(null);
    }
    
    @ApiOperation("导出日志记录")
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('system:request-record:export')")
    public CommonResult<String> exportRecords(
            @ApiParam("开始时间") @RequestParam(value = "startTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam("结束时间") @RequestParam(value = "endTime", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @ApiParam("用户名") @RequestParam(value = "userName", required = false) String userName,
            @ApiParam("请求URL") @RequestParam(value = "requestUrl", required = false) String requestUrl) {
        
        String filePath = systemRequestRecordService.exportRecords(startTime, endTime, userName, requestUrl);
        return CommonResult.success(filePath);
    }
}
