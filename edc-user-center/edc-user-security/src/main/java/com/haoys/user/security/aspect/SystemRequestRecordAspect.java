package com.haoys.user.security.aspect;

import com.alibaba.fastjson.JSON;
import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.domain.model.LoginUserInfo;
import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.user.common.util.ServletUtils;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.SystemLogProperties;
import com.haoys.user.enums.system.BusinessStatus;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import com.haoys.user.model.SystemRequestRecord;
import com.haoys.user.service.SystemUserInfoService;
import com.haoys.user.util.DeviceInfoUtils;
import com.haoys.user.util.SensitiveDataUtils;
import net.dreamlu.mica.ip2region.core.IpInfo;
import net.dreamlu.mica.ip2region.impl.Ip2regionSearcherImpl;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.validation.BindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 系统访问日志记录切面 - 增强版
 * 用于医学审计日志，支持高性能、可靠的日志记录
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Aspect
@Component
@Order(1)
public class SystemRequestRecordAspect {
    
    private static final Logger log = LoggerFactory.getLogger(SystemRequestRecordAspect.class);
    
    @Autowired
    private SystemLogProperties logProperties;
    
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    
    // 性能统计
    private static final AtomicLong totalRequests = new AtomicLong(0);
    private static final AtomicLong slowRequests = new AtomicLong(0);
    private static final AtomicLong errorRequests = new AtomicLong(0);
    
    // ThreadLocal存储请求上下文
    private static final ThreadLocal<SystemRequestRecord> REQUEST_CONTEXT = new ThreadLocal<>();
    
    /**
     * 环绕通知 - 记录完整的请求生命周期
     */
    @Around("@annotation(controllerLog)")
    public Object doAround(ProceedingJoinPoint joinPoint, Log controllerLog) throws Throwable {
        // 检查是否启用日志记录
        if (!logProperties.isEnabled()) {
            return joinPoint.proceed();
        }
        
        long startTime = System.currentTimeMillis();
        SystemRequestRecord record = null;
        Object result = null;
        Throwable exception = null;
        
        try {
            // 检查是否需要过滤此请求
            if (shouldSkipLogging()) {
                return joinPoint.proceed();
            }
            
            // 初始化日志记录
            record = initializeLogRecord(joinPoint, controllerLog, startTime);
            REQUEST_CONTEXT.set(record);
            
            // 执行目标方法
            result = joinPoint.proceed();
            
            // 记录成功响应
            recordSuccessResponse(record, result);
            
            return result;
            
        } catch (Throwable e) {
            exception = e;
            // 记录异常信息
            recordException(record, e);
            throw e;
            
        } finally {
            try {
                // 完成日志记录
                if (record != null) {
                    completeLogRecord(record, startTime, result, exception);
                    
                    // 异步保存日志
                    saveLogAsync(record);
                    
                    // 更新统计信息
                    updateStatistics(record);
                }
            } catch (Exception e) {
                log.error("日志记录失败", e);
            } finally {
                // 清理ThreadLocal
                REQUEST_CONTEXT.remove();
                // 清理MDC
                MDC.remove("traceId");
            }
        }
    }
    
    /**
     * 检查是否应该跳过日志记录
     */
    private boolean shouldSkipLogging() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return true;
            }
            
            String requestUri = request.getRequestURI();
            
            // 过滤静态资源
            if (logProperties.isFilterStaticResources()) {
                for (String pattern : logProperties.getExcludeUrlPatterns()) {
                    if (pathMatcher.match(pattern, requestUri)) {
                        return true;
                    }
                }
            }
            
            // 过滤健康检查
            if (logProperties.isFilterHealthCheck() && 
                (requestUri.contains("/health") || requestUri.contains("/actuator"))) {
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.warn("检查过滤条件时发生异常", e);
            return false;
        }
    }
    
    /**
     * 初始化日志记录
     */
    private SystemRequestRecord initializeLogRecord(ProceedingJoinPoint joinPoint, Log controllerLog, long startTime) {
        SystemRequestRecord record = new SystemRequestRecord();
        
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return record;
            }
            
            // 生成链路追踪ID
            String traceId = generateTraceId();
            record.setTraceId(traceId);
            MDC.put("traceId", traceId);
            
            // 基本请求信息
            record.setRequestStartTime(new Date(startTime));
            record.setRequestMethod(request.getMethod().toUpperCase());
            record.setRequestUrl(request.getRequestURI());
            record.setSessionId(request.getSession().getId());
            
            // 方法信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = signature.getName();
            record.setMethodName(className + "." + methodName + "()");
            
            // 注解信息
            record.setRequestName(controllerLog.title());
            record.setBusinessType(controllerLog.businessType().value());
            record.setOperatorType(controllerLog.operatorType().value());
            record.setDataFrom(controllerLog.platformType().value());
            record.setProjectRecordLog(controllerLog.projectRecordLog());
            
            // 网络信息
            String ipAddress = RequestIpUtils.getIpAddress(request);
            record.setRequestIp(ipAddress);
            record.setUserAgent(request.getHeader("User-Agent"));
            record.setReferer(request.getHeader("Referer"));
            
            // 用户信息
            setUserInfo(record);
            
            // 设备信息
            if (logProperties.isSaveDeviceInfo()) {
                setDeviceInfo(record, request);
            }
            
            // 地理位置信息
            if (logProperties.isSaveLocationInfo()) {
                setLocationInfo(record, ipAddress);
            }
            
            // 请求参数
            if (logProperties.isSaveRequestData() && controllerLog.isSaveRequestData()) {
                setRequestParams(record, joinPoint);
            }
            
            // 访问类型判断
            setAccessType(record, request);
            
        } catch (Exception e) {
            log.error("初始化日志记录失败", e);
        }
        
        return record;
    }
    
    /**
     * 记录成功响应
     */
    private void recordSuccessResponse(SystemRequestRecord record, Object result) {
        if (record == null) return;
        
        try {
            record.setIsSuccess(true);
            record.setStatus(1);
            
            // 记录响应数据
            if (logProperties.isSaveResponseData() && result != null) {
                String responseJson = JSON.toJSONString(result);
                if (responseJson.length() > logProperties.getMaxResponseLength()) {
                    responseJson = responseJson.substring(0, logProperties.getMaxResponseLength()) + "...";
                }
                record.setResponseResult(responseJson);
                
                // 计算响应大小
                record.setResponseSize((long) responseJson.getBytes().length);
            }
            
            // 从CommonResult中获取状态码
            if (result instanceof CommonResult) {
                CommonResult<?> commonResult = (CommonResult<?>) result;
                record.setHttpStatus(commonResult.getCode());
                if (commonResult.getCode() != 200) {
                    record.setIsSuccess(false);
                    record.setStatus(0);
                    record.setErrorMessage(commonResult.getMessage());
                }
            } else {
                record.setHttpStatus(200);
            }
            
        } catch (Exception e) {
            log.error("记录成功响应失败", e);
        }
    }
    
    /**
     * 记录异常信息
     */
    private void recordException(SystemRequestRecord record, Throwable exception) {
        if (record == null || exception == null) return;
        
        try {
            record.setIsSuccess(false);
            record.setStatus(0);
            record.setHttpStatus(500);
            record.setExceptionType(exception.getClass().getSimpleName());
            
            // 错误消息
            String errorMessage = exception.getMessage();
            if (StringUtils.isNotEmpty(errorMessage)) {
                if (errorMessage.length() > 2000) {
                    errorMessage = errorMessage.substring(0, 2000) + "...";
                }
                record.setErrorMessage(errorMessage);
            }
            
            // 简化的异常堆栈
            String stackTrace = getSimplifiedStackTrace(exception);
            record.setExceptionStack(stackTrace);
            
        } catch (Exception e) {
            log.error("记录异常信息失败", e);
        }
    }
    
    /**
     * 完成日志记录
     */
    private void completeLogRecord(SystemRequestRecord record, long startTime, Object result, Throwable exception) {
        if (record == null) return;
        
        try {
            long endTime = System.currentTimeMillis();
            record.setRequestEndTime(new Date(endTime));
            record.setResponseTime(endTime - startTime);
            
            // 检查是否为慢请求
            if (record.getResponseTime() > logProperties.getSlowRequestThreshold()) {
                log.warn("慢请求检测: {} ms, URL: {}, Method: {}", 
                    record.getResponseTime(), record.getRequestUrl(), record.getMethodName());
            }
            
        } catch (Exception e) {
            log.error("完成日志记录失败", e);
        }
    }
    
    /**
     * 异步保存日志
     */
    private void saveLogAsync(SystemRequestRecord record) {
        try {
            if (logProperties.isAsyncEnabled()) {
                AsyncTaskManager.ownerTask().execute(AsyncTaskFactory.saveSystemRequestRecord(record));
            } else {
                // 同步保存（用于测试或特殊需求）
                saveLogSync(record);
            }
        } catch (Exception e) {
            log.error("保存日志失败", e);
        }
    }
    
    /**
     * 同步保存日志
     */
    private void saveLogSync(SystemRequestRecord record) {
        // 这里需要实现具体的保存逻辑
        // 可以通过Service或Repository保存到数据库
        log.info("同步保存日志: {}", record.getTraceId());
    }
    
    /**
     * 更新统计信息
     */
    private void updateStatistics(SystemRequestRecord record) {
        totalRequests.incrementAndGet();
        
        if (!record.getIsSuccess()) {
            errorRequests.incrementAndGet();
        }
        
        if (record.getResponseTime() != null && 
            record.getResponseTime() > logProperties.getSlowRequestThreshold()) {
            slowRequests.incrementAndGet();
        }
    }
    
    /**
     * 设置用户信息
     */
    private void setUserInfo(SystemRequestRecord record) {
        try {
            LoginUserInfo loginUserInfo = SecurityUtils.getLoginUser();
            if (loginUserInfo != null) {
                record.setUserId(loginUserInfo.getId());
                record.setUserName(loginUserInfo.getUsername());
                record.setRealName(loginUserInfo.getRealName());
            }
        } catch (Exception e) {
            log.debug("获取用户信息失败", e);
        }
    }
    
    /**
     * 设置设备信息
     */
    private void setDeviceInfo(SystemRequestRecord record, HttpServletRequest request) {
        try {
            String userAgent = request.getHeader("User-Agent");
            if (StringUtils.isNotEmpty(userAgent)) {
                Map<String, String> deviceInfo = DeviceInfoUtils.parseUserAgent(userAgent);
                record.setBrowser(deviceInfo.get("browser"));
                record.setOperatingSystem(deviceInfo.get("os"));
                record.setDeviceType(deviceInfo.get("deviceType"));
            }
        } catch (Exception e) {
            log.debug("解析设备信息失败", e);
        }
    }
    
    /**
     * 设置地理位置信息
     */
    private void setLocationInfo(SystemRequestRecord record, String ipAddress) {
        try {
            if (StringUtils.isNotEmpty(ipAddress) && !"127.0.0.1".equals(ipAddress) && !"localhost".equals(ipAddress)) {
                Ip2regionSearcherImpl ip2regionSearcher = SpringUtils.getBean(Ip2regionSearcherImpl.class);
                if (ip2regionSearcher != null) {
                    IpInfo ipInfo = ip2regionSearcher.memorySearch(ipAddress);
                    if (ipInfo != null) {
                        record.setLocation(ipInfo.getAddressAndIsp());
                    }
                }
            }
        } catch (Exception e) {
            log.debug("获取地理位置信息失败", e);
        }
    }
    
    /**
     * 设置请求参数
     */
    private void setRequestParams(SystemRequestRecord record, ProceedingJoinPoint joinPoint) {
        try {
            String params = argsArrayToString(joinPoint.getArgs());
            
            // 敏感信息脱敏
            params = SensitiveDataUtils.maskSensitiveData(params, logProperties.getSensitiveKeys());
            
            // 长度限制
            if (params.length() > logProperties.getMaxParamLength()) {
                params = params.substring(0, logProperties.getMaxParamLength()) + "...";
            }
            
            record.setRequestParam(params);
        } catch (Exception e) {
            log.debug("设置请求参数失败", e);
        }
    }
    
    /**
     * 设置访问类型
     */
    private void setAccessType(SystemRequestRecord record, HttpServletRequest request) {
        try {
            String userAgent = request.getHeader("User-Agent");
            String requestUri = request.getRequestURI();
            
            if (requestUri.startsWith("/api/")) {
                record.setAccessType("API");
            } else if (StringUtils.isNotEmpty(userAgent) && 
                      (userAgent.contains("Mobile") || userAgent.contains("Android") || userAgent.contains("iPhone"))) {
                record.setAccessType("MOBILE");
            } else {
                record.setAccessType("WEB");
            }
        } catch (Exception e) {
            log.debug("设置访问类型失败", e);
        }
    }
    
    /**
     * 参数转换为字符串
     */
    private String argsArrayToString(Object[] paramsArray) {
        StringBuilder params = new StringBuilder();
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (o != null && !isFilterObject(o)) {
                    try {
                        params.append(JSON.toJSONString(o)).append(" ");
                    } catch (Exception e) {
                        params.append(o.toString()).append(" ");
                    }
                }
            }
        }
        return params.toString().trim();
    }
    
    /**
     * 判断是否需要过滤的对象
     */
    private boolean isFilterObject(Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection<?> collection = (Collection<?>) o;
            for (Object value : collection) {
                if (value instanceof MultipartFile) {
                    return true;
                }
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map<?, ?> map = (Map<?, ?>) o;
            for (Object value : map.values()) {
                if (value instanceof MultipartFile) {
                    return true;
                }
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || 
               o instanceof HttpServletResponse || o instanceof BindingResult;
    }
    
    /**
     * 获取当前请求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 生成链路追踪ID
     */
    private String generateTraceId() {
        return "EDC-" + System.currentTimeMillis() + "-" + 
               Integer.toHexString(new Random().nextInt(0xFFFFFF));
    }
    
    /**
     * 获取简化的异常堆栈
     */
    private String getSimplifiedStackTrace(Throwable exception) {
        try {
            StackTraceElement[] stackTrace = exception.getStackTrace();
            StringBuilder sb = new StringBuilder();
            
            // 只保留前5个堆栈信息
            int maxLines = Math.min(5, stackTrace.length);
            for (int i = 0; i < maxLines; i++) {
                sb.append(stackTrace[i].toString()).append("\n");
            }
            
            String result = sb.toString();
            // 限制长度
            if (result.length() > 1000) {
                result = result.substring(0, 1000) + "...";
            }
            
            return result;
        } catch (Exception e) {
            return exception.getClass().getSimpleName() + ": " + exception.getMessage();
        }
    }
    
    /**
     * 获取统计信息
     */
    public static Map<String, Long> getStatistics() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("totalRequests", totalRequests.get());
        stats.put("slowRequests", slowRequests.get());
        stats.put("errorRequests", errorRequests.get());
        return stats;
    }
}
