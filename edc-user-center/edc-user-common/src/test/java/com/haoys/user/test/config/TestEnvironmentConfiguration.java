package com.haoys.user.test.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 测试环境配置类
 * 
 * <p>提供测试环境的特殊配置</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@TestConfiguration
@Profile("test")
public class TestEnvironmentConfiguration {

    /**
     * 测试环境标识Bean
     */
    @Bean
    @Primary
    public String testEnvironmentMarker() {
        return "test-environment";
    }
}
