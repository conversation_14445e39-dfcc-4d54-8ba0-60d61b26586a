package com.haoys.user.common.api;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CommonResult 测试类
 * 
 * <AUTHOR>
 * @since 2.0.0
 */
@DisplayName("CommonResult 测试")
class CommonResultTest {

    @Nested
    @DisplayName("成功响应测试")
    class SuccessResponseTest {

        @Test
        @DisplayName("创建无数据成功响应")
        void testSuccessWithoutData() {
            CommonResult<String> result = CommonResult.success();
            
            assertTrue(result.isSuccess());
            assertFalse(result.isFailed());
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode());
            assertEquals(ResultCode.SUCCESS.getMessage(), result.getMessage());
            assertNull(result.getData());
            assertEquals(200, result.getStatus());
            assertNotNull(result.getTimestamp());
        }

        @Test
        @DisplayName("创建带数据成功响应")
        void testSuccessWithData() {
            String testData = "test data";
            CommonResult<String> result = CommonResult.success(testData);
            
            assertTrue(result.isSuccess());
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode());
            assertEquals(ResultCode.SUCCESS.getMessage(), result.getMessage());
            assertEquals(testData, result.getData());
        }

        @Test
        @DisplayName("创建带数据和消息的成功响应")
        void testSuccessWithDataAndMessage() {
            String testData = "test data";
            String message = "操作成功";
            CommonResult<String> result = CommonResult.success(testData, message);
            
            assertTrue(result.isSuccess());
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode());
            assertEquals(message, result.getMessage());
            assertEquals(testData, result.getData());
        }

        @Test
        @DisplayName("创建带数据和结果码的成功响应")
        void testSuccessWithDataAndResultCode() {
            String testData = "test data";
            CommonResult<String> result = CommonResult.success(testData, ResultCode.SUCCESS);
            
            assertTrue(result.isSuccess());
            assertEquals(ResultCode.SUCCESS.getCode(), result.getCode());
            assertEquals(ResultCode.SUCCESS.getMessage(), result.getMessage());
            assertEquals(testData, result.getData());
        }
    }

    @Nested
    @DisplayName("失败响应测试")
    class FailedResponseTest {

        @Test
        @DisplayName("创建默认失败响应")
        void testFailedDefault() {
            CommonResult<String> result = CommonResult.failed();
            
            assertTrue(result.isFailed());
            assertFalse(result.isSuccess());
            assertEquals(ResultCode.FAILED.getCode(), result.getCode());
            assertEquals(ResultCode.FAILED.getMessage(), result.getMessage());
            assertNull(result.getData());
            assertEquals(500, result.getStatus());
        }

        @Test
        @DisplayName("创建带错误码的失败响应")
        void testFailedWithResultCode() {
            CommonResult<String> result = CommonResult.failed(ResultCode.VALIDATE_FAILED);
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.VALIDATE_FAILED.getCode(), result.getCode());
            assertEquals(ResultCode.VALIDATE_FAILED.getMessage(), result.getMessage());
            assertNull(result.getData());
        }

        @Test
        @DisplayName("创建带错误码和消息的失败响应")
        void testFailedWithResultCodeAndMessage() {
            String customMessage = "自定义错误消息";
            CommonResult<String> result = CommonResult.failed(ResultCode.FAILED, customMessage);
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.FAILED.getCode(), result.getCode());
            assertEquals(customMessage, result.getMessage());
            assertNull(result.getData());
        }

        @Test
        @DisplayName("创建带消息的失败响应")
        void testFailedWithMessage() {
            String message = "操作失败";
            CommonResult<String> result = CommonResult.failed(message);
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.FAILED.getCode(), result.getCode());
            assertEquals(message, result.getMessage());
            assertEquals(500, result.getStatus());
        }

        @Test
        @DisplayName("创建带错误码和消息的失败响应")
        void testFailedWithCodeAndMessage() {
            int code = 10001;
            String message = "自定义错误";
            CommonResult<String> result = CommonResult.failed(code, message);
            
            assertTrue(result.isFailed());
            assertEquals(code, result.getCode());
            assertEquals(message, result.getMessage());
            assertEquals(500, result.getStatus());
        }
    }

    @Nested
    @DisplayName("特殊响应测试")
    class SpecialResponseTest {

        @Test
        @DisplayName("创建参数验证失败响应")
        void testValidateFailed() {
            CommonResult<String> result = CommonResult.validateFailed();
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.VALIDATE_FAILED.getCode(), result.getCode());
            assertEquals(ResultCode.VALIDATE_FAILED.getMessage(), result.getMessage());
        }

        @Test
        @DisplayName("创建带消息的参数验证失败响应")
        void testValidateFailedWithMessage() {
            String message = "参数验证失败";
            CommonResult<String> result = CommonResult.validateFailed(message);
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL.getCode(), result.getCode());
            assertEquals(message, result.getMessage());
            assertEquals(400, result.getStatus());
        }

        @Test
        @DisplayName("创建未授权响应")
        void testUnauthorized() {
            CommonResult<String> result = CommonResult.unauthorized();
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.UNAUTHORIZED.getCode(), result.getCode());
            assertEquals(ResultCode.UNAUTHORIZED.getMessage(), result.getMessage());
            assertEquals(401, result.getStatus());
        }

        @Test
        @DisplayName("创建带消息的未授权响应")
        void testUnauthorizedWithMessage() {
            String message = "请先登录";
            CommonResult<String> result = CommonResult.unauthorized(message);
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.UNAUTHORIZED.getCode(), result.getCode());
            assertEquals(message, result.getMessage());
            assertEquals(401, result.getStatus());
        }

        @Test
        @DisplayName("创建禁止访问响应")
        void testForbidden() {
            CommonResult<String> result = CommonResult.forbidden();
            
            assertTrue(result.isFailed());
            assertEquals(ResultCode.FORBIDDEN.getCode(), result.getCode());
            assertEquals(ResultCode.FORBIDDEN.getMessage(), result.getMessage());
            assertEquals(403, result.getStatus());
        }
    }

    @Nested
    @DisplayName("安全性测试")
    class SecurityTest {

        @Test
        @DisplayName("测试敏感信息过滤")
        void testSensitiveDataFiltering() {
            String sensitiveMessage = "用户信息: password=123456, token=abc123";
            CommonResult<String> result = CommonResult.failed(sensitiveMessage);

            assertFalse(result.getMessage().contains("123456"));
            assertFalse(result.getMessage().contains("abc123"));
            assertTrue(result.getMessage().contains("password=***"));
            assertTrue(result.getMessage().contains("token=***"));
        }

        @Test
        @DisplayName("测试消息长度限制")
        void testMessageLengthLimit() {
            StringBuilder longMessage = new StringBuilder();
            for (int i = 0; i < 600; i++) {
                longMessage.append("a");
            }
            
            CommonResult<String> result = CommonResult.failed(longMessage.toString());
            
            assertTrue(result.getMessage().length() <= 500);
            assertTrue(result.getMessage().endsWith("..."));
        }

        @Test
        @DisplayName("测试null参数处理")
        void testNullParameterHandling() {
            // 测试null消息
            CommonResult<String> result1 = CommonResult.success(null, (String) null);
            assertEquals("", result1.getMessage());
            
            // 测试null数据
            CommonResult<String> result2 = CommonResult.success(null);
            assertNull(result2.getData());
            
            // 测试null结果码
            assertThrows(IllegalArgumentException.class, () -> {
                CommonResult.failed((BaseResultCode) null);
            });
        }
    }

    @Nested
    @DisplayName("工具方法测试")
    class UtilityMethodTest {

        @Test
        @DisplayName("测试追踪ID设置")
        void testTraceIdSetting() {
            String traceId = "trace-123";
            CommonResult<String> result = CommonResult.success("data").withTraceId(traceId);
            
            assertEquals(traceId, result.getTraceId());
        }

        @Test
        @DisplayName("测试追踪ID长度限制")
        void testTraceIdLengthLimit() {
            StringBuilder longTraceId = new StringBuilder();
            for (int i = 0; i < 100; i++) {
                longTraceId.append("a");
            }
            
            CommonResult<String> result = CommonResult.success("data").withTraceId(longTraceId.toString());
            
            assertTrue(result.getTraceId().length() <= 64);
        }

        @Test
        @DisplayName("测试敏感数据标记")
        void testSensitiveDataMarking() {
            CommonResult<String> result = CommonResult.success("sensitive data").markSensitive();
            
            assertNull(result.getSafeData());
            assertEquals("sensitive data", result.getData());
        }

        @Test
        @DisplayName("测试equals和hashCode")
        void testEqualsAndHashCode() {
            CommonResult<String> result1 = CommonResult.success("data", "message");
            CommonResult<String> result2 = CommonResult.success("data", "message");
            CommonResult<String> result3 = CommonResult.success("other", "message");
            
            assertEquals(result1, result2);
            assertEquals(result1.hashCode(), result2.hashCode());
            assertNotEquals(result1, result3);
        }
    }

    @Nested
    @DisplayName("HTTP状态码映射测试")
    class HttpStatusMappingTest {

        @Test
        @DisplayName("测试成功状态码映射")
        void testSuccessStatusMapping() {
            CommonResult<String> result = CommonResult.success("data");
            assertEquals(200, result.getStatus());
        }

        @Test
        @DisplayName("测试未授权状态码映射")
        void testUnauthorizedStatusMapping() {
            CommonResult<String> result = CommonResult.failed(ResultCode.UNAUTHORIZED);
            assertEquals(401, result.getStatus());
        }

        @Test
        @DisplayName("测试禁止访问状态码映射")
        void testForbiddenStatusMapping() {
            CommonResult<String> result = CommonResult.failed(ResultCode.FORBIDDEN);
            assertEquals(403, result.getStatus());
        }

        @Test
        @DisplayName("测试参数错误状态码映射")
        void testBadRequestStatusMapping() {
            CommonResult<String> result = CommonResult.failed(ResultCode.REQUEST_BUSSINESS_PARAM_FAIL);
            assertEquals(400, result.getStatus());
        }

        @Test
        @DisplayName("测试服务器错误状态码映射")
        void testInternalServerErrorStatusMapping() {
            CommonResult<String> result = CommonResult.failed(ResultCode.FAILED);
            assertEquals(500, result.getStatus());
        }
    }
}
