package com.haoys.user.common.file;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 图片压缩服务测试类
 * 验证URL和文件路径参数处理的修复
 */
@DisplayName("图片压缩服务测试")
class ImageCompressionServiceTest {

    private static final Logger log = LoggerFactory.getLogger(ImageCompressionServiceTest.class);

    private ImageCompressionService imageCompressionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        imageCompressionService = new ImageCompressionService();
    }

    /**
     * 测试URL参数处理 - 模拟processImageWithCache(String originalImageUrl, ...)的使用场景
     */
    @Test
    @DisplayName("测试URL参数处理")
    void testCompressExistingImageWithUrl() {
        log.info("=== 测试URL参数处理 ===");

        // 模拟一个HTTP URL（这个URL可能不存在，但我们主要测试参数处理逻辑）
        String testUrl = "https://example.com/test/image.jpg";

        // 调用单参数版本的compressExistingImage方法
        // 这模拟了processImageWithCache(String originalImageUrl, ...)中的调用
        String result = imageCompressionService.compressExistingImage(testUrl);

        log.info("URL参数处理结果: 输入={}, 输出={}", testUrl, result);

        // 验证方法没有抛出异常，并且返回了结果（即使是原URL）
        assertNotNull(result, "返回结果不应为null");
        assertEquals(testUrl, result, "由于URL不存在，应该返回原URL");

        log.info("✓ URL参数处理测试通过");
    }

    /**
     * 测试文件路径参数处理 - 模拟processImageWithCache(ProjectTesteeFormImageVo imageVo, ...)的使用场景
     */
    @Test
    @DisplayName("测试文件路径参数处理")
    void testCompressExistingImageWithFilePath() {
        log.info("=== 测试文件路径参数处理 ===");

        // 模拟一个文件路径
        String testFilePath = "/upload/test/image.jpg";
        String testUrl = "https://example.com/test/image.jpg";

        // 调用双参数版本的compressExistingImage方法
        // 这模拟了processImageWithCache(ProjectTesteeFormImageVo imageVo, ...)中的调用
        String result = imageCompressionService.compressExistingImage(testFilePath, testUrl);

        log.info("文件路径参数处理结果: 输入路径={}, 备用URL={}, 输出={}", testFilePath, testUrl, result);

        // 验证方法没有抛出异常，并且返回了结果
        assertNotNull(result, "返回结果不应为null");
        assertEquals(testFilePath, result, "由于文件不存在，应该返回原路径");

        log.info("✓ 文件路径参数处理测试通过");
    }

    /**
     * 测试参数类型检测逻辑
     */
    @Test
    @DisplayName("测试参数类型检测逻辑")
    void testParameterTypeDetection() {
        log.info("=== 测试参数类型检测逻辑 ===");

        // 测试各种输入参数
        String[] testInputs = {
            "https://example.com/image.jpg",  // HTTP URL
            "http://example.com/image.png",   // HTTP URL
            "/upload/local/image.jpeg",       // 本地路径
            "upload/relative/image.gif",      // 相对路径
            "file:///absolute/path/image.bmp" // 文件协议URL
        };

        for (String input : testInputs) {
            log.info("测试输入: {}", input);

            // 测试单参数方法
            String result1 = imageCompressionService.compressExistingImage(input);
            log.info("  单参数结果: {}", result1);

            // 测试双参数方法
            String result2 = imageCompressionService.compressExistingImage(input, null);
            log.info("  双参数结果: {}", result2);

            // 验证两种调用方式的结果一致
            assertEquals(result1, result2, "单参数和双参数方法结果应该一致");
        }

        log.info("✓ 参数类型检测逻辑测试完成");
    }

    /**
     * 测试空值和异常情况处理
     */
    @Test
    @DisplayName("测试空值和异常情况处理")
    void testNullAndExceptionHandling() {
        log.info("=== 测试空值和异常情况处理 ===");

        // 测试null输入
        String result1 = imageCompressionService.compressExistingImage(null);
        log.info("null输入处理结果: {}", result1);
        assertNull(result1, "null输入应该返回null");

        // 测试空字符串输入
        String result2 = imageCompressionService.compressExistingImage("");
        log.info("空字符串输入处理结果: {}", result2);
        assertEquals("", result2, "空字符串输入应该返回空字符串");

        // 测试双参数null输入
        String result3 = imageCompressionService.compressExistingImage(null, null);
        log.info("双参数null输入处理结果: {}", result3);
        assertNull(result3, "双参数null输入应该返回null");

        log.info("✓ 空值和异常情况处理测试通过");
    }

    /**
     * 测试日志输出格式 - 验证监控友好的日志格式
     */
    @Test
    @DisplayName("测试日志输出格式")
    void testLogOutputFormat() {
        log.info("=== 测试日志输出格式 ===");

        // 测试各种场景的日志输出
        String[] testCases = {
            "https://example.com/test-image.jpg",  // HTTP URL
            "/upload/test/image.png",              // 本地路径
            "invalid-format.txt"                   // 非图片格式
        };

        for (String testCase : testCases) {
            log.info("测试用例: {}", testCase);

            // 调用压缩方法，主要目的是验证日志格式
            String result = imageCompressionService.compressExistingImage(testCase);

            // 验证返回结果不为null（即使压缩失败也应该返回原路径）
            assertNotNull(result, "压缩结果不应为null");

            log.info("测试用例 {} 完成，结果: {}", testCase, result);
        }

        log.info("✓ 日志输出格式测试完成");
    }
}
