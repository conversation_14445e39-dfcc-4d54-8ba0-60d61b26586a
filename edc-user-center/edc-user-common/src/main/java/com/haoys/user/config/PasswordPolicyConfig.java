package com.haoys.user.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 密码策略配置
 * 
 * <p>定义密码复杂度要求和验证规则</p>
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "pwd")
public class PasswordPolicyConfig {

    /**
     * 是否启用密码策略
     */
    private boolean openPwdConfig = false;

    /**
     * 密码最小长度
     */
    private int minLength = 8;

    /**
     * 密码最大长度
     */
    private int maxLength = 20;

    /**
     * 是否要求包含数字
     */
    private boolean requireDigit = true;

    /**
     * 是否要求包含字母
     */
    private boolean requireLetter = true;

    /**
     * 是否要求包含特殊字符
     */
    private boolean requireSpecialChar = false;

    /**
     * 是否要求包含大写字母
     */
    private boolean requireUppercase = false;

    /**
     * 是否要求包含小写字母
     */
    private boolean requireLowercase = false;

    /**
     * 密码历史记录数量（不能重复使用最近N个密码）
     */
    private int passwordHistoryCount = 5;

    /**
     * 密码过期天数（0表示不过期）
     */
    private int passwordExpiryDays = 0;

    /**
     * 密码过期前提醒天数
     */
    private int passwordExpiryWarningDays = 7;

    /**
     * 允许的特殊字符
     */
    private String allowedSpecialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

    // Getters and Setters
    public boolean isOpenPwdConfig() {
        return openPwdConfig;
    }

    public void setOpenPwdConfig(boolean openPwdConfig) {
        this.openPwdConfig = openPwdConfig;
    }

    public int getMinLength() {
        return minLength;
    }

    public void setMinLength(int minLength) {
        this.minLength = minLength;
    }

    public int getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public boolean isRequireDigit() {
        return requireDigit;
    }

    public void setRequireDigit(boolean requireDigit) {
        this.requireDigit = requireDigit;
    }

    public boolean isRequireLetter() {
        return requireLetter;
    }

    public void setRequireLetter(boolean requireLetter) {
        this.requireLetter = requireLetter;
    }

    public boolean isRequireSpecialChar() {
        return requireSpecialChar;
    }

    public void setRequireSpecialChar(boolean requireSpecialChar) {
        this.requireSpecialChar = requireSpecialChar;
    }

    public boolean isRequireUppercase() {
        return requireUppercase;
    }

    public void setRequireUppercase(boolean requireUppercase) {
        this.requireUppercase = requireUppercase;
    }

    public boolean isRequireLowercase() {
        return requireLowercase;
    }

    public void setRequireLowercase(boolean requireLowercase) {
        this.requireLowercase = requireLowercase;
    }

    public int getPasswordHistoryCount() {
        return passwordHistoryCount;
    }

    public void setPasswordHistoryCount(int passwordHistoryCount) {
        this.passwordHistoryCount = passwordHistoryCount;
    }

    public int getPasswordExpiryDays() {
        return passwordExpiryDays;
    }

    public void setPasswordExpiryDays(int passwordExpiryDays) {
        this.passwordExpiryDays = passwordExpiryDays;
    }

    public int getPasswordExpiryWarningDays() {
        return passwordExpiryWarningDays;
    }

    public void setPasswordExpiryWarningDays(int passwordExpiryWarningDays) {
        this.passwordExpiryWarningDays = passwordExpiryWarningDays;
    }

    public String getAllowedSpecialChars() {
        return allowedSpecialChars;
    }

    public void setAllowedSpecialChars(String allowedSpecialChars) {
        this.allowedSpecialChars = allowedSpecialChars;
    }
}
