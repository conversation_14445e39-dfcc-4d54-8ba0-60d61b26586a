package com.haoys.user.config;

import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.manager.AsyncTaskManager;
import com.haoys.user.manager.factory.AsyncTaskFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Map;

/**
 * 系统日志定时任务配置
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "edc.system.log", name = "enabled", havingValue = "true", matchIfMissing = true)
public class SystemLogScheduleConfig {
    
    private static final Logger log = LoggerFactory.getLogger(SystemLogScheduleConfig.class);
    
    @Autowired
    private SystemLogProperties logProperties;
    
    /**
     * 数据清理任务
     * 每天凌晨2点执行，清理过期的日志数据
     */
    @Scheduled(cron = "${scheduled.data-cleanup.cron:0 0 2 * * ?}")
    @ConditionalOnProperty(prefix = "scheduled.data-cleanup", name = "enabled", havingValue = "true", matchIfMissing = true)
    public void cleanupExpiredData() {
        if (!logProperties.isAutoCleanupEnabled()) {
            log.debug("数据自动清理功能已禁用，跳过清理任务");
            return;
        }
        
        try {
            log.info("开始执行数据清理任务");
            
            // 使用异步任务执行清理
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.cleanupExpiredSystemRequestRecords(logProperties.getDataRetentionDays())
            );
            
            log.info("数据清理任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行数据清理任务失败", e);
        }
    }
    
    /**
     * 表优化任务
     * 每周日凌晨3点执行，优化数据库表
     */
    @Scheduled(cron = "${scheduled.table-optimize.cron:0 0 3 * * SUN}")
    @ConditionalOnProperty(prefix = "scheduled.table-optimize", name = "enabled", havingValue = "true", matchIfMissing = true)
    public void optimizeTable() {
        try {
            log.info("开始执行表优化任务");
            
            // 使用异步任务执行优化
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.optimizeSystemRequestRecordTable()
            );
            
            log.info("表优化任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行表优化任务失败", e);
        }
    }
    
    /**
     * 监控报告任务
     * 每天早上8点执行，生成系统监控报告
     */
    @Scheduled(cron = "${scheduled.monitor-report.cron:0 0 8 * * ?}")
    @ConditionalOnProperty(prefix = "scheduled.monitor-report", name = "enabled", havingValue = "true", matchIfMissing = true)
    public void generateMonitorReport() {
        try {
            log.info("开始执行监控报告任务");
            
            // 使用异步任务生成报告
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.generateSystemMonitorReport()
            );
            
            log.info("监控报告任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行监控报告任务失败", e);
        }
    }
    
    /**
     * 健康检查任务
     * 每5分钟执行一次，检查系统健康状态
     */
    @Scheduled(cron = "${scheduled.health-check.cron:0 */5 * * * ?}")
    @ConditionalOnProperty(prefix = "scheduled.health-check", name = "enabled", havingValue = "true", matchIfMissing = true)
    public void checkSystemHealth() {
        try {
            log.debug("开始执行系统健康检查");
            
            // 使用异步任务执行健康检查
            AsyncTaskManager.ownerTask().execute(
                AsyncTaskFactory.checkSystemHealth()
            );
            
            log.debug("系统健康检查任务已提交到异步队列");
        } catch (Exception e) {
            log.error("执行系统健康检查失败", e);
        }
    }
    
    /**
     * 统计数据刷新任务
     * 每小时执行一次，刷新统计数据缓存
     */
    @Scheduled(cron = "0 0 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.statistics-refresh", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void refreshStatistics() {
        try {
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.debug("SystemRequestRecordService 未注入，跳过统计数据刷新");
                return;
            }

            log.debug("开始刷新统计数据缓存");

            // 刷新实时统计数据
            Object realTimeStats = recordService.getClass().getMethod("getRealTimeStatistics").invoke(recordService);
            log.debug("实时统计数据已刷新: {}", realTimeStats);

            // 刷新表统计信息
            Object tableStats = recordService.getClass().getMethod("getTableStatistics").invoke(recordService);
            log.debug("表统计信息已刷新: {}", tableStats);

        } catch (Exception e) {
            log.error("刷新统计数据失败", e);
        }
    }
    
    /**
     * 性能监控任务
     * 每分钟执行一次，监控系统性能指标
     */
    @Scheduled(cron = "0 * * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.performance-monitor", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void monitorPerformance() {
        try {
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                return;
            }

            // 获取实时统计数据
            Object realTimeStats = recordService.getClass().getMethod("getRealTimeStatistics").invoke(recordService);

            if (realTimeStats instanceof Map) {
                Map<?, ?> statsMap = (Map<?, ?>) realTimeStats;

                // 检查错误率
                Object errorRequests = statsMap.get("errorRequests");
                Object totalRequests = statsMap.get("totalRequests");

                if (errorRequests instanceof Number && totalRequests instanceof Number) {
                    long errors = ((Number) errorRequests).longValue();
                    long total = ((Number) totalRequests).longValue();

                    if (total > 0) {
                        double errorRate = (double) errors / total * 100;

                        // 错误率告警阈值检查
                        if (errorRate > 10) { // 错误率超过10%
                            log.warn("系统错误率告警: 错误率={}%, 错误数={}, 总请求数={}",
                                    String.format("%.2f", errorRate), errors, total);
                        }

                        // 记录性能指标
                        if (total > 100) { // 只有在有足够样本时才记录
                            log.info("系统性能指标: 总请求数={}, 成功数={}, 错误数={}, 错误率={}%",
                                    total,
                                    statsMap.get("successRequests"),
                                    errors,
                                    String.format("%.2f", errorRate));
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("性能监控任务执行失败", e);
        }
    }
    
    /**
     * 慢请求监控任务
     * 每10分钟执行一次，监控慢请求情况
     */
    @Scheduled(cron = "0 */10 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.slow-request-monitor", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void monitorSlowRequests() {
        try {
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                return;
            }

            // 获取最近10分钟的慢请求
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            java.util.Date endTime = calendar.getTime();
            calendar.add(java.util.Calendar.MINUTE, -10);
            java.util.Date startTime = calendar.getTime();

            Object slowRecords = recordService.getClass().getMethod("getSlowRecords", Long.class, java.util.Date.class, java.util.Date.class, int.class)
                    .invoke(recordService, logProperties.getSlowRequestThreshold(), startTime, endTime, 10);

            if (slowRecords instanceof java.util.List) {
                java.util.List<?> recordList = (java.util.List<?>) slowRecords;
                if (!recordList.isEmpty()) {
                    log.warn("发现 {} 个慢请求（最近10分钟）", recordList.size());

                    for (Object record : recordList) {
                        try {
                            Object requestUrl = record.getClass().getMethod("getRequestUrl").invoke(record);
                            Object methodName = record.getClass().getMethod("getMethodName").invoke(record);
                            Object responseTime = record.getClass().getMethod("getResponseTime").invoke(record);
                            Object userName = record.getClass().getMethod("getUserName").invoke(record);

                            log.warn("慢请求详情: URL={}, 方法={}, 响应时间={}ms, 用户={}",
                                    requestUrl, methodName, responseTime, userName);
                        } catch (Exception e) {
                            log.debug("获取慢请求详情失败", e);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("慢请求监控任务执行失败", e);
        }
    }
    
    /**
     * 异常请求监控任务
     * 每5分钟执行一次，监控异常请求情况
     */
    @Scheduled(cron = "0 */5 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.error-request-monitor", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void monitorErrorRequests() {
        try {
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                return;
            }

            // 获取最近5分钟的异常请求
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            java.util.Date endTime = calendar.getTime();
            calendar.add(java.util.Calendar.MINUTE, -5);
            java.util.Date startTime = calendar.getTime();

            Object errorRecords = recordService.getClass().getMethod("getErrorRecords", java.util.Date.class, java.util.Date.class, int.class)
                    .invoke(recordService, startTime, endTime, 20);

            if (errorRecords instanceof java.util.List) {
                java.util.List<?> recordList = (java.util.List<?>) errorRecords;
                if (!recordList.isEmpty()) {
                    log.warn("发现 {} 个异常请求（最近5分钟）", recordList.size());

                    // 统计异常类型
                    java.util.Map<String, Long> exceptionTypeCount = new java.util.HashMap<>();
                    for (Object record : recordList) {
                        try {
                            Object exceptionType = record.getClass().getMethod("getExceptionType").invoke(record);
                            if (exceptionType != null) {
                                String typeStr = exceptionType.toString();
                                exceptionTypeCount.put(typeStr,
                                    exceptionTypeCount.getOrDefault(typeStr, 0L) + 1);
                            }
                        } catch (Exception e) {
                            log.debug("获取异常类型失败", e);
                        }
                    }

                    log.warn("异常类型统计: {}", exceptionTypeCount);
                }
            }

        } catch (Exception e) {
            log.error("异常请求监控任务执行失败", e);
        }
    }
    
    /**
     * 数据库连接监控任务
     * 每30分钟执行一次，监控数据库连接状态
     */
    @Scheduled(cron = "0 */30 * * * ?")
    @ConditionalOnProperty(prefix = "scheduled.database-monitor", name = "enabled", havingValue = "true", matchIfMissing = false)
    public void monitorDatabaseConnection() {
        try {
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                return;
            }

            // 执行简单查询测试数据库连接
            Object tableStats = recordService.getClass().getMethod("getTableStatistics").invoke(recordService);

            if (tableStats instanceof Map && !((Map<?, ?>) tableStats).isEmpty()) {
                log.debug("数据库连接正常，表统计信息: {}", tableStats);
            } else {
                log.warn("数据库连接可能存在问题，无法获取表统计信息");
            }

        } catch (Exception e) {
            log.error("数据库连接监控失败", e);
        }
    }
}
