package com.haoys.user.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StreamOperations;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;

@Component
public class MessageProducer {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public void sendMessage(String streamKey, Map<String, Object> message, Duration delay, Integer priority) {
        StreamOperations<String, Object, Object> streamOperations = redisTemplate.opsForStream();
        RecordId messageId = streamOperations.add(streamKey, message);
        if (delay != null) {
            redisTemplate.opsForZSet().add("delayed:" + streamKey, messageId.getValue(), System.currentTimeMillis() + delay.toMillis());
        }
        if (priority != null) {
            redisTemplate.opsForZSet().add("priority:" + streamKey, messageId.getValue(), priority);
        }
    }
}

