package com.haoys.user.util;

import com.haoys.user.common.util.StringUtils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 敏感数据脱敏工具类
 * 用于对日志中的敏感信息进行脱敏处理
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
public class SensitiveDataUtils {
    
    // 默认脱敏字符
    private static final String MASK_CHAR = "*";
    
    // 常见敏感字段的正则表达式
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "([\"']?(?:password|pwd|passwd|pass)[\"']?\\s*[:=]\\s*[\"']?)([^\"',\\s}]+)([\"']?)",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern TOKEN_PATTERN = Pattern.compile(
        "([\"']?(?:token|accessToken|refreshToken|authToken|authorization)[\"']?\\s*[:=]\\s*[\"']?)([^\"',\\s}]+)([\"']?)",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern SECRET_PATTERN = Pattern.compile(
        "([\"']?(?:secret|secretKey|apiSecret|clientSecret)[\"']?\\s*[:=]\\s*[\"']?)([^\"',\\s}]+)([\"']?)",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern KEY_PATTERN = Pattern.compile(
        "([\"']?(?:key|apiKey|privateKey|publicKey)[\"']?\\s*[:=]\\s*[\"']?)([^\"',\\s}]+)([\"']?)",
        Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern CREDENTIAL_PATTERN = Pattern.compile(
        "([\"']?(?:credential|credentials|auth|sign|signature)[\"']?\\s*[:=]\\s*[\"']?)([^\"',\\s}]+)([\"']?)",
        Pattern.CASE_INSENSITIVE
    );
    
    // 身份证号正则表达式
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
        "\\b([1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx])\\b"
    );
    
    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "\\b(1[3-9]\\d{9})\\b"
    );
    
    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "\\b([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})\\b"
    );
    
    // 银行卡号正则表达式
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile(
        "\\b([1-9]\\d{15,18})\\b"
    );
    
    /**
     * 对敏感数据进行脱敏处理
     * 
     * @param data 原始数据
     * @param sensitiveKeys 敏感字段关键字数组
     * @return 脱敏后的数据
     */
    public static String maskSensitiveData(String data, String[] sensitiveKeys) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }
        
        String maskedData = data;
        
        // 使用预定义的正则表达式进行脱敏
        maskedData = maskByPattern(maskedData, PASSWORD_PATTERN);
        maskedData = maskByPattern(maskedData, TOKEN_PATTERN);
        maskedData = maskByPattern(maskedData, SECRET_PATTERN);
        maskedData = maskByPattern(maskedData, KEY_PATTERN);
        maskedData = maskByPattern(maskedData, CREDENTIAL_PATTERN);
        
        // 使用自定义敏感字段进行脱敏
        if (sensitiveKeys != null && sensitiveKeys.length > 0) {
            for (String key : sensitiveKeys) {
                maskedData = maskCustomField(maskedData, key);
            }
        }
        
        // 脱敏个人信息
        maskedData = maskPersonalInfo(maskedData);
        
        return maskedData;
    }
    
    /**
     * 使用正则表达式进行脱敏
     */
    private static String maskByPattern(String data, Pattern pattern) {
        Matcher matcher = pattern.matcher(data);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String prefix = matcher.group(1);
            String value = matcher.group(2);
            String suffix = matcher.group(3);
            
            String maskedValue = maskValue(value);
            matcher.appendReplacement(sb, prefix + maskedValue + suffix);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 脱敏自定义字段
     */
    private static String maskCustomField(String data, String fieldName) {
        if (StringUtils.isEmpty(fieldName)) {
            return data;
        }
        
        // 构建动态正则表达式
        String regex = "([\"']?" + Pattern.quote(fieldName) + "[\"']?\\s*[:=]\\s*[\"']?)([^\"',\\s}]+)([\"']?)";
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        
        return maskByPattern(data, pattern);
    }
    
    /**
     * 脱敏个人信息
     */
    private static String maskPersonalInfo(String data) {
        String maskedData = data;
        
        // 脱敏身份证号
        maskedData = maskIdCard(maskedData);
        
        // 脱敏手机号
        maskedData = maskPhone(maskedData);
        
        // 脱敏邮箱
        maskedData = maskEmail(maskedData);
        
        // 脱敏银行卡号
        maskedData = maskBankCard(maskedData);
        
        return maskedData;
    }
    
    /**
     * 脱敏身份证号
     */
    public static String maskIdCard(String data) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }
        
        Matcher matcher = ID_CARD_PATTERN.matcher(data);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String idCard = matcher.group(1);
            String masked = idCard.substring(0, 6) + "********" + idCard.substring(14);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 脱敏手机号
     */
    public static String maskPhone(String data) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }
        
        Matcher matcher = PHONE_PATTERN.matcher(data);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String phone = matcher.group(1);
            String masked = phone.substring(0, 3) + "****" + phone.substring(7);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 脱敏邮箱
     */
    public static String maskEmail(String data) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }
        
        Matcher matcher = EMAIL_PATTERN.matcher(data);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String username = matcher.group(1);
            String domain = matcher.group(2);
            
            String maskedUsername;
            if (username.length() <= 3) {
                maskedUsername = username.charAt(0) + "**";
            } else {
                maskedUsername = username.substring(0, 2) + "****" + username.substring(username.length() - 1);
            }
            
            matcher.appendReplacement(sb, maskedUsername + "@" + domain);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 脱敏银行卡号
     */
    public static String maskBankCard(String data) {
        if (StringUtils.isEmpty(data)) {
            return data;
        }
        
        Matcher matcher = BANK_CARD_PATTERN.matcher(data);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String cardNumber = matcher.group(1);
            String masked = cardNumber.substring(0, 4) + "********" + cardNumber.substring(cardNumber.length() - 4);
            matcher.appendReplacement(sb, masked);
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * 脱敏值
     */
    private static String maskValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return value;
        }

        int length = value.length();
        if (length <= 2) {
            return repeatChar(MASK_CHAR, length);
        } else if (length <= 6) {
            return value.charAt(0) + repeatChar(MASK_CHAR, length - 2) + value.charAt(length - 1);
        } else {
            return value.substring(0, 2) + repeatChar(MASK_CHAR, length - 4) + value.substring(length - 2);
        }
    }

    /**
     * 重复字符串（Java 8 兼容版本）
     */
    private static String repeatChar(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 检查字符串是否包含敏感信息
     */
    public static boolean containsSensitiveData(String data, String[] sensitiveKeys) {
        if (StringUtils.isEmpty(data)) {
            return false;
        }
        
        // 检查预定义的敏感模式
        if (PASSWORD_PATTERN.matcher(data).find() ||
            TOKEN_PATTERN.matcher(data).find() ||
            SECRET_PATTERN.matcher(data).find() ||
            KEY_PATTERN.matcher(data).find() ||
            CREDENTIAL_PATTERN.matcher(data).find()) {
            return true;
        }
        
        // 检查自定义敏感字段
        if (sensitiveKeys != null) {
            for (String key : sensitiveKeys) {
                if (data.toLowerCase().contains(key.toLowerCase())) {
                    return true;
                }
            }
        }
        
        // 检查个人信息
        return ID_CARD_PATTERN.matcher(data).find() ||
               PHONE_PATTERN.matcher(data).find() ||
               BANK_CARD_PATTERN.matcher(data).find();
    }
    
    /**
     * 脱敏JSON字符串中的敏感字段
     */
    public static String maskJsonSensitiveData(String jsonData, String[] sensitiveKeys) {
        if (StringUtils.isEmpty(jsonData)) {
            return jsonData;
        }
        
        // 对JSON格式的数据进行特殊处理
        String maskedData = jsonData;
        
        // 处理JSON中的敏感字段
        if (sensitiveKeys != null) {
            for (String key : sensitiveKeys) {
                // JSON格式: "key":"value" 或 "key": "value"
                String regex = "(\"" + Pattern.quote(key) + "\"\\s*:\\s*\")([^\"]+)(\")";
                Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
                maskedData = maskByPattern(maskedData, pattern);
            }
        }
        
        return maskedData;
    }
    
    /**
     * 脱敏URL参数中的敏感信息
     */
    public static String maskUrlParameters(String url, String[] sensitiveKeys) {
        if (StringUtils.isEmpty(url) || !url.contains("?")) {
            return url;
        }
        
        String[] parts = url.split("\\?", 2);
        if (parts.length != 2) {
            return url;
        }
        
        String baseUrl = parts[0];
        String queryString = parts[1];
        
        // 脱敏查询参数
        String maskedQuery = maskQueryParameters(queryString, sensitiveKeys);
        
        return baseUrl + "?" + maskedQuery;
    }
    
    /**
     * 脱敏查询参数
     */
    private static String maskQueryParameters(String queryString, String[] sensitiveKeys) {
        if (StringUtils.isEmpty(queryString)) {
            return queryString;
        }
        
        String[] params = queryString.split("&");
        StringBuilder maskedQuery = new StringBuilder();
        
        for (int i = 0; i < params.length; i++) {
            String param = params[i];
            String[] keyValue = param.split("=", 2);
            
            if (keyValue.length == 2) {
                String key = keyValue[0];
                String value = keyValue[1];
                
                // 检查是否为敏感参数
                if (isSensitiveKey(key, sensitiveKeys)) {
                    value = maskValue(value);
                }
                
                maskedQuery.append(key).append("=").append(value);
            } else {
                maskedQuery.append(param);
            }
            
            if (i < params.length - 1) {
                maskedQuery.append("&");
            }
        }
        
        return maskedQuery.toString();
    }
    
    /**
     * 检查是否为敏感字段
     */
    private static boolean isSensitiveKey(String key, String[] sensitiveKeys) {
        if (StringUtils.isEmpty(key)) {
            return false;
        }
        
        String lowerKey = key.toLowerCase();
        
        // 检查预定义的敏感字段
        String[] defaultSensitiveKeys = {"password", "pwd", "token", "secret", "key", "authorization", "passwd", "credential", "auth", "sign", "signature"};
        for (String sensitiveKey : defaultSensitiveKeys) {
            if (lowerKey.contains(sensitiveKey)) {
                return true;
            }
        }
        
        // 检查自定义敏感字段
        if (sensitiveKeys != null) {
            for (String sensitiveKey : sensitiveKeys) {
                if (lowerKey.contains(sensitiveKey.toLowerCase())) {
                    return true;
                }
            }
        }
        
        return false;
    }
}
