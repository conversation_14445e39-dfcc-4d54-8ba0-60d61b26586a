package com.haoys.user.domain.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 安全Token请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@ApiModel(description = "安全Token请求参数")
public class SecureTokenParam implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 生成Code请求参数
     */
    @Data
    @ApiModel(description = "生成Code请求参数")
    public static class GenerateCodeParam implements Serializable {

        private static final long serialVersionUID = 1L;

        @NotBlank(message = "AppId不能为空")
        @ApiModelProperty(value = "应用ID", required = true, example = "edc_app_dev")
        private String appId;

        @NotBlank(message = "AppSecret不能为空")
        @ApiModelProperty(value = "应用密钥", required = true, example = "edc_secret_dev_2025_abcdef123456")
        private String appSecret;

        @ApiModelProperty(value = "用户ID（可选）", example = "user123")
        private String userId;

        @ApiModelProperty(value = "扩展信息（可选）", example = "extra info")
        private String extraInfo;

        @ApiModelProperty(value = "环境标识（可选，默认为当前环境）", example = "dev")
        private String environment;
    }
    
    /**
     * 获取AccessToken请求参数
     */
    @Data
    @ApiModel(description = "获取AccessToken请求参数")
    public static class GetAccessTokenParam implements Serializable {

        private static final long serialVersionUID = 1L;

        @NotBlank(message = "code不能为空")
        @ApiModelProperty(value = "验证码", required = true, example = "code_abc123")
        private String code;

        @NotBlank(message = "refreshCode不能为空")
        @ApiModelProperty(value = "刷新码", required = true, example = "refresh_abc123")
        private String refreshCode;
    }
    
    /**
     * 验证AccessToken请求参数
     */
    @Data
    @ApiModel(description = "验证AccessToken请求参数")
    public static class ValidateTokenParam implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @NotBlank(message = "accessToken不能为空")
        @ApiModelProperty(value = "访问令牌", required = true, example = "access_xyz789")
        private String accessToken;
    }
}
