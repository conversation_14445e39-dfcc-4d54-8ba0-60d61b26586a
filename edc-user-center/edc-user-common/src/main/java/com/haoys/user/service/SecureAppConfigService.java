package com.haoys.user.service;

import com.haoys.user.model.SecureAppConfig;

import java.util.List;

/**
 * 安全应用配置服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface SecureAppConfigService {
    
    /**
     * 根据ID查询应用配置
     */
    SecureAppConfig getById(Long id);
    
    /**
     * 根据AppId和环境查询应用配置
     */
    SecureAppConfig getByAppIdAndEnvironment(String appId, String environment);
    
    /**
     * 根据环境查询所有启用的应用
     */
    List<SecureAppConfig> getByEnvironment(String environment);
    
    /**
     * 查询所有启用的应用
     */
    List<SecureAppConfig> getAllEnabled();
    
    /**
     * 验证AppId和AppSecret
     */
    SecureAppConfig validateAppCredentials(String appId, String appSecret, String environment);
    
    /**
     * 创建应用配置
     */
    SecureAppConfig create(SecureAppConfig config);
    
    /**
     * 更新应用配置
     */
    SecureAppConfig update(SecureAppConfig config);
    
    /**
     * 删除应用配置
     */
    boolean delete(Long id);
    
    /**
     * 删除应用配置（根据AppId和环境）
     */
    boolean deleteByAppIdAndEnvironment(String appId, String environment);
    
    /**
     * 检查今日请求次数是否超限
     */
    boolean checkDailyRequestLimit(String appId, String environment);
    
    /**
     * 获取当前环境标识
     */
    String getCurrentEnvironment();
}
