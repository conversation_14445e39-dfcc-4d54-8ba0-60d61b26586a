package com.haoys.user.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Redis数据管理响应对象
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "Redis数据管理响应对象")
public class RedisManagementVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 秘钥验证响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "秘钥验证响应")
    public static class SecretVerifyResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "是否有效", example = "true")
        private Boolean valid;
        
        @ApiModelProperty(value = "验证时间")
        private LocalDateTime verifyTime;
        
        @ApiModelProperty(value = "下一步操作提示", example = "请使用code和refreshCode获取AccessToken")
        private String nextStep;
    }

    /**
     * AccessToken响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "AccessToken响应")
    public static class AccessTokenResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "访问令牌", example = "access_token_abc123")
        private String accessToken;
        
        @ApiModelProperty(value = "过期时间")
        private LocalDateTime expireTime;
        
        @ApiModelProperty(value = "有效期（秒）", example = "3600")
        private Long expiresIn;
        
        @ApiModelProperty(value = "用户ID", example = "admin")
        private String userId;
    }

    /**
     * Token验证响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "Token验证响应")
    public static class TokenValidateResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "是否有效", example = "true")
        private Boolean valid;
        
        @ApiModelProperty(value = "剩余时间（秒）", example = "3600")
        private Long remainingTime;
        
        @ApiModelProperty(value = "用户ID", example = "admin")
        private String userId;
        
        @ApiModelProperty(value = "验证时间")
        private LocalDateTime validateTime;
    }

    /**
     * Key查询响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "Key查询响应")
    public static class KeyQueryResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "Redis键", example = "user:info:123")
        private String key;
        
        @ApiModelProperty(value = "是否存在", example = "true")
        private Boolean exists;
        
        @ApiModelProperty(value = "数据类型", example = "string")
        private String type;
        
        @ApiModelProperty(value = "数据值")
        private Object value;
        
        @ApiModelProperty(value = "过期时间（秒）", example = "3600")
        private Long ttl;
        
        @ApiModelProperty(value = "查询时间")
        private LocalDateTime queryTime;
    }

    /**
     * 模糊查询响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "模糊查询响应")
    public static class PatternQueryResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "匹配模式", example = "user:*")
        private String pattern;
        
        @ApiModelProperty(value = "匹配的键列表")
        private List<String> keys;
        
        @ApiModelProperty(value = "匹配数量", example = "50")
        private Integer count;
        
        @ApiModelProperty(value = "是否截断", example = "false")
        private Boolean truncated;
        
        @ApiModelProperty(value = "查询时间")
        private LocalDateTime queryTime;
    }

    /**
     * 前缀查询响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "前缀查询响应")
    public static class PrefixQueryResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "前缀", example = "user:")
        private String prefix;
        
        @ApiModelProperty(value = "匹配的键列表")
        private List<String> keys;
        
        @ApiModelProperty(value = "匹配数量", example = "50")
        private Integer count;
        
        @ApiModelProperty(value = "是否截断", example = "false")
        private Boolean truncated;
        
        @ApiModelProperty(value = "查询时间")
        private LocalDateTime queryTime;
    }

    /**
     * 删除响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "删除响应")
    public static class DeleteResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "Redis键", example = "user:info:123")
        private String key;
        
        @ApiModelProperty(value = "是否成功", example = "true")
        private Boolean success;
        
        @ApiModelProperty(value = "删除前是否存在", example = "true")
        private Boolean existed;
        
        @ApiModelProperty(value = "删除时间")
        private LocalDateTime deleteTime;
    }

    /**
     * 批量删除响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "批量删除响应")
    public static class BatchDeleteResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "请求删除的键列表")
        private List<String> requestKeys;
        
        @ApiModelProperty(value = "成功删除的键列表")
        private List<String> successKeys;
        
        @ApiModelProperty(value = "失败的键列表")
        private List<String> failedKeys;
        
        @ApiModelProperty(value = "删除数量", example = "10")
        private Long deletedCount;
        
        @ApiModelProperty(value = "删除时间")
        private LocalDateTime deleteTime;
    }

    /**
     * 模糊删除响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "模糊删除响应")
    public static class PatternDeleteResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "匹配模式", example = "user:*")
        private String pattern;
        
        @ApiModelProperty(value = "匹配的键列表")
        private List<String> matchedKeys;
        
        @ApiModelProperty(value = "删除数量", example = "10")
        private Long deletedCount;
        
        @ApiModelProperty(value = "删除时间")
        private LocalDateTime deleteTime;
    }

    /**
     * 前缀删除响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "前缀删除响应")
    public static class PrefixDeleteResponse implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @ApiModelProperty(value = "前缀", example = "user:")
        private String prefix;
        
        @ApiModelProperty(value = "匹配的键列表")
        private List<String> matchedKeys;
        
        @ApiModelProperty(value = "删除数量", example = "10")
        private Long deletedCount;
        
        @ApiModelProperty(value = "删除时间")
        private LocalDateTime deleteTime;
    }

    /**
     * Redis键详情
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "Redis键详情")
    public static class KeyDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "Redis键", example = "user:info:123")
        private String key;

        @ApiModelProperty(value = "数据类型", example = "string")
        private String type;

        @ApiModelProperty(value = "数据值")
        private Object value;

        @ApiModelProperty(value = "过期时间（秒）", example = "3600")
        private Long ttl;

        @ApiModelProperty(value = "内存占用（字节）", example = "1024")
        private Long memoryUsage;
    }

    /**
     * AuthCode生成响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "AuthCode生成响应")
    public static class AuthCodeGenerateResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "授权码", example = "AUTH_CODE_123456")
        private String authCode;

        @ApiModelProperty(value = "生成时间")
        private LocalDateTime generateTime;

        @ApiModelProperty(value = "过期时间")
        private LocalDateTime expireTime;

        @ApiModelProperty(value = "有效期(秒)", example = "3600")
        private Long expiresIn;
    }

    /**
     * AuthCode查询响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "AuthCode查询响应")
    public static class AuthCodeQueryResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "总数量", example = "100")
        private Long total;

        @ApiModelProperty(value = "当前页", example = "1")
        private Integer page;

        @ApiModelProperty(value = "每页大小", example = "20")
        private Integer size;

        @ApiModelProperty(value = "总页数", example = "5")
        private Integer totalPages;

        @ApiModelProperty(value = "键列表")
        private List<KeyInfo> keys;

        @ApiModelProperty(value = "查询时间")
        private LocalDateTime queryTime;

        /**
         * 键信息
         */
        @Data
        @Accessors(chain = true)
        @ApiModel(description = "键信息")
        public static class KeyInfo implements Serializable {

            private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "键名", example = "user:info:123")
            private String key;

            @ApiModelProperty(value = "数据类型", example = "string")
            private String type;

            @ApiModelProperty(value = "TTL(秒)", example = "3600")
            private Long ttl;

            @ApiModelProperty(value = "数据值")
            private Object value;
        }
    }

    /**
     * 分页查询响应
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "分页查询响应")
    public static class PageQueryResponse implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "总数量", example = "100")
        private Long total;

        @ApiModelProperty(value = "当前页", example = "1")
        private Integer page;

        @ApiModelProperty(value = "每页大小", example = "20")
        private Integer size;

        @ApiModelProperty(value = "总页数", example = "5")
        private Integer totalPages;

        @ApiModelProperty(value = "键列表")
        private List<String> keys;

        @ApiModelProperty(value = "查询时间")
        private LocalDateTime queryTime;
    }
}
