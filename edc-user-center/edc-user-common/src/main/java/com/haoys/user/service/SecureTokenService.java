package com.haoys.user.service;

import com.haoys.user.domain.dto.SecureTokenDto;
import com.haoys.user.domain.param.SecureTokenParam;
import com.haoys.user.domain.vo.SecureTokenVo;

/**
 * 安全Token管理服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface SecureTokenService {
    
    /**
     * 生成Code和RefreshCode
     * 
     * @param param 生成参数
     * @return Code响应
     */
    SecureTokenVo.CodeResponse generateCode(SecureTokenParam.GenerateCodeParam param);
    
    /**
     * 根据Code和RefreshCode获取AccessToken
     *
     * @param param 获取参数（包含code和refreshCode）
     * @return AccessToken响应
     */
    SecureTokenVo.AccessTokenResponse getAccessToken(SecureTokenParam.GetAccessTokenParam param);
    
    /**
     * 验证AccessToken是否有效
     * 
     * @param accessToken 访问令牌
     * @return 验证结果
     */
    SecureTokenVo.ValidateResponse validateAccessToken(String accessToken);
    
    /**
     * 刷新AccessToken
     * 
     * @param accessToken 当前访问令牌
     * @return 新的AccessToken响应
     */
    SecureTokenVo.AccessTokenResponse refreshAccessToken(String accessToken);
    
    /**
     * 撤销AccessToken
     * 
     * @param accessToken 访问令牌
     * @return 是否成功
     */
    Boolean revokeAccessToken(String accessToken);
    
    /**
     * 清理过期的Token
     * 
     * @return 清理数量
     */
    Long cleanExpiredTokens();
}
