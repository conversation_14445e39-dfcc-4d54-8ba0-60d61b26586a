//package com.haoys.mis.service;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.connection.stream.ObjectRecord;
//import org.springframework.data.redis.connection.stream.RecordId;
//import org.springframework.data.redis.connection.stream.StreamRecords;
//import org.springframework.data.redis.core.ReactiveRedisTemplate;
//import org.springframework.stereotype.Service;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//@Service
//public class SimpleMessageQueueService {
//
//    @Autowired
//    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;
//
//    public Mono<RecordId> publishMessage(String streamKey, String message) {
//        ObjectRecord<String, String> record = StreamRecords.newRecord()
//                .ofObject(message)
//                .withStreamKey(streamKey);
//        return reactiveRedisTemplate.opsForStream().add(record);
//    }
//
//    public Flux<ObjectRecord<String, String>> consumeMessage(String streamKey) {
//        return reactiveRedisTemplate.opsForStream().readAndAck(String.class, String.class, streamKey).retry();
//    }
//}