package com.haoys.user.elasticsearch;

import com.haoys.user.common.constants.CommomDataModelConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TreeSearchParam extends PatientRecordSearchParam {

    @ApiModelProperty(value = "数据库id")
    private String dataBaseId;

    @ApiModelProperty(value = "纳排搜索功能搜索id")
    private String searchId;

    @ApiModelProperty(value = "纳排条件")
    private SearchDto include;

    @ApiModelProperty(value = "排除条件")
    private SearchDto exclude;

    @ApiModelProperty(value = "视图类型")
    private String searchView= CommomDataModelConstants.PATIENT_VIEW;

    @ApiModelProperty(value = "导出的字段")
    private List<TreeExport> exportFields;


}



