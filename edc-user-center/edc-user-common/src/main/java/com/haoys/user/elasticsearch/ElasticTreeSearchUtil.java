package com.haoys.user.elasticsearch;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch.core.search.HighlightField;
import co.elastic.clients.json.JsonData;
import com.haoys.user.common.constants.DefineConstant;
import com.haoys.user.common.util.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ElasticTreeSearchUtil {
    /**
     * 构建高亮显示的字段
     * @param include
     * @return
     */
    public static Map<String, Object> buildHeightField(SearchDto include) {
        Map<String ,Object> map= new HashMap<>();
        // 构建高亮设置
        Map<String, HighlightField> filedMap = new HashMap<>();
        // 要显示的高亮的字段的名称
        Map<String, String> heightMap = new HashMap<>();

        if (StringUtils.isNotEmpty(include.getFieldCode())) {
            filedMap.put(StrUtil.toCamelCase(include.getFormCode()) + "." + StrUtil.toCamelCase(include.getFieldCode()), HighlightField.of(h -> h.preTags("<font color='red'>").postTags("</font>").numberOfFragments(3)
                    .fragmentSize(150)));
            heightMap.put(StrUtil.toCamelCase(include.getFormCode()) + "." + include.getFieldCode(), include.getFieldName());
        } else if (!CollectionUtils.isEmpty(include.getChildren())) {
            getHeightField(include, filedMap, heightMap);
        }
        map.put("fieldMap",filedMap);
        map.put("heightMap",filedMap);
        return map;
    }


    /**
     * 从搜索条件中获取高亮字段
     * @param dto
     * @param filedMap
     * @param heightMap
     */
    private static void getHeightField(SearchDto dto, Map<String, HighlightField> filedMap, Map<String, String> heightMap) {

        List<SearchDto> children = dto.getChildren();
        if (CollectionUtil.isNotEmpty(children)){
            children.forEach(child -> {
                if (SearchTypeEnum.AND.getCode().equals(child.getSearchType())){
                    if (StringUtils.isNotEmpty(child.getFieldCode())){
                        String filed = StrUtil.toCamelCase(child.getFormCode()) + "." + child.getFieldCode();
                        filedMap.put(filed, HighlightField.of(h -> h.preTags("<font color='red'>").postTags("</font>").numberOfFragments(3)
                                .fragmentSize(150)));
                        heightMap.put(filed, child.getFieldName());
                    }else if (!CollectionUtils.isEmpty(child.getChildren())){
                        getHeightField(child,filedMap, heightMap);
                    }
                }
            });
        }
    }

    /**
     * 根据字段类型构建查询条件
     * @param child
     * @param searchValues
     * @param searchQuery
     * @param filed
     */

    public static void buildQuery(SearchDto child, List<String> searchValues, BoolQuery.Builder searchQuery, String filed) {
        if ("varchar".equals(child.getVariableType()) || "text".equals(child.getVariableType())) {
            // 字符串
            buildString(child, searchValues, searchQuery, filed);
        } else if (child.getVariableType().contains("int") || child.getVariableType().contains("float")) {
            // 数字类型
            buildNumber(child, searchValues, searchQuery, filed);
        } else if (child.getVariableType().contains("timestamp") || child.getVariableType().contains("date") || child.getVariableType().contains("datetime")) {
            // 日期类型
            buildDate(child, searchValues, searchQuery, filed);
        } else if (child.getVariableType().contains("bool")) {
            // bool类型
            buildBool(child, searchValues, searchQuery, filed);
        }
    }

    /**
     * bool类型
     *
     * @param searchValues
     * @param searchQuery
     * @param filed
     */
    public static void buildBool(SearchDto child, List<String> searchValues, BoolQuery.Builder searchQuery, String filed) {

        boolean isBool;
        if ("是".equals(searchValues.get(0)) || "true".equals(searchValues.get(0))) {
            isBool = true;
        } else {
            isBool = false;
        }

        if (DefineConstant.EMPTY.equals(child.getValueType())) {
            searchQuery.must(qy -> qy.exists(r -> r.field(filed)));
        } else if (DefineConstant.NOT_EMPTY.equals(child.getValueType())) {
            searchQuery.mustNot(qy -> qy.exists(r -> r.field(filed)));
        } else if (DefineConstant.EQUAL_CODE.equals(child.getValueType())) {
            // 等于搜索条件构建
            searchQuery.must(qy -> qy.term(r -> r.field(filed).value(isBool)));
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(child.getValueType())) {
            // 不等于搜索条件构建
            searchQuery.mustNot(qy -> qy.term(r -> r.field(filed).value(isBool)));
        }
    }


    /**
     * 日期类型
     *
     * @param child
     * @param searchValues
     * @param searchQuery
     * @param filed
     */
    public static void buildDate(SearchDto child, List<String> searchValues, BoolQuery.Builder searchQuery, String filed) {
        if (DefineConstant.GREATER_THAN_CODE.equals(child.getValueType())) {
            //  大于搜索条件构建
            RangeQuery.Builder query = new RangeQuery.Builder();
            query.field(filed).gt(JsonData.of(searchValues.get(0)));
            query.format(formatDateType(child.getFormatType()));
            searchQuery.must(qy -> qy.range(query.build()));

        } else if (DefineConstant.LESS_THAN_CODE.equals(child.getValueType())) {
            // 小于搜索条件构建

            RangeQuery.Builder query = new RangeQuery.Builder();
            query.field(filed).lt(JsonData.of(searchValues.get(0)));
            query.format(formatDateType(child.getFormatType()));
            searchQuery.must(qy -> qy.range(query.build()));

        } else if (DefineConstant.EQUAL_CODE.equals(child.getValueType())) {
            // 等于搜索条件构建
            for (String searchValue : searchValues) {
                child.setSearchValue(searchValue);
                Query query = eqdate(child, filed);
                searchQuery.must(query);
            }
        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(child.getValueType())) {
            // 大于等于搜索条件构建

            RangeQuery.Builder query = new RangeQuery.Builder();
            query.field(filed).gte(JsonData.of(searchValues.get(0)));
            query.format(formatDateType(child.getFormatType()));
            searchQuery.must(qy -> qy.range(query.build()));


        } else if (DefineConstant.LESS_THAN_EQUAL_CODE.equals(child.getValueType())) {
            // 小于等于条件构建
            RangeQuery.Builder query = new RangeQuery.Builder();
            query.field(filed).lte(JsonData.of(searchValues.get(0)));
            query.format(formatDateType(child.getFormatType()));
            searchQuery.must(qy -> qy.range(query.build()));

        } else if (DefineConstant.BETWEEN.equals(child.getValueType())) {
            // 范围区间搜索条件构建
            RangeQuery.Builder query1 = new RangeQuery.Builder();
            query1.field(filed).gte(JsonData.of(searchValues.get(0))).lte(JsonData.of(searchValues.get(1)));
            query1.format(formatDateType(child.getFormatType()));
            searchQuery.must(qy -> qy.range(query1.build()));

        } else if (DefineConstant.EMPTY.equals(child.getValueType())) {
            // 空值搜索条件构建
            searchQuery.mustNot(qy -> qy.wildcard(w -> w.field(filed).wildcard("**")));
        } else if (DefineConstant.NOT_EMPTY.equals(child.getValueType())) {
            // 非空搜索条件构建
            searchQuery.must(qy -> qy.wildcard(w -> w.field(filed).wildcard("**")));
        }
    }

    /**
     * 数字类型
     *
     * @param child
     * @param searchValues
     * @param searchQuery
     * @param filed
     */
    public static void buildNumber(SearchDto child, List<String> searchValues, BoolQuery.Builder searchQuery, String filed) {
        if (DefineConstant.GREATER_THAN_CODE.equals(child.getValueType())) {
            //  大于搜索条件构建
            searchQuery.must(qy -> qy.range(r -> r.field(filed).gt(JsonData.of(searchValues.get(0)))));

        } else if (DefineConstant.LESS_THAN_CODE.equals(child.getValueType())) {
            // 小于搜索条件构建
            searchQuery.must(qy -> qy.range(r -> r.field(filed).lt(JsonData.of(searchValues.get(0)))));

        } else if (DefineConstant.EQUAL_CODE.equals(child.getValueType())) {
            // 等于搜索条件构建
            for (String searchValue : searchValues) {
                searchQuery.must(qy -> qy.term(r -> r.field(filed).value(searchValue)));
            }
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(child.getValueType())) {
            // 大于等于搜索条件构建
            searchQuery.must(qy -> qy.range(r -> r.field(filed).gte(JsonData.of(searchValues.get(0)))));

        } else if (DefineConstant.GREATER_THAN_EQUAL_CODE.equals(child.getValueType())) {
            // 小于等于条件构建
            searchQuery.must(qy -> qy.range(r -> r.field(filed).lte(JsonData.of(searchValues.get(0)))));

        } else if (DefineConstant.BETWEEN.equals(child.getValueType())) {
            // 范围区间搜索条件构建
            searchQuery.must(qy -> qy.range(r -> r.field(filed).gte(JsonData.of(searchValues.get(0)))));
            searchQuery.must(qy -> qy.range(r -> r.field(filed).lte(JsonData.of(searchValues.get(1)))));

        } else if (DefineConstant.EMPTY.equals(child.getValueType())) {
            // 空值搜索条件构建
            searchQuery.mustNot(qy -> qy.wildcard(w -> w.field(filed).wildcard("**")));
        } else if (DefineConstant.NOT_EMPTY.equals(child.getValueType())) {
            // 非空搜索条件构建
            searchQuery.must(qy -> qy.wildcard(w -> w.field(filed).wildcard("**")));
        }
    }

    /**
     * 字符串类型
     *
     * @param child
     * @param searchValues
     * @param searchQuery
     * @param filed
     */
    public static void buildString(SearchDto child, List<String> searchValues, BoolQuery.Builder searchQuery, String filed) {
        String filedValue = filed;

        if (DefineConstant.EQUAL_CODE.equals(child.getValueType())) {
            // 等于搜索条件构建
            for (String searchValue : searchValues) {
                searchQuery.should(qy -> qy.wildcard(w -> w.field(filedValue).wildcard(searchValue)));
            }
        } else if (DefineConstant.NOT_EQUAL_CODE.equals(child.getValueType())) {
            // 不等于搜索条件构建
            for (String searchValue : searchValues) {
                searchQuery.mustNot(qy -> qy.wildcard(w -> w.field(filedValue).wildcard(searchValue)));
            }
        } else if (DefineConstant.CONTAINS_CODE.equals(child.getValueType())) {
            // 包含搜索条件构建
            for (String searchValue : searchValues) {
                searchQuery.should(qy -> qy.match(w -> w.field(filedValue).query(searchValue)));
            }
        } else if (DefineConstant.NOT_CONTAINS_CODE.equals(child.getValueType())) {
            // 不包含搜索条件构建
            for (String searchValue : searchValues) {
                searchQuery.mustNot(qy -> qy.match(w -> w.field(filedValue).query(searchValue)));
            }
        } else if (DefineConstant.EMPTY.equals(child.getValueType())) {
            // 空值搜索条件构建
            searchQuery.mustNot(qy -> qy.wildcard(w -> w.field(filedValue).wildcard("**")));
        } else if (DefineConstant.NOT_EMPTY.equals(child.getValueType())) {
            // 非空搜索条件构建
            searchQuery.must(qy -> qy.wildcard(w -> w.field(filedValue).wildcard("**")));
        }
    }


    /**
     * 针对日期类型的搜索条件进行处理
     *
     * @param searchDto
     * @param filed
     */
    public static Query eqdate(SearchDto searchDto, String filed) {
        RangeQuery.Builder rangeQuery = new RangeQuery.Builder();
        String formatType = searchDto.getFormatType();
        if (StrUtil.isNotBlank(formatType)) {
            if (formatType.contains("d")) {
                // 获取某一天
                rangeQuery.field(filed).gte(JsonData.of(searchDto.getSearchValue())).lte(JsonData.of(searchDto.getSearchValue()));
            } else if (formatType.contains("m")) {
                // 获取当月的第一天
                String startDate = DateUtil.format(DateUtil.beginOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");
                // 获取当月的最后一天
                String endDate = DateUtil.format(DateUtil.endOfMonth(DateUtil.parse(searchDto.getSearchValue() + "-01")), "yyyy-MM-dd");

                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            } else if (formatType.contains("Y")) {
                // 获取当年的第一天
                String startDate = DateUtil.format(DateUtil.beginOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                // 获取当年的最后一天
                String endDate = DateUtil.format(DateUtil.endOfYear(DateUtil.parse(searchDto.getSearchValue() + "-01-01")), "yyyy-MM-dd");
                rangeQuery.field(filed).gte(JsonData.of(startDate)).lte(JsonData.of(endDate));

            }
        }
        rangeQuery.format("yyyy-MM-dd");
        return Query.of(q -> q.range(rangeQuery.build()));
    }

    /**
     * 格式化日期类型
     *
     * @param formatType
     * @return
     */
    public static String formatDateType(String formatType) {
        if (StrUtil.isNotBlank(formatType)) {
            if (formatType.indexOf("d") > 0) {
                return "yyyy-MM-dd";
            } else if (formatType.indexOf("m") > 0) {
                return "yyyy-MM";
            } else if (formatType.indexOf("Y") > 0) {
                return "yyyy";
            }
        }
        return "";
    }


}
