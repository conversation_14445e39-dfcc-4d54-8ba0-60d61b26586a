package com.haoys.user.service;

import java.util.Map;

/**
 * 业务流程管理服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface BusinessProcessService {
    
    /**
     * 初始化业务流程
     * 
     * @return 初始化结果
     */
    Map<String, Object> initializeBusinessProcess();
    
    /**
     * 创建业务表和相关流程
     * 
     * @param businessType 业务类型
     * @return 创建结果
     */
    Map<String, Object> createBusinessTableAndProcess(String businessType);
    
    /**
     * 检查业务流程完整性
     * 
     * @return 检查结果
     */
    Map<String, Object> checkBusinessProcessIntegrity();
    
    /**
     * 修复业务流程
     * 
     * @return 修复结果
     */
    Map<String, Object> repairBusinessProcess();
    
    /**
     * 获取业务流程状态
     * 
     * @return 状态信息
     */
    Map<String, Object> getBusinessProcessStatus();
}
