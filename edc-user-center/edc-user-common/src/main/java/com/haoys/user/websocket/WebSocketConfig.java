package com.haoys.user.websocket;

import com.haoys.user.storge.cloud.OssStorageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;


import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket配置类
 *
 * <p>配置WebSocket服务端点，支持STOMP协议和原生WebSocket</p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-06-25
 */
@Slf4j
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Autowired
    private SimpMessagingTemplate simpMessagingTemplate;

    @Autowired
    private OssStorageConfig ossStorageConfig;

    @Value("${websocket.log.enabled:false}")
    private boolean logEnabled;

    @Value("${websocket.log.online-output-enabled:true}")
    private boolean onlineOutputEnabled;

    @Value("${websocket.log.print-enabled:true}")
    private boolean printEnabled;

    private ExecutorService executorService;
    /**
     * 注册STOMP端点
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/websocket")
                .setAllowedOrigins("*")
                .addInterceptors()
                .withSockJS();

        // 添加新的日志端点
        registry.addEndpoint("/websocket/logs")
                .setAllowedOrigins("*")
                .withSockJS();
    }

    /**
     * 配置消息代理
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        registry.enableSimpleBroker("/queue", "/topic");
        registry.setApplicationDestinationPrefixes("/app");
    }





    /**
     * 推送日志到STOMP主题
     */
    @PostConstruct
    public void pushLogger() {
        // 检查是否启用打印功能
        if (!printEnabled) {
            if (logEnabled) {
                log.info("WebSocket日志打印功能已禁用，跳过STOMP日志推送服务启动");
            }
            return;
        }

        if (logEnabled) {
            log.info("正在启动WebSocket STOMP日志推送服务...");
        }

        // 创建线程池（优化：减少线程数，添加异常处理）
        executorService = Executors.newFixedThreadPool(2, r -> {
            Thread thread = new Thread(r, "websocket-stomp-push");
            thread.setDaemon(true);
            thread.setUncaughtExceptionHandler((t, e) -> {
                if (logEnabled) {
                    log.error("WebSocket STOMP推送线程异常: {}", t.getName(), e);
                }
            });
            return thread;
        });

        // 进程内日志推送任务
        Runnable processLogTask = () -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    LoggerMessage logMessage = LoggerQueue.getInstance().poll();
                    if (logMessage != null && simpMessagingTemplate != null) {
                        simpMessagingTemplate.convertAndSend("/topic/pullLogger", logMessage);
                    }
                } catch (Exception e) {
                    if (logEnabled) {
                        log.error("推送进程内日志失败", e);
                    }
                }
            }
        };

        // 文件日志推送任务
        Runnable fileLogTask = () -> {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    LoggerMessage logMessage = LoggerQueue.getInstance().pollFileLog();
                    if (logMessage != null && simpMessagingTemplate != null) {
                        // 兼容旧版本，发送字符串格式
                        String logContent = logMessage.getBody() != null ? logMessage.getBody() : logMessage.getFormattedMessage();
                        simpMessagingTemplate.convertAndSend("/topic/pullFileLogger", logContent);

                        // 新版本，发送完整的LoggerMessage对象
                        simpMessagingTemplate.convertAndSend("/topic/pullFileLoggerV2", logMessage);
                    }
                } catch (Exception e) {
                    log.error("推送文件日志失败", e);
                }
            }
        };

        // 启动任务（优化：移除重复任务，使用定时任务）
        executorService.submit(fileLogTask);
        executorService.submit(processLogTask);

        log.info("WebSocket STOMP日志推送服务已启动，文件日志任务和进程日志任务各启动1个实例");
    }

    /**
     * 销毁时清理资源
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("WebSocket STOMP日志推送服务已停止");
        }
    }
}