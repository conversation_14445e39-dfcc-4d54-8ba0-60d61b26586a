package com.haoys.user.mongodb;

import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoCollection;
import org.bson.Document;

public class MongoDBUtil {
    
    private static MongoClient mongoClient;
    private static final String DEFAULT_CONNECTION_STRING = "mongodb://localhost:27017";
    
    // 私有构造函数，防止外部实例化
    private MongoDBUtil() {}
    
    // 获取MongoDB连接实例
    private static MongoClient getClient() {
        if (mongoClient == null) {
            synchronized (MongoDBUtil.class) {
                if (mongoClient == null) {
                    mongoClient = MongoClients.create(DEFAULT_CONNECTION_STRING);
                }
            }
        }
        return mongoClient;
    }
    
    // 连接MongoDB数据库
    public static void connect() {
        getClient(); // 调用getClient()以确保连接已经初始化
    }
    
    // 获取数据库
    public static MongoDatabase getDatabase(String dbName) {
        connect(); // 确保连接已经初始化
        return mongoClient.getDatabase(dbName);
    }
    
    // 获取集合
    public static MongoCollection<Document> getCollection(String dbName, String collectionName) {
        MongoDatabase database = getDatabase(dbName);
        return database.getCollection(collectionName);
    }
    
    // 关闭连接
    public static void close() {
        if (mongoClient != null) {
            mongoClient.close();
        }
    }
}

