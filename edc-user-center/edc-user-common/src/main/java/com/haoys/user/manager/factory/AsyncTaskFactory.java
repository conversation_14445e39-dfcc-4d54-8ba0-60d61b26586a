package com.haoys.user.manager.factory;

import com.haoys.user.common.ip.RequestIpUtils;
import com.haoys.user.common.spring.SpringUtils;
import com.haoys.user.common.util.ServletUtils;
import com.haoys.user.model.SystemRequestRecord;
import eu.bitwalker.useragentutils.UserAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.TimerTask;

/**
 * 异步任务工厂类
 * 用于创建各种异步任务
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
public class AsyncTaskFactory {
    
    private static final Logger log = LoggerFactory.getLogger(AsyncTaskFactory.class);

    /**
     * 保存系统请求日志（原有方法）
     *
     * @param operLog 操作日志
     * @return TimerTask
     */
    public static TimerTask saveSystemRequestLog(final Object operLog) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    // 使用反射调用，避免编译时依赖
                    Object systemRequestLogService = SpringUtils.getBean("systemRequestLogService");
                    systemRequestLogService.getClass().getMethod("insertOperlog", Object.class).invoke(systemRequestLogService, operLog);
                } catch (Exception e) {
                    log.error("保存系统请求日志失败", e);
                }
            }
        };
    }

    /**
     * 保存系统访问记录（新增方法）
     *
     * @param record 访问记录
     * @return TimerTask
     */
    public static TimerTask saveSystemRequestRecord(final SystemRequestRecord record) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (record == null) {
                        log.warn("系统访问记录为空，跳过保存");
                        return;
                    }

                    // 使用反射调用，避免编译时依赖
                    Object systemRequestRecordService = SpringUtils.getBean("systemRequestRecordService");
                    Object result = systemRequestRecordService.getClass().getMethod("saveRecord", SystemRequestRecord.class).invoke(systemRequestRecordService, record);
                    if (result instanceof Boolean && !(Boolean) result) {
                        log.warn("保存系统访问记录失败: traceId={}", record.getTraceId());
                    }
                } catch (Exception e) {
                    log.error("异步保存系统访问记录失败: record={}", record, e);
                }
            }
        };
    }
    
    /**
     * 批量保存系统访问记录
     *
     * @param records 访问记录列表
     * @return TimerTask
     */
    public static TimerTask batchSaveSystemRequestRecords(final java.util.List<?> records) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (records == null || records.isEmpty()) {
                        log.warn("系统访问记录列表为空，跳过批量保存");
                        return;
                    }

                    // 使用反射调用，避免编译时依赖
                    Object recordService = SpringUtils.getBean("systemRequestRecordService");
                    if (recordService != null) {
                        Object result = recordService.getClass().getMethod("batchSaveRecords", java.util.List.class).invoke(recordService, records);
                        if (result instanceof Boolean && !(Boolean) result) {
                            log.warn("批量保存系统访问记录失败，记录数: {}", records.size());
                        } else {
                            log.debug("批量保存系统访问记录成功，记录数: {}", records.size());
                        }
                    } else {
                        log.error("无法获取 SystemRequestRecordService 实例");
                    }
                } catch (Exception e) {
                    log.error("异步批量保存系统访问记录失败，记录数: {}",
                             records != null ? records.size() : 0, e);
                }
            }
        };
    }
    
    /**
     * 记录系统登录日志信息
     * 
     * @param request HTTP请求
     * @param username 用户名
     * @param status 状态
     * @param message 消息
     * @param operateType 操作类型
     * @param args 其他参数
     * @return TimerTask
     */
    public static TimerTask recordSystemloginLogInfo(HttpServletRequest request, final String username, 
                                                    final String status, final String message, 
                                                    final String operateType, final Object... args) {
        UserAgent userAgentOld;
        String userAgentValue = "";
        
        if (request == null) {
            try {
                userAgentValue = ServletUtils.getRequest().getHeader("User-Agent");
                userAgentOld = UserAgent.parseUserAgentString(userAgentValue);
                request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            } catch (Exception e) {
                log.warn("获取请求信息失败", e);
                userAgentOld = UserAgent.parseUserAgentString("");
            }
        } else {
            userAgentValue = request.getHeader("User-Agent");
            userAgentOld = UserAgent.parseUserAgentString(userAgentValue);
        }
        
        String ipAddress = RequestIpUtils.getIpAddress(request);
        final UserAgent userAgent = userAgentOld;
        String finalUserAgentValue = userAgentValue;
        
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    // 这里可以实现登录日志记录逻辑
                    log.info("记录登录日志: username={}, status={}, ip={}", username, status, ipAddress);
                } catch (Exception e) {
                    log.error("记录登录日志失败", e);
                }
            }
        };
    }
    
    /**
     * 清理过期的系统访问记录
     *
     * @param retentionDays 保留天数
     * @return TimerTask
     */
    public static TimerTask cleanupExpiredSystemRequestRecords(final int retentionDays) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object systemRequestRecordService = SpringUtils.getBean("systemRequestRecordService");
                    Object result = systemRequestRecordService.getClass().getMethod("deleteExpiredRecords", int.class).invoke(systemRequestRecordService, retentionDays);
                    if (result instanceof Integer) {
                        log.info("清理过期系统访问记录完成，删除记录数: {}, 保留天数: {}", result, retentionDays);
                    }
                } catch (Exception e) {
                    log.error("清理过期系统访问记录失败", e);
                }
            }
        };
    }
    
    /**
     * 优化系统访问记录表
     *
     * @return TimerTask
     */
    public static TimerTask optimizeSystemRequestRecordTable() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object systemRequestRecordService = SpringUtils.getBean("systemRequestRecordService");
                    systemRequestRecordService.getClass().getMethod("optimizeTable").invoke(systemRequestRecordService);
                    log.info("优化系统访问记录表完成");
                } catch (Exception e) {
                    log.error("优化系统访问记录表失败", e);
                }
            }
        };
    }
    
    /**
     * 生成系统监控报告
     * @return TimerTask
     */
    public static TimerTask generateSystemMonitorReport() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object systemRequestRecordService = SpringUtils.getBean("systemRequestRecordService");
                    if (systemRequestRecordService != null) {
                        Object monitorData = systemRequestRecordService.getClass().getMethod("getSystemMonitorData").invoke(systemRequestRecordService);
                        if (monitorData instanceof java.util.Map) {
                            log.info("生成系统监控报告完成，数据项数: {}", ((java.util.Map<?, ?>) monitorData).size());
                        }

                        // 这里可以添加报告发送逻辑，比如发送邮件或推送到监控系统

                    }
                } catch (Exception e) {
                    log.error("生成系统监控报告失败", e);
                }
            }
        };
    }
    
    /**
     * 检查系统健康状态
     * @return TimerTask
     */
    public static TimerTask checkSystemHealth() {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    Object systemRequestRecordService = SpringUtils.getBean("systemRequestRecordService");
                    Object realTimeStats = systemRequestRecordService.getClass().getMethod("getRealTimeStatistics").invoke(systemRequestRecordService);
                    
                    if (realTimeStats instanceof java.util.Map) {
                        java.util.Map<?, ?> statsMap = (java.util.Map<?, ?>) realTimeStats;

                        // 检查错误率
                        Object errorRequests = statsMap.get("errorRequests");
                        Object totalRequests = statsMap.get("totalRequests");

                        if (errorRequests instanceof Number && totalRequests instanceof Number) {
                            long errors = ((Number) errorRequests).longValue();
                            long total = ((Number) totalRequests).longValue();

                            if (total > 0) {
                                double errorRate = (double) errors / total * 100;
                                if (errorRate > 10) { // 错误率超过10%
                                    log.warn("系统错误率过高: {}%, 错误数: {}, 总请求数: {}",
                                            String.format("%.2f", errorRate), errors, total);
                                }
                            }
                        }

                        log.debug("系统健康检查完成: {}", realTimeStats);
                    }
                } catch (Exception e) {
                    log.error("系统健康检查失败", e);
                }
            }
        };
    }
    
    /**
     * 处理慢请求告警
     *
     * @param record 慢请求记录
     * @return TimerTask
     */
    public static TimerTask handleSlowRequestAlert(final Object record) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (record != null) {
                        // 使用反射获取属性值
                        Object responseTime = record.getClass().getMethod("getResponseTime").invoke(record);
                        if (responseTime != null) {
                            Object requestUrl = record.getClass().getMethod("getRequestUrl").invoke(record);
                            Object methodName = record.getClass().getMethod("getMethodName").invoke(record);
                            Object userName = record.getClass().getMethod("getUserName").invoke(record);
                            Object requestIp = record.getClass().getMethod("getRequestIp").invoke(record);

                            log.warn("慢请求告警: URL={}, 方法={}, 响应时间={}ms, 用户={}, IP={}",
                                    requestUrl, methodName, responseTime, userName, requestIp);

                            // 这里可以添加告警通知逻辑，比如发送邮件、短信或推送到监控系统
                        }
                    }
                } catch (Exception e) {
                    log.error("处理慢请求告警失败", e);
                }
            }
        };
    }

    /**
     * 处理异常请求告警
     *
     * @param record 异常请求记录
     * @return TimerTask
     */
    public static TimerTask handleErrorRequestAlert(final Object record) {
        return new TimerTask() {
            @Override
            public void run() {
                try {
                    if (record != null) {
                        // 使用反射获取属性值
                        Object isSuccess = record.getClass().getMethod("getIsSuccess").invoke(record);
                        if (isSuccess instanceof Boolean && !(Boolean) isSuccess) {
                            Object requestUrl = record.getClass().getMethod("getRequestUrl").invoke(record);
                            Object methodName = record.getClass().getMethod("getMethodName").invoke(record);
                            Object errorMessage = record.getClass().getMethod("getErrorMessage").invoke(record);
                            Object userName = record.getClass().getMethod("getUserName").invoke(record);
                            Object requestIp = record.getClass().getMethod("getRequestIp").invoke(record);

                            log.error("异常请求告警: URL={}, 方法={}, 错误信息={}, 用户={}, IP={}",
                                    requestUrl, methodName, errorMessage, userName, requestIp);

                            // 这里可以添加告警通知逻辑
                        }
                    }
                } catch (Exception e) {
                    log.error("处理异常请求告警失败", e);
                }
            }
        };
    }
}
