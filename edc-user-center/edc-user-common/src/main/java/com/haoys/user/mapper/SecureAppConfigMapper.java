package com.haoys.user.mapper;

import com.haoys.user.model.SecureAppConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 安全应用配置Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Mapper
public interface SecureAppConfigMapper {
    
    /**
     * 根据主键查询
     */
    SecureAppConfig selectByPrimaryKey(Long id);
    
    /**
     * 根据AppId和环境查询
     */
    SecureAppConfig selectByAppIdAndEnvironment(@Param("appId") String appId, @Param("environment") String environment);
    
    /**
     * 根据环境查询所有启用的应用
     */
    List<SecureAppConfig> selectByEnvironment(@Param("environment") String environment);
    
    /**
     * 查询所有启用的应用
     */
    List<SecureAppConfig> selectAllEnabled();
    
    /**
     * 插入记录
     */
    int insert(SecureAppConfig record);
    
    /**
     * 选择性插入记录
     */
    int insertSelective(SecureAppConfig record);
    
    /**
     * 根据主键选择性更新
     */
    int updateByPrimaryKeySelective(SecureAppConfig record);
    
    /**
     * 根据主键更新
     */
    int updateByPrimaryKey(SecureAppConfig record);
    
    /**
     * 根据主键删除
     */
    int deleteByPrimaryKey(Long id);
    
    /**
     * 根据AppId和环境删除
     */
    int deleteByAppIdAndEnvironment(@Param("appId") String appId, @Param("environment") String environment);
    
    /**
     * 验证AppId和AppSecret
     */
    SecureAppConfig validateAppCredentials(@Param("appId") String appId, @Param("appSecret") String appSecret, @Param("environment") String environment);
    
    /**
     * 统计今日请求次数
     */
    int countTodayRequests(@Param("appId") String appId, @Param("environment") String environment);
}
