package com.haoys.user.common.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;

/**
 * SQL性能监控拦截器
 * 监控SQL查询性能，记录慢查询和异常SQL
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class SqlPerformanceInterceptor implements Interceptor {

    /**
     * SQL性能阈值配置（毫秒）
     */
    private static final long SLOW_SQL_THRESHOLD = 500L; // 0.5秒
    private static final long VERY_SLOW_SQL_THRESHOLD = 2000L; // 2秒
    
    /**
     * SQL统计信息缓存
     */
    private final ConcurrentHashMap<String, SqlStats> sqlStatsMap = new ConcurrentHashMap<>();
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        
        String sqlId = mappedStatement.getId();
        String sqlType = mappedStatement.getSqlCommandType().name();
        
        try {
            Object result = invocation.proceed();
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 获取实际执行的SQL
            String actualSql = getActualSql(mappedStatement, parameter);
            
            // 记录SQL执行性能
            logSqlPerformance(sqlId, sqlType, actualSql, duration, true, null);
            updateSqlStats(sqlId, duration, true);
            
            return result;
        } catch (Throwable throwable) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 获取实际执行的SQL
            String actualSql = getActualSql(mappedStatement, parameter);
            
            // 记录异常情况下的SQL执行信息
            logSqlPerformance(sqlId, sqlType, actualSql, duration, false, throwable);
            updateSqlStats(sqlId, duration, false);
            
            throw throwable;
        }
    }
    
    /**
     * 获取实际执行的SQL语句
     */
    private String getActualSql(MappedStatement mappedStatement, Object parameter) {
        try {
            Configuration configuration = mappedStatement.getConfiguration();
            BoundSql boundSql = mappedStatement.getBoundSql(parameter);
            String sql = boundSql.getSql();
            
            if (parameter == null) {
                return sql.replaceAll("\\s+", " ").trim();
            }
            
            // 替换参数
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            
            if (parameterMappings != null) {
                for (ParameterMapping parameterMapping : parameterMappings) {
                    if (parameter != null) {
                        String propertyName = parameterMapping.getProperty();
                        Object value;
                        
                        if (boundSql.hasAdditionalParameter(propertyName)) {
                            value = boundSql.getAdditionalParameter(propertyName);
                        } else if (parameter != null && typeHandlerRegistry.hasTypeHandler(parameter.getClass())) {
                            value = parameter;
                        } else {
                            MetaObject metaObject = configuration.newMetaObject(parameter);
                            value = metaObject.getValue(propertyName);
                        }
                        
                        String parameterValue = getParameterValue(value);
                        sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(parameterValue));
                    }
                }
            }
            
            return sql.replaceAll("\\s+", " ").trim();
        } catch (Exception e) {
            log.debug("获取实际SQL失败: {}", e.getMessage());
            return "SQL_PARSE_ERROR";
        }
    }
    
    /**
     * 获取参数值的字符串表示
     */
    private String getParameterValue(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        if (obj instanceof String) {
            return "'" + obj + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            return "'" + formatter.format((Date) obj) + "'";
        } else {
            return obj.toString();
        }
    }
    
    /**
     * 记录SQL性能日志
     */
    private void logSqlPerformance(String sqlId, String sqlType, String actualSql, long duration, boolean success, Throwable throwable) {
        String status = success ? "SUCCESS" : "ERROR";
        
        // 截断过长的SQL用于日志显示
        String displaySql = actualSql.length() > 200 ? actualSql.substring(0, 200) + "..." : actualSql;
        
        if (duration > VERY_SLOW_SQL_THRESHOLD) {
            log.error("VERY_SLOW_SQL - SQL_ID: {} | 类型: {} | 耗时: {}ms | 状态: {} | 阈值: {}ms | SQL: {}", 
                    sqlId, sqlType, duration, status, VERY_SLOW_SQL_THRESHOLD, displaySql);
            if (throwable != null) {
                log.error("SQL执行异常: ", throwable);
            }
        } else if (duration > SLOW_SQL_THRESHOLD) {
            log.warn("SLOW_SQL - SQL_ID: {} | 类型: {} | 耗时: {}ms | 状态: {} | 阈值: {}ms | SQL: {}", 
                    sqlId, sqlType, duration, status, SLOW_SQL_THRESHOLD, displaySql);
            if (throwable != null) {
                log.warn("SQL执行异常: {}", throwable.getMessage());
            }
        } else {
            log.debug("SQL_EXECUTION - SQL_ID: {} | 类型: {} | 耗时: {}ms | 状态: {} | SQL: {}", 
                    sqlId, sqlType, duration, status, displaySql);
            if (throwable != null) {
                log.debug("SQL执行异常: {}", throwable.getMessage());
            }
        }
    }
    
    /**
     * 更新SQL统计信息
     */
    private void updateSqlStats(String sqlId, long duration, boolean success) {
        sqlStatsMap.computeIfAbsent(sqlId, k -> new SqlStats())
                  .updateStats(duration, success);
    }
    
    /**
     * 获取SQL性能统计信息
     */
    public ConcurrentHashMap<String, SqlStats> getSqlPerformanceStats() {
        return new ConcurrentHashMap<>(sqlStatsMap);
    }
    
    /**
     * 清空SQL统计信息
     */
    public void clearSqlStats() {
        sqlStatsMap.clear();
        log.info("SQL性能统计信息已清空");
    }
    
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以从配置文件中读取阈值配置
    }
    
    /**
     * SQL统计信息内部类
     */
    public static class SqlStats {
        private final AtomicLong totalExecutions = new AtomicLong(0);
        private final AtomicLong successExecutions = new AtomicLong(0);
        private final AtomicLong errorExecutions = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private volatile long maxDuration = 0;
        private volatile long minDuration = Long.MAX_VALUE;
        private final AtomicLong slowQueryCount = new AtomicLong(0);
        
        public void updateStats(long duration, boolean success) {
            totalExecutions.incrementAndGet();
            totalDuration.addAndGet(duration);
            
            if (success) {
                successExecutions.incrementAndGet();
            } else {
                errorExecutions.incrementAndGet();
            }
            
            if (duration > SLOW_SQL_THRESHOLD) {
                slowQueryCount.incrementAndGet();
            }
            
            // 更新最大最小耗时
            synchronized (this) {
                if (duration > maxDuration) {
                    maxDuration = duration;
                }
                if (duration < minDuration) {
                    minDuration = duration;
                }
            }
        }
        
        public long getTotalExecutions() { return totalExecutions.get(); }
        public long getSuccessExecutions() { return successExecutions.get(); }
        public long getErrorExecutions() { return errorExecutions.get(); }
        public long getSlowQueryCount() { return slowQueryCount.get(); }
        public long getAverageDuration() { 
            long executions = totalExecutions.get();
            return executions > 0 ? totalDuration.get() / executions : 0;
        }
        public long getMaxDuration() { return maxDuration; }
        public long getMinDuration() { return minDuration == Long.MAX_VALUE ? 0 : minDuration; }
        public double getSuccessRate() {
            long total = totalExecutions.get();
            return total > 0 ? (double) successExecutions.get() / total * 100 : 0.0;
        }
        public double getSlowQueryRate() {
            long total = totalExecutions.get();
            return total > 0 ? (double) slowQueryCount.get() / total * 100 : 0.0;
        }
    }
}
