package com.haoys.user.participle;

import com.alibaba.fastjson.JSON;
import com.huaban.analysis.jieba.JiebaSegmenter;
import com.huaban.analysis.jieba.SegToken;
import com.huaban.analysis.jieba.WordDictionary;
//import org.wltea.analyzer.core.IKSegmenter;
//import org.wltea.analyzer.core.Lexeme;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.Iterator;
import java.util.List;

/**
 * IK分词器工具类
 */
public class IKUtil {

    /**
     * 分词
     * @param content
     * @param spiltChar
     * @return
     */
    /*public static String spilt(String content,String spiltChar) throws IOException {
        StringReader stringReader=new StringReader(content);
        IKSegmenter ikSegmenter=new IKSegmenter(stringReader,true);

        Lexeme lexeme = null;
        StringBuilder stringBuilder=new StringBuilder("");
        while( (lexeme = ikSegmenter.next())!=null ){
            stringBuilder.append(lexeme.getLexemeText()+spiltChar);
        }
        return stringBuilder.toString();
    }*/


    /**
     * 结巴分词器
     * @param text
     * @throws IOException
     */
    public static String JieBa(String text) throws IOException{
        //WordDictionary wordDictionary = WordDictionary.getInstance(); // 获取词库

        // 加载自定义的词典

        File directory = new File("haoys-mis-admin/src/main/resources");
        String base = directory.getCanonicalPath();
        Path path = FileSystems.getDefault().getPath(base + "/config", "dict.txt");
        WordDictionary.getInstance().loadUserDict(path);

        JiebaSegmenter jiebaSegmenter = new JiebaSegmenter();

//        List<String> dataList = jiebaSegmenter.sentenceProcess(text);
//        Iterator<String> iterator = dataList.iterator();
//        StringBuffer buffer = new StringBuffer();   // 返回数据
//        while (iterator.hasNext()){
//            buffer.append(iterator.next()+",");
//        }

        List<SegToken> segTokens = jiebaSegmenter.process(text, JiebaSegmenter.SegMode.INDEX); // 分词处理
        Iterator<SegToken> iterator = segTokens.iterator(); //迭代器
        SegToken segToken;
        StringBuffer buffer = new StringBuffer();   // 返回数据
        while (iterator.hasNext()){
            segToken = iterator.next();
            buffer.append(segToken.word+",");
        }
        return buffer.toString();
    }



    public static void main(String[] args) throws IOException {
        String content = "1. 机械煎药2. 路路通(克)×10克   藿香(克)×10克   砂仁(克)×6克枣仁(克)×15克   远志(克)×12克   鸡内金(克)×10克代赭石(袋)(10g)×3袋   生甘草(克)×10克   旋覆花(包)(10g)×1包降香(克)×10克   川芎(克)×10克   生黄芩(克)×10克川楝子(克)×10克   青皮(克)×10克   香附(克)×10克郁金(克)×10克   枳壳(克)×6克   柴胡(克)×10克生黄芪(克)×30克   白芍(克)×10克   赤芍(克)×10克陈皮(克)×10克   丝瓜络(克)×6克     7剂  水煎服  每日两次";


        content = "2型糖尿病慢性支气管炎乏力蛋白尿脾肾亏虚证气血失调证";
        /*String result = IKUtil.spilt(content, ",");
        System.out.println(JSON.toJSONString(result));*/

        String result = IKUtil.JieBa(content);
        System.out.println(JSON.toJSONString(result));
    }

}
