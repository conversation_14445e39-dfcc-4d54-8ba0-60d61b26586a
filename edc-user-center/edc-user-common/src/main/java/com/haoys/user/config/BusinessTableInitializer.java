package com.haoys.user.config;

import com.haoys.user.service.DatabaseTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 业务表初始化器
 * 在应用启动时自动检查并创建必要的业务表
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Component
@Order(100) // 设置执行顺序，确保在其他初始化之后执行
public class BusinessTableInitializer implements ApplicationRunner {
    
    @Autowired
    private DatabaseTableService databaseTableService;
    
    @Value("${business.table.auto-create:true}")
    private Boolean autoCreateTables;
    
    @Value("${business.table.check-on-startup:true}")
    private Boolean checkOnStartup;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (!checkOnStartup) {
            log.info("业务表启动检查已禁用，跳过表检查");
            return;
        }
        
        log.info("================================================================================");
        log.info("🚀 开始业务表初始化检查");
        log.info("================================================================================");
        log.info("📋 初始化配置:");
        log.info("  - 自动创建表: {}", autoCreateTables);
        log.info("  - 启动时检查: {}", checkOnStartup);
        log.info("  - 当前环境: {}", activeProfile);
        log.info("--------------------------------------------------------------------------------");
        
        try {
            // 获取数据库信息
            Map<String, Object> dbInfo = databaseTableService.getDatabaseInfo();
            log.info("📊 数据库信息:");
            log.info("  - 数据库: {}", dbInfo.get("database"));
            log.info("  - 版本: {}", dbInfo.get("version"));
            log.info("  - 字符集: {}", dbInfo.get("charset"));
            log.info("  - 现有表数量: {}", dbInfo.get("tableCount"));
            log.info("--------------------------------------------------------------------------------");
            
            if (autoCreateTables) {
                // 检查并创建业务表
                Map<String, Object> result = databaseTableService.checkAndCreateBusinessTables();
                
                log.info("🔧 业务表检查结果:");
                log.info("  - 总表数: {}", result.get("totalTables"));
                log.info("  - 新建表数: {}", result.get("createdCount"));
                log.info("  - 已存在表数: {}", result.get("existingCount"));
                log.info("  - 失败表数: {}", result.get("failedCount"));
                
                if (result.get("createdCount") != null && (Integer) result.get("createdCount") > 0) {
                    log.info("✅ 新建的表: {}", result.get("createdTables"));
                }
                
                if (result.get("existingCount") != null && (Integer) result.get("existingCount") > 0) {
                    log.info("📋 已存在的表: {}", result.get("existingTables"));
                }
                
                if (result.get("failedCount") != null && (Integer) result.get("failedCount") > 0) {
                    log.error("❌ 创建失败的表: {}", result.get("failedTables"));
                }
                
                // 检查是否有失败的表
                Integer failedCount = (Integer) result.get("failedCount");
                if (failedCount != null && failedCount > 0) {
                    log.warn("⚠️ 有 {} 个表创建失败，请检查日志并手动处理", failedCount);
                } else {
                    log.info("✅ 所有业务表检查完成，系统就绪");
                }
            } else {
                log.info("🔍 仅检查表状态（自动创建已禁用）");
                
                // 仅检查表是否存在，不创建
                Map<String, String> businessTables = getBusinessTableDefinitions();
                int existingCount = 0;
                int missingCount = 0;
                
                for (String tableName : businessTables.keySet()) {
                    if (databaseTableService.tableExists(tableName)) {
                        existingCount++;
                        log.info("  ✅ 表[{}]存在", tableName);
                    } else {
                        missingCount++;
                        log.warn("  ❌ 表[{}]不存在", tableName);
                    }
                }
                
                log.info("📊 表状态统计: 存在 {}, 缺失 {}", existingCount, missingCount);
                
                if (missingCount > 0) {
                    log.warn("⚠️ 有 {} 个必要的业务表不存在，建议启用自动创建功能或手动创建", missingCount);
                }
            }
            
        } catch (Exception e) {
            log.error("❌ 业务表初始化过程中发生错误", e);
            
            // 根据环境决定是否抛出异常
            if ("prod".equals(activeProfile) || "production".equals(activeProfile)) {
                log.error("🚨 生产环境下业务表初始化失败，系统启动中止");
                throw new RuntimeException("业务表初始化失败，系统无法正常启动", e);
            } else {
                log.warn("⚠️ 开发环境下业务表初始化失败，系统继续启动");
            }
        }
        
        log.info("================================================================================");
        log.info("🎉 业务表初始化检查完成");
        log.info("================================================================================");
    }
    
    /**
     * 获取业务表定义（与DatabaseTableServiceImpl中的方法保持一致）
     */
    private Map<String, String> getBusinessTableDefinitions() {
        Map<String, String> tables = new java.util.LinkedHashMap<>();

        // 安全应用配置表
        tables.put("secure_app_config", "sql/secure_app_config.sql");

        // 安全应用请求日志表
        tables.put("secure_app_request_log", "sql/secure_app_config.sql");

        // 系统监控相关表
        tables.put("system_access_log", "sql/system_monitor_tables.sql");
        tables.put("system_online_user", "sql/system_monitor_tables.sql");
        tables.put("system_access_statistics", "sql/system_monitor_tables.sql");

        // AI聊天系统相关表
        tables.put("ai_chat_session", "sql/ai_chat_tables.sql");
        tables.put("ai_chat_message", "sql/ai_chat_tables.sql");
        tables.put("ai_model_config", "sql/ai_chat_tables.sql");
        tables.put("ai_token_usage", "sql/ai_chat_tables.sql");
        tables.put("ai_document_parse", "sql/ai_chat_tables.sql");

        return tables;
    }
}
