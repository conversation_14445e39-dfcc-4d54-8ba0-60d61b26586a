package com.haoys.user.common.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 系统许可证信息实体
 * 用于管理系统使用期限和授权信息
 * 
 * <AUTHOR>
 * @since 2025-01-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemLicenseInfo {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 许可证名称
     */
    private String licenseName;
    
    /**
     * 许可证类型：TRIAL-试用版, STANDARD-标准版, ENTERPRISE-企业版
     */
    private String licenseType;
    
    /**
     * 加密的许可证密钥
     */
    private String encryptedLicenseKey;
    
    /**
     * 加密的到期时间
     */
    private String encryptedExpireTime;
    
    /**
     * 最大用户数限制
     */
    private Integer maxUsers;
    
    /**
     * 最大项目数限制
     */
    private Integer maxProjects;
    
    /**
     * 功能权限配置（JSON格式）
     */
    private String featurePermissions;
    
    /**
     * 许可证状态：ACTIVE-激活, EXPIRED-过期, DISABLED-禁用
     */
    private String status;
    
    /**
     * 公钥（用于验证）
     */
    private String publicKey;
    
    /**
     * 私钥（加密存储，仅管理员可见）
     */
    private String privateKey;
    
    /**
     * 系统指纹（硬件标识）
     */
    private String systemFingerprint;
    
    /**
     * 激活时间
     */
    private Date activationTime;
    
    /**
     * 最后验证时间
     */
    private Date lastValidationTime;
    
    /**
     * 验证失败次数
     */
    private Integer validationFailureCount;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 备注信息
     */
    private String remarks;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    // 业务方法
    
    /**
     * 检查许可证是否有效
     */
    public boolean isValid() {
        return "ACTIVE".equals(this.status) && 
               Boolean.TRUE.equals(this.enabled) &&
               this.validationFailureCount != null && 
               this.validationFailureCount < 5; // 最多允许5次验证失败
    }
    
    /**
     * 检查是否为试用版
     */
    public boolean isTrialVersion() {
        return "TRIAL".equals(this.licenseType);
    }
    
    /**
     * 检查是否为企业版
     */
    public boolean isEnterpriseVersion() {
        return "ENTERPRISE".equals(this.licenseType);
    }
    
    /**
     * 增加验证失败次数
     */
    public void incrementValidationFailure() {
        if (this.validationFailureCount == null) {
            this.validationFailureCount = 1;
        } else {
            this.validationFailureCount++;
        }
        this.lastValidationTime = new Date();
    }
    
    /**
     * 重置验证失败次数
     */
    public void resetValidationFailure() {
        this.validationFailureCount = 0;
        this.lastValidationTime = new Date();
    }
    
    /**
     * 设置为过期状态
     */
    public void markAsExpired() {
        this.status = "EXPIRED";
        this.updateTime = new Date();
    }
    
    /**
     * 设置为禁用状态
     */
    public void markAsDisabled() {
        this.status = "DISABLED";
        this.enabled = false;
        this.updateTime = new Date();
    }
    
    /**
     * 激活许可证
     */
    public void activate() {
        this.status = "ACTIVE";
        this.enabled = true;
        this.activationTime = new Date();
        this.updateTime = new Date();
        this.validationFailureCount = 0;
    }
}
