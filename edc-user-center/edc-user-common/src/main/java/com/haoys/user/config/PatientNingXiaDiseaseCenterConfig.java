package com.haoys.user.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "disease-nxzbk")
public class PatientNingXiaDiseaseCenterConfig {

    private String database_name;
    private String patient_elastic_index;
    private String visit_elastic_index;
    private String patient_full_text_index;
    private String visit_nested_elastic_index;
    private String patient_nested_text_index;
    private String patient_join_visit_index;
}
