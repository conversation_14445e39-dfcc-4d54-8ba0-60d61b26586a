package com.haoys.user.elasticsearch;

import lombok.Data;

import java.util.Map;

/**
 * 查询结果集-total
 * @date 2021/4/5 11:46
 */
@Data
public class ResultTotal {
    /**
     * 记录条数,聚合后的条数
     */
    private Long recordTotal;
    /**
     * 所有满足条件的总数
     */
    private Long sumTotal;

    private Map<String, Object> sourceMap;

    public ResultTotal(Long recordTotal, Long sumTotal, Map<String, Object> sourceMap) {
        this.recordTotal = recordTotal;
        this.sumTotal = sumTotal;
        this.sourceMap = sourceMap;
    }

    /**getter and setter method**/

}

