package com.haoys.user.storge.cloud;

import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.haoys.user.exception.StorageException;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

/**
 * 七牛云存储
 */
@Slf4j
public class QiniuCloudStorageService extends OssStorageService {
    private UploadManager uploadManager;
    private String token;
    private Auth auth;
    private Configuration cfg;

    QiniuCloudStorageService(OssStorageConfig config) {
        this.config = config;
        cfg = new Configuration(Region.autoRegion());
        uploadManager = new UploadManager(cfg);
        auth = Auth.create(config.getAccessKeyId(), config.getAccessKeySecret());
        token = auth.uploadToken(config.getBucketName());
    }

    @Override
    public String upload(InputStream inputStream, String path) {
        byte[] bytes = IoUtil.readBytes(inputStream);
        return this.upload(bytes, path);
    }

    @Override
    public String upload(byte[] data, String path) {
        try {
            Response res = uploadManager.put(data, path, token);
            log.info("upload info:{}", JSON.toJSON(res));
            if (!res.isOK()) {
                throw new RuntimeException("上传七牛出错：" + res.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new StorageException("上传文件失败，请核对七牛配置信息");
        }
        return config.getDomain() + "/" + path;
    }

    @Override
    public InputStream download(String path) {
        final ByteArrayOutputStream output = new ByteArrayOutputStream();
        HttpUtil.download(config.getDomain() + "/" + path, output, false);
        return new ByteArrayInputStream(output.toByteArray());
    }

    @Override
    public void delete(String path) {
        BucketManager bucketManager = new BucketManager(auth, cfg);
        try {
            bucketManager.delete(config.getBucketName(), path);
        } catch (QiniuException e) {
            e.printStackTrace();
        }
    }


}
