package com.haoys.user.util;

import com.haoys.user.common.util.StringUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 设备信息解析工具类
 * 用于解析User-Agent字符串，提取浏览器、操作系统、设备类型等信息
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
public class DeviceInfoUtils {
    
    // 浏览器正则表达式
    private static final Pattern CHROME_PATTERN = Pattern.compile("Chrome/([\\d.]+)");
    private static final Pattern FIREFOX_PATTERN = Pattern.compile("Firefox/([\\d.]+)");
    private static final Pattern SAFARI_PATTERN = Pattern.compile("Safari/([\\d.]+)");
    private static final Pattern EDGE_PATTERN = Pattern.compile("Edge/([\\d.]+)");
    private static final Pattern IE_PATTERN = Pattern.compile("MSIE ([\\d.]+)");
    private static final Pattern OPERA_PATTERN = Pattern.compile("Opera/([\\d.]+)");
    
    // 操作系统正则表达式
    private static final Pattern WINDOWS_PATTERN = Pattern.compile("Windows NT ([\\d.]+)");
    private static final Pattern MAC_PATTERN = Pattern.compile("Mac OS X ([\\d_.]+)");
    private static final Pattern LINUX_PATTERN = Pattern.compile("Linux");
    private static final Pattern ANDROID_PATTERN = Pattern.compile("Android ([\\d.]+)");
    private static final Pattern IOS_PATTERN = Pattern.compile("OS ([\\d_]+)");
    
    // 设备类型正则表达式
    private static final Pattern MOBILE_PATTERN = Pattern.compile("Mobile|Android|iPhone|iPad|iPod|BlackBerry|Windows Phone");
    private static final Pattern TABLET_PATTERN = Pattern.compile("iPad|Tablet");
    
    /**
     * 解析User-Agent字符串
     * 
     * @param userAgent User-Agent字符串
     * @return 包含浏览器、操作系统、设备类型的Map
     */
    public static Map<String, String> parseUserAgent(String userAgent) {
        Map<String, String> deviceInfo = new HashMap<>();
        
        if (StringUtils.isEmpty(userAgent)) {
            deviceInfo.put("browser", "Unknown");
            deviceInfo.put("os", "Unknown");
            deviceInfo.put("deviceType", "UNKNOWN");
            return deviceInfo;
        }
        
        // 解析浏览器
        String browser = parseBrowser(userAgent);
        deviceInfo.put("browser", browser);
        
        // 解析操作系统
        String os = parseOperatingSystem(userAgent);
        deviceInfo.put("os", os);
        
        // 解析设备类型
        String deviceType = parseDeviceType(userAgent);
        deviceInfo.put("deviceType", deviceType);
        
        return deviceInfo;
    }
    
    /**
     * 解析浏览器信息
     */
    private static String parseBrowser(String userAgent) {
        // Chrome (需要在Safari之前检查，因为Chrome也包含Safari标识)
        if (userAgent.contains("Chrome") && !userAgent.contains("Edge")) {
            Matcher matcher = CHROME_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "Chrome " + matcher.group(1);
            }
            return "Chrome";
        }
        
        // Edge
        if (userAgent.contains("Edge")) {
            Matcher matcher = EDGE_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "Edge " + matcher.group(1);
            }
            return "Edge";
        }
        
        // Firefox
        if (userAgent.contains("Firefox")) {
            Matcher matcher = FIREFOX_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "Firefox " + matcher.group(1);
            }
            return "Firefox";
        }
        
        // Safari
        if (userAgent.contains("Safari") && !userAgent.contains("Chrome")) {
            Matcher matcher = SAFARI_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "Safari " + matcher.group(1);
            }
            return "Safari";
        }
        
        // Internet Explorer
        if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            Matcher matcher = IE_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "IE " + matcher.group(1);
            }
            return "Internet Explorer";
        }
        
        // Opera
        if (userAgent.contains("Opera")) {
            Matcher matcher = OPERA_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "Opera " + matcher.group(1);
            }
            return "Opera";
        }
        
        // 微信浏览器
        if (userAgent.contains("MicroMessenger")) {
            return "WeChat Browser";
        }
        
        // QQ浏览器
        if (userAgent.contains("QQBrowser")) {
            return "QQ Browser";
        }
        
        // UC浏览器
        if (userAgent.contains("UCBrowser")) {
            return "UC Browser";
        }
        
        return "Unknown";
    }
    
    /**
     * 解析操作系统信息
     */
    private static String parseOperatingSystem(String userAgent) {
        // Windows
        if (userAgent.contains("Windows")) {
            Matcher matcher = WINDOWS_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                String version = matcher.group(1);
                return "Windows " + getWindowsVersion(version);
            }
            return "Windows";
        }
        
        // Mac OS
        if (userAgent.contains("Mac OS X")) {
            Matcher matcher = MAC_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                String version = matcher.group(1).replace("_", ".");
                return "Mac OS X " + version;
            }
            return "Mac OS X";
        }
        
        // iOS
        if (userAgent.contains("iPhone") || userAgent.contains("iPad") || userAgent.contains("iPod")) {
            Matcher matcher = IOS_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                String version = matcher.group(1).replace("_", ".");
                return "iOS " + version;
            }
            return "iOS";
        }
        
        // Android
        if (userAgent.contains("Android")) {
            Matcher matcher = ANDROID_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                return "Android " + matcher.group(1);
            }
            return "Android";
        }
        
        // Linux
        if (LINUX_PATTERN.matcher(userAgent).find()) {
            return "Linux";
        }
        
        return "Unknown";
    }
    
    /**
     * 解析设备类型
     */
    private static String parseDeviceType(String userAgent) {
        // 平板设备
        if (TABLET_PATTERN.matcher(userAgent).find()) {
            return "TABLET";
        }
        
        // 移动设备
        if (MOBILE_PATTERN.matcher(userAgent).find()) {
            return "MOBILE";
        }
        
        // 桌面设备
        if (userAgent.contains("Windows") || userAgent.contains("Mac OS X") || userAgent.contains("Linux")) {
            return "DESKTOP";
        }
        
        return "UNKNOWN";
    }
    
    /**
     * 获取Windows版本名称
     */
    private static String getWindowsVersion(String version) {
        switch (version) {
            case "10.0":
                return "10";
            case "6.3":
                return "8.1";
            case "6.2":
                return "8";
            case "6.1":
                return "7";
            case "6.0":
                return "Vista";
            case "5.1":
                return "XP";
            case "5.0":
                return "2000";
            default:
                return version;
        }
    }
    
    /**
     * 判断是否为移动设备
     */
    public static boolean isMobileDevice(String userAgent) {
        if (StringUtils.isEmpty(userAgent)) {
            return false;
        }
        return MOBILE_PATTERN.matcher(userAgent).find();
    }
    
    /**
     * 判断是否为平板设备
     */
    public static boolean isTabletDevice(String userAgent) {
        if (StringUtils.isEmpty(userAgent)) {
            return false;
        }
        return TABLET_PATTERN.matcher(userAgent).find();
    }
    
    /**
     * 判断是否为桌面设备
     */
    public static boolean isDesktopDevice(String userAgent) {
        if (StringUtils.isEmpty(userAgent)) {
            return false;
        }
        return !isMobileDevice(userAgent) && !isTabletDevice(userAgent);
    }
    
    /**
     * 获取简化的浏览器名称
     */
    public static String getSimpleBrowserName(String userAgent) {
        if (StringUtils.isEmpty(userAgent)) {
            return "Unknown";
        }
        
        if (userAgent.contains("Chrome") && !userAgent.contains("Edge")) {
            return "Chrome";
        } else if (userAgent.contains("Firefox")) {
            return "Firefox";
        } else if (userAgent.contains("Safari") && !userAgent.contains("Chrome")) {
            return "Safari";
        } else if (userAgent.contains("Edge")) {
            return "Edge";
        } else if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            return "IE";
        } else if (userAgent.contains("Opera")) {
            return "Opera";
        } else if (userAgent.contains("MicroMessenger")) {
            return "WeChat";
        } else if (userAgent.contains("QQBrowser")) {
            return "QQ";
        } else if (userAgent.contains("UCBrowser")) {
            return "UC";
        }
        
        return "Unknown";
    }
    
    /**
     * 获取简化的操作系统名称
     */
    public static String getSimpleOSName(String userAgent) {
        if (StringUtils.isEmpty(userAgent)) {
            return "Unknown";
        }
        
        if (userAgent.contains("Windows")) {
            return "Windows";
        } else if (userAgent.contains("Mac OS X")) {
            return "macOS";
        } else if (userAgent.contains("iPhone") || userAgent.contains("iPad") || userAgent.contains("iPod")) {
            return "iOS";
        } else if (userAgent.contains("Android")) {
            return "Android";
        } else if (userAgent.contains("Linux")) {
            return "Linux";
        }
        
        return "Unknown";
    }
}
