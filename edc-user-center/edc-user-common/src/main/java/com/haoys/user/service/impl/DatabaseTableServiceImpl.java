package com.haoys.user.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.haoys.user.service.DatabaseTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 数据库表管理服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class DatabaseTableServiceImpl implements DatabaseTableService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public boolean tableExists(String tableName) {
        if (StrUtil.isBlank(tableName)) {
            return false;
        }
        
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            boolean exists = count != null && count > 0;
            log.debug("检查表[{}]是否存在: {}", tableName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查表[{}]是否存在时发生错误", tableName, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean createTable(String tableName, String createSql) {
        if (StrUtil.isBlank(tableName) || StrUtil.isBlank(createSql)) {
            log.warn("表名或创建SQL为空: tableName={}, createSql={}", tableName, StrUtil.isBlank(createSql) ? "空" : "非空");
            return false;
        }
        
        try {
            // 检查表是否已存在
            if (tableExists(tableName)) {
                log.info("表[{}]已存在，跳过创建", tableName);
                return true;
            }
            
            // 执行创建表SQL
            jdbcTemplate.execute(createSql);
            log.info("成功创建表[{}]", tableName);
            return true;
        } catch (Exception e) {
            log.error("创建表[{}]失败", tableName, e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean dropTable(String tableName) {
        if (StrUtil.isBlank(tableName)) {
            return false;
        }
        
        try {
            if (!tableExists(tableName)) {
                log.info("表[{}]不存在，无需删除", tableName);
                return true;
            }
            
            String sql = "DROP TABLE " + tableName;
            jdbcTemplate.execute(sql);
            log.info("成功删除表[{}]", tableName);
            return true;
        } catch (Exception e) {
            log.error("删除表[{}]失败", tableName, e);
            return false;
        }
    }
    
    @Override
    public List<Map<String, Object>> getTableStructure(String tableName) {
        if (StrUtil.isBlank(tableName)) {
            return new ArrayList<>();
        }
        
        try {
            String sql = "DESCRIBE " + tableName;
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
            log.debug("获取表[{}]结构信息: {} 个字段", tableName, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取表[{}]结构信息失败", tableName, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public Map<String, Object> checkAndCreateBusinessTables() {
        log.info("开始检查并创建业务表...");
        
        Map<String, Object> result = new HashMap<>();
        List<String> createdTables = new ArrayList<>();
        List<String> existingTables = new ArrayList<>();
        List<String> failedTables = new ArrayList<>();
        
        // 定义需要创建的业务表及其SQL脚本路径
        Map<String, String> businessTables = getBusinessTableDefinitions();
        
        for (Map.Entry<String, String> entry : businessTables.entrySet()) {
            String tableName = entry.getKey();
            String sqlFilePath = entry.getValue();
            
            try {
                if (tableExists(tableName)) {
                    existingTables.add(tableName);
                    log.info("业务表[{}]已存在", tableName);
                } else {
                    // 读取SQL文件内容
                    String createSql = readSqlFromFile(sqlFilePath);
                    if (StrUtil.isNotBlank(createSql)) {
                        if (createTable(tableName, createSql)) {
                            createdTables.add(tableName);
                            log.info("成功创建业务表[{}]", tableName);
                        } else {
                            failedTables.add(tableName);
                            log.error("创建业务表[{}]失败", tableName);
                        }
                    } else {
                        failedTables.add(tableName);
                        log.error("无法读取表[{}]的SQL文件: {}", tableName, sqlFilePath);
                    }
                }
            } catch (Exception e) {
                failedTables.add(tableName);
                log.error("处理业务表[{}]时发生错误", tableName, e);
            }
        }
        
        result.put("totalTables", businessTables.size());
        result.put("createdTables", createdTables);
        result.put("existingTables", existingTables);
        result.put("failedTables", failedTables);
        result.put("createdCount", createdTables.size());
        result.put("existingCount", existingTables.size());
        result.put("failedCount", failedTables.size());
        
        log.info("业务表检查完成 - 总计: {}, 新建: {}, 已存在: {}, 失败: {}", 
                businessTables.size(), createdTables.size(), existingTables.size(), failedTables.size());
        
        return result;
    }
    
    @Override
    @Transactional
    public boolean executeSqlScript(String sqlScript) {
        if (StrUtil.isBlank(sqlScript)) {
            return false;
        }
        
        try {
            // 分割SQL语句（以分号分隔）
            String[] statements = sqlScript.split(";");
            
            for (String statement : statements) {
                String trimmedStatement = statement.trim();
                if (StrUtil.isNotBlank(trimmedStatement) && !trimmedStatement.startsWith("--")) {
                    jdbcTemplate.execute(trimmedStatement);
                }
            }
            
            log.info("成功执行SQL脚本，共 {} 条语句", statements.length);
            return true;
        } catch (Exception e) {
            log.error("执行SQL脚本失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public Map<String, Object> executeBatchSql(List<String> sqlStatements) {
        Map<String, Object> result = new HashMap<>();
        List<String> successStatements = new ArrayList<>();
        List<String> failedStatements = new ArrayList<>();
        
        if (sqlStatements == null || sqlStatements.isEmpty()) {
            result.put("successCount", 0);
            result.put("failedCount", 0);
            result.put("successStatements", successStatements);
            result.put("failedStatements", failedStatements);
            return result;
        }
        
        for (String sql : sqlStatements) {
            if (StrUtil.isBlank(sql)) {
                continue;
            }
            
            try {
                jdbcTemplate.execute(sql.trim());
                successStatements.add(sql);
            } catch (Exception e) {
                failedStatements.add(sql);
                log.error("执行SQL语句失败: {}", sql, e);
            }
        }
        
        result.put("totalCount", sqlStatements.size());
        result.put("successCount", successStatements.size());
        result.put("failedCount", failedStatements.size());
        result.put("successStatements", successStatements);
        result.put("failedStatements", failedStatements);
        
        log.info("批量执行SQL完成 - 总计: {}, 成功: {}, 失败: {}", 
                sqlStatements.size(), successStatements.size(), failedStatements.size());
        
        return result;
    }
    
    @Override
    public Map<String, Object> getDatabaseInfo() {
        Map<String, Object> info = new HashMap<>();
        
        try {
            // 获取数据库版本
            String version = jdbcTemplate.queryForObject("SELECT VERSION()", String.class);
            info.put("version", version);
            
            // 获取当前数据库名
            String database = jdbcTemplate.queryForObject("SELECT DATABASE()", String.class);
            info.put("database", database);
            
            // 获取表数量
            Integer tableCount = jdbcTemplate.queryForObject(
                    "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE()", 
                    Integer.class);
            info.put("tableCount", tableCount);
            
            // 获取字符集
            String charset = jdbcTemplate.queryForObject(
                    "SELECT DEFAULT_CHARACTER_SET_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = DATABASE()", 
                    String.class);
            info.put("charset", charset);
            
            log.debug("获取数据库信息: {}", info);
        } catch (Exception e) {
            log.error("获取数据库信息失败", e);
        }
        
        return info;
    }
    
    /**
     * 定义业务表及其SQL文件路径
     */
    private Map<String, String> getBusinessTableDefinitions() {
        Map<String, String> tables = new LinkedHashMap<>();

        // 安全应用配置表
        tables.put("secure_app_config", "sql/secure_app_config.sql");

        // 安全应用请求日志表
        tables.put("secure_app_request_log", "sql/secure_app_config.sql");

        // 系统监控相关表
        tables.put("system_access_log", "sql/system_monitor_tables.sql");
        tables.put("system_online_user", "sql/system_monitor_tables.sql");

        // AI聊天系统相关表
        tables.put("ai_chat_session", "sql/ai_chat_tables.sql");
        tables.put("ai_chat_message", "sql/ai_chat_tables.sql");
        tables.put("ai_model_config", "sql/ai_chat_tables.sql");
        tables.put("ai_token_usage", "sql/ai_chat_tables.sql");
        tables.put("ai_document_parse", "sql/ai_chat_tables.sql");

        return tables;
    }
    
    /**
     * 从文件读取SQL内容
     */
    private String readSqlFromFile(String filePath) {
        try {
            ClassPathResource resource = new ClassPathResource(filePath);
            if (resource.exists()) {
                return IoUtil.read(resource.getInputStream(), StandardCharsets.UTF_8);
            } else {
                log.warn("SQL文件不存在: {}", filePath);
                return null;
            }
        } catch (IOException e) {
            log.error("读取SQL文件失败: {}", filePath, e);
            return null;
        }
    }
}
