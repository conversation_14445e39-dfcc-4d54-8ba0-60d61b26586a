package com.haoys.user.elasticsearch;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.GetResponse;
import co.elastic.clients.elasticsearch.core.IndexResponse;
import co.elastic.clients.elasticsearch.core.UpdateResponse;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Service
public class ElasticSearchService {

    private final String  ERROR_ES_INDEX="edc_xjbfy_record_error_index";

    //@Resource(name="clientByPasswd")
    @Resource(name="clientByApiKey")
    private ElasticsearchClient elasticsearchClient;

    public void addIndex(String name) throws IOException {
        elasticsearchClient.indices().create(c -> c.index(name));
    }

    public boolean indexExists(String name) throws IOException {
        return elasticsearchClient.indices().exists(b -> b.index(name)).value();
    }

    public void deleteIndex(String name) throws IOException {
        elasticsearchClient.indices().delete(c -> c.index(name));
    }

    public void update(String indexName,String id,Map<String, Object> map){
        // 构建需要修改的内容，这里使用了Map
        // 构建修改文档的请求
        String info="";
        try {
            UpdateResponse<JSONObject> update = elasticsearchClient.update(e -> e
                            .index(indexName)
                            .id(id)
                            .doc(map),
                    JSONObject.class
            );
            info= update.toString();
        }catch (Exception e){
          try {
              Map<String, Object> documentMap = new HashMap<>();
              documentMap.put("patientSn", id);
              documentMap.put("error_message", e.getMessage());
              documentMap.put("error_stack_trace", e.getStackTrace());
              documentMap.put("indexResponse_message", info);
              elasticsearchClient.index(element -> element.index(ERROR_ES_INDEX).id(id).document(documentMap));
          }catch (Exception ex){
              e.printStackTrace();
          }
        }
    }

    public GetResponse<JSONObject> selectById(String indexName, String id) throws IOException {
        GetResponse<JSONObject> response = elasticsearchClient.get(g -> g
                        .index(indexName)
                        .id(id),
                JSONObject.class
        );
        return response;
    }

    public GetResponse<JSONObject> selectByIdAndFilterFiled(String indexName,String id,String filed) throws IOException {
        GetResponse<JSONObject> response = elasticsearchClient.get(g -> g
                        .index(indexName).sourceIncludes(filed)
                        .id(id),
                JSONObject.class
        );
        return response;
    }

    public void insertDocument(String indexName, Map<String, Object> document, String id) {
        String jsonValue = "";
        try {
            IndexResponse indexResponse = elasticsearchClient.index(e -> e.index(indexName).id(id).document(document));
            jsonValue = indexResponse.result().jsonValue();
        } catch (IOException e) {
            log.error("insertDocument error", e);
            try {
                Map<String, Object> documentMap = new HashMap<>();
                documentMap.put("patientSn", document.get("patientSn"));
                documentMap.put("error_message", e.getMessage());
                documentMap.put("error_stack_trace", e.getStackTrace());
                documentMap.put("indexResponse_message", jsonValue);
                elasticsearchClient.index(element -> element.index(ERROR_ES_INDEX).id(id).document(documentMap));
            } catch (IOException exception) {
                //throw new RuntimeException(exception);
                log.error("insertDocument record error", exception.getMessage());
            }
            //throw new ServiceException(ResultCode.BUSINESS_CODE_0000000000.INDEX_CREATE_ERROR.getMessage());
        }
    }
}
