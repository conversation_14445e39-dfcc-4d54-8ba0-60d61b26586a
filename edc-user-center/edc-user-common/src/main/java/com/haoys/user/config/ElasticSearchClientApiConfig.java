package com.haoys.user.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.message.BasicHeader;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder.HttpClientConfigCallback;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;

@Slf4j
@ConfigurationProperties(prefix = "elasticsearch") //配置的前缀
@Configuration
public class ElasticSearchClientApiConfig {

    @Setter
    private String hosts;
    @Setter
    private String username;
    @Setter
    private String passwd;
    @Setter
    private String apikey;
    @Setter
    private String certificate_path;

    @Value("${elasticsearch.enabled_ssl}")
    private Boolean enabled_ssl;

    /**
     * 解析配置的字符串，转为HttpHost对象数组
     * @return
     */
    private HttpHost[] toHttpHost() {
        if (!StringUtils.hasLength(hosts)) {
            throw new RuntimeException("invalid elasticsearch configuration");
        }

        String[] hostArray = hosts.split(",");
        HttpHost[] httpHosts = new HttpHost[hostArray.length];
        HttpHost httpHost;
        for (int i = 0; i < hostArray.length; i++) {
            String[] strings = hostArray[i].split(":");
            httpHost = new HttpHost(strings[0], Integer.parseInt(strings[1]), "https");
            httpHosts[i] = httpHost;
        }

        return httpHosts;
    }
    private SSLContext buildSSLContext() {
        ClassPathResource resource = new ClassPathResource(certificate_path);
        SSLContext sslContext = null;
        try {
            CertificateFactory factory = CertificateFactory.getInstance("X.509");
            Certificate trustedCa;
            try (InputStream is = resource.getInputStream()) {
                trustedCa = factory.generateCertificate(is);
            }
            KeyStore trustStore = KeyStore.getInstance("pkcs12");
            trustStore.load(null, null);
            trustStore.setCertificateEntry("ca", trustedCa);
            SSLContextBuilder sslContextBuilder = SSLContexts.custom().loadTrustMaterial(trustStore, null);
            sslContext = sslContextBuilder.build();
            log.info("ES连接认证成功, 证书ID: {}", sslContext.getClientSessionContext().getIds());
        } catch (CertificateException | IOException | KeyStoreException | NoSuchAlgorithmException |
                 KeyManagementException e) {
            log.error("ES连接认证失败, 原因: {}", e.getMessage());
        }
        return sslContext;
    }

    private ElasticsearchTransport getElasticsearchTransport(String username, String passwd, HttpHost...hosts) {
        // 账号密码的配置
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, passwd));

        // 自签证书的设置，并且还包含了账号密码
        HttpClientConfigCallback callback = httpAsyncClientBuilder -> httpAsyncClientBuilder
                .setSSLContext(buildSSLContext())
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                .setDefaultCredentialsProvider(credentialsProvider);

        // 用builder创建RestClient对象
        RestClient client = RestClient
                           .builder(hosts)
                           .setHttpClientConfigCallback(callback)
                           .build();

        return new RestClientTransport(client, new JacksonJsonpMapper());
    }

    private ElasticsearchTransport getElasticsearchTransport(String apiKey, HttpHost...hosts) {
        // 将ApiKey放入header中
        Header[] headers = new Header[] {new BasicHeader("Authorization", "ApiKey " + apiKey)};

        // es自签证书的设置
        HttpClientConfigCallback callback = httpAsyncClientBuilder -> httpAsyncClientBuilder
                .setSSLContext(buildSSLContext())
                .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE);

        // 用builder创建RestClient对象
        RestClient client = RestClient
                           .builder(hosts)
                           .setHttpClientConfigCallback(callback)
                           .setDefaultHeaders(headers)
                           .build();

        return new RestClientTransport(client, new JacksonJsonpMapper());
    }



    private ElasticsearchClient passwordClient() {
        String serverUrl = "https://"+hosts;
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(username, passwd));
        RestClient restClient = RestClient.builder(HttpHost.create(serverUrl)).setCompressionEnabled(true)
                .setHttpClientConfigCallback(httpClientBuilder -> {
                    // 这里设置鉴权需要的用户名elastic和对应密码
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                    SSLContextBuilder sscb = SSLContexts.custom();
                    try {
                        sscb.loadTrustMaterial((TrustStrategy) (chain, authType) -> {
                            // 在这里跳过证书信息校验
                            //System.out.println("isTrusted|" + authType + "|" + Arrays.toString(chain));
                            return true;
                        });
                        httpClientBuilder.setSSLContext(sscb.build());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // 这里跳过主机名称校验
                    httpClientBuilder.setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE);
                    return httpClientBuilder;
                }).build();
        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());
        return new ElasticsearchClient(transport);
    }

    @Bean
    public ElasticsearchClient clientByApiKey() throws Exception {
        // 如果不启用ssl 则跳过验证
        if (enabled_ssl){
            ElasticsearchTransport transport = getElasticsearchTransport(apikey, toHttpHost());
            return new ElasticsearchClient(transport);
        }else {
            return passwordClient();
        }

    }

    @Bean
    public ElasticsearchClient clientByPasswd() {
        return passwordClient();
    }

}
