package com.haoys.user.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Redis数据管理配置类
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@Component
@ConfigurationProperties(prefix = "redis.management")
public class RedisManagementConfig {
    
    /**
     * 管理密钥配置
     */
    private String secretKey = "EDC-REDIS-MANAGEMENT-SECRET-2025";
    
    /**
     * 是否启用Redis管理功能
     */
    private Boolean enabled = true;
    
    /**
     * 查询限制数量
     */
    private Integer queryLimit = 1000;
    
    /**
     * 删除确认要求
     */
    private Boolean requireConfirmation = true;
    
    /**
     * 允许的操作类型
     */
    private String[] allowedOperations = {"query", "delete"};
    
    /**
     * 禁止操作的key前缀
     */
    private String[] forbiddenPrefixes = {"system:", "security:", "session:"};
}
