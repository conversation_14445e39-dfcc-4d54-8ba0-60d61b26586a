<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SecureAppConfigMapper">
    
    <resultMap id="BaseResultMap" type="com.haoys.user.model.SecureAppConfig">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="app_description" jdbcType="VARCHAR" property="appDescription" />
        <result column="environment" jdbcType="VARCHAR" property="environment" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="code_expiration" jdbcType="INTEGER" property="codeExpiration" />
        <result column="access_token_expiration" jdbcType="INTEGER" property="accessTokenExpiration" />
        <result column="max_daily_requests" jdbcType="INTEGER" property="maxDailyRequests" />
        <result column="allowed_ips" jdbcType="LONGVARCHAR" property="allowedIps" />
        <result column="webhook_url" jdbcType="VARCHAR" property="webhookUrl" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
        <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, app_id, app_secret, app_name, app_description, environment, status, 
        code_expiration, access_token_expiration, max_daily_requests, allowed_ips, 
        webhook_url, create_time, update_time, create_user_id, update_user_id, 
        tenant_id, platform_id
    </sql>
    
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from secure_app_config
        where id = #{id,jdbcType=BIGINT}
    </select>
    
    <select id="selectByAppIdAndEnvironment" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from secure_app_config
        where app_id = #{appId,jdbcType=VARCHAR} 
        and environment = #{environment,jdbcType=VARCHAR}
        and status = 1
    </select>
    
    <select id="selectByEnvironment" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from secure_app_config
        where environment = #{environment,jdbcType=VARCHAR}
        and status = 1
        order by create_time desc
    </select>
    
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from secure_app_config
        where status = 1
        order by environment, create_time desc
    </select>
    
    <select id="validateAppCredentials" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from secure_app_config
        where app_id = #{appId,jdbcType=VARCHAR} 
        and app_secret = #{appSecret,jdbcType=VARCHAR}
        and environment = #{environment,jdbcType=VARCHAR}
        and status = 1
    </select>
    
    <select id="countTodayRequests" resultType="java.lang.Integer">
        select count(*)
        from secure_app_request_log
        where app_id = #{appId,jdbcType=VARCHAR}
        and environment = #{environment,jdbcType=VARCHAR}
        and DATE(request_time) = CURDATE()
    </select>
    
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from secure_app_config
        where id = #{id,jdbcType=BIGINT}
    </delete>
    
    <delete id="deleteByAppIdAndEnvironment">
        delete from secure_app_config
        where app_id = #{appId,jdbcType=VARCHAR} 
        and environment = #{environment,jdbcType=VARCHAR}
    </delete>
    
    <insert id="insert" parameterType="com.haoys.user.model.SecureAppConfig">
        insert into secure_app_config (id, app_id, app_secret, 
        app_name, app_description, environment, 
        status, code_expiration, access_token_expiration, 
        max_daily_requests, allowed_ips, webhook_url, 
        create_time, update_time, create_user_id, 
        update_user_id, tenant_id, platform_id)
        values (#{id,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{appSecret,jdbcType=VARCHAR}, 
        #{appName,jdbcType=VARCHAR}, #{appDescription,jdbcType=VARCHAR}, #{environment,jdbcType=VARCHAR}, 
        #{status,jdbcType=TINYINT}, #{codeExpiration,jdbcType=INTEGER}, #{accessTokenExpiration,jdbcType=INTEGER}, 
        #{maxDailyRequests,jdbcType=INTEGER}, #{allowedIps,jdbcType=LONGVARCHAR}, #{webhookUrl,jdbcType=VARCHAR}, 
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=BIGINT}, 
        #{updateUserId,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{platformId,jdbcType=BIGINT})
    </insert>
    
    <insert id="insertSelective" parameterType="com.haoys.user.model.SecureAppConfig">
        insert into secure_app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="appId != null">app_id,</if>
            <if test="appSecret != null">app_secret,</if>
            <if test="appName != null">app_name,</if>
            <if test="appDescription != null">app_description,</if>
            <if test="environment != null">environment,</if>
            <if test="status != null">status,</if>
            <if test="codeExpiration != null">code_expiration,</if>
            <if test="accessTokenExpiration != null">access_token_expiration,</if>
            <if test="maxDailyRequests != null">max_daily_requests,</if>
            <if test="allowedIps != null">allowed_ips,</if>
            <if test="webhookUrl != null">webhook_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createUserId != null">create_user_id,</if>
            <if test="updateUserId != null">update_user_id,</if>
            <if test="tenantId != null">tenant_id,</if>
            <if test="platformId != null">platform_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="appId != null">#{appId,jdbcType=VARCHAR},</if>
            <if test="appSecret != null">#{appSecret,jdbcType=VARCHAR},</if>
            <if test="appName != null">#{appName,jdbcType=VARCHAR},</if>
            <if test="appDescription != null">#{appDescription,jdbcType=VARCHAR},</if>
            <if test="environment != null">#{environment,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="codeExpiration != null">#{codeExpiration,jdbcType=INTEGER},</if>
            <if test="accessTokenExpiration != null">#{accessTokenExpiration,jdbcType=INTEGER},</if>
            <if test="maxDailyRequests != null">#{maxDailyRequests,jdbcType=INTEGER},</if>
            <if test="allowedIps != null">#{allowedIps,jdbcType=LONGVARCHAR},</if>
            <if test="webhookUrl != null">#{webhookUrl,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="createUserId != null">#{createUserId,jdbcType=BIGINT},</if>
            <if test="updateUserId != null">#{updateUserId,jdbcType=BIGINT},</if>
            <if test="tenantId != null">#{tenantId,jdbcType=BIGINT},</if>
            <if test="platformId != null">#{platformId,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    
    <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SecureAppConfig">
        update secure_app_config
        <set>
            <if test="appId != null">app_id = #{appId,jdbcType=VARCHAR},</if>
            <if test="appSecret != null">app_secret = #{appSecret,jdbcType=VARCHAR},</if>
            <if test="appName != null">app_name = #{appName,jdbcType=VARCHAR},</if>
            <if test="appDescription != null">app_description = #{appDescription,jdbcType=VARCHAR},</if>
            <if test="environment != null">environment = #{environment,jdbcType=VARCHAR},</if>
            <if test="status != null">status = #{status,jdbcType=TINYINT},</if>
            <if test="codeExpiration != null">code_expiration = #{codeExpiration,jdbcType=INTEGER},</if>
            <if test="accessTokenExpiration != null">access_token_expiration = #{accessTokenExpiration,jdbcType=INTEGER},</if>
            <if test="maxDailyRequests != null">max_daily_requests = #{maxDailyRequests,jdbcType=INTEGER},</if>
            <if test="allowedIps != null">allowed_ips = #{allowedIps,jdbcType=LONGVARCHAR},</if>
            <if test="webhookUrl != null">webhook_url = #{webhookUrl,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateUserId != null">update_user_id = #{updateUserId,jdbcType=BIGINT},</if>
            <if test="tenantId != null">tenant_id = #{tenantId,jdbcType=BIGINT},</if>
            <if test="platformId != null">platform_id = #{platformId,jdbcType=BIGINT},</if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    
    <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SecureAppConfig">
        update secure_app_config
        set app_id = #{appId,jdbcType=VARCHAR},
        app_secret = #{appSecret,jdbcType=VARCHAR},
        app_name = #{appName,jdbcType=VARCHAR},
        app_description = #{appDescription,jdbcType=VARCHAR},
        environment = #{environment,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        code_expiration = #{codeExpiration,jdbcType=INTEGER},
        access_token_expiration = #{accessTokenExpiration,jdbcType=INTEGER},
        max_daily_requests = #{maxDailyRequests,jdbcType=INTEGER},
        allowed_ips = #{allowedIps,jdbcType=LONGVARCHAR},
        webhook_url = #{webhookUrl,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_user_id = #{updateUserId,jdbcType=BIGINT},
        tenant_id = #{tenantId,jdbcType=BIGINT},
        platform_id = #{platformId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>
    
</mapper>
