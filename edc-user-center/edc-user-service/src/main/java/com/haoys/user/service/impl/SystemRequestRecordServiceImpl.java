package com.haoys.user.service.impl;

import com.haoys.user.config.SystemLogProperties;
import com.haoys.user.mapper.SystemRequestRecordMapper;
import com.haoys.user.model.SystemRequestRecord;
import com.haoys.user.service.SystemRequestRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 系统访问日志记录服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-25
 */
@Service
public class SystemRequestRecordServiceImpl implements SystemRequestRecordService {
    
    private static final Logger log = LoggerFactory.getLogger(SystemRequestRecordServiceImpl.class);
    
    @Autowired
    private SystemRequestRecordMapper recordMapper;
    
    @Autowired
    private SystemLogProperties logProperties;
    
    @Override
    @Transactional
    public boolean saveRecord(SystemRequestRecord record) {
        try {
            if (record == null) {
                return false;
            }
            
            // 设置默认值
            if (record.getCreateTime() == null) {
                record.setCreateTime(new Date());
            }
            if (record.getUpdateTime() == null) {
                record.setUpdateTime(new Date());
            }
            
            int result = recordMapper.insert(record);
            return result > 0;
        } catch (Exception e) {
            log.error("保存系统访问日志记录失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional
    public boolean batchSaveRecords(List<SystemRequestRecord> records) {
        try {
            if (records == null || records.isEmpty()) {
                return false;
            }
            
            // 设置默认值
            Date now = new Date();
            for (SystemRequestRecord record : records) {
                if (record.getCreateTime() == null) {
                    record.setCreateTime(now);
                }
                if (record.getUpdateTime() == null) {
                    record.setUpdateTime(now);
                }
            }
            
            int result = recordMapper.batchInsert(records);
            return result > 0;
        } catch (Exception e) {
            log.error("批量保存系统访问日志记录失败", e);
            return false;
        }
    }
    
    @Override
    public SystemRequestRecord getRecordById(Long id) {
        try {
            if (id == null) {
                return null;
            }
            return recordMapper.selectById(id);
        } catch (Exception e) {
            log.error("根据ID查询日志记录失败: {}", id, e);
            return null;
        }
    }
    
    @Override
    public List<SystemRequestRecord> getRecordsByTraceId(String traceId) {
        try {
            if (traceId == null || traceId.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return recordMapper.selectByTraceId(traceId);
        } catch (Exception e) {
            log.error("根据链路追踪ID查询日志记录失败: {}", traceId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<SystemRequestRecord> getRecordsBySessionId(String sessionId) {
        try {
            if (sessionId == null || sessionId.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return recordMapper.selectBySessionId(sessionId);
        } catch (Exception e) {
            log.error("根据会话ID查询日志记录失败: {}", sessionId, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public Map<String, Object> getRecordsByPage(int pageNum, int pageSize, String userName, 
                                               String requestUrl, Date startTime, Date endTime, Boolean isSuccess) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 计算偏移量
            int offset = (pageNum - 1) * pageSize;
            
            // 查询数据
            List<SystemRequestRecord> records = recordMapper.selectByPage(
                offset, pageSize, userName, requestUrl, startTime, endTime, isSuccess);
            
            // 查询总数
            long total = recordMapper.countByCondition(userName, requestUrl, startTime, endTime, isSuccess);
            
            result.put("records", records);
            result.put("total", total);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", (total + pageSize - 1) / pageSize);
            
        } catch (Exception e) {
            log.error("分页查询日志记录失败", e);
            result.put("records", new ArrayList<>());
            result.put("total", 0L);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("pages", 0L);
        }
        return result;
    }
    
    @Override
    public List<SystemRequestRecord> getRecordsByUser(Long userId, String userName, 
                                                     Date startTime, Date endTime, int limit) {
        try {
            return recordMapper.selectByUser(userId, userName, startTime, endTime, limit);
        } catch (Exception e) {
            log.error("根据用户查询日志记录失败: userId={}, userName={}", userId, userName, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<SystemRequestRecord> getRecordsByIp(String requestIp, Date startTime, Date endTime, int limit) {
        try {
            if (requestIp == null || requestIp.trim().isEmpty()) {
                return new ArrayList<>();
            }
            return recordMapper.selectByIp(requestIp, startTime, endTime, limit);
        } catch (Exception e) {
            log.error("根据IP查询日志记录失败: {}", requestIp, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<SystemRequestRecord> getErrorRecords(Date startTime, Date endTime, int limit) {
        try {
            return recordMapper.selectErrorLogs(startTime, endTime, limit);
        } catch (Exception e) {
            log.error("查询异常日志记录失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<SystemRequestRecord> getSlowRecords(Long minResponseTime, Date startTime, Date endTime, int limit) {
        try {
            if (minResponseTime == null) {
                minResponseTime = logProperties.getSlowRequestThreshold();
            }
            return recordMapper.selectSlowLogs(minResponseTime, startTime, endTime, limit);
        } catch (Exception e) {
            log.error("查询慢请求日志记录失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getAccessStatistics(Date startTime, Date endTime, String groupBy) {
        try {
            if (groupBy == null || groupBy.trim().isEmpty()) {
                groupBy = "day";
            }
            return recordMapper.selectAccessStatistics(startTime, endTime, groupBy);
        } catch (Exception e) {
            log.error("获取访问量统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getHotUrlStatistics(Date startTime, Date endTime, int limit) {
        try {
            return recordMapper.selectHotUrls(startTime, endTime, limit);
        } catch (Exception e) {
            log.error("获取热点URL统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getUserStatistics(Date startTime, Date endTime, int limit) {
        try {
            return recordMapper.selectUserStatistics(startTime, endTime, limit);
        } catch (Exception e) {
            log.error("获取用户访问统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getIpStatistics(Date startTime, Date endTime, int limit) {
        try {
            return recordMapper.selectIpStatistics(startTime, endTime, limit);
        } catch (Exception e) {
            log.error("获取IP访问统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getResponseTimeDistribution(Date startTime, Date endTime) {
        try {
            return recordMapper.selectResponseTimeDistribution(startTime, endTime);
        } catch (Exception e) {
            log.error("获取响应时间分布统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getDeviceTypeDistribution(Date startTime, Date endTime) {
        try {
            return recordMapper.selectDeviceTypeDistribution(startTime, endTime);
        } catch (Exception e) {
            log.error("获取设备类型分布统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Map<String, Object>> getBrowserDistribution(Date startTime, Date endTime) {
        try {
            return recordMapper.selectBrowserDistribution(startTime, endTime);
        } catch (Exception e) {
            log.error("获取浏览器分布统计失败", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    @Transactional
    public int deleteExpiredRecords(int retentionDays) {
        try {
            if (retentionDays <= 0) {
                retentionDays = logProperties.getDataRetentionDays();
            }
            
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -retentionDays);
            Date beforeDate = calendar.getTime();
            
            int deletedCount = recordMapper.deleteByCreateTimeBefore(beforeDate);
            log.info("删除过期日志记录完成，删除记录数: {}, 保留天数: {}", deletedCount, retentionDays);
            
            // 优化表
            if (deletedCount > 0) {
                recordMapper.optimizeTable();
            }
            
            return deletedCount;
        } catch (Exception e) {
            log.error("删除过期日志记录失败", e);
            return 0;
        }
    }
    
    @Override
    @Transactional
    public int deleteRecordsByIds(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return 0;
            }
            return recordMapper.deleteByIds(ids);
        } catch (Exception e) {
            log.error("根据ID列表删除日志记录失败", e);
            return 0;
        }
    }
    
    @Override
    @Transactional
    public int deleteAllRecords() {
        try {
            int deletedCount = recordMapper.deleteAll();
            log.warn("清空所有日志记录完成，删除记录数: {}", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("清空所有日志记录失败", e);
            return 0;
        }
    }
    
    @Override
    public Map<String, Object> getTableStatistics() {
        try {
            return recordMapper.getTableStatistics();
        } catch (Exception e) {
            log.error("获取表统计信息失败", e);
            return new HashMap<>();
        }
    }
    
    @Override
    public void optimizeTable() {
        try {
            recordMapper.optimizeTable();
            log.info("优化系统访问日志表完成");
        } catch (Exception e) {
            log.error("优化系统访问日志表失败", e);
        }
    }
    
    @Override
    public Map<String, Object> getSystemMonitorData() {
        Map<String, Object> monitorData = new HashMap<>();
        try {
            // 获取基础统计信息
            Map<String, Object> tableStats = getTableStatistics();
            monitorData.putAll(tableStats);
            
            // 获取最近24小时的统计
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            calendar.add(Calendar.HOUR_OF_DAY, -24);
            Date startTime = calendar.getTime();
            
            // 访问量统计
            List<Map<String, Object>> accessStats = getAccessStatistics(startTime, endTime, "hour");
            monitorData.put("hourlyStats", accessStats);
            
            // 错误日志统计
            List<SystemRequestRecord> errorRecords = getErrorRecords(startTime, endTime, 10);
            monitorData.put("recentErrors", errorRecords);
            
            // 慢请求统计
            List<SystemRequestRecord> slowRecords = getSlowRecords(null, startTime, endTime, 10);
            monitorData.put("slowRequests", slowRecords);
            
            // 热点URL统计
            List<Map<String, Object>> hotUrls = getHotUrlStatistics(startTime, endTime, 10);
            monitorData.put("hotUrls", hotUrls);
            
        } catch (Exception e) {
            log.error("获取系统监控数据失败", e);
        }
        return monitorData;
    }
    
    @Override
    public Map<String, Object> getRealTimeStatistics() {
        Map<String, Object> realTimeStats = new HashMap<>();
        try {
            // 获取最近1小时的统计
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            calendar.add(Calendar.HOUR_OF_DAY, -1);
            Date startTime = calendar.getTime();
            
            // 总请求数
            long totalRequests = recordMapper.countByCondition(null, null, startTime, endTime, null);
            realTimeStats.put("totalRequests", totalRequests);
            
            // 成功请求数
            long successRequests = recordMapper.countByCondition(null, null, startTime, endTime, true);
            realTimeStats.put("successRequests", successRequests);
            
            // 失败请求数
            long errorRequests = recordMapper.countByCondition(null, null, startTime, endTime, false);
            realTimeStats.put("errorRequests", errorRequests);
            
            // 成功率
            double successRate = totalRequests > 0 ? (double) successRequests / totalRequests * 100 : 0;
            realTimeStats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            // 活跃用户数
            List<Map<String, Object>> userStats = getUserStatistics(startTime, endTime, 1000);
            realTimeStats.put("activeUsers", userStats.size());
            
            // 独立IP数
            List<Map<String, Object>> ipStats = getIpStatistics(startTime, endTime, 1000);
            realTimeStats.put("uniqueIps", ipStats.size());
            
        } catch (Exception e) {
            log.error("获取实时统计数据失败", e);
        }
        return realTimeStats;
    }
    
    @Override
    public String exportRecords(Date startTime, Date endTime, String userName, String requestUrl) {
        try {
            // 这里可以实现导出功能，比如导出为Excel或CSV文件
            // 暂时返回一个占位符
            log.info("导出日志记录: startTime={}, endTime={}, userName={}, requestUrl={}", 
                    startTime, endTime, userName, requestUrl);
            return "export_" + System.currentTimeMillis() + ".xlsx";
        } catch (Exception e) {
            log.error("导出日志记录失败", e);
            return null;
        }
    }
}
