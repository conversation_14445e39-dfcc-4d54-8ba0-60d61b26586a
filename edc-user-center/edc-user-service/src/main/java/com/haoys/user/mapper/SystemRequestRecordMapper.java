package com.haoys.user.mapper;

import com.haoys.user.model.SystemRequestRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Primary;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问日志记录 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Mapper
@Primary
public interface SystemRequestRecordMapper {
    
    /**
     * 插入系统访问日志记录
     * 
     * @param record 日志记录
     * @return 影响行数
     */
    int insert(SystemRequestRecord record);
    
    /**
     * 批量插入系统访问日志记录
     * 
     * @param records 日志记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("records") List<SystemRequestRecord> records);
    
    /**
     * 根据ID查询日志记录
     * 
     * @param id 主键ID
     * @return 日志记录
     */
    SystemRequestRecord selectById(@Param("id") Long id);
    
    /**
     * 根据链路追踪ID查询日志记录
     * 
     * @param traceId 链路追踪ID
     * @return 日志记录列表
     */
    List<SystemRequestRecord> selectByTraceId(@Param("traceId") String traceId);
    
    /**
     * 根据会话ID查询日志记录
     * 
     * @param sessionId 会话ID
     * @return 日志记录列表
     */
    List<SystemRequestRecord> selectBySessionId(@Param("sessionId") String sessionId);
    
    /**
     * 分页查询日志记录
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @param userName 用户名（可选）
     * @param requestUrl 请求URL（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param isSuccess 是否成功（可选）
     * @return 日志记录列表
     */
    List<SystemRequestRecord> selectByPage(
        @Param("offset") int offset,
        @Param("limit") int limit,
        @Param("userName") String userName,
        @Param("requestUrl") String requestUrl,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("isSuccess") Boolean isSuccess
    );
    
    /**
     * 统计日志记录总数
     * 
     * @param userName 用户名（可选）
     * @param requestUrl 请求URL（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param isSuccess 是否成功（可选）
     * @return 总数
     */
    long countByCondition(
        @Param("userName") String userName,
        @Param("requestUrl") String requestUrl,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("isSuccess") Boolean isSuccess
    );
    
    /**
     * 根据用户查询日志记录
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 日志记录列表
     */
    List<SystemRequestRecord> selectByUser(
        @Param("userId") Long userId,
        @Param("userName") String userName,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 根据IP地址查询日志记录
     * 
     * @param requestIp IP地址
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 日志记录列表
     */
    List<SystemRequestRecord> selectByIp(
        @Param("requestIp") String requestIp,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 查询异常日志记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 异常日志记录列表
     */
    List<SystemRequestRecord> selectErrorLogs(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 查询慢请求日志记录
     * 
     * @param minResponseTime 最小响应时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 慢请求日志记录列表
     */
    List<SystemRequestRecord> selectSlowLogs(
        @Param("minResponseTime") Long minResponseTime,
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 统计访问量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段（hour, day, month）
     * @return 统计结果
     */
    List<Map<String, Object>> selectAccessStatistics(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("groupBy") String groupBy
    );
    
    /**
     * 统计热点URL
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 热点URL统计
     */
    List<Map<String, Object>> selectHotUrls(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 统计用户访问情况
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return 用户访问统计
     */
    List<Map<String, Object>> selectUserStatistics(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 统计IP访问情况
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制数量
     * @return IP访问统计
     */
    List<Map<String, Object>> selectIpStatistics(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime,
        @Param("limit") int limit
    );
    
    /**
     * 统计响应时间分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 响应时间分布统计
     */
    List<Map<String, Object>> selectResponseTimeDistribution(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime
    );
    
    /**
     * 统计设备类型分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 设备类型分布统计
     */
    List<Map<String, Object>> selectDeviceTypeDistribution(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime
    );
    
    /**
     * 统计浏览器分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 浏览器分布统计
     */
    List<Map<String, Object>> selectBrowserDistribution(
        @Param("startTime") Date startTime,
        @Param("endTime") Date endTime
    );
    
    /**
     * 删除过期日志记录
     * 
     * @param beforeDate 删除此日期之前的记录
     * @return 删除的记录数
     */
    int deleteByCreateTimeBefore(@Param("beforeDate") Date beforeDate);
    
    /**
     * 根据ID列表删除日志记录
     * 
     * @param ids ID列表
     * @return 删除的记录数
     */
    int deleteByIds(@Param("ids") List<Long> ids);
    
    /**
     * 清空所有日志记录
     * 
     * @return 删除的记录数
     */
    int deleteAll();
    
    /**
     * 获取表统计信息
     * 
     * @return 表统计信息
     */
    Map<String, Object> getTableStatistics();
    
    /**
     * 优化表
     */
    void optimizeTable();
}
