package com.haoys.user.domain.param.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.entity.ProjectResearchersInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
public class SystemUserInfoParam implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "用户id-编辑数据时设置")
    private Long id;

    @ApiModelProperty(value = "用户名", required = true)
    private String username;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "默认密码")
    private String defaultPwd;

    @ApiModelProperty(value = "用户头像")
    private String icon;

    @Email
    @ApiModelProperty(value = "邮箱")
    private String email;

    @NotEmpty(message = "用户类型不能为空")
    @ApiModelProperty(value = "用户类型", required = true)
    private String userType;

    @ApiModelProperty(value = "用户注册来源")
    private String registerFrom;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "账号类型 1-用户名 2-手机号 3-邮箱", required = true)
    private String accountType = "1";

    @ApiModelProperty(value = "联系方式-手机号")
    private String mobile;

    @ApiModelProperty(value = "手机验证码-如果输入需要验证")
    private String verificationCode;

    @ApiModelProperty(value = "预留第三方登录对接id")
    private String thirdOpenId;

    @ApiModelProperty(value = "bdp用户id")
    private String bpdUserId;

    @ApiModelProperty(value = "备注信息")
    private String note;

    @ApiModelProperty(value = "系统角色描述信息-PM等")
    private String roleDesc;

    @ApiModelProperty(value = "启用、停用")
    private Boolean sealFlag;

    @ApiModelProperty(value = "帐号启用状态：0->禁用；1->启用")
    private Integer status;

    @NotEmpty(message = "填写中心不能为空")
    @ApiModelProperty(value = "用户所属中心", required = true)
    private String orgId;

    @ApiModelProperty(value = "身份类型 1-项目负责人 2-医生 3-CRC 4-数据经理 5-医学经理 参照字典项008")
    private String identityType;

    @ApiModelProperty(value = "科室id-参照科室管理", required = true)
    private String department;

    @ApiModelProperty(value = "职称id-参照字典", required = true)
    private String positional;

    @ApiModelProperty(value = "企业部门")
    private String enterprise;

    @ApiModelProperty(value = "地址")
    private String address;
    
    @ApiModelProperty(value = "研究者信息")
    private ProjectResearchersInfo researchersInfo;

    @ApiModelProperty(value = "创建项目数量限制")
    private Integer projectCountLimit;

    @ApiModelProperty(value = "项目新增参与者限制")
    private Integer projectTesteeLimit;

    @ApiModelProperty(value = "是否为运营方项目管理员")
    private Boolean companyOwnerUser;

    @ApiModelProperty(value = "所属管理员-推广")
    private Long parentUser;

    @ApiModelProperty(value = "操作人id")
    private String createUserId;

    @ApiModelProperty(value = "用户角色ID")
    private List<Long> roleIdList;

    @ApiModelProperty(value = "激活状态0/1")
    private Boolean activeStatus;

    @ApiModelProperty(value = "锁定状态0/1")
    private Boolean lockStatus;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;
}
